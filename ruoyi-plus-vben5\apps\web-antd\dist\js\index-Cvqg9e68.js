import{_ as i}from"./edit-table.vue_vue_type_script_setup_true_lang-bZLp2ZzR.js";import{C as m}from"./index-C1KbofmV.js";import{_ as p}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as a,h as e,o as s,w as t,j as _,a as o,b as r}from"../jse/index-index-C-MnMZEz.js";import"./vxe-table-DzEj5Fop.js";import"./bootstrap-DCMzVRvD.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-BeyziwLP.js";import"./index-BxBCzu2M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-CW_j1L6F.js";import"./DownOutlined-CERO2SW5.js";import"./isMobile-8sZ0LT6r.js";import"./index-CIjgbPOA.js";import"./index-B-GBMyZJ.js";import"./Overflow-DmNzxpBy.js";import"./List-DFkqSBvs.js";import"./move-DLDqWE9R.js";import"./slide-B82O6h2Y.js";import"./useMemo-BwJyMulH.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./use-vxe-grid-BC7vZzEr.js";import"./get-popup-container-P4S1sr5h.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./index-BLwHKR_M.js";const c={class:"flex flex-col gap-4"},Y=a({__name:"index",setup(l){return(n,f)=>(s(),e(r(p),null,{default:t(()=>[_("div",c,[o(r(m),{title:"可编辑表格",size:"small"},{default:t(()=>[o(i,{class:"h-[500px]"})]),_:1})])]),_:1}))}});export{Y as default};
