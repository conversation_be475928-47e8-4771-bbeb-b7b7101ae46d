package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysOperLogBoToSysOperLogMapper__5.class,OperLogEventToSysOperLogBoMapper__5.class},
    imports = {}
)
public interface SysOperLogBoToOperLogEventMapper__5 extends BaseMapper<SysOperLogBo, OperLogEvent> {
}
