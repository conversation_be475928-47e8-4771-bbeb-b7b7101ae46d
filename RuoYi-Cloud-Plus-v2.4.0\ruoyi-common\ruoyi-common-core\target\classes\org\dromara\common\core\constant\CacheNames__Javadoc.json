{"doc": " 缓存组名称常量\n <p>\n key 格式为 cacheNames#ttl#maxIdleTime#maxSize#local\n <p>\n ttl 过期时间 如果设置为0则不过期 默认为0\n maxIdleTime 最大空闲时间 根据LRU算法清理空闲数据 如果设置为0则不检测 默认为0\n maxSize 组最大长度 根据LRU算法清理溢出数据 如果设置为0则无限长 默认为0\n local 默认开启本地缓存为1 关闭本地缓存为0\n <p>\n 例子: test#60s、test#0#60s、test#0#1m#1000、test#1h#0#500、test#1h#0#500#0\n\n <AUTHOR> Li\n", "fields": [{"name": "DEMO_CACHE", "doc": " 演示案例\n"}, {"name": "SYS_CONFIG", "doc": " 系统配置\n"}, {"name": "SYS_DICT", "doc": " 数据字典\n"}, {"name": "SYS_DICT_TYPE", "doc": " 数据字典类型\n"}, {"name": "SYS_TENANT", "doc": " 租户\n"}, {"name": "SYS_CLIENT", "doc": " 客户端\n"}, {"name": "SYS_USER_NAME", "doc": " 用户账户\n"}, {"name": "SYS_NICKNAME", "doc": " 用户名称\n"}, {"name": "SYS_DEPT", "doc": " 部门\n"}, {"name": "SYS_OSS", "doc": " OSS内容\n"}, {"name": "SYS_ROLE_CUSTOM", "doc": " 角色自定义权限\n"}, {"name": "SYS_DEPT_AND_CHILD", "doc": " 部门及以下权限\n"}, {"name": "SYS_OSS_CONFIG", "doc": " OSS配置\n"}, {"name": "ONLINE_TOKEN", "doc": " 在线用户\n"}], "enumConstants": [], "methods": [], "constructors": []}