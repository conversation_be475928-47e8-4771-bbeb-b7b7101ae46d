{"doc": " WebSocketHandler 实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "afterConnectionEstablished", "paramTypes": ["org.springframework.web.socket.WebSocketSession"], "doc": " 连接成功后\n"}, {"name": "handleTextMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.TextMessage"], "doc": " 处理接收到的文本消息\n\n @param session WebSocket会话\n @param message 接收到的文本消息\n @throws Exception 处理消息过程中可能抛出的异常\n"}, {"name": "handleBinaryMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.BinaryMessage"], "doc": " 处理接收到的二进制消息\n\n @param session WebSocket会话\n @param message 接收到的二进制消息\n @throws Exception 处理消息过程中可能抛出的异常\n"}, {"name": "handlePongMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.PongMessage"], "doc": " 处理接收到的Pong消息（心跳监测）\n\n @param session WebSocket会话\n @param message 接收到的Pong消息\n @throws Exception 处理消息过程中可能抛出的异常\n"}, {"name": "handleTransportError", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.Throwable"], "doc": " 处理WebSocket传输错误\n\n @param session   WebSocket会话\n @param exception 发生的异常\n @throws Exception 处理过程中可能抛出的异常\n"}, {"name": "afterConnectionClosed", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.CloseStatus"], "doc": " 在WebSocket连接关闭后执行清理操作\n\n @param session WebSocket会话\n @param status  关闭状态信息\n"}, {"name": "supportsPartialMessages", "paramTypes": [], "doc": " 指示处理程序是否支持接收部分消息\n\n @return 如果支持接收部分消息，则返回true；否则返回false\n"}], "constructors": []}