var c=(e,t,l)=>new Promise((p,h)=>{var T=o=>{try{m(l.next(o))}catch(d){h(d)}},n=o=>{try{m(l.throw(o))}catch(d){h(d)}},m=o=>o.done?p(o.value):Promise.resolve(o.value).then(T,n);m((l=l.apply(e,t)).next())});import{$ as f,bm as D,al as N,ar as u,aj as A}from"./bootstrap-DCMzVRvD.js";import{a as E,b as L,c as B,d as O}from"./index-C5dPwGGG.js";import{u as k,d as I}from"./popup-D6rC6QBG.js";import{g as M,h as R,i as G}from"./index-C0wIoq37.js";import{g as w}from"./dict-BLkXAGS5.js";import{r as _}from"./render-BxXtQdeV.js";import{a as i,k as q,M as H,d as W,p as Y,B as j,P as z,h as $,o as U,w as K,b as C}from"../jse/index-index-C-MnMZEz.js";import{a as F}from"./get-popup-container-P4S1sr5h.js";import{u as J}from"./use-drawer-6qcpK-D1.js";import{l as Q,a as X}from"./tree-DFBawhPd.js";const ce=()=>[{component:"Input",fieldName:"menuName",label:"菜单名称 "},{component:"Select",componentProps:{getPopupContainer:F,options:w(u.SYS_NORMAL_DISABLE)},fieldName:"status",label:"菜单状态 "},{component:"Select",componentProps:{getPopupContainer:F,options:w(u.SYS_SHOW_HIDE)},fieldName:"visible",label:"显示状态"}],Z=[{label:"目录",value:"M"},{label:"菜单",value:"C"},{label:"按钮",value:"F"}],v=[{label:"是",value:"0"},{label:"否",value:"1"}],ee={C:{icon:G,value:"菜单"},F:{icon:R,value:"按钮"},M:{icon:M,value:"目录"}},fe=[{title:"菜单名称",field:"menuName",treeNode:!0,width:200,slots:{default:({row:e})=>f(e.menuName)}},{title:"图标",field:"icon",width:80,slots:{default:({row:e})=>(e==null?void 0:e.icon)==="#"?"":i("span",{class:"flex justify-center"},[i(D,{icon:e.icon},null)])}},{title:"排序",field:"orderNum",width:120},{title:"组件类型",field:"menuType",width:150,slots:{default:({row:e})=>{const t=ee[e.menuType];return t?i("span",{class:"flex items-center justify-center gap-1"},[H(t.icon,{class:"size-[18px]"}),i("span",null,[t.value])]):"未知"}}},{title:"权限标识",field:"perms"},{title:"组件路径",field:"component"},{title:"状态",field:"status",width:100,slots:{default:({row:e})=>_(e.status,u.SYS_NORMAL_DISABLE)}},{title:"显示",field:"visible",width:100,slots:{default:({row:e})=>_(e.visible,u.SYS_SHOW_HIDE)}},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],te=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"menuId"},{component:"TreeSelect",defaultValue:0,fieldName:"parentId",label:"上级菜单",rules:"selectRequired"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:Z,optionType:"button"},defaultValue:"M",dependencies:{componentProps:(e,t)=>(Object.keys(t.errors.value).forEach(l=>{t.setFieldError(l,void 0)}),{}),triggerFields:["menuType"]},fieldName:"menuType",label:"菜单类型"},{component:"Input",dependencies:{show:e=>e.menuType!=="F",triggerFields:["menuType"]},renderComponentContent:e=>({addonBefore:()=>i(D,{icon:e.icon},null),addonAfter:()=>i("a",{href:"https://icon-sets.iconify.design/",target:"_blank"},[q("搜索图标")])}),fieldName:"icon",help:"点击搜索图标跳转到iconify & 粘贴",label:"菜单图标"},{component:"Input",fieldName:"menuName",label:"菜单名称",help:"支持i18n写法, 如: menu.system.user",rules:"required"},{component:"InputNumber",fieldName:"orderNum",help:"排序, 数字越小越靠前",label:"显示排序",defaultValue:0,rules:"required"},{component:"Input",componentProps:e=>({placeholder:e.isFrame==="0"?"填写链接地址http(s)://  使用新页面打开":"填写`路由地址`或者`链接地址`  链接默认使用内部iframe内嵌打开"}),dependencies:{rules:e=>e.isFrame!=="0"?N({message:"请输入路由地址"}).refine(t=>!t.startsWith("/"),{message:"路由地址不需要带/"}):N({message:"请输入链接地址"}).regex(/^https?:\/\//,{message:"请输入正确的链接地址"}),show:e=>(e==null?void 0:e.menuType)!=="F",triggerFields:["isFrame","menuType"]},fieldName:"path",help:`路由地址不带/, 如: menu, user
 链接为http(s)://开头
 链接默认使用内部iframe打开, 可通过{是否外链}控制打开方式`,label:"路由地址"},{component:"Input",componentProps:e=>({disabled:e.isFrame==="0"}),defaultValue:"",dependencies:{rules:e=>e.path&&!/^https?:\/\//.test(e.path)?N().min(1,{message:"非链接时必填组件路径"}).refine(t=>!t.startsWith("/")&&!t.endsWith("/"),{message:"组件路径开头/末尾不需要带/"}):N().optional(),show:e=>e.menuType==="C",triggerFields:["menuType","path"]},fieldName:"component",help:"填写./src/views下的组件路径, 如system/menu/index",label:"组件路径"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:v,optionType:"button"},defaultValue:"1",dependencies:{show:e=>e.menuType!=="F",triggerFields:["menuType"]},fieldName:"isFrame",help:`外链为http(s)://开头
 选择否时, 使用iframe从内部打开页面, 否则新窗口打开`,label:"是否外链"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:w(u.SYS_SHOW_HIDE),optionType:"button"},defaultValue:"0",dependencies:{show:e=>e.menuType!=="F",triggerFields:["menuType"]},fieldName:"visible",help:"隐藏后不会出现在菜单栏, 但仍然可以访问",label:"是否显示"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:w(u.SYS_NORMAL_DISABLE),optionType:"button"},defaultValue:"0",dependencies:{show:e=>e.menuType!=="F",triggerFields:["menuType"]},fieldName:"status",help:"停用后不会出现在菜单栏, 也无法访问",label:"菜单状态"},{component:"Input",dependencies:{show:e=>e.menuType!=="M",triggerFields:["menuType"]},fieldName:"perms",help:`控制器中定义的权限字符
 如: @SaCheckPermission("system:user:import")`,label:"权限标识"},{component:"Input",componentProps:e=>({disabled:e.isFrame==="0",placeholder:"必须为json字符串格式"}),dependencies:{show:e=>e.menuType==="C",triggerFields:["menuType"]},fieldName:"queryParam",help:`vue-router中的query属性
 如{"name": "xxx", "age": 16}`,label:"路由参数"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:v,optionType:"button"},defaultValue:"0",dependencies:{show:e=>e.menuType==="C",triggerFields:["menuType"]},fieldName:"isCache",help:"路由的keepAlive属性",label:"是否缓存"}],he=W({__name:"menu-drawer",emits:["reload"],setup(e,{emit:t}){const l=t,p=Y(!1),h=j(()=>p.value?f("pages.common.edit"):f("pages.common.add")),[T,n]=A({commonConfig:{componentProps:{class:"w-full"},formItemClass:"col-span-2",labelWidth:90},schema:te(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function m(){return c(this,null,function*(){const a=yield O();a.forEach(g=>{g.menuName=f(g.menuName)});const s=a.filter(g=>g.menuType!=="F"),y=Q(s,{id:"menuId",pid:"parentId"}),b=[{menuId:0,menuName:f("menu.root"),children:y}];X(b,"menuName"," / "),n.updateSchema([{componentProps:{fieldNames:{label:"menuName",value:"menuId"},getPopupContainer:F,listHeight:300,showSearch:!0,treeData:b,treeDefaultExpandAll:!1,treeDefaultExpandedKeys:[0],treeLine:{showLeafIcon:!1},treeNodeFilterProp:"menuName",treeNodeLabelProp:"fullName"},fieldName:"parentId"}])})}const{onBeforeClose:o,markInitialized:d,resetInitialized:S}=k({initializedGetter:I(n),currentGetter:I(n)}),[P,r]=J({onBeforeClose:o,onClosed:V,onConfirm:x,onOpenChange(a){return c(this,null,function*(){if(!a)return null;r.drawerLoading(!0);const{id:s,update:y}=r.getData();if(p.value=y,yield m(),s&&(yield n.setFieldValue("parentId",s),y)){const b=yield E(s);yield n.setValues(b)}yield d(),r.drawerLoading(!1)})}});function x(){return c(this,null,function*(){try{r.lock(!0);const{valid:a}=yield n.validate();if(!a)return;const s=z(yield n.getValues());yield p.value?L(s):B(s),S(),l("reload"),r.close()}catch(a){console.error(a)}finally{r.lock(!1)}})}function V(){return c(this,null,function*(){yield n.resetForm(),S()})}return(a,s)=>(U(),$(C(P),{title:h.value,class:"w-[600px]"},{default:K(()=>[i(C(T))]),_:1},8,["title"]))}});export{he as _,fe as c,ce as q};
