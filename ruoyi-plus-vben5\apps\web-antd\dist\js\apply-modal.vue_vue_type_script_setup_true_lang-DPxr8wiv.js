var g=(w,p,s)=>new Promise((u,a)=>{var d=t=>{try{n(s.next(t))}catch(e){a(e)}},c=t=>{try{n(s.throw(t))}catch(e){a(e)}},n=t=>t.done?u(t.value):Promise.resolve(t.value).then(d,c);n((s=s.apply(w,p)).next())});import{aj as L,aw as k}from"./bootstrap-DCMzVRvD.js";import{g as x,c as V}from"./index-CZhogUxH.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{_ as I}from"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import{d as N,h as B,o as D,w as h,a as y,b as C}from"../jse/index-index-C-MnMZEz.js";import{u as T}from"./use-modal-CeMSCP2m.js";const z=N({__name:"apply-modal",emits:["complete"],setup(w,{emit:p}){const s=p,[u,a]=T({title:"流程发起",fullscreenButton:!1,onConfirm:n,onOpenChange(e){return g(this,null,function*(){var r;if(!e)return null;const{taskId:i}=a.getData(),l=yield x(i),o={};l.buttonList.forEach(m=>{o[m.code]=m.show});const f=(r=o==null?void 0:o.copy)!=null?r:!1;c.updateSchema([{fieldName:"flowCopyList",dependencies:{if:f,triggerFields:[""]}}])})}}),[d,c]=L({commonConfig:{formItemClass:"col-span-2",labelWidth:100,componentProps:{class:"w-full"}},schema:[{fieldName:"messageType",component:"CheckboxGroup",componentProps:{options:[{label:"站内信",value:"1",disabled:!0},{label:"邮件",value:"2"},{label:"短信",value:"3"}]},label:"通知方式",defaultValue:["1"]},{fieldName:"attachment",component:"FileUpload",componentProps:{maxCount:10,maxSize:20,accept:"png, jpg, jpeg, doc, docx, xlsx, xls, ppt, pdf"},defaultValue:[],label:"附件上传",formItemClass:"items-start"},{fieldName:"flowCopyList",component:"Input",defaultValue:[],label:"抄送人"}],showDefaultActions:!1,wrapperClass:"grid-cols-2"});function n(){return g(this,null,function*(){try{a.modalLoading(!0);const{messageType:e,flowCopyList:i,attachment:l}=k(yield c.getValues()),{taskId:o,taskVariables:f,variables:r}=a.getData(),m=i.map(b=>({userId:b.userId,userName:b.nickName})),_={fileId:l.join(","),messageType:e,flowCopyList:m,taskId:o,taskVariables:f,variables:r};yield V(_),a.close(),s("complete")}catch(e){console.error(e)}finally{a.modalLoading(!1)}})}return(e,i)=>(D(),B(C(u),null,{default:h(()=>[y(C(d),null,{flowCopyList:h(l=>[y(C(I),{"user-list":l.modelValue,"onUpdate:userList":o=>l.modelValue=o},null,8,["user-list","onUpdate:userList"])]),_:1})]),_:1}))}});export{z as _};
