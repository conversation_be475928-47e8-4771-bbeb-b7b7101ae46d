var p=(n,s,t)=>new Promise((o,r)=>{var c=e=>{try{a(t.next(e))}catch(i){r(i)}},f=e=>{try{a(t.throw(e))}catch(i){r(i)}},a=e=>e.done?o(e.value):Promise.resolve(e.value).then(c,f);a((t=t.apply(n,s)).next())});import{i as l}from"./api-CwjVPMpk.js";import{u as m,_}from"./use-echarts-CF-NZzbo.js";import{d,p as h,v as u,h as w,o as g,b}from"../jse/index-index-C-MnMZEz.js";const v=d({name:"Isp",__name:"isp",setup(n){const s=h(),{renderEcharts:t}=m(s);return u(()=>p(null,null,function*(){const o=yield l();t({legend:{left:"left",orient:"vertical"},series:[{data:o,emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)",shadowOffsetX:0}},label:{formatter:"{b}: {c} - ({d}%)",show:!0},radius:"50%",type:"pie"}],title:{left:"center",text:"网络运营商占比"},tooltip:{trigger:"item"}})})),(o,r)=>(g(),w(b(_),{ref_key:"ispRef",ref:s,height:"720px",width:"100%"},null,512))}});export{v as _};
