package org.dromara.resource.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1177;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.SysOss;
import org.dromara.resource.domain.SysOssToSysOssVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1177.class,
    uses = {SysOssToSysOssVoMapper.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper extends BaseMapper<SysOssVo, SysOss> {
}
