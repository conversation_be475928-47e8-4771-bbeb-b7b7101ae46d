var I=Object.defineProperty;var $=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var D=(r,o,e)=>o in r?I(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,M=(r,o)=>{for(var e in o||(o={}))B.call(o,e)&&D(r,e,o[e]);if($)for(var e of $(o))N.call(o,e)&&D(r,e,o[e]);return r};var h=(r,o,e)=>new Promise((k,i)=>{var y=s=>{try{d(e.next(s))}catch(u){i(u)}},p=s=>{try{d(e.throw(s))}catch(u){i(u)}},d=s=>s.done?k(s.value):Promise.resolve(s.value).then(y,p);d((e=e.apply(r,o)).next())});import{as as S,an as O}from"./bootstrap-DCMzVRvD.js";import{d as R,l as q,S as z,h as c,o as l,w as a,a as w,b as n,T as C,k as _,t as b}from"../jse/index-index-C-MnMZEz.js";import{v as W}from"./vxe-table-DzEj5Fop.js";import{q as j,c as F,_ as G,w as L,a as T,b as H}from"./warehouse-modal.vue_vue_type_script_setup_true_lang-GsQB75_D.js";import{c as J}from"./download-UJak946_.js";import V from"./index-BeyziwLP.js";import{_ as K}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{u as Q}from"./use-vxe-grid-BC7vZzEr.js";import{u as U}from"./use-modal-CeMSCP2m.js";import{P as X}from"./index-DNdMANjv.js";import{g as Y}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./popup-D6rC6QBG.js";import"./dict-BLkXAGS5.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Se=R({__name:"index",setup(r){const o={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:j(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={checkboxConfig:{highlight:!0,reserve:!0},columns:F,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(f,...x)=>h(null,[f,...x],function*({page:t},m={}){return yield L(M({pageNum:t.currentPage,pageSize:t.pageSize},m))})}},rowConfig:{keyField:"warehouseId"},id:"warehousemanage-warehouse-index"},[k,i]=Q({formOptions:o,gridOptions:e}),[y,p]=U({connectedComponent:G});function d(){p.setData({}),p.open()}function s(t){return h(this,null,function*(){p.setData({id:t.warehouseId}),p.open()})}function u(t){return h(this,null,function*(){yield T(t.warehouseId),yield i.query()})}function P(){const m=i.grid.getCheckboxRecords().map(f=>f.warehouseId);O.confirm({title:"提示",okType:"danger",content:`确认删除选中的${m.length}条记录吗？`,onOk:()=>h(null,null,function*(){yield T(m),yield i.query()})})}function A(){J(H,"仓库列表数据",i.formApi.form.values,{fieldMappingTime:o.fieldMappingTime})}return(t,m)=>{const f=q("a-button"),x=q("ghost-button"),g=z("access");return l(),c(n(K),{"auto-content-height":!0},{default:a(()=>[w(n(k),{"table-title":"仓库列表列表"},{"toolbar-tools":a(()=>[w(n(V),null,{default:a(()=>[C((l(),c(f,{onClick:A},{default:a(()=>[_(b(t.$t("pages.common.export")),1)]),_:1})),[[g,["warehousemanage:warehouse:export"],"code"]]),C((l(),c(f,{disabled:!n(W)(n(i)),danger:"",type:"primary",onClick:P},{default:a(()=>[_(b(t.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[g,["warehousemanage:warehouse:remove"],"code"]]),C((l(),c(f,{type:"primary",onClick:d},{default:a(()=>[_(b(t.$t("pages.common.add")),1)]),_:1})),[[g,["warehousemanage:warehouse:add"],"code"]])]),_:1})]),action:a(({row:v})=>[w(n(V),null,{default:a(()=>[C((l(),c(x,{onClick:S(E=>s(v),["stop"])},{default:a(()=>[_(b(t.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[g,["warehousemanage:warehouse:edit"],"code"]]),w(n(X),{"get-popup-container":n(Y),placement:"left",title:"确认删除？",onConfirm:E=>u(v)},{default:a(()=>[C((l(),c(x,{danger:"",onClick:m[0]||(m[0]=S(()=>{},["stop"]))},{default:a(()=>[_(b(t.$t("pages.common.delete")),1)]),_:1})),[[g,["warehousemanage:warehouse:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),w(n(y),{onReload:m[1]||(m[1]=v=>n(i).query())})]),_:1})}}});export{Se as default};
