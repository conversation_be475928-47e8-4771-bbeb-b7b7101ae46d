import{_ as w,b as A,c as b,d as y,a as S,e as V,f as v,N as C,g as F,h as I,i as N}from"./layout.vue_vue_type_script_setup_true_lang-DJtp5nRW.js";import{A as U,_ as z,a as D}from"./authentication-CoHR1nIG.js";import{_ as G,a as M}from"./theme-toggle.vue_vue_type_script_setup_true_lang-BfrV53rV.js";import{z as o}from"./bootstrap-DCMzVRvD.js";import{p as a,c as t,o as s}from"../jse/index-index-C-MnMZEz.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./use-modal-CeMSCP2m.js";import"./x-Bfkqqjgb.js";import"./use-drawer-6qcpK-D1.js";import"./TabsList.vue_vue_type_script_setup_true_lang-QZrN9Wp8.js";import"./rotate-cw-DzZTu9nW.js";import"./use-tabs-Zz_nc_n2.js";import"./index-A0HTSyFu.js";import"./index-D6-099PU.js";const r=a(!1);function P(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const k=o(n,[["render",c]]);export{U as AuthPageLayout,z as AuthenticationColorToggle,D as AuthenticationLayoutToggle,w as BasicLayout,A as Breadcrumb,b as CheckUpdates,y as GlobalSearch,S as IFrameRouterView,k as IFrameView,G as LanguageToggle,V as LockScreen,v as LockScreenModal,C as Notification,F as Preferences,I as PreferencesButton,M as ThemeToggle,N as UserDropdown,P as useOpenPreferences};
