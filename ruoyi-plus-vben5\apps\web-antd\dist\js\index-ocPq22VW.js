import{C as r,c as u}from"./helper-Bc7QQ92Q.js";import{y as e}from"./bootstrap-DCMzVRvD.js";function a(s){return e.get("/system/user/list",{params:s})}function i(s){return u("/system/user/export",s)}function p(s){return e.post("/system/user/importData",s,{headers:{"Content-Type":r.FORM_DATA},isTransformResponse:!1})}function m(){return e.post("/system/user/importTemplate",{},{isTransformResponse:!1,responseType:"blob"})}function f(s){const t=s?`/system/user/${s}`:"/system/user/";return e.get(t)}function c(s){return e.postWithMsg("/system/user",s)}function y(s){return e.putWithMsg("/system/user",s)}function d(s){const t={userId:s.userId,status:s.status};return e.putWithMsg("/system/user/changeStatus",t)}function g(s){return e.deleteWithMsg(`/system/user/${s}`)}function l(s){return e.putWithMsg("/system/user/resetPwd",s,{encrypt:!0})}function T(){return e.get("/system/user/deptTree")}function h(s){return e.get(`/system/user/list/dept/${s}`)}export{l as a,d as b,a as c,m as d,g as e,f,T as g,i as h,y as i,c as j,h as l,p as u};
