var m=(i,r,o)=>new Promise((s,f)=>{var u=t=>{try{d(o.next(t))}catch(c){f(c)}},e=t=>{try{d(o.throw(t))}catch(c){f(c)}},d=t=>t.done?s(t.value):Promise.resolve(t.value).then(u,e);d((o=o.apply(i,r)).next())});import{$ as h,aj as v}from"./bootstrap-DCMzVRvD.js";import{c as I}from"./index-B4NcjlQn.js";import{g as D,h as k,i as P}from"./index-BYl8LdbP.js";import{u as S,d as w}from"./popup-D6rC6QBG.js";import{_ as V}from"./options-tag.vue_vue_type_script_setup_true_lang-k3ySxERw.js";import{p as x}from"./constant-CNx795op.js";import{a as g,d as B,p as F,B as q,P as W,h as A,o as L,w as z,j as $,b}from"../jse/index-index-C-MnMZEz.js";import{u as G}from"./use-modal-CeMSCP2m.js";import{a as j}from"./tree-DFBawhPd.js";import{a as O}from"./get-popup-container-P4S1sr5h.js";const te=()=>[{component:"Input",fieldName:"flowName",label:"流程名称"},{component:"Input",fieldName:"flowCode",label:"流程code"}],ae=[{type:"checkbox",width:60},{field:"flowName",title:"流程名称",minWidth:150},{field:"flowCode",title:"流程code",minWidth:150},{field:"version",title:"版本号",minWidth:80,formatter:({cellValue:i})=>`V${i}.0`},{field:"activityStatus",title:"激活状态",minWidth:100,slots:{default:"activityStatus"}},{field:"isPublish",title:"发布状态",minWidth:100,slots:{default:({row:i})=>{const r=i.isPublish;return g(V,{options:x,value:r},null)}}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],T=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"id"},{component:"TreeSelect",fieldName:"category",label:"流程分类",rules:"selectRequired"},{component:"Input",fieldName:"flowCode",label:"流程code",rules:"required"},{component:"Input",fieldName:"flowName",label:"流程名称",rules:"required"},{component:"Input",fieldName:"formPath",label:"表单路径",rules:"required"}],U={class:"min-h-[400px]"},oe=B({__name:"process-definition-modal",emits:["reload"],setup(i,{emit:r}){const o=r,s=F(!1),f=q(()=>s.value?h("pages.common.edit"):h("pages.common.add")),[u,e]=v({commonConfig:{componentProps:{class:"w-full"},formItemClass:"col-span-2",labelWidth:90},schema:T(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function d(){return m(this,null,function*(){const a=yield I();j(a,"label"," / "),e.updateSchema([{componentProps:{fieldNames:{label:"label",value:"id"},getPopupContainer:O,listHeight:300,showSearch:!0,treeData:a,treeDefaultExpandAll:!0,treeLine:{showLeafIcon:!1},treeNodeFilterProp:"label",treeNodeLabelProp:"fullName"},fieldName:"category"}])})}const{onBeforeClose:t,markInitialized:c,resetInitialized:p}=S({initializedGetter:w(e),currentGetter:w(e)}),[N,n]=G({onBeforeClose:t,onClosed:_,onConfirm:C,onOpenChange(a){return m(this,null,function*(){if(!a)return null;n.modalLoading(!0);const{id:l}=n.getData();if(s.value=!!l,yield d(),s.value&&l){const y=yield D(l);yield e.setValues(y)}yield c(),n.modalLoading(!1)})}});function C(){return m(this,null,function*(){try{n.lock(!0);const{valid:a}=yield e.validate();if(!a)return;const l=W(yield e.getValues());s.value?(yield k(l),o("reload","update")):(yield P(l),o("reload","add")),p(),n.close()}catch(a){console.error(a)}finally{n.lock(!1)}})}function _(){return m(this,null,function*(){yield e.resetForm(),p()})}return(a,l)=>(L(),A(b(N),{"fullscreen-button":!1,title:f.value,class:"w-[550px]"},{default:z(()=>[$("div",U,[g(b(u))])]),_:1},8,["title"]))}});export{oe as _,ae as c,te as q};
