2025-06-15 21:07:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:07:49 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 47936 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:07:49 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-15 21:07:49 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 21:07:49 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:07:50 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-15 21:07:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-15 21:07:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-15 21:07:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 21:07:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-15 21:07:51 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-15 21:07:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1285 ms
2025-06-15 21:07:52 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-15 21:07:53 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:53 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:53 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-7] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:53 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:54 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:08:07 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7343922c
2025-06-15 21:08:08 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:08:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-15 21:08:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-15 21:08:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-15 21:08:08 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-15 21:08:08 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-15 21:08:08 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server *************:8800 register finished
2025-06-15 21:08:10 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:08:10 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-15 21:08:10 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-15 21:08:10 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-15 21:08:10 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 23.665 seconds (process running for 24.163)
2025-06-15 21:08:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:08:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-15 21:08:10 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 21:08:10 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:08:10 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 21:08:10 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-15 21:08:10 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62cc079a
2025-06-15 21:08:10 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 21:08:10 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934236414295494656]
2025-06-15 21:08:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-15 21:08:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-15 21:14:36 [snail-job-scheduled-thread-1] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1934238099071909888]
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:23 [nacos.client.config.listener.task-0] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:49:24 [nacos.client.config.listener.task-0] INFO  o.s.c.e.event.RefreshEventListener - Refresh keys changed: [datasource.wms.password, datasource.wms.url, datasource.wms.username]
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:04:00 [nacos.client.config.listener.task-0] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:04:00 [nacos.client.config.listener.task-0] INFO  o.s.c.e.event.RefreshEventListener - Refresh keys changed: [datasource.wms.password, datasource.wms.url, datasource.wms.username]
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:08 [nacos.client.config.listener.task-0] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:05:08 [nacos.client.config.listener.task-0] INFO  o.s.c.e.event.RefreshEventListener - Refresh keys changed: [datasource.wms.password, datasource.wms.url, datasource.wms.username]
2025-06-15 22:33:47 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-15 22:33:47 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-15 22:33:47 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-15 22:33:47 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-15 22:33:47 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-15 22:33:47 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-15 22:33:47 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934236414295494656]
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-15 22:33:47 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 22:33:48 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-15 22:42:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:42:44 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 51832 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:42:44 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-15 22:42:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 22:42:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:42:45 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-15 22:42:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-15 22:42:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-15 22:42:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 22:42:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-15 22:42:46 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-15 22:42:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1370 ms
2025-06-15 22:42:47 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-15 22:42:48 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:48 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:48 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:48 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:49 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:42:57 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@46383a78
2025-06-15 22:42:59 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:42:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-15 22:42:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-15 22:42:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-15 22:42:59 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-15 22:42:59 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-15 22:42:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server *************:8800 register finished
2025-06-15 22:43:00 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:43:00 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-15 22:43:00 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-15 22:43:00 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-15 22:43:00 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 19.49 seconds (process running for 20.047)
2025-06-15 22:43:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:43:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-15 22:43:00 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 22:43:01 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3907ae02
2025-06-15 22:43:01 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 22:43:01 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934260300768702464]
2025-06-15 22:43:01 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 22:43:01 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 22:43:01 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 22:43:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-15 22:43:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-15 22:47:07 [snail-job-scheduled-thread-4] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1934261381363994624]
