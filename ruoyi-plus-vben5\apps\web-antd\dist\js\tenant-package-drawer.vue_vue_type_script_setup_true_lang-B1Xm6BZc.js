var d=(I,g,r)=>new Promise((i,f)=>{var k=o=>{try{l(r.next(o))}catch(c){f(c)}},s=o=>{try{l(r.throw(o))}catch(c){f(c)}},l=o=>o.done?i(o.value):Promise.resolve(o.value).then(k,s);l((r=r.apply(I,g)).next())});import{$ as w,aj as M,ao as z}from"./bootstrap-DCMzVRvD.js";import{t as A,m as G}from"./index-C5dPwGGG.js";import{d as L,e as j,f as q}from"./index-C9ZGxfH6.js";import{M as K}from"./menu-select-table-C_Crb3c1.js";import{u as R,d as U}from"./popup-D6rC6QBG.js";import{d as O,p as C,B as P,P as $,h as E,o as H,w as b,a as _,b as h,j as J,I as S}from"../jse/index-index-C-MnMZEz.js";import{u as Q}from"./use-drawer-6qcpK-D1.js";import{e as V}from"./tree-DFBawhPd.js";const re=()=>[{component:"Input",fieldName:"packageName",label:"套餐名称"}],ie=[{type:"checkbox",width:60},{title:"套餐名称",field:"packageName"},{title:"备注",field:"remark"},{title:"状态",field:"status",slots:{default:"status"}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],W=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"packageId"},{component:"Radio",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"menuCheckStrictly"},{component:"Input",fieldName:"packageName",label:"套餐名称",rules:"required"},{component:"menuIds",defaultValue:[],fieldName:"menuIds",label:"关联菜单"},{component:"Textarea",fieldName:"remark",label:"备注"}],X={class:"h-[600px] w-full"},ce=O({__name:"tenant-package-drawer",emits:["reload"],setup(I,{emit:g}){const r=g,i=C(!1),f=P(()=>i.value?w("pages.common.edit"):w("pages.common.add")),[k,s]=M({commonConfig:{formItemClass:"col-span-2"},layout:"vertical",schema:W(),showDefaultActions:!1,wrapperClass:"grid-cols-2"}),l=C([]);function o(a){return d(this,null,function*(){if(a){const e=yield A(a),t=e.menus;V(t,n=>{n.label=w(n.label)}),l.value=e.menus,yield S(),yield s.setFieldValue("menuIds",e.checkedKeys)}else{const e=yield G();V(e,t=>{t.label=w(t.label)}),l.value=e,yield S(),yield s.setFieldValue("menuIds",[])}})}function c(){return d(this,null,function*(){var n,p,m;const a=yield U(s)(),e=(m=(p=(n=y.value)==null?void 0:n.getCheckedKeys)==null?void 0:p.call(n))!=null?m:[];return a+e.join(",")})}const{onBeforeClose:x,markInitialized:N,resetInitialized:v}=R({initializedGetter:c,currentGetter:c}),[F,u]=Q({onBeforeClose:x,onClosed:T,onConfirm:B,destroyOnClose:!0,onOpenChange(a){return d(this,null,function*(){if(!a)return null;u.drawerLoading(!0);const{id:e}=u.getData();if(i.value=!!e,i.value&&e){const t=yield L(e);yield s.setValues(z(t,["menuIds"]))}yield o(e),yield N(),u.drawerLoading(!1)})}}),y=C();function B(){return d(this,null,function*(){var a,e,t;try{u.drawerLoading(!0);const{valid:n}=yield s.validate();if(!n)return;const p=(t=(e=(a=y.value)==null?void 0:a.getCheckedKeys)==null?void 0:e.call(a))!=null?t:[],m=$(yield s.getValues());m.menuIds=p,yield i.value?j(m):q(m),v(),r("reload"),u.close()}catch(n){console.error(n)}finally{u.drawerLoading(!1)}})}function T(){return d(this,null,function*(){yield s.resetForm(),v()})}function D(a){s.setFieldValue("menuCheckStrictly",a)}return(a,e)=>(H(),E(h(F),{title:f.value,class:"w-[800px]"},{default:b(()=>[_(h(k),null,{menuIds:b(t=>[J("div",X,[_(h(K),{ref_key:"menuSelectRef",ref:y,"checked-keys":t.value,association:h(s).form.values.menuCheckStrictly,menus:l.value,"onUpdate:association":D},null,8,["checked-keys","association","menus"])])]),_:1})]),_:1},8,["title"]))}});export{ce as _,ie as c,re as q};
