package org.dromara.wms.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.wms.domain.WmsWarehouse;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-15T14:29:49+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WmsWarehouseVoToWmsWarehouseMapper__1Impl implements WmsWarehouseVoToWmsWarehouseMapper__1 {

    @Override
    public WmsWarehouse convert(WmsWarehouseVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WmsWarehouse wmsWarehouse = new WmsWarehouse();

        wmsWarehouse.setRemark( arg0.getRemark() );
        wmsWarehouse.setWarehouseId( arg0.getWarehouseId() );
        wmsWarehouse.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wmsWarehouse.setWarehouseName( arg0.getWarehouseName() );
        wmsWarehouse.setWarehouseNumber( arg0.getWarehouseNumber() );
        wmsWarehouse.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wmsWarehouse.setWarehouseType( arg0.getWarehouseType() );

        return wmsWarehouse;
    }

    @Override
    public WmsWarehouse convert(WmsWarehouseVo arg0, WmsWarehouse arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setRemark( arg0.getRemark() );
        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setWarehouseType( arg0.getWarehouseType() );

        return arg1;
    }
}
