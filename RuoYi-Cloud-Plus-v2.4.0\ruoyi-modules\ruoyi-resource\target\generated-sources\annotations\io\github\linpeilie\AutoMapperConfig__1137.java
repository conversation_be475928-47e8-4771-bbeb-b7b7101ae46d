package io.github.linpeilie;

import org.dromara.resource.domain.SysOssConfigToSysOssConfigVoMapper;
import org.dromara.resource.domain.SysOssToSysOssVoMapper;
import org.dromara.resource.domain.bo.SysOssBoToSysOssMapper;
import org.dromara.resource.domain.bo.SysOssConfigBoToSysOssConfigMapper;
import org.dromara.resource.domain.convert.SysOssVoConvert;
import org.dromara.resource.domain.vo.SysOssConfigVoToSysOssConfigMapper;
import org.dromara.resource.domain.vo.SysOssVoToSysOssMapper;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__1137.class, SysOssVoConvert.class, SysOssToSysOssVoMapper.class, SysOssVoToSysOssMapper.class, SysOssConfigVoToSysOssConfigMapper.class, SysOssConfigToSysOssConfigVoMapper.class, SysOssConfigBoToSysOssConfigMapper.class, SysOssBoToSysOssMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__1137 {
}
