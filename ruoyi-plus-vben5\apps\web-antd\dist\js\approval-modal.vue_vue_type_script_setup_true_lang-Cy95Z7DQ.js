var U=Object.defineProperty,B=Object.defineProperties;var D=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable;var k=(t,a,e)=>a in t?U(t,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[a]=e,C=(t,a)=>{for(var e in a||(a={}))M.call(a,e)&&k(t,e,a[e]);if(I)for(var e of I(a))j.call(a,e)&&k(t,e,a[e]);return t},_=(t,a)=>B(t,D(a));var N=(t,a,e)=>new Promise((w,n)=>{var c=l=>{try{o(e.next(l))}catch(b){n(b)}},L=l=>{try{o(e.throw(l))}catch(b){n(b)}},o=l=>l.done?w(l.value):Promise.resolve(l.value).then(c,L);o((e=e.apply(t,a)).next())});import{aj as P,ao as S,ap as A}from"./bootstrap-DCMzVRvD.js";import{a as T,c as E}from"./index-CZhogUxH.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{_ as v}from"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import{d as $,p as q,P as z,h as G,o as u,b as g,w as x,a as y,c as h,K,F,j as O,t as W}from"../jse/index-index-C-MnMZEz.js";import{u as H}from"./use-modal-CeMSCP2m.js";const J={class:"opacity-70"},Q={key:1,class:"text-red-500"},le=$({__name:"approval-modal",emits:["complete"],setup(t,{emit:a}){const e=a,[w,n]=P({commonConfig:{formItemClass:"col-span-2",labelWidth:100,componentProps:{class:"w-full"}},schema:[{fieldName:"taskId",component:"Input",label:"任务ID",dependencies:{show:!1,triggerFields:[""]}},{fieldName:"messageType",component:"CheckboxGroup",componentProps:{options:[{label:"站内信",value:"1",disabled:!0},{label:"邮件",value:"2"},{label:"短信",value:"3"}]},label:"通知方式",defaultValue:["1"]},{fieldName:"attachment",component:"FileUpload",componentProps:{maxCount:10,maxSize:20,accept:"png, jpg, jpeg, doc, docx, xlsx, xls, ppt, pdf"},defaultValue:[],label:"附件上传",formItemClass:"items-start"},{fieldName:"flowCopyList",component:"Input",defaultValue:[],label:"抄送人"},{fieldName:"assigneeMap",component:"Input",label:"下一步审批人"},{fieldName:"message",component:"Textarea",label:"审批意见",formItemClass:"items-start"}],showDefaultActions:!1,wrapperClass:"grid-cols-2"}),c=q([]),[L,o]=H({title:"审批通过",fullscreenButton:!1,class:"min-h-[365px]",onConfirm:l,onOpenChange(m){return N(this,null,function*(){if(!m)return yield n.resetForm(),null;o.modalLoading(!0);const{taskId:r,copyPermission:s,assignPermission:i}=o.getData();if(n.updateSchema([{fieldName:"flowCopyList",dependencies:{if:s,triggerFields:[""]}},{fieldName:"assigneeMap",dependencies:{if:i,triggerFields:[""]}}]),i){const f=yield T({taskId:r});c.value=f.map(p=>_(C({},p),{selectUserList:[]}))}yield n.setFieldValue("taskId",r),o.modalLoading(!1)})}});function l(){return N(this,null,function*(){var m;try{o.modalLoading(!0);const{valid:r}=yield n.validate();if(!r)return;const s=z(yield n.getValues()),i=s.flowCopyList.map(p=>({userId:p.userId,userName:p.nickName})),f=_(C({},S(s,["attachment"])),{fileId:s.attachment.join(","),taskVariables:{},variables:{},flowCopyList:i});if((m=o.getData())!=null&&m.assignPermission){for(const d of c.value)if(d.selectUserList.length===0){A.warn(`未选择节点[${d.nodeName}]审批人`);return}const p={};c.value.forEach(d=>{p[d.nodeCode]=d.selectUserList.map(V=>V.userId).join(",")}),f.assigneeMap=p}yield E(f),o.close(),e("complete")}catch(r){console.error(r)}finally{o.modalLoading(!1)}})}return(m,r)=>(u(),G(g(L),null,{default:x(()=>[y(g(w),null,{flowCopyList:x(s=>[y(g(v),{"user-list":s.modelValue,"onUpdate:userList":i=>s.modelValue=i},null,8,["user-list","onUpdate:userList"])]),assigneeMap:x(()=>[(u(!0),h(F,null,K(c.value,s=>(u(),h("div",{key:s.nodeCode,class:"flex items-center gap-2"},[s.permissionFlag?(u(),h(F,{key:0},[O("span",J,W(s.nodeName),1),y(g(v),{"allow-user-ids":s.permissionFlag,"user-list":s.selectUserList,"onUpdate:userList":i=>s.selectUserList=i},null,8,["allow-user-ids","user-list","onUpdate:userList"])],64)):(u(),h("span",Q,"没有权限, 请联系管理员"))]))),128))]),_:1})]),_:1}))}});export{le as _};
