package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysLogininforToSysLogininforVoMapper__11.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__11 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
