package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__11;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysPostBoToSysPostMapper__11.class,SysPostVoToSysPostMapper__11.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__11 extends BaseMapper<SysPost, SysPostVo> {
}
