package org.dromara.demo.domain.vo;

import io.github.linpeilie.AutoMapperConfig__696;
import io.github.linpeilie.BaseMapper;
import org.dromara.demo.domain.TestTree;
import org.dromara.demo.domain.TestTreeToTestTreeVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__696.class,
    uses = {TestTreeToTestTreeVoMapper__1.class},
    imports = {}
)
public interface TestTreeVoToTestTreeMapper__1 extends BaseMapper<TestTreeVo, TestTree> {
}
