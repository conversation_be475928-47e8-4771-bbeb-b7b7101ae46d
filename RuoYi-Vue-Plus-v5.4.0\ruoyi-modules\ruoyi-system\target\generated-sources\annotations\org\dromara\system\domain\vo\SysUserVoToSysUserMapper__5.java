package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__5;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysRoleVoToSysRoleMapper__5.class,SysRoleToSysRoleVoMapper__5.class,SysUserToSysUserVoMapper__5.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__5 extends BaseMapper<SysUserVo, SysUser> {
}
