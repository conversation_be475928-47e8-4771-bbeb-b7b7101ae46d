var P=Object.defineProperty;var T=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var E=(r,a,o)=>a in r?P(r,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[a]=o,I=(r,a)=>{for(var o in a||(a={}))B.call(a,o)&&E(r,o,a[o]);if(T)for(var o of T(a))F.call(a,o)&&E(r,o,a[o]);return r};var x=(r,a,o)=>new Promise((v,i)=>{var $=p=>{try{g(o.next(p))}catch(C){i(C)}},d=p=>{try{g(o.throw(p))}catch(C){i(C)}},g=p=>p.done?v(p.value):Promise.resolve(p.value).then($,d);g((o=o.apply(r,a)).next())});import{as as O}from"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import{q as N,c as R,_ as j,d as G,a as L}from"./dept-drawer.vue_vue_type_script_setup_true_lang-BSPBVMyO.js";import S from"./index-BeyziwLP.js";import{_ as M}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{e as q}from"./tree-DFBawhPd.js";import{d as W,l as V,S as Y,h as b,o as y,w as n,a as m,b as l,T as k,k as u,t as f,I as z}from"../jse/index-index-C-MnMZEz.js";import{u as H}from"./use-vxe-grid-BC7vZzEr.js";import{u as J}from"./use-drawer-6qcpK-D1.js";import{P as K}from"./index-DNdMANjv.js";import{g as Q}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-ocPq22VW.js";import"./helper-Bc7QQ92Q.js";import"./popup-D6rC6QBG.js";import"./dict-BLkXAGS5.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Te=W({__name:"index",setup(r){const a={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:N(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},o={columns:R,height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:(s,...D)=>x(null,[s,...D],function*(t,e={}){return{rows:yield G(I({},e))}}),querySuccess:()=>{q(i.grid.getData(),t=>t.expand=!0),z(()=>{h(!0)})}}},scrollY:{enabled:!1,gt:0},rowConfig:{keyField:"deptId"},treeConfig:{parentField:"parentId",rowField:"deptId",transform:!0},id:"system-dept-index"},[v,i]=H({formOptions:a,gridOptions:o,gridEvents:{cellDblclick:t=>{const{row:e={}}=t;if(!(e!=null&&e.children))return;const s=e==null?void 0:e.expand;i.grid.setTreeExpand(e,!s),e.expand=!s},toggleTreeExpand:t=>{const{row:e={},expanded:s}=t;e.expand=s}}}),[$,d]=J({connectedComponent:j});function g(){d.setData({update:!1}),d.open()}function p(t){const{deptId:e}=t;d.setData({id:e,update:!1}),d.open()}function C(t){return x(this,null,function*(){d.setData({id:t.deptId,update:!0}),d.open()})}function A(t){return x(this,null,function*(){yield L(t.deptId),yield i.query()})}function h(t){var e;q(i.grid.getData(),s=>s.expand=t),(e=i.grid)==null||e.setAllTreeExpand(t)}return(t,e)=>{const s=V("a-button"),D=V("ghost-button"),_=Y("access");return y(),b(l(M),{"auto-content-height":!0},{default:n(()=>[m(l(v),{"table-title":"部门列表","table-title-help":"双击展开/收起子菜单"},{"toolbar-tools":n(()=>[m(l(S),null,{default:n(()=>[m(s,{onClick:e[0]||(e[0]=c=>h(!1))},{default:n(()=>[u(f(t.$t("pages.common.collapse")),1)]),_:1}),m(s,{onClick:e[1]||(e[1]=c=>h(!0))},{default:n(()=>[u(f(t.$t("pages.common.expand")),1)]),_:1}),k((y(),b(s,{type:"primary",onClick:g},{default:n(()=>[u(f(t.$t("pages.common.add")),1)]),_:1})),[[_,["system:dept:add"],"code"]])]),_:1})]),action:n(({row:c})=>[m(l(S),null,{default:n(()=>[k((y(),b(D,{onClick:w=>C(c)},{default:n(()=>[u(f(t.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[_,["system:dept:edit"],"code"]]),k((y(),b(D,{class:"btn-success",onClick:w=>p(c)},{default:n(()=>[u(f(t.$t("pages.common.add")),1)]),_:2},1032,["onClick"])),[[_,["system:dept:add"],"code"]]),m(l(K),{"get-popup-container":l(Q),placement:"left",title:"确认删除？",onConfirm:w=>A(c)},{default:n(()=>[k((y(),b(D,{danger:"",onClick:e[2]||(e[2]=O(()=>{},["stop"]))},{default:n(()=>[u(f(t.$t("pages.common.delete")),1)]),_:1})),[[_,["system:dept:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),m(l($),{onReload:e[3]||(e[3]=c=>l(i).query())})]),_:1})}}});export{Te as default};
