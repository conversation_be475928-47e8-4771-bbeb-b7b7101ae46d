package org.dromara.wms.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 仓库部门关联对象 ware_warehouse_dept
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ware_warehouse_dept")
public class WmsWarehouseDept extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 部门ID
     */
    private Long deptId;

}
