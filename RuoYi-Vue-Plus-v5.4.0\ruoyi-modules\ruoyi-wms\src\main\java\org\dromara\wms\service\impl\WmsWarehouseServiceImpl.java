package org.dromara.wms.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wms.domain.bo.WmsWarehouseBo;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.mapper.WmsWarehouseMapper;
import org.dromara.wms.service.IWmsWarehouseService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 仓库列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WmsWarehouseServiceImpl implements IWmsWarehouseService {

    private final WmsWarehouseMapper baseMapper;

    /**
     * 查询仓库列表
     *
     * @param warehouseId 主键
     * @return 仓库列表
     */
    @Override
    public WmsWarehouseVo queryById(Long warehouseId){
        return baseMapper.selectVoById(warehouseId);
    }

    /**
     * 分页查询仓库列表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 仓库列表分页列表
     */
    @Override
    public TableDataInfo<WmsWarehouseVo> queryPageList(WmsWarehouseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WmsWarehouse> lqw = buildQueryWrapper(bo);
        Page<WmsWarehouseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的仓库列表列表
     *
     * @param bo 查询条件
     * @return 仓库列表列表
     */
    @Override
    public List<WmsWarehouseVo> queryList(WmsWarehouseBo bo) {
        LambdaQueryWrapper<WmsWarehouse> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WmsWarehouse> buildQueryWrapper(WmsWarehouseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WmsWarehouse> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WmsWarehouse::getWarehouseId);
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseNumber()), WmsWarehouse::getWarehouseNumber, bo.getWarehouseNumber());
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseName()), WmsWarehouse::getWarehouseName, bo.getWarehouseName());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), WmsWarehouse::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseInventoryStatus()), WmsWarehouse::getWarehouseInventoryStatus, bo.getWarehouseInventoryStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseRecevingStatus()), WmsWarehouse::getWarehouseRecevingStatus, bo.getWarehouseRecevingStatus());
        return lqw;
    }

    /**
     * 新增仓库列表
     *
     * @param bo 仓库列表
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WmsWarehouseBo bo) {
        WmsWarehouse add = MapstructUtils.convert(bo, WmsWarehouse.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWarehouseId(add.getWarehouseId());
        }
        return flag;
    }

    /**
     * 修改仓库列表
     *
     * @param bo 仓库列表
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WmsWarehouseBo bo) {
        WmsWarehouse update = MapstructUtils.convert(bo, WmsWarehouse.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WmsWarehouse entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除仓库列表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
