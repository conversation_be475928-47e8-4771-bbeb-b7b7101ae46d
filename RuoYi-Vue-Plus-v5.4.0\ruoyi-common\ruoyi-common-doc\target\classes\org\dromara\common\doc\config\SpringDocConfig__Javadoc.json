{"doc": " 接口文档配置\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "openApiBuilder", "paramTypes": ["java.util.Optional", "org.springdoc.core.service.SecurityService", "org.springdoc.core.properties.SpringDocConfigProperties", "org.springdoc.core.utils.PropertyResolverUtils", "java.util.Optional", "java.util.Optional", "java.util.Optional"], "doc": " 自定义 openapi 处理器\n"}, {"name": "openApiCustomizer", "paramTypes": [], "doc": " 对已经生成好的 OpenApi 进行自定义操作\n"}], "constructors": []}