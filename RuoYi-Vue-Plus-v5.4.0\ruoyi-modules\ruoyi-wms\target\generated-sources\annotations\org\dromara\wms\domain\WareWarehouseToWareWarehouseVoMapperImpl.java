package org.dromara.wms.domain;

import javax.annotation.processing.Generated;
import org.dromara.wms.domain.vo.WareWarehouseVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-11T21:33:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WareWarehouseToWareWarehouseVoMapperImpl implements WareWarehouseToWareWarehouseVoMapper {

    @Override
    public WareWarehouseVo convert(WareWarehouse arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WareWarehouseVo wareWarehouseVo = new WareWarehouseVo();

        wareWarehouseVo.setRemark( arg0.getRemark() );
        wareWarehouseVo.setWarehouseId( arg0.getWarehouseId() );
        wareWarehouseVo.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wareWarehouseVo.setWarehouseName( arg0.getWarehouseName() );
        wareWarehouseVo.setWarehouseNumber( arg0.getWarehouseNumber() );
        wareWarehouseVo.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wareWarehouseVo.setWarehouseType( arg0.getWarehouseType() );

        return wareWarehouseVo;
    }

    @Override
    public WareWarehouseVo convert(WareWarehouse arg0, WareWarehouseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setRemark( arg0.getRemark() );
        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setWarehouseType( arg0.getWarehouseType() );

        return arg1;
    }
}
