2025-06-17 08:13:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-17 08:13:51 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 29700 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:13:51 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-17 08:13:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-17 08:13:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-17 08:13:52 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-17 08:13:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-17 08:13:52 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-17 08:13:52 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 08:13:52 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-17 08:13:53 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-17 08:13:53 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1866 ms
2025-06-17 08:13:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-17 08:13:55 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-17 08:13:55 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-17 08:13:55 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-17 08:13:55 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-17 08:13:57 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-17 08:13:57 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@279dd959
2025-06-17 08:13:59 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-17 08:13:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-17 08:14:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-17 08:14:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-17 08:14:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-17 08:14:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-17 08:14:00 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server *************:8800 register finished
2025-06-17 08:14:01 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-17 08:14:01 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-17 08:14:01 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-17 08:14:02 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-17 08:14:02 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-17 08:14:02 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-17 08:14:02 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-17 08:14:02 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-17 08:14:02 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-17 08:14:02 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 14.741 seconds (process running for 15.522)
2025-06-17 08:14:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-17 08:14:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-17 08:14:02 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-17 08:14:02 [RMI TCP Connection(8)-*************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 08:14:02 [RMI TCP Connection(8)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 08:14:02 [RMI TCP Connection(8)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-17 08:14:02 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@195a0cad
2025-06-17 08:14:02 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-17 08:14:02 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934766415478444032]
2025-06-17 08:14:12 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-17 08:14:12 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-17 08:17:13 [snail-job-scheduled-thread-4] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1934767227252449280]
2025-06-17 08:53:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-10] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[134] Task scheduled successfully.
2025-06-17 08:53:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-14] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[135] Task scheduled successfully.
2025-06-17 08:53:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-18] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[136] Task scheduled successfully.
2025-06-17 08:53:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-22] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[137] Task scheduled successfully.
2025-06-17 08:53:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-26] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[138] Task scheduled successfully.
2025-06-17 08:53:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-30] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[139] Task scheduled successfully.
2025-06-17 08:53:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-34] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[140] Task scheduled successfully.
2025-06-17 08:53:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-38] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[141] Task scheduled successfully.
2025-06-17 08:53:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-42] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[142] Task scheduled successfully.
2025-06-17 08:53:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-46] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[143] Task scheduled successfully.
2025-06-17 08:54:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-50] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[144] Task scheduled successfully.
2025-06-17 08:54:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-54] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[145] Task scheduled successfully.
2025-06-17 08:54:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-58] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[146] Task scheduled successfully.
2025-06-17 08:54:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-62] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[147] Task scheduled successfully.
2025-06-17 08:54:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-66] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[148] Task scheduled successfully.
2025-06-17 08:54:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-70] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[149] Task scheduled successfully.
2025-06-17 08:54:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-74] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[150] Task scheduled successfully.
2025-06-17 08:54:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-78] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[151] Task scheduled successfully.
2025-06-17 08:54:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-82] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[152] Task scheduled successfully.
2025-06-17 08:54:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-86] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[153] Task scheduled successfully.
2025-06-17 08:54:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-90] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[154] Task scheduled successfully.
2025-06-17 08:54:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-94] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[155] Task scheduled successfully.
2025-06-17 08:55:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-98] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[156] Task scheduled successfully.
2025-06-17 08:55:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-102] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[157] Task scheduled successfully.
2025-06-17 08:55:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-106] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[158] Task scheduled successfully.
2025-06-17 08:55:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-110] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[159] Task scheduled successfully.
2025-06-17 08:55:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-114] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[160] Task scheduled successfully.
2025-06-17 08:55:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-118] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[161] Task scheduled successfully.
2025-06-17 08:55:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-122] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[162] Task scheduled successfully.
2025-06-17 08:55:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-126] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[163] Task scheduled successfully.
2025-06-17 08:55:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-130] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[164] Task scheduled successfully.
2025-06-17 08:55:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-134] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[165] Task scheduled successfully.
2025-06-17 08:55:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-138] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[166] Task scheduled successfully.
2025-06-17 08:55:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-142] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[167] Task scheduled successfully.
2025-06-17 08:56:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-146] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[168] Task scheduled successfully.
2025-06-17 08:56:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-150] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[169] Task scheduled successfully.
2025-06-17 08:56:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-154] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[170] Task scheduled successfully.
2025-06-17 08:56:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-158] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[171] Task scheduled successfully.
2025-06-17 08:56:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-162] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[172] Task scheduled successfully.
2025-06-17 08:56:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-166] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[173] Task scheduled successfully.
2025-06-17 08:56:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-170] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[174] Task scheduled successfully.
2025-06-17 08:56:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-174] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[175] Task scheduled successfully.
2025-06-17 08:56:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-178] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[176] Task scheduled successfully.
2025-06-17 08:56:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-182] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[177] Task scheduled successfully.
2025-06-17 08:56:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-186] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[178] Task scheduled successfully.
2025-06-17 08:56:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-190] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[179] Task scheduled successfully.
2025-06-17 08:57:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-194] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[180] Task scheduled successfully.
2025-06-17 08:57:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-198] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[181] Task scheduled successfully.
2025-06-17 08:57:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-202] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[182] Task scheduled successfully.
2025-06-17 08:57:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-206] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[183] Task scheduled successfully.
2025-06-17 08:57:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-210] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[184] Task scheduled successfully.
2025-06-17 08:57:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-214] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[185] Task scheduled successfully.
2025-06-17 08:57:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-218] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[186] Task scheduled successfully.
2025-06-17 08:57:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-222] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[187] Task scheduled successfully.
2025-06-17 08:57:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-226] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[188] Task scheduled successfully.
2025-06-17 08:57:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-230] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[189] Task scheduled successfully.
2025-06-17 08:57:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-234] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[190] Task scheduled successfully.
2025-06-17 08:57:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-238] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[191] Task scheduled successfully.
2025-06-17 08:58:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-242] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[192] Task scheduled successfully.
2025-06-17 08:58:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-246] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[193] Task scheduled successfully.
2025-06-17 08:58:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-250] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[194] Task scheduled successfully.
2025-06-17 08:58:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-254] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[195] Task scheduled successfully.
2025-06-17 08:58:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-258] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[196] Task scheduled successfully.
2025-06-17 08:58:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-262] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[197] Task scheduled successfully.
2025-06-17 08:58:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-266] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[198] Task scheduled successfully.
2025-06-17 08:58:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-270] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[199] Task scheduled successfully.
2025-06-17 08:58:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-274] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[200] Task scheduled successfully.
2025-06-17 08:58:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-278] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[201] Task scheduled successfully.
2025-06-17 08:58:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-282] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[202] Task scheduled successfully.
2025-06-17 08:58:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-286] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[203] Task scheduled successfully.
2025-06-17 08:59:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-290] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[204] Task scheduled successfully.
2025-06-17 08:59:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-294] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[205] Task scheduled successfully.
2025-06-17 08:59:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-298] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[206] Task scheduled successfully.
2025-06-17 08:59:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-302] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[207] Task scheduled successfully.
2025-06-17 08:59:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-306] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[208] Task scheduled successfully.
2025-06-17 08:59:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-310] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[209] Task scheduled successfully.
2025-06-17 08:59:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-314] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[210] Task scheduled successfully.
2025-06-17 08:59:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-318] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[211] Task scheduled successfully.
2025-06-17 08:59:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-322] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[212] Task scheduled successfully.
2025-06-17 08:59:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-326] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[213] Task scheduled successfully.
2025-06-17 08:59:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-330] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[214] Task scheduled successfully.
2025-06-17 08:59:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-334] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[215] Task scheduled successfully.
2025-06-17 09:00:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-338] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[216] Task scheduled successfully.
2025-06-17 09:00:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-342] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[217] Task scheduled successfully.
2025-06-17 09:00:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-346] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[218] Task scheduled successfully.
2025-06-17 09:00:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-350] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[219] Task scheduled successfully.
2025-06-17 09:00:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-354] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[220] Task scheduled successfully.
2025-06-17 09:00:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-358] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[221] Task scheduled successfully.
2025-06-17 09:00:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-362] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[222] Task scheduled successfully.
2025-06-17 09:00:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-366] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[223] Task scheduled successfully.
2025-06-17 09:00:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-370] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[224] Task scheduled successfully.
2025-06-17 09:00:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-374] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[225] Task scheduled successfully.
2025-06-17 09:00:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-378] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[226] Task scheduled successfully.
2025-06-17 09:00:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-382] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[227] Task scheduled successfully.
2025-06-17 09:01:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-386] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[228] Task scheduled successfully.
2025-06-17 09:01:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-390] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[229] Task scheduled successfully.
2025-06-17 09:01:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-394] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[230] Task scheduled successfully.
2025-06-17 09:01:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-398] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[231] Task scheduled successfully.
2025-06-17 09:01:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-402] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[232] Task scheduled successfully.
2025-06-17 09:01:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-406] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[233] Task scheduled successfully.
2025-06-17 09:01:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-410] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[234] Task scheduled successfully.
2025-06-17 09:01:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-414] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[235] Task scheduled successfully.
2025-06-17 09:01:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-418] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[236] Task scheduled successfully.
2025-06-17 09:01:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-422] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[237] Task scheduled successfully.
2025-06-17 09:01:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-426] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[238] Task scheduled successfully.
2025-06-17 09:01:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-430] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[239] Task scheduled successfully.
2025-06-17 09:02:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-434] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[240] Task scheduled successfully.
2025-06-17 09:02:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-438] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[241] Task scheduled successfully.
2025-06-17 09:02:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-442] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[242] Task scheduled successfully.
2025-06-17 09:02:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-446] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[243] Task scheduled successfully.
2025-06-17 09:02:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-450] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[244] Task scheduled successfully.
2025-06-17 09:02:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-454] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[245] Task scheduled successfully.
2025-06-17 09:02:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-458] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[246] Task scheduled successfully.
2025-06-17 09:02:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-462] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[247] Task scheduled successfully.
2025-06-17 09:02:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-466] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[248] Task scheduled successfully.
2025-06-17 09:02:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-470] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[249] Task scheduled successfully.
2025-06-17 09:02:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-474] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[250] Task scheduled successfully.
2025-06-17 09:02:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-478] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[251] Task scheduled successfully.
2025-06-17 09:03:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-482] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[252] Task scheduled successfully.
2025-06-17 09:03:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-486] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[253] Task scheduled successfully.
2025-06-17 09:03:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-490] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[254] Task scheduled successfully.
2025-06-17 09:03:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-494] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[255] Task scheduled successfully.
2025-06-17 09:03:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-498] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[256] Task scheduled successfully.
2025-06-17 09:03:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-502] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[257] Task scheduled successfully.
2025-06-17 09:03:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-506] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[258] Task scheduled successfully.
2025-06-17 09:03:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-510] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[259] Task scheduled successfully.
2025-06-17 09:03:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-514] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[260] Task scheduled successfully.
2025-06-17 09:03:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-518] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[261] Task scheduled successfully.
2025-06-17 09:03:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-522] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[262] Task scheduled successfully.
2025-06-17 09:03:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-526] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[263] Task scheduled successfully.
2025-06-17 09:04:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-530] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[264] Task scheduled successfully.
2025-06-17 09:04:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-534] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[265] Task scheduled successfully.
2025-06-17 09:04:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-538] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[266] Task scheduled successfully.
2025-06-17 09:04:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-542] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[267] Task scheduled successfully.
2025-06-17 09:04:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-546] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[268] Task scheduled successfully.
2025-06-17 09:04:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-550] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[269] Task scheduled successfully.
2025-06-17 09:04:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-554] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[270] Task scheduled successfully.
2025-06-17 09:04:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-558] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[271] Task scheduled successfully.
2025-06-17 09:04:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-562] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[272] Task scheduled successfully.
2025-06-17 09:04:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-566] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[273] Task scheduled successfully.
2025-06-17 09:04:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-570] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[274] Task scheduled successfully.
2025-06-17 09:04:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-574] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[275] Task scheduled successfully.
2025-06-17 09:05:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-578] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[276] Task scheduled successfully.
2025-06-17 09:05:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-582] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[277] Task scheduled successfully.
2025-06-17 09:05:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-586] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[278] Task scheduled successfully.
2025-06-17 09:05:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-590] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[279] Task scheduled successfully.
2025-06-17 09:05:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-594] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[280] Task scheduled successfully.
2025-06-17 09:05:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-598] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[281] Task scheduled successfully.
2025-06-17 09:05:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-602] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[282] Task scheduled successfully.
2025-06-17 09:05:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-606] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[283] Task scheduled successfully.
2025-06-17 09:05:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-610] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[284] Task scheduled successfully.
2025-06-17 09:05:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-614] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[285] Task scheduled successfully.
2025-06-17 09:05:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-618] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[286] Task scheduled successfully.
2025-06-17 09:05:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-622] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[287] Task scheduled successfully.
2025-06-17 09:06:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-626] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[288] Task scheduled successfully.
2025-06-17 09:06:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-630] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[289] Task scheduled successfully.
2025-06-17 09:06:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-634] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[290] Task scheduled successfully.
2025-06-17 09:06:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-638] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[291] Task scheduled successfully.
2025-06-17 09:06:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-642] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[292] Task scheduled successfully.
2025-06-17 09:06:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-646] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[293] Task scheduled successfully.
2025-06-17 09:06:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-650] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[294] Task scheduled successfully.
2025-06-17 09:06:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-654] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[295] Task scheduled successfully.
2025-06-17 09:06:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-658] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[296] Task scheduled successfully.
2025-06-17 09:06:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-662] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[297] Task scheduled successfully.
2025-06-17 09:06:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-666] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[298] Task scheduled successfully.
2025-06-17 09:06:57 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-670] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[299] Task scheduled successfully.
2025-06-17 09:07:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-674] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[300] Task scheduled successfully.
2025-06-17 09:07:07 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-678] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[301] Task scheduled successfully.
2025-06-17 09:07:12 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-682] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[302] Task scheduled successfully.
2025-06-17 09:07:17 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-686] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[303] Task scheduled successfully.
2025-06-17 09:07:22 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-690] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[304] Task scheduled successfully.
2025-06-17 09:07:27 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-694] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[305] Task scheduled successfully.
2025-06-17 09:07:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-698] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[306] Task scheduled successfully.
2025-06-17 09:07:37 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-702] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[307] Task scheduled successfully.
2025-06-17 09:07:42 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-706] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[308] Task scheduled successfully.
2025-06-17 09:07:47 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-710] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[309] Task scheduled successfully.
2025-06-17 09:07:52 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-714] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[310] Task scheduled successfully.
2025-06-17 09:08:26 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-719] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[311] Task scheduled successfully.
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-17 15:12:33 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934766415478444032]
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-17 15:12:33 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-17 15:12:34 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
