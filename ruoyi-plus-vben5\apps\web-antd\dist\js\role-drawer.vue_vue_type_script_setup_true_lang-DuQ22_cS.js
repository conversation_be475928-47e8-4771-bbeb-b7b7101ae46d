var d=(_,C,l)=>new Promise((c,f)=>{var y=r=>{try{n(l.next(r))}catch(i){f(i)}},s=r=>{try{n(l.throw(r))}catch(i){f(i)}},n=r=>r.done?c(r.value):Promise.resolve(r.value).then(y,s);n((l=l.apply(_,C)).next())});import{$ as w,aj as A}from"./bootstrap-DCMzVRvD.js";import{r as G,m as j}from"./index-C5dPwGGG.js";import{c as z,d as K,e as U}from"./index-Ds1FCatV.js";import{M as L}from"./menu-select-table-C_Crb3c1.js";import{u as N,d as O}from"./popup-D6rC6QBG.js";import{d as P}from"./data-CPbGrVe8.js";import{d as R,p as v,B as $,P as q,h as E,o as H,w as I,a as S,b as h,j as J,I as V}from"../jse/index-index-C-MnMZEz.js";import{u as Q}from"./use-drawer-6qcpK-D1.js";import{e as x}from"./tree-DFBawhPd.js";const W={class:"h-[600px] w-full"},le=R({__name:"role-drawer",emits:["reload"],setup(_,{emit:C}){const l=C,c=v(!1),f=$(()=>c.value?w("pages.common.edit"):w("pages.common.add")),[y,s]=A({commonConfig:{componentProps:{class:"w-full"},formItemClass:"col-span-1"},layout:"vertical",schema:P(),showDefaultActions:!1,wrapperClass:"grid-cols-2 gap-x-4"}),n=v([]);function r(a){return d(this,null,function*(){if(a){const e=yield G(a),t=e.menus;x(t,o=>{o.label=w(o.label)}),n.value=e.menus,yield V(),yield s.setFieldValue("menuIds",e.checkedKeys)}else{const e=yield j();x(e,t=>{t.label=w(t.label)}),n.value=e,yield V(),yield s.setFieldValue("menuIds",[])}})}function i(){return d(this,null,function*(){var o,p,m;const a=yield O(s)(),e=(m=(p=(o=k.value)==null?void 0:o.getCheckedKeys)==null?void 0:p.call(o))!=null?m:[];return a+e.join(",")})}const{onBeforeClose:b,markInitialized:B,resetInitialized:g}=N({initializedGetter:i,currentGetter:i}),[F,u]=Q({onBeforeClose:b,onClosed:D,onConfirm:T,destroyOnClose:!0,onOpenChange(a){return d(this,null,function*(){if(!a)return null;u.drawerLoading(!0);const{id:e}=u.getData();if(c.value=!!e,c.value&&e){const t=yield z(e);yield s.setValues(t)}yield r(e),yield B(),u.drawerLoading(!1)})}}),k=v();function T(){return d(this,null,function*(){var a,e,t;try{u.lock(!0);const{valid:o}=yield s.validate();if(!o)return;const p=(t=(e=(a=k.value)==null?void 0:a.getCheckedKeys)==null?void 0:e.call(a))!=null?t:[],m=q(yield s.getValues());m.menuIds=p,yield c.value?K(m):U(m),l("reload"),g(),u.close()}catch(o){console.error(o)}finally{u.lock(!1)}})}function D(){return d(this,null,function*(){yield s.resetForm(),g()})}function M(a){s.setFieldValue("menuCheckStrictly",a)}return(a,e)=>(H(),E(h(F),{title:f.value,class:"w-[800px]"},{default:I(()=>[S(h(y),null,{menuIds:I(t=>[J("div",W,[S(h(L),{ref_key:"menuSelectRef",ref:k,"checked-keys":t.value,association:h(s).form.values.menuCheckStrictly,menus:n.value,"onUpdate:association":M},null,8,["checked-keys","association","menus"])])]),_:1})]),_:1},8,["title"]))}});export{le as _};
