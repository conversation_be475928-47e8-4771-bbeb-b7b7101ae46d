package org.dromara.resource.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.SysOssConfig;
import org.dromara.resource.domain.SysOssConfigToSysOssConfigVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssConfigToSysOssConfigVoMapper__7.class},
    imports = {}
)
public interface SysOssConfigVoToSysOssConfigMapper__7 extends BaseMapper<SysOssConfigVo, SysOssConfig> {
}
