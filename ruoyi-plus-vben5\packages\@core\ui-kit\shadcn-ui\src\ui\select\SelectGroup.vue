<script setup lang="ts">
import type { SelectGroupProps } from 'radix-vue';

import { cn } from '@vben-core/shared/utils';
import { SelectGroup } from 'radix-vue';
import { computed } from 'vue';

const props = defineProps<{ class?: any } & SelectGroupProps>();

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});
</script>

<template>
  <SelectGroup :class="cn('w-full p-1', props.class)" v-bind="delegatedProps">
    <slot></slot>
  </SelectGroup>
</template>
