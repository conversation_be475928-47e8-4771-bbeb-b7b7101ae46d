import{o as V,h as B,t as F,p as k,q as L,s as M,c as W,_ as p,v as H,g as G,C as h}from"./bootstrap-DCMzVRvD.js";import{d as J,B as i,p as I,q as K,a as m,F as Q}from"../jse/index-index-C-MnMZEz.js";const U={small:8,middle:16,large:24},X=()=>({prefixCls:String,size:{type:[String,Number,Array]},direction:B.oneOf(F("horizontal","vertical")).def("horizontal"),align:B.oneOf(F("start","end","center","baseline")),wrap:V()});function Y(e){return typeof e=="string"?U[e]:e||0}const d=J({compatConfig:{MODE:3},name:"ASpace",inheritAttrs:!1,props:X(),slots:Object,setup(e,j){let{slots:o,attrs:f}=j;const{prefixCls:l,space:g,direction:x}=k("space",e),[q,D]=L(l),z=M(),n=i(()=>{var t,a,s;return(s=(t=e.size)!==null&&t!==void 0?t:(a=g==null?void 0:g.value)===null||a===void 0?void 0:a.size)!==null&&s!==void 0?s:"small"}),y=I(),r=I();K(n,()=>{[y.value,r.value]=(Array.isArray(n.value)?n.value:[n.value,n.value]).map(t=>Y(t))},{immediate:!0});const C=i(()=>e.align===void 0&&e.direction==="horizontal"?"center":e.align),E=i(()=>W(l.value,D.value,`${l.value}-${e.direction}`,{[`${l.value}-rtl`]:x.value==="rtl",[`${l.value}-align-${C.value}`]:C.value})),P=i(()=>x.value==="rtl"?"marginLeft":"marginRight"),R=i(()=>{const t={};return z.value&&(t.columnGap=`${y.value}px`,t.rowGap=`${r.value}px`),p(p({},t),e.wrap&&{flexWrap:"wrap",marginBottom:`${-r.value}px`})});return()=>{var t,a;const{wrap:s,direction:T="horizontal"}=e,b=(t=o.default)===null||t===void 0?void 0:t.call(o),_=H(b),w=_.length;if(w===0)return null;const c=(a=o.split)===null||a===void 0?void 0:a.call(o),A=`${l.value}-item`,N=y.value,S=w-1;return m("div",G(G({},f),{},{class:[E.value,f.class],style:[R.value,f.style]}),[_.map((O,u)=>{let $=b.indexOf(O);$===-1&&($=`$$space-${u}`);let v={};return z.value||(T==="vertical"?u<S&&(v={marginBottom:`${N/(c?2:1)}px`}):v=p(p({},u<S&&{[P.value]:`${N/(c?2:1)}px`}),s&&{paddingBottom:`${r.value}px`})),q(m(Q,{key:$},[m("div",{class:A,style:v},[O]),u<S&&c&&m("span",{class:`${A}-split`,style:v},[c])]))})])}}});d.Compact=h;d.install=function(e){return e.component(d.name,d),e.component(h.name,h),e};export{h as Compact,d as default,X as spaceProps};
