import{aj as a,ak as i}from"./bootstrap-DCMzVRvD.js";import{s as l}from"./init-C8TKSdFQ.js";import{I as u}from"./index-BY49C_DM.js";import{M as t}from"../jse/index-index-C-MnMZEz.js";l({configVxeTable:r=>{r.setConfig({grid:{align:"center",border:!1,minHeight:180,formConfig:{enabled:!1},proxyConfig:{autoLoad:!0,response:{result:"rows",total:"total",list:"rows"},showActiveMsg:!0,showResponseMsg:!1},showOverflow:!0,pagerConfig:{pageSize:10,pageSizes:[10,20,30,40,50]},rowConfig:{isHover:!0,isCurrent:!1},columnConfig:{resizable:!0},toolbarConfig:{custom:{icon:"vxe-icon-setting"},zoom:!0,refresh:{code:"query"}},round:!0,size:"medium",customConfig:{storage:!1}}}),r.renderer.add("CellImage",{renderTableDefault(e,o){const{column:n,row:s}=o;return t(u,{src:s[n.field]})}}),r.renderer.add("CellLink",{renderTableDefault(e){const{props:o}=e;return t(i,{size:"small",type:"link"},{default:()=>o==null?void 0:o.text})}})},useVbenForm:a});function c(r){var e,o,n;return((n=(o=(e=r==null?void 0:r.grid)==null?void 0:e.getCheckboxRecords)==null?void 0:o.call(e))==null?void 0:n.length)>0}function C(r,e){if(e.length===0)return;const o=e.map(s=>s.field).join(","),n=e.map(s=>s.order).join(",");r.orderByColumn=o,r.isAsc=n}export{C as a,c as v};
