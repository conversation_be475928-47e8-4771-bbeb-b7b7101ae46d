{"doc": " sql操作工具类\n\n <AUTHOR>\n", "fields": [{"name": "SQL_REGEX", "doc": " 定义常用的 sql关键字\n"}, {"name": "SQL_PATTERN", "doc": " 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）\n"}], "enumConstants": [], "methods": [{"name": "escapeOrderBySql", "paramTypes": ["java.lang.String"], "doc": " 检查字符，防止注入绕过\n"}, {"name": "isValidOrderBySql", "paramTypes": ["java.lang.String"], "doc": " 验证 order by 语法是否符合规范\n"}, {"name": "filterKeyword", "paramTypes": ["java.lang.String"], "doc": " SQL关键字检查\n"}], "constructors": []}