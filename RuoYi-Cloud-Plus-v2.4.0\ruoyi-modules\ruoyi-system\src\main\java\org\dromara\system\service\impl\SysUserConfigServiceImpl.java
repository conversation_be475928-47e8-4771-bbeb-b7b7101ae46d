package org.dromara.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysUserConfig;
import org.dromara.system.mapper.SysUserConfigMapper;
import org.dromara.system.service.ISysUserConfigService;
import org.springframework.stereotype.Service;

/**
 * 用户配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RequiredArgsConstructor
@Service
public class SysUserConfigServiceImpl implements ISysUserConfigService {

    private final SysUserConfigMapper baseMapper;

    private static final String WAREHOUSE_CONFIG_KEY = "current_warehouse";

    @Override
    public Long getCurrentUserWarehouse() {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<SysUserConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUserConfig::getUserId, userId)
               .eq(SysUserConfig::getConfigKey, WAREHOUSE_CONFIG_KEY);
        
        SysUserConfig config = baseMapper.selectOne(wrapper);
        if (config != null && config.getConfigValue() != null) {
            try {
                return Long.parseLong(config.getConfigValue());
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    @Override
    public void setCurrentUserWarehouse(Long warehouseId) {
        Long userId = LoginHelper.getUserId();
        
        LambdaQueryWrapper<SysUserConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUserConfig::getUserId, userId)
               .eq(SysUserConfig::getConfigKey, WAREHOUSE_CONFIG_KEY);
        
        SysUserConfig config = baseMapper.selectOne(wrapper);
        
        if (config == null) {
            // 新增配置
            config = new SysUserConfig();
            config.setUserId(userId);
            config.setConfigKey(WAREHOUSE_CONFIG_KEY);
            config.setConfigValue(warehouseId != null ? warehouseId.toString() : null);
            baseMapper.insert(config);
        } else {
            // 更新配置
            config.setConfigValue(warehouseId != null ? warehouseId.toString() : null);
            baseMapper.updateById(config);
        }
    }

}
