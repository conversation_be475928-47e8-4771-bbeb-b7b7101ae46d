package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssBoToSysOssMapper__7;
import org.dromara.resource.domain.vo.SysOssVo;
import org.dromara.resource.domain.vo.SysOssVoToSysOssMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssBoToSysOssMapper__7.class,SysOssVoToSysOssMapper__7.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__7 extends BaseMapper<SysOss, SysOssVo> {
}
