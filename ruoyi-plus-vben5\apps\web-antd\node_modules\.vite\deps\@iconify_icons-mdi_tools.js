import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-mdi@1.2.48/node_modules/@iconify/icons-mdi/tools.js
var require_tools = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-mdi@1.2.48/node_modules/@iconify/icons-mdi/tools.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="currentColor" d="m21.71 20.29l-1.42 1.42a1 1 0 0 1-1.41 0L7 9.85A3.81 3.81 0 0 1 6 10a4 4 0 0 1-3.78-5.3l2.54 2.54l.53-.53l1.42-1.42l.53-.53L4.7 2.22A4 4 0 0 1 10 6a3.81 3.81 0 0 1-.15 1l11.86 11.88a1 1 0 0 1 0 1.41M2.29 18.88a1 1 0 0 0 0 1.41l1.42 1.42a1 1 0 0 0 1.41 0l5.47-5.46l-2.83-2.83M20 2l-4 2v2l-2.17 2.17l2 2L18 8h2l2-4Z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_tools();
//# sourceMappingURL=@iconify_icons-mdi_tools.js.map
