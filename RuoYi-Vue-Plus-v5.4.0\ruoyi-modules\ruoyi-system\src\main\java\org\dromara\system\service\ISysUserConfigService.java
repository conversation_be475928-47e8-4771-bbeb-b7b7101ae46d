package org.dromara.system.service;

/**
 * 用户配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface ISysUserConfigService {

    /**
     * 获取用户配置值
     *
     * @param userId 用户ID
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @return 配置值
     */
    String getUserConfig(Long userId, String configKey, String tenantId);

    /**
     * 设置用户配置值
     *
     * @param userId 用户ID
     * @param configKey 配置键
     * @param configValue 配置值
     * @param tenantId 租户ID
     */
    void setUserConfig(Long userId, String configKey, String configValue, String tenantId);

    /**
     * 获取当前用户的仓库选择
     *
     * @return 仓库ID
     */
    Long getCurrentUserWarehouse();

    /**
     * 设置当前用户的仓库选择
     *
     * @param warehouseId 仓库ID
     */
    void setCurrentUserWarehouse(Long warehouseId);

}
