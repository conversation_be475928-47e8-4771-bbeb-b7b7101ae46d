var Pe=Object.defineProperty,Be=Object.defineProperties;var $e=Object.getOwnPropertyDescriptors;var ue=Object.getOwnPropertySymbols;var Le=Object.prototype.hasOwnProperty,Fe=Object.prototype.propertyIsEnumerable;var W=(n,t,s)=>t in n?Pe(n,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[t]=s,g=(n,t)=>{for(var s in t||(t={}))Le.call(t,s)&&W(n,s,t[s]);if(ue)for(var s of ue(t))Fe.call(t,s)&&W(n,s,t[s]);return n},N=(n,t)=>Be(n,$e(t));var O=(n,t,s)=>W(n,typeof t!="symbol"?t+"":t,s);var B=(n,t,s)=>new Promise((v,d)=>{var x=i=>{try{V(s.next(i))}catch(P){d(P)}},$=i=>{try{V(s.throw(i))}catch(P){d(P)}},V=i=>i.done?v(i.value):Promise.resolve(i.value).then(x,$);V((s=s.apply(n,t)).next())});import{af as k,ag as Ee,ah as Re,A as T,ai as Ae,aj as be,d as ve,E as Ge,B as h,a1 as He,ak as _e,P as K,q as ze,v as Me,y as Ne,c as X,o as G,a as j,a2 as ce,w as m,K as de,r as f,f as H,j as me,k as fe,h as pe,t as Q,b as r,e as S,g as C,T as je,n as ge,G as Y,H as qe,O as Ie,al as De,I as Ue,ac as Je,M as We}from"../jse/index-index-C-MnMZEz.js";import{cn as Ke,co as Xe,cp as Qe,$ as _,cq as Ye,cr as Ze,cs as et,ax as tt,ct as ot}from"./bootstrap-DCMzVRvD.js";import{u as st,V as he,a as at,b as rt,e as nt}from"./init-C8TKSdFQ.js";function it(){return{class:"",gridClass:"",gridOptions:{},gridEvents:{},formOptions:void 0,showSearchForm:!0}}class lt{constructor(t={}){O(this,"isMounted",!1);O(this,"stateHandler");O(this,"formApi",{});O(this,"grid",{});O(this,"state",null);O(this,"store");const s=g({},t),v=it();this.store=new Ke(k(s,v),{onUpdate:()=>{this.state=this.store.state}}),this.state=this.store.state,this.stateHandler=new Ee,Re(this)}mount(t,s){!this.isMounted&&t&&(this.grid=t,this.formApi=s,this.stateHandler.setConditionTrue(),this.isMounted=!0)}query(){return B(this,arguments,function*(t={}){try{yield this.grid.commitProxy("query",T(t))}catch(s){console.error("Error occurred while querying:",s)}})}reload(){return B(this,arguments,function*(t={}){try{yield this.grid.commitProxy("reload",T(t))}catch(s){console.error("Error occurred while reloading:",s)}})}setGridOptions(t){this.setState({gridOptions:t})}setLoading(t){this.setState({gridOptions:{loading:t}})}setState(t){Ae(t)?this.store.setState(s=>k(t(s),s)):this.store.setState(s=>k(t,s))}toggleSearchForm(t){var s,v;return this.setState({showSearchForm:be(t)?t:!((s=this.state)!=null&&s.showSearchForm)}),(v=this.state)==null?void 0:v.showSearchForm}unmount(){this.isMounted=!1,this.stateHandler.reset()}}const ut={class:"mr-1 pl-1 text-[1rem]"},ct={class:"mt-2"},Z="form-",q="toolbar-actions",I="toolbar-tools",dt="table-title",mt=ve({__name:"use-vxe-grid",props:{api:{},tableTitle:{},tableTitleHelp:{},class:{},gridClass:{},gridOptions:{},gridEvents:{},formOptions:{},showSearchForm:{type:Boolean},separator:{type:[Boolean,Object]}},setup(n){var z,ae;const t=n,s=Ge("gridRef"),v=(ae=(z=t.api)==null?void 0:z.useStore)==null?void 0:ae.call(z),{gridOptions:d,class:x,gridClass:$,gridEvents:V,formOptions:i,tableTitle:P,tableTitleHelp:ee,showSearchForm:L,separator:y}=Xe(t,v),{isMobile:ye}=Qe(),D=h(()=>!i.value||L.value===!1||y.value===!1?!1:y.value===!0||y.value===void 0?!0:y.value.show!==!1),te=h(()=>!y.value||be(y.value)||!y.value.backgroundColor?void 0:y.value.backgroundColor),l=He(),[Se,c]=st({compact:!0,handleSubmit:()=>B(null,null,function*(){const e=yield c.getValues();c.setLatestSubmissionValues(T(e)),t.api.reload(e)}),handleReset:()=>B(null,null,function*(){var a;const e=yield c.getValues();yield c.resetForm();const o=yield c.getValues();c.setLatestSubmissionValues(o),(_e(e,o)||!((a=i.value)!=null&&a.submitOnChange))&&t.api.reload(o)}),commonConfig:{componentProps:{class:"w-full"}},showCollapseButton:!0,submitButtonOptions:{content:h(()=>_("common.search"))},submitOnEnter:!0,wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}),U=h(()=>{var e;return!!((e=l[dt])!=null&&e.call(l))||P.value}),J=h(()=>{var e,o;return!!((e=l[q])!=null&&e.call(l))||!!((o=l[I])!=null&&o.call(l))||U.value}),Ce=h(()=>{var p,b,w,F,E,R,A;const e=(p=l[q])==null?void 0:p.call(l),o=(b=l[I])==null?void 0:b.call(l),a={code:"search",icon:"vxe-icon-search",circle:!0,status:L.value?"primary":void 0,title:L.value?_("common.hideSearchPanel"):_("common.showSearchPanel")},u={tools:(E=(F=(w=d.value)==null?void 0:w.toolbarConfig)==null?void 0:F.tools)!=null?E:[]};return(A=(R=d.value)==null?void 0:R.toolbarConfig)!=null&&A.search&&i.value&&(u.tools=Array.isArray(u.tools)?[...u.tools,a]:[a]),J.value?(u.slots=g(g({},e||U.value?{buttons:q}:{}),o?{tools:I}:{}),{toolbarConfig:u}):{toolbarConfig:u}}),oe=h(()=>{var a,u,p;const e=(p=(u=(a=he)==null?void 0:a.getConfig())==null?void 0:u.grid)!=null?p:{},o=K(k({},T(Ce.value),T(d.value),e));if(o.proxyConfig){const{ajax:b}=o.proxyConfig;o.proxyConfig.enabled=!!b,o.proxyConfig.autoLoad=!1}if(o.pagerConfig){const b=["PrevJump","PrevPage","Number","NextPage","NextJump"],w=["Total","Sizes","Home",...b,"End"];o.pagerConfig=k({},o.pagerConfig,{pageSize:20,background:!0,pageSizes:[10,20,30,50,100,200],className:"mt-2 w-full",layouts:ye.value?b:w,size:"mini"})}return o.formConfig&&(o.formConfig.enabled=!1),o});function we(e){var o,a;e.code==="search"&&se(),(a=(o=V.value)==null?void 0:o.toolbarToolClick)==null||a.call(o,e)}function se(){var e,o;(o=(e=t.api)==null?void 0:e.toggleSearchForm)==null||o.call(e)}const Oe=h(()=>N(g({},V.value),{toolbarToolClick:we})),Te=h(()=>{const e=[];for(const o of Object.keys(l))["empty","form","loading",q,I].includes(o)||e.push(o);return e}),ke=h(()=>{const e=[];for(const o of Object.keys(l))o.startsWith(Z)&&e.push(o);return e.map(o=>o.replace(Z,""))});function xe(){return B(this,null,function*(){var b,w,F,E,R,A,re,ne,ie,M,le;yield Ue();const e=(F=(w=(b=he)==null?void 0:b.getConfig())==null?void 0:w.grid)!=null?F:{},o=k({},T(d.value),T(e)),a=(E=o.proxyConfig)==null?void 0:E.autoLoad;((R=oe.value.proxyConfig)==null?void 0:R.enabled)&&a&&((ne=(re=t.api.grid).commitProxy)==null||ne.call(re,"_init",K(i.value)?(A=K(yield c.getValues()))!=null?A:{}:{}));const p=(ie=d.value)==null?void 0:ie.formConfig;p&&p.enabled&&console.warn("[Vben Vxe Table]: The formConfig in the grid is not supported, please use the `formOptions` props"),(le=(M=t.api)==null?void 0:M.setState)==null||le.call(M,{gridOptions:o}),nt(t.api,o,()=>c.getLatestSubmissionValues())})}ze(i,()=>{c.setState(e=>{const o=k({},i.value,e);return N(g({},o),{collapseTriggerResize:!!o.showCollapseButton})})},{immediate:!0});const Ve=h(()=>{var e;return(e=c.getState())==null?void 0:e.compact});return Me(()=>{var e,o;(o=(e=t.api)==null?void 0:e.mount)==null||o.call(e,s.value,c),xe()}),Ne(()=>{var e,o,a;(e=c==null?void 0:c.unmount)==null||e.call(c),(a=(o=t.api)==null?void 0:o.unmount)==null||a.call(o)}),(e,o)=>(G(),X("div",{class:ge(r(Y)("bg-card h-full rounded-md",r(x)))},[j(r(rt),Ie({ref_key:"gridRef",ref:s,class:r(Y)("p-2",{"pt-0":J.value&&!r(i)},r($))},oe.value,De(Oe.value)),ce({"toolbar-tools":m(a=>{var u,p;return[f(e.$slots,"toolbar-tools",S(C(a))),(p=(u=r(d))==null?void 0:u.toolbarConfig)!=null&&p.search&&r(i)?(G(),pe(r(at),{key:0,icon:"vxe-icon-search",circle:"",class:"ml-2",status:r(L)?"primary":void 0,title:r(_)("common.search"),onClick:se},null,8,["status","title"])):H("",!0)]}),form:m(()=>[r(i)?je((G(),X("div",{key:0,class:ge(r(Y)("relative rounded py-3",Ve.value?D.value?"pb-8":"pb-4":D.value?"pb-4":"pb-0"))},[f(e.$slots,"form",{},()=>[j(r(Se),null,ce({"reset-before":m(a=>[f(e.$slots,"reset-before",S(C(a)))]),"submit-before":m(a=>[f(e.$slots,"submit-before",S(C(a)))]),"expand-before":m(a=>[f(e.$slots,"expand-before",S(C(a)))]),"expand-after":m(a=>[f(e.$slots,"expand-after",S(C(a)))]),_:2},[de(ke.value,a=>({name:a,fn:m(u=>[f(e.$slots,`${Z}${a}`,S(C(u)))])}))]),1024)]),D.value?(G(),X("div",{key:0,style:qe(g({},te.value?{backgroundColor:te.value}:void 0)),class:"bg-background-deep z-100 absolute -left-2 bottom-1 h-2 w-[calc(100%+1rem)] overflow-hidden md:bottom-2 md:h-3"},null,4)):H("",!0)],2)),[[tt,r(L)!==!1]]):H("",!0)]),loading:m(()=>[f(e.$slots,"loading",{},()=>[j(r(et),{spinning:!0})])]),empty:m(()=>[f(e.$slots,"empty",{},()=>[j(r(Ze),{class:"mx-auto"}),me("div",ct,Q(r(_)("common.noData")),1)])]),_:2},[J.value?{name:"toolbar-actions",fn:m(a=>[U.value?f(e.$slots,"table-title",{key:0},()=>[me("div",ut,[fe(Q(r(P))+" ",1),r(ee)?(G(),pe(r(Ye),{key:0,"trigger-class":"pb-1"},{default:m(()=>[fe(Q(r(ee)),1)]),_:1})):H("",!0)])]):H("",!0),f(e.$slots,"toolbar-actions",S(C(a)))]),key:"0"}:void 0,de(Te.value,a=>({name:a,fn:m(u=>[f(e.$slots,a,S(C(u)))])}))]),1040,["class"])],2))}});function bt(n){const t=new lt(n),s=t;return s.useStore=d=>ot(t.store,d),[ve((d,{attrs:x,slots:$})=>(Je(()=>{t.unmount()}),t.setState(g(g({},d),x)),()=>We(mt,N(g(g({},d),x),{api:s}),$)),{name:"VbenVxeGrid",inheritAttrs:!1}),s]}export{bt as u};
