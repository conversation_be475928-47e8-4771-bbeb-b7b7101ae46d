{"doc": " 菜单信息\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRouters", "paramTypes": [], "doc": " 获取路由信息\n\n @return 路由信息\n"}, {"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 获取菜单列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据菜单编号获取详细信息\n\n @param menuId 菜单ID\n"}, {"name": "treeselect", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 获取菜单下拉树列表\n"}, {"name": "roleMenuTreeselect", "paramTypes": ["java.lang.Long"], "doc": " 加载对应角色菜单列表树\n\n @param roleId 角色ID\n"}, {"name": "tenantPackageMenuTreeselect", "paramTypes": ["java.lang.Long"], "doc": " 加载对应租户套餐菜单列表树\n\n @param packageId 租户套餐ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 新增菜单\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 修改菜单\n"}, {"name": "remove", "paramTypes": ["java.lang.Long"], "doc": " 删除菜单\n\n @param menuId 菜单ID\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 批量级联删除菜单\n\n @param menuIds 菜单ID串\n"}], "constructors": []}