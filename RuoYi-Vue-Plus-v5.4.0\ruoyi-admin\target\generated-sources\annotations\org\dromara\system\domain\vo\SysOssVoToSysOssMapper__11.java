package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysOssToSysOssVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOssToSysOssVoMapper__11.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__11 extends BaseMapper<SysOssVo, SysOss> {
}
