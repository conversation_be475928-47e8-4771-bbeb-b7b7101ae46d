import{$ as a}from"./bootstrap-DCMzVRvD.js";import{A as r}from"./authentication-CoHR1nIG.js";import{d as s,B as o,a0 as t,h as c,o as i,b as e}from"../jse/index-index-C-MnMZEz.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-BfrV53rV.js";const h=s({__name:"auth",setup(m){const p=o(()=>t.app.name),n=o(()=>t.logo.source);return(u,l)=>(i(),c(e(r),{"app-name":p.value,logo:n.value,"page-description":e(a)("authentication.pageDesc"),"page-title":e(a)("authentication.pageTitle")},null,8,["app-name","logo","page-description","page-title"]))}});export{h as default};
