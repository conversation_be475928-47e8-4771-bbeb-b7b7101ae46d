import {
  ref
} from "./chunk-7J2PGW6H.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useState.js
function useState(defaultStateValue) {
  const initValue = typeof defaultStateValue === "function" ? defaultStateValue() : defaultStateValue;
  const innerValue = ref(initValue);
  function triggerChange(newValue) {
    innerValue.value = newValue;
  }
  return [innerValue, triggerChange];
}

export {
  useState
};
//# sourceMappingURL=chunk-BK6YDXFT.js.map
