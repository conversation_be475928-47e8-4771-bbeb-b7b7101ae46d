{"doc": " 客户端管理Service业务层处理\n\n <AUTHOR>\n @date 2023-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询客户端管理\n"}, {"name": "queryByClientId", "paramTypes": ["java.lang.String"], "doc": " 查询客户端管理\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询客户端管理列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": " 查询客户端管理列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": " 新增客户端管理\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": " 修改客户端管理\n"}, {"name": "updateClientStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 修改状态\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.SysClient"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除客户端管理\n"}], "constructors": []}