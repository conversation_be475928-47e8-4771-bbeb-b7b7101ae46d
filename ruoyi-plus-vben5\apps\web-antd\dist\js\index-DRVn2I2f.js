import{av as s}from"./bootstrap-DCMzVRvD.js";import{_ as r}from"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import{_ as n}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as u,h as _,o as d,w as c,j as o,a as m,b as a}from"../jse/index-index-C-MnMZEz.js";const i={class:"bg-background rounded-lg p-4"},B=u({__name:"index",setup(l){const t=s().query;return(f,e)=>(d(),_(a(n),null,{default:c(()=>[o("div",i,[e[0]||(e[0]=o("span",null,"当前参数:",-1)),m(a(r),{data:a(t)},null,8,["data"])])]),_:1}))}});export{B as default};
