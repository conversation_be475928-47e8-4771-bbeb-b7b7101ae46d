import{D as u,a as d}from"./index-D59rZjD-.js";import{d as i,h as _,o as p,b as e,w as t,a as l,k as s,t as o}from"../jse/index-index-C-MnMZEz.js";const k=i({__name:"redis-description",props:{data:{}},setup(m){return(a,b)=>(p(),_(e(u),{bordered:"",column:{lg:4,md:3,sm:1,xl:4,xs:1},size:"small"},{default:t(()=>[l(e(d),{label:"redis版本"},{default:t(()=>[s(o(a.data.redis_version),1)]),_:1}),l(e(d),{label:"redis模式"},{default:t(()=>[s(o(a.data.redis_mode==="standalone"?"单机模式":"集群模式"),1)]),_:1}),l(e(d),{label:"tcp端口"},{default:t(()=>[s(o(a.data.tcp_port),1)]),_:1}),l(e(d),{label:"客户端数"},{default:t(()=>[s(o(a.data.connected_clients),1)]),_:1}),l(e(d),{label:"运行时间"},{default:t(()=>[s(o(a.data.uptime_in_days)+" 天 ",1)]),_:1}),l(e(d),{label:"使用内存"},{default:t(()=>[s(o(a.data.used_memory_human),1)]),_:1}),l(e(d),{label:"使用CPU"},{default:t(()=>{var r,n;return[s(o(Number.parseFloat((n=(r=a.data)==null?void 0:r.used_cpu_user_children)!=null?n:"0").toFixed(2)),1)]}),_:1}),l(e(d),{label:"内存配置"},{default:t(()=>[s(o(a.data.maxmemory_human),1)]),_:1}),l(e(d),{label:"AOF是否开启"},{default:t(()=>[s(o(a.data.aof_enabled==="0"?"否":"是"),1)]),_:1}),l(e(d),{label:"RDB是否成功"},{default:t(()=>[s(o(a.data.rdb_last_bgsave_status),1)]),_:1}),l(e(d),{label:"key数量"},{default:t(()=>[s(o(a.data.dbSize),1)]),_:1}),l(e(d),{label:"网络入口/出口"},{default:t(()=>[s(o(`${a.data.instantaneous_input_kbps}kps/${a.data.instantaneous_output_kbps}kps`),1)]),_:1})]),_:1}))}});export{k as _};
