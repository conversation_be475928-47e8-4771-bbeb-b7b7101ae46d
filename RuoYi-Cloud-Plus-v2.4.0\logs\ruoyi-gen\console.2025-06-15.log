2025-06-15 21:13:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:13:21 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 50268 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:13:21 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-15 21:13:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 21:13:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:13:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:13:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:13:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:13:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 21:13:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d58136
2025-06-15 21:13:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 21:13:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 21:13:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 21:13:28 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:13:42 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:13:42 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:13:42 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:13:42 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:13:42 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6fff46bf
2025-06-15 21:13:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 21:13:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 21:13:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 21:13:49 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 21:13:49 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-15 21:13:52 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 34.102 seconds (process running for 34.852)
2025-06-15 21:13:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:13:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:13:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-15 21:13:52 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:14:32 [metrics-collector-sync-job-thread-1] WARN  i.m.core.instrument.MeterRegistry - This FunctionCounter has been already registered (MeterId{name='dubbo.application.info.total', tags=[tag(application.module.id=1.1),tag(application.name=ruoyi-gen),tag(application.version=3.3.4),tag(git.commit.id=82a03c3721c68e20226e7cd4f3031b74a8263e41),tag(hostname=奚翔),tag(ip=*************)]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-06-15 21:16:02 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:16:02 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[30ms]
2025-06-15 21:24:23 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:23 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][执行耗时:00:00:00.006]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][封装耗时:00:00:00.010][封装行数:27]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
param2=BASE TABLE(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][tables][catalog:null][schema:SCHEMA:ry-cloud][pattern:wms_warehouse][type:1][result:1][执行耗时:00:00:00.097]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][action:select][执行耗时:00:00:00.006]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][action:select][封装耗时:00:00:00.002][封装行数:14]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:14][根据metadata解析:0][根据系统表查询:14][根据驱动内置接口补充:0][执行耗时:00:00:00.020]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:14][根据metadata解析:0][根据系统表查询:14][根据根据驱动内置接口补充:0][执行耗时:00:00:00.020]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868472-25703517][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.STATISTICS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY SEQ_IN_INDEX ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868472-25703517][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868472-25703517][thread:689][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE (CONSTRAINT_SCHEMA = ? AND TABLE_NAME LIKE ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][constraints][result:1][执行耗时:00:00:00.005]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][action:select][cmd:
show create table `ry-cloud`.wms_warehouse
]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][table ddl][table:wms_warehouse][result:1][执行耗时:00:00:00.003]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][cmd:
SHOW CREATE TABLE `ry-cloud`.wms_warehouse
]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.PARTITIONS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][partition][table:wms_warehouse][result:true][执行耗时:00:00:00.008]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-15 21:24:33 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:33 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][执行耗时:00:00:00.004]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][封装耗时:00:00:00.002][封装行数:27]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
param2=BASE TABLE(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][tables][catalog:null][schema:SCHEMA:ry-cloud][pattern:wms_warehouse][type:1][result:1][执行耗时:00:00:00.013]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:13]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:13][根据metadata解析:0][根据系统表查询:13][根据驱动内置接口补充:0][执行耗时:00:00:00.007]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:13][根据metadata解析:0][根据系统表查询:13][根据根据驱动内置接口补充:0][执行耗时:00:00:00.007]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904595-67913753][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.STATISTICS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY SEQ_IN_INDEX ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904595-67913753][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904595-67913753][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE (CONSTRAINT_SCHEMA = ? AND TABLE_NAME LIKE ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][constraints][result:1][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][action:select][cmd:
show create table `ry-cloud`.wms_warehouse
]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][table ddl][table:wms_warehouse][result:1][执行耗时:00:00:00.003]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][cmd:
SHOW CREATE TABLE `ry-cloud`.wms_warehouse
]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.PARTITIONS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][partition][table:wms_warehouse][result:true][执行耗时:00:00:00.005]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:25:30 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:25:30 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-15 21:26:01 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:26:01 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-15 21:26:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:26:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:27:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:27:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:27:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:27:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 21:29:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:29:28 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 50072 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:29:28 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-15 21:29:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 21:29:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:29:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:29:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:29:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:29:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 21:29:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e5c04a4
2025-06-15 21:29:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 21:29:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 21:29:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 21:29:36 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:29:50 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:29:50 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:29:51 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:29:51 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:29:51 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6fff46bf
2025-06-15 21:29:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 21:29:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 21:29:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 21:29:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 21:29:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-15 21:30:02 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:30:03 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 37.594 seconds (process running for 38.169)
2025-06-15 21:30:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:30:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:30:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-15 21:30:17 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:30:18 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[128ms]
2025-06-15 21:30:40 [metrics-collector-sync-job-thread-1] WARN  i.m.core.instrument.MeterRegistry - This FunctionCounter has been already registered (MeterId{name='dubbo.application.info.total', tags=[tag(application.module.id=1.1),tag(application.name=ruoyi-gen),tag(application.version=3.3.4),tag(git.commit.id=82a03c3721c68e20226e7cd4f3031b74a8263e41),tag(hostname=奚翔),tag(ip=*************)]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-06-15 21:47:00 [XNIO-1 task-2] ERROR c.a.c.n.d.NacosDiscoveryClient - get service name from nacos server failed.
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:70)
	at com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient.getServices(NacosDiscoveryClient.java:80)
	at org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClient.getServices(CompositeDiscoveryClient.java:68)
	at org.springframework.cloud.client.discovery.health.DiscoveryClientHealthIndicator.health(DiscoveryClientHealthIndicator.java:73)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.getHealth(HealthEndpointWebExtension.java:94)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.getHealth(HealthEndpointWebExtension.java:47)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.health(HealthEndpointWebExtension.java:80)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.health(HealthEndpointWebExtension.java:69)
	at jdk.internal.reflect.GeneratedMethodAccessor201.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$ServletWebOperationAdapter.handle(AbstractWebMvcEndpointHandlerMapping.java:327)
	at org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$OperationHandler.handle(AbstractWebMvcEndpointHandlerMapping.java:434)
	at jdk.internal.reflect.GeneratedMethodAccessor192.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:143)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:143)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:31)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:07 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:33:48 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:48 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:49 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:49 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:50 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:50 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:51 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:51 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:52 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:52 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:52 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:52 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unsubscribe service org.dromara.system.api.RemoteLogService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=50072&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:33:53 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gen', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=[240e:3a1:bc85:beb1:187:bb4c:9c3e:8a7b], username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=9202, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:45:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:45:50 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 35696 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:45:50 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-15 22:45:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 22:45:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:45:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:45:55 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:45:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:45:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 22:45:56 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7266f136
2025-06-15 22:45:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 22:45:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 22:45:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 22:45:57 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:46:11 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:46:11 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:46:11 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:46:11 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:46:11 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@58658f63
2025-06-15 22:46:17 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 22:46:17 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 22:46:17 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 22:46:17 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 22:46:17 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-15 22:46:21 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 34.132 seconds (process running for 34.762)
2025-06-15 22:46:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:46:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 22:46:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-15 22:46:21 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 22:47:01 [metrics-collector-sync-job-thread-1] WARN  i.m.core.instrument.MeterRegistry - This FunctionCounter has been already registered (MeterId{name='dubbo.application.info.total', tags=[tag(application.module.id=1.1),tag(application.name=ruoyi-gen),tag(application.version=3.3.4),tag(git.commit.id=82a03c3721c68e20226e7cd4f3031b74a8263e41),tag(hostname=奚翔),tag(ip=*************)]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
