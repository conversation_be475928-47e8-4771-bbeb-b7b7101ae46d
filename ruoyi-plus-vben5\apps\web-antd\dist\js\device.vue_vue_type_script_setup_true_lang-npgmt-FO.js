var c=(n,o,t)=>new Promise((s,r)=>{var f=e=>{try{a(t.next(e))}catch(i){r(i)}},d=e=>{try{a(t.throw(e))}catch(i){r(i)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(f,d);a((t=t.apply(n,o)).next())});import{d as p}from"./api-CwjVPMpk.js";import{u as l,_ as m}from"./use-echarts-CF-NZzbo.js";import{d as _,p as h,v as u,h as v,o as w,b as g}from"../jse/index-index-C-MnMZEz.js";const B=_({name:"Device",__name:"device",setup(n){const o=h(),{renderEcharts:t}=l(o);return u(()=>c(null,null,function*(){const s=yield p();t({legend:{left:"left",orient:"vertical"},series:[{data:s,emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)",shadowOffsetX:0}},label:{formatter:"{b}: {c} - ({d}%)",show:!0},radius:"50%",type:"pie"}],title:{left:"center",text:"使用设备占比"},tooltip:{trigger:"item"}})})),(s,r)=>(w(),v(g(m),{ref_key:"deviceRef",ref:o,height:"720px",width:"100%"},null,512))}});export{B as _};
