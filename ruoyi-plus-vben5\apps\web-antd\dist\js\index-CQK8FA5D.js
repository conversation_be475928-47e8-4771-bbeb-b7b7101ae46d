var E=Object.defineProperty;var N=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var $=(n,e,i)=>e in n?E(n,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[e]=i,V=(n,e)=>{for(var i in e||(e={}))j.call(e,i)&&$(n,i,e[i]);if(N)for(var i of N(e))G.call(e,i)&&$(n,i,e[i]);return n};var c=(n,e,i)=>new Promise((y,u)=>{var f=m=>{try{g(i.next(m))}catch(r){u(r)}},C=m=>{try{g(i.throw(m))}catch(r){u(r)}},g=m=>m.done?y(m.value):Promise.resolve(m.value).then(f,C);g((i=i.apply(n,e)).next())});import{ar as L,as as _,$ as H,an as J}from"./bootstrap-DCMzVRvD.js";import{v as K}from"./vxe-table-DzEj5Fop.js";import{p as B,d as D,a as Q}from"./index-DCFckLr6.js";import{_ as X}from"./category-tree.vue_vue_type_script_setup_true_lang-DGRUd3to.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";/* empty css                                                          */import{_ as Y}from"./flow-info-modal.vue_vue_type_script_setup_true_lang-CB-fzQzq.js";import{_ as Z}from"./options-tag.vue_vue_type_script_setup_true_lang-k3ySxERw.js";import{r as tt}from"./render-BxXtQdeV.js";import{a as et}from"./constant-CNx795op.js";import{a as l,d as ot,p as M,l as it,S as nt,h as T,o as h,w as s,j as k,b as a,c as at,f as rt,k as v,T as lt,t as st}from"../jse/index-index-C-MnMZEz.js";import{_ as pt}from"./instance-invalid-modal.vue_vue_type_script_setup_true_lang-B4-g38s8.js";import{_ as mt}from"./instance-variable-modal.vue_vue_type_script_setup_true_lang-BLC1BZuW.js";import dt from"./index-BeyziwLP.js";import{_ as ct}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{u as ut}from"./use-vxe-grid-BC7vZzEr.js";import{u as w}from"./use-modal-CeMSCP2m.js";import{P as ft}from"./index-DNdMANjv.js";import{g as gt}from"./get-popup-container-P4S1sr5h.js";import{a as vt}from"./Group-oWwucTzK.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-B4NcjlQn.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-BLwHKR_M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./index-CHpIOV4R.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./relativeTime-DEqmspR6.js";import"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import"./index-BxBCzu2M.js";import"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import"./index-CZhogUxH.js";import"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import"./user-select-modal-9zMWfXzj.js";import"./index-ocPq22VW.js";import"./helper-Bc7QQ92Q.js";import"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./x-Bfkqqjgb.js";import"./index-BELOxkuV.js";import"./index-qvRUEWLR.js";import"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import"./index-i2_yEmR1.js";import"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import"./index-D59rZjD-.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BIMmoqOy.js";import"./move-DLDqWE9R.js";import"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import"./rotate-cw-DzZTu9nW.js";import"./index-B6iusSRX.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-Ollxi7Rl.js";import"./dict-BLkXAGS5.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./Checkbox-DRV8G-PI.js";const _t=()=>[{component:"Input",label:"任务名称",fieldName:"nodeName"},{component:"Input",label:"流程名称",fieldName:"flowName"},{component:"Input",label:"流程编码",fieldName:"flowCode"}],yt=[{type:"checkbox",width:60},{field:"flowName",title:"流程名称",minWidth:150},{field:"nodeName",title:"任务名称",minWidth:150},{field:"flowCode",title:"流程编码",minWidth:150},{field:"createByName",title:"申请人",minWidth:150},{field:"version",title:"版本号",minWidth:150,formatter:({cellValue:n})=>`V${n}.0`},{field:"activityStatus",title:"状态",minWidth:100,slots:{default:({row:n})=>{const e=n.activityStatus;return l(Z,{options:et,value:e},null)}}},{field:"flowStatus",title:"流程状态",minWidth:100,slots:{default:({row:n})=>tt(n.flowStatus,L.WF_BUSINESS_STATUS)}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:200}],Ct={class:"flex h-full gap-[8px]"},bt={class:"flex flex-col"},ht={key:0},Ze=ot({__name:"index",setup(n){const e=M([]),i={schema:_t(),commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",handleReset:()=>c(null,null,function*(){e.value=[];const{formApi:o,reload:t}=r;yield o.resetForm();const p=o.form.values;o.setLatestSubmissionValues(p),yield t(p)})},y=[{label:"运行中",value:"process_running"},{label:"已完成",value:"process_completed"}];let u=B;const f=M("process_running");function C(o){return c(this,null,function*(){const{value:t}=o.target;switch(t){case"process_completed":{u=Q;break}case"process_running":{u=B;break}}yield r.reload()})}const g={checkboxConfig:{highlight:!0,reserve:!0,trigger:"default"},columns:yt,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(p,...U)=>c(null,[p,...U],function*({page:o},t={}){return e.value.length===1?t.category=e.value[0]:Reflect.deleteProperty(t,"category"),yield u(V({pageNum:o.currentPage,pageSize:o.pageSize},t))})}},headerCellConfig:{height:44},cellConfig:{height:66},rowConfig:{keyField:"id"},id:"workflow-definition-index"},[m,r]=ut({formOptions:i,gridOptions:g}),[W,x]=w({connectedComponent:pt});function z(o){return c(this,null,function*(){x.setData({id:o.id}),x.open()})}function A(o){return c(this,null,function*(){yield D(o.id),yield r.query()})}function R(){const t=r.grid.getCheckboxRecords().map(p=>p.id);J.confirm({title:"提示",okType:"danger",content:`确认删除选中的${t.length}条记录吗？`,onOk:()=>c(null,null,function*(){yield D(t),yield r.query()})})}const[P,S]=w({connectedComponent:mt});function F(o){S.setData({record:o.variable}),S.open()}const[O,I]=w({connectedComponent:Y});function q(o){console.log(o),I.setData({businessId:o.businessId}),I.open()}return(o,t)=>{const p=it("a-button"),U=nt("access");return h(),T(a(ct),{"auto-content-height":!0},{default:s(()=>[k("div",Ct,[l(X,{"select-code":e.value,"onUpdate:selectCode":t[0]||(t[0]=d=>e.value=d),class:"w-[260px]",onReload:t[1]||(t[1]=()=>a(r).reload()),onSelect:t[2]||(t[2]=()=>a(r).reload())},null,8,["select-code"]),l(a(m),{class:"flex-1 overflow-hidden"},{"toolbar-actions":s(()=>[l(a(vt),{value:f.value,"onUpdate:value":t[3]||(t[3]=d=>f.value=d),options:y,"button-style":"solid","option-type":"button",onChange:C},null,8,["value"])]),"toolbar-tools":s(()=>[l(a(dt),null,{default:s(()=>[lt((h(),T(p,{disabled:!a(K)(a(r)),danger:"",type:"primary",onClick:R},{default:s(()=>[v(st(a(H)("pages.common.delete")),1)]),_:1},8,["disabled"])),[[U,["system:user:remove"],"code"]])]),_:1})]),action:s(({row:d})=>[k("div",bt,[f.value==="process_running"?(h(),at("div",ht,[l(p,{danger:"",size:"small",type:"link",onClick:_(b=>z(d),["stop"])},{default:s(()=>t[6]||(t[6]=[v(" 作废流程 ")])),_:2,__:[6]},1032,["onClick"]),l(a(ft),{"get-popup-container":a(gt),placement:"left",title:"确认删除？",onConfirm:b=>A(d)},{default:s(()=>[l(p,{danger:"",size:"small",type:"link",onClick:t[4]||(t[4]=_(()=>{},["stop"]))},{default:s(()=>t[7]||(t[7]=[v(" 删除流程 ")])),_:1,__:[7]})]),_:2},1032,["get-popup-container","onConfirm"])])):rt("",!0),k("div",null,[l(p,{size:"small",type:"link",onClick:_(b=>q(d),["stop"])},{default:s(()=>t[8]||(t[8]=[v(" 流程预览 ")])),_:2,__:[8]},1032,["onClick"]),l(p,{size:"small",type:"link",onClick:_(b=>F(d),["stop"])},{default:s(()=>t[9]||(t[9]=[v(" 变量查看 ")])),_:2,__:[9]},1032,["onClick"])])])]),_:1})]),l(a(W),{onReload:t[5]||(t[5]=()=>a(r).reload())}),l(a(P)),l(a(O))]),_:1})}}});export{Ze as default};
