var c=(p,s,o)=>new Promise((l,n)=>{var a=e=>{try{t(o.next(e))}catch(r){n(r)}},i=e=>{try{t(o.throw(e))}catch(r){n(r)}},t=e=>e.done?l(e.value):Promise.resolve(e.value).then(a,i);t((o=o.apply(p,s)).next())});import"./vxe-table-DzEj5Fop.js";import{c as d,o as _,f as b}from"./data-DW-5ziL3.js";import{P as k}from"./index-DNdMANjv.js";import{d as C,l as g,c as x,o as y,a as f,w as m,b as u,k as v}from"../jse/index-index-C-MnMZEz.js";import{u as w}from"./use-vxe-grid-BC7vZzEr.js";const L=C({__name:"online-device",setup(p){const s={columns:d,keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:()=>c(null,null,function*(){return yield _()})}},rowConfig:{keyField:"tokenId"}},[o,l]=w({gridOptions:s});function n(a){return c(this,null,function*(){yield b(a.tokenId),yield l.query()})}return(a,i)=>{const t=g("a-button");return y(),x("div",null,[f(u(o),{"table-title":"我的在线设备"},{action:m(({row:e})=>[f(u(k),{title:`确认强制下线[${e.userName}]?`,placement:"left",onConfirm:r=>n(e)},{default:m(()=>[f(t,{danger:"",size:"small",type:"link"},{default:m(()=>i[0]||(i[0]=[v("强制下线")])),_:1,__:[0]})]),_:2},1032,["title","onConfirm"])]),_:1})])}}});export{L as _};
