import{j as J,aD as U,e as X,b4 as G,bd as Q,p as Y,be as Z,bf as ee,aJ as oe,c as ne,bg as te,g as T,b5 as ae,_ as r,b6 as le,aS as se,bh as j,ak as ie,bi as re,b7 as ce,bj as I,b8 as f,bk as ue,aM as pe}from"./bootstrap-DCMzVRvD.js";import{P as de}from"./index-qvRUEWLR.js";import{d as fe,p as me,$ as ve,B as z,a as i}from"../jse/index-index-C-MnMZEz.js";const ge=o=>{const{componentCls:l,iconCls:t,zIndexPopup:a,colorText:s,colorWarning:y,marginXS:p,fontSize:c,fontWeightStrong:b,lineHeight:d}=o;return{[l]:{zIndex:a,[`${l}-inner-content`]:{color:s},[`${l}-message`]:{position:"relative",marginBottom:p,color:s,fontSize:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${l}-message-icon ${t}`]:{color:y,fontSize:c,flex:"none",lineHeight:1,paddingTop:(Math.round(c*d)-c)/2},"&-title":{flex:"auto",marginInlineStart:p},"&-title-only":{fontWeight:b}},[`${l}-description`]:{position:"relative",marginInlineStart:c+p,marginBottom:p,color:s,fontSize:c},[`${l}-buttons`]:{textAlign:"end",button:{marginInlineStart:p}}}}},Ce=J("Popconfirm",o=>ge(o),o=>{const{zIndexPopupBase:l}=o;return{zIndexPopup:l+60}});var ye=function(o,l){var t={};for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&l.indexOf(a)<0&&(t[a]=o[a]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(o);s<a.length;s++)l.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(o,a[s])&&(t[a[s]]=o[a[s]]);return t};const be=()=>r(r({},ce()),{prefixCls:String,content:f(),title:f(),description:f(),okType:ue("primary"),disabled:{type:Boolean,default:!1},okText:f(),cancelText:f(),icon:f(),okButtonProps:I(),cancelButtonProps:I(),showCancel:{type:Boolean,default:!0},onConfirm:Function,onCancel:Function}),Pe=fe({compatConfig:{MODE:3},name:"APopconfirm",inheritAttrs:!1,props:X(be(),r(r({},le()),{trigger:"click",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0,okType:"primary",disabled:!1})),slots:Object,setup(o,l){let{slots:t,emit:a,expose:s,attrs:y}=l;const p=me();G(o.visible===void 0),s({getPopupDomNode:()=>{var e,n;return(n=(e=p.value)===null||e===void 0?void 0:e.getPopupDomNode)===null||n===void 0?void 0:n.call(e)}});const[c,b]=Q(!1,{value:ve(o,"open")}),d=(e,n)=>{o.open===void 0&&b(e),a("update:open",e),a("openChange",e,n)},D=e=>{d(!1,e)},O=e=>{var n;return(n=o.onConfirm)===null||n===void 0?void 0:n.call(o,e)},A=e=>{var n;d(!1,e),(n=o.onCancel)===null||n===void 0||n.call(o,e)},E=e=>{e.keyCode===pe.ESC&&c&&d(!1,e)},R=e=>{const{disabled:n}=o;n||d(e)},{prefixCls:u,getPrefixCls:S}=Y("popconfirm",o),F=z(()=>S()),L=z(()=>S("btn")),[M]=Ce(u),[h]=Z("Popconfirm",ee.Popconfirm),V=()=>{var e,n,m,v,g;const{okButtonProps:C,cancelButtonProps:P,title:x=(e=t.title)===null||e===void 0?void 0:e.call(t),description:k=(n=t.description)===null||n===void 0?void 0:n.call(t),cancelText:W=(m=t.cancel)===null||m===void 0?void 0:m.call(t),okText:H=(v=t.okText)===null||v===void 0?void 0:v.call(t),okType:_,icon:B=((g=t.icon)===null||g===void 0?void 0:g.call(t))||i(se,null,null),showCancel:K=!0}=o,{cancelButton:$,okButton:w}=t,N=r({onClick:A,size:"small"},P),q=r(r(r({onClick:O},j(_)),{size:"small"}),C);return i("div",{class:`${u.value}-inner-content`},[i("div",{class:`${u.value}-message`},[B&&i("span",{class:`${u.value}-message-icon`},[B]),i("div",{class:[`${u.value}-message-title`,{[`${u.value}-message-title-only`]:!!k}]},[x])]),k&&i("div",{class:`${u.value}-description`},[k]),i("div",{class:`${u.value}-buttons`},[K?$?$(N):i(ie,N,{default:()=>[W||h.value.cancelText]}):null,w?w(q):i(re,{buttonProps:r(r({size:"small"},j(_)),C),actionFn:O,close:D,prefixCls:L.value,quitOnNullishReturnValue:!0,emitEvent:!0},{default:()=>[H||h.value.okText]})])])};return()=>{var e;const{placement:n,overlayClassName:m,trigger:v="click"}=o,g=ye(o,["placement","overlayClassName","trigger"]),C=oe(g,["title","content","cancelText","okText","onUpdate:open","onConfirm","onCancel","prefixCls"]),P=ne(u.value,m);return M(i(de,T(T(T({},C),y),{},{trigger:v,placement:n,onOpenChange:R,open:c.value,overlayClassName:P,transitionName:ae(F.value,"zoom-big",o.transitionName),ref:p,"data-popover-inject":!0}),{default:()=>[te(((e=t.default)===null||e===void 0?void 0:e.call(t))||[],{onKeydown:x=>{E(x)}},!1)],content:V}))}}}),Oe=U(Pe);export{Oe as P};
