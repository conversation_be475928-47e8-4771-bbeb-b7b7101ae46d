{"doc": " 社会化关系服务\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": " 根据 authId 查询用户授权信息\n\n @param authId 认证id\n @return 授权信息\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteSocialBo"], "doc": " 查询列表\n\n @param bo 社会化关系业务对象\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteSocialBo"], "doc": " 保存社会化关系\n\n @param bo 社会化关系业务对象\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteSocialBo"], "doc": " 更新社会化关系\n\n @param bo 社会化关系业务对象\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": " 删除社会化关系\n\n @param socialId 社会化关系ID\n @return 结果\n"}], "constructors": []}