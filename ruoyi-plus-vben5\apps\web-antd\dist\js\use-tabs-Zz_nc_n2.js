var V=Object.defineProperty,F=Object.defineProperties;var z=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var _=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable;var K=(e,t,a)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,d=(e,t)=>{for(var a in t||(t={}))_.call(t,a)&&K(e,a,t[a]);if(x)for(var a of x(t))A.call(t,a)&&K(e,a,t[a]);return e},w=(e,t)=>F(e,z(t));var B=(e,t)=>{var a={};for(var s in e)_.call(e,s)&&t.indexOf(s)<0&&(a[s]=e[s]);if(e!=null&&x)for(var s of x(e))t.indexOf(s)<0&&A.call(e,s)&&(a[s]=e[s]);return a};var c=(e,t,a)=>new Promise((s,n)=>{var i=b=>{try{r(a.next(b))}catch(u){n(u)}},o=b=>{try{r(a.throw(b))}catch(u){n(u)}},r=b=>b.done?s(b.value):Promise.resolve(b.value).then(i,o);r((a=a.apply(e,t)).next())});import{bo as j,aB as G,av as H}from"./bootstrap-DCMzVRvD.js";import{a6 as J,a7 as Q,a8 as X,a0 as Y,A as Z}from"../jse/index-index-C-MnMZEz.js";const $=j("core-tabbar",{actions:{_bulkCloseByKeys(e){return c(this,null,function*(){const t=new Set(e);this.tabs=this.tabs.filter(a=>!t.has(h(a))),yield this.updateCacheTabs()})},_close(e){if(l(e))return;const t=this.tabs.findIndex(a=>T(a,e));t!==-1&&this.tabs.splice(t,1)},_goToDefaultTab(e){return c(this,null,function*(){if(this.getTabs.length<=0)return;const t=this.getTabs[0];t&&(yield this._goToTab(t,e))})},_goToTab(e,t){return c(this,null,function*(){const{params:a,path:s,query:n}=e,i={params:a||{},path:s,query:n||{}};yield t.replace(i)})},addTab(e){var s,n;let t=ee(e);if(t.key||(t.key=m(e)),!te(t))return t;const a=this.tabs.findIndex(i=>T(i,t));if(a===-1){const i=Y.tabbar.maxCount,o=(n=(s=e==null?void 0:e.meta)==null?void 0:s.maxNumOfOpenTab)!=null?n:-1;if(o>0&&this.tabs.filter(r=>r.name===e.name).length>=o){const r=this.tabs.findIndex(b=>b.name===e.name);r!==-1&&this.tabs.splice(r,1)}else if(i>0&&this.tabs.length>=i){const r=this.tabs.findIndex(b=>!Reflect.has(b.meta,"affixTab")||!b.meta.affixTab);r!==-1&&this.tabs.splice(r,1)}this.tabs.push(t)}else{const i=Z(this.tabs)[a],o=w(d(d({},i),t),{meta:d(d({},i==null?void 0:i.meta),t.meta)});if(i){const r=i.meta;Reflect.has(r,"affixTab")&&(o.meta.affixTab=r.affixTab),Reflect.has(r,"newTabTitle")&&(o.meta.newTabTitle=r.newTabTitle)}t=o,this.tabs.splice(a,1,o)}return this.updateCacheTabs(),t},closeAllTabs(e){return c(this,null,function*(){const t=this.tabs.filter(a=>l(a));this.tabs=t.length>0?t:[...this.tabs].splice(0,1),yield this._goToDefaultTab(e),this.updateCacheTabs()})},closeLeftTabs(e){return c(this,null,function*(){const t=this.tabs.findIndex(n=>T(n,e));if(t<1)return;const a=this.tabs.slice(0,t),s=[];for(const n of a)l(n)||s.push(n.key);yield this._bulkCloseByKeys(s)})},closeOtherTabs(e){return c(this,null,function*(){const t=this.tabs.map(s=>h(s)),a=[];for(const s of t)if(s!==h(e)){const n=this.tabs.find(i=>h(i)===s);if(!n)continue;l(n)||a.push(n.key)}yield this._bulkCloseByKeys(a)})},closeRightTabs(e){return c(this,null,function*(){const t=this.tabs.findIndex(a=>T(a,e));if(t!==-1&&t<this.tabs.length-1){const a=this.tabs.slice(t+1),s=[];for(const n of a)l(n)||s.push(n.key);yield this._bulkCloseByKeys(s)}})},closeTab(e,t){return c(this,null,function*(){const{currentRoute:a}=t;if(m(a.value)!==h(e)){this._close(e),this.updateCacheTabs();return}const s=this.getTabs.findIndex(o=>h(o)===m(a.value)),n=this.getTabs[s-1],i=this.getTabs[s+1];i?(this._close(e),yield this._goToTab(i,t)):n?(this._close(e),yield this._goToTab(n,t)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(e,t){return c(this,null,function*(){const a=decodeURIComponent(e),s=this.tabs.findIndex(i=>h(i)===a);if(s===-1)return;const n=this.tabs[s];n&&(yield this.closeTab(n,t))})},getTabByKey(e){return this.getTabs.find(t=>h(t)===e)},openTabInNewWindow(e){return c(this,null,function*(){X(e.fullPath||e.path)})},pinTab(e){return c(this,null,function*(){var i;const t=this.tabs.findIndex(o=>T(o,e));if(t===-1)return;const a=this.tabs[t];e.meta.affixTab=!0,e.meta.title=(i=a==null?void 0:a.meta)==null?void 0:i.title,this.tabs.splice(t,1,e);const n=this.tabs.filter(o=>l(o)).findIndex(o=>T(o,e));yield this.sortTabs(t,n)})},refresh(e){return c(this,null,function*(){if(typeof e=="string")return yield this.refreshByName(e);const{currentRoute:t}=e,{name:a}=t.value;this.excludeCachedTabs.add(a),this.renderRouteView=!1,J(),yield new Promise(s=>setTimeout(s,200)),this.excludeCachedTabs.delete(a),this.renderRouteView=!0,Q()})},refreshByName(e){return c(this,null,function*(){this.excludeCachedTabs.add(e),yield new Promise(t=>setTimeout(t,200)),this.excludeCachedTabs.delete(e)})},resetTabTitle(e){return c(this,null,function*(){var a;if((a=e==null?void 0:e.meta)!=null&&a.newTabTitle)return;const t=this.tabs.find(s=>T(s,e));t&&(t.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(e){for(const t of e)t.meta.affixTab=!0,this.addTab(ae(t))},setMenuList(e){this.menuList=e},setTabTitle(e,t){return c(this,null,function*(){const a=this.tabs.find(s=>T(s,e));a&&(a.meta.newTabTitle=t,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(e,t){return c(this,null,function*(){const a=this.tabs[e];a&&(this.tabs.splice(e,1),this.tabs.splice(t,0,a),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(e){return c(this,null,function*(){var a,s;yield((s=(a=e==null?void 0:e.meta)==null?void 0:a.affixTab)!=null?s:!1)?this.unpinTab(e):this.pinTab(e)})},unpinTab(e){return c(this,null,function*(){var i;const t=this.tabs.findIndex(o=>T(o,e));if(t===-1)return;const a=this.tabs[t];e.meta.affixTab=!1,e.meta.title=(i=a==null?void 0:a.meta)==null?void 0:i.title,this.tabs.splice(t,1,e);const n=this.tabs.filter(o=>l(o)).length;yield this.sortTabs(t,n)})},updateCacheTabs(){return c(this,null,function*(){var t;const e=new Set;for(const a of this.tabs){if(!((t=a.meta)==null?void 0:t.keepAlive))continue;(a.matched||[]).forEach((i,o)=>{o>0&&e.add(i.name)});const n=a.name;e.add(n)}this.cachedTabs=e})}},getters:{affixTabs(){return this.tabs.filter(t=>l(t)).sort((t,a)=>{var i,o,r,b;const s=(o=(i=t.meta)==null?void 0:i.affixTabOrder)!=null?o:0,n=(b=(r=a.meta)==null?void 0:r.affixTabOrder)!=null?b:0;return s-n})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getMenuList(){return this.menuList},getTabs(){const e=this.tabs.filter(t=>!l(t));return[...this.affixTabs,...e].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,menuList:["close","affix","maximize","reload","open-in-new-window","close-left","close-right","close-other","close-all"],renderRouteView:!0,tabs:[],updateTime:Date.now()})});function ee(e){if(!e)return e;const n=e,{matched:t,meta:a}=n,s=B(n,["matched","meta"]);return w(d({},s),{matched:t?t.map(i=>({meta:i.meta,name:i.name,path:i.path})):void 0,meta:w(d({},a),{newTabTitle:a.newTabTitle})})}function l(e){var t,a;return(a=(t=e==null?void 0:e.meta)==null?void 0:t.affixTab)!=null?a:!1}function te(e){var a;const t=(a=e==null?void 0:e.matched)!=null?a:[];return!e.meta.hideInTab&&t.every(s=>!s.meta.hideInTab)}function m(e){const{fullPath:t,path:a,meta:{fullPathKey:s}={},query:n={}}=e,i=Array.isArray(n.pageKey)?n.pageKey[0]:n.pageKey;let o;i?o=i:o=s===!1?a:t!=null?t:a;try{return decodeURIComponent(o)}catch(r){return o}}function h(e){var t;return(t=e.key)!=null?t:m(e)}function T(e,t){return h(e)===h(t)}function ae(e){return{meta:e.meta,name:e.name,path:e.path,key:m(e)}}function oe(){const e=G(),t=H(),a=$();function s(f){return c(this,null,function*(){yield a.closeLeftTabs(f||t)})}function n(){return c(this,null,function*(){yield a.closeAllTabs(e)})}function i(f){return c(this,null,function*(){yield a.closeRightTabs(f||t)})}function o(f){return c(this,null,function*(){yield a.closeOtherTabs(f||t)})}function r(f){return c(this,null,function*(){yield a.closeTab(f||t,e)})}function b(f){return c(this,null,function*(){yield a.pinTab(f||t)})}function u(f){return c(this,null,function*(){yield a.unpinTab(f||t)})}function S(f){return c(this,null,function*(){yield a.toggleTabPin(f||t)})}function v(f){return c(this,null,function*(){yield a.refresh(f||e)})}function O(f){return c(this,null,function*(){yield a.openTabInNewWindow(f||t)})}function L(f){return c(this,null,function*(){yield a.closeTabByKey(f,e)})}function P(f){return c(this,null,function*(){a.setUpdateTime(),yield a.setTabTitle(t,f)})}function N(){return c(this,null,function*(){a.setUpdateTime(),yield a.resetTabTitle(t)})}function D(f=t){var R;const y=a.getTabs,k=a.affixTabs,g=y.findIndex(W=>W.path===f.path),C=y.length<=1,{meta:I}=f,E=(R=I==null?void 0:I.affixTab)!=null?R:!1,p=t.path===f.path,U=g===0||g-k.length<=0||!p,q=!p||g===y.length-1,M=C||!p||y.length-k.length<=1;return{disabledCloseAll:C,disabledCloseCurrent:!!E||C,disabledCloseLeft:U,disabledCloseOther:M,disabledCloseRight:q,disabledRefresh:!p}}return{closeAllTabs:n,closeCurrentTab:r,closeLeftTabs:s,closeOtherTabs:o,closeRightTabs:i,closeTabByKey:L,getTabDisableState:D,openTabInNewWindow:O,pinTab:b,refreshTab:v,resetTabTitle:N,setTabTitle:P,toggleTabPin:S,unpinTab:u}}export{$ as a,m as g,oe as u};
