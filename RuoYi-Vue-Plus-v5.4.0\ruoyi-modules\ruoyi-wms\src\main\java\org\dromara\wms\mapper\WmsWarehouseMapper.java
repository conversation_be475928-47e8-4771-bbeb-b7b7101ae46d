package org.dromara.wms.mapper;

import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 仓库列表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface WmsWarehouseMapper extends BaseMapperPlus<WmsWarehouse, WmsWarehouseVo> {

    /**
     * 查询仓库关联的部门ID列表
     *
     * @param warehouseId 仓库ID
     * @return 部门ID列表
     */
    @Select("SELECT dept_id FROM ware_warehouse_dept WHERE warehouse_id = #{warehouseId}")
    List<Long> selectDeptIdsByWarehouseId(Long warehouseId);

    /**
     * 查询仓库关联的部门名称
     *
     * @param warehouseId 仓库ID
     * @return 部门名称，多个用逗号分隔
     */
    @Select("SELECT GROUP_CONCAT(d.dept_name) FROM ware_warehouse_dept wd " +
            "LEFT JOIN sys_dept d ON wd.dept_id = d.dept_id " +
            "WHERE wd.warehouse_id = #{warehouseId}")
    String selectDeptNamesByWarehouseId(Long warehouseId);

    /**
     * 查询用户可访问的仓库列表（基于部门权限）
     *
     * @param userId 用户ID
     * @return 仓库列表
     */
    @Select("SELECT DISTINCT w.warehouse_id, w.warehouse_name " +
            "FROM wms_warehouse w " +
            "INNER JOIN ware_warehouse_dept wd ON w.warehouse_id = wd.warehouse_id " +
            "INNER JOIN sys_user_role ur ON ur.user_id = #{userId} " +
            "INNER JOIN sys_role_dept rd ON ur.role_id = rd.role_id " +
            "WHERE wd.dept_id = rd.dept_id " +
            "AND w.tenant_id = #{tenantId} " +
            "ORDER BY w.warehouse_name")
    List<WmsWarehouseVo> selectUserAccessibleWarehouses(Long userId, String tenantId);

}
