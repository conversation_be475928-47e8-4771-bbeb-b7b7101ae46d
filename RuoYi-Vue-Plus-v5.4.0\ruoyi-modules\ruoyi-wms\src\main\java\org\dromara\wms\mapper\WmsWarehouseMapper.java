package org.dromara.wms.mapper;

import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 仓库列表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface WmsWarehouseMapper extends BaseMapperPlus<WmsWarehouse, WmsWarehouseVo> {

    /**
     * 查询仓库关联的部门ID列表
     *
     * @param warehouseId 仓库ID
     * @return 部门ID列表
     */
    @Select("SELECT dept_id FROM ware_warehouse_dept WHERE warehouse_id = #{warehouseId}")
    List<Long> selectDeptIdsByWarehouseId(Long warehouseId);

    /**
     * 查询仓库关联的部门名称
     *
     * @param warehouseId 仓库ID
     * @return 部门名称，多个用逗号分隔
     */
    @Select("SELECT GROUP_CONCAT(d.dept_name) FROM ware_warehouse_dept wd " +
            "LEFT JOIN sys_dept d ON wd.dept_id = d.dept_id " +
            "WHERE wd.warehouse_id = #{warehouseId}")
    String selectDeptNamesByWarehouseId(Long warehouseId);

}
