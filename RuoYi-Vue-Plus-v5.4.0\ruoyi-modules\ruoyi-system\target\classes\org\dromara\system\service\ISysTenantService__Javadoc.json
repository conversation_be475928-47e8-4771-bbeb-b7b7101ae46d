{"doc": " 租户Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询租户\n"}, {"name": "queryByTenantId", "paramTypes": ["java.lang.String"], "doc": " 基于租户ID查询租户\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询租户列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 查询租户列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 新增租户\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 修改租户\n"}, {"name": "updateTenantStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 修改租户状态\n"}, {"name": "checkTenantAllowed", "paramTypes": ["java.lang.String"], "doc": " 校验租户是否允许操作\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除租户信息\n"}, {"name": "checkCompanyNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 校验企业名称是否唯一\n"}, {"name": "checkAccountBalance", "paramTypes": ["java.lang.String"], "doc": " 校验账号余额\n"}, {"name": "checkExpireTime", "paramTypes": ["java.lang.String"], "doc": " 校验有效期\n"}, {"name": "syncTenantPackage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 同步租户套餐\n"}, {"name": "syncTenantDict", "paramTypes": [], "doc": " 同步租户字典\n"}], "constructors": []}