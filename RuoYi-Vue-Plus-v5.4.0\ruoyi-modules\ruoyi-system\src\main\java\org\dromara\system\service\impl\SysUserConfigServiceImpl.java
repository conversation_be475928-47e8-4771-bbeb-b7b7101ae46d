package org.dromara.system.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.SysUserConfig;
import org.dromara.system.mapper.SysUserConfigMapper;
import org.dromara.system.service.ISysUserConfigService;
import org.dromara.common.satoken.utils.LoginHelper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;

/**
 * 用户配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@RequiredArgsConstructor
@Service
public class SysUserConfigServiceImpl implements ISysUserConfigService {

    private final SysUserConfigMapper baseMapper;

    private static final String WAREHOUSE_CONFIG_KEY = "selected_warehouse";

    @Override
    public String getUserConfig(Long userId, String configKey, String tenantId) {
        return baseMapper.selectConfigValue(userId, configKey, tenantId);
    }

    @Override
    public void setUserConfig(Long userId, String configKey, String configValue, String tenantId) {
        LambdaQueryWrapper<SysUserConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysUserConfig::getUserId, userId)
                   .eq(SysUserConfig::getConfigKey, configKey)
                   .eq(SysUserConfig::getTenantId, tenantId);
        
        SysUserConfig existingConfig = baseMapper.selectOne(queryWrapper);
        
        if (existingConfig != null) {
            // 更新现有配置
            existingConfig.setConfigValue(configValue);
            baseMapper.updateById(existingConfig);
        } else {
            // 创建新配置
            SysUserConfig newConfig = new SysUserConfig();
            newConfig.setUserId(userId);
            newConfig.setConfigKey(configKey);
            newConfig.setConfigValue(configValue);
            newConfig.setTenantId(tenantId);
            baseMapper.insert(newConfig);
        }
    }

    @Override
    public Long getCurrentUserWarehouse() {
        Long userId = LoginHelper.getUserId();
        String tenantId = LoginHelper.getTenantId();
        String warehouseIdStr = getUserConfig(userId, WAREHOUSE_CONFIG_KEY, tenantId);
        
        if (StringUtils.isNotBlank(warehouseIdStr)) {
            try {
                return Long.parseLong(warehouseIdStr);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    @Override
    public void setCurrentUserWarehouse(Long warehouseId) {
        Long userId = LoginHelper.getUserId();
        String tenantId = LoginHelper.getTenantId();
        String warehouseIdStr = warehouseId != null ? warehouseId.toString() : null;
        setUserConfig(userId, WAREHOUSE_CONFIG_KEY, warehouseIdStr, tenantId);
    }

}
