declare const DictEnum: {
    readonly SYS_COMMON_STATUS: "sys_common_status";
    readonly SYS_DEVICE_TYPE: "sys_device_type";
    readonly SYS_GRANT_TYPE: "sys_grant_type";
    readonly SYS_NORMAL_DISABLE: "sys_normal_disable";
    readonly SYS_NOTICE_STATUS: "sys_notice_status";
    readonly SYS_NOTICE_TYPE: "sys_notice_type";
    readonly SYS_OPER_TYPE: "sys_oper_type";
    readonly SYS_OSS_ACCESS_POLICY: "oss_access_policy";
    readonly SYS_SHOW_HIDE: "sys_show_hide";
    readonly SYS_USER_SEX: "sys_user_sex";
    readonly SYS_YES_NO: "sys_yes_no";
    readonly WF_BUSINESS_STATUS: "wf_business_status";
    readonly WF_FORM_TYPE: "wf_form_type";
    readonly WF_TASK_STATUS: "wf_task_status";
};
type DictEnumKey = keyof typeof DictEnum;

declare const CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT = "--vben-content-height";
declare const CSS_VARIABLE_LAYOUT_CONTENT_WIDTH = "--vben-content-width";
declare const CSS_VARIABLE_LAYOUT_HEADER_HEIGHT = "--vben-header-height";
declare const CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT = "--vben-footer-height";
declare const ELEMENT_ID_MAIN_CONTENT = "__vben_main_content";
declare const DEFAULT_NAMESPACE = "vben";

declare const VBEN_GITHUB_URL = "https://github.com/vbenjs/vue-vben-admin";
declare const VBEN_DOC_URL = "https://doc.vben.pro";
declare const VBEN_LOGO_URL = "https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp";
declare const VBEN_PREVIEW_URL = "https://www.vben.pro";
declare const VBEN_ELE_PREVIEW_URL = "https://ele.vben.pro";
declare const VBEN_NAIVE_PREVIEW_URL = "https://naive.vben.pro";
declare const VBEN_ANT_PREVIEW_URL = "https://ant.vben.pro";

export { CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT, CSS_VARIABLE_LAYOUT_CONTENT_WIDTH, CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT, CSS_VARIABLE_LAYOUT_HEADER_HEIGHT, DEFAULT_NAMESPACE, DictEnum, ELEMENT_ID_MAIN_CONTENT, VBEN_ANT_PREVIEW_URL, VBEN_DOC_URL, VBEN_ELE_PREVIEW_URL, VBEN_GITHUB_URL, VBEN_LOGO_URL, VBEN_NAIVE_PREVIEW_URL, VBEN_PREVIEW_URL };
export type { DictEnumKey };
