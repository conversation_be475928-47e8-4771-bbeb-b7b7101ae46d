var E=Object.defineProperty;var D=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var M=(a,o,e)=>o in a?E(a,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[o]=e,$=(a,o)=>{for(var e in o||(o={}))I.call(o,e)&&M(a,e,o[e]);if(D)for(var e of D(o))B.call(o,e)&&M(a,e,o[e]);return a};var d=(a,o,e)=>new Promise((k,r)=>{var w=m=>{try{C(e.next(m))}catch(h){r(h)}},c=m=>{try{C(e.throw(m))}catch(h){r(h)}},C=m=>m.done?k(m.value):Promise.resolve(m.value).then(w,c);C((e=e.apply(a,o)).next())});import{as as T,an as N}from"./bootstrap-DCMzVRvD.js";import{v as O}from"./vxe-table-DzEj5Fop.js";import{d as z,e as Y,f as j,g as F}from"./index-D1nLcUEe.js";import{c as G}from"./download-UJak946_.js";import{q as L,c as W,_ as H}from"./config-modal.vue_vue_type_script_setup_true_lang-_L9rZFd8.js";import q from"./index-BeyziwLP.js";import{_ as J}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as K,l as S,S as Q,h as l,o as f,w as i,a as g,b as s,T as _,k as u,t as b}from"../jse/index-index-C-MnMZEz.js";import{u as U}from"./use-vxe-grid-BC7vZzEr.js";import{u as X}from"./use-modal-CeMSCP2m.js";import{P as Z}from"./index-DNdMANjv.js";import{g as ee}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./popup-D6rC6QBG.js";import"./dict-BLkXAGS5.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Se=K({__name:"index",setup(a){const o={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:L(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",fieldMappingTime:[["createTime",["params[beginTime]","params[endTime]"],["YYYY-MM-DD 00:00:00","YYYY-MM-DD 23:59:59"]]]},e={checkboxConfig:{highlight:!0,reserve:!0},columns:W,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(p,...x)=>d(null,[p,...x],function*({page:t},n={}){return yield z($({pageNum:t.currentPage,pageSize:t.pageSize},n))})}},rowConfig:{keyField:"configId"},id:"system-config-index"},[k,r]=U({formOptions:o,gridOptions:e}),[w,c]=X({connectedComponent:H});function C(){c.setData({}),c.open()}function m(t){return d(this,null,function*(){c.setData({id:t.configId}),c.open()})}function h(t){return d(this,null,function*(){yield Y([t.configId]),yield r.query()})}function V(){const n=r.grid.getCheckboxRecords().map(p=>p.configId);N.confirm({title:"提示",okType:"danger",content:`确认删除选中的${n.length}条记录吗？`,onOk:()=>d(null,null,function*(){yield Y(n),yield r.query()})})}function P(){G(j,"参数配置",r.formApi.form.values,{fieldMappingTime:o.fieldMappingTime})}function R(){return d(this,null,function*(){yield F(),yield r.query()})}return(t,n)=>{const p=S("a-button"),x=S("ghost-button"),y=Q("access");return f(),l(s(J),{"auto-content-height":!0},{default:i(()=>[g(s(k),{"table-title":"参数列表"},{"toolbar-tools":i(()=>[g(s(q),null,{default:i(()=>[g(p,{onClick:R},{default:i(()=>n[2]||(n[2]=[u(" 刷新缓存 ")])),_:1,__:[2]}),_((f(),l(p,{onClick:P},{default:i(()=>[u(b(t.$t("pages.common.export")),1)]),_:1})),[[y,["system:config:export"],"code"]]),_((f(),l(p,{disabled:!s(O)(s(r)),danger:"",type:"primary",onClick:V},{default:i(()=>[u(b(t.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[y,["system:config:remove"],"code"]]),_((f(),l(p,{type:"primary",onClick:C},{default:i(()=>[u(b(t.$t("pages.common.add")),1)]),_:1})),[[y,["system:config:add"],"code"]])]),_:1})]),action:i(({row:v})=>[g(s(q),null,{default:i(()=>[_((f(),l(x,{onClick:T(A=>m(v),["stop"])},{default:i(()=>[u(b(t.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[y,["system:config:edit"],"code"]]),g(s(Z),{"get-popup-container":s(ee),placement:"left",title:"确认删除？",onConfirm:A=>h(v)},{default:i(()=>[_((f(),l(x,{danger:"",onClick:n[0]||(n[0]=T(()=>{},["stop"]))},{default:i(()=>[u(b(t.$t("pages.common.delete")),1)]),_:1})),[[y,["system:config:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),g(s(w),{onReload:n[1]||(n[1]=v=>s(r).query())})]),_:1})}}});export{Se as default};
