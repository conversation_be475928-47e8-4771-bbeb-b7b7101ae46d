package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysRoleToSysRoleVoMapper__5.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__5 extends BaseMapper<SysRoleVo, SysRole> {
}
