var M=Object.defineProperty;var $=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var T=(a,e,t)=>e in a?M(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,q=(a,e)=>{for(var t in e||(e={}))N.call(e,t)&&T(a,t,e[t]);if($)for(var t of $(e))O.call(e,t)&&T(a,t,e[t]);return a};var m=(a,e,t)=>new Promise((w,b)=>{var n=s=>{try{d(t.next(s))}catch(u){b(u)}},_=s=>{try{d(t.throw(s))}catch(u){b(u)}},d=s=>s.done?w(s.value):Promise.resolve(s.value).then(n,_);d((t=t.apply(a,e)).next())});import{as as R,an as z}from"./bootstrap-DCMzVRvD.js";import{v as j}from"./vxe-table-DzEj5Fop.js";import{d as F,a as S,b as G}from"./dict-BLkXAGS5.js";import{c as L}from"./download-UJak946_.js";import{e as W}from"./mitt-Dl_8zfXM.js";import{c as H,q as I,_ as J}from"./dict-data-drawer.vue_vue_type_script_setup_true_lang-D6gQqMfN.js";import V from"./index-BeyziwLP.js";import{d as K,p as Q,l as E,S as U,c as X,o as p,a as g,w as i,b as c,T as y,h as C,k as v,t as h}from"../jse/index-index-C-MnMZEz.js";import{u as Y}from"./use-vxe-grid-BC7vZzEr.js";import{u as Z}from"./use-drawer-6qcpK-D1.js";import{P as ee}from"./index-DNdMANjv.js";import{g as te}from"./get-popup-container-P4S1sr5h.js";const ge=K({__name:"index",setup(a){const e=Q(""),t={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:I(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"},w={checkboxConfig:{highlight:!0,reserve:!0},columns:H,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(f,...x)=>m(null,[f,...x],function*({page:o},r={}){const l=q({pageNum:o.currentPage,pageSize:o.pageSize},r);return e.value&&(l.dictType=e.value),yield F(l)})}},rowConfig:{keyField:"dictCode"},id:"system-dict-data-index"},[b,n]=Y({formOptions:t,gridOptions:w}),[_,d]=Z({connectedComponent:J});function s(){d.setData({dictType:e.value}),d.open()}function u(o){return m(this,null,function*(){d.setData({dictType:e.value,dictCode:o.dictCode}),d.open()})}function P(o){return m(this,null,function*(){yield S([o.dictCode]),yield n.query()})}function A(){const r=n.grid.getCheckboxRecords().map(f=>f.dictCode);z.confirm({title:"提示",okType:"danger",content:`确认删除选中的${r.length}条记录吗？`,onOk:()=>m(null,null,function*(){yield S(r),yield n.query()})})}function B(){L(G,"字典数据",n.formApi.form.values)}return W.on("rowClick",o=>m(null,null,function*(){e.value=o,yield n.query()})),(o,r)=>{const f=E("a-button"),x=E("ghost-button"),l=U("access");return p(),X("div",null,[g(c(b),{id:"dict-data","table-title":"字典数据列表"},{"toolbar-tools":i(()=>[g(c(V),null,{default:i(()=>[y((p(),C(f,{onClick:B},{default:i(()=>[v(h(o.$t("pages.common.export")),1)]),_:1})),[[l,["system:dict:export"],"code"]]),y((p(),C(f,{disabled:!c(j)(c(n)),danger:"",type:"primary",onClick:A},{default:i(()=>[v(h(o.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[l,["system:dict:remove"],"code"]]),y((p(),C(f,{disabled:e.value==="",type:"primary",onClick:s},{default:i(()=>[v(h(o.$t("pages.common.add")),1)]),_:1},8,["disabled"])),[[l,["system:dict:add"],"code"]])]),_:1})]),action:i(({row:k})=>[g(c(V),null,{default:i(()=>[y((p(),C(x,{onClick:D=>u(k)},{default:i(()=>[v(h(o.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[l,["system:dict:edit"],"code"]]),g(c(ee),{"get-popup-container":D=>c(te)(D,"dict-data"),placement:"left",title:"确认删除？",onConfirm:D=>P(k)},{default:i(()=>[y((p(),C(x,{danger:"",onClick:r[0]||(r[0]=R(()=>{},["stop"]))},{default:i(()=>[v(h(o.$t("pages.common.delete")),1)]),_:1})),[[l,["system:dict:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),g(c(_),{onReload:r[1]||(r[1]=k=>c(n).query())})])}}});export{ge as _};
