{"doc": " RedisMetadataReport\n", "fields": [], "enumConstants": [], "methods": [{"name": "registerServiceAppMapping", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " Store class and application names using Redis hashes\n key: default 'dubbo:mapping'\n field: class (serviceInterface)\n value: application_names\n @param serviceInterface field(class)\n @param defaultMappingGroup  {@link ServiceNameMapping#DEFAULT_MAPPING_GROUP}\n @param newConfigContent new application_names\n @param ticket previous application_names\n @return\n"}, {"name": "storeMappingInCluster", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " use 'watch' to implement cas.\n Find information about slot distribution by key.\n"}, {"name": "storeMappingStandalone", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " use 'watch' to implement cas.\n Find information about slot distribution by key.\n"}, {"name": "buildMappingKey", "paramTypes": ["java.lang.String"], "doc": " build mapping key\n @param defaultMappingGroup {@link ServiceNameMapping#DEFAULT_MAPPING_GROUP}\n @return\n"}, {"name": "buildPubSubKey", "paramTypes": [], "doc": " build pub/sub key\n"}, {"name": "getConfigItem", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " get content and use content to complete cas\n @param serviceKey class\n @param group {@link ServiceNameMapping#DEFAULT_MAPPING_GROUP}\n"}, {"name": "getMappingData", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " get current application_names\n"}, {"name": "removeServiceAppMappingListener", "paramTypes": ["java.lang.String", "org.apache.dubbo.metadata.MappingListener"], "doc": " remove listener. If have no listener,thread will dead\n"}, {"name": "getServiceAppMapping", "paramTypes": ["java.lang.String", "org.apache.dubbo.metadata.MappingListener", "org.apache.dubbo.common.URL"], "doc": " Start a thread and subscribe to {@link this#buildPubSubKey()}.\n Notify {@link MappingListener} if there is a change in the 'application_names' message.\n"}], "constructors": []}