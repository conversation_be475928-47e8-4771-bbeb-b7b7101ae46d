2025-06-15 08:42:43 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 08:42:43 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.14 with PID 26116 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 08:42:43 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-15 08:42:47 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 08:42:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 08:42:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 08:42:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e93f91d
2025-06-15 08:42:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 08:42:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 08:42:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 08:42:49 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-15 08:42:49 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 08:42:49 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 08:42:49 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 08:42:50 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-15 08:42:50 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 08:46:54 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-15 08:46:58 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 08:46:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 08:46:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 08:46:58 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7baea18b
2025-06-15 08:46:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 08:46:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 08:46:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 08:47:00 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-15 08:47:00 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 08:47:00 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 08:47:00 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 08:47:01 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-15 08:47:01 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:09:55 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-15 14:09:59 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:09:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 14:09:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 14:09:59 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4ebd0b52
2025-06-15 14:09:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 14:09:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 14:09:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 14:10:01 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-15 14:10:01 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 14:10:01 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 14:10:01 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 14:10:02 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-15 14:10:02 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:11:59 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-15 14:12:03 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:12:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 14:12:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 14:12:03 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3100b5cf
2025-06-15 14:12:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 14:12:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 14:12:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 14:12:05 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-15 14:12:05 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 14:12:05 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 14:12:05 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 14:12:06 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-15 14:12:06 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:49:42 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-15 14:49:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:49:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 14:49:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 14:49:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@46d83ec4
2025-06-15 14:49:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 14:49:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 14:49:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 14:49:48 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-15 14:49:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 14:49:48 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 14:49:48 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 14:49:49 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-15 14:49:49 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 15:40:00 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-15 15:40:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 15:40:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 15:40:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 15:40:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@58ec3e8b
2025-06-15 15:40:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 15:40:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 15:40:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 15:40:06 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-15 15:40:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 15:40:06 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 15:40:06 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 15:40:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-15 15:40:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,12763,0]\",\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35750\",\"posY\":\"12737\",\"robotCode\":\"4191\",\"robotDir\":\"-9\",\"robotIp\":\"************\",\"speed\":\"50\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974100619},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[56053,41301,0]\",\"[62299,41301,0]\",\"[63400,41301,0]\",\"[65504,41301,0]\",\"[69893,41301,0]\",\"[70998,41301,0]\",\"[73441,41301,0]\",\"[76448,41301,0]\",\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"59157\",\"posY\":\"41313\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"797\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974100619},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17771\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"26\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974100620},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[26399,20951,-90]\",\"[26399,19493,-90]\",\"[26399,18015,-90]\",\"[26399,16517,-90]\",\"[26399,15009,-90]\",\"[26399,13522,-90]\",\"[25587,14208,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"26389\",\"posY\":\"19844\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1186\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974100620}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:54:54 [snail-job-grpc-server-executor-2] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:54:54 [snail-grpc-server-2] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[291] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:54:54 [snail-job-job-291-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:54:54 [snail-job-job-291-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[291] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,12763,0]\",\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35883\",\"posY\":\"12718\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974105830},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[62299,41301,0]\",\"[63400,41301,0]\",\"[65504,41301,0]\",\"[69893,41301,0]\",\"[70998,41301,0]\",\"[73441,41301,0]\",\"[76448,41301,0]\",\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"63406\",\"posY\":\"41312\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"797\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974105831},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17765\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974105831},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[26399,15009,-90]\",\"[26399,13522,-90]\",\"[25587,14208,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"26392\",\"posY\":\"14200\",\"robotCode\":\"4194\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"655\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974105832}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:54:55 [snail-job-grpc-client-executor-127.0.0.1-105] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[105]
2025-06-15 15:54:59 [snail-job-grpc-server-executor-4] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:54:59 [snail-grpc-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[292] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:54:59 [snail-job-job-292-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:54:59 [snail-job-job-292-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[292] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,12763,0]\",\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35694\",\"posY\":\"12725\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"51\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974110756},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[65504,41301,0]\",\"[69893,41301,0]\",\"[70998,41301,0]\",\"[73441,41301,0]\",\"[76448,41301,0]\",\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"67339\",\"posY\":\"41306\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"800\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974110756},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17765\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974110756},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[26399,13522,-90]\",\"[25587,14208,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"26387\",\"posY\":\"13867\",\"robotCode\":\"4194\",\"robotDir\":\"-79\",\"robotIp\":\"************\",\"speed\":\"241\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974110757}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:04 [snail-job-grpc-server-executor-6] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:55:04 [snail-grpc-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[293] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:55:04 [snail-job-job-293-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:55:04 [snail-job-job-293-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[293] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,12763,0]\",\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35514\",\"posY\":\"12723\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974115834},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[70998,41301,0]\",\"[73441,41301,0]\",\"[76448,41301,0]\",\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"71048\",\"posY\":\"41302\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"799\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974115834},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17765\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974115835},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[26399,13522,-90]\",\"[25587,14208,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25592\",\"posY\":\"14228\",\"robotCode\":\"4194\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"29\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974115835}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:05 [snail-job-grpc-client-executor-127.0.0.1-109] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[109]
2025-06-15 15:55:09 [snail-job-grpc-server-executor-8] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:55:09 [snail-grpc-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[294] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:55:09 [snail-job-job-294-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:55:09 [snail-job-job-294-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[294] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,12763,0]\",\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35120\",\"posY\":\"12724\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"199\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974120505},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[73441,41301,0]\",\"[76448,41301,0]\",\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"74802\",\"posY\":\"41313\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"795\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974120505},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17765\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974120505},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25587\",\"posY\":\"14223\",\"robotCode\":\"4194\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"6\",\"stop\":\"0\",\"timestamp\":1749974120506}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:10 [snail-job-grpc-client-executor-127.0.0.1-112] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[112]
2025-06-15 15:55:14 [snail-job-grpc-server-executor-10] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:55:14 [snail-grpc-server-6] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[295] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:55:14 [snail-job-job-295-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:55:14 [snail-job-job-295-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[295] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,12763,0]\",\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"34574\",\"posY\":\"12732\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974125690},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[76448,41301,0]\",\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"78396\",\"posY\":\"42021\",\"robotCode\":\"4192\",\"robotDir\":\"55\",\"robotIp\":\"************\",\"speed\":\"586\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974125690},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17765\",\"robotCode\":\"4193\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974125691},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25587\",\"posY\":\"14223\",\"robotCode\":\"4194\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"6\",\"stop\":\"0\",\"timestamp\":1749974125691}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:15 [snail-job-grpc-client-executor-127.0.0.1-114] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[114]
2025-06-15 15:55:19 [snail-job-grpc-server-executor-12] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:1ms
2025-06-15 15:55:19 [snail-grpc-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[296] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:55:19 [snail-job-job-296-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:55:19 [snail-job-job-296-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[296] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"34146\",\"posY\":\"12724\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"200\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974130859},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"78672\",\"posY\":\"43348\",\"robotCode\":\"4192\",\"robotDir\":\"89\",\"robotIp\":\"************\",\"speed\":\"95\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974130859},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36223\",\"posY\":\"17765\",\"robotCode\":\"4193\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974130860},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25588\",\"posY\":\"14222\",\"robotCode\":\"4194\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"6\",\"stop\":\"0\",\"timestamp\":1749974130860}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:20 [snail-job-grpc-client-executor-127.0.0.1-118] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[118]
2025-06-15 15:55:24 [snail-job-grpc-server-executor-14] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:55:24 [snail-grpc-server-8] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[297] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:55:24 [snail-job-job-297-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:55:24 [snail-job-job-297-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[297] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[34266,12763,0]\",\"[33063,12763,0]\",\"[32997,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"33159\",\"posY\":\"12723\",\"robotCode\":\"4191\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"162\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749974135874},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[78690,43380,90]\",\"[80558,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"78967\",\"posY\":\"42050\",\"robotCode\":\"4192\",\"robotDir\":\"130\",\"robotIp\":\"************\",\"speed\":\"296\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974135875},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36225\",\"posY\":\"14496\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1204\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974135875},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25589\",\"posY\":\"14221\",\"robotCode\":\"4194\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"6\",\"stop\":\"0\",\"timestamp\":1749974135876}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:25 [snail-job-grpc-client-executor-127.0.0.1-120] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[120]
2025-06-15 15:55:33 [snail-job-grpc-server-executor-16] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-15 15:55:33 [snail-grpc-server-9] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[298] Workflow batch:[null] Task scheduled successfully.
2025-06-15 15:55:33 [snail-job-job-298-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-15 15:55:33 [snail-job-job-298-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[298] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"80\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[33018,12720,0]\",\"[33063,12763,0]\",\"[34266,12763,0]\",\"[35520,12763,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"33018\",\"posY\":\"12720\",\"robotCode\":\"4191\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"152\",\"stop\":\"0\",\"timestamp\":1749974144329},{\"battery\":\"73\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[80558,41587,180]\",\"[81620,41587,180]\",\"[81686,41587,180]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"80638\",\"posY\":\"41578\",\"robotCode\":\"4192\",\"robotDir\":\"-179\",\"robotIp\":\"************\",\"speed\":\"268\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974144329},{\"battery\":\"83\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36225\",\"posY\":\"6148\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"192\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749974144330},{\"battery\":\"64\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[25587,14208,0]\",\"[24383,14208,0]\",\"[24317,14208,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25588\",\"posY\":\"14221\",\"robotCode\":\"4194\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749974144330}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-15 15:55:33 [snail-job-grpc-client-executor-127.0.0.1-123] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[123]
2025-06-15 15:55:41 [snail-job-grpc-client-executor-127.0.0.1-125] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[125]
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 16:12:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
