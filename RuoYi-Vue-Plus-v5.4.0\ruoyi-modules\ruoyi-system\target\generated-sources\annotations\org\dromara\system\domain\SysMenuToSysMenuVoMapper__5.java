package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__5;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysMenuVoToSysMenuMapper__5.class,SysMenuBoToSysMenuMapper__5.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__5 extends BaseMapper<SysMenu, SysMenuVo> {
}
