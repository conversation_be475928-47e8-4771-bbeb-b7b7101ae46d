{"doc": " 自定义字典值校验器\n\n <AUTHOR>\n", "fields": [{"name": "dictType", "doc": " 字典类型\n"}, {"name": "separator", "doc": " 分隔符\n"}], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": ["org.dromara.common.core.validate.dicts.DictPattern"], "doc": " 初始化校验器，提取注解上的字典类型\n\n @param annotation 注解实例\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "jakarta.validation.ConstraintValidatorContext"], "doc": " 校验字段值是否为指定字典类型中的合法值\n\n @param value   被校验的字段值\n @param context 校验上下文（可用于构建错误信息）\n @return true 表示校验通过（合法字典值），false 表示不通过\n"}], "constructors": []}