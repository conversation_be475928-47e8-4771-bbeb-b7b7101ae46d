{"doc": " 常用正则表达式字符串\n <p>\n 常用正则表达式集合，更多正则见: https://any86.github.io/any-rule/\n\n <AUTHOR>\n", "fields": [{"name": "DICTIONARY_TYPE", "doc": " 字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）\n"}, {"name": "PERMISSION_STRING", "doc": " 权限标识必须符合以下格式：\n 1. 标准格式：xxx:yyy:zzz\n - 第一部分（xxx）：只能包含字母、数字和下划线（_），不能使用 `*`\n - 第二部分（yyy）：可以包含字母、数字、下划线（_）和 `*`\n - 第三部分（zzz）：可以包含字母、数字、下划线（_）和 `*`\n 2. 允许空字符串（\"\"），表示没有权限标识\n"}, {"name": "ID_CARD_LAST_6", "doc": " 身份证号码（后6位）\n"}, {"name": "QQ_NUMBER", "doc": " QQ号码\n"}, {"name": "POSTAL_CODE", "doc": " 邮政编码\n"}, {"name": "ACCOUNT", "doc": " 注册账号\n"}, {"name": "PASSWORD", "doc": " 密码：包含至少8个字符，包括大写字母、小写字母、数字和特殊字符\n"}, {"name": "STATUS", "doc": " 通用状态（0表示正常，1表示停用）\n"}], "enumConstants": [], "methods": [], "constructors": []}