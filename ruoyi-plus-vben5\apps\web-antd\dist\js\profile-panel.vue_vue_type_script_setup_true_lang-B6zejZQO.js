var qe=Object.defineProperty,Fe=Object.defineProperties;var Ke=Object.getOwnPropertyDescriptors;var oe=Object.getOwnPropertySymbols;var Qe=Object.prototype.hasOwnProperty,Ze=Object.prototype.propertyIsEnumerable;var se=(a,t,i)=>t in a?qe(a,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[t]=i,vt=(a,t)=>{for(var i in t||(t={}))Qe.call(t,i)&&se(a,i,t[i]);if(oe)for(var i of oe(t))Ze.call(t,i)&&se(a,i,t[i]);return a},Lt=(a,t)=>Fe(a,Ke(t));var Pt=(a,t,i)=>new Promise((e,o)=>{var r=u=>{try{s(i.next(u))}catch(l){o(l)}},n=u=>{try{s(i.throw(u))}catch(l){o(l)}},s=u=>u.done?e(u.value):Promise.resolve(u.value).then(r,n);s((i=i.apply(a,t)).next())});import{c as Je}from"./index-DLUQE3Et.js";import{ax as ti,i as ei,ap as Te,$ as B,T as q,a as ii,z as ai,cp as ri}from"./bootstrap-DCMzVRvD.js";import{d as kt,as as ni,p as st,B as j,v as oi,y as si,b as v,at as hi,c as gt,o as W,T as ci,j as O,H as ut,n as H,l as Oe,h as Ct,O as Ne,w as N,f as ht,a as M,D as li,q as ui,k as ot,t as Q,a0 as pi}from"../jse/index-index-C-MnMZEz.js";import di from"./index-BeyziwLP.js";import{A as Nt}from"./index-BELOxkuV.js";import{u as Se}from"./use-modal-CeMSCP2m.js";import{D as fi,a as bt}from"./index-D59rZjD-.js";import{T as he}from"./index-B6iusSRX.js";import{C as mi}from"./index-C1KbofmV.js";function gi(a){const t=a.split(","),e=t[0].match(/:(.*?);/)[1],o=window.atob(t[1]);let r=o.length;const n=new Uint8Array(r);for(;r--;)n[r]=o.codePointAt(r);return new Blob([n],{type:e})}function ce(a,t){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);t&&(e=e.filter(function(o){return Object.getOwnPropertyDescriptor(a,o).enumerable})),i.push.apply(i,e)}return i}function Ae(a){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?ce(Object(i),!0).forEach(function(e){wi(a,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):ce(Object(i)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})}return a}function vi(a,t){if(typeof a!="object"||!a)return a;var i=a[Symbol.toPrimitive];if(i!==void 0){var e=i.call(a,t);if(typeof e!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(a)}function Re(a){var t=vi(a,"string");return typeof t=="symbol"?t:t+""}function Ht(a){"@babel/helpers - typeof";return Ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ht(a)}function bi(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function le(a,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,Re(e.key),e)}}function yi(a,t,i){return t&&le(a.prototype,t),i&&le(a,i),Object.defineProperty(a,"prototype",{writable:!1}),a}function wi(a,t,i){return t=Re(t),t in a?Object.defineProperty(a,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[t]=i,a}function _e(a){return xi(a)||Di(a)||Ei(a)||Ci()}function xi(a){if(Array.isArray(a))return Wt(a)}function Di(a){if(typeof Symbol!="undefined"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function Ei(a,t){if(a){if(typeof a=="string")return Wt(a,t);var i=Object.prototype.toString.call(a).slice(8,-1);if(i==="Object"&&a.constructor&&(i=a.constructor.name),i==="Map"||i==="Set")return Array.from(a);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Wt(a,t)}}function Wt(a,t){(t==null||t>a.length)&&(t=a.length);for(var i=0,e=new Array(t);i<t;i++)e[i]=a[i];return e}function Ci(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var zt=typeof window!="undefined"&&typeof window.document!="undefined",V=zt?window:{},Qt=zt&&V.document.documentElement?"ontouchstart"in V.document.documentElement:!1,Zt=zt?"PointerEvent"in V:!1,S="cropper",Jt="all",ke="crop",ze="move",Be="zoom",at="e",rt="w",lt="s",K="n",yt="ne",wt="nw",xt="se",Dt="sw",$t="".concat(S,"-crop"),ue="".concat(S,"-disabled"),L="".concat(S,"-hidden"),pe="".concat(S,"-hide"),Mi="".concat(S,"-invisible"),_t="".concat(S,"-modal"),jt="".concat(S,"-move"),Mt="".concat(S,"Action"),St="".concat(S,"Preview"),te="crop",Ie="move",Le="none",Ut="crop",Vt="cropend",Gt="cropmove",qt="cropstart",de="dblclick",Ti=Qt?"touchstart":"mousedown",Oi=Qt?"touchmove":"mousemove",Ni=Qt?"touchend touchcancel":"mouseup",fe=Zt?"pointerdown":Ti,me=Zt?"pointermove":Oi,ge=Zt?"pointerup pointercancel":Ni,ve="ready",be="resize",ye="wheel",Ft="zoom",we="image/jpeg",Si=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Ai=/^data:/,Ri=/^data:image\/jpeg;base64,/,_i=/^img|canvas$/i,Pe=200,Ye=100,xe={viewMode:0,dragMode:te,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:Pe,minContainerHeight:Ye,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},ki='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',zi=Number.isNaN||V.isNaN;function E(a){return typeof a=="number"&&!zi(a)}var De=function(t){return t>0&&t<1/0};function Yt(a){return typeof a=="undefined"}function ct(a){return Ht(a)==="object"&&a!==null}var Bi=Object.prototype.hasOwnProperty;function pt(a){if(!ct(a))return!1;try{var t=a.constructor,i=t.prototype;return t&&i&&Bi.call(i,"isPrototypeOf")}catch(e){return!1}}function I(a){return typeof a=="function"}var Ii=Array.prototype.slice;function Xe(a){return Array.from?Array.from(a):Ii.call(a)}function R(a,t){return a&&I(t)&&(Array.isArray(a)||E(a.length)?Xe(a).forEach(function(i,e){t.call(a,i,e,a)}):ct(a)&&Object.keys(a).forEach(function(i){t.call(a,a[i],i,a)})),a}var A=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),o=1;o<i;o++)e[o-1]=arguments[o];return ct(t)&&e.length>0&&e.forEach(function(r){ct(r)&&Object.keys(r).forEach(function(n){t[n]=r[n]})}),t},Li=/\.\d*(?:0|9){12}\d*$/;function ft(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return Li.test(a)?Math.round(a*t)/t:a}var Pi=/^width|height|left|top|marginLeft|marginTop$/;function Z(a,t){var i=a.style;R(t,function(e,o){Pi.test(o)&&E(e)&&(e="".concat(e,"px")),i[o]=e})}function Yi(a,t){return a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function k(a,t){if(t){if(E(a.length)){R(a,function(e){k(e,t)});return}if(a.classList){a.classList.add(t);return}var i=a.className.trim();i?i.indexOf(t)<0&&(a.className="".concat(i," ").concat(t)):a.className=t}}function U(a,t){if(t){if(E(a.length)){R(a,function(i){U(i,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function dt(a,t,i){if(t){if(E(a.length)){R(a,function(e){dt(e,t,i)});return}i?k(a,t):U(a,t)}}var Xi=/([a-z\d])([A-Z])/g;function ee(a){return a.replace(Xi,"$1-$2").toLowerCase()}function Kt(a,t){return ct(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(ee(t)))}function Tt(a,t,i){ct(i)?a[t]=i:a.dataset?a.dataset[t]=i:a.setAttribute("data-".concat(ee(t)),i)}function Hi(a,t){if(ct(a[t]))try{delete a[t]}catch(i){a[t]=void 0}else if(a.dataset)try{delete a.dataset[t]}catch(i){a.dataset[t]=void 0}else a.removeAttribute("data-".concat(ee(t)))}var He=/\s\s*/,We=function(){var a=!1;if(zt){var t=!1,i=function(){},e=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(r){t=r}});V.addEventListener("test",i,e),V.removeEventListener("test",i,e)}return a}();function X(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=i;t.trim().split(He).forEach(function(r){if(!We){var n=a.listeners;n&&n[r]&&n[r][i]&&(o=n[r][i],delete n[r][i],Object.keys(n[r]).length===0&&delete n[r],Object.keys(n).length===0&&delete a.listeners)}a.removeEventListener(r,o,e)})}function Y(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=i;t.trim().split(He).forEach(function(r){if(e.once&&!We){var n=a.listeners,s=n===void 0?{}:n;o=function(){delete s[r][i],a.removeEventListener(r,o,e);for(var l=arguments.length,h=new Array(l),c=0;c<l;c++)h[c]=arguments[c];i.apply(a,h)},s[r]||(s[r]={}),s[r][i]&&a.removeEventListener(r,s[r][i],e),s[r][i]=o,a.listeners=s}a.addEventListener(r,o,e)})}function mt(a,t,i){var e;return I(Event)&&I(CustomEvent)?e=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0}):(e=document.createEvent("CustomEvent"),e.initCustomEvent(t,!0,!0,i)),a.dispatchEvent(e)}function $e(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var Xt=V.location,Wi=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Ee(a){var t=a.match(Wi);return t!==null&&(t[1]!==Xt.protocol||t[2]!==Xt.hostname||t[3]!==Xt.port)}function Ce(a){var t="timestamp=".concat(new Date().getTime());return a+(a.indexOf("?")===-1?"?":"&")+t}function Et(a){var t=a.rotate,i=a.scaleX,e=a.scaleY,o=a.translateX,r=a.translateY,n=[];E(o)&&o!==0&&n.push("translateX(".concat(o,"px)")),E(r)&&r!==0&&n.push("translateY(".concat(r,"px)")),E(t)&&t!==0&&n.push("rotate(".concat(t,"deg)")),E(i)&&i!==1&&n.push("scaleX(".concat(i,")")),E(e)&&e!==1&&n.push("scaleY(".concat(e,")"));var s=n.length?n.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function $i(a){var t=Ae({},a),i=0;return R(a,function(e,o){delete t[o],R(t,function(r){var n=Math.abs(e.startX-r.startX),s=Math.abs(e.startY-r.startY),u=Math.abs(e.endX-r.endX),l=Math.abs(e.endY-r.endY),h=Math.sqrt(n*n+s*s),c=Math.sqrt(u*u+l*l),p=(c-h)/h;Math.abs(p)>Math.abs(i)&&(i=p)})}),i}function At(a,t){var i=a.pageX,e=a.pageY,o={endX:i,endY:e};return t?o:Ae({startX:i,startY:e},o)}function ji(a){var t=0,i=0,e=0;return R(a,function(o){var r=o.startX,n=o.startY;t+=r,i+=n,e+=1}),t/=e,i/=e,{pageX:t,pageY:i}}function J(a){var t=a.aspectRatio,i=a.height,e=a.width,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=De(e),n=De(i);if(r&&n){var s=i*t;o==="contain"&&s>e||o==="cover"&&s<e?i=e/t:e=i*t}else r?i=e/t:n&&(e=i*t);return{width:e,height:i}}function Ui(a){var t=a.width,i=a.height,e=a.degree;if(e=Math.abs(e)%180,e===90)return{width:i,height:t};var o=e%90*Math.PI/180,r=Math.sin(o),n=Math.cos(o),s=t*n+i*r,u=t*r+i*n;return e>90?{width:u,height:s}:{width:s,height:u}}function Vi(a,t,i,e){var o=t.aspectRatio,r=t.naturalWidth,n=t.naturalHeight,s=t.rotate,u=s===void 0?0:s,l=t.scaleX,h=l===void 0?1:l,c=t.scaleY,p=c===void 0?1:c,y=i.aspectRatio,b=i.naturalWidth,C=i.naturalHeight,w=e.fillColor,g=w===void 0?"transparent":w,T=e.imageSmoothingEnabled,m=T===void 0?!0:T,f=e.imageSmoothingQuality,x=f===void 0?"low":f,d=e.maxWidth,D=d===void 0?1/0:d,_=e.maxHeight,P=_===void 0?1/0:_,G=e.minWidth,tt=G===void 0?0:G,et=e.minHeight,F=et===void 0?0:et,$=document.createElement("canvas"),z=$.getContext("2d"),it=J({aspectRatio:y,width:D,height:P}),Ot=J({aspectRatio:y,width:tt,height:F},"cover"),Bt=Math.min(it.width,Math.max(Ot.width,b)),It=Math.min(it.height,Math.max(Ot.height,C)),ie=J({aspectRatio:o,width:D,height:P}),ae=J({aspectRatio:o,width:tt,height:F},"cover"),re=Math.min(ie.width,Math.max(ae.width,r)),ne=Math.min(ie.height,Math.max(ae.height,n)),Ve=[-re/2,-ne/2,re,ne];return $.width=ft(Bt),$.height=ft(It),z.fillStyle=g,z.fillRect(0,0,Bt,It),z.save(),z.translate(Bt/2,It/2),z.rotate(u*Math.PI/180),z.scale(h,p),z.imageSmoothingEnabled=m,z.imageSmoothingQuality=x,z.drawImage.apply(z,[a].concat(_e(Ve.map(function(Ge){return Math.floor(ft(Ge))})))),z.restore(),$}var je=String.fromCharCode;function Gi(a,t,i){var e="";i+=t;for(var o=t;o<i;o+=1)e+=je(a.getUint8(o));return e}var qi=/^data:.*,/;function Fi(a){var t=a.replace(qi,""),i=atob(t),e=new ArrayBuffer(i.length),o=new Uint8Array(e);return R(o,function(r,n){o[n]=i.charCodeAt(n)}),e}function Ki(a,t){for(var i=[],e=8192,o=new Uint8Array(a);o.length>0;)i.push(je.apply(null,Xe(o.subarray(0,e)))),o=o.subarray(e);return"data:".concat(t,";base64,").concat(btoa(i.join("")))}function Qi(a){var t=new DataView(a),i;try{var e,o,r;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var n=t.byteLength,s=2;s+1<n;){if(t.getUint8(s)===255&&t.getUint8(s+1)===225){o=s;break}s+=1}if(o){var u=o+4,l=o+10;if(Gi(t,u,4)==="Exif"){var h=t.getUint16(l);if(e=h===18761,(e||h===19789)&&t.getUint16(l+2,e)===42){var c=t.getUint32(l+4,e);c>=8&&(r=l+c)}}}if(r){var p=t.getUint16(r,e),y,b;for(b=0;b<p;b+=1)if(y=r+b*12+2,t.getUint16(y,e)===274){y+=8,i=t.getUint16(y,e),t.setUint16(y,1,e);break}}}catch(C){i=1}return i}function Zi(a){var t=0,i=1,e=1;switch(a){case 2:i=-1;break;case 3:t=-180;break;case 4:e=-1;break;case 5:t=90,e=-1;break;case 6:t=90;break;case 7:t=90,i=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:i,scaleY:e}}var Ji={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,o=this.cropper,r=Number(i.minContainerWidth),n=Number(i.minContainerHeight);k(o,L),U(t,L);var s={width:Math.max(e.offsetWidth,r>=0?r:Pe),height:Math.max(e.offsetHeight,n>=0?n:Ye)};this.containerData=s,Z(o,{width:s.width,height:s.height}),k(t,L),U(o,L)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,o=Math.abs(i.rotate)%180===90,r=o?i.naturalHeight:i.naturalWidth,n=o?i.naturalWidth:i.naturalHeight,s=r/n,u=t.width,l=t.height;t.height*s>t.width?e===3?u=t.height*s:l=t.width/s:e===3?l=t.width/s:u=t.height*s;var h={aspectRatio:s,naturalWidth:r,naturalHeight:n,width:u,height:l};this.canvasData=h,this.limited=e===1||e===2,this.limitCanvas(!0,!0),h.width=Math.min(Math.max(h.width,h.minWidth),h.maxWidth),h.height=Math.min(Math.max(h.height,h.minHeight),h.maxHeight),h.left=(t.width-h.width)/2,h.top=(t.height-h.height)/2,h.oldLeft=h.left,h.oldTop=h.top,this.initialCanvasData=A({},h)},limitCanvas:function(t,i){var e=this.options,o=this.containerData,r=this.canvasData,n=this.cropBoxData,s=e.viewMode,u=r.aspectRatio,l=this.cropped&&n;if(t){var h=Number(e.minCanvasWidth)||0,c=Number(e.minCanvasHeight)||0;s>1?(h=Math.max(h,o.width),c=Math.max(c,o.height),s===3&&(c*u>h?h=c*u:c=h/u)):s>0&&(h?h=Math.max(h,l?n.width:0):c?c=Math.max(c,l?n.height:0):l&&(h=n.width,c=n.height,c*u>h?h=c*u:c=h/u));var p=J({aspectRatio:u,width:h,height:c});h=p.width,c=p.height,r.minWidth=h,r.minHeight=c,r.maxWidth=1/0,r.maxHeight=1/0}if(i)if(s>(l?0:1)){var y=o.width-r.width,b=o.height-r.height;r.minLeft=Math.min(0,y),r.minTop=Math.min(0,b),r.maxLeft=Math.max(0,y),r.maxTop=Math.max(0,b),l&&this.limited&&(r.minLeft=Math.min(n.left,n.left+(n.width-r.width)),r.minTop=Math.min(n.top,n.top+(n.height-r.height)),r.maxLeft=n.left,r.maxTop=n.top,s===2&&(r.width>=o.width&&(r.minLeft=Math.min(0,y),r.maxLeft=Math.max(0,y)),r.height>=o.height&&(r.minTop=Math.min(0,b),r.maxTop=Math.max(0,b))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=o.width,r.maxTop=o.height},renderCanvas:function(t,i){var e=this.canvasData,o=this.imageData;if(i){var r=Ui({width:o.naturalWidth*Math.abs(o.scaleX||1),height:o.naturalHeight*Math.abs(o.scaleY||1),degree:o.rotate||0}),n=r.width,s=r.height,u=e.width*(n/e.naturalWidth),l=e.height*(s/e.naturalHeight);e.left-=(u-e.width)/2,e.top-=(l-e.height)/2,e.width=u,e.height=l,e.aspectRatio=n/s,e.naturalWidth=n,e.naturalHeight=s,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,Z(this.canvas,A({width:e.width,height:e.height},Et({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,o=e.naturalWidth*(i.width/i.naturalWidth),r=e.naturalHeight*(i.height/i.naturalHeight);A(e,{width:o,height:r,left:(i.width-o)/2,top:(i.height-r)/2}),Z(this.image,A({width:e.width,height:e.height},Et(A({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,o=Number(t.autoCropArea)||.8,r={width:i.width,height:i.height};e&&(i.height*e>i.width?r.height=r.width/e:r.width=r.height*e),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*o),r.height=Math.max(r.minHeight,r.height*o),r.left=i.left+(i.width-r.width)/2,r.top=i.top+(i.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=A({},r)},limitCropBox:function(t,i){var e=this.options,o=this.containerData,r=this.canvasData,n=this.cropBoxData,s=this.limited,u=e.aspectRatio;if(t){var l=Number(e.minCropBoxWidth)||0,h=Number(e.minCropBoxHeight)||0,c=s?Math.min(o.width,r.width,r.width+r.left,o.width-r.left):o.width,p=s?Math.min(o.height,r.height,r.height+r.top,o.height-r.top):o.height;l=Math.min(l,o.width),h=Math.min(h,o.height),u&&(l&&h?h*u>l?h=l/u:l=h*u:l?h=l/u:h&&(l=h*u),p*u>c?p=c/u:c=p*u),n.minWidth=Math.min(l,c),n.minHeight=Math.min(h,p),n.maxWidth=c,n.maxHeight=p}i&&(s?(n.minLeft=Math.max(0,r.left),n.minTop=Math.max(0,r.top),n.maxLeft=Math.min(o.width,r.left+r.width)-n.width,n.maxTop=Math.min(o.height,r.top+r.height)-n.height):(n.minLeft=0,n.minTop=0,n.maxLeft=o.width-n.width,n.maxTop=o.height-n.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&Tt(this.face,Mt,e.width>=i.width&&e.height>=i.height?ze:Jt),Z(this.cropBox,A({width:e.width,height:e.height},Et({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),mt(this.element,Ut,this.getData())}},ta={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,o=i?this.crossOriginUrl:this.url,r=t.alt||"The image to preview",n=document.createElement("img");if(i&&(n.crossOrigin=i),n.src=o,n.alt=r,this.viewBox.appendChild(n),this.viewBoxImage=n,!!e){var s=e;typeof e=="string"?s=t.ownerDocument.querySelectorAll(e):e.querySelector&&(s=[e]),this.previews=s,R(s,function(u){var l=document.createElement("img");Tt(u,St,{width:u.offsetWidth,height:u.offsetHeight,html:u.innerHTML}),i&&(l.crossOrigin=i),l.src=o,l.alt=r,l.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',u.innerHTML="",u.appendChild(l)})}},resetPreview:function(){R(this.previews,function(t){var i=Kt(t,St);Z(t,{width:i.width,height:i.height}),t.innerHTML=i.html,Hi(t,St)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,o=e.width,r=e.height,n=t.width,s=t.height,u=e.left-i.left-t.left,l=e.top-i.top-t.top;!this.cropped||this.disabled||(Z(this.viewBoxImage,A({width:n,height:s},Et(A({translateX:-u,translateY:-l},t)))),R(this.previews,function(h){var c=Kt(h,St),p=c.width,y=c.height,b=p,C=y,w=1;o&&(w=p/o,C=r*w),r&&C>y&&(w=y/r,b=o*w,C=y),Z(h,{width:b,height:C}),Z(h.getElementsByTagName("img")[0],A({width:n*w,height:s*w},Et(A({translateX:-u*w,translateY:-l*w},t))))}))}},ea={bind:function(){var t=this.element,i=this.options,e=this.cropper;I(i.cropstart)&&Y(t,qt,i.cropstart),I(i.cropmove)&&Y(t,Gt,i.cropmove),I(i.cropend)&&Y(t,Vt,i.cropend),I(i.crop)&&Y(t,Ut,i.crop),I(i.zoom)&&Y(t,Ft,i.zoom),Y(e,fe,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&Y(e,ye,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&Y(e,de,this.onDblclick=this.dblclick.bind(this)),Y(t.ownerDocument,me,this.onCropMove=this.cropMove.bind(this)),Y(t.ownerDocument,ge,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&Y(window,be,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;I(i.cropstart)&&X(t,qt,i.cropstart),I(i.cropmove)&&X(t,Gt,i.cropmove),I(i.cropend)&&X(t,Vt,i.cropend),I(i.crop)&&X(t,Ut,i.crop),I(i.zoom)&&X(t,Ft,i.zoom),X(e,fe,this.onCropStart),i.zoomable&&i.zoomOnWheel&&X(e,ye,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&X(e,de,this.onDblclick),X(t.ownerDocument,me,this.onCropMove),X(t.ownerDocument,ge,this.onCropEnd),i.responsive&&X(window,be,this.onResize)}},ia={resize:function(){if(!this.disabled){var t=this.options,i=this.container,e=this.containerData,o=i.offsetWidth/e.width,r=i.offsetHeight/e.height,n=Math.abs(o-1)>Math.abs(r-1)?o:r;if(n!==1){var s,u;t.restore&&(s=this.getCanvasData(),u=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(R(s,function(l,h){s[h]=l*n})),this.setCropBoxData(R(u,function(l,h){u[h]=l*n})))}}},dblclick:function(){this.disabled||this.options.dragMode===Le||this.setDragMode(Yi(this.dragBox,$t)?Ie:te)},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,o=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?o=t.deltaY>0?1:-1:t.wheelDelta?o=-t.wheelDelta/120:t.detail&&(o=t.detail>0?1:-1),this.zoom(-o*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(E(i)&&i!==1||E(e)&&e!==0||t.ctrlKey))){var o=this.options,r=this.pointers,n;t.changedTouches?R(t.changedTouches,function(s){r[s.identifier]=At(s)}):r[t.pointerId||0]=At(t),Object.keys(r).length>1&&o.zoomable&&o.zoomOnTouch?n=Be:n=Kt(t.target,Mt),Si.test(n)&&mt(this.element,qt,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===ke&&(this.cropping=!0,k(this.dragBox,_t)))}},cropMove:function(t){var i=this.action;if(!(this.disabled||!i)){var e=this.pointers;t.preventDefault(),mt(this.element,Gt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?R(t.changedTouches,function(o){A(e[o.identifier]||{},At(o,!0))}):A(e[t.pointerId||0]||{},At(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?R(t.changedTouches,function(o){delete e[o.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,dt(this.dragBox,_t,this.cropped&&this.options.modal)),mt(this.element,Vt,{originalEvent:t,action:i}))}}},aa={change:function(t){var i=this.options,e=this.canvasData,o=this.containerData,r=this.cropBoxData,n=this.pointers,s=this.action,u=i.aspectRatio,l=r.left,h=r.top,c=r.width,p=r.height,y=l+c,b=h+p,C=0,w=0,g=o.width,T=o.height,m=!0,f;!u&&t.shiftKey&&(u=c&&p?c/p:1),this.limited&&(C=r.minLeft,w=r.minTop,g=C+Math.min(o.width,e.width,e.left+e.width),T=w+Math.min(o.height,e.height,e.top+e.height));var x=n[Object.keys(n)[0]],d={x:x.endX-x.startX,y:x.endY-x.startY},D=function(P){switch(P){case at:y+d.x>g&&(d.x=g-y);break;case rt:l+d.x<C&&(d.x=C-l);break;case K:h+d.y<w&&(d.y=w-h);break;case lt:b+d.y>T&&(d.y=T-b);break}};switch(s){case Jt:l+=d.x,h+=d.y;break;case at:if(d.x>=0&&(y>=g||u&&(h<=w||b>=T))){m=!1;break}D(at),c+=d.x,c<0&&(s=rt,c=-c,l-=c),u&&(p=c/u,h+=(r.height-p)/2);break;case K:if(d.y<=0&&(h<=w||u&&(l<=C||y>=g))){m=!1;break}D(K),p-=d.y,h+=d.y,p<0&&(s=lt,p=-p,h-=p),u&&(c=p*u,l+=(r.width-c)/2);break;case rt:if(d.x<=0&&(l<=C||u&&(h<=w||b>=T))){m=!1;break}D(rt),c-=d.x,l+=d.x,c<0&&(s=at,c=-c,l-=c),u&&(p=c/u,h+=(r.height-p)/2);break;case lt:if(d.y>=0&&(b>=T||u&&(l<=C||y>=g))){m=!1;break}D(lt),p+=d.y,p<0&&(s=K,p=-p,h-=p),u&&(c=p*u,l+=(r.width-c)/2);break;case yt:if(u){if(d.y<=0&&(h<=w||y>=g)){m=!1;break}D(K),p-=d.y,h+=d.y,c=p*u}else D(K),D(at),d.x>=0?y<g?c+=d.x:d.y<=0&&h<=w&&(m=!1):c+=d.x,d.y<=0?h>w&&(p-=d.y,h+=d.y):(p-=d.y,h+=d.y);c<0&&p<0?(s=Dt,p=-p,c=-c,h-=p,l-=c):c<0?(s=wt,c=-c,l-=c):p<0&&(s=xt,p=-p,h-=p);break;case wt:if(u){if(d.y<=0&&(h<=w||l<=C)){m=!1;break}D(K),p-=d.y,h+=d.y,c=p*u,l+=r.width-c}else D(K),D(rt),d.x<=0?l>C?(c-=d.x,l+=d.x):d.y<=0&&h<=w&&(m=!1):(c-=d.x,l+=d.x),d.y<=0?h>w&&(p-=d.y,h+=d.y):(p-=d.y,h+=d.y);c<0&&p<0?(s=xt,p=-p,c=-c,h-=p,l-=c):c<0?(s=yt,c=-c,l-=c):p<0&&(s=Dt,p=-p,h-=p);break;case Dt:if(u){if(d.x<=0&&(l<=C||b>=T)){m=!1;break}D(rt),c-=d.x,l+=d.x,p=c/u}else D(lt),D(rt),d.x<=0?l>C?(c-=d.x,l+=d.x):d.y>=0&&b>=T&&(m=!1):(c-=d.x,l+=d.x),d.y>=0?b<T&&(p+=d.y):p+=d.y;c<0&&p<0?(s=yt,p=-p,c=-c,h-=p,l-=c):c<0?(s=xt,c=-c,l-=c):p<0&&(s=wt,p=-p,h-=p);break;case xt:if(u){if(d.x>=0&&(y>=g||b>=T)){m=!1;break}D(at),c+=d.x,p=c/u}else D(lt),D(at),d.x>=0?y<g?c+=d.x:d.y>=0&&b>=T&&(m=!1):c+=d.x,d.y>=0?b<T&&(p+=d.y):p+=d.y;c<0&&p<0?(s=wt,p=-p,c=-c,h-=p,l-=c):c<0?(s=Dt,c=-c,l-=c):p<0&&(s=yt,p=-p,h-=p);break;case ze:this.move(d.x,d.y),m=!1;break;case Be:this.zoom($i(n),t),m=!1;break;case ke:if(!d.x||!d.y){m=!1;break}f=$e(this.cropper),l=x.startX-f.left,h=x.startY-f.top,c=r.minWidth,p=r.minHeight,d.x>0?s=d.y>0?xt:yt:d.x<0&&(l-=c,s=d.y>0?Dt:wt),d.y<0&&(h-=p),this.cropped||(U(this.cropBox,L),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}m&&(r.width=c,r.height=p,r.left=l,r.top=h,this.action=s,this.renderCropBox()),R(n,function(_){_.startX=_.endX,_.startY=_.endY})}},ra={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&k(this.dragBox,_t),U(this.cropBox,L),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=A({},this.initialImageData),this.canvasData=A({},this.initialCanvasData),this.cropBoxData=A({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(A(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),U(this.dragBox,_t),k(this.cropBox,L)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,R(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,U(this.cropper,ue)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,k(this.cropper,ue)),this},destroy:function(){var t=this.element;return t[S]?(t[S]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,o=e.left,r=e.top;return this.moveTo(Yt(t)?t:o+Number(t),Yt(i)?i:r+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,o=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(E(t)&&(e.left=t,o=!0),E(i)&&(e.top=i,o=!0),o&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var o=this.options,r=this.canvasData,n=r.width,s=r.height,u=r.naturalWidth,l=r.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&o.zoomable){var h=u*t,c=l*t;if(mt(this.element,Ft,{ratio:t,oldRatio:n/u,originalEvent:e})===!1)return this;if(e){var p=this.pointers,y=$e(this.cropper),b=p&&Object.keys(p).length?ji(p):{pageX:e.pageX,pageY:e.pageY};r.left-=(h-n)*((b.pageX-y.left-r.left)/n),r.top-=(c-s)*((b.pageY-y.top-r.top)/s)}else pt(i)&&E(i.x)&&E(i.y)?(r.left-=(h-n)*((i.x-r.left)/n),r.top-=(c-s)*((i.y-r.top)/s)):(r.left-=(h-n)/2,r.top-=(c-s)/2);r.width=h,r.height=c,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),E(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,E(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(E(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,o=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(E(t)&&(e.scaleX=t,o=!0),E(i)&&(e.scaleY=i,o=!0),o&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.options,e=this.imageData,o=this.canvasData,r=this.cropBoxData,n;if(this.ready&&this.cropped){n={x:r.left-o.left,y:r.top-o.top,width:r.width,height:r.height};var s=e.width/e.naturalWidth;if(R(n,function(h,c){n[c]=h/s}),t){var u=Math.round(n.y+n.height),l=Math.round(n.x+n.width);n.x=Math.round(n.x),n.y=Math.round(n.y),n.width=l-n.x,n.height=u-n.y}}else n={x:0,y:0,width:0,height:0};return i.rotatable&&(n.rotate=e.rotate||0),i.scalable&&(n.scaleX=e.scaleX||1,n.scaleY=e.scaleY||1),n},setData:function(t){var i=this.options,e=this.imageData,o=this.canvasData,r={};if(this.ready&&!this.disabled&&pt(t)){var n=!1;i.rotatable&&E(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,n=!0),i.scalable&&(E(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,n=!0),E(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,n=!0)),n&&this.renderCanvas(!0,!0);var s=e.width/e.naturalWidth;E(t.x)&&(r.left=t.x*s+o.left),E(t.y)&&(r.top=t.y*s+o.top),E(t.width)&&(r.width=t.width*s),E(t.height)&&(r.height=t.height*s),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?A({},this.containerData):{}},getImageData:function(){return this.sized?A({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&R(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&pt(t)&&(E(t.left)&&(i.left=t.left),E(t.top)&&(i.top=t.top),E(t.width)?(i.width=t.width,i.height=t.width/e):E(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,i;return this.ready&&this.cropped&&(i={left:t.left,top:t.top,width:t.width,height:t.height}),i||{}},setCropBoxData:function(t){var i=this.cropBoxData,e=this.options.aspectRatio,o,r;return this.ready&&this.cropped&&!this.disabled&&pt(t)&&(E(t.left)&&(i.left=t.left),E(t.top)&&(i.top=t.top),E(t.width)&&t.width!==i.width&&(o=!0,i.width=t.width),E(t.height)&&t.height!==i.height&&(r=!0,i.height=t.height),e&&(o?i.height=i.width/e:r&&(i.width=i.height*e)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=Vi(this.image,this.imageData,i,t);if(!this.cropped)return e;var o=this.getData(t.rounded),r=o.x,n=o.y,s=o.width,u=o.height,l=e.width/Math.floor(i.naturalWidth);l!==1&&(r*=l,n*=l,s*=l,u*=l);var h=s/u,c=J({aspectRatio:h,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=J({aspectRatio:h,width:t.minWidth||0,height:t.minHeight||0},"cover"),y=J({aspectRatio:h,width:t.width||(l!==1?e.width:s),height:t.height||(l!==1?e.height:u)}),b=y.width,C=y.height;b=Math.min(c.width,Math.max(p.width,b)),C=Math.min(c.height,Math.max(p.height,C));var w=document.createElement("canvas"),g=w.getContext("2d");w.width=ft(b),w.height=ft(C),g.fillStyle=t.fillColor||"transparent",g.fillRect(0,0,b,C);var T=t.imageSmoothingEnabled,m=T===void 0?!0:T,f=t.imageSmoothingQuality;g.imageSmoothingEnabled=m,f&&(g.imageSmoothingQuality=f);var x=e.width,d=e.height,D=r,_=n,P,G,tt,et,F,$;D<=-s||D>x?(D=0,P=0,tt=0,F=0):D<=0?(tt=-D,D=0,P=Math.min(x,s+D),F=P):D<=x&&(tt=0,P=Math.min(s,x-D),F=P),P<=0||_<=-u||_>d?(_=0,G=0,et=0,$=0):_<=0?(et=-_,_=0,G=Math.min(d,u+_),$=G):_<=d&&(et=0,G=Math.min(u,d-_),$=G);var z=[D,_,P,G];if(F>0&&$>0){var it=b/s;z.push(tt*it,et*it,F*it,$*it)}return g.drawImage.apply(g,[e].concat(_e(z.map(function(Ot){return Math.floor(ft(Ot))})))),w},setAspectRatio:function(t){var i=this.options;return!this.disabled&&!Yt(t)&&(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,o=this.face;if(this.ready&&!this.disabled){var r=t===te,n=i.movable&&t===Ie;t=r||n?t:Le,i.dragMode=t,Tt(e,Mt,t),dt(e,$t,r),dt(e,jt,n),i.cropBoxMovable||(Tt(o,Mt,t),dt(o,$t,r),dt(o,jt,n))}return this}},na=V.Cropper,Ue=function(){function a(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(bi(this,a),!t||!_i.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=A({},xe,pt(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return yi(a,[{key:"init",value:function(){var i=this.element,e=i.tagName.toLowerCase(),o;if(!i[S]){if(i[S]=this,e==="img"){if(this.isImg=!0,o=i.getAttribute("src")||"",this.originalUrl=o,!o)return;o=i.src}else e==="canvas"&&window.HTMLCanvasElement&&(o=i.toDataURL());this.load(o)}}},{key:"load",value:function(i){var e=this;if(i){this.url=i,this.imageData={};var o=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(Ai.test(i)){Ri.test(i)?this.read(Fi(i)):this.clone();return}var n=new XMLHttpRequest,s=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=s,n.onerror=s,n.ontimeout=s,n.onprogress=function(){n.getResponseHeader("content-type")!==we&&n.abort()},n.onload=function(){e.read(n.response)},n.onloadend=function(){e.reloading=!1,e.xhr=null},r.checkCrossOrigin&&Ee(i)&&o.crossOrigin&&(i=Ce(i)),n.open("GET",i,!0),n.responseType="arraybuffer",n.withCredentials=o.crossOrigin==="use-credentials",n.send()}}},{key:"read",value:function(i){var e=this.options,o=this.imageData,r=Qi(i),n=0,s=1,u=1;if(r>1){this.url=Ki(i,we);var l=Zi(r);n=l.rotate,s=l.scaleX,u=l.scaleY}e.rotatable&&(o.rotate=n),e.scalable&&(o.scaleX=s,o.scaleY=u),this.clone()}},{key:"clone",value:function(){var i=this.element,e=this.url,o=i.crossOrigin,r=e;this.options.checkCrossOrigin&&Ee(e)&&(o||(o="anonymous"),r=Ce(e)),this.crossOrigin=o,this.crossOriginUrl=r;var n=document.createElement("img");o&&(n.crossOrigin=o),n.src=r||e,n.alt=i.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),k(n,pe),i.parentNode.insertBefore(n,i.nextSibling)}},{key:"start",value:function(){var i=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var o=V.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(V.navigator.userAgent),r=function(l,h){A(i.imageData,{naturalWidth:l,naturalHeight:h,aspectRatio:l/h}),i.initialImageData=A({},i.imageData),i.sizing=!1,i.sized=!0,i.build()};if(e.naturalWidth&&!o){r(e.naturalWidth,e.naturalHeight);return}var n=document.createElement("img"),s=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),o||s.removeChild(n)},n.src=e.src,o||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",s.appendChild(n))}},{key:"stop",value:function(){var i=this.image;i.onload=null,i.onerror=null,i.parentNode.removeChild(i),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var i=this.element,e=this.options,o=this.image,r=i.parentNode,n=document.createElement("div");n.innerHTML=ki;var s=n.querySelector(".".concat(S,"-container")),u=s.querySelector(".".concat(S,"-canvas")),l=s.querySelector(".".concat(S,"-drag-box")),h=s.querySelector(".".concat(S,"-crop-box")),c=h.querySelector(".".concat(S,"-face"));this.container=r,this.cropper=s,this.canvas=u,this.dragBox=l,this.cropBox=h,this.viewBox=s.querySelector(".".concat(S,"-view-box")),this.face=c,u.appendChild(o),k(i,L),r.insertBefore(s,i.nextSibling),U(o,pe),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,k(h,L),e.guides||k(h.getElementsByClassName("".concat(S,"-dashed")),L),e.center||k(h.getElementsByClassName("".concat(S,"-center")),L),e.background&&k(s,"".concat(S,"-bg")),e.highlight||k(c,Mi),e.cropBoxMovable&&(k(c,jt),Tt(c,Mt,Jt)),e.cropBoxResizable||(k(h.getElementsByClassName("".concat(S,"-line")),L),k(h.getElementsByClassName("".concat(S,"-point")),L)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),I(e.ready)&&Y(i,ve,e.ready,{once:!0}),mt(i,ve)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var i=this.cropper.parentNode;i&&i.removeChild(this.cropper),U(this.element,L)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=na,a}},{key:"setDefaults",value:function(i){A(xe,pt(i)&&i)}}])}();A(Ue.prototype,Ji,ta,ea,ia,aa,ra);const oa=["alt","crossorigin","src"],Me="cropper-image",sa=kt({name:"CropperImage",__name:"cropper",props:{alt:{default:"",type:String},circled:{default:!1,type:Boolean},crossorigin:{default:void 0,type:String},height:{default:"360px",type:[String,Number]},imageStyle:{default:()=>({}),type:Object},options:{default:()=>({}),type:Object},realTimePreview:{default:!0,type:Boolean},src:{required:!0,type:String}},emits:["cropend","ready","cropendError","readyError"],setup(a,{emit:t}){const i=a,e=t,o={aspectRatio:1,autoCrop:!0,background:!0,center:!0,checkCrossOrigin:!1,checkOrientation:!0,cropBoxMovable:!0,cropBoxResizable:!0,guides:!0,highlight:!0,modal:!0,movable:!0,responsive:!0,restore:!0,rotatable:!0,scalable:!0,toggleDragModeOnDblclick:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0},r=ni(),n=st(),s=st(),u=st(!1),l=hi(b,80),h=j(()=>vt({height:i.height,maxWidth:"100%"},i.imageStyle)),c=j(()=>[Me,r.class,{[`${Me}--circled`]:i.circled}]),p=j(()=>({height:`${`${i.height}`.replace(/px/,"")}px`}));oi(y),si(()=>{var g;(g=s.value)==null||g.destroy()});function y(){return Pt(this,null,function*(){const g=v(n);if(g){try{(yield fetch(i.src)).status!==200&&e("readyError")}catch(T){e("readyError")}s.value=new Ue(g,vt(Lt(vt({},o),{crop(){l()},cropmove(){l()},ready:()=>{u.value=!0,b(),e("ready",s.value)},zoom(){l()}}),i.options))}})}function b(){i.realTimePreview&&C()}function C(){if(!s.value)return;const g=s.value.getData();(i.circled?w():s.value.getCroppedCanvas()).toBlob(m=>{if(!m)return;const f=new FileReader;f.readAsDataURL(m),f.onloadend=x=>{var d,D;e("cropend",{imgBase64:(D=(d=x.target)==null?void 0:d.result)!=null?D:"",imgInfo:g})},f.onerror=()=>{e("cropendError")}},"image/png")}function w(){const g=s.value.getCroppedCanvas(),T=document.createElement("canvas"),m=T.getContext("2d"),f=g.width,x=g.height;return T.width=f,T.height=x,m.imageSmoothingEnabled=!0,m.drawImage(g,0,0,f,x),m.globalCompositeOperation="destination-in",m.beginPath(),m.arc(f/2,x/2,Math.min(f,x)/2,0,2*Math.PI,!0),m.fill(),T}return(g,T)=>(W(),gt("div",{class:H(c.value),style:ut(p.value)},[ci(O("img",{ref_key:"imgElRef",ref:n,alt:a.alt,crossorigin:a.crossorigin,src:a.src,style:ut(h.value)},null,12,oa),[[ti,u.value]])],6))}}),ha=["alt","src"],nt="cropper-am",ca=kt({name:"CropperModal",__name:"cropper-modal",props:{circled:{default:!0,type:Boolean},size:{default:0,type:Number},src:{default:"",type:String},uploadApi:{required:!0,type:Function}},emits:["uploadSuccess","uploadError","register"],setup(a,{emit:t}){const i=a,e=t;let o="";const r=st(i.src||""),n=st(""),s=st();let u=1,l=1;const[h,c]=Se({onConfirm:T,onOpenChange(m){m?p(!0):(n.value="",p(!1))}});function p(m){c.setState({confirmLoading:m,loading:m})}function y(m){if(i.size>0&&m.size>1024*1024*i.size)return e("uploadError",{msg:B("component.cropper.imageTooBig")}),!1;const f=new FileReader;return f.readAsDataURL(m),r.value="",n.value="",f.addEventListener("load",x=>{var d,D;r.value=(D=(d=x.target)==null?void 0:d.result)!=null?D:"",o=m.name}),!1}function b({imgBase64:m}){n.value=m}function C(m){s.value=m,p(!1)}function w(){p(!1)}function g(m,f){var x,d;m==="scaleX"&&(u=f=u===-1?1:-1),m==="scaleY"&&(l=f=l===-1?1:-1),(d=(x=s==null?void 0:s.value)==null?void 0:x[m])==null||d.call(x,f)}function T(){return Pt(this,null,function*(){const m=i.uploadApi;if(m&&ei(m)){if(!n.value){Te.warn("未选择图片");return}const f=gi(n.value);try{p(!0);const x=yield m({file:f,filename:o,name:"file"});e("uploadSuccess",{data:x.url,source:n.value}),c.close()}finally{p(!1)}}})}return(m,f)=>{const x=Oe("a-button");return W(),Ct(v(h),Ne(m.$attrs,{"confirm-text":v(B)("component.cropper.okText"),"fullscreen-button":!1,title:v(B)("component.cropper.modalTitle"),class:"w-[800px]"}),{default:N(()=>[O("div",{class:H(nt)},[O("div",{class:H([`${nt}-left`,"w-full"])},[O("div",{class:H(`${nt}-cropper`)},[r.value?(W(),Ct(sa,{key:0,circled:a.circled,src:r.value,crossorigin:"anonymous",height:"300px",onCropend:b,onReady:C,onReadyError:w},null,8,["circled","src"])):ht("",!0)],2),O("div",{class:H(`${nt}-toolbar`)},[M(v(ii),{"before-upload":y,"file-list":[],accept:"image/*"},{default:N(()=>[M(v(q),{title:v(B)("component.cropper.selectImage"),placement:"bottom"},{default:N(()=>[M(x,{size:"small",type:"primary"},{icon:N(()=>f[7]||(f[7]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[ant-design--upload-outlined]"})],-1)])),_:1})]),_:1},8,["title"])]),_:1}),M(v(di),null,{default:N(()=>[M(v(q),{title:v(B)("component.cropper.btn_reset"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,size:"small",type:"primary",onClick:f[0]||(f[0]=d=>g("reset"))},{icon:N(()=>f[8]||(f[8]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[ant-design--reload-outlined]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"]),M(v(q),{title:v(B)("component.cropper.btn_rotate_left"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,size:"small",type:"primary",onClick:f[1]||(f[1]=d=>g("rotate",-45))},{icon:N(()=>f[9]||(f[9]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[ant-design--rotate-left-outlined]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"]),M(v(q),{title:v(B)("component.cropper.btn_rotate_right"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,"pre-icon":"ant-design:rotate-right-outlined",size:"small",type:"primary",onClick:f[2]||(f[2]=d=>g("rotate",45))},{icon:N(()=>f[10]||(f[10]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[ant-design--rotate-right-outlined]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"]),M(v(q),{title:v(B)("component.cropper.btn_scale_x"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,size:"small",type:"primary",onClick:f[3]||(f[3]=d=>g("scaleX"))},{icon:N(()=>f[11]||(f[11]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[vaadin--arrows-long-h]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"]),M(v(q),{title:v(B)("component.cropper.btn_scale_y"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,size:"small",type:"primary",onClick:f[4]||(f[4]=d=>g("scaleY"))},{icon:N(()=>f[12]||(f[12]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[vaadin--arrows-long-v]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"]),M(v(q),{title:v(B)("component.cropper.btn_zoom_in"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,size:"small",type:"primary",onClick:f[5]||(f[5]=d=>g("zoom",.1))},{icon:N(()=>f[13]||(f[13]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[ant-design--zoom-in-outlined]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"]),M(v(q),{title:v(B)("component.cropper.btn_zoom_out"),placement:"bottom"},{default:N(()=>[M(x,{disabled:!r.value,size:"small",type:"primary",onClick:f[6]||(f[6]=d=>g("zoom",-.1))},{icon:N(()=>f[14]||(f[14]=[O("div",{class:"flex items-center justify-center"},[O("span",{class:"icon-[ant-design--zoom-out-outlined]"})],-1)])),_:1},8,["disabled"])]),_:1},8,["title"])]),_:1})],2)],2),O("div",{class:H(`${nt}-right`)},[O("div",{class:H(`${nt}-preview`)},[n.value?(W(),gt("img",{key:0,alt:v(B)("component.cropper.preview"),src:n.value},null,8,ha)):ht("",!0)],2),n.value?(W(),gt("div",{key:0,class:H(`${nt}-group`)},[M(v(Nt),{src:n.value,size:"large"},null,8,["src"]),M(v(Nt),{size:48,src:n.value},null,8,["src"]),M(v(Nt),{size:64,src:n.value},null,8,["src"]),M(v(Nt),{size:80,src:n.value},null,8,["src"])],2)):ht("",!0)],2)])]),_:1},16,["confirm-text","title"])}}}),la=["src"],Rt="cropper-avatar",ua=kt({name:"CropperAvatar",__name:"cropper-avatar",props:{btnProps:{default:()=>({}),type:Object},btnText:{default:"",type:String},showBtn:{default:!0,type:Boolean},size:{default:5,type:Number},uploadApi:{required:!0,type:Function},value:{default:"",type:String},width:{default:"200px",type:[String,Number]}},emits:["update:value","change"],setup(a,{expose:t,emit:i}){const e=a,o=i,r=st(e.value||""),[n,s]=Se({connectedComponent:ca}),u=j(()=>[Rt]),l=j(()=>`${`${e.width}`.replace(/px/,"")}px`),h=j(()=>`${Number.parseInt(`${e.width}`.replace(/px/,""))/2}px`),c=j(()=>({width:v(l)})),p=j(()=>({height:v(l),width:v(l)}));li(()=>{r.value=e.value||""}),ui(()=>r.value,w=>{o("update:value",w)});function y({data:w,source:g}){r.value=g,o("change",{data:w,source:g}),Te.success(B("component.cropper.uploadSuccess"))}const b=()=>s.close(),C=()=>s.open();return t({closeModal:b,openModal:C}),(w,g)=>{const T=Oe("a-button");return W(),gt("div",{class:H(u.value),style:ut(c.value)},[O("div",{class:H(`${Rt}-image-wrapper`),style:ut(p.value),onClick:C},[O("div",{class:H(`${Rt}-image-mask`),style:ut(p.value)},[O("span",{style:ut(Lt(vt({},p.value),{width:`${h.value}`,height:`${h.value}`,lineHeight:`${h.value}`})),class:"icon-[ant-design--cloud-upload-outlined] text-[#d6d6d6]"},null,4)],6),r.value?(W(),gt("img",{key:0,src:r.value,alt:"avatar"},null,8,la)):ht("",!0)],6),a.showBtn?(W(),Ct(T,Ne({key:0,class:`${Rt}-upload-btn`,onClick:C},a.btnProps),{default:N(()=>[ot(Q(a.btnText?a.btnText:v(B)("component.cropper.selectImage")),1)]),_:1},16,["class"])):ht("",!0),M(v(n),{size:a.size,src:r.value,"upload-api":a.uploadApi,onUploadSuccess:y},null,8,["size","src","upload-api"])],6)}}}),pa=ai(ua,[["__scopeId","data-v-dad85e31"]]),da={key:0,class:"flex flex-col items-center gap-[24px]"},fa={class:"flex flex-col items-center gap-[20px]"},ma={class:"flex flex-col items-center gap-[8px]"},ga={class:"text-foreground text-xl font-bold"},va=["src"],ba={class:"px-[24px]"},Sa=kt({__name:"profile-panel",props:{profile:{}},emits:["uploadFinish"],setup(a){const t=a,i=j(()=>{var r;return((r=t.profile)==null?void 0:r.user.avatar)||pi.app.defaultAvatar}),{isDark:e}=ri(),o=j(()=>`https://v2.jinrishici.com/one.svg?font-size=12&color=${e.value?"white":"gray"}`);return(r,n)=>(W(),Ct(v(mi),{loading:!r.profile,class:"h-full lg:w-1/3"},{default:N(()=>{var s;return[r.profile?(W(),gt("div",da,[O("div",fa,[M(v(q),{title:"点击上传头像"},{default:N(()=>[M(v(pa),{"show-btn":!1,"upload-api":v(Je),value:i.value,width:"120",onChange:n[0]||(n[0]=u=>r.$emit("uploadFinish"))},null,8,["upload-api","value"])]),_:1}),O("div",ma,[O("span",ga,Q((s=r.profile.user.nickName)!=null?s:"未知"),1),O("img",{src:o.value},null,8,va)])]),O("div",ba,[M(v(fi),{column:1},{default:N(()=>[M(v(bt),{label:"账号"},{default:N(()=>[ot(Q(r.profile.user.userName),1)]),_:1}),M(v(bt),{label:"手机号码"},{default:N(()=>[ot(Q(r.profile.user.phonenumber||"未绑定手机号"),1)]),_:1}),M(v(bt),{label:"邮箱"},{default:N(()=>[ot(Q(r.profile.user.email||"未绑定邮箱"),1)]),_:1}),M(v(bt),{label:"部门"},{default:N(()=>[M(v(he),{color:"processing"},{default:N(()=>{var u;return[ot(Q((u=r.profile.user.deptName)!=null?u:"未分配部门"),1)]}),_:1}),r.profile.postGroup?(W(),Ct(v(he),{key:0,color:"processing"},{default:N(()=>[ot(Q(r.profile.postGroup),1)]),_:1})):ht("",!0)]),_:1}),M(v(bt),{label:"上次登录"},{default:N(()=>[ot(Q(r.profile.user.loginDate),1)]),_:1})]),_:1})])])):ht("",!0)]}),_:1},8,["loading"]))}});export{Sa as _};
