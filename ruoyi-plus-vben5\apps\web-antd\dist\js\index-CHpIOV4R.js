import{f as p,d as w,_ as l}from"./bootstrap-DCMzVRvD.js";import{d as x,s as C,v as M,x as H,y as W,q as _,z as y}from"../jse/index-index-C-MnMZEz.js";const E=x({compatConfig:{MODE:3},name:"ResizeObserver",props:{disabled:Boolean,onResize:Function},emits:["resize"],setup(i,v){let{slots:c}=v;const n=C({width:0,height:0,offsetHeight:0,offsetWidth:0});let h=null,s=null;const r=()=>{s&&(s.disconnect(),s=null)},b=e=>{const{onResize:t}=i,o=e[0].target,{width:O,height:R}=o.getBoundingClientRect(),{offsetWidth:a,offsetHeight:f}=o,g=Math.floor(O),u=Math.floor(R);if(n.width!==g||n.height!==u||n.offsetWidth!==a||n.offsetHeight!==f){const m={width:g,height:u,offsetWidth:a,offsetHeight:f};l(n,m),t&&Promise.resolve().then(()=>{t(l(l({},m),{offsetWidth:a,offsetHeight:f}),o)})}},z=y(),d=()=>{const{disabled:e}=i;if(e){r();return}const t=p(z);t!==h&&(r(),h=t),!s&&t&&(s=new w(b),s.observe(t))};return M(()=>{d()}),H(()=>{d()}),W(()=>{r()}),_(()=>i.disabled,()=>{d()},{flush:"post"}),()=>{var e;return(e=c.default)===null||e===void 0?void 0:e.call(c)[0]}}});export{E as R};
