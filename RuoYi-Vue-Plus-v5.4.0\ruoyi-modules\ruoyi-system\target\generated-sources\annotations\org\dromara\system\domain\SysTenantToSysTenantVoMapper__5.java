package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__5;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysTenantBoToSysTenantMapper__5.class,SysTenantVoToSysTenantMapper__5.class},
    imports = {}
)
public interface SysTenantToSysTenantVoMapper__5 extends BaseMapper<SysTenant, SysTenantVo> {
}
