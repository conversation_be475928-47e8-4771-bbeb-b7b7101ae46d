const l={id:"id",pid:"parentId",children:"children"},d=r=>Object.assign({},l,r);function N(r,o={}){const c=d(o),t=new Map,n=[],{id:s,pid:f,children:i}=c;for(const e of r)e[i]=e[i]||[],t.set(e[s],e);for(const e of r){const a=t.get(e[f]);(a?a[i]:n).push(e)}return n}function E(r,o={}){o=d(o);const{children:c}=o,t=[...r];for(let n=0;n<t.length;n++)t[n][c]&&t.splice(n+1,0,...t[n][c]);return t}function h(r,o,c={}){r.forEach(t=>{const n=o(t,c)||t;t.children&&h(t.children,o,n)})}function I(r,o="label",c="-"){function t(n,s=[]){const f=[...s,n[o]];n.fullName=f.join(c),n.children&&n.children.length>0&&n.children.forEach(i=>{t(i,f)})}r.forEach(n=>{t(n)})}function p(r,o,c={}){const t=d(c),{id:n,children:s}=t,f=[];function i(e,a){if(e[n]===a)return!0;if(e[s]){for(const u of e[s])if(i(u,a))return f.push(e[n]),!0}return!1}for(const e of r)if(i(e,o))break;return f.sort()}function T(r,o,c={}){const t=new Set;return o.forEach(n=>{p(r,n,c).forEach(s=>{t.add(s)})}),[...t].sort()}export{I as a,h as e,T as f,N as l,E as t};
