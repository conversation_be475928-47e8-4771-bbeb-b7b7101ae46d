{"doc": " OSS对象存储 配置属性\n\n <AUTHOR> Li\n", "fields": [{"name": "tenantId", "doc": " 租户id\n"}, {"name": "endpoint", "doc": " 访问站点\n"}, {"name": "domain", "doc": " 自定义域名\n"}, {"name": "prefix", "doc": " 前缀\n"}, {"name": "accessKey", "doc": " ACCESS_KEY\n"}, {"name": "secret<PERSON>ey", "doc": " SECRET_KEY\n"}, {"name": "bucketName", "doc": " 存储空间名\n"}, {"name": "region", "doc": " 存储区域\n"}, {"name": "isHttps", "doc": " 是否https（Y=是,N=否）\n"}, {"name": "accessPolicy", "doc": " 桶权限类型(0private 1public 2custom)\n"}], "enumConstants": [], "methods": [], "constructors": []}