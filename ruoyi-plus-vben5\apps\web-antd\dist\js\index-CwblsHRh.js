var K=Object.defineProperty;var P=Object.getOwnPropertySymbols;var Q=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var S=(p,m,s)=>m in p?K(p,m,{enumerable:!0,configurable:!0,writable:!0,value:s}):p[m]=s,T=(p,m)=>{for(var s in m||(m={}))Q.call(m,s)&&S(p,s,m[s]);if(P)for(var s of P(m))X.call(m,s)&&S(p,s,m[s]);return p};var b=(p,m,s)=>new Promise((M,c)=>{var x=r=>{try{y(s.next(r))}catch(h){c(h)}},I=r=>{try{y(s.throw(r))}catch(h){c(h)}},y=r=>r.done?M(r.value):Promise.resolve(r.value).then(x,I);y((s=s.apply(p,m)).next())});import{at as Z,as as V,$ as g,an as ee}from"./bootstrap-DCMzVRvD.js";import{v as te}from"./vxe-table-DzEj5Fop.js";import{b as oe,c as ae,e as q,h as re}from"./index-ocPq22VW.js";import{_ as se}from"./table-switch.vue_vue_type_script_setup_true_lang-BPKnQ2Wy.js";import{c as ie}from"./download-UJak946_.js";import{q as ne,c as me,_ as pe}from"./user-drawer.vue_vue_type_script_setup_true_lang-Bod6Baeq.js";import{_ as le}from"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import{_ as de}from"./user-import-modal.vue_vue_type_script_setup_true_lang-DhHbe8Oa.js";import{_ as ue}from"./user-info-modal.vue_vue_type_script_setup_true_lang-D-sXDAcL.js";import{_ as ce}from"./user-reset-pwd-modal.vue_vue_type_script_setup_true_lang-B34FCKYX.js";import U from"./index-BeyziwLP.js";import{D as fe}from"./index-BIMmoqOy.js";import{M as ge,a as Y}from"./index-Bg2oL4a6.js";import{A as _e}from"./index-BELOxkuV.js";import{_ as Ce}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as ve,p as ye,l as B,S as he,h as _,o as d,w as a,j as ke,a as n,b as e,c as E,f as be,F as xe,T as C,k as u,t as v,a0 as De}from"../jse/index-index-C-MnMZEz.js";import{u as w}from"./use-modal-CeMSCP2m.js";import{u as Me}from"./use-vxe-grid-BC7vZzEr.js";import{u as Ie}from"./use-drawer-6qcpK-D1.js";import{P as we}from"./index-DNdMANjv.js";import{g as $e}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./index-D-hwdOI6.js";import"./index-D1nLcUEe.js";import"./index-ErD4UjKl.js";import"./popup-D6rC6QBG.js";import"./data-CPbGrVe8.js";import"./dict-BLkXAGS5.js";import"./index-B6iusSRX.js";import"./tree-DFBawhPd.js";import"./index-BLwHKR_M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-BxBCzu2M.js";import"./index-CHpIOV4R.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./relativeTime-DEqmspR6.js";import"./render-BxXtQdeV.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./index-D59rZjD-.js";import"./Dropdown-BOZk78PH.js";import"./move-DLDqWE9R.js";import"./slide-B82O6h2Y.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./index-qvRUEWLR.js";import"./x-Bfkqqjgb.js";const Ae={class:"flex h-full gap-[8px]"},zt=ve({__name:"index",setup(p){const[m,s]=w({connectedComponent:de});function M(){s.open()}const c=ye([]),x={schema:ne(),commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",handleReset:()=>b(null,null,function*(){c.value=[];const{formApi:o,reload:t}=r;yield o.resetForm();const l=o.form.values;o.setLatestSubmissionValues(l),yield t(l)}),fieldMappingTime:[["createTime",["params[beginTime]","params[endTime]"],["YYYY-MM-DD 00:00:00","YYYY-MM-DD 23:59:59"]]]},I={checkboxConfig:{highlight:!0,reserve:!0,trigger:"default",checkMethod:({row:o})=>(o==null?void 0:o.userId)!==1},columns:me,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(l,...R)=>b(null,[l,...R],function*({page:o},t={}){return c.value.length===1?t.deptId=c.value[0]:Reflect.deleteProperty(t,"deptId"),yield ae(T({pageNum:o.currentPage,pageSize:o.pageSize},t))})}},headerCellConfig:{height:44},cellConfig:{height:48},rowConfig:{keyField:"userId"},id:"system-user-index"},[y,r]=Me({formOptions:x,gridOptions:I}),[h,D]=Ie({connectedComponent:pe});function N(){D.setData({}),D.open()}function F(o){D.setData({id:o.userId}),D.open()}function z(o){return b(this,null,function*(){yield q([o.userId]),yield r.query()})}function O(){const t=r.grid.getCheckboxRecords().map(l=>l.userId);ee.confirm({title:"提示",okType:"danger",content:`确认删除选中的${t.length}条记录吗？`,onOk:()=>b(null,null,function*(){yield q(t),yield r.query()})})}function j(){ie(re,"用户管理",r.formApi.form.values,{fieldMappingTime:x.fieldMappingTime})}const[L,$]=w({connectedComponent:ue});function G(o){$.setData({userId:o.userId}),$.open()}const[W,A]=w({connectedComponent:ce});function H(o){A.setData({record:o}),A.open()}const{hasAccessByCodes:J}=Z();return(o,t)=>{const l=B("a-button"),R=B("ghost-button"),f=he("access");return d(),_(e(Ce),{"auto-content-height":!0},{default:a(()=>[ke("div",Ae,[n(le,{"select-dept-id":c.value,"onUpdate:selectDeptId":t[0]||(t[0]=i=>c.value=i),class:"w-[260px]",onReload:t[1]||(t[1]=()=>e(r).reload()),onSelect:t[2]||(t[2]=()=>e(r).reload())},null,8,["select-dept-id"]),n(e(y),{class:"flex-1 overflow-hidden","table-title":"用户列表"},{"toolbar-tools":a(()=>[n(e(U),null,{default:a(()=>[C((d(),_(l,{onClick:j},{default:a(()=>[u(v(e(g)("pages.common.export")),1)]),_:1})),[[f,["system:user:export"],"code"]]),C((d(),_(l,{onClick:M},{default:a(()=>[u(v(e(g)("pages.common.import")),1)]),_:1})),[[f,["system:user:import"],"code"]]),C((d(),_(l,{disabled:!e(te)(e(r)),danger:"",type:"primary",onClick:O},{default:a(()=>[u(v(e(g)("pages.common.delete")),1)]),_:1},8,["disabled"])),[[f,["system:user:remove"],"code"]]),C((d(),_(l,{type:"primary",onClick:N},{default:a(()=>[u(v(e(g)("pages.common.add")),1)]),_:1})),[[f,["system:user:add"],"code"]])]),_:1})]),avatar:a(({row:i})=>[n(e(_e),{src:i.avatar||e(De).app.defaultAvatar},null,8,["src"])]),status:a(({row:i})=>[n(e(se),{value:i.status,"onUpdate:value":k=>i.status=k,api:()=>e(oe)(i),disabled:i.userId===1||!e(J)(["system:user:edit"]),onReload:t[3]||(t[3]=()=>e(r).query())},null,8,["value","onUpdate:value","api","disabled"])]),action:a(({row:i})=>[i.userId!==1?(d(),E(xe,{key:0},[n(e(U),null,{default:a(()=>[C((d(),_(R,{onClick:V(k=>F(i),["stop"])},{default:a(()=>[u(v(e(g)("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[f,["system:user:edit"],"code"]]),n(e(we),{"get-popup-container":e($e),placement:"left",title:"确认删除？",onConfirm:k=>z(i)},{default:a(()=>[C((d(),_(R,{danger:"",onClick:t[4]||(t[4]=V(()=>{},["stop"]))},{default:a(()=>[u(v(e(g)("pages.common.delete")),1)]),_:1})),[[f,["system:user:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024),n(e(fe),{placement:"bottomRight"},{overlay:a(()=>[n(e(ge),null,{default:a(()=>[n(e(Y),{key:"1",onClick:k=>G(i)},{default:a(()=>t[7]||(t[7]=[u(" 用户信息 ")])),_:2,__:[7]},1032,["onClick"]),C((d(),E("span",null,[n(e(Y),{key:"2",onClick:k=>H(i)},{default:a(()=>t[8]||(t[8]=[u(" 重置密码 ")])),_:2,__:[8]},1032,["onClick"])])),[[f,["system:user:resetPwd"],"code"]])]),_:2},1024)]),default:a(()=>[n(l,{size:"small",type:"link"},{default:a(()=>[u(v(e(g)("pages.common.more")),1)]),_:1})]),_:2},1024)],64)):be("",!0)]),_:1})]),n(e(m),{onReload:t[5]||(t[5]=i=>e(r).query())}),n(e(h),{onReload:t[6]||(t[6]=i=>e(r).query())}),n(e(L)),n(e(W))]),_:1})}}});export{zt as default};
