{"doc": " 请假Service业务层处理\n\n <AUTHOR>\n @date 2023-07-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "eval", "paramTypes": ["java.lang.Integer"], "doc": " spel条件表达：判断小于2\n\n @param leaveDays 待判断的变量（可不传自行返回true或false）\n @return boolean\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询请假\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询请假列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 查询请假列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 新增请假\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 修改请假\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.List"], "doc": " 批量删除请假\n"}, {"name": "processHandler", "paramTypes": ["org.dromara.common.core.domain.event.ProcessEvent"], "doc": " 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成，单任务完成等)\n 正常使用只需#processEvent.flowCode=='leave1'\n 示例为了方便则使用startsWith匹配了全部示例key\n\n @param processEvent 参数\n"}, {"name": "processTaskHandler", "paramTypes": ["org.dromara.common.core.domain.event.ProcessTaskEvent"], "doc": " 执行任务创建监听\n 示例：也可通过  @EventListener(condition = \"#processTaskEvent.flowCode=='leave1'\")进行判断\n 在方法中判断流程节点key\n if (\"xxx\".equals(processTaskEvent.getNodeCode())) {\n //执行业务逻辑\n }\n\n @param processTaskEvent 参数\n"}, {"name": "processDeleteHandler", "paramTypes": ["org.dromara.common.core.domain.event.ProcessDeleteEvent"], "doc": " 监听删除流程事件\n 正常使用只需#processDeleteEvent.flowCode=='leave1'\n 示例为了方便则使用startsWith匹配了全部示例key\n\n @param processDeleteEvent 参数\n"}], "constructors": []}