{"doc": " 数据权限拦截器\n\n <AUTHOR> Li\n @version 3.5.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "before<PERSON><PERSON><PERSON>", "paramTypes": ["org.apache.ibatis.executor.Executor", "org.apache.ibatis.mapping.MappedStatement", "java.lang.Object", "org.apache.ibatis.session.RowBounds", "org.apache.ibatis.session.ResultHandler", "org.apache.ibatis.mapping.BoundSql"], "doc": " 在执行查询之前，检查并处理数据权限相关逻辑\n\n @param executor      MyBatis 执行器对象\n @param ms            映射语句对象\n @param parameter     方法参数\n @param rowBounds     分页对象\n @param resultHandler 结果处理器\n @param boundSql      绑定的 SQL 对象\n @throws SQLException 如果发生 SQL 异常\n"}, {"name": "beforePrepare", "paramTypes": ["org.apache.ibatis.executor.statement.StatementHandler", "java.sql.Connection", "java.lang.Integer"], "doc": " 在准备 SQL 语句之前，检查并处理更新和删除操作的数据权限相关逻辑\n\n @param sh                 MyBatis StatementHandler 对象\n @param connection         数据库连接对象\n @param transactionTimeout 事务超时时间\n"}, {"name": "processSelect", "paramTypes": ["net.sf.jsqlparser.statement.select.Select", "int", "java.lang.String", "java.lang.Object"], "doc": " 处理 SELECT 查询语句中的 WHERE 条件\n\n @param select SELECT 查询对象\n @param index  查询语句的索引\n @param sql    查询语句\n @param obj    WHERE 条件参数\n"}, {"name": "processUpdate", "paramTypes": ["net.sf.jsqlparser.statement.update.Update", "int", "java.lang.String", "java.lang.Object"], "doc": " 处理 UPDATE 语句中的 WHERE 条件\n\n @param update UPDATE 查询对象\n @param index  查询语句的索引\n @param sql    查询语句\n @param obj    WHERE 条件参数\n"}, {"name": "processDelete", "paramTypes": ["net.sf.jsqlparser.statement.delete.Delete", "int", "java.lang.String", "java.lang.Object"], "doc": " 处理 DELETE 语句中的 WHERE 条件\n\n @param delete DELETE 查询对象\n @param index  查询语句的索引\n @param sql    查询语句\n @param obj    WHERE 条件参数\n"}, {"name": "setWhere", "paramTypes": ["net.sf.jsqlparser.statement.select.PlainSelect", "java.lang.String"], "doc": " 设置 SELECT 语句的 WHERE 条件\n\n @param plainSelect       SELECT 查询对象\n @param mappedStatementId 映射语句的 ID\n"}, {"name": "buildTableExpression", "paramTypes": ["net.sf.jsqlparser.schema.Table", "net.sf.jsqlparser.expression.Expression", "java.lang.String"], "doc": " 构建表达式，用于处理表的数据权限\n\n @param table        表对象\n @param where        WHERE 条件表达式\n @param whereSegment WHERE 条件片段\n @return 构建的表达式\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String"], "doc": " 构造函数，初始化 PlusDataPermissionHandler 实例\n\n @param mapperPackage 扫描的映射器包\n"}]}