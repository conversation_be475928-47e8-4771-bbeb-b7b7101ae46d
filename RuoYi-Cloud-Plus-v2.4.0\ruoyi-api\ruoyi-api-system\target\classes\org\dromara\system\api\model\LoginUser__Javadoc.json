{"doc": " 用户信息\n\n <AUTHOR>\n", "fields": [{"name": "tenantId", "doc": " 租户ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "deptId", "doc": " 部门ID\n"}, {"name": "deptCategory", "doc": " 部门类别编码\n"}, {"name": "deptName", "doc": " 部门名\n"}, {"name": "token", "doc": " 用户唯一标识\n"}, {"name": "userType", "doc": " 用户类型\n"}, {"name": "loginTime", "doc": " 登录时间\n"}, {"name": "expireTime", "doc": " 过期时间\n"}, {"name": "ipaddr", "doc": " 登录IP地址\n"}, {"name": "loginLocation", "doc": " 登录地点\n"}, {"name": "browser", "doc": " 浏览器类型\n"}, {"name": "os", "doc": " 操作系统\n"}, {"name": "menuPermission", "doc": " 菜单权限\n"}, {"name": "rolePermission", "doc": " 角色权限\n"}, {"name": "username", "doc": " 用户名\n"}, {"name": "nickname", "doc": " 用户昵称\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "roles", "doc": " 角色对象\n"}, {"name": "posts", "doc": " 岗位对象\n"}, {"name": "roleId", "doc": " 数据权限 当前角色ID\n"}, {"name": "client<PERSON>ey", "doc": " 客户端\n"}, {"name": "deviceType", "doc": " 设备类型\n"}], "enumConstants": [], "methods": [{"name": "getLoginId", "paramTypes": [], "doc": " 获取登录id\n"}], "constructors": []}