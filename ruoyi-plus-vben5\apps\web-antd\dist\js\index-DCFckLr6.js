import{y as e}from"./bootstrap-DCMzVRvD.js";function s(n){return e.get(`/workflow/instance/getInfo/${n}`)}function o(n){return e.get("/workflow/instance/pageByRunning",{params:n})}function r(n){return e.get("/workflow/instance/pageByFinish",{params:n})}function a(n){return e.deleteWithMsg(`/workflow/instance/deleteByInstanceIds/${n}`)}function i(n){return e.putWithMsg("/workflow/instance/cancelProcessApply",n)}function c(n){return e.get("/workflow/instance/pageByCurrent",{params:n})}function f(n){return e.get(`/workflow/instance/flowHisTaskList/${n}`)}function u(n){return e.postWithMsg("/workflow/instance/invalid",n)}export{r as a,c as b,i as c,a as d,f,s as g,o as p,u as w};
