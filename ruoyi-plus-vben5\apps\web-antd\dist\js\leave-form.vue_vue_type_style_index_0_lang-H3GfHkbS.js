var d=(g,m,o)=>new Promise((l,u)=>{var _=t=>{try{n(o.next(t))}catch(i){u(i)}},r=t=>{try{n(o.throw(t))}catch(i){u(i)}},n=t=>t.done?l(t.value):Promise.resolve(t.value).then(_,r);n((o=o.apply(g,m)).next())});import{av as I,aj as N,aB as W,ax as z,aw as U,ao as $}from"./bootstrap-DCMzVRvD.js";import{d as q,B as y,p as E,E as L,v as O,U as v,l as P,h as B,o as h,w as D,j as G,T as J,f as Y,c as K,a as w,b as C,k as F}from"../jse/index-index-C-MnMZEz.js";import{s as Q}from"./index-CZhogUxH.js";import{_ as X}from"./apply-modal.vue_vue_type_script_setup_true_lang-DPxr8wiv.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{c as Z,d as ee,e as ae}from"./index-Bt61TO42.js";import{m as te}from"./data-DSNSln3q.js";import{_ as oe}from"./leave-description.vue_vue_type_script_setup_true_lang-Cq7KQRsz.js";import{C as se}from"./index-C1KbofmV.js";import{u as re}from"./use-modal-CeMSCP2m.js";const ne={id:"leave-form"},le={key:1,class:"flex justify-end gap-2"},Ce=q({__name:"leave-form",setup(g){var M,V;const m=I(),o=((M=m.query)==null?void 0:M.readonly)==="true",l=(V=m.query)==null?void 0:V.id,u=y(()=>!o),[_,r]=N({commonConfig:{formItemClass:"col-span-2",labelWidth:100,componentProps:{class:"w-full",disabled:o}},schema:te(!o),showDefaultActions:!1,wrapperClass:"grid-cols-2"}),n=E(),t=y(()=>o&&n.value),i=L("cardRef");O(()=>d(null,null,function*(){if(l){const a=yield Z(l);n.value=a,yield r.setValues(a);const e=[v(a.startDate),v(a.endDate)];yield r.setFieldValue("dateRange",e),o&&(window.parent.postMessage({type:"mounted"},"*"),setTimeout(()=>{var p,f;const s=(p=i.value)==null?void 0:p.$el,c=(f=s==null?void 0:s.offsetHeight)!=null?f:0;c&&window.parent.postMessage({type:"height",height:c},"*")}))}}));const k=W();function b(){return d(this,null,function*(){const{valid:a}=yield r.validate();if(!a)return;let e=U(yield r.getValues());return e=$(e,"flowType"),e.startDate=v(e.dateRange[0]).format("YYYY-MM-DD HH:mm:ss"),e.endDate=v(e.dateRange[1]).format("YYYY-MM-DD HH:mm:ss"),l?(e.id=l,yield ee(e)):yield ae(e)})}const[T,R]=re({connectedComponent:X});function x(){return d(this,null,function*(){try{yield b(),k.push("/demo/leave")}catch(a){console.error(a)}})}function A(){return d(this,null,function*(){var a;try{const e=yield b(),s={leaveDays:e.leaveDays,userList:["1","3","4"]},c=yield r.getValues(),p=(a=c==null?void 0:c.flowType)!=null?a:"leave1",f={businessId:e.id,flowCode:p,variables:s},{taskId:j}=yield Q(f);R.setData({taskId:j,taskVariables:s,variables:{}}),R.open()}catch(e){console.error(e)}})}function S(){r.resetForm(),k.push("/demo/leave")}const H=y(()=>t.value?"small":"default");return(a,e)=>{const s=P("a-button");return h(),B(C(se),{ref_key:"cardRef",ref:i,size:H.value},{default:D(()=>[G("div",ne,[J(w(C(_),null,null,512),[[z,!t.value]]),t.value?(h(),B(oe,{key:0,data:n.value},null,8,["data"])):Y("",!0),u.value?(h(),K("div",le,[w(s,{onClick:x},{default:D(()=>e[0]||(e[0]=[F("暂存")])),_:1,__:[0]}),w(s,{type:"primary",onClick:A},{default:D(()=>e[1]||(e[1]=[F("提交")])),_:1,__:[1]})])):Y("",!0),w(C(T),{onComplete:S})])]),_:1},8,["size"])}}});export{Ce as _};
