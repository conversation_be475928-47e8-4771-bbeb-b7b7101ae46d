#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules/vitepress/bin/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules/vitepress/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules/vitepress/bin/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules/vitepress/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitepress/bin/vitepress.js" "$@"
else
  exec node  "$basedir/../vitepress/bin/vitepress.js" "$@"
fi
