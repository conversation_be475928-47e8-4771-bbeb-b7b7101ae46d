var v=(k,m,n)=>new Promise((u,c)=>{var B=g=>{try{h(n.next(g))}catch(b){c(b)}},y=g=>{try{h(n.throw(g))}catch(b){c(b)}},h=g=>g.done?u(g.value):Promise.resolve(g.value).then(B,y);h((n=n.apply(k,m)).next())});import{a<PERSON> as he,bK as Me,aB as Se,ar as Oe,an as O,ap as Ae}from"./bootstrap-DCMzVRvD.js";import{a as i,d as $e,p as A,B as I,V as Te,q as Be,y as Pe,ab as Ne,l as xe,h as d,o as r,b as a,w as s,j as l,c as De,f,t as $,L as je,k as p}from"../jse/index-index-C-MnMZEz.js";import{f as Re,c as ze,d as Fe}from"./index-DCFckLr6.js";import{g as Ve,t as P,u as <PERSON>e,e as He}from"./index-CZhogUxH.js";import{r as Ue}from"./render-BxXtQdeV.js";import{a as j}from"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import{_ as qe}from"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import{_ as We}from"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import{_ as Le}from"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import T from"./user-select-modal-9zMWfXzj.js";import{_ as Ye}from"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import{_ as Ge}from"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import{C as Je}from"./index-C1KbofmV.js";import{_ as Qe}from"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import{a as Xe,T as Y}from"./index-BaVK9zYh.js";import R from"./index-BeyziwLP.js";import{M as Ze,a as N}from"./index-Bg2oL4a6.js";import{a as Ke}from"./get-popup-container-P4S1sr5h.js";import{D as et}from"./index-BIMmoqOy.js";import{_ as tt}from"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import{u as C}from"./use-modal-CeMSCP2m.js";import nt from"./index-i2_yEmR1.js";var at={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};function G(k){for(var m=1;m<arguments.length;m++){var n=arguments[m]!=null?Object(arguments[m]):{},u=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(u=u.concat(Object.getOwnPropertySymbols(n).filter(function(c){return Object.getOwnPropertyDescriptor(n,c).enumerable}))),u.forEach(function(c){ot(k,c,n[c])})}return k}function ot(k,m,n){return m in k?Object.defineProperty(k,m,{value:n,enumerable:!0,configurable:!0,writable:!0}):k[m]=n,k}var z=function(m,n){var u=G({},m,n.attrs);return i(he,G({},u,{icon:at}),null)};z.displayName="CopyOutlined";z.inheritAttrs=!1;const st={class:"flex items-center gap-2"},it={class:"flex flex-col gap-5 p-4"},rt={class:"flex flex-col gap-3"},lt={class:"flex items-center gap-2"},dt={class:"text-2xl font-bold"},ut={class:"flex items-center gap-2"},ft={class:"flex items-center opacity-50"},pt={class:"flex items-center gap-1"},mt={class:"flex items-center gap-1"},ct={key:0,class:"border-t-solid bg-background absolute bottom-0 left-0 w-full border-t-[1px] p-3"},kt={class:"flex justify-end"},Ft=$e({name:"ApprovalPanel",inheritAttrs:!1,__name:"approval-panel",props:{task:{},type:{}},emits:["reload"],setup(k,{emit:m}){const n=k,u=m,c=A(),B=I(()=>c.value?Number(c.value.nodeRatio)>0:!1),y=I(()=>{const t={};return c.value&&c.value.buttonList.forEach(e=>{t[e.code]=e.show}),t}),h=I(()=>{const t=new Set(["addSign","subSign","transfer","trust"]);return Object.keys(y.value).some(e=>t.has(e)&&y.value[e])}),g=I(()=>{var t,e;return!(n.type==="readonly"||n.type==="myself"&&["finish","invalid"].includes((e=(t=n.task)==null?void 0:t.flowStatus)!=null?e:""))}),b=A(),x=A(!1),D=A(!1),F=A(300);Te("message",t=>{const e=t.data;if(Me(e)&&(e.type==="mounted"&&(D.value=!0),e.type==="height")){const o=e.height;F.value=o}});function V(t){return v(this,null,function*(){try{if(!t)return null;x.value=!0,D.value=!1;const e=yield Re(t.businessId);b.value=e;const o=yield Ve(n.task.id);c.value=o}catch(e){console.error(e)}finally{x.value=!1}})}Be(()=>n.task,V),Pe(()=>b.value=void 0);const J=I(()=>{var t;return((t=n.task)==null?void 0:t.flowStatus)==="waiting"});function Q(){return v(this,null,function*(){O.confirm({title:"提示",content:"确定要撤销该申请吗？",centered:!0,okButtonProps:{danger:!0},onOk:()=>v(null,null,function*(){yield ze({businessId:n.task.businessId,message:"申请人撤销流程！"}),u("reload")})})})}const E=I(()=>n.task?["back","cancel","draft"].includes(n.task.flowStatus):!1),X=Se();function Z(){var e;const t=(e=n.task)==null?void 0:e.formPath;t&&X.push({path:t,query:{id:n.task.businessId}})}function K(){O.confirm({title:"提示",content:"确定删除该申请吗？",centered:!0,okButtonProps:{danger:!0},onOk:()=>v(null,null,function*(){yield Fe([n.task.id]),u("reload")})})}const[ee,H]=C({connectedComponent:Ye});function te(){var t,e,o;H.setData({taskId:(t=n.task)==null?void 0:t.id,definitionId:(e=n.task)==null?void 0:e.definitionId,nodeCode:(o=n.task)==null?void 0:o.nodeCode}),H.open()}function ne(){j({title:"审批终止",description:"确定终止当前审批流程吗？",onOk:t=>v(null,null,function*(){yield He({taskId:n.task.id,comment:t}),u("reload")})})}const[ae,U]=C({connectedComponent:qe});function oe(){var o,w,M,_,S;const t=(w=(o=y.value)==null?void 0:o.copy)!=null?w:!1,e=(_=(M=y.value)==null?void 0:M.pop)!=null?_:!1;U.setData({taskId:(S=n.task)==null?void 0:S.id,copyPermission:t,assignPermission:e}),U.open()}const[se,ie]=C({connectedComponent:T});function re(t){if(t.length===0)return;const e=t[0];j({title:"委托",description:`确定委托给[${e==null?void 0:e.nickName}]吗?`,onOk:o=>v(null,null,function*(){yield P({taskId:n.task.id,userId:e.userId,message:o},"delegateTask"),u("reload")})})}const[le,de]=C({connectedComponent:T});function ue(t){if(t.length===0)return;const e=t[0];j({title:"转办",description:`确定转办给[${e==null?void 0:e.nickName}]吗?`,onOk:o=>v(null,null,function*(){yield P({taskId:n.task.id,userId:e.userId,message:o},"transferTask"),u("reload")})})}const[fe,pe]=C({connectedComponent:T});function me(t){if(t.length===0)return;const e=t.map(o=>o.userId);O.confirm({title:"提示",content:"确认加签吗?",centered:!0,onOk:()=>v(null,null,function*(){yield P({taskId:n.task.id,userIds:e},"addSignature"),u("reload")})})}const[ce,ke]=C({connectedComponent:T});function ye(t){if(t.length===0)return;const e=t.map(o=>o.userId);O.confirm({title:"提示",content:"确认减签吗?",centered:!0,onOk:()=>v(null,null,function*(){yield P({taskId:n.task.id,userIds:e},"reductionSignature"),u("reload")})})}const[ve,q]=C({connectedComponent:Ge});function ge(){var t;q.setData({taskId:(t=n.task)==null?void 0:t.id}),q.open()}const[be,Ce]=C({connectedComponent:T});function we(t){if(t.length===0)return;const e=t[0];e&&O.confirm({title:"修改办理人",content:`确定修改办理人为${e==null?void 0:e.nickName}吗?`,centered:!0,onOk:()=>v(null,null,function*(){yield Ee([n.task.id],e.userId),u("reload")})})}const{copy:_e}=Ne({legacy:!0});function Ie(t){return v(this,null,function*(){yield _e(t),Ae.success("复制成功")})}return(t,e)=>{const o=xe("a-button");return t.task?(r(),d(a(Je),{key:0,"body-style":{overflowY:"auto",height:"100%"},loading:x.value,class:"thin-scrollbar flex-1 overflow-y-hidden",size:"small"},{title:s(()=>[l("div",st,[l("div",null,"编号: "+$(t.task.id),1),i(a(z),{class:"cursor-pointer",onClick:e[0]||(e[0]=w=>Ie(t.task.id))})])]),extra:s(()=>[i(o,{size:"small",onClick:e[1]||(e[1]=()=>V(t.task))},{default:s(()=>e[10]||(e[10]=[l("div",{class:"flex items-center justify-center"},[l("span",{class:"icon-[material-symbols--refresh] size-24px"})],-1)])),_:1,__:[10]})]),default:s(()=>[l("div",it,[l("div",rt,[l("div",lt,[l("div",dt,$(t.task.flowName),1),l("div",null,[(r(),d(je(a(Ue)(t.task.flowStatus,a(Oe).WF_BUSINESS_STATUS))))])]),l("div",ut,[i(a(Qe),{alt:t.task.createByName,class:"bg-primary size-[28px] rounded-full text-white",src:""},null,8,["alt"]),l("span",null,$(t.task.createByName),1),l("div",ft,[l("div",pt,[e[11]||(e[11]=l("span",{class:"icon-[bxs--category-alt] size-[16px]"},null,-1)),p(" 流程分类: "+$(t.task.categoryName),1)]),i(a(nt),{type:"vertical"}),l("div",mt,[e[12]||(e[12]=l("span",{class:"icon-[mdi--clock-outline] size-[16px]"},null,-1)),p(" 提交时间: "+$(t.task.createTime),1)])])])]),b.value?(r(),d(a(Xe),{key:0,class:"flex-1"},{default:s(()=>[i(a(Y),{key:"1",tab:"审批详情"},{default:s(()=>[i(We,{"current-flow-info":b.value,"iframe-loaded":D.value,"iframe-height":F.value,task:t.task},null,8,["current-flow-info","iframe-loaded","iframe-height","task"])]),_:1}),i(a(Y),{key:"2",tab:"审批流程图"},{default:s(()=>[i(Le,{"instance-id":b.value.instanceId},null,8,["instance-id"])]),_:1})]),_:1})):f("",!0)]),e[26]||(e[26]=l("div",{class:"h-[57px]"},null,-1)),g.value?(r(),De("div",ct,[l("div",kt,[t.type==="myself"?(r(),d(a(R),{key:0},{default:s(()=>[J.value?(r(),d(o,{key:0,danger:"",type:"primary",onClick:Q},{default:s(()=>e[13]||(e[13]=[p(" 撤销申请 ")])),_:1,__:[13]})):f("",!0),E.value?(r(),d(o,{key:1,onClick:Z},{default:s(()=>e[14]||(e[14]=[p(" 重新编辑 ")])),_:1,__:[14]})):f("",!0),E.value?(r(),d(o,{key:2,danger:"",type:"primary",onClick:K},{default:s(()=>e[15]||(e[15]=[p(" 删除 ")])),_:1,__:[15]})):f("",!0)]),_:1})):f("",!0),t.type==="approve"?(r(),d(a(R),{key:1},{default:s(()=>{var w,M;return[i(o,{type:"primary",onClick:oe},{default:s(()=>e[16]||(e[16]=[p("通过")])),_:1,__:[16]}),(w=y.value)!=null&&w.termination?(r(),d(o,{key:0,danger:"",type:"primary",onClick:ne},{default:s(()=>e[17]||(e[17]=[p(" 终止 ")])),_:1,__:[17]})):f("",!0),(M=y.value)!=null&&M.back?(r(),d(o,{key:1,danger:"",type:"primary",onClick:te},{default:s(()=>e[18]||(e[18]=[p(" 驳回 ")])),_:1,__:[18]})):f("",!0),i(a(et),{"get-popup-container":a(Ke),placement:"bottomRight"},{overlay:s(()=>[i(a(Ze),null,{default:s(()=>{var _,S,W,L;return[(_=y.value)!=null&&_.trust?(r(),d(a(N),{key:"1",onClick:e[2]||(e[2]=()=>a(ie).open())},{default:s(()=>e[19]||(e[19]=[p(" 委托 ")])),_:1,__:[19]})):f("",!0),(S=y.value)!=null&&S.transfer?(r(),d(a(N),{key:"2",onClick:e[3]||(e[3]=()=>a(de).open())},{default:s(()=>e[20]||(e[20]=[p(" 转办 ")])),_:1,__:[20]})):f("",!0),B.value&&((W=y.value)!=null&&W.addSign)?(r(),d(a(N),{key:"3",onClick:e[4]||(e[4]=()=>a(pe).open())},{default:s(()=>e[21]||(e[21]=[p(" 加签 ")])),_:1,__:[21]})):f("",!0),B.value&&((L=y.value)!=null&&L.subSign)?(r(),d(a(N),{key:"4",onClick:e[5]||(e[5]=()=>a(ke).open())},{default:s(()=>e[22]||(e[22]=[p(" 减签 ")])),_:1,__:[22]})):f("",!0)]}),_:1})]),default:s(()=>[h.value?(r(),d(o,{key:0},{default:s(()=>e[23]||(e[23]=[p(" 其他 ")])),_:1,__:[23]})):f("",!0)]),_:1},8,["get-popup-container"]),i(a(ae),{onComplete:e[6]||(e[6]=_=>t.$emit("reload"))}),i(a(ee),{onComplete:e[7]||(e[7]=_=>t.$emit("reload"))}),i(a(se),{mode:"single",onFinish:re}),i(a(le),{mode:"single",onFinish:ue}),i(a(fe),{mode:"multiple",onFinish:me}),i(a(ce),{mode:"multiple",onFinish:ye})]}),_:1})):f("",!0),t.type==="admin"?(r(),d(a(R),{key:2},{default:s(()=>[i(o,{onClick:ge},{default:s(()=>e[24]||(e[24]=[p(" 流程干预 ")])),_:1,__:[24]}),i(o,{onClick:e[8]||(e[8]=()=>a(Ce).open())},{default:s(()=>e[25]||(e[25]=[p(" 修改办理人 ")])),_:1,__:[25]}),i(a(ve),{onComplete:e[9]||(e[9]=w=>t.$emit("reload"))}),i(a(be),{mode:"single",onFinish:we})]),_:1})):f("",!0)])])):f("",!0)]),_:1,__:[26]},8,["loading"])):(r(),d(a(tt),{key:1,title:"点击左侧选择"}))}}});export{Ft as _};
