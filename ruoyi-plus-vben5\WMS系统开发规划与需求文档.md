# WMS仓储管理系统开发规划与需求文档

## 📋 项目概述

### 项目背景
基于RuoYi-Plus-Vben5框架的企业级仓储管理系统(WMS)，旨在实现现代化、智能化的仓储作业管理，提升仓储效率，降低运营成本。

### 项目目标
- 🎯 实现完整的仓储业务流程数字化管理
- 📊 提供实时库存数据和智能分析
- 🚀 支持多仓库、多组织架构管理
- 📱 支持移动端作业和自动化设备集成
- 💡 提供智能预警和决策支持

### 技术架构
- **前端框架**: Vue 3.5.13 + Vben Admin 5.5.6 + Ant Design Vue 4.2.6
- **状态管理**: Pinia 3.0.2
- **构建工具**: Vite 6.3.4 + TypeScript 5.8.3
- **项目架构**: Monorepo (pnpm workspace)

---

## 🗓️ 开发规划时间表

### 第一阶段：基础数据管理模块 (4周)

#### Week 1-2: 物料主数据管理
**功能点**:
- 物料信息管理（编码、名称、规格、单位等）
- 物料分类管理（多级分类树）
- 物料属性扩展（批次管理、序列号管理、保质期等）
- 供应商物料关联管理

**技术实现**:
```typescript
// 物料主数据模型
interface MaterialVO {
  materialId: string;
  materialCode: string;      // 物料编码
  materialName: string;      // 物料名称
  materialType: string;      // 物料类型
  categoryId: string;        // 分类ID
  specification: string;     // 规格型号
  unit: string;             // 基本单位
  brand: string;            // 品牌
  model: string;            // 型号
  barcode?: string;         // 条码
  qrcode?: string;          // 二维码
  isLotManaged: boolean;    // 是否批次管理
  isSerialManaged: boolean; // 是否序列号管理
  hasExpiryDate: boolean;   // 是否有保质期
  shelfLife?: number;       // 保质期(天)
  safetyStock: number;      // 安全库存
  minStock: number;         // 最小库存
  maxStock: number;         // 最大库存
  abc: string;              // ABC分类
  status: string;           // 状态
  images?: string[];        // 物料图片
  remark?: string;          // 备注
}
```

**交付物**:
- 物料管理页面及CRUD操作
- 物料分类树形结构管理
- 物料导入导出功能
- API接口文档

#### Week 3-4: 货位管理
**功能点**:
- 仓库区域规划（库区、货架、层级、位置）
- 货位编码规则配置
- 货位状态管理（启用、禁用、维修、占用）
- 货位容量管理（重量、体积限制）
- 货位可视化展示

**技术实现**:
```typescript
// 货位管理模型
interface LocationVO {
  locationId: string;
  locationCode: string;      // 货位编码
  locationName: string;      // 货位名称
  warehouseId: string;       // 所属仓库
  zoneCode: string;          // 库区编码
  zoneName: string;          // 库区名称
  aisleCode: string;         // 巷道编码
  shelfCode: string;         // 货架编码
  levelCode: string;         // 层级编码
  positionCode: string;      // 位置编码
  locationType: string;      // 货位类型(存储位/拣货位/暂存位)
  locationStatus: string;    // 货位状态
  maxWeight: number;         // 最大承重(kg)
  maxVolume: number;         // 最大体积(m³)
  currentWeight: number;     // 当前重量
  currentVolume: number;     // 当前体积
  length: number;            // 长度(cm)
  width: number;             // 宽度(cm)
  height: number;            // 高度(cm)
  temperature?: number;      // 温度要求
  humidity?: number;         // 湿度要求
  isReserved: boolean;       // 是否预留
  reservedFor?: string;      // 预留给谁
  sortOrder: number;         // 排序号
  remark?: string;           // 备注
}
```

**交付物**:
- 货位管理页面（树形+表格展示）
- 货位容量配置功能
- 货位状态批量操作
- 货位可视化图表

### 第二阶段：库存管理模块 (3周)

#### Week 5-6: 实时库存管理
**功能点**:
- 库存实时查询（按物料、批次、货位等多维度）
- 库存状态管理（可用、冻结、待检、在途等）
- 库存预警设置（最小库存、最大库存、安全库存）
- 库存流水账查询
- 库存调整功能

**技术实现**:
```typescript
// 库存数据模型
interface InventoryVO {
  inventoryId: string;
  warehouseId: string;       // 仓库ID
  warehouseName: string;     // 仓库名称
  locationId: string;        // 货位ID
  locationCode: string;      // 货位编码
  materialId: string;        // 物料ID
  materialCode: string;      // 物料编码
  materialName: string;      // 物料名称
  lotNumber?: string;        // 批次号
  serialNumber?: string;     // 序列号
  quantity: number;          // 总库存数量
  availableQty: number;      // 可用数量
  frozenQty: number;         // 冻结数量
  allocatedQty: number;      // 已分配数量
  inTransitQty: number;      // 在途数量
  qualityStatus: string;     // 质量状态(合格/待检/不合格)
  manufactureDate?: Date;    // 生产日期
  expiryDate?: Date;         // 到期日期
  supplierBatch?: string;    // 供应商批次
  unitCost: number;          // 单位成本
  totalCost: number;         // 总成本
  lastUpdateTime: Date;      // 最后更新时间
  remark?: string;           // 备注
}

// 库存预警配置
interface InventoryAlertConfigVO {
  configId: string;
  materialId: string;
  warehouseId: string;
  minStock: number;          // 最小库存
  maxStock: number;          // 最大库存
  safetyStock: number;       // 安全库存
  reorderPoint: number;      // 再订购点
  reorderQty: number;        // 再订购量
  expiryDays: number;        // 到期预警天数
  isActive: boolean;         // 是否启用
}
```

**交付物**:
- 库存查询界面（支持多条件筛选）
- 库存预警配置页面
- 库存调整单功能
- 库存流水账报表

#### Week 7: 库存分析报表
**功能点**:
- 库存余额表
- 库存周转率分析
- 呆滞库存分析
- ABC分类分析
- 库存成本分析

**交付物**:
- 库存分析报表页面
- 图表可视化展示
- 报表导出功能

### 第三阶段：入库管理模块 (4周)

#### Week 8-9: 入库计划与预约
**功能点**:
- 入库计划制定
- 供应商送货预约
- 收货时间安排
- 入库任务分配

**技术实现**:
```typescript
// 入库计划模型
interface InboundPlanVO {
  planId: string;
  planNumber: string;        // 计划编号
  warehouseId: string;       // 目标仓库
  inboundType: string;       // 入库类型
  supplierId?: string;       // 供应商ID
  sourceOrderNo?: string;    // 源单号
  planDate: Date;            // 计划日期
  planStartTime: string;     // 计划开始时间
  planEndTime: string;       // 计划结束时间
  status: string;            // 状态
  priority: number;          // 优先级
  dockNumber?: string;       // 月台号
  assignedTo?: string;       // 分配给谁
  items: InboundPlanItemVO[]; // 计划明细
  remark?: string;           // 备注
}

// 入库单据模型
interface InboundOrderVO {
  inboundId: string;
  inboundNumber: string;     // 入库单号
  warehouseId: string;       // 目标仓库
  inboundType: string;       // 入库类型
  supplierId?: string;       // 供应商ID
  supplierName?: string;     // 供应商名称
  sourceOrderNo?: string;    // 源单号
  planDate: Date;            // 计划入库日期
  actualDate?: Date;         // 实际入库日期
  status: string;            // 单据状态
  totalQty: number;          // 总数量
  totalAmount: number;       // 总金额
  dockNumber?: string;       // 月台号
  receiptUser?: string;      // 收货人
  qcUser?: string;           // 质检员
  putawayUser?: string;      // 上架员
  items: InboundItemVO[];    // 入库明细
  attachments?: string[];    // 附件
  remark?: string;           // 备注
  createTime: Date;          // 创建时间
  updateTime: Date;          // 更新时间
}
```

#### Week 10-11: 收货与质检
**功能点**:
- 收货作业（扫码收货、数量核对）
- 质量检验流程
- 不合格品处理
- 收货差异处理

**交付物**:
- 收货作业页面
- 质检流程管理
- 移动端收货界面
- 差异处理流程

### 第四阶段：出库管理模块 (4周)

#### Week 12-13: 出库计划与分配
**功能点**:
- 出库申请管理
- 库存自动分配
- 分配策略配置（FIFO、LIFO、指定批次等）
- 出库任务生成

**技术实现**:
```typescript
// 出库订单模型
interface OutboundOrderVO {
  outboundId: string;
  outboundNumber: string;    // 出库单号
  warehouseId: string;       // 源仓库
  outboundType: string;      // 出库类型
  customerId?: string;       // 客户ID
  customerName?: string;     // 客户名称
  targetOrderNo?: string;    // 目标单号
  planDate: Date;            // 计划出库日期
  actualDate?: Date;         // 实际出库日期
  shipmentDate?: Date;       // 发货日期
  priority: string;          // 优先级
  status: string;            // 单据状态
  pickingStrategy: string;   // 拣货策略
  waveNumber?: string;       // 波次号
  totalQty: number;          // 总数量
  totalAmount: number;       // 总金额
  pickerUser?: string;       // 拣货员
  checkerUser?: string;      // 复核员
  shipperUser?: string;      // 发货员
  items: OutboundItemVO[];   // 出库明细
  pickingTasks?: PickingTaskVO[]; // 拣货任务
  trackingNumber?: string;   // 物流单号
  carrierName?: string;      // 承运商
  remark?: string;           // 备注
}

// 拣货任务模型
interface PickingTaskVO {
  taskId: string;
  outboundId: string;        // 出库单ID
  taskNumber: string;        // 任务编号
  materialId: string;        // 物料ID
  materialCode: string;      // 物料编码
  locationId: string;        // 货位ID
  locationCode: string;      // 货位编码
  lotNumber?: string;        // 批次号
  planQty: number;           // 计划数量
  actualQty?: number;        // 实际数量
  status: string;            // 任务状态
  assignedTo?: string;       // 分配给
  startTime?: Date;          // 开始时间
  endTime?: Date;            // 结束时间
  priority: number;          // 优先级
  remark?: string;           // 备注
}
```

#### Week 14-15: 拣货与复核
**功能点**:
- 拣货路径优化
- 移动端拣货作业
- 拣货任务管理
- 复核包装流程

**交付物**:
- 拣货任务界面
- 移动端拣货功能
- 复核包装页面
- 拣货路径优化算法

### 第五阶段：库内作业模块 (3周)

#### Week 16-17: 移库调拨管理
**功能点**:
- 库内移库（货位间移动）
- 跨库调拨（仓库间调拨）
- 移库任务管理
- 移库审批流程

**技术实现**:
```typescript
// 移库调拨模型
interface TransferOrderVO {
  transferId: string;
  transferNumber: string;    // 移库单号
  transferType: string;      // 移库类型
  sourceWarehouseId: string; // 源仓库
  targetWarehouseId: string; // 目标仓库
  sourceLocationId?: string; // 源货位
  targetLocationId?: string; // 目标货位
  reason: string;            // 移库原因
  status: string;            // 单据状态
  planDate: Date;            // 计划日期
  actualDate?: Date;         // 实际日期
  operatorUser?: string;     // 操作员
  approvalUser?: string;     // 审批人
  approvalTime?: Date;       // 审批时间
  items: TransferItemVO[];   // 移库明细
  remark?: string;           // 备注
}
```

#### Week 18: 盘点管理
**功能点**:
- 盘点计划制定
- 盘点任务分配
- 移动端盘点作业
- 盘点差异处理

**技术实现**:
```typescript
// 盘点单据模型
interface StocktakeVO {
  stocktakeId: string;
  stocktakeNumber: string;   // 盘点单号
  warehouseId: string;       // 盘点仓库
  stocktakeType: string;     // 盘点类型
  stocktakeScope: string;    // 盘点范围
  planDate: Date;            // 计划盘点日期
  actualStartDate?: Date;    // 实际开始日期
  actualEndDate?: Date;      // 实际结束日期
  status: string;            // 盘点状态
  freezeInventory: boolean;  // 是否冻结库存
  supervisorUser?: string;   // 监盘人
  items: StocktakeItemVO[];  // 盘点明细
  differences: StocktakeDiffVO[]; // 盘点差异
  remark?: string;           // 备注
}
```

**交付物**:
- 移库调拨管理页面
- 盘点管理功能
- 移动端盘点界面

### 第六阶段：高级功能模块 (4周)

#### Week 19-20: 智能预警系统
**功能点**:
- 库存预警（低库存、高库存、到期预警）
- 作业预警（超时预警、异常预警）
- 设备预警（故障预警、维护提醒）
- 预警规则配置

**技术实现**:
```typescript
// 预警规则配置
interface AlertRuleVO {
  ruleId: string;
  ruleName: string;          // 规则名称
  ruleType: string;          // 规则类型
  alertLevel: string;        // 预警级别
  triggerCondition: string;  // 触发条件
  alertMessage: string;      // 预警消息
  recipients: string[];      // 接收人
  isActive: boolean;         // 是否启用
  executeTime?: string;      // 执行时间
  lastExecuteTime?: Date;    // 最后执行时间
}

// 预警记录
interface AlertRecordVO {
  alertId: string;
  ruleId: string;            // 规则ID
  alertType: string;         // 预警类型
  alertLevel: string;        // 预警级别
  alertMessage: string;      // 预警消息
  triggerData: any;          // 触发数据
  status: string;            // 处理状态
  handleUser?: string;       // 处理人
  handleTime?: Date;         // 处理时间
  handleRemark?: string;     // 处理备注
  createTime: Date;          // 创建时间
}
```

#### Week 21-22: 报表分析系统
**功能点**:
- 库存分析报表
- 作业效率报表
- 成本分析报表
- 自定义报表配置

**技术实现**:
```typescript
// 报表配置模型
interface ReportConfigVO {
  reportId: string;
  reportName: string;        // 报表名称
  reportType: string;        // 报表类型
  dataSource: string;        // 数据源
  sqlQuery?: string;         // SQL查询
  parameters: ReportParamVO[]; // 参数配置
  chartConfig?: any;         // 图表配置
  refreshInterval?: number;   // 刷新间隔
  isActive: boolean;         // 是否启用
  createdBy: string;         // 创建人
  createTime: Date;          // 创建时间
}
```

---

## 📱 移动端开发规划

### 移动端功能模块

#### 1. 基础功能
- 用户登录/退出
- 仓库切换
- 扫码功能（条码/二维码）
- 消息通知

#### 2. 收货作业
- 收货预约查询
- 扫码收货
- 数量确认
- 质检录入
- 收货完成

#### 3. 上架作业
- 上架任务接收
- 货位扫码确认
- 上架完成确认

#### 4. 拣货作业
- 拣货任务接收
- 拣货路径导航
- 货位扫码确认
- 数量确认
- 拣货完成

#### 5. 盘点作业
- 盘点任务接收
- 货位扫码
- 物料扫码
- 数量录入
- 盘点提交

#### 6. 库存查询
- 实时库存查询
- 批次追溯查询
- 库存预警查看

---

## 🔧 技术实现要点

### 1. 数据库设计
```sql
-- 核心表结构设计
-- 仓库表
CREATE TABLE wms_warehouse (
  warehouse_id BIGINT PRIMARY KEY,
  warehouse_number VARCHAR(50) UNIQUE NOT NULL,
  warehouse_name VARCHAR(100) NOT NULL,
  warehouse_type CHAR(1) DEFAULT '0',
  status CHAR(1) DEFAULT '0',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 物料表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY,
  material_code VARCHAR(50) UNIQUE NOT NULL,
  material_name VARCHAR(200) NOT NULL,
  category_id BIGINT,
  unit VARCHAR(20),
  is_lot_managed TINYINT(1) DEFAULT 0,
  is_serial_managed TINYINT(1) DEFAULT 0,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 货位表
CREATE TABLE wms_location (
  location_id BIGINT PRIMARY KEY,
  location_code VARCHAR(50) UNIQUE NOT NULL,
  warehouse_id BIGINT NOT NULL,
  zone_code VARCHAR(20),
  location_type VARCHAR(20),
  status CHAR(1) DEFAULT '0',
  max_weight DECIMAL(10,2),
  max_volume DECIMAL(10,2),
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 库存表
CREATE TABLE wms_inventory (
  inventory_id BIGINT PRIMARY KEY,
  warehouse_id BIGINT NOT NULL,
  location_id BIGINT,
  material_id BIGINT NOT NULL,
  lot_number VARCHAR(50),
  quantity DECIMAL(12,3) NOT NULL,
  available_qty DECIMAL(12,3) NOT NULL,
  frozen_qty DECIMAL(12,3) DEFAULT 0,
  quality_status VARCHAR(20) DEFAULT 'QUALIFIED',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. API设计规范
```typescript
// RESTful API 设计规范
// GET    /api/wms/materials       - 查询物料列表
// GET    /api/wms/materials/{id}  - 查询物料详情
// POST   /api/wms/materials       - 新增物料
// PUT    /api/wms/materials/{id}  - 更新物料
// DELETE /api/wms/materials/{id}  - 删除物料

// 批量操作
// POST   /api/wms/materials/batch - 批量操作
// GET    /api/wms/materials/export - 导出数据
// POST   /api/wms/materials/import - 导入数据
```

### 3. 缓存策略
```typescript
// Redis 缓存设计
// 库存缓存：inventory:{warehouseId}:{materialId}:{locationId}
// 用户权限缓存：user:permissions:{userId}
// 数据字典缓存：dict:{dictType}
// 会话缓存：session:{token}
```

### 4. 消息队列
```typescript
// 异步任务处理
// 库存更新队列：inventory.update
// 预警通知队列：alert.notification
// 报表生成队列：report.generate
// 数据同步队列：data.sync
```

---

## 🎯 验收标准

### 功能验收标准
1. **业务流程完整性**：各业务流程能够完整闭环
2. **数据准确性**：库存数据实时准确，账实相符
3. **操作便捷性**：用户操作简单便捷，减少人工错误
4. **性能要求**：页面响应时间 < 2秒，大数据量查询 < 5秒
5. **稳定性**：系统连续运行无故障，可用性 > 99.5%

### 技术验收标准
1. **代码质量**：遵循项目编码规范，通过ESLint检查
2. **单元测试**：核心功能测试覆盖率 > 80%
3. **接口文档**：完整的API文档和使用说明
4. **部署文档**：详细的部署和配置文档
5. **用户手册**：完整的用户操作手册

---

## 📚 交付物清单

### 开发交付物
- [ ] 前端页面及组件代码
- [ ] 后端API接口代码
- [ ] 数据库设计文档及脚本
- [ ] 移动端应用代码

### 文档交付物
- [ ] 需求规格说明书
- [ ] 系统设计文档
- [ ] 数据库设计文档
- [ ] API接口文档
- [ ] 部署运维文档
- [ ] 用户操作手册
- [ ] 测试用例及测试报告

### 配置交付物
- [ ] 开发环境配置
- [ ] 测试环境配置
- [ ] 生产环境配置
- [ ] 数据字典配置
- [ ] 权限配置

---

## ⚠️ 风险评估与应对

### 技术风险
1. **数据一致性风险**：多并发操作可能导致库存数据不一致
   - **应对措施**：使用数据库事务和分布式锁机制

2. **性能风险**：大数据量查询可能影响系统性能
   - **应对措施**：数据库索引优化、查询分页、缓存策略

3. **集成风险**：与ERP等系统集成可能出现兼容性问题
   - **应对措施**：制定详细的接口规范、充分测试

### 业务风险
1. **需求变更风险**：业务需求可能频繁变更
   - **应对措施**：敏捷开发、模块化设计、及时沟通

2. **用户接受度风险**：用户可能不适应新系统
   - **应对措施**：用户培训、分阶段上线、及时反馈

### 进度风险
1. **开发进度延期风险**：开发任务复杂度高可能导致延期
   - **应对措施**：合理工期安排、定期进度检查、资源调配

---

## 📞 项目联系信息

**项目经理**：[姓名]
**技术负责人**：[姓名]
**测试负责人**：[姓名]
**产品负责人**：[姓名]

**项目开始时间**：[日期]
**预计完成时间**：[日期]
**项目状态**：规划阶段

---

*本文档版本：v1.0*
*最后更新时间：2024年12月*
*文档维护人：开发团队* 
