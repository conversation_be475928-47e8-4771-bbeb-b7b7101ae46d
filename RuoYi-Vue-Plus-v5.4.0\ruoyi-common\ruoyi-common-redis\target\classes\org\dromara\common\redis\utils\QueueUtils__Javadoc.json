{"doc": " 分布式队列工具\n 轻量级队列 重量级数据量 请使用 MQ\n 要求 redis 5.X 以上\n\n <AUTHOR> Li\n @version 3.6.0 新增\n", "fields": [], "enumConstants": [], "methods": [{"name": "getClient", "paramTypes": [], "doc": " 获取客户端实例\n"}, {"name": "addQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 添加普通队列数据\n\n @param queueName 队列名\n @param data      数据\n"}, {"name": "getQueueObject", "paramTypes": ["java.lang.String"], "doc": " 通用获取一个队列数据 没有数据返回 null(不支持延迟队列)\n\n @param queueName 队列名\n"}, {"name": "removeQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 通用删除队列数据(不支持延迟队列)\n"}, {"name": "destroyQueue", "paramTypes": ["java.lang.String"], "doc": " 通用销毁队列 所有阻塞监听 报错(不支持延迟队列)\n"}, {"name": "addDelayedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": " 添加延迟队列数据 默认毫秒\n\n @param queueName 队列名\n @param data      数据\n @param time      延迟时间\n"}, {"name": "addDelayedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long", "java.util.concurrent.TimeUnit"], "doc": " 添加延迟队列数据\n\n @param queueName 队列名\n @param data      数据\n @param time      延迟时间\n @param timeUnit  单位\n"}, {"name": "getDelayedQueueObject", "paramTypes": ["java.lang.String"], "doc": " 获取一个延迟队列数据 没有数据返回 null\n\n @param queueName 队列名\n"}, {"name": "removeDelayedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 删除延迟队列数据\n"}, {"name": "destroy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 销毁延迟队列 所有阻塞监听 报错\n"}, {"name": "addPriorityQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 添加优先队列数据\n\n @param queueName 队列名\n @param data      数据\n"}, {"name": "getPriorityQueueObject", "paramTypes": ["java.lang.String"], "doc": " 优先队列获取一个队列数据 没有数据返回 null(不支持延迟队列)\n\n @param queueName 队列名\n"}, {"name": "removePriorityQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 优先队列删除队列数据(不支持延迟队列)\n"}, {"name": "destroyPriorityQueue", "paramTypes": ["java.lang.String"], "doc": " 优先队列销毁队列 所有阻塞监听 报错(不支持延迟队列)\n"}, {"name": "trySetBoundedQueueCapacity", "paramTypes": ["java.lang.String", "int"], "doc": " 尝试设置 有界队列 容量 用于限制数量\n\n @param queueName 队列名\n @param capacity  容量\n"}, {"name": "trySetBoundedQueueCapacity", "paramTypes": ["java.lang.String", "int", "boolean"], "doc": " 尝试设置 有界队列 容量 用于限制数量\n\n @param queueName 队列名\n @param capacity  容量\n @param destroy   是否销毁\n"}, {"name": "addBoundedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 添加有界队列数据\n\n @param queueName 队列名\n @param data      数据\n @return 添加成功 true 已达到界限 false\n"}, {"name": "getBoundedQueueObject", "paramTypes": ["java.lang.String"], "doc": " 有界队列获取一个队列数据 没有数据返回 null(不支持延迟队列)\n\n @param queueName 队列名\n"}, {"name": "removeBoundedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 有界队列删除队列数据(不支持延迟队列)\n"}, {"name": "destroyBoundedQueue", "paramTypes": ["java.lang.String"], "doc": " 有界队列销毁队列 所有阻塞监听 报错(不支持延迟队列)\n"}, {"name": "subscribeBlockingQueue", "paramTypes": ["java.lang.String", "java.util.function.Function", "boolean"], "doc": " 订阅阻塞队列(可订阅所有实现类 例如: 延迟 优先 有界 等)\n"}], "constructors": []}