package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__12;
import org.dromara.system.domain.SysOperLog;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOperLogBoToOperLogEventMapper__12.class,OperLogEventToSysOperLogBoMapper__12.class},
    imports = {}
)
public interface SysOperLogBoToSysOperLogMapper__12 extends BaseMapper<SysOperLogBo, SysOperLog> {
}
