import {
  tooltip_default
} from "./chunk-OA25L6G7.js";
import {
  dynamicApp
} from "./chunk-2K5G4TR6.js";
import {
  VxeUI
} from "./chunk-TETVOAVO.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

export {
  VxeTooltip,
  Tooltip,
  tooltip_default2 as tooltip_default
};
//# sourceMappingURL=chunk-FWEU7AYH.js.map
