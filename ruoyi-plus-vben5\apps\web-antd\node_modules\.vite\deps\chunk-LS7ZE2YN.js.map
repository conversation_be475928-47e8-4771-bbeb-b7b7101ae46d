{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useMergedState.js"], "sourcesContent": ["import { toRaw, watchEffect, unref, watch, ref } from 'vue';\nexport default function useMergedState(defaultStateValue, option) {\n  const {\n    defaultValue,\n    value = ref()\n  } = option || {};\n  let initValue = typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;\n  if (value.value !== undefined) {\n    initValue = unref(value);\n  }\n  if (defaultValue !== undefined) {\n    initValue = typeof defaultValue === 'function' ? defaultValue() : defaultValue;\n  }\n  const innerValue = ref(initValue);\n  const mergedValue = ref(initValue);\n  watchEffect(() => {\n    let val = value.value !== undefined ? value.value : innerValue.value;\n    if (option.postState) {\n      val = option.postState(val);\n    }\n    mergedValue.value = val;\n  });\n  function triggerChange(newValue) {\n    const preVal = mergedValue.value;\n    innerValue.value = newValue;\n    if (toRaw(mergedValue.value) !== newValue && option.onChange) {\n      option.onChange(newValue, preVal);\n    }\n  }\n  // Effect of reset value to `undefined`\n  watch(value, () => {\n    innerValue.value = value.value;\n  });\n  return [mergedValue, triggerChange];\n}"], "mappings": ";;;;;;;;;AACe,SAAR,eAAgC,mBAAmB,QAAQ;AAChE,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ,IAAI;AAAA,EACd,IAAI,UAAU,CAAC;AACf,MAAI,YAAY,OAAO,sBAAsB,aAAa,kBAAkB,IAAI;AAChF,MAAI,MAAM,UAAU,QAAW;AAC7B,gBAAY,MAAM,KAAK;AAAA,EACzB;AACA,MAAI,iBAAiB,QAAW;AAC9B,gBAAY,OAAO,iBAAiB,aAAa,aAAa,IAAI;AAAA,EACpE;AACA,QAAM,aAAa,IAAI,SAAS;AAChC,QAAM,cAAc,IAAI,SAAS;AACjC,cAAY,MAAM;AAChB,QAAI,MAAM,MAAM,UAAU,SAAY,MAAM,QAAQ,WAAW;AAC/D,QAAI,OAAO,WAAW;AACpB,YAAM,OAAO,UAAU,GAAG;AAAA,IAC5B;AACA,gBAAY,QAAQ;AAAA,EACtB,CAAC;AACD,WAAS,cAAc,UAAU;AAC/B,UAAM,SAAS,YAAY;AAC3B,eAAW,QAAQ;AACnB,QAAI,MAAM,YAAY,KAAK,MAAM,YAAY,OAAO,UAAU;AAC5D,aAAO,SAAS,UAAU,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,OAAO,MAAM;AACjB,eAAW,QAAQ,MAAM;AAAA,EAC3B,CAAC;AACD,SAAO,CAAC,aAAa,aAAa;AACpC;", "names": []}