var R=Object.defineProperty;var $=Object.getOwnPropertySymbols;var T=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var S=(s,o,e)=>o in s?R(s,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[o]=e,q=(s,o)=>{for(var e in o||(o={}))T.call(o,e)&&S(s,e,o[e]);if($)for(var e of $(o))N.call(o,e)&&S(s,e,o[e]);return s};var C=(s,o,e)=>new Promise((x,r)=>{var w=l=>{try{f(e.next(l))}catch(g){r(g)}},p=l=>{try{f(e.throw(l))}catch(g){r(g)}},f=l=>l.done?x(l.value):Promise.resolve(l.value).then(w,p);f((e=e.apply(s,o)).next())});import{at as z,as as A,an as U}from"./bootstrap-DCMzVRvD.js";import{v as j}from"./vxe-table-DzEj5Fop.js";import{q as F,c as G,a as L,b as W,d as H,e as V,f as I}from"./client-drawer-CQa9fZq8.js";import{_ as J}from"./table-switch.vue_vue_type_script_setup_true_lang-BPKnQ2Wy.js";import{c as K}from"./download-UJak946_.js";import P from"./index-BeyziwLP.js";import{_ as Q}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as X,l as B,S as Y,h as d,o as c,w as a,a as u,b as i,T as b,k as v,t as _}from"../jse/index-index-C-MnMZEz.js";import{u as Z}from"./use-vxe-grid-BC7vZzEr.js";import{u as ee}from"./use-drawer-6qcpK-D1.js";import{P as te}from"./index-DNdMANjv.js";import{g as oe}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./popup-D6rC6QBG.js";import"./dict-BLkXAGS5.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./secret-input-DRN8XoW8.js";import"./index-BxBCzu2M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./uuid-B0AYzFfo.js";import"./index-D-hwdOI6.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Ge=X({__name:"index",setup(s){const o={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:F(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={checkboxConfig:{highlight:!0,reserve:!0,checkMethod:({row:t})=>(t==null?void 0:t.id)!==1},columns:G,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(h,...D)=>C(null,[h,...D],function*({page:t},n={}){return yield H(q({pageNum:t.currentPage,pageSize:t.pageSize},n))})}},rowConfig:{keyField:"id"},id:"system-client-index",showOverflow:!1},[x,r]=Z({formOptions:o,gridOptions:e}),[w,p]=ee({connectedComponent:L});function f(){p.setData({}),p.open()}function l(t){return C(this,null,function*(){p.setData({id:t.id}),p.open()})}function g(t){return C(this,null,function*(){yield V([t.id]),yield r.query()})}function E(){const n=r.grid.getCheckboxRecords().map(h=>h.id);U.confirm({title:"提示",okType:"danger",content:`确认删除选中的${n.length}条记录吗？`,onOk:()=>C(null,null,function*(){yield V(n),yield r.query()})})}function M(){K(I,"客户端数据",r.formApi.form.values)}const{hasAccessByCodes:O}=z();return(t,n)=>{const h=B("a-button"),D=B("ghost-button"),y=Y("access");return c(),d(i(Q),{"auto-content-height":!0},{default:a(()=>[u(i(x),{"table-title":"客户端列表"},{"toolbar-tools":a(()=>[u(i(P),null,{default:a(()=>[b((c(),d(h,{onClick:M},{default:a(()=>[v(_(t.$t("pages.common.export")),1)]),_:1})),[[y,["system:client:export"],"code"]]),b((c(),d(h,{disabled:!i(j)(i(r)),danger:"",type:"primary",onClick:E},{default:a(()=>[v(_(t.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[y,["system:client:remove"],"code"]]),b((c(),d(h,{type:"primary",onClick:f},{default:a(()=>[v(_(t.$t("pages.common.add")),1)]),_:1})),[[y,["system:client:add"],"code"]])]),_:1})]),status:a(({row:m})=>[u(i(J),{value:m.status,"onUpdate:value":k=>m.status=k,api:()=>i(W)(m),disabled:m.id===1||!i(O)(["system:client:edit"]),onReload:n[0]||(n[0]=k=>i(r).query())},null,8,["value","onUpdate:value","api","disabled"])]),action:a(({row:m})=>[u(i(P),null,{default:a(()=>[b((c(),d(D,{onClick:A(k=>l(m),["stop"])},{default:a(()=>[v(_(t.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[y,["system:client:edit"],"code"]]),u(i(te),{disabled:m.id===1,"get-popup-container":i(oe),placement:"left",title:"确认删除？",onConfirm:k=>g(m)},{default:a(()=>[b((c(),d(D,{disabled:m.id===1,danger:"",onClick:n[1]||(n[1]=A(()=>{},["stop"]))},{default:a(()=>[v(_(t.$t("pages.common.delete")),1)]),_:2},1032,["disabled"])),[[y,["system:client:remove"],"code"]])]),_:2},1032,["disabled","get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),u(i(w),{onReload:n[2]||(n[2]=m=>i(r).query())})]),_:1})}}});export{Ge as default};
