import{_ as r}from"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import{d as l,p as c,h as i,o as u,w as d,j as f,a as _,b as t}from"../jse/index-index-C-MnMZEz.js";import{u as p}from"./use-modal-CeMSCP2m.js";const m={class:"min-h-[400px] overflow-y-auto"},b=l({__name:"instance-variable-modal",setup(h){const a=c({}),[s,n]=p({title:"流程变量",fullscreenButton:!1,footer:!1,onOpenChange:e=>{if(!e)return a.value={},null;const o=n.getData().record;a.value=JSON.parse(o)}});return(e,o)=>(u(),i(t(s),null,{default:d(()=>[f("div",m,[_(t(r),{data:a.value},null,8,["data"])])]),_:1}))}});export{b as _};
