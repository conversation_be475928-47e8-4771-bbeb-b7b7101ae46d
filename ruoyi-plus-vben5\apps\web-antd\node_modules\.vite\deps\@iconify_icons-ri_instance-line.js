import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-ri@1.2.10/node_modules/@iconify/icons-ri/instance-line.js
var require_instance_line = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-ri@1.2.10/node_modules/@iconify/icons-ri/instance-line.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="currentColor" d="M4.5 7.653v8.694l7.5 4.342l7.5-4.342V7.653L12 3.311L4.5 7.653ZM12 1l9.5 5.5v11L12 23l-9.5-5.5v-11L12 1ZM6.499 9.97L11 12.577v5.049h2v-5.049l4.501-2.605l-1.002-1.731L12 10.844L7.501 8.24L6.499 9.97Z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_instance_line();
//# sourceMappingURL=@iconify_icons-ri_instance-line.js.map
