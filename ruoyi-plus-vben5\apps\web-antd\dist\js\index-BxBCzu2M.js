import{t as me,b as Ae,r as X,f as Pe,i as Re,I as x,a as Ee}from"./Search-ClCped_G.js";import{p as ae,bQ as Y,g as _,c as V,v as $e,b8 as j,h as Te,t as Oe,aY as te,aT as _e,cN as q,b4 as Fe,aJ as le,_ as C,aG as Be,aH as je,aC as Me,aX as Ve,cO as De}from"./bootstrap-DCMzVRvD.js";import{u as ge}from"./index-CFj2VWFk.js";import{d as k,B,a as w,p as M,ac as ue,D as oe,q as ne,z as pe,C as F,I as ce}from"../jse/index-index-C-MnMZEz.js";import{a as he,g as be}from"./statusUtils-d85DZFMd.js";import{R as Le}from"./index-CHpIOV4R.js";import{B as ke}from"./BaseInput-B4f3ADM3.js";import"./SearchOutlined-BOD_ZIye.js";import"./css-Dmgy8YJo.js";const Ge=k({compatConfig:{MODE:3},name:"AInputGroup",inheritAttrs:!1,props:{prefixCls:String,size:{type:String},compact:{type:Boolean,default:void 0}},setup(e,s){let{slots:n,attrs:a}=s;const{prefixCls:o,direction:h,getPrefixCls:m}=ae("input-group",e),i=Y.useInject();Y.useProvide(i,{isFormItemInput:!1});const d=B(()=>m("input")),[r,g]=ge(d),c=B(()=>{const l=o.value;return{[`${l}`]:!0,[g.value]:!0,[`${l}-lg`]:e.size==="large",[`${l}-sm`]:e.size==="small",[`${l}-compact`]:e.compact,[`${l}-rtl`]:h.value==="rtl"}});return()=>{var l;return r(w("span",_(_({},a),{},{class:V(c.value,a.class)}),[(l=n.default)===null||l===void 0?void 0:l.call(n)]))}}}),de=e=>e!=null&&(Array.isArray(e)?$e(e).length:!0);function He(e){return de(e.addonBefore)||de(e.addonAfter)}const Ne=["text","input"],qe=k({compatConfig:{MODE:3},name:"ClearableLabeledInput",inheritAttrs:!1,props:{prefixCls:String,inputType:Te.oneOf(Oe("text","input")),value:j(),defaultValue:j(),allowClear:{type:Boolean,default:void 0},element:j(),handleReset:Function,disabled:{type:Boolean,default:void 0},direction:{type:String},size:{type:String},suffix:j(),prefix:j(),addonBefore:j(),addonAfter:j(),readonly:{type:Boolean,default:void 0},focused:{type:Boolean,default:void 0},bordered:{type:Boolean,default:!0},triggerFocus:{type:Function},hidden:Boolean,status:String,hashId:String},setup(e,s){let{slots:n,attrs:a}=s;const o=Y.useInject(),h=i=>{const{value:d,disabled:r,readonly:g,handleReset:c,suffix:l=n.suffix}=e,b=!r&&!g&&d,S=`${i}-clear-icon`;return w(_e,{onClick:c,onMousedown:E=>E.preventDefault(),class:V({[`${S}-hidden`]:!b,[`${S}-has-suffix`]:!!l},S),role:"button"},null)},m=(i,d)=>{const{value:r,allowClear:g,direction:c,bordered:l,hidden:b,status:S,addonAfter:E=n.addonAfter,addonBefore:v=n.addonBefore,hashId:R}=e,{status:y,hasFeedback:I}=o;if(!g)return te(d,{value:r,disabled:e.disabled});const z=V(`${i}-affix-wrapper`,`${i}-affix-wrapper-textarea-with-clear-btn`,he(`${i}-affix-wrapper`,be(y,S),I),{[`${i}-affix-wrapper-rtl`]:c==="rtl",[`${i}-affix-wrapper-borderless`]:!l,[`${a.class}`]:!He({addonAfter:E,addonBefore:v})&&a.class},R);return w("span",{class:z,style:a.style,hidden:b},[te(d,{style:null,value:r,disabled:e.disabled}),h(i)])};return()=>{var i;const{prefixCls:d,inputType:r,element:g=(i=n.element)===null||i===void 0?void 0:i.call(n)}=e;return r===Ne[0]?m(d,g):null}}}),Ye=`
  min-height:0 !important;
  max-height:none !important;
  height:0 !important;
  visibility:hidden !important;
  overflow:hidden !important;
  position:absolute !important;
  z-index:-1000 !important;
  top:0 !important;
  right:0 !important;
  pointer-events: none !important;
`,Qe=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],W={};let P;function Ze(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(s&&W[n])return W[n];const a=window.getComputedStyle(e),o=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),h=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),m=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),d={sizingStyle:Qe.map(r=>`${r}:${a.getPropertyValue(r)}`).join(";"),paddingSize:h,borderSize:m,boxSizing:o};return s&&n&&(W[n]=d),d}function Ue(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;P||(P=document.createElement("textarea"),P.setAttribute("tab-index","-1"),P.setAttribute("aria-hidden","true"),document.body.appendChild(P)),e.getAttribute("wrap")?P.setAttribute("wrap",e.getAttribute("wrap")):P.removeAttribute("wrap");const{paddingSize:o,borderSize:h,boxSizing:m,sizingStyle:i}=Ze(e,s);P.setAttribute("style",`${i};${Ye}`),P.value=e.value||e.placeholder||"";let d,r,g,c=P.scrollHeight;if(m==="border-box"?c+=h:m==="content-box"&&(c-=o),n!==null||a!==null){P.value=" ";const b=P.scrollHeight-o;n!==null&&(d=b*n,m==="border-box"&&(d=d+o+h),c=Math.max(d,c)),a!==null&&(r=b*a,m==="border-box"&&(r=r+o+h),g=c>r?"":"hidden",c=Math.min(r,c))}const l={height:`${c}px`,overflowY:g,resize:"none"};return d&&(l.minHeight=`${d}px`),r&&(l.maxHeight=`${r}px`),l}const J=0,K=1,ee=2,Xe=k({compatConfig:{MODE:3},name:"ResizableTextArea",inheritAttrs:!1,props:me(),setup(e,s){let{attrs:n,emit:a,expose:o}=s,h,m;const i=M(),d=M({}),r=M(ee);ue(()=>{q.cancel(h),q.cancel(m)});const g=()=>{try{if(i.value&&document.activeElement===i.value.input){const f=i.value.getSelectionStart(),D=i.value.getSelectionEnd(),L=i.value.getScrollTop();i.value.setSelectionRange(f,D),i.value.setScrollTop(L)}}catch(f){}},c=M(),l=M();oe(()=>{const f=e.autoSize||e.autosize;f?(c.value=f.minRows,l.value=f.maxRows):(c.value=void 0,l.value=void 0)});const b=B(()=>!!(e.autoSize||e.autosize)),S=()=>{r.value=J};ne([()=>e.value,c,l,b],()=>{b.value&&S()},{immediate:!0});const E=M();ne([r,i],()=>{if(i.value)if(r.value===J)r.value=K;else if(r.value===K){const f=Ue(i.value.input,!1,c.value,l.value);r.value=ee,E.value=f}else g()},{immediate:!0,flush:"post"});const v=pe(),R=M(),y=()=>{q.cancel(R.value)},I=f=>{r.value===ee&&(a("resize",f),b.value&&(y(),R.value=q(()=>{S()})))};ue(()=>{y()}),o({resizeTextarea:()=>{S()},textArea:B(()=>{var f;return(f=i.value)===null||f===void 0?void 0:f.input}),instance:v}),Fe(e.autosize===void 0);const $=()=>{const{prefixCls:f,disabled:D}=e,L=le(e,["prefixCls","onPressEnter","autoSize","autosize","defaultValue","allowClear","type","maxlength","valueModifiers"]),Q=V(f,n.class,{[`${f}-disabled`]:D}),G=b.value?E.value:null,H=[n.style,d.value,G],O=C(C(C({},L),n),{style:H,class:Q});return(r.value===J||r.value===K)&&H.push({overflowX:"hidden",overflowY:"hidden"}),O.autofocus||delete O.autofocus,O.rows===0&&delete O.rows,w(Le,{onResize:I,disabled:!b.value},{default:()=>[w(ke,_(_({},O),{},{ref:i,tag:"textarea"}),null)]})};return()=>$()}});function xe(e,s){return[...e||""].slice(0,s).join("")}function fe(e,s,n,a){let o=n;return e?o=xe(n,a):[...s||""].length<n.length&&[...n||""].length>a&&(o=s),o}const We=k({compatConfig:{MODE:3},name:"ATextarea",inheritAttrs:!1,props:me(),setup(e,s){let{attrs:n,expose:a,emit:o}=s;var h;const m=Be(),i=Y.useInject(),d=B(()=>be(i.status,e.status)),r=F((h=e.value)!==null&&h!==void 0?h:e.defaultValue),g=F(),c=F(""),{prefixCls:l,size:b,direction:S}=ae("input",e),[E,v]=ge(l),R=je(),y=B(()=>e.showCount===""||e.showCount||!1),I=B(()=>Number(e.maxlength)>0),z=F(!1),$=F(),f=F(0),D=t=>{z.value=!0,$.value=c.value,f.value=t.currentTarget.selectionStart,o("compositionstart",t)},L=t=>{var u;z.value=!1;let p=t.currentTarget.value;if(I.value){const T=f.value>=e.maxlength+1||f.value===((u=$.value)===null||u===void 0?void 0:u.length);p=fe(T,$.value,p,e.maxlength)}p!==c.value&&(O(p),X(t.currentTarget,t,Z,p)),o("compositionend",t)},Q=pe();ne(()=>e.value,()=>{var t;"value"in Q.vnode.props,r.value=(t=e.value)!==null&&t!==void 0?t:""});const G=t=>{var u;Ae((u=g.value)===null||u===void 0?void 0:u.textArea,t)},H=()=>{var t,u;(u=(t=g.value)===null||t===void 0?void 0:t.textArea)===null||u===void 0||u.blur()},O=(t,u)=>{r.value!==t&&(e.value===void 0?r.value=t:ce(()=>{var p,T,A;g.value.textArea.value!==c.value&&((A=(p=g.value)===null||p===void 0?void 0:(T=p.instance).update)===null||A===void 0||A.call(T))}),ce(()=>{u&&u()}))},ye=t=>{t.keyCode===13&&o("pressEnter",t),o("keydown",t)},Se=t=>{const{onBlur:u}=e;u==null||u(t),m.onFieldBlur()},Z=t=>{o("update:value",t.target.value),o("change",t),o("input",t),m.onFieldChange()},Ce=t=>{X(g.value.textArea,t,Z),O("",()=>{G()})},re=t=>{let u=t.target.value;if(r.value!==u){if(I.value){const p=t.target,T=p.selectionStart>=e.maxlength+1||p.selectionStart===u.length||!p.selectionStart;u=fe(T,c.value,u,e.maxlength)}X(t.currentTarget,t,Z,u),O(u)}},we=()=>{var t,u;const{class:p}=n,{bordered:T=!0}=e,A=C(C(C({},le(e,["allowClear"])),n),{class:[{[`${l.value}-borderless`]:!T,[`${p}`]:p&&!y.value,[`${l.value}-sm`]:b.value==="small",[`${l.value}-lg`]:b.value==="large"},he(l.value,d.value),v.value],disabled:R.value,showCount:null,prefixCls:l.value,onInput:re,onChange:re,onBlur:Se,onKeydown:ye,onCompositionstart:D,onCompositionend:L});return!((t=e.valueModifiers)===null||t===void 0)&&t.lazy&&delete A.onInput,w(Xe,_(_({},A),{},{id:(u=A==null?void 0:A.id)!==null&&u!==void 0?u:m.id.value,ref:g,maxlength:e.maxlength,lazy:e.lazy}),null)};return a({focus:G,blur:H,resizableTextArea:g}),oe(()=>{let t=Pe(r.value);!z.value&&I.value&&(e.value===null||e.value===void 0)&&(t=xe(t,e.maxlength)),c.value=t}),()=>{var t;const{maxlength:u,bordered:p=!0,hidden:T}=e,{style:A,class:Ie}=n,ze=C(C(C({},e),n),{prefixCls:l.value,inputType:"text",handleReset:Ce,direction:S.value,bordered:p,style:y.value?void 0:A,hashId:v.value,disabled:(t=e.disabled)!==null&&t!==void 0?t:R.value});let U=w(qe,_(_({},ze),{},{value:c.value,status:e.status}),{element:we});if(y.value||i.hasFeedback){const se=[...c.value].length;let N="";typeof y.value=="object"?N=y.value.formatter({value:c.value,count:se,maxlength:u}):N=`${se}${I.value?` / ${u}`:""}`,U=w("div",{hidden:T,class:V(`${l.value}-textarea`,{[`${l.value}-textarea-rtl`]:S.value==="rtl",[`${l.value}-textarea-show-count`]:y.value,[`${l.value}-textarea-in-form-item`]:i.isFormItemInput},`${l.value}-textarea-show-count`,Ie,v.value),style:A,"data-count":typeof N!="object"?N:void 0},[U,i.hasFeedback&&w("span",{class:`${l.value}-textarea-suffix`},[i.feedbackIcon])])}return E(U)}}});var Je={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"};function ve(e){for(var s=1;s<arguments.length;s++){var n=arguments[s]!=null?Object(arguments[s]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.forEach(function(o){Ke(e,o,n[o])})}return e}function Ke(e,s,n){return s in e?Object.defineProperty(e,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[s]=n,e}var ie=function(s,n){var a=ve({},s,n.attrs);return w(Me,ve({},a,{icon:Je}),null)};ie.displayName="EyeInvisibleOutlined";ie.inheritAttrs=!1;var et=function(e,s){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&s.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)s.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};const tt={click:"onClick",hover:"onMouseover"},nt=e=>e?w(De,null,null):w(ie,null,null),at=k({compatConfig:{MODE:3},name:"AInputPassword",inheritAttrs:!1,props:C(C({},Re()),{prefixCls:String,inputPrefixCls:String,action:{type:String,default:"click"},visibilityToggle:{type:Boolean,default:!0},visible:{type:Boolean,default:void 0},"onUpdate:visible":Function,iconRender:Function}),setup(e,s){let{slots:n,attrs:a,expose:o,emit:h}=s;const m=F(!1),i=()=>{const{disabled:v}=e;v||(m.value=!m.value,h("update:visible",m.value))};oe(()=>{e.visible!==void 0&&(m.value=!!e.visible)});const d=F();o({focus:()=>{var v;(v=d.value)===null||v===void 0||v.focus()},blur:()=>{var v;(v=d.value)===null||v===void 0||v.blur()}});const c=v=>{const{action:R,iconRender:y=n.iconRender||nt}=e,I=tt[R]||"",z=y(m.value),$={[I]:i,class:`${v}-icon`,key:"passwordIcon",onMousedown:f=>{f.preventDefault()},onMouseup:f=>{f.preventDefault()}};return te(Ve(z)?z:w("span",null,[z]),$)},{prefixCls:l,getPrefixCls:b}=ae("input-password",e),S=B(()=>b("input",e.inputPrefixCls)),E=()=>{const{size:v,visibilityToggle:R}=e,y=et(e,["size","visibilityToggle"]),I=R&&c(l.value),z=V(l.value,a.class,{[`${l.value}-${v}`]:!!v}),$=C(C(C({},le(y,["suffix","iconRender","action"])),a),{type:m.value?"text":"password",class:z,prefixCls:S.value,suffix:I});return v&&($.size=v),w(x,_({ref:d},$),n)};return()=>E()}});x.Group=Ge;x.Search=Ee;x.TextArea=We;x.Password=at;x.install=function(e){return e.component(x.name,x),e.component(x.Group.name,x.Group),e.component(x.Search.name,x.Search),e.component(x.TextArea.name,x.TextArea),e.component(x.Password.name,x.Password),e};export{Ge as InputGroup,at as InputPassword,Ee as InputSearch,We as Textarea,x as default};
