{"doc": " 通用常量信息\n\n <AUTHOR>\n", "fields": [{"name": "UTF8", "doc": " UTF-8 字符集\n"}, {"name": "GBK", "doc": " GBK 字符集\n"}, {"name": "WWW", "doc": " www主域\n"}, {"name": "HTTP", "doc": " http请求\n"}, {"name": "HTTPS", "doc": " https请求\n"}, {"name": "SUCCESS", "doc": " 通用成功标识\n"}, {"name": "FAIL", "doc": " 通用失败标识\n"}, {"name": "LOGIN_SUCCESS", "doc": " 登录成功\n"}, {"name": "LOGOUT", "doc": " 注销\n"}, {"name": "REGISTER", "doc": " 注册\n"}, {"name": "LOGIN_FAIL", "doc": " 登录失败\n"}, {"name": "CAPTCHA_EXPIRATION", "doc": " 验证码有效期（分钟）\n"}, {"name": "TOP_PARENT_ID", "doc": " 顶级部门id\n"}], "enumConstants": [], "methods": [], "constructors": []}