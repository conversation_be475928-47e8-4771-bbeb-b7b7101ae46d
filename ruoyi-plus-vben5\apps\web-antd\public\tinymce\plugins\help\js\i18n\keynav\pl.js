tinymce.Resource.add('tinymce.html-i18n.help-keynav.pl',
'<h1>Początek nawigacji przy użyciu klawiatury</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Ustaw fokus na pasek menu</dt>\n' +
  '  <dd>Windows lub Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Ustaw fokus na pasek narzędzi</dt>\n' +
  '  <dd>Windows lub Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Ustaw fokus na sekcję Footer</dt>\n' +
  '  <dd>Windows lub Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Skup się na powiadomieniu</dt>\n' +
  '  <dd>Windows lub Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Ustaw fokus na kontekstowy pasek narzędzi</dt>\n' +
  '  <dd>Windows, Linux lub macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Nawigacja zostanie rozpoczęta od pierwszego elementu interfejsu użytkownika, który jest podświetlony lub — w przypadku pierwszego elementu\n' +
  '  w ścieżce elementów w sekcji Footer — podkreślony.</p>\n' +
  '\n' +
  '<h1>Nawigacja pomiędzy sekcjami interfejsu użytkownika</h1>\n' +
  '\n' +
  '<p>Aby przenieść się z danej sekcji interfejsu użytkownika do następnej, naciśnij <strong>Tab</strong>.</p>\n' +
  '\n' +
  '<p>Aby przenieść się z danej sekcji interfejsu użytkownika do poprzedniej, naciśnij <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Kolejność klawisza <strong>Tab</strong> w takich sekcjach interfejsu użytkownika jest następująca:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Pasek menu</li>\n' +
  '  <li>Każda grupa na pasku narzędzi</li>\n' +
  '  <li>Pasek boczny</li>\n' +
  '  <li>Ścieżka elementów w sekcji Footer</li>\n' +
  '  <li>Przycisk przełączania liczby słów w sekcji Footer</li>\n' +
  '  <li>Łącze brandujące w sekcji Footer</li>\n' +
  '  <li>Uchwyt zmiany rozmiaru edytora w sekcji Footer</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Jeżeli nie ma sekcji interfejsu użytkownika, jest to pomijane.</p>\n' +
  '\n' +
  '<p>Jeżeli na sekcji Footer jest ustawiony fokus nawigacji przy użyciu klawiatury i nie ma widocznego paska bocznego, naciśnięcie <strong>Shift+Tab</strong>\n' +
  '  przenosi fokus na pierwszą grupę paska narzędzi, a nie na ostatnią.</p>\n' +
  '\n' +
  '<h1>Nawigacja wewnątrz sekcji interfejsu użytkownika</h1>\n' +
  '\n' +
  '<p>Aby przenieść się z danego elementu interfejsu użytkownika do następnego, naciśnij odpowiedni klawisz <strong>strzałki</strong>.</p>\n' +
  '\n' +
  '<p>Klawisze strzałek <strong>w prawo</strong> i <strong>w lewo</strong> służą do</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>przenoszenia się pomiędzy menu na pasku menu,</li>\n' +
  '  <li>otwarcia podmenu w menu,</li>\n' +
  '  <li>przenoszenia się pomiędzy przyciskami w grupie paska narzędzi,</li>\n' +
  '  <li>przenoszenia się pomiędzy elementami w ścieżce elementów w sekcji Footer.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Klawisze strzałek <strong>w dół</strong> i <strong>w górę</strong> służą do</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>przenoszenia się pomiędzy elementami menu w menu,</li>\n' +
  '  <li>przenoszenia się pomiędzy elementami w wyskakującym menu paska narzędzi.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Klawisze <strong>strzałek</strong> służą do przemieszczania się w sekcji interfejsu użytkownika z ustawionym fokusem.</p>\n' +
  '\n' +
  '<p>Aby zamknąć otwarte menu, otwarte podmenu lub otwarte menu wyskakujące, naciśnij klawisz <strong>Esc</strong>.</p>\n' +
  '\n' +
  '<p>Jeżeli fokus jest ustawiony na górze konkretnej sekcji interfejsu użytkownika, naciśnięcie klawisza <strong>Esc</strong> powoduje wyjście\n' +
  '  z nawigacji przy użyciu klawiatury.</p>\n' +
  '\n' +
  '<h1>Wykonanie elementu menu lub przycisku paska narzędzi</h1>\n' +
  '\n' +
  '<p>Gdy podświetlony jest żądany element menu lub przycisk paska narzędzi, naciśnij klawisz <strong>Return</strong>, <strong>Enter</strong>\n' +
  '  lub <strong>Spacja</strong>, aby go wykonać.</p>\n' +
  '\n' +
  '<h1>Nawigacja po oknie dialogowym bez kart</h1>\n' +
  '\n' +
  '<p>Gdy otwiera się okno dialogowe bez kart, fokus ustawiany jest na pierwszą interaktywną część okna.</p>\n' +
  '\n' +
  '<p>Pomiędzy interaktywnymi częściami okna dialogowego nawiguj, naciskając klawisze <strong>Tab</strong> lub <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>Nawigacja po oknie dialogowym z kartami</h1>\n' +
  '\n' +
  '<p>W przypadku okna dialogowego z kartami po otwarciu okna dialogowego fokus ustawiany jest na pierwszy przycisk w menu karty.</p>\n' +
  '\n' +
  '<p>Nawigację pomiędzy interaktywnymi częściami karty okna dialogowego prowadzi się poprzez naciskanie klawiszy <strong>Tab</strong> lub\n' +
  '  <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Przełączenie się na inną kartę okna dialogowego wykonuje się poprzez ustawienie fokusu na menu karty i naciśnięcie odpowiedniego klawisza <strong>strzałki</strong>\n' +
  '  w celu przemieszczenia się pomiędzy dostępnymi kartami.</p>\n');