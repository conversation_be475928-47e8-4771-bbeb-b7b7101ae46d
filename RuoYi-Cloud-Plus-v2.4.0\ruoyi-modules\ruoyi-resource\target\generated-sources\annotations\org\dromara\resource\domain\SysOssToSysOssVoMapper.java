package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1195;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssBoToSysOssMapper;
import org.dromara.resource.domain.vo.SysOssVo;
import org.dromara.resource.domain.vo.SysOssVoToSysOssMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1195.class,
    uses = {SysOssBoToSysOssMapper.class,SysOssVoToSysOssMapper.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper extends BaseMapper<SysOss, SysOssVo> {
}
