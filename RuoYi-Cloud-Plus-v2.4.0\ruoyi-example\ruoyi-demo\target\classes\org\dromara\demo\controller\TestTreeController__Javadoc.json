{"doc": " 测试树表Controller\n\n <AUTHOR>\n @date 2021-07-26\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo"], "doc": " 查询测试树表列表\n"}, {"name": "export", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出测试树表列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取测试树表详细信息\n\n @param id 测试树ID\n"}, {"name": "add", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo"], "doc": " 新增测试树表\n"}, {"name": "edit", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo"], "doc": " 修改测试树表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除测试树表\n\n @param ids 测试树ID串\n"}], "constructors": []}