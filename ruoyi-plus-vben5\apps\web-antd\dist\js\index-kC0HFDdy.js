import{j as Y,m as Z,_ as T,r as q,aD as J,h as v,p as K,aO as ee,aP as oe,aQ as ne,aR as te,aS as le,aT as ie,aU as ae,aV as se,c as re,aW as ce,aX as de,aY as ue,aZ as ge,a_ as pe,t as me,ax as fe,g as V}from"./bootstrap-DCMzVRvD.js";import{d as ve,C as w,B as $e,a as s,T as he}from"../jse/index-index-C-MnMZEz.js";const B=(e,o,n,i,a)=>({backgroundColor:e,border:`${i.lineWidth}px ${i.lineType} ${o}`,[`${a}-icon`]:{color:n}}),ye=e=>{const{componentCls:o,motionDurationSlow:n,marginXS:i,marginSM:a,fontSize:u,fontSizeLG:r,lineHeight:g,borderRadiusLG:$,motionEaseInOutCirc:c,alertIconSizeLG:d,colorText:m,paddingContentVerticalSM:f,alertPaddingHorizontal:h,paddingMD:C,paddingContentHorizontalLG:S}=e;return{[o]:T(T({},q(e)),{position:"relative",display:"flex",alignItems:"center",padding:`${f}px ${h}px`,wordWrap:"break-word",borderRadius:$,[`&${o}-rtl`]:{direction:"rtl"},[`${o}-content`]:{flex:1,minWidth:0},[`${o}-icon`]:{marginInlineEnd:i,lineHeight:0},"&-description":{display:"none",fontSize:u,lineHeight:g},"&-message":{color:m},[`&${o}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${c}, opacity ${n} ${c},
        padding-top ${n} ${c}, padding-bottom ${n} ${c},
        margin-bottom ${n} ${c}`},[`&${o}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${o}-with-description`]:{alignItems:"flex-start",paddingInline:S,paddingBlock:C,[`${o}-icon`]:{marginInlineEnd:a,fontSize:d,lineHeight:0},[`${o}-message`]:{display:"block",marginBottom:i,color:m,fontSize:r},[`${o}-description`]:{display:"block"}},[`${o}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},Ce=e=>{const{componentCls:o,colorSuccess:n,colorSuccessBorder:i,colorSuccessBg:a,colorWarning:u,colorWarningBorder:r,colorWarningBg:g,colorError:$,colorErrorBorder:c,colorErrorBg:d,colorInfo:m,colorInfoBorder:f,colorInfoBg:h}=e;return{[o]:{"&-success":B(a,i,n,e,o),"&-info":B(h,f,m,e,o),"&-warning":B(g,r,u,e,o),"&-error":T(T({},B(d,c,$,e,o)),{[`${o}-description > pre`]:{margin:0,padding:0}})}}},Se=e=>{const{componentCls:o,iconCls:n,motionDurationMid:i,marginXS:a,fontSizeIcon:u,colorIcon:r,colorIconHover:g}=e;return{[o]:{"&-action":{marginInlineStart:a},[`${o}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:u,lineHeight:`${u}px`,backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:r,transition:`color ${i}`,"&:hover":{color:g}}},"&-close-text":{color:r,transition:`color ${i}`,"&:hover":{color:g}}}}},xe=e=>[ye(e),Ce(e),Se(e)],Ie=Y("Alert",e=>{const{fontSizeHeading3:o}=e,n=Z(e,{alertIconSizeLG:o,alertPaddingHorizontal:12});return[xe(n)]}),be={success:se,info:ae,error:ie,warning:le},we={success:te,info:ne,error:oe,warning:ee},Be=me("success","info","warning","error"),Te=()=>({type:v.oneOf(Be),closable:{type:Boolean,default:void 0},closeText:v.any,message:v.any,description:v.any,afterClose:Function,showIcon:{type:Boolean,default:void 0},prefixCls:String,banner:{type:Boolean,default:void 0},icon:v.any,closeIcon:v.any,onClose:Function}),He=ve({compatConfig:{MODE:3},name:"AAlert",inheritAttrs:!1,props:Te(),setup(e,o){let{slots:n,emit:i,attrs:a,expose:u}=o;const{prefixCls:r,direction:g}=K("alert",e),[$,c]=Ie(r),d=w(!1),m=w(!1),f=w(),h=l=>{l.preventDefault();const p=f.value;p.style.height=`${p.offsetHeight}px`,p.style.height=`${p.offsetHeight}px`,d.value=!0,i("close",l)},C=()=>{var l;d.value=!1,m.value=!0,(l=e.afterClose)===null||l===void 0||l.call(e)},S=$e(()=>{const{type:l}=e;return l!==void 0?l:e.banner?"warning":"info"});u({animationEnd:C});const j=w({});return()=>{var l,p,H,_,z,A,E,O,L,D;const{banner:P,closeIcon:W=(l=n.closeIcon)===null||l===void 0?void 0:l.call(n)}=e;let{closable:F,showIcon:y}=e;const M=(p=e.closeText)!==null&&p!==void 0?p:(H=n.closeText)===null||H===void 0?void 0:H.call(n),x=(_=e.description)!==null&&_!==void 0?_:(z=n.description)===null||z===void 0?void 0:z.call(n),R=(A=e.message)!==null&&A!==void 0?A:(E=n.message)===null||E===void 0?void 0:E.call(n),I=(O=e.icon)!==null&&O!==void 0?O:(L=n.icon)===null||L===void 0?void 0:L.call(n),G=(D=n.action)===null||D===void 0?void 0:D.call(n);y=P&&y===void 0?!0:y;const N=(x?we:be)[S.value]||null;M&&(F=!0);const t=r.value,k=re(t,{[`${t}-${S.value}`]:!0,[`${t}-closing`]:d.value,[`${t}-with-description`]:!!x,[`${t}-no-icon`]:!y,[`${t}-banner`]:!!P,[`${t}-closable`]:F,[`${t}-rtl`]:g.value==="rtl",[c.value]:!0}),X=F?s("button",{type:"button",onClick:h,class:`${t}-close-icon`,tabindex:0},[M?s("span",{class:`${t}-close-text`},[M]):W===void 0?s(ce,null,null):W]):null,Q=I&&(de(I)?ue(I,{class:`${t}-icon`}):s("span",{class:`${t}-icon`},[I]))||s(N,{class:`${t}-icon`},null),U=ge(`${t}-motion`,{appear:!1,css:!0,onAfterLeave:C,onBeforeLeave:b=>{b.style.maxHeight=`${b.offsetHeight}px`},onLeave:b=>{b.style.maxHeight="0px"}});return $(m.value?null:s(pe,U,{default:()=>[he(s("div",V(V({role:"alert"},a),{},{style:[a.style,j.value],class:[a.class,k],"data-show":!d.value,ref:f}),[y?Q:null,s("div",{class:`${t}-content`},[R?s("div",{class:`${t}-message`},[R]):null,x?s("div",{class:`${t}-description`},[x]):null]),G?s("div",{class:`${t}-action`},[G]):null,X]),[[fe,!d.value]])]}))}}}),Ae=J(He);export{Ae as A};
