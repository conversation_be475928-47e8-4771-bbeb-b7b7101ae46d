{"doc": " 字典 业务层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictTypeList", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 根据条件分页查询字典类型\n\n @param dictType 字典类型信息\n @return 字典类型集合信息\n"}, {"name": "selectDictTypeAll", "paramTypes": [], "doc": " 根据所有字典类型\n\n @return 字典类型集合信息\n"}, {"name": "selectDictDataByType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询字典数据\n\n @param dictType 字典类型\n @return 字典数据集合信息\n"}, {"name": "selectDictTypeById", "paramTypes": ["java.lang.Long"], "doc": " 根据字典类型ID查询信息\n\n @param dictId 字典类型ID\n @return 字典类型\n"}, {"name": "selectDictTypeByType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询信息\n\n @param dictType 字典类型\n @return 字典类型\n"}, {"name": "deleteDictTypeByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除字典信息\n\n @param dictIds 需要删除的字典ID\n"}, {"name": "resetDictCache", "paramTypes": [], "doc": " 重置字典缓存数据\n"}, {"name": "insertDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 新增保存字典类型信息\n\n @param bo 字典类型信息\n @return 结果\n"}, {"name": "updateDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 修改保存字典类型信息\n\n @param bo 字典类型信息\n @return 结果\n"}, {"name": "checkDictTypeUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 校验字典类型称是否唯一\n\n @param dictType 字典类型\n @return 结果\n"}], "constructors": []}