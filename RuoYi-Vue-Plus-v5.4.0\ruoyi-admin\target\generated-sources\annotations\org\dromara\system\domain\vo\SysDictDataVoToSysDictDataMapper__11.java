package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysDictDataToSysDictDataVoMapper__11.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper__11 extends BaseMapper<SysDictDataVo, SysDictData> {
}
