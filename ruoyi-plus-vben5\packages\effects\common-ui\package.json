{"name": "@vben/common-ui", "version": "5.5.6", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/effects/common-ui"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}, "./es/tippy": {"types": "./src/components/tippy/index.ts", "default": "./src/components/tippy/index.ts"}, "./es/loading": {"types": "./src/components/loading/index.ts", "default": "./src/components/loading/index.ts"}}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-sql": "^6.7.1", "@codemirror/lang-vue": "^0.1.3", "@codemirror/lang-xml": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.2", "@vben-core/form-ui": "workspace:*", "@vben-core/popup-ui": "workspace:*", "@vben-core/preferences": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/preferences": "workspace:*", "@vben/types": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "codemirror": "6.0.1", "qrcode": "catalog:", "tippy.js": "catalog:", "vditor": "3.10.9", "vue": "catalog:", "vue-codemirror6": "1.3.4", "vue-json-pretty": "^2.4.0", "vue-json-viewer": "catalog:", "vue-router": "catalog:", "vue-tippy": "catalog:"}, "devDependencies": {"@types/qrcode": "catalog:"}}