{"doc": " 租户管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询租户列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出租户列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取租户详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 新增租户\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 修改租户\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": " 状态修改\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除租户\n\n @param ids 主键串\n"}, {"name": "dynamicTenant", "paramTypes": ["java.lang.String"], "doc": " 动态切换租户\n\n @param tenantId 租户ID\n"}, {"name": "dynamicClear", "paramTypes": [], "doc": " 清除动态租户\n"}, {"name": "syncTenantPackage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 同步租户套餐\n\n @param tenantId  租户id\n @param packageId 套餐id\n"}, {"name": "syncTenantDict", "paramTypes": [], "doc": " 同步租户字典\n"}], "constructors": []}