package io.github.linpeilie;

import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper;
import org.dromara.system.domain.SysPostToSysPostVoMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper;
import org.dromara.wms.domain.WmsWarehouseToWmsWarehouseVoMapper;
import org.dromara.wms.domain.bo.WmsWarehouseBoToWmsWarehouseMapper;
import org.dromara.wms.domain.vo.WmsWarehouseVoToWmsWarehouseMapper;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__1070.class, SysRoleToSysRoleVoMapper.class, WmsWarehouseToWmsWarehouseVoMapper.class, SysDictDataToSysDictDataVoMapper.class, OperLogEventToSysOperLogBoMapper.class, WmsWarehouseBoToWmsWarehouseMapper.class, WmsWarehouseVoToWmsWarehouseMapper.class, SysPostToSysPostVoMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__1070 {
}
