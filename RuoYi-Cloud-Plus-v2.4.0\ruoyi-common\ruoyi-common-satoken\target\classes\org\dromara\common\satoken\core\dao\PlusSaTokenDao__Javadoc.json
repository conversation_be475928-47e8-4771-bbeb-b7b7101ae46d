{"doc": " Sa-Token持久层接口(使用框架自带RedisUtils实现 协议统一)\n <p>\n 采用 caffeine + redis 多级缓存 优化并发查询效率\n <p>\n SaTokenDaoBySessionFollowObject 是 SaTokenDao 子集简化了session方法处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String"], "doc": " 获取Value，如无返空\n"}, {"name": "set", "paramTypes": ["java.lang.String", "java.lang.String", "long"], "doc": " 写入Value，并设定存活时间 (单位: 秒)\n"}, {"name": "update", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 修修改指定key-value键值对 (过期时间不变)\n"}, {"name": "delete", "paramTypes": ["java.lang.String"], "doc": " 删除Value\n"}, {"name": "getTimeout", "paramTypes": ["java.lang.String"], "doc": " 获取Value的剩余存活时间 (单位: 秒)\n"}, {"name": "updateTimeout", "paramTypes": ["java.lang.String", "long"], "doc": " 修改Value的剩余存活时间 (单位: 秒)\n"}, {"name": "getObject", "paramTypes": ["java.lang.String"], "doc": " 获取Object，如无返空\n"}, {"name": "getObject", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": " 获取 Object (指定反序列化类型)，如无返空\n\n @param key 键名称\n @return object\n"}, {"name": "setObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": " 写入Object，并设定存活时间 (单位: 秒)\n"}, {"name": "updateObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 更新Object (过期时间不变)\n"}, {"name": "deleteObject", "paramTypes": ["java.lang.String"], "doc": " 删除Object\n"}, {"name": "getObjectTimeout", "paramTypes": ["java.lang.String"], "doc": " 获取Object的剩余存活时间 (单位: 秒)\n"}, {"name": "updateObjectTimeout", "paramTypes": ["java.lang.String", "long"], "doc": " 修改Object的剩余存活时间 (单位: 秒)\n"}, {"name": "searchData", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int", "boolean"], "doc": " 搜索数据\n"}], "constructors": []}