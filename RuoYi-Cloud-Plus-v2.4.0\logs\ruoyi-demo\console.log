2025-06-16 08:29:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:29:00 [main] INFO  o.dromara.demo.RuoYiDemoApplication - Starting RuoYiDemoApplication using Java 17.0.14 with PID 39248 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-example\ruoyi-demo\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:29:00 [main] INFO  o.dromara.demo.RuoYiDemoApplication - The following 1 profile is active: "dev"
2025-06-16 08:29:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:29:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-resource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:29:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:29:05 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:29:06 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:29:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:29:07 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:29:07 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:29:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 08:29:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 08:29:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@73e6d475
2025-06-16 08:29:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 08:29:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-06-16 08:29:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 08:29:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-16 08:29:07 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.dromara.demo.domain.ShardingOrderItem ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-16 08:29:07 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.dromara.demo.domain.ShardingOrder ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-16 08:29:08 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:29:11 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1b0e9707
2025-06-16 08:29:12 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:29:12 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:29:12 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:29:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:29:12 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-demo 162.168.2.50:9401 register finished
2025-06-16 08:29:17 [main] INFO  o.dromara.demo.RuoYiDemoApplication - Started RuoYiDemoApplication in 20.0 seconds (process running for 20.732)
2025-06-16 08:29:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:29:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:29:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-resource.yml, group=DEFAULT_GROUP
2025-06-16 08:29:17 [RMI TCP Connection(6)-162.168.2.50] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 08:29:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
