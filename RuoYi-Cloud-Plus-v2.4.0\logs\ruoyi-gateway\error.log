2025-06-16 00:13:00 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=[240e:3a1:bc85:beb1:187:bb4c:9c3e:8a7b], username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:17:12 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:33:15 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:16 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:17 [boundedElastic-4] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:28 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:29 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:30 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:27 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:28 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:29 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:57 [boundedElastic-14] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:58 [boundedElastic-14] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:59 [boundedElastic-14] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:36:24 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:43:59 [boundedElastic-9] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:00 [boundedElastic-9] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:01 [boundedElastic-9] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:37 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:38 [boundedElastic-5] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:39 [boundedElastic-5] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:46:41 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 10:00:19 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:00:20 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:00:21 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:04:46 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 10:11:53 [boundedElastic-3] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/auth/tenant/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-auth"
2025-06-16 10:11:53 [boundedElastic-3] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:11:54 [boundedElastic-3] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/wms/warehouse/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-wms"
2025-06-16 10:11:54 [boundedElastic-3] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/system/user/deptTree,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-system"
2025-06-16 10:11:55 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:11:57 [reactor-http-nio-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:12:07 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/auth/tenant/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-auth"
2025-06-16 10:12:07 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/wms/warehouse/accessible,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-wms"
2025-06-16 10:12:07 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/system/user/deptTree,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-system"
2025-06-16 10:12:07 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/wms/warehouse/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-wms"
2025-06-16 10:12:07 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:12:08 [boundedElastic-8] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:12:44 [boundedElastic-4] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/system/user/getInfo,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-system"
2025-06-16 10:32:20 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:32:21 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:32:22 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:33:12 [boundedElastic-10] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:33:13 [boundedElastic-10] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:33:14 [boundedElastic-10] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:33:43 [boundedElastic-1] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:33:44 [boundedElastic-1] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:33:46 [boundedElastic-1] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:50:54 [boundedElastic-25] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:50:55 [boundedElastic-18] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:50:56 [boundedElastic-18] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:02 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:03 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:04 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:21 [boundedElastic-24] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:22 [boundedElastic-24] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:23 [boundedElastic-24] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:28 [boundedElastic-21] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:29 [boundedElastic-21] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:30 [boundedElastic-18] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:33 [boundedElastic-18] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:35 [boundedElastic-18] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:51:36 [boundedElastic-26] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:07 [boundedElastic-27] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse/close,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:32 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:33 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:34 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:36 [boundedElastic-19] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:37 [boundedElastic-19] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:53:39 [boundedElastic-19] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:16 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:16 [boundedElastic-26] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:17 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:17 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:18 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:18 [boundedElastic-26] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:32 [boundedElastic-24] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:32 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:33 [boundedElastic-24] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:33 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:34 [boundedElastic-24] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:34 [boundedElastic-20] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 10:54:37 [boundedElastic-18] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse/close,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 17:39:01 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
