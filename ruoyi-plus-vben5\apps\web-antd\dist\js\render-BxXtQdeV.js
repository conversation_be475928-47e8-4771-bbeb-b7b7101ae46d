import"./bootstrap-DCMzVRvD.js";import{W as k,L as h,O as D,A as O,b as S,D as L,C as T,c as _,F as B,d as E,S as V,M as f,Q as N,e as j,U as A,B as M,f as P}from"./index-C0wIoq37.js";import{c as Q}from"./index-DjJOU2eu.js";import{t as m}from"./data-1kX019oc.js";import{T as y}from"./index-B6iusSRX.js";import{S as U}from"./index-Ollxi7Rl.js";import{d as q,B as l,c as $,o as d,h as v,w as z,k as F,t as J,n as W,L as G,b as H,a as s,N as R,M as X}from"../jse/index-index-C-MnMZEz.js";import{g as K}from"./dict-BLkXAGS5.js";import{_ as g}from"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";const Y=q({__name:"index",props:{dicts:{default:void 0},value:{}},setup(e){const n=e,o=l(()=>{var p;const a=n.dicts.find(b=>b.dictValue==n.value),r=(p=a==null?void 0:a.listClass)!=null?p:"";return Reflect.has(m,r)?m[r].color:r}),c=l(()=>{var r;const a=n.dicts.find(u=>u.dictValue==n.value);return(r=a==null?void 0:a.cssClass)!=null?r:""}),t=l(()=>{var r;const a=n.dicts.find(u=>u.dictValue==n.value);return(r=a==null?void 0:a.dictLabel)!=null?r:"unknown"}),i=l(()=>o.value?y:"div"),C=l(()=>{var a;return((a=n.dicts)==null?void 0:a.length)===0});return(a,r)=>(d(),$("div",null,[C.value?(d(),v(H(U),{key:1,spinning:!0,size:"small"})):(d(),v(G(i.value),{key:0,class:W(c.value),color:o.value},{default:z(()=>[F(J(t.value),1)]),_:1},8,["class","color"]))]))}});function Z(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!R(e)}function ue(e){if(typeof e!="object"&&typeof e!="string")return s("span",null,[e]);if(typeof e=="object")return s(g,{class:"break-normal",data:e},null);try{const n=JSON.parse(e);return typeof n!="object"?s("span",null,[n]):s(g,{class:"break-normal",data:n},null)}catch(n){return s("span",null,[e])}}function fe(e){var i;const n=e.toUpperCase(),c=(i={DELETE:"red",GET:"green",POST:"blue",PUT:"orange"}[n])!=null?i:"default",t=`${n}请求`;return s(y,{color:c},Z(t)?t:{default:()=>[t]})}function w(e,n){return s(Y,{dicts:n,value:e},null)}function de(e,n,o=!0,c=1){return Array.isArray(e)?s("div",{class:["flex",o?"flex-col":"flex-row"],style:{gap:`${c}px`}},[e.map((t,i)=>s("div",{key:i},[w(t,n)]))]):s("div",null,[e])}function pe(e,n){const o=K(n);return w(e,o)}function x(e,n,o=!1,c="2px"){return s("span",{class:["flex","items-center",o?"justify-center":""]},[X(e),s("span",{style:{marginLeft:c}},[n])])}const I=[{icon:k,value:"windows"},{icon:h,value:"linux"},{icon:D,value:"osx"},{icon:O,value:"android"},{icon:S,value:"iphone"}],ee=[{icon:T,value:"chrome"},{icon:_,value:"edge"},{icon:B,value:"firefox"},{icon:E,value:"opera"},{icon:V,value:"safari"},{icon:f,value:"micromessenger"},{icon:f,value:"windowswechat"},{icon:N,value:"quark"},{icon:f,value:"wxwork"},{icon:Q,value:"qq"},{icon:j,value:"dingtalk"},{icon:A,value:"uc"},{icon:M,value:"baidu"}];function me(e,n=!1){if(!e)return;let o=I.find(t=>e.toLocaleLowerCase().includes(t.value));e.toLocaleLowerCase().includes("windows")&&(o=I[0]);const c=o?o.icon:L;return x(c,e,n,"5px")}function ve(e,n=!1){if(!e)return;const o=ee.find(t=>e.toLocaleLowerCase().includes(t.value)),c=o?o.icon:P;return x(c,e,n,"5px")}export{me as a,ve as b,fe as c,ue as d,w as e,de as f,pe as r};
