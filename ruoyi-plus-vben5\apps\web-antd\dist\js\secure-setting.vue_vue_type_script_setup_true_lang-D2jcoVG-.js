var P=Object.defineProperty,g=Object.defineProperties;var b=Object.getOwnPropertyDescriptors;var p=Object.getOwnPropertySymbols;var _=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable;var f=(t,s,e)=>s in t?P(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e,d=(t,s)=>{for(var e in s||(s={}))_.call(s,e)&&f(t,e,s[e]);if(p)for(var e of p(s))x.call(s,e)&&f(t,e,s[e]);return t},u=(t,s)=>g(t,b(s));var w=(t,s,e)=>new Promise((r,i)=>{var m=n=>{try{o(e.next(n))}catch(l){i(l)}},a=n=>{try{o(e.throw(n))}catch(l){i(l)}},o=n=>n.done?r(n.value):Promise.resolve(n.value).then(m,a);o((e=e.apply(t,s)).next())});import{aj as h,al as c,am as B,an as O,ao as y}from"./bootstrap-DCMzVRvD.js";import{u as F}from"./index-DLUQE3Et.js";import{d as N,c as S,o as k,a as C,b as I}from"../jse/index-index-C-MnMZEz.js";const q={class:"mt-[16px] md:w-full lg:w-1/2 2xl:w-2/5"},E=N({__name:"secure-setting",setup(t){const[s,e]=h({actionWrapperClass:"text-left mb-[16px] ml-[96px]",commonConfig:{labelWidth:90},handleSubmit:m,resetButtonOptions:{show:!1},schema:[{component:"InputPassword",fieldName:"oldPassword",label:"旧密码",rules:c({message:"请输入密码"}).min(5,"密码长度不能少于5个字符").max(20,"密码长度不能超过20个字符")},{component:"InputPassword",dependencies:{rules(a){return c({message:"请输入新密码"}).min(5,"密码长度不能少于5个字符").max(20,"密码长度不能超过20个字符").refine(o=>o!==a.oldPassword,"新旧密码不能相同")},triggerFields:["newPassword","oldPassword"]},fieldName:"newPassword",label:"新密码",rules:"required"},{component:"InputPassword",dependencies:{rules(a){return c({message:"请输入确认密码"}).min(5,"密码长度不能少于5个字符").max(20,"密码长度不能超过20个字符").refine(o=>o===a.newPassword,"新密码和确认密码不一致")},triggerFields:["newPassword","confirmPassword"]},fieldName:"confirmPassword",label:"确认密码",rules:"required"}],submitButtonOptions:{content:"修改密码"}});function r(a){e.setState(o=>u(d({},o),{submitButtonOptions:u(d({},o.submitButtonOptions),{loading:a})}))}const i=B();function m(a){O.confirm({content:"确认修改密码吗？",onOk:()=>w(null,null,function*(){try{r(!0);const o=y(a,["confirmPassword"]);yield F(o),yield i.logout(!0)}catch(o){console.error(o)}finally{r(!1)}}),title:"提示"})}return(a,o)=>(k(),S("div",q,[C(I(s))]))}});export{E as _};
