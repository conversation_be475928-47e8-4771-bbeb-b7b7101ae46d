var F=Object.defineProperty;var z=Object.getOwnPropertySymbols;var J=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var E=(n,t,e)=>t in n?F(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,M=(n,t)=>{for(var e in t||(t={}))J.call(t,e)&&E(n,e,t[e]);if(z)for(var e of z(t))Q.call(t,e)&&E(n,e,t[e]);return n};var f=(n,t,e)=>new Promise((s,r)=>{var d=l=>{try{g(e.next(l))}catch(o){r(o)}},h=l=>{try{g(e.throw(l))}catch(o){r(o)}},g=l=>l.done?s(l.value):Promise.resolve(l.value).then(d,h);g((e=e.apply(n,t)).next())});import{w as K,cF as X,at as Y,cG as ee,av as te,bc as ae,$ as S,ap as N,z as oe,bb as ne,am as se,az as re,aB as ie,cH as le,bA as ce,bz as P,cI as ue,cJ as pe}from"./bootstrap-DCMzVRvD.js";import{n as de,o as me,G as fe}from"./index-C0wIoq37.js";import{u as W,i as he,j as ge}from"./tenant-DKIZPfcZ.js";import{S as ve}from"./index-Ollxi7Rl.js";import ye from"./index-CIjgbPOA.js";import{p as T,y as we,aq as _e,I as xe,_ as be,d as $,m as ke,u as Se,q as U,B as I,c as q,o as R,a as p,w as y,b as a,r as Te,v as D,C as Me,f as Ae,a2 as Ce,M as Le,ap as L,a0 as O,h as Be}from"../jse/index-index-C-MnMZEz.js";import{u as Ie}from"./use-tabs-Zz_nc_n2.js";import{_ as Ne}from"./login.vue_vue_type_script_setup_true_lang-Dj1gXWQf.js";import{e as $e,N as Re,i as ze,_ as Ee}from"./layout.vue_vue_type_script_setup_true_lang-DJtp5nRW.js";import{u as Pe}from"./use-modal-CeMSCP2m.js";import{_ as Oe}from"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./index-D6-099PU.js";import"./helper-Bc7QQ92Q.js";import"./index-B-GBMyZJ.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./Overflow-DmNzxpBy.js";import"./index-CHpIOV4R.js";import"./List-DFkqSBvs.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CERO2SW5.js";import"./SearchOutlined-BOD_ZIye.js";import"./move-DLDqWE9R.js";import"./slide-B82O6h2Y.js";import"./useMemo-BwJyMulH.js";import"./statusUtils-d85DZFMd.js";import"./captcha-Bo71W0x_.js";import"./oauth-common-CrHfL2p7.js";import"./index-DjJOU2eu.js";import"./oauth-login.vue_vue_type_script_setup_true_lang-CLt7AYa7.js";import"./index-By0xk_Xq.js";import"./auth-title-CzJiGVH3.js";import"./index-A0HTSyFu.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-BfrV53rV.js";import"./use-drawer-6qcpK-D1.js";import"./x-Bfkqqjgb.js";import"./TabsList.vue_vue_type_script_setup_true_lang-QZrN9Wp8.js";import"./rotate-cw-DzZTu9nW.js";const Ve=K("book-open-text",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M16 12h2",key:"7q9ll5"}],["path",{d:"M16 8h2",key:"msurwy"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}],["path",{d:"M6 12h2",key:"32wvfc"}],["path",{d:"M6 8h2",key:"30oboj"}]]),b=T(),V=T(!1),B=T({advancedStyle:{colorStops:[{color:"gray",offset:0},{color:"gray",offset:1}],type:"linear"},content:"",contentType:"multi-line-text",globalAlpha:.25,gridLayoutOptions:{cols:2,gap:[20,20],matrix:[[1,0],[0,1]],rows:2},height:200,layout:"grid",rotate:30,width:160});function We(){function n(s){return f(this,null,function*(){var d;const{Watermark:r}=yield be(()=>f(null,null,function*(){const{Watermark:h}=yield import("./index.esm-Ofvdfudw.js");return{Watermark:h}}),[]);B.value=M(M({},B.value),s),b.value=new r(B.value),yield(d=b.value)==null?void 0:d.create()})}function t(s){return f(this,null,function*(){var r;b.value?(yield xe(),yield(r=b.value)==null?void 0:r.changeOptions(M(M({},B.value),s))):yield n(s)})}function e(){b.value&&(b.value.destroy(),b.value=void 0)}return V.value||(V.value=!0,we(()=>{e()})),{destroyWatermark:e,updateWatermark:t,watermark:_e(b)}}const Ue=$({name:"LoginExpiredModal",__name:"login-expired-modal",props:ke({avatar:{default:""},zIndex:{default:0},codeLoginPath:{},forgetPasswordPath:{},loading:{type:Boolean},qrCodeLoginPath:{},registerPath:{},showCodeLogin:{type:Boolean},showForgetPassword:{type:Boolean},showQrcodeLogin:{type:Boolean},showRegister:{type:Boolean},showRememberMe:{type:Boolean},showThirdPartyLogin:{type:Boolean},subTitle:{},title:{},submitButtonText:{}},{open:{type:Boolean},openModifiers:{}}),emits:["update:open"],setup(n){const t=n,e=Se(n,"open"),[s,r]=Pe();U(()=>e.value,o=>{r.setState({isOpen:o})});const d=I(()=>t.zIndex||l()),h=["ant-message","loading"];function g(o){return h.some(v=>o.classList.contains(v))}function l(){let o=0;return[...document.querySelectorAll("*")].forEach(c=>{const i=window.getComputedStyle(c).getPropertyValue("z-index");i&&!Number.isNaN(Number.parseInt(i))&&!g(c)&&(o=Math.max(o,Number.parseInt(i)))}),o+1}return(o,v)=>(R(),q("div",null,[p(a(s),{closable:!1,"close-on-click-modal":!1,"close-on-press-escape":!1,footer:!1,"fullscreen-button":!1,header:!1,"z-index":d.value,class:"border-none px-10 py-6 text-center shadow-xl sm:w-[600px] sm:rounded-2xl md:h-[unset]"},{default:y(()=>[p(a(Oe),{src:o.avatar,class:"mx-auto mb-6 size-20"},null,8,["src"]),p(a(X),{"show-forget-password":!1,"show-register":!1,"show-remember-me":!1,"sub-title":o.$t("authentication.loginAgainSubTitle"),title:o.$t("authentication.loginAgainTitle")},{default:y(()=>[Te(o.$slots,"default")]),_:3},8,["sub-title","title"])]),_:3},8,["z-index"])]))}}),qe={key:0,class:"mr-[8px] hidden md:block"},De=$({__name:"index",setup(n){const{hasAccessByRoles:t}=Y(),e=T(),s=T(),r=W(),{initTenant:d,setChecked:h}=r,{tenantEnable:g,tenantList:l}=ee(r),o=I(()=>t(["superadmin"])&&a(g));D(()=>f(null,null,function*(){t(["superadmin"])&&(yield d())}));const v=te(),{closeOtherTabs:c,refreshTab:A,closeAllTabs:i}=Ie();function w(u){return f(this,null,function*(){h(u),v.meta.requireHomeRedirect?yield i():(yield c(),yield A())})}const m=ae(),_=Me(),k=T(!1),G=(u,x)=>f(null,null,function*(){var C;if(a(e)!==u)try{k.value=!0,yield he(u),e.value=u,(C=_.value)==null||C.call(_),_.value=N.success(`${S("component.tenantToggle.switch")} ${x.companyName}`),w(!0),setTimeout(()=>m.resetCache())}catch(j){console.error(j)}finally{k.value=!1}});function H(){return f(this,null,function*(){var u;try{k.value=!0,yield ge(),(u=_.value)==null||u.call(_),_.value=N.success(S("component.tenantToggle.reset")),e.value="",w(!1),setTimeout(()=>m.resetCache())}catch(x){console.error(x)}finally{k.value=!1}})}function Z(u,x){return x.companyName.toLowerCase().includes(u.toLowerCase())}return(u,x)=>o.value?(R(),q("div",qe,[p(a(ye),{value:s.value,"onUpdate:value":x[0]||(x[0]=C=>s.value=C),disabled:k.value,"field-names":{label:"companyName",value:"tenantId"},"filter-option":Z,options:a(l),placeholder:a(S)("component.tenantToggle.placeholder"),"dropdown-style":{position:"fixed",zIndex:1024},"allow-clear":"",class:"w-60","show-search":"",onDeselect:H,onSelect:G},Ce({_:2},[k.value?{name:"suffixIcon",fn:y(()=>[p(a(ve),{size:"small",spinning:""})]),key:"0"}:void 0]),1032,["value","disabled","options","placeholder"])])):Ae("",!0)}}),Ge=oe(De,[["__scopeId","data-v-79240949"]]),Bt=$({__name:"basic",setup(n){const t=ne(),e=se(),s=re(),r=ie(),{destroyWatermark:d,updateWatermark:h}=We(),g=W(),l=I(()=>{const i=[{handler:()=>{L(ce,{target:"_blank"})},icon:Ve,text:S("ui.widgets.document")},{handler:()=>{r.push("/profile")},icon:de,text:S("ui.widgets.profile")},{handler:()=>{L("https://gitee.com/dapppp/ruoyi-plus-vben5",{target:"_blank"})},icon:()=>Le(fe,{class:"text-red-800"}),text:"Gitee项目地址"},{handler:()=>{L(P,{target:"_blank"})},icon:me,text:"Vben官方地址"},{handler:()=>{L(`${P}/issues`,{target:"_blank"})},icon:le,text:S("ui.widgets.qa")}];return g.checked&&i.splice(1,1),i}),o=I(()=>{var i;return((i=t.userInfo)==null?void 0:i.avatar)||O.app.defaultAvatar});function v(){return f(this,null,function*(){yield e.logout(!1),pe()})}const c=ue();D(()=>c.startListeningMessage());function A(){N.warning("暂未开放")}return U(()=>O.app.watermark,i=>f(null,null,function*(){var w,m;i?yield h({content:`${(w=t.userInfo)==null?void 0:w.username} - ${(m=t.userInfo)==null?void 0:m.realName}`}):d()}),{immediate:!0}),(i,w)=>(R(),Be(a(Ee),{onClearPreferencesAndLogout:v},{"header-right-1":y(()=>[p(a(Ge))]),"user-dropdown":y(()=>{var m;return[p(a(ze),{avatar:o.value,menus:l.value,text:(m=a(t).userInfo)==null?void 0:m.realName,description:"<EMAIL>","tag-text":"Pro",onLogout:v},null,8,["avatar","menus","text"])]}),notification:y(()=>[p(a(Re),{dot:a(c).showDot,notifications:a(c).notifications,onClear:a(c).clearAllMessage,onMakeAll:a(c).setAllRead,onRead:a(c).setRead,onViewAll:A},null,8,["dot","notifications","onClear","onMakeAll","onRead"])]),extra:y(()=>[p(a(Ue),{open:a(s).loginExpired,"onUpdate:open":w[0]||(w[0]=m=>a(s).loginExpired=m),avatar:o.value},{default:y(()=>[p(Ne)]),_:1},8,["open","avatar"])]),"lock-screen":y(()=>[p(a($e),{avatar:o.value,onToLogin:v},null,8,["avatar"])]),_:1}))}});export{Bt as default};
