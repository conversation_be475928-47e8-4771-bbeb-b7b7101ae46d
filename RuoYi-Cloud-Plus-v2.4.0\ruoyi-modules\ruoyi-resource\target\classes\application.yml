# Tomcat
server:
  port: 9204

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-resource
  profiles:
    # 环境配置
    active: dev

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: 127.0.0.1:8848
      username: nacos
      password: nacos
      discovery:
        # 注册组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml
