var j=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var b=(s,a)=>{var e={};for(var o in s)q.call(s,o)&&a.indexOf(o)<0&&(e[o]=s[o]);if(s!=null&&j)for(var o of j(s))a.indexOf(o)<0&&R.call(s,o)&&(e[o]=s[o]);return e};var $=(s,a,e)=>new Promise((o,r)=>{var n=d=>{try{u(e.next(d))}catch(h){r(h)}},l=d=>{try{u(e.throw(d))}catch(h){r(h)}},u=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,l);u((e=e.apply(s,a)).next())});import{d as f,h as m,o as i,b as t,e as O,g as D,w as c,r as _,B as v,a as g,O as k,G as M,a4 as H,a5 as K,m as L,u as F,c as y,F as G,K as N,n as T,f as w,k as A,L as E,t as x,a0 as I,aw as V,j as p,I as W}from"../jse/index-index-C-MnMZEz.js";import{w as B,bG as P,df as X,dg as Y,dh as J,di as Q,cu as C,dj as Z,dk as ee,cc as te,dl as ae,dm as oe,dn as se,bC as ne,dp as le,aN as re,as as de,z as ie,cp as ce,$ as z,dq as ue}from"./bootstrap-DCMzVRvD.js";const pe=B("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);const me=B("moon-star",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9",key:"4ay0iu"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}]]);const fe=B("sun-moon",[["path",{d:"M12 8a2.83 2.83 0 0 0 4 4 4 4 0 1 1-4-4",key:"1fu5g2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.9 4.9 1.4 1.4",key:"b9915j"}],["path",{d:"m17.7 17.7 1.4 1.4",key:"qc3ed3"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.3 17.7-1.4 1.4",key:"5gca6"}],["path",{d:"m19.1 4.9-1.4 1.4",key:"wpu9u6"}]]);const he=B("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),ge=f({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(s,{emit:a}){const r=P(s,a);return(n,l)=>(i(),m(t(X),O(D(t(r))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),ye=f({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(s,{emit:a}){const e=s,o=a,r=v(()=>{const d=e,{class:l}=d;return b(d,["class"])}),n=P(r,o);return(l,u)=>(i(),m(t(Y),null,{default:c(()=>[g(t(J),k(t(n),{class:t(M)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),_e=f({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const a=s;return(e,o)=>(i(),m(t(Q),O(D(a)),{default:c(()=>[_(e.$slots,"default")]),_:3},16))}}),ve=f({__name:"DropdownMenuItem",props:{class:{},inset:{type:Boolean},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const a=s,e=v(()=>{const l=a,{class:r}=l;return b(l,["class"])}),o=C(e);return(r,n)=>(i(),m(t(Z),k(t(o),{class:t(M)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",a.class)}),{default:c(()=>[_(r.$slots,"default")]),_:3},16,["class"]))}}),ke=f({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const e=C(s);return(o,r)=>(i(),m(t(ee),k({class:"outline-none"},t(e)),{default:c(()=>[_(o.$slots,"default")]),_:3},16))}}),be=te("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{defaultVariants:{size:"default",variant:"default"},variants:{size:{default:"h-9 px-3",lg:"h-10 px-3",sm:"h-8 px-2"},variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground"}}}),xe=f({__name:"ToggleGroup",props:{class:{},size:{},variant:{},rovingFocus:{type:Boolean},disabled:{type:Boolean},orientation:{},dir:{},loop:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},type:{},modelValue:{},defaultValue:{}},emits:["update:modelValue"],setup(s,{emit:a}){const e=s,o=a;H("toggleGroup",{size:e.size,variant:e.variant});const r=v(()=>{const d=e,{class:l}=d;return b(d,["class"])}),n=P(r,o);return(l,u)=>(i(),m(t(ae),k(t(n),{class:t(M)("flex items-center justify-center gap-1",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"]))}}),we=f({__name:"ToggleGroupItem",props:{class:{},size:{},variant:{},value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const a=s,e=K("toggleGroup"),o=v(()=>{const h=a,{class:n,size:l,variant:u}=h;return b(h,["class","size","variant"])}),r=C(o);return(n,l)=>{var u,d;return i(),m(t(oe),k(t(r),{class:t(M)(t(be)({variant:((u=t(e))==null?void 0:u.variant)||n.variant,size:((d=t(e))==null?void 0:d.size)||n.size}),a.class)}),{default:c(()=>[_(n.$slots,"default")]),_:3},16,["class"])}}}),Me=f({name:"DropdownRadioMenu",__name:"dropdown-radio-menu",props:L({menus:{}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const a=F(s,"modelValue");function e(o){a.value=o}return(o,r)=>(i(),m(t(ge),null,{default:c(()=>[g(t(ke),{"as-child":"",class:"flex items-center gap-1"},{default:c(()=>[_(o.$slots,"default")]),_:3}),g(t(ye),{align:"start"},{default:c(()=>[g(t(_e),null,{default:c(()=>[(i(!0),y(G,null,N(o.menus,n=>(i(),m(t(ve),{key:n.key,class:T([n.value===a.value?"bg-accent text-accent-foreground":"","data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer"]),onClick:l=>e(n.value)},{default:c(()=>[n.icon?(i(),m(E(n.icon),{key:0,class:"mr-2 size-4"})):w("",!0),n.icon?w("",!0):(i(),y("span",{key:1,class:T([n.value===a.value?"bg-foreground":"","mr-2 size-1.5 rounded-full"])},null,2)),A(" "+x(n.label),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:3}))}}),Be={class:"text-md flex-center"},$e=["href"],ze=["href"],Oe=f({name:"Copyright",__name:"copyright",props:{companyName:{default:"Vben Admin"},companySiteLink:{default:""},date:{default:"2024"},icp:{default:""},icpLink:{default:""}},setup(s){return(a,e)=>(i(),y("div",Be,[a.icp?(i(),y("a",{key:0,href:a.icpLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},x(a.icp),9,$e)):w("",!0),A(" Copyright © "+x(a.date)+" ",1),a.companyName?(i(),y("a",{key:1,href:a.companySiteLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},x(a.companyName),9,ze)):w("",!0)]))}}),De=f({name:"LanguageToggle",__name:"language-toggle",setup(s){function a(e){return $(this,null,function*(){if(!e)return;const o=e;V({app:{locale:o}}),yield le(o)})}return(e,o)=>(i(),y("div",null,[g(t(Me),{menus:t(se),"model-value":t(I).app.locale,"onUpdate:modelValue":a},{default:c(()=>[g(t(ne),null,{default:c(()=>[g(t(pe),{class:"text-foreground size-4"})]),_:1})]),_:1},8,["menus","model-value"])]))}}),Ve=f({name:"ThemeToggleButton",__name:"theme-button",props:L({type:{default:"normal"}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const a=s,e=F(s,"modelValue"),o=v(()=>e.value?"light":"dark"),r=v(()=>a.type==="normal"?{variant:"heavy"}:{class:"rounded-full",size:"icon",style:{padding:"7px"},variant:"icon"});function n(l){if(!(document.startViewTransition&&!window.matchMedia("(prefers-reduced-motion: reduce)").matches)||!l){e.value=!e.value;return}const d=l.clientX,h=l.clientY,U=Math.hypot(Math.max(d,innerWidth-d),Math.max(h,innerHeight-h));document.startViewTransition(()=>$(null,null,function*(){e.value=!e.value,yield W()})).ready.then(()=>{const S=[`circle(0px at ${d}px ${h}px)`,`circle(${U}px at ${d}px ${h}px)`];document.documentElement.animate({clipPath:e.value?[...S].reverse():S},{duration:450,easing:"ease-in",pseudoElement:e.value?"::view-transition-old(root)":"::view-transition-new(root)"})})}return(l,u)=>(i(),m(t(re),k({"aria-label":o.value,class:[[`is-${o.value}`],"theme-toggle cursor-pointer border-none bg-none"],"aria-live":"polite"},r.value,{onClick:de(n,["stop"])}),{default:c(()=>u[0]||(u[0]=[p("svg",{"aria-hidden":"true",height:"24",viewBox:"0 0 24 24",width:"24"},[p("mask",{id:"theme-toggle-moon",class:"theme-toggle__moon",fill:"hsl(var(--foreground)/80%)",stroke:"none"},[p("rect",{fill:"white",height:"100%",width:"100%",x:"0",y:"0"}),p("circle",{cx:"40",cy:"8",fill:"black",r:"11"})]),p("circle",{id:"sun",class:"theme-toggle__sun",cx:"12",cy:"12",mask:"url(#theme-toggle-moon)",r:"11"}),p("g",{class:"theme-toggle__sun-beams"},[p("line",{x1:"12",x2:"12",y1:"1",y2:"3"}),p("line",{x1:"12",x2:"12",y1:"21",y2:"23"}),p("line",{x1:"4.22",x2:"5.64",y1:"4.22",y2:"5.64"}),p("line",{x1:"18.36",x2:"19.78",y1:"18.36",y2:"19.78"}),p("line",{x1:"1",x2:"3",y1:"12",y2:"12"}),p("line",{x1:"21",x2:"23",y1:"12",y2:"12"}),p("line",{x1:"4.22",x2:"5.64",y1:"19.78",y2:"18.36"}),p("line",{x1:"18.36",x2:"19.78",y1:"5.64",y2:"4.22"})])],-1)])),_:1,__:[0]},16,["aria-label","class"]))}}),Pe=ie(Ve,[["__scopeId","data-v-8e18cb5a"]]),Le=f({name:"ThemeToggle",__name:"theme-toggle",props:{shouldOnHover:{type:Boolean,default:!1}},setup(s){function a(r){V({theme:{mode:r?"dark":"light"}})}const{isDark:e}=ce(),o=[{icon:he,name:"light",title:z("preferences.theme.light")},{icon:me,name:"dark",title:z("preferences.theme.dark")},{icon:fe,name:"auto",title:z("preferences.followSystem")}];return(r,n)=>(i(),y("div",null,[g(t(ue),{disabled:!r.shouldOnHover,side:"bottom"},{trigger:c(()=>[g(Pe,{"model-value":t(e),type:"icon","onUpdate:modelValue":a},null,8,["model-value"])]),default:c(()=>[g(t(xe),{"model-value":t(I).theme.mode,class:"gap-2",type:"single",variant:"outline","onUpdate:modelValue":n[0]||(n[0]=l=>t(V)({theme:{mode:l}}))},{default:c(()=>[(i(),y(G,null,N(o,l=>g(t(we),{key:l.name,value:l.name},{default:c(()=>[(i(),m(E(l.icon),{class:"size-5"}))]),_:2},1032,["value"])),64))]),_:1},8,["model-value"])]),_:1},8,["disabled"])]))}});export{me as M,he as S,De as _,Le as a,Me as b,Oe as c,ge as d,ke as e,ye as f,ve as g,_e as h,xe as i,we as j,fe as k};
