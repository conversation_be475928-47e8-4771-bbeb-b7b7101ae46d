var I=(x,g,f)=>new Promise((a,v)=>{var h=t=>{try{o(f.next(t))}catch(e){v(e)}},b=t=>{try{o(f.throw(t))}catch(e){v(e)}},o=t=>t.done?a(t.value):Promise.resolve(t.value).then(h,b);o((f=f.apply(x,g)).next())});import{d as A,U as m,C as E,B as y,h as p,o as n,w as s,f as D,b as l,a as r,k as i,t as u,L as M,c as _,F as C,K as T,j as O}from"../jse/index-index-C-MnMZEz.js";import{d as U,r as $}from"./relativeTime-DEqmspR6.js";import{f as j}from"./index-ocPq22VW.js";import{r as F}from"./render-BxXtQdeV.js";import{D as R,a as d}from"./index-D59rZjD-.js";import{T as k}from"./index-B6iusSRX.js";import{u as K}from"./use-modal-CeMSCP2m.js";import{ar as P}from"./bootstrap-DCMzVRvD.js";const Y={key:0,class:"flex flex-wrap gap-0.5"},q={key:1},G={key:0,class:"flex flex-wrap gap-0.5"},H={key:1},te=A({__name:"user-info-modal",setup(x){m.extend(U),m.extend($);const[g,f]=K({onOpenChange:v,onClosed(){a.value=null}}),a=E(null);function v(o){return I(this,null,function*(){if(!o)return null;f.modalLoading(!0);const{userId:t}=f.getData(),e=yield j(t),{postIds:B=[],posts:L=[],roleIds:w=[],roles:S=[],user:N}=e,V=L.filter(c=>B.includes(c.postId)).map(c=>c.postName),z=S.filter(c=>w.includes(c.roleId)).map(c=>c.roleName);N.postNames=V,N.roleNames=z,a.value=N,f.modalLoading(!1)})}const h=y(()=>{if(!a.value)return"-";const{deptName:o,nickName:t,userName:e}=a.value;return`${e} / ${t} / ${o!=null?o:"-"}`}),b=y(()=>{if(!a.value)return"-";const{loginDate:o}=a.value;m.locale("zh-cn");const t=m().diff(m(o),"second");return m.duration(t,"seconds").humanize()});return(o,t)=>(n(),p(l(g),{footer:!1,"fullscreen-button":!1,title:"用户信息"},{default:s(()=>[a.value?(n(),p(l(R),{key:0,size:"small",column:1,bordered:""},{default:s(()=>[r(l(d),{label:"userId"},{default:s(()=>[i(u(a.value.userId),1)]),_:1}),r(l(d),{label:"用户状态"},{default:s(()=>[(n(),p(M(l(F)(a.value.status,l(P).SYS_NORMAL_DISABLE))))]),_:1}),r(l(d),{label:"用户信息"},{default:s(()=>[i(u(h.value),1)]),_:1}),r(l(d),{label:"手机号"},{default:s(()=>[i(u(a.value.phonenumber||"-"),1)]),_:1}),r(l(d),{label:"邮箱"},{default:s(()=>[i(u(a.value.email||"-"),1)]),_:1}),r(l(d),{label:"岗位"},{default:s(()=>[a.value.postNames.length>0?(n(),_("div",Y,[(n(!0),_(C,null,T(a.value.postNames,e=>(n(),p(l(k),{key:e},{default:s(()=>[i(u(e),1)]),_:2},1024))),128))])):(n(),_("span",q,"-"))]),_:1}),r(l(d),{label:"权限"},{default:s(()=>[a.value.roleNames.length>0?(n(),_("div",G,[(n(!0),_(C,null,T(a.value.roleNames,e=>(n(),p(l(k),{key:e},{default:s(()=>[i(u(e),1)]),_:2},1024))),128))])):(n(),_("span",H,"-"))]),_:1}),r(l(d),{label:"创建时间"},{default:s(()=>[i(u(a.value.createTime),1)]),_:1}),r(l(d),{label:"上次登录IP"},{default:s(()=>{var e;return[i(u((e=a.value.loginIp)!=null?e:"-"),1)]}),_:1}),r(l(d),{label:"上次登录时间"},{default:s(()=>{var e;return[O("span",null,u((e=a.value.loginDate)!=null?e:"-"),1),b.value?(n(),p(l(k),{key:0,class:"ml-2",bordered:!1,color:"processing"},{default:s(()=>[i(u(b.value)+"前 ",1)]),_:1})):D("",!0)]}),_:1}),r(l(d),{label:"备注"},{default:s(()=>{var e;return[i(u((e=a.value.remark)!=null?e:"-"),1)]}),_:1})]),_:1})):D("",!0)]),_:1}))}});export{te as _};
