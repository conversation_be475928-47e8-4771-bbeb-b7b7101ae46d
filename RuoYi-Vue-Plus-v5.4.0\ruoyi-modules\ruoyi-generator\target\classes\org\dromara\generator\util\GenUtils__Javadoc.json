{"doc": " 代码生成器 工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "initTable", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": " 初始化表信息\n"}, {"name": "initColumnField", "paramTypes": ["org.dromara.generator.domain.GenTableColumn", "org.dromara.generator.domain.GenTable"], "doc": " 初始化列属性字段\n"}, {"name": "arraysContains", "paramTypes": ["java.lang.String[]", "java.lang.String"], "doc": " 校验数组是否包含指定值\n\n @param arr         数组\n @param targetValue 值\n @return 是否包含\n"}, {"name": "getModuleName", "paramTypes": ["java.lang.String"], "doc": " 获取模块名\n\n @param packageName 包名\n @return 模块名\n"}, {"name": "getBusinessName", "paramTypes": ["java.lang.String"], "doc": " 获取业务名\n\n @param tableName 表名\n @return 业务名\n"}, {"name": "convertClassName", "paramTypes": ["java.lang.String"], "doc": " 表名转换成Java类名\n\n @param tableName 表名称\n @return 类名\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": " 批量替换前缀\n\n @param replacementm 替换值\n @param searchList   替换列表\n"}, {"name": "replaceText", "paramTypes": ["java.lang.String"], "doc": " 关键字替换\n\n @param text 需要被替换的名字\n @return 替换后的名字\n"}, {"name": "getDbType", "paramTypes": ["java.lang.String"], "doc": " 获取数据库类型字段\n\n @param columnType 列类型\n @return 截取后的列类型\n"}, {"name": "getColumnLength", "paramTypes": ["java.lang.String"], "doc": " 获取字段长度\n\n @param columnType 列类型\n @return 截取后的列类型\n"}], "constructors": []}