package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {},
    imports = {}
)
public interface SysLogininforBoToSysLogininforMapper__11 extends BaseMapper<SysLogininforBo, SysLogininfor> {
}
