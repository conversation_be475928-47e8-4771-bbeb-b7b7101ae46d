var C=(o,b,c)=>new Promise((d,g)=>{var p=n=>{try{v(c.next(n))}catch(l){g(l)}},t=n=>{try{v(c.throw(n))}catch(l){g(l)}},v=n=>n.done?d(n.value):Promise.resolve(n.value).then(p,t);v((c=c.apply(o,b)).next())});import{y,$ as u,aq as B,bn as T,ar as V}from"./bootstrap-DCMzVRvD.js";import{Tinymce as M}from"./index-CDHRR1Za.js";import{g as k}from"./dict-BLkXAGS5.js";import{u as N}from"./popup-D6rC6QBG.js";import{p as x}from"./pick-CyUZAAhv.js";import"./index-BxBCzu2M.js";import{I as E}from"./Search-ClCped_G.js";import{d as G,p as q,B as z,P as A,h as L,o as P,w as f,a as i,b as e,j as W,O as _}from"../jse/index-index-C-MnMZEz.js";import{u as Y}from"./use-modal-CeMSCP2m.js";import{a as w}from"./Group-oWwucTzK.js";function re(o){return y.get("/system/notice/list",{params:o})}function j(o){return y.get(`/system/notice/${o}`)}function J(o){return y.postWithMsg("/system/notice",o)}function H(o){return y.putWithMsg("/system/notice",o)}function ue(o){return y.deleteWithMsg(`/system/notice/${o}`)}const K={class:"grid sm:grid-cols-1 lg:grid-cols-2"},ce=G({__name:"notice-modal",emits:["reload"],setup(o,{emit:b}){const c=b,d=q(!1),g=z(()=>d.value?u("pages.common.edit"):u("pages.common.add")),p={noticeId:void 0,noticeTitle:"",status:"0",noticeType:"1",noticeContent:""},t=q(p),v=q({status:[{required:!0,message:u("ui.formRules.selectRequired")}],noticeContent:[{required:!0,message:u("ui.formRules.required")}],noticeType:[{required:!0,message:u("ui.formRules.selectRequired")}],noticeTitle:[{required:!0,message:u("ui.formRules.required")}]}),{validate:n,validateInfos:l,resetFields:O}=B.useForm(t,v);function R(){return JSON.stringify(t.value)}const{onBeforeClose:S,markInitialized:U,resetInitialized:I}=N({initializedGetter:R,currentGetter:R}),[$,m]=Y({class:"w-[800px]",fullscreenButton:!0,onBeforeClose:S,onClosed:h,onConfirm:D,onOpenChange:r=>C(null,null,function*(){if(!r)return null;m.modalLoading(!0);const{id:a}=m.getData();if(d.value=!!a,d.value&&a){const s=yield j(a),F=x(s,Object.keys(p));t.value=F}yield U(),m.modalLoading(!1)})});function D(){return C(this,null,function*(){try{m.lock(!0),yield n();const r=A(t.value);yield d.value?H(r):J(r),I(),c("reload"),m.close()}catch(r){console.error(r)}finally{m.lock(!1)}})}function h(){return C(this,null,function*(){t.value=p,O(),I()})}return(r,a)=>(P(),L(e($),{title:g.value},{default:f(()=>[i(e(B),{layout:"vertical"},{default:f(()=>[i(e(T),_({label:"公告标题"},e(l).noticeTitle),{default:f(()=>[i(e(E),{placeholder:e(u)("ui.formRules.required"),value:t.value.noticeTitle,"onUpdate:value":a[0]||(a[0]=s=>t.value.noticeTitle=s)},null,8,["placeholder","value"])]),_:1},16),W("div",K,[i(e(T),_({label:"公告状态"},e(l).status),{default:f(()=>[i(e(w),{"button-style":"solid","option-type":"button",value:t.value.status,"onUpdate:value":a[1]||(a[1]=s=>t.value.status=s),options:e(k)(e(V).SYS_NOTICE_STATUS)},null,8,["value","options"])]),_:1},16),i(e(T),_({label:"公告类型"},e(l).noticeType),{default:f(()=>[i(e(w),{"button-style":"solid","option-type":"button",value:t.value.noticeType,"onUpdate:value":a[2]||(a[2]=s=>t.value.noticeType=s),options:e(k)(e(V).SYS_NOTICE_TYPE)},null,8,["value","options"])]),_:1},16)]),i(e(T),_({label:"公告内容"},e(l).noticeContent),{default:f(()=>[i(e(M),{modelValue:t.value.noticeContent,"onUpdate:modelValue":a[3]||(a[3]=s=>t.value.noticeContent=s)},null,8,["modelValue"])]),_:1},16)]),_:1})]),_:1},8,["title"]))}});export{ce as _,ue as a,re as n};
