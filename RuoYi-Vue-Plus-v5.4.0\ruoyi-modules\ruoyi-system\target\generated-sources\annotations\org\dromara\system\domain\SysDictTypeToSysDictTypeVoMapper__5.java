package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__5;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__5.class,SysDictTypeVoToSysDictTypeMapper__5.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__5 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
