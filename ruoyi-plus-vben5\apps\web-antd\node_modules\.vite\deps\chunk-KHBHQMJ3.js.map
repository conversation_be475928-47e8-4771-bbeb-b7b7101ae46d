{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/type.js"], "sourcesContent": ["// https://stackoverflow.com/questions/46176165/ways-to-get-string-literal-type-of-array-values-without-enum-overhead\nexport const tuple = function () {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return args;\n};\nexport const tupleNum = function () {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  return args;\n};\nexport const withInstall = comp => {\n  const c = comp;\n  c.install = function (app) {\n    app.component(c.displayName || c.name, comp);\n  };\n  return comp;\n};\nexport function eventType() {\n  return {\n    type: [Function, Array]\n  };\n}\nexport function objectType(defaultVal) {\n  return {\n    type: Object,\n    default: defaultVal\n  };\n}\nexport function booleanType(defaultVal) {\n  return {\n    type: Boolean,\n    default: defaultVal\n  };\n}\nexport function functionType(defaultVal) {\n  return {\n    type: Function,\n    default: defaultVal\n  };\n}\nexport function anyType(defaultVal, required) {\n  const type = {\n    validator: () => true,\n    default: defaultVal\n  };\n  return required ? type : type;\n}\nexport function vNodeType() {\n  return {\n    validator: () => true\n  };\n}\nexport function arrayType(defaultVal) {\n  return {\n    type: Array,\n    default: defaultVal\n  };\n}\nexport function stringType(defaultVal) {\n  return {\n    type: String,\n    default: defaultVal\n  };\n}\nexport function someType(types, defaultVal) {\n  return types ? {\n    type: types,\n    default: defaultVal\n  } : anyType(defaultVal);\n}"], "mappings": ";AACO,IAAM,QAAQ,WAAY;AAC/B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AACA,SAAO;AACT;AACO,IAAM,WAAW,WAAY;AAClC,WAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,SAAK,KAAK,IAAI,UAAU,KAAK;AAAA,EAC/B;AACA,SAAO;AACT;AACO,IAAM,cAAc,UAAQ;AACjC,QAAM,IAAI;AACV,IAAE,UAAU,SAAU,KAAK;AACzB,QAAI,UAAU,EAAE,eAAe,EAAE,MAAM,IAAI;AAAA,EAC7C;AACA,SAAO;AACT;AACO,SAAS,YAAY;AAC1B,SAAO;AAAA,IACL,MAAM,CAAC,UAAU,KAAK;AAAA,EACxB;AACF;AACO,SAAS,WAAW,YAAY;AACrC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACO,SAAS,YAAY,YAAY;AACtC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACO,SAAS,aAAa,YAAY;AACvC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACO,SAAS,QAAQ,YAAY,UAAU;AAC5C,QAAM,OAAO;AAAA,IACX,WAAW,MAAM;AAAA,IACjB,SAAS;AAAA,EACX;AACA,SAAO,WAAW,OAAO;AAC3B;AACO,SAAS,YAAY;AAC1B,SAAO;AAAA,IACL,WAAW,MAAM;AAAA,EACnB;AACF;AACO,SAAS,UAAU,YAAY;AACpC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACO,SAAS,WAAW,YAAY;AACrC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACO,SAAS,SAAS,OAAO,YAAY;AAC1C,SAAO,QAAQ;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX,IAAI,QAAQ,UAAU;AACxB;", "names": []}