import{Tinymce as i}from"./index-CDHRR1Za.js";import{_ as r}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as m,p as n,h as p,o as c,w as u,j as o,a as d,b as s}from"../jse/index-index-C-MnMZEz.js";import f from"./index-D-hwdOI6.js";import"./bootstrap-DCMzVRvD.js";import"./index-Ollxi7Rl.js";const _={class:"flex flex-col gap-[16px]"},x={class:"flex items-center gap-[16px]"},B=m({__name:"index",setup(v){const a=n(!1),l=n("");return(h,e)=>(c(),p(s(r),{title:"Tinymce富文本"},{default:u(()=>[o("div",_,[o("div",x,[e[2]||(e[2]=o("span",null,"禁用",-1)),d(s(f),{checked:a.value,"onUpdate:checked":e[0]||(e[0]=t=>a.value=t)},null,8,["checked"])]),d(s(i),{modelValue:l.value,"onUpdate:modelValue":e[1]||(e[1]=t=>l.value=t),height:800,disabled:a.value},null,8,["modelValue","disabled"])])]),_:1}))}});export{B as default};
