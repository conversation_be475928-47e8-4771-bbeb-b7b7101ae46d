var ke=Object.defineProperty,Ee=Object.defineProperties;var _e=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var Oe=Object.prototype.hasOwnProperty,Me=Object.prototype.propertyIsEnumerable;var z=(e,n,t)=>n in e?ke(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,P=(e,n)=>{for(var t in n||(n={}))Oe.call(n,t)&&z(e,t,n[t]);if(j)for(var t of j(n))Me.call(n,t)&&z(e,t,n[t]);return e},N=(e,n)=>Ee(e,_e(n));var B=(e,n,t)=>new Promise((r,o)=>{var s=u=>{try{d(t.next(u))}catch(a){o(a)}},l=u=>{try{d(t.throw(u))}catch(a){o(a)}},d=u=>u.done?r(u.value):Promise.resolve(u.value).then(s,l);d((t=t.apply(e,n)).next())});import{q as A,d as ee,aA as Ue,p as L,v as $e,ac as we,R as Le,az as Ve,M as T,I as V,m as H,u as De,C as Ie,B as R,a0 as je,as as ze,c as Pe,o as Z,a as Ne,w as Be,h as Te,f as He,b as W,O as Ze,al as We}from"../jse/index-index-C-MnMZEz.js";import{eZ as Ke,e_ as _,cp as Fe,u as qe}from"./bootstrap-DCMzVRvD.js";import{S as Ge}from"./index-Ollxi7Rl.js";function Je(e,n,t){var r=e.length;return t=t===void 0?r:t,!n&&t>=r?e:Ke(e,n,t)}var Ye="\\ud800-\\udfff",Qe="\\u0300-\\u036f",Xe="\\ufe20-\\ufe2f",en="\\u20d0-\\u20ff",nn=Qe+Xe+en,tn="\\ufe0e\\ufe0f",rn="\\u200d",on=RegExp("["+rn+Ye+nn+tn+"]");function ne(e){return on.test(e)}function an(e){return e.split("")}var te="\\ud800-\\udfff",un="\\u0300-\\u036f",sn="\\ufe20-\\ufe2f",ln="\\u20d0-\\u20ff",dn=un+sn+ln,cn="\\ufe0e\\ufe0f",fn="["+te+"]",D="["+dn+"]",I="\\ud83c[\\udffb-\\udfff]",mn="(?:"+D+"|"+I+")",re="[^"+te+"]",oe="(?:\\ud83c[\\udde6-\\uddff]){2}",ae="[\\ud800-\\udbff][\\udc00-\\udfff]",vn="\\u200d",ue=mn+"?",ie="["+cn+"]?",gn="(?:"+vn+"(?:"+[re,oe,ae].join("|")+")"+ie+ue+")*",pn=ie+ue+gn,bn="(?:"+[re+D+"?",D,oe,ae,fn].join("|")+")",xn=RegExp(I+"(?="+I+")|"+bn+pn,"g");function yn(e){return e.match(xn)||[]}function hn(e){return ne(e)?yn(e):an(e)}function Cn(e){return function(n){n=_(n);var t=ne(n)?hn(n):void 0,r=t?t[0]:n.charAt(0),o=t?Je(t,1).join(""):n.slice(1);return r[e]()+o}}var Sn=Cn("toUpperCase");function Rn(e){return Sn(_(e).toLowerCase())}function An(e,n,t,r){for(var o=-1,s=e==null?0:e.length;++o<s;)t=n(t,e[o],o,e);return t}function kn(e){return function(n){return e==null?void 0:e[n]}}var En={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},_n=kn(En),On=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Mn="\\u0300-\\u036f",Un="\\ufe20-\\ufe2f",$n="\\u20d0-\\u20ff",wn=Mn+Un+$n,Ln="["+wn+"]",Vn=RegExp(Ln,"g");function Dn(e){return e=_(e),e&&e.replace(On,_n).replace(Vn,"")}var In=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function jn(e){return e.match(In)||[]}var zn=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function Pn(e){return zn.test(e)}var se="\\ud800-\\udfff",Nn="\\u0300-\\u036f",Bn="\\ufe20-\\ufe2f",Tn="\\u20d0-\\u20ff",Hn=Nn+Bn+Tn,le="\\u2700-\\u27bf",de="a-z\\xdf-\\xf6\\xf8-\\xff",Zn="\\xac\\xb1\\xd7\\xf7",Wn="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Kn="\\u2000-\\u206f",Fn=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ce="A-Z\\xc0-\\xd6\\xd8-\\xde",qn="\\ufe0e\\ufe0f",fe=Zn+Wn+Kn+Fn,me="['’]",K="["+fe+"]",Gn="["+Hn+"]",ve="\\d+",Jn="["+le+"]",ge="["+de+"]",pe="[^"+se+fe+ve+le+de+ce+"]",Yn="\\ud83c[\\udffb-\\udfff]",Qn="(?:"+Gn+"|"+Yn+")",Xn="[^"+se+"]",be="(?:\\ud83c[\\udde6-\\uddff]){2}",xe="[\\ud800-\\udbff][\\udc00-\\udfff]",C="["+ce+"]",et="\\u200d",F="(?:"+ge+"|"+pe+")",nt="(?:"+C+"|"+pe+")",q="(?:"+me+"(?:d|ll|m|re|s|t|ve))?",G="(?:"+me+"(?:D|LL|M|RE|S|T|VE))?",ye=Qn+"?",he="["+qn+"]?",tt="(?:"+et+"(?:"+[Xn,be,xe].join("|")+")"+he+ye+")*",rt="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",ot="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",at=he+ye+tt,ut="(?:"+[Jn,be,xe].join("|")+")"+at,it=RegExp([C+"?"+ge+"+"+q+"(?="+[K,C,"$"].join("|")+")",nt+"+"+G+"(?="+[K,C+F,"$"].join("|")+")",C+"?"+F+"+"+q,C+"+"+G,ot,rt,ve,ut].join("|"),"g");function st(e){return e.match(it)||[]}function lt(e,n,t){return e=_(e),n=n,n===void 0?Pn(e)?st(e):jn(e):e.match(n)||[]}var dt="['’]",ct=RegExp(dt,"g");function ft(e){return function(n){return An(lt(Dn(n).replace(ct,"")),e,"")}}var mt=ft(function(e,n,t){return n=n.toLowerCase(),e+(t?Rn(n):n)}),vt=["onActivate","onAddUndo","onBeforeAddUndo","onBeforeExecCommand","onBeforeGetContent","onBeforeRenderUI","onBeforeSetContent","onBeforePaste","onBlur","onChange","onClearUndos","onClick","onContextMenu","onCommentChange","onCompositionEnd","onCompositionStart","onCompositionUpdate","onCopy","onCut","onDblclick","onDeactivate","onDirty","onDrag","onDragDrop","onDragEnd","onDragGesture","onDragOver","onDrop","onExecCommand","onFocus","onFocusIn","onFocusOut","onGetContent","onHide","onInit","onInput","onKeyDown","onKeyPress","onKeyUp","onLoadContent","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onNodeChange","onObjectResizeStart","onObjectResized","onObjectSelected","onPaste","onPostProcess","onPostRender","onPreProcess","onProgressState","onRedo","onRemove","onReset","onSaveContent","onSelectionChange","onSetAttrib","onSetContent","onShow","onSubmit","onUndo","onVisualAid"],gt=function(e){return vt.map(function(n){return n.toLowerCase()}).indexOf(e.toLowerCase())!==-1},pt=function(e,n,t){Object.keys(n).filter(gt).forEach(function(r){var o=n[r];typeof o=="function"&&(r==="onInit"?o(e,t):t.on(r.substring(2),function(s){return o(s,t)}))})},bt=function(e,n,t,r){var o=e.modelEvents?e.modelEvents:null,s=Array.isArray(o)?o.join(" "):o;A(r,function(l,d){t&&typeof l=="string"&&l!==d&&l!==t.getContent({format:e.outputFormat})&&t.setContent(l)}),t.on(s||"change input undo redo",function(){n.emit("update:modelValue",t.getContent({format:e.outputFormat}))})},xt=function(e,n,t,r,o,s){r.setContent(s()),t.attrs["onUpdate:modelValue"]&&bt(n,t,r,o),pt(e,t.attrs,r)},J=0,Ce=function(e){var n=Date.now(),t=Math.floor(Math.random()*1e9);return J++,e+"_"+t+J+String(n)},yt=function(e){return e!==null&&e.tagName.toLowerCase()==="textarea"},Y=function(e){return typeof e=="undefined"||e===""?[]:Array.isArray(e)?e:e.split(" ")},ht=function(e,n){return Y(e).concat(Y(n))},Ct=function(e){return e==null},Q=function(e){var n;return typeof((n=e.options)===null||n===void 0?void 0:n.set)=="function"&&e.options.isRegistered("disabled")},X=function(){return{listeners:[],scriptId:Ce("tiny-script"),scriptLoaded:!1}},St=function(){var e=X(),n=function(o,s,l,d){var u=s.createElement("script");u.referrerPolicy="origin",u.type="application/javascript",u.id=o,u.src=l;var a=function(){u.removeEventListener("load",a),d()};u.addEventListener("load",a),s.head&&s.head.appendChild(u)},t=function(o,s,l){e.scriptLoaded?l():(e.listeners.push(l),o.getElementById(e.scriptId)||n(e.scriptId,o,s,function(){e.listeners.forEach(function(d){return d()}),e.scriptLoaded=!0}))},r=function(){e=X()};return{load:t,reinitialize:r}},Rt=St(),At=function(){return typeof window!="undefined"?window:global},y=function(){var e=At();return e&&e.tinymce?e.tinymce:null},kt={apiKey:String,licenseKey:String,cloudChannel:String,id:String,init:Object,initialValue:String,inline:Boolean,modelEvents:[String,Array],plugins:[String,Array],tagName:String,toolbar:[String,Array],modelValue:String,disabled:Boolean,readonly:Boolean,tinymceScriptSrc:String,outputFormat:{type:String,validator:function(e){return e==="html"||e==="text"}}},g=function(){return g=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},g.apply(this,arguments)},Et=function(e,n,t,r){return e(r||"div",{id:n,ref:t})},_t=function(e,n,t){return e("textarea",{id:n,visibility:"hidden",ref:t})},$={selector:void 0,target:void 0},w=function(e,n){var t;typeof((t=e.mode)===null||t===void 0?void 0:t.set)=="function"?e.mode.set(n):e.setMode(n)},Ot=ee({props:kt,setup:function(e,n){var t=e.init?g(g({},e.init),$):g({},$),r=Ue(e),o=r.disabled,s=r.readonly,l=r.modelValue,d=r.tagName,u=L(null),a=null,k=e.id||Ce("tiny-vue"),S=e.init&&e.init.inline||e.inline,p=!!n.attrs["onUpdate:modelValue"],h=!0,O=e.initialValue?e.initialValue:"",b="",M=function(i){return p?function(){return l!=null&&l.value?l.value:""}:function(){return i?O:b}},c=function(){var i=M(h),f=g(g({},t),{disabled:e.disabled,readonly:e.readonly,target:u.value,plugins:ht(t.plugins,e.plugins),toolbar:e.toolbar||t.toolbar,inline:S,license_key:e.licenseKey,setup:function(m){a=m,!Q(a)&&e.disabled===!0&&w(a,"readonly"),m.on("init",function(U){return xt(U,e,n,m,l,i)}),typeof t.setup=="function"&&t.setup(m)}});yt(u.value)&&(u.value.style.visibility=""),y().init(f),h=!1};A(s,function(i){a!==null&&w(a,i?"readonly":"design")}),A(o,function(i){a!==null&&(Q(a)?a.options.set("disabled",i):w(a,i?"readonly":"design"))}),A(d,function(i){var f;a&&(p||(b=a.getContent()),(f=y())===null||f===void 0||f.remove(a),V(function(){return c()}))}),$e(function(){if(y()!==null)c();else if(u.value&&u.value.ownerDocument){var i=e.cloudChannel?e.cloudChannel:"7",f=e.apiKey?e.apiKey:"no-api-key",m=Ct(e.tinymceScriptSrc)?"https://cdn.tiny.cloud/1/".concat(f,"/tinymce/").concat(i,"/tinymce.min.js"):e.tinymceScriptSrc;Rt.load(u.value.ownerDocument,m,c)}}),we(function(){y()!==null&&y().remove(a)}),S||(Le(function(){h||c()}),Ve(function(){var i;a&&(p||(b=a.getContent()),(i=y())===null||i===void 0||i.remove(a))}));var v=function(i){var f;a&&(b=a.getContent(),(f=y())===null||f===void 0||f.remove(a),t=g(g(g({},t),i),$),V(function(){return c()}))};return n.expose({rerender:v,getEditor:function(){return a}}),function(){return S?Et(T,k,u,e.tagName):_t(T,k,u)}}});const Mt="preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap emoticons accordion",Ut="undo redo | accordion accordionremove | blocks fontfamily fontsize | bold italic underline strikethrough | align numlist bullist | link image | table media | lineheight outdent indent| forecolor backcolor removeformat | charmap emoticons | code fullscreen preview | save print | pagebreak anchor codesample | ltr rtl",$t={class:"app-tinymce"},jt=ee({name:"Tinymce",inheritAttrs:!1,__name:"editor",props:H({height:{default:400},options:{default:()=>({})},plugins:{default:Mt},toolbar:{default:Ut},disabled:{type:Boolean,default:!1}},{modelValue:{default:""},modelModifiers:{}}),emits:H(["mounted"],["update:modelValue"]),setup(e,{emit:n}){const t=e,r=n,o="/tinymce/tinymce.min.js",s=De(e,"modelValue"),l=Ie(null),{isDark:d,locale:u}=Fe(),a=R(()=>d.value?"oxide-dark":"oxide"),k=R(()=>d.value?"dark":"default"),S=R(()=>je.app.locale.replace("-","_").includes("en_US")?"en":"zh_CN"),p=L(!0);A([d,u],()=>B(null,null,function*(){l.value&&(l.value.destroy(),p.value=!1,yield V(),p.value=!0)}));const h=L(!0),O=R(()=>{const{height:c,options:v,plugins:i,toolbar:f}=t;return N(P({auto_focus:!0,branding:!1,content_css:k.value,content_style:"body { font-family:Helvetica,Arial,sans-serif; font-size:16px }",contextmenu:"link image table",default_link_target:"_blank",height:c,image_advtab:!0,image_caption:!0,importcss_append:!0,language:S.value,link_title:!1,menubar:"file edit view insert format tools table help",noneditable_class:"mceNonEditable",paste_data_images:!0,images_file_types:"jpeg,jpg,png,gif,bmp,webp",plugins:i,quickbars_selection_toolbar:"bold italic | quicklink h2 h3 blockquote quickimage quicktable",skin:a.value,toolbar:f,toolbar_mode:"sliding"},v),{images_upload_handler:(m,U)=>new Promise((Se,Re)=>{const Ae=m.blob();qe(Ae,{onUploadProgress:x=>{const E=Math.trunc(x.loaded/x.total*100);U(E)}}).then(x=>{const{url:E}=x;console.log("tinymce上传图片:",E),Se(E)}).catch(x=>{console.error("tinymce上传图片失败:",x),Re({message:x.message,remove:!0})})}),setup:m=>{l.value=m,m.on("init",()=>{r("mounted"),h.value=!1})}})}),b=ze(),M=R(()=>{const c={};for(const v in b)if(v.startsWith("on")){const i=mt(v.split("on")[1]);c[i]=b[v]}return c});return(c,v)=>(Z(),Pe("div",$t,[Ne(W(Ge),{spinning:h.value},{default:Be(()=>[p.value?(Z(),Te(W(Ot),Ze({key:0,modelValue:s.value,"onUpdate:modelValue":v[0]||(v[0]=i=>s.value=i),init:O.value,"tinymce-script-src":o,disabled:c.disabled,"license-key":"gpl"},We(M.value)),null,16,["modelValue","init","disabled"])):He("",!0)]),_:1},8,["spinning"])]))}});export{jt as Tinymce};
