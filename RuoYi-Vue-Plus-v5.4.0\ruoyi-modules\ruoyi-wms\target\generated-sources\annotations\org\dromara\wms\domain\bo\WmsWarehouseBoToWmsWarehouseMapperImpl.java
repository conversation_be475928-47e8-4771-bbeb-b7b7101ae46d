package org.dromara.wms.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.wms.domain.WmsWarehouse;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-15T15:39:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class WmsWarehouseBoToWmsWarehouseMapperImpl implements WmsWarehouseBoToWmsWarehouseMapper {

    @Override
    public WmsWarehouse convert(WmsWarehouseBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WmsWarehouse wmsWarehouse = new WmsWarehouse();

        wmsWarehouse.setSearchValue( arg0.getSearchValue() );
        wmsWarehouse.setCreateDept( arg0.getCreateDept() );
        wmsWarehouse.setCreateBy( arg0.getCreateBy() );
        wmsWarehouse.setCreateTime( arg0.getCreateTime() );
        wmsWarehouse.setUpdateBy( arg0.getUpdateBy() );
        wmsWarehouse.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            wmsWarehouse.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        wmsWarehouse.setWarehouseId( arg0.getWarehouseId() );
        wmsWarehouse.setWarehouseNumber( arg0.getWarehouseNumber() );
        wmsWarehouse.setWarehouseName( arg0.getWarehouseName() );
        wmsWarehouse.setWarehouseType( arg0.getWarehouseType() );
        wmsWarehouse.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wmsWarehouse.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wmsWarehouse.setRemark( arg0.getRemark() );

        return wmsWarehouse;
    }

    @Override
    public WmsWarehouse convert(WmsWarehouseBo arg0, WmsWarehouse arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseType( arg0.getWarehouseType() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
