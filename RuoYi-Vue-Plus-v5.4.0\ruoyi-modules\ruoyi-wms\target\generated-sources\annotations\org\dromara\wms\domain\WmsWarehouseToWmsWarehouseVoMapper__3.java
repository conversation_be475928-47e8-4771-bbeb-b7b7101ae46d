package org.dromara.wms.domain;

import io.github.linpeilie.AutoMapperConfig__1039;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.bo.WmsWarehouseBoToWmsWarehouseMapper__5;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.vo.WmsWarehouseVoToWmsWarehouseMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1039.class,
    uses = {WmsWarehouseBoToWmsWarehouseMapper__5.class,WmsWarehouseVoToWmsWarehouseMapper__3.class},
    imports = {}
)
public interface WmsWarehouseToWmsWarehouseVoMapper__3 extends BaseMapper<WmsWarehouse, WmsWarehouseVo> {
}
