package org.dromara.resource.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.SysOss;
import org.dromara.resource.domain.SysOssToSysOssVoMapper__10;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssToSysOssVoMapper__10.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__10 extends BaseMapper<SysOssVo, SysOss> {
}
