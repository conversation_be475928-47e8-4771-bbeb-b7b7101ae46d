import{c5 as Q,j as Z,m as ee,_ as x,r as te,h as re,p as T,aL as E,g as P,c6 as ae,aE as ne,aY as oe}from"./bootstrap-DCMzVRvD.js";import{C,v as F,y as se,a5 as ie,a4 as le,d as H,B as I,q as M,I as j,a as m,D as ce}from"../jse/index-index-C-MnMZEz.js";import{R as ue}from"./index-CHpIOV4R.js";import{e as de}from"./eagerComputed-CeBU4kWY.js";import{P as pe}from"./index-qvRUEWLR.js";function ge(){const e=C({});let n=null;const a=Q();return F(()=>{n=a.value.subscribe(t=>{e.value=t})}),se(()=>{a.value.unsubscribe(n)}),e}const ve=e=>{const{antCls:n,componentCls:a,iconCls:t,avatarBg:o,avatarColor:S,containerSize:i,containerSizeLG:c,containerSizeSM:f,textFontSize:p,textFontSizeLG:u,textFontSizeSM:k,borderRadius:$,borderRadiusLG:s,borderRadiusSM:z,lineWidth:g,lineType:h}=e,d=(v,r,l)=>({width:v,height:v,lineHeight:`${v-g*2}px`,borderRadius:"50%",[`&${a}-square`]:{borderRadius:l},[`${a}-string`]:{position:"absolute",left:{_skip_check_:!0,value:"50%"},transformOrigin:"0 center"},[`&${a}-icon`]:{fontSize:r,[`> ${t}`]:{margin:0}}});return{[a]:x(x(x(x({},te(e)),{position:"relative",display:"inline-block",overflow:"hidden",color:S,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:o,border:`${g}px ${h} transparent`,"&-image":{background:"transparent"},[`${n}-image-img`]:{display:"block"}}),d(i,p,$)),{"&-lg":x({},d(c,u,s)),"&-sm":x({},d(f,k,z)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},fe=e=>{const{componentCls:n,groupBorderColor:a,groupOverlapping:t,groupSpace:o}=e;return{[`${n}-group`]:{display:"inline-flex",[`${n}`]:{borderColor:a},"> *:not(:first-child)":{marginInlineStart:t}},[`${n}-group-popover`]:{[`${n} + ${n}`]:{marginInlineStart:o}}}},_=Z("Avatar",e=>{const{colorTextLightSolid:n,colorTextPlaceholder:a}=e,t=ee(e,{avatarBg:a,avatarColor:n});return[ve(t),fe(t)]},e=>{const{controlHeight:n,controlHeightLG:a,controlHeightSM:t,fontSize:o,fontSizeLG:S,fontSizeXL:i,fontSizeHeading3:c,marginXS:f,marginXXS:p,colorBorderBg:u}=e;return{containerSize:n,containerSizeLG:a,containerSizeSM:t,textFontSize:Math.round((S+i)/2),textFontSizeLG:c,textFontSizeSM:o,groupSpace:p,groupOverlapping:-f,groupBorderColor:u}}),N=Symbol("AvatarContextKey"),me=()=>ie(N,{}),Se=e=>le(N,e),he=()=>({prefixCls:String,shape:{type:String,default:"circle"},size:{type:[Number,String,Object],default:()=>"default"},src:String,srcset:String,icon:re.any,alt:String,gap:Number,draggable:{type:Boolean,default:void 0},crossOrigin:String,loadError:{type:Function}}),R=H({compatConfig:{MODE:3},name:"AAvatar",inheritAttrs:!1,props:he(),slots:Object,setup(e,n){let{slots:a,attrs:t}=n;const o=C(!0),S=C(!1),i=C(1),c=C(null),f=C(null),{prefixCls:p}=T("avatar",e),[u,k]=_(p),$=me(),s=I(()=>e.size==="default"?$.size:e.size),z=ge(),g=de(()=>{if(typeof e.size!="object")return;const r=ae.find(b=>z.value[b]);return e.size[r]}),h=r=>g.value?{width:`${g.value}px`,height:`${g.value}px`,lineHeight:`${g.value}px`,fontSize:`${r?g.value/2:18}px`}:{},d=()=>{if(!c.value||!f.value)return;const r=c.value.offsetWidth,l=f.value.offsetWidth;if(r!==0&&l!==0){const{gap:b=4}=e;b*2<l&&(i.value=l-b*2<r?(l-b*2)/r:1)}},v=()=>{const{loadError:r}=e;(r==null?void 0:r())!==!1&&(o.value=!1)};return M(()=>e.src,()=>{j(()=>{o.value=!0,i.value=1})}),M(()=>e.gap,()=>{j(()=>{d()})}),F(()=>{j(()=>{d(),S.value=!0})}),()=>{var r,l;const{shape:b,src:w,alt:W,srcset:X,draggable:D,crossOrigin:q}=e,K=(r=$.shape)!==null&&r!==void 0?r:b,A=E(a,e,"icon"),y=p.value,U={[`${t.class}`]:!!t.class,[y]:!0,[`${y}-lg`]:s.value==="large",[`${y}-sm`]:s.value==="small",[`${y}-${K}`]:!0,[`${y}-image`]:w&&o.value,[`${y}-icon`]:A,[k.value]:!0},V=typeof s.value=="number"?{width:`${s.value}px`,height:`${s.value}px`,lineHeight:`${s.value}px`,fontSize:A?`${s.value/2}px`:"18px"}:{},L=(l=a.default)===null||l===void 0?void 0:l.call(a);let O;if(w&&o.value)O=m("img",{draggable:D,src:w,srcset:X,onError:v,alt:W,crossorigin:q},null);else if(A)O=A;else if(S.value||i.value!==1){const G=`scale(${i.value}) translateX(-50%)`,Y={msTransform:G,WebkitTransform:G,transform:G},J=typeof s.value=="number"?{lineHeight:`${s.value}px`}:{};O=m(ue,{onResize:d},{default:()=>[m("span",{class:`${y}-string`,ref:c,style:x(x({},J),Y)},[L])]})}else O=m("span",{class:`${y}-string`,ref:c,style:{opacity:0}},[L]);return u(m("span",P(P({},t),{},{ref:f,class:U,style:[V,h(!!A),t.style]}),[O]))}}}),ye=()=>({prefixCls:String,maxCount:Number,maxStyle:{type:Object,default:void 0},maxPopoverPlacement:{type:String,default:"top"},maxPopoverTrigger:String,size:{type:[Number,String,Object],default:"default"},shape:{type:String,default:"circle"}}),B=H({compatConfig:{MODE:3},name:"AAvatarGroup",inheritAttrs:!1,props:ye(),setup(e,n){let{slots:a,attrs:t}=n;const{prefixCls:o,direction:S}=T("avatar",e),i=I(()=>`${o.value}-group`),[c,f]=_(o);return ce(()=>{const p={size:e.size,shape:e.shape};Se(p)}),()=>{const{maxPopoverPlacement:p="top",maxCount:u,maxStyle:k,maxPopoverTrigger:$="hover",shape:s}=e,z={[i.value]:!0,[`${i.value}-rtl`]:S.value==="rtl",[`${t.class}`]:!!t.class,[f.value]:!0},g=E(a,e),h=ne(g).map((v,r)=>oe(v,{key:`avatar-key-${r}`})),d=h.length;if(u&&u<d){const v=h.slice(0,u),r=h.slice(u,d);return v.push(m(pe,{key:"avatar-popover-key",content:r,trigger:$,placement:p,overlayClassName:`${i.value}-popover`},{default:()=>[m(R,{style:k,shape:s},{default:()=>[`+${d-u}`]})]})),c(m("div",P(P({},t),{},{class:z,style:t.style}),[v]))}return c(m("div",P(P({},t),{},{class:z,style:t.style}),[h]))}}});R.Group=B;R.install=function(e){return e.component(R.name,R),e.component(B.name,B),e};export{R as A,B as G,ge as u};
