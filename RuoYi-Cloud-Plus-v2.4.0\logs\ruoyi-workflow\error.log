2025-06-17 15:12:39 [Dubbo-framework-shared-scheduler-thread-8] ERROR o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Refresh instance and metadata error., dubbo version: 3.3.4, current host: *************, error code: 5-12. This may be caused by , go to https://dubbo.apache.org/faq/5/12 to find instructions. 
java.lang.RuntimeException: Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableConsumer.execute(ThrowableConsumer.java:51)
	at org.apache.dubbo.common.function.ThrowableConsumer.execute(ThrowableConsumer.java:63)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.doUnregister(NacosServiceDiscovery.java:103)
	at org.apache.dubbo.registry.client.AbstractServiceDiscovery.doUpdate(AbstractServiceDiscovery.java:350)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.doUpdate(NacosServiceDiscovery.java:115)
	at org.apache.dubbo.registry.client.AbstractServiceDiscovery.update(AbstractServiceDiscovery.java:204)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.apache.dubbo.registry.client.metadata.ServiceInstanceMetadataUtils.refreshMetadataAndInstance(ServiceInstanceMetadataUtils.java:239)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.lambda$registerServiceInstance$5(DefaultApplicationDeployer.java:1033)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:08 [dubbo-client-heartbeat-reconnect-thread-1] ERROR o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Failed to connect to provider server by other reason., dubbo version: 3.3.4, current host: *************, error code: 6-1. This may be caused by network disconnected, go to https://dubbo.apache.org/faq/6/1 to find instructions. 
io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /*************:20880
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:08 [dubbo-client-heartbeat-reconnect-thread-1] ERROR o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Fail to connect toHeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/*************:64084 -> /*************:20880]], dubbo version: 3.3.4, current host: *************, error code: 6-16. This may be caused by , go to https://dubbo.apache.org/faq/6/16 to find instructions. 
org.apache.dubbo.remoting.RemotingException: client(url: dubbo://*************:20880/org.dromara.system.api.RemoteDeptService?REGISTRY_CLUSTER=default:dev&application=ruoyi-workflow&background=false&cache=false&check=false&codec=dubbo&deprecated=false&dubbo=2.0.2&dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}]&dubbo.metadata.revision=7092d8208bd8d2ca5be86b5c320c6886&dubbo.metadata.storage-type=remote&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&heartbeat=60000&interface=org.dromara.system.api.RemoteDeptService&logger=slf4j&meta-v=1.0.0&metadata-type=remote&methods=selectDeptLeaderById,selectDeptNameByIds,selectDeptsByList&pid=34800&prefer.serialization=hessian2,fastjson2&qos.enable=false&register-mode=instance&register.ip=*************&release=3.3.4&retries=0&service-name-mapping=true&side=consumer&sticky=false&timeout=3000&timestamp=1750119339228&unloadClusterRelated=false&validation=jvalidationNew) failed to connect to server /*************:20880, error message is:Connection refused: no further information: /*************:20880
	at org.apache.dubbo.remoting.transport.netty4.NettyClient.doConnect(NettyClient.java:231)
	at org.apache.dubbo.remoting.transport.netty4.NettyClient.doConnect(NettyClient.java:175)
	at org.apache.dubbo.remoting.transport.AbstractClient.connect(AbstractClient.java:266)
	at org.apache.dubbo.remoting.transport.AbstractClient.reconnect(AbstractClient.java:347)
	at org.apache.dubbo.remoting.exchange.support.header.HeaderExchangeClient.reconnect(HeaderExchangeClient.java:189)
	at org.apache.dubbo.remoting.exchange.support.header.ReconnectTimerTask.doTask(ReconnectTimerTask.java:56)
	at org.apache.dubbo.remoting.exchange.support.header.AbstractTimerTask.run(AbstractTimerTask.java:93)
	at org.apache.dubbo.common.timer.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:654)
	at org.apache.dubbo.common.timer.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:733)
	at org.apache.dubbo.common.timer.HashedWheelTimer$Worker.run(HashedWheelTimer.java:455)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /*************:20880
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:36 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-workflow', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=9205, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
