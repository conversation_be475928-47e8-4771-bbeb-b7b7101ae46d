import{C as o}from"./helper-Bc7QQ92Q.js";import{y as t}from"./bootstrap-DCMzVRvD.js";function a(e){return t.get("/tool/gen/list",{params:e})}function g(e){return t.get(`/tool/gen/${e}`)}function i(e){return t.get("/tool/gen/db/list",{params:e})}function u(e,n){return t.postWithMsg("/tool/gen/importTable",{dataName:n,tables:e},{headers:{"Content-Type":o.FORM_URLENCODED}})}function c(e){return t.putWithMsg("/tool/gen",e)}function l(e){return t.deleteWithMsg(`/tool/gen/${e}`)}function f(e){return t.get(`/tool/gen/preview/${e}`)}function p(e){return t.get(`/tool/gen/genCode/${e}`)}function m(e){return t.get(`/tool/gen/synchDb/${e}`,{successMessageMode:"message"})}function d(e){return t.get("/tool/gen/batchGenCode",{isTransformResponse:!1,params:{tableIdStr:e},responseType:"blob"})}function b(){return t.get("/tool/gen/getDataNames")}export{g as a,a as b,d as c,p as d,c as e,l as f,b as g,u as i,f as p,i as r,m as s};
