2025-06-13 08:09:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 08:09:35 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 26740 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 08:09:35 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-13 08:09:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-13 08:09:38 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-13 08:09:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 08:09:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-13 08:09:38 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-13 08:09:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2579 ms
2025-06-13 08:09:40 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-13 08:09:41 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:09:41 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:09:41 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:09:41 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:09:41 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5949eba8
2025-06-13 08:09:42 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-13 08:09:42 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-13 08:09:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-13 08:09:42 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-13 08:09:42 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-13 08:09:43 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-13 08:09:43 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-13 08:09:43 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-13 08:09:43 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-13 08:09:43 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-13 08:09:43 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-13 08:09:43 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 8.67 seconds (process running for 9.383)
2025-06-13 08:09:43 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-13 08:09:43 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 08:09:43 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 08:09:43 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-13 08:09:43 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as f73bf1ba48c1
2025-06-13 08:09:44 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3c4a8f25
2025-06-13 08:09:44 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-13 08:09:44 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1933315797089959936]
2025-06-13 08:09:53 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-13 08:09:53 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-13 08:10:23 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1933315797089959936]
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-13 08:10:23 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-13 08:10:23 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-13 08:10:23 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1933315797089959936]
2025-06-13 08:10:24 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-13 08:10:24 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-13 08:10:24 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-13 08:20:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 08:20:28 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 16376 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 08:20:28 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-13 08:20:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-13 08:20:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-13 08:20:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 08:20:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-13 08:20:30 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-13 08:20:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1269 ms
2025-06-13 08:20:31 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-13 08:20:32 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:20:32 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:20:32 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:20:32 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:20:32 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5167268
2025-06-13 08:20:32 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-13 08:20:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-13 08:20:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-13 08:20:32 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-13 08:20:32 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-13 08:20:32 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-13 08:20:32 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-13 08:20:32 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-13 08:20:32 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-13 08:20:32 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-13 08:20:32 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-13 08:20:32 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.448 seconds (process running for 4.902)
2025-06-13 08:20:32 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-13 08:20:33 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as f73bf1ba48c1
2025-06-13 08:20:33 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 08:20:33 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 08:20:33 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-13 08:20:33 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1668db42
2025-06-13 08:20:33 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-13 08:20:33 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1933318527976120320]
2025-06-13 08:20:42 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-13 08:20:42 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-13 08:20:53 [snail-job-scheduled-thread-2] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1933318613900595200]
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-13 08:22:27 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1933318527976120320]
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-13 08:22:27 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-13 08:22:27 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-13 08:22:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-13 08:22:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 08:22:55 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 24632 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 08:22:55 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-13 08:22:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-13 08:22:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-13 08:22:57 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 08:22:57 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-13 08:22:57 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-13 08:22:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1272 ms
2025-06-13 08:22:58 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-13 08:22:59 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:22:59 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:22:59 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:22:59 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 08:22:59 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5167268
2025-06-13 08:22:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-13 08:22:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-13 08:22:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-13 08:22:59 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-13 08:22:59 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-13 08:22:59 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-13 08:22:59 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-13 08:22:59 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-13 08:22:59 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-13 08:22:59 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-13 08:22:59 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-13 08:22:59 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.508 seconds (process running for 4.979)
2025-06-13 08:23:00 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-13 08:23:00 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as f73bf1ba48c1
2025-06-13 08:23:00 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 08:23:00 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 08:23:00 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 08:23:00 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6eba5d5
2025-06-13 08:23:00 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-13 08:23:00 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1933319145134424064]
2025-06-13 08:23:09 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-13 08:23:09 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-13 08:23:20 [snail-job-scheduled-thread-2] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1933319223488208896]
2025-06-13 08:45:05 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-8] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[248] Task scheduled successfully.
2025-06-13 09:07:50 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-13] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[249] Task scheduled successfully.
2025-06-13 09:07:55 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-17] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[250] Task scheduled successfully.
2025-06-13 09:08:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-21] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[251] Task scheduled successfully.
2025-06-13 09:08:05 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-25] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[252] Task scheduled successfully.
2025-06-13 09:08:10 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-29] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[253] Task scheduled successfully.
2025-06-13 09:08:15 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-33] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[254] Task scheduled successfully.
2025-06-13 09:08:20 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-37] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[255] Task scheduled successfully.
2025-06-13 09:08:25 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-41] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[256] Task scheduled successfully.
2025-06-13 09:08:30 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-45] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[257] Task scheduled successfully.
2025-06-13 09:08:35 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-49] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[258] Task scheduled successfully.
2025-06-13 09:08:40 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-53] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[259] Task scheduled successfully.
2025-06-13 09:08:45 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-57] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[260] Task scheduled successfully.
2025-06-13 09:08:50 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-61] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[261] Task scheduled successfully.
2025-06-13 09:08:55 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-65] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[262] Task scheduled successfully.
2025-06-13 09:09:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-69] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[263] Task scheduled successfully.
2025-06-13 09:09:05 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-73] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[264] Task scheduled successfully.
2025-06-13 09:09:10 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-77] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[265] Task scheduled successfully.
2025-06-13 09:09:15 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-81] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[266] Task scheduled successfully.
2025-06-13 09:09:20 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-85] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[267] Task scheduled successfully.
2025-06-13 09:09:25 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-89] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[268] Task scheduled successfully.
2025-06-13 09:09:30 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-93] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[269] Task scheduled successfully.
2025-06-13 09:09:35 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-97] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[270] Task scheduled successfully.
2025-06-13 09:09:40 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-101] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[271] Task scheduled successfully.
2025-06-13 09:09:45 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-105] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[272] Task scheduled successfully.
2025-06-13 09:09:50 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-109] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[273] Task scheduled successfully.
2025-06-13 09:09:55 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-113] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[274] Task scheduled successfully.
2025-06-13 09:10:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-117] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[275] Task scheduled successfully.
2025-06-13 09:10:05 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-121] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[276] Task scheduled successfully.
2025-06-13 09:10:10 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-125] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[277] Task scheduled successfully.
2025-06-13 09:10:15 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-129] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[278] Task scheduled successfully.
2025-06-13 09:10:20 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-133] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[279] Task scheduled successfully.
2025-06-13 09:10:25 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-137] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[280] Task scheduled successfully.
2025-06-13 09:10:30 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-141] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[281] Task scheduled successfully.
2025-06-13 09:10:35 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-145] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[282] Task scheduled successfully.
2025-06-13 09:10:40 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-149] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[283] Task scheduled successfully.
2025-06-13 09:10:45 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-153] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[284] Task scheduled successfully.
2025-06-13 09:10:50 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-157] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[285] Task scheduled successfully.
2025-06-13 09:11:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-162] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[286] Task scheduled successfully.
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-13 16:36:32 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1933319145134424064]
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-13 16:36:32 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-13 16:36:33 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-13 16:36:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-13 16:36:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-13 16:40:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 16:40:24 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 15140 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 16:40:24 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-13 16:40:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-13 16:40:25 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-13 16:40:25 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 16:40:25 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-13 16:40:25 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-13 16:40:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1436 ms
2025-06-13 16:40:27 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-13 16:40:28 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:40:28 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:40:28 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:40:28 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:40:28 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5167268
2025-06-13 16:40:28 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-13 16:40:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-13 16:40:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-13 16:40:28 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-13 16:40:28 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-13 16:40:28 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-13 16:40:28 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-13 16:40:28 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-13 16:40:28 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-13 16:40:28 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-13 16:40:28 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-13 16:40:28 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.878 seconds (process running for 5.35)
2025-06-13 16:40:29 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-13 16:40:29 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as f73bf1ba48c1
2025-06-13 16:40:29 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 16:40:29 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 16:40:29 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 16:40:29 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e0785f7
2025-06-13 16:40:29 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-13 16:40:29 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1933444340012703744]
2025-06-13 16:40:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-13 16:40:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-13 16:40:49 [snail-job-scheduled-thread-3] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1933444424762793984]
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-13 16:50:01 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1933444340012703744]
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-13 16:50:01 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-13 16:50:01 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-13 16:50:02 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-13 16:50:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-13 16:50:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-13 16:51:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 16:51:17 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 16788 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 16:51:17 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-13 16:51:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-13 16:51:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-13 16:51:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 16:51:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-13 16:51:18 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-13 16:51:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1327 ms
2025-06-13 16:51:20 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-13 16:51:20 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:51:20 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:51:20 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:51:20 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-13 16:51:20 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5167268
2025-06-13 16:51:20 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-13 16:51:21 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-13 16:51:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-13 16:51:21 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-13 16:51:21 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-13 16:51:21 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-13 16:51:21 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-13 16:51:21 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-13 16:51:21 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-13 16:51:21 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-13 16:51:21 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-13 16:51:21 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.645 seconds (process running for 5.138)
2025-06-13 16:51:21 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-13 16:51:21 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as f73bf1ba48c1
2025-06-13 16:51:21 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 16:51:21 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 16:51:21 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 16:51:21 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1034442c
2025-06-13 16:51:21 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-13 16:51:21 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1933447076741574656]
2025-06-13 16:51:31 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-13 16:51:31 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-13 16:51:41 [snail-job-scheduled-thread-1] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1933447162615746560]
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-13 17:30:26 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1933447076741574656]
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-13 17:30:26 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-13 17:30:26 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-13 17:30:28 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-13 17:30:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-13 17:30:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
