import{h as me,cL as nn,_ as O,cQ as Nt,g as ae,c as ge,v as on,d6 as an,aJ as Dt,dO as ln,a_ as rn,ax as sn,e as dn,aM as we,aC as Be,aK as cn,aX as un,m as fn,r as vn,K as yn,k as dt,j as hn,dN as gn}from"./bootstrap-DCMzVRvD.js";import{d as De,a4 as $t,B as $,a5 as Pt,C as H,a as N,s as Tt,z as pn,v as It,x as bn,p as Me,D as Ce,q as Ke,ac as mn,T as Kn,I as Lt,F as kn,y as xn,A as ct,ad as Sn}from"../jse/index-index-C-MnMZEz.js";import{L as wn}from"./List-DFkqSBvs.js";import{e as se}from"./eagerComputed-CeBU4kWY.js";import{g as Cn}from"./index-DabkQ3D7.js";const At=Symbol("TreeContextKey"),On=De({compatConfig:{MODE:3},name:"TreeContext",props:{value:{type:Object}},setup(e,t){let{slots:n}=t;return $t(At,$(()=>e.value)),()=>{var o;return(o=n.default)===null||o===void 0?void 0:o.call(n)}}}),nt=()=>Pt(At,$(()=>({}))),_t=Symbol("KeysStateKey"),En=e=>{$t(_t,e)},Mt=()=>Pt(_t,{expandedKeys:H([]),selectedKeys:H([]),loadedKeys:H([]),loadingKeys:H([]),checkedKeys:H([]),halfCheckedKeys:H([]),expandedKeysSet:$(()=>new Set),selectedKeysSet:$(()=>new Set),loadedKeysSet:$(()=>new Set),loadingKeysSet:$(()=>new Set),checkedKeysSet:$(()=>new Set),halfCheckedKeysSet:$(()=>new Set),flattenNodes:H([])}),Nn=e=>{let{prefixCls:t,level:n,isStart:o,isEnd:a}=e;const s=`${t}-indent-unit`,l=[];for(let v=0;v<n;v+=1)l.push(N("span",{key:v,class:{[s]:!0,[`${s}-start`]:o[v],[`${s}-end`]:a[v]}},null));return N("span",{"aria-hidden":"true",class:`${t}-indent`},[l])},Ft={eventKey:[String,Number],prefixCls:String,title:me.any,data:{type:Object,default:void 0},parent:{type:Object,default:void 0},isStart:{type:Array},isEnd:{type:Array},active:{type:Boolean,default:void 0},onMousemove:{type:Function},isLeaf:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},selectable:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},disableCheckbox:{type:Boolean,default:void 0},icon:me.any,switcherIcon:me.any,domRef:{type:Function}},Dn={prefixCls:{type:String},motion:{type:Object},focusable:{type:Boolean},activeItem:{type:Object},focused:{type:Boolean},tabindex:{type:Number},checkable:{type:Boolean},selectable:{type:Boolean},disabled:{type:Boolean},height:{type:Number},itemHeight:{type:Number},virtual:{type:Boolean},onScroll:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onActiveChange:{type:Function},onContextmenu:{type:Function},onListChangeStart:{type:Function},onListChangeEnd:{type:Function}},$n=()=>({prefixCls:String,focusable:{type:Boolean,default:void 0},activeKey:[Number,String],tabindex:Number,children:me.any,treeData:{type:Array},fieldNames:{type:Object},showLine:{type:[Boolean,Object],default:void 0},showIcon:{type:Boolean,default:void 0},icon:me.any,selectable:{type:Boolean,default:void 0},expandAction:[String,Boolean],disabled:{type:Boolean,default:void 0},multiple:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},checkStrictly:{type:Boolean,default:void 0},draggable:{type:[Function,Boolean]},defaultExpandParent:{type:Boolean,default:void 0},autoExpandParent:{type:Boolean,default:void 0},defaultExpandAll:{type:Boolean,default:void 0},defaultExpandedKeys:{type:Array},expandedKeys:{type:Array},defaultCheckedKeys:{type:Array},checkedKeys:{type:[Object,Array]},defaultSelectedKeys:{type:Array},selectedKeys:{type:Array},allowDrop:{type:Function},dropIndicatorRender:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onKeydown:{type:Function},onContextmenu:{type:Function},onClick:{type:Function},onDblclick:{type:Function},onScroll:{type:Function},onExpand:{type:Function},onCheck:{type:Function},onSelect:{type:Function},onLoad:{type:Function},loadData:{type:Function},loadedKeys:{type:Array},onMouseenter:{type:Function},onMouseleave:{type:Function},onRightClick:{type:Function},onDragstart:{type:Function},onDragenter:{type:Function},onDragover:{type:Function},onDragleave:{type:Function},onDragend:{type:Function},onDrop:{type:Function},onActiveChange:{type:Function},filterTreeNode:{type:Function},motion:me.any,switcherIcon:me.any,height:Number,itemHeight:Number,virtual:{type:Boolean,default:void 0},direction:{type:String},rootClassName:String,rootStyle:Object});var Pn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const ut="open",ft="close",Tn="---",vt=De({compatConfig:{MODE:3},name:"ATreeNode",inheritAttrs:!1,props:Ft,isTreeNode:1,setup(e,t){let{attrs:n,slots:o,expose:a}=t;nn(!("slots"in e.data),`treeData slots is deprecated, please use ${Object.keys(e.data.slots||{}).map(r=>"`v-slot:"+r+"` ")}instead`);const s=H(!1),l=nt(),{expandedKeysSet:v,selectedKeysSet:p,loadedKeysSet:f,loadingKeysSet:y,checkedKeysSet:h,halfCheckedKeysSet:b}=Mt(),{dragOverNodeKey:m,dropPosition:w,keyEntities:g}=l.value,S=$(()=>Fe(e.eventKey,{expandedKeysSet:v.value,selectedKeysSet:p.value,loadedKeysSet:f.value,loadingKeysSet:y.value,checkedKeysSet:h.value,halfCheckedKeysSet:b.value,dragOverNodeKey:m,dropPosition:w,keyEntities:g})),K=se(()=>S.value.expanded),_=se(()=>S.value.selected),A=se(()=>S.value.checked),P=se(()=>S.value.loaded),T=se(()=>S.value.loading),U=se(()=>S.value.halfChecked),W=se(()=>S.value.dragOver),F=se(()=>S.value.dragOverGapTop),R=se(()=>S.value.dragOverGapBottom),X=se(()=>S.value.pos),V=H(),ne=$(()=>{const{eventKey:r}=e,{keyEntities:c}=l.value,{children:M}=c[r]||{};return!!(M||[]).length}),Q=$(()=>{const{isLeaf:r}=e,{loadData:c}=l.value,M=ne.value;return r===!1?!1:r||!c&&!M||c&&P.value&&!M}),ve=$(()=>Q.value?null:K.value?ut:ft),le=$(()=>{const{disabled:r}=e,{disabled:c}=l.value;return!!(c||r)}),xe=$(()=>{const{checkable:r}=e,{checkable:c}=l.value;return!c||r===!1?!1:c}),re=$(()=>{const{selectable:r}=e,{selectable:c}=l.value;return typeof r=="boolean"?r:c}),z=$(()=>{const{data:r,active:c,checkable:M,disableCheckbox:G,disabled:Y,selectable:J}=e;return O(O({active:c,checkable:M,disableCheckbox:G,disabled:Y,selectable:J},r),{dataRef:r,data:r,isLeaf:Q.value,checked:A.value,expanded:K.value,loading:T.value,selected:_.value,halfChecked:U.value})}),de=pn(),q=$(()=>{const{eventKey:r}=e,{keyEntities:c}=l.value,{parent:M}=c[r]||{};return O(O({},je(O({},e,S.value))),{parent:M})}),Z=Tt({eventData:q,eventKey:$(()=>e.eventKey),selectHandle:V,pos:X,key:de.vnode.key});a(Z);const oe=r=>{const{onNodeDoubleClick:c}=l.value;c(r,q.value)},ce=r=>{if(le.value)return;const{onNodeSelect:c}=l.value;r.preventDefault(),c(r,q.value)},pe=r=>{if(le.value)return;const{disableCheckbox:c}=e,{onNodeCheck:M}=l.value;if(!xe.value||c)return;r.preventDefault();const G=!A.value;M(r,q.value,G)},Se=r=>{const{onNodeClick:c}=l.value;c(r,q.value),re.value?ce(r):pe(r)},Oe=r=>{const{onNodeMouseEnter:c}=l.value;c(r,q.value)},He=r=>{const{onNodeMouseLeave:c}=l.value;c(r,q.value)},Re=r=>{const{onNodeContextMenu:c}=l.value;c(r,q.value)},ze=r=>{const{onNodeDragStart:c}=l.value;r.stopPropagation(),s.value=!0,c(r,Z);try{r.dataTransfer.setData("text/plain","")}catch(M){}},Ve=r=>{const{onNodeDragEnter:c}=l.value;r.preventDefault(),r.stopPropagation(),c(r,Z)},qe=r=>{const{onNodeDragOver:c}=l.value;r.preventDefault(),r.stopPropagation(),c(r,Z)},Pe=r=>{const{onNodeDragLeave:c}=l.value;r.stopPropagation(),c(r,Z)},Ge=r=>{const{onNodeDragEnd:c}=l.value;r.stopPropagation(),s.value=!1,c(r,Z)},We=r=>{const{onNodeDrop:c}=l.value;r.preventDefault(),r.stopPropagation(),s.value=!1,c(r,Z)},Te=r=>{const{onNodeExpand:c}=l.value;T.value||c(r,q.value)},Ie=()=>{const{data:r}=e,{draggable:c}=l.value;return!!(c&&(!c.nodeDraggable||c.nodeDraggable(r)))},Le=()=>{const{draggable:r,prefixCls:c}=l.value;return r&&(r!=null&&r.icon)?N("span",{class:`${c}-draggable-icon`},[r.icon]):null},Xe=()=>{var r,c,M;const{switcherIcon:G=o.switcherIcon||((r=l.value.slots)===null||r===void 0?void 0:r[(M=(c=e.data)===null||c===void 0?void 0:c.slots)===null||M===void 0?void 0:M.switcherIcon])}=e,{switcherIcon:Y}=l.value,J=G||Y;return typeof J=="function"?J(z.value):J},Ae=()=>{const{loadData:r,onNodeLoad:c}=l.value;T.value||r&&K.value&&!Q.value&&!ne.value&&!P.value&&c(q.value)};It(()=>{Ae()}),bn(()=>{Ae()});const Ue=()=>{const{prefixCls:r}=l.value,c=Xe();if(Q.value)return c!==!1?N("span",{class:ge(`${r}-switcher`,`${r}-switcher-noop`)},[c]):null;const M=ge(`${r}-switcher`,`${r}-switcher_${K.value?ut:ft}`);return c!==!1?N("span",{onClick:Te,class:M},[c]):null},Ye=()=>{var r,c;const{disableCheckbox:M}=e,{prefixCls:G}=l.value,Y=le.value;return xe.value?N("span",{class:ge(`${G}-checkbox`,A.value&&`${G}-checkbox-checked`,!A.value&&U.value&&`${G}-checkbox-indeterminate`,(Y||M)&&`${G}-checkbox-disabled`),onClick:pe},[(c=(r=l.value).customCheckable)===null||c===void 0?void 0:c.call(r)]):null},_e=()=>{const{prefixCls:r}=l.value;return N("span",{class:ge(`${r}-iconEle`,`${r}-icon__${ve.value||"docu"}`,T.value&&`${r}-icon_loading`)},null)},be=()=>{const{disabled:r,eventKey:c}=e,{draggable:M,dropLevelOffset:G,dropPosition:Y,prefixCls:J,indent:i,dropIndicatorRender:d,dragOverNodeKey:u,direction:x}=l.value;return!r&&M!==!1&&u===c?d({dropPosition:Y,dropLevelOffset:G,indent:i,prefixCls:J,direction:x}):null},Je=()=>{var r,c,M,G,Y,J;const{icon:i=o.icon,data:d}=e,u=o.title||((r=l.value.slots)===null||r===void 0?void 0:r[(M=(c=e.data)===null||c===void 0?void 0:c.slots)===null||M===void 0?void 0:M.title])||((G=l.value.slots)===null||G===void 0?void 0:G.title)||e.title,{prefixCls:x,showIcon:E,icon:C,loadData:k}=l.value,I=le.value,B=`${x}-node-content-wrapper`;let D;if(E){const ee=i||((Y=l.value.slots)===null||Y===void 0?void 0:Y[(J=d==null?void 0:d.slots)===null||J===void 0?void 0:J.icon])||C;D=ee?N("span",{class:ge(`${x}-iconEle`,`${x}-icon__customize`)},[typeof ee=="function"?ee(z.value):ee]):_e()}else k&&T.value&&(D=_e());let L;typeof u=="function"?L=u(z.value):L=u,L=L===void 0?Tn:L;const j=N("span",{class:`${x}-title`},[L]);return N("span",{ref:V,title:typeof u=="string"?u:"",class:ge(`${B}`,`${B}-${ve.value||"normal"}`,!I&&(_.value||s.value)&&`${x}-node-selected`),onMouseenter:Oe,onMouseleave:He,onContextmenu:Re,onClick:Se,onDblclick:oe},[D,j,be()])};return()=>{const r=O(O({},e),n),{eventKey:c,isLeaf:M,isStart:G,isEnd:Y,domRef:J,active:i,data:d,onMousemove:u,selectable:x}=r,E=Pn(r,["eventKey","isLeaf","isStart","isEnd","domRef","active","data","onMousemove","selectable"]),{prefixCls:C,filterTreeNode:k,keyEntities:I,dropContainerKey:B,dropTargetKey:D,draggingNodeKey:L}=l.value,j=le.value,ee=Nt(E,{aria:!0,data:!0}),{level:ue}=I[c]||{},ie=Y[Y.length-1],te=Ie(),ye=!j&&te,Ee=L===c,Qe=x!==void 0?{"aria-selected":!!x}:void 0;return N("div",ae(ae({ref:J,class:ge(n.class,`${C}-treenode`,{[`${C}-treenode-disabled`]:j,[`${C}-treenode-switcher-${K.value?"open":"close"}`]:!M,[`${C}-treenode-checkbox-checked`]:A.value,[`${C}-treenode-checkbox-indeterminate`]:U.value,[`${C}-treenode-selected`]:_.value,[`${C}-treenode-loading`]:T.value,[`${C}-treenode-active`]:i,[`${C}-treenode-leaf-last`]:ie,[`${C}-treenode-draggable`]:ye,dragging:Ee,"drop-target":D===c,"drop-container":B===c,"drag-over":!j&&W.value,"drag-over-gap-top":!j&&F.value,"drag-over-gap-bottom":!j&&R.value,"filter-node":k&&k(q.value)}),style:n.style,draggable:ye,"aria-grabbed":Ee,onDragstart:ye?ze:void 0,onDragenter:te?Ve:void 0,onDragover:te?qe:void 0,onDragleave:te?Pe:void 0,onDrop:te?We:void 0,onDragend:te?Ge:void 0,onMousemove:u},Qe),ee),[N(Nn,{prefixCls:C,level:ue,isStart:G,isEnd:Y},null),Le(),Ue(),Ye(),Je()])}}});function fe(e,t){if(!e)return[];const n=e.slice(),o=n.indexOf(t);return o>=0&&n.splice(o,1),n}function he(e,t){const n=(e||[]).slice();return n.indexOf(t)===-1&&n.push(t),n}function ot(e){return e.split("-")}function jt(e,t){return`${e}-${t}`}function In(e){return e&&e.type&&e.type.isTreeNode}function Ln(e,t){const n=[],o=t[e];function a(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach(l=>{let{key:v,children:p}=l;n.push(v),a(p)})}return a(o.children),n}function An(e){if(e.parent){const t=ot(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function _n(e){const t=ot(e.pos);return Number(t[t.length-1])===0}function yt(e,t,n,o,a,s,l,v,p,f){var y;const{clientX:h,clientY:b}=e,{top:m,height:w}=e.target.getBoundingClientRect(),S=((f==="rtl"?-1:1)*(((a==null?void 0:a.x)||0)-h)-12)/o;let K=v[n.eventKey];if(b<m+w/2){const X=l.findIndex(Q=>Q.key===K.key),V=X<=0?0:X-1,ne=l[V].key;K=v[ne]}const _=K.key,A=K,P=K.key;let T=0,U=0;if(!p.has(_))for(let X=0;X<S&&An(K);X+=1)K=K.parent,U+=1;const W=t.eventData,F=K.node;let R=!0;return _n(K)&&K.level===0&&b<m+w/2&&s({dragNode:W,dropNode:F,dropPosition:-1})&&K.key===n.eventKey?T=-1:(A.children||[]).length&&p.has(P)?s({dragNode:W,dropNode:F,dropPosition:0})?T=0:R=!1:U===0?S>-1.5?s({dragNode:W,dropNode:F,dropPosition:1})?T=1:R=!1:s({dragNode:W,dropNode:F,dropPosition:0})?T=0:s({dragNode:W,dropNode:F,dropPosition:1})?T=1:R=!1:s({dragNode:W,dropNode:F,dropPosition:1})?T=1:R=!1,{dropPosition:T,dropLevelOffset:U,dropTargetKey:K.key,dropTargetPos:K.pos,dragOverNodeKey:P,dropContainerKey:T===0?null:((y=K.parent)===null||y===void 0?void 0:y.key)||null,dropAllowed:R}}function ht(e,t){if(!e)return;const{multiple:n}=t;return n?e.slice():e.length?[e[0]]:e}function Ze(e){if(!e)return null;let t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(typeof e=="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return null;return t}function gt(e,t){const n=new Set;function o(a){if(n.has(a))return;const s=t[a];if(!s)return;n.add(a);const{parent:l,node:v}=s;v.disabled||l&&o(l.key)}return(e||[]).forEach(a=>{o(a)}),[...n]}var Mn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};function $e(e,t){return e!=null?e:t}function at(e){const{title:t,_title:n,key:o,children:a}=e||{},s=t||"title";return{title:s,_title:n||[s],key:o||"key",children:a||"children"}}function Fn(e){function t(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return on(n).map(a=>{var s,l,v,p;if(!In(a))return null;const f=a.children||{},y=a.key,h={};for(const[X,V]of Object.entries(a.props))h[an(X)]=V;const{isLeaf:b,checkable:m,selectable:w,disabled:g,disableCheckbox:S}=h,K={isLeaf:b||b===""||void 0,checkable:m||m===""||void 0,selectable:w||w===""||void 0,disabled:g||g===""||void 0,disableCheckbox:S||S===""||void 0},_=O(O({},h),K),{title:A=(s=f.title)===null||s===void 0?void 0:s.call(f,_),icon:P=(l=f.icon)===null||l===void 0?void 0:l.call(f,_),switcherIcon:T=(v=f.switcherIcon)===null||v===void 0?void 0:v.call(f,_)}=h,U=Mn(h,["title","icon","switcherIcon"]),W=(p=f.default)===null||p===void 0?void 0:p.call(f),F=O(O(O({},U),{title:A,icon:P,switcherIcon:T,key:y,isLeaf:b}),K),R=t(W);return R.length&&(F.children=R),F})}return t(e)}function jn(e,t,n){const{_title:o,key:a,children:s}=at(n),l=new Set(t===!0?[]:t),v=[];function p(f){let y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return f.map((h,b)=>{const m=jt(y?y.pos:"0",b),w=$e(h[a],m);let g;for(let K=0;K<o.length;K+=1){const _=o[K];if(h[_]!==void 0){g=h[_];break}}const S=O(O({},Dt(h,[...o,a,s])),{title:g,key:w,parent:y,pos:m,children:null,data:h,isStart:[...y?y.isStart:[],b===0],isEnd:[...y?y.isEnd:[],b===f.length-1]});return v.push(S),t===!0||l.has(w)?S.children=p(h[s]||[],S):S.children=[],S})}return p(e),v}function Bn(e,t,n){let o={};typeof n=="object"?o=n:o={externalGetKey:n},o=o||{};const{childrenPropName:a,externalGetKey:s,fieldNames:l}=o,{key:v,children:p}=at(l),f=a||p;let y;s?typeof s=="string"?y=b=>b[s]:typeof s=="function"&&(y=b=>s(b)):y=(b,m)=>$e(b[v],m);function h(b,m,w,g){const S=b?b[f]:e,K=b?jt(w.pos,m):"0",_=b?[...g,b]:[];if(b){const A=y(b,K),P={node:b,index:m,pos:K,key:A,parentPos:w.node?w.pos:null,level:w.level+1,nodes:_};t(P)}S&&S.forEach((A,P)=>{h(A,P,{node:b,pos:K,level:w?w.level+1:-1},_)})}h(null)}function Hn(e){let{initWrapper:t,processEntity:n,onProcessFinished:o,externalGetKey:a,childrenPropName:s,fieldNames:l}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},v=arguments.length>2?arguments[2]:void 0;const p=a||v,f={},y={};let h={posEntities:f,keyEntities:y};return t&&(h=t(h)||h),Bn(e,b=>{const{node:m,index:w,pos:g,key:S,parentPos:K,level:_,nodes:A}=b,P={node:m,nodes:A,index:w,key:S,pos:g,level:_},T=$e(S,g);f[g]=P,y[T]=P,P.parent=f[K],P.parent&&(P.parent.children=P.parent.children||[],P.parent.children.push(P)),n&&n(P,h)},{externalGetKey:p,childrenPropName:s,fieldNames:l}),o&&o(h),h}function Fe(e,t){let{expandedKeysSet:n,selectedKeysSet:o,loadedKeysSet:a,loadingKeysSet:s,checkedKeysSet:l,halfCheckedKeysSet:v,dragOverNodeKey:p,dropPosition:f,keyEntities:y}=t;const h=y[e];return{eventKey:e,expanded:n.has(e),selected:o.has(e),loaded:a.has(e),loading:s.has(e),checked:l.has(e),halfChecked:v.has(e),pos:String(h?h.pos:""),parent:h.parent,dragOver:p===e&&f===0,dragOverGapTop:p===e&&f===-1,dragOverGapBottom:p===e&&f===1}}function je(e){const{data:t,expanded:n,selected:o,checked:a,loaded:s,loading:l,halfChecked:v,dragOver:p,dragOverGapTop:f,dragOverGapBottom:y,pos:h,active:b,eventKey:m}=e,w=O(O({dataRef:t},t),{expanded:n,selected:o,checked:a,loaded:s,loading:l,halfChecked:v,dragOver:p,dragOverGapTop:f,dragOverGapBottom:y,pos:h,active:b,eventKey:m,key:m});return"props"in w||Object.defineProperty(w,"props",{get(){return e}}),w}function Bt(e,t){const n=new Set;return e.forEach(o=>{t.has(o)||n.add(o)}),n}function Rn(e){const{disabled:t,disableCheckbox:n,checkable:o}=e||{};return!!(t||n)||o===!1}function zn(e,t,n,o){const a=new Set(e),s=new Set;for(let v=0;v<=n;v+=1)(t.get(v)||new Set).forEach(f=>{const{key:y,node:h,children:b=[]}=f;a.has(y)&&!o(h)&&b.filter(m=>!o(m.node)).forEach(m=>{a.add(m.key)})});const l=new Set;for(let v=n;v>=0;v-=1)(t.get(v)||new Set).forEach(f=>{const{parent:y,node:h}=f;if(o(h)||!f.parent||l.has(f.parent.key))return;if(o(f.parent.node)){l.add(y.key);return}let b=!0,m=!1;(y.children||[]).filter(w=>!o(w.node)).forEach(w=>{let{key:g}=w;const S=a.has(g);b&&!S&&(b=!1),!m&&(S||s.has(g))&&(m=!0)}),b&&a.add(y.key),m&&s.add(y.key),l.add(y.key)});return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(Bt(s,a))}}function Vn(e,t,n,o,a){const s=new Set(e);let l=new Set(t);for(let p=0;p<=o;p+=1)(n.get(p)||new Set).forEach(y=>{const{key:h,node:b,children:m=[]}=y;!s.has(h)&&!l.has(h)&&!a(b)&&m.filter(w=>!a(w.node)).forEach(w=>{s.delete(w.key)})});l=new Set;const v=new Set;for(let p=o;p>=0;p-=1)(n.get(p)||new Set).forEach(y=>{const{parent:h,node:b}=y;if(a(b)||!y.parent||v.has(y.parent.key))return;if(a(y.parent.node)){v.add(h.key);return}let m=!0,w=!1;(h.children||[]).filter(g=>!a(g.node)).forEach(g=>{let{key:S}=g;const K=s.has(S);m&&!K&&(m=!1),!w&&(K||l.has(S))&&(w=!0)}),m||s.delete(h.key),w&&l.add(h.key),v.add(h.key)});return{checkedKeys:Array.from(s),halfCheckedKeys:Array.from(Bt(l,s))}}function et(e,t,n,o,a,s){let l;l=Rn;const v=new Set(e.filter(f=>!!n[f]));let p;return t===!0?p=zn(v,a,o,l):p=Vn(v,t.halfCheckedKeys,a,o,l),p}function qn(e){const t=Me(0),n=H();return Ce(()=>{const o=new Map;let a=0;const s=e.value||{};for(const l in s)if(Object.prototype.hasOwnProperty.call(s,l)){const v=s[l],{level:p}=v;let f=o.get(p);f||(f=new Set,o.set(p,f)),f.add(v),a=Math.max(a,p)}t.value=a,n.value=o}),{maxLevel:t,levelEntities:n}}var pt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Gn=De({compatConfig:{MODE:3},name:"MotionTreeNode",inheritAttrs:!1,props:O(O({},Ft),{active:Boolean,motion:Object,motionNodes:{type:Array},onMotionStart:Function,onMotionEnd:Function,motionType:String}),setup(e,t){let{attrs:n,slots:o}=t;const a=H(!0),s=nt(),l=H(!1),v=$(()=>e.motion?e.motion:ln()),p=(f,y)=>{var h,b,m,w;y==="appear"?(b=(h=v.value)===null||h===void 0?void 0:h.onAfterEnter)===null||b===void 0||b.call(h,f):y==="leave"&&((w=(m=v.value)===null||m===void 0?void 0:m.onAfterLeave)===null||w===void 0||w.call(m,f)),l.value||e.onMotionEnd(),l.value=!0};return Ke(()=>e.motionNodes,()=>{e.motionNodes&&e.motionType==="hide"&&a.value&&Lt(()=>{a.value=!1})},{immediate:!0,flush:"post"}),It(()=>{e.motionNodes&&e.onMotionStart()}),mn(()=>{e.motionNodes&&p()}),()=>{const{motion:f,motionNodes:y,motionType:h,active:b,eventKey:m}=e,w=pt(e,["motion","motionNodes","motionType","active","eventKey"]);return y?N(rn,ae(ae({},v.value),{},{appear:h==="show",onAfterAppear:g=>p(g,"appear"),onAfterLeave:g=>p(g,"leave")}),{default:()=>[Kn(N("div",{class:`${s.value.prefixCls}-treenode-motion`},[y.map(g=>{const S=pt(g.data,[]),{title:K,key:_,isStart:A,isEnd:P}=g;return delete S.children,N(vt,ae(ae({},S),{},{title:K,active:b,data:g.data,key:_,eventKey:_,isStart:A,isEnd:P}),o)})]),[[sn,a.value]])]}):N(vt,ae(ae({class:n.class,style:n.style},w),{},{active:b,eventKey:m}),o)}}});function Wn(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];const n=e.length,o=t.length;if(Math.abs(n-o)!==1)return{add:!1,key:null};function a(s,l){const v=new Map;s.forEach(f=>{v.set(f,!0)});const p=l.filter(f=>!v.has(f));return p.length===1?p[0]:null}return n<o?{add:!0,key:a(e,t)}:{add:!1,key:a(t,e)}}function bt(e,t,n){const o=e.findIndex(l=>l.key===n),a=e[o+1],s=t.findIndex(l=>l.key===n);if(a){const l=t.findIndex(v=>v.key===a.key);return t.slice(s+1,l)}return t.slice(s+1)}var mt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Kt={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Xn=()=>{},ke=`RC_TREE_MOTION_${Math.random()}`,tt={key:ke},Ht={key:ke,level:0,index:0,pos:"0",node:tt,nodes:[tt]},kt={parent:null,children:[],pos:Ht.pos,data:tt,title:null,key:ke,isStart:[],isEnd:[]};function xt(e,t,n,o){return t===!1||!n?e:e.slice(0,Math.ceil(n/o)+1)}function St(e){const{key:t,pos:n}=e;return $e(t,n)}function Un(e){let t=String(e.key),n=e;for(;n.parent;)n=n.parent,t=`${n.key} > ${t}`;return t}const Yn=De({compatConfig:{MODE:3},name:"NodeList",inheritAttrs:!1,props:Dn,setup(e,t){let{expose:n,attrs:o}=t;const a=Me(),s=Me(),{expandedKeys:l,flattenNodes:v}=Mt();n({scrollTo:g=>{a.value.scrollTo(g)},getIndentWidth:()=>s.value.offsetWidth});const p=H(v.value),f=H([]),y=Me(null);function h(){p.value=v.value,f.value=[],y.value=null,e.onListChangeEnd()}const b=nt();Ke([()=>l.value.slice(),v],(g,S)=>{let[K,_]=g,[A,P]=S;const T=Wn(A,K);if(T.key!==null){const{virtual:U,height:W,itemHeight:F}=e;if(T.add){const R=P.findIndex(ne=>{let{key:Q}=ne;return Q===T.key}),X=xt(bt(P,_,T.key),U,W,F),V=P.slice();V.splice(R+1,0,kt),p.value=V,f.value=X,y.value="show"}else{const R=_.findIndex(ne=>{let{key:Q}=ne;return Q===T.key}),X=xt(bt(_,P,T.key),U,W,F),V=_.slice();V.splice(R+1,0,kt),p.value=V,f.value=X,y.value="hide"}}else P!==_&&(p.value=_)}),Ke(()=>b.value.dragging,g=>{g||h()});const m=$(()=>e.motion===void 0?p.value:v.value),w=()=>{e.onActiveChange(null)};return()=>{const g=O(O({},e),o),{prefixCls:S,selectable:K,checkable:_,disabled:A,motion:P,height:T,itemHeight:U,virtual:W,focusable:F,activeItem:R,focused:X,tabindex:V,onKeydown:ne,onFocus:Q,onBlur:ve,onListChangeStart:le,onListChangeEnd:xe}=g,re=mt(g,["prefixCls","selectable","checkable","disabled","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabindex","onKeydown","onFocus","onBlur","onListChangeStart","onListChangeEnd"]);return N(kn,null,[X&&R&&N("span",{style:Kt,"aria-live":"assertive"},[Un(R)]),N("div",null,[N("input",{style:Kt,disabled:F===!1||A,tabindex:F!==!1?V:null,onKeydown:ne,onFocus:Q,onBlur:ve,value:"",onChange:Xn,"aria-label":"for screen reader"},null)]),N("div",{class:`${S}-treenode`,"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden"}},[N("div",{class:`${S}-indent`},[N("div",{ref:s,class:`${S}-indent-unit`},null)])]),N(wn,ae(ae({},Dt(re,["onActiveChange"])),{},{data:m.value,itemKey:St,height:T,fullHeight:!1,virtual:W,itemHeight:U,prefixCls:`${S}-list`,ref:a,onVisibleChange:(z,de)=>{const q=new Set(z);de.filter(oe=>!q.has(oe)).some(oe=>St(oe)===ke)&&h()}}),{default:z=>{const{pos:de}=z,q=mt(z.data,[]),{title:Z,key:oe,isStart:ce,isEnd:pe}=z,Se=$e(oe,de);return delete q.key,delete q.children,N(Gn,ae(ae({},q),{},{eventKey:Se,title:Z,active:!!R&&oe===R.key,data:z.data,isStart:ce,isEnd:pe,motion:P,motionNodes:oe===ke?f.value:null,motionType:y.value,onMotionStart:le,onMotionEnd:h,onMousemove:w}),null)}})])}}});function Jn(e){let{dropPosition:t,dropLevelOffset:n,indent:o}=e;const a={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:"2px"};switch(t){case-1:a.top=0,a.left=`${-n*o}px`;break;case 1:a.bottom=0,a.left=`${-n*o}px`;break;case 0:a.bottom=0,a.left=`${o}`;break}return N("div",{style:a},null)}const Qn=10,mo=De({compatConfig:{MODE:3},name:"Tree",inheritAttrs:!1,props:dn($n(),{prefixCls:"vc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,expandAction:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:Jn,allowDrop:()=>!0}),setup(e,t){let{attrs:n,slots:o,expose:a}=t;const s=H(!1);let l={};const v=H(),p=H([]),f=H([]),y=H([]),h=H([]),b=H([]),m=H([]),w={},g=Tt({draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null}),S=H([]);Ke([()=>e.treeData,()=>e.children],()=>{S.value=e.treeData!==void 0?e.treeData.slice():Fn(ct(e.children))},{immediate:!0,deep:!0});const K=H({}),_=H(!1),A=H(null),P=H(!1),T=$(()=>at(e.fieldNames)),U=H();let W=null,F=null,R=null;const X=$(()=>({expandedKeysSet:V.value,selectedKeysSet:ne.value,loadedKeysSet:Q.value,loadingKeysSet:ve.value,checkedKeysSet:le.value,halfCheckedKeysSet:xe.value,dragOverNodeKey:g.dragOverNodeKey,dropPosition:g.dropPosition,keyEntities:K.value})),V=$(()=>new Set(m.value)),ne=$(()=>new Set(p.value)),Q=$(()=>new Set(h.value)),ve=$(()=>new Set(b.value)),le=$(()=>new Set(f.value)),xe=$(()=>new Set(y.value));Ce(()=>{if(S.value){const i=Hn(S.value,{fieldNames:T.value});K.value=O({[ke]:Ht},i.keyEntities)}});let re=!1;Ke([()=>e.expandedKeys,()=>e.autoExpandParent,K],(i,d)=>{let[u,x]=i,[E,C]=d,k=m.value;if(e.expandedKeys!==void 0||re&&x!==C)k=e.autoExpandParent||!re&&e.defaultExpandParent?gt(e.expandedKeys,K.value):e.expandedKeys;else if(!re&&e.defaultExpandAll){const I=O({},K.value);delete I[ke],k=Object.keys(I).map(B=>I[B].key)}else!re&&e.defaultExpandedKeys&&(k=e.autoExpandParent||e.defaultExpandParent?gt(e.defaultExpandedKeys,K.value):e.defaultExpandedKeys);k&&(m.value=k),re=!0},{immediate:!0});const z=H([]);Ce(()=>{z.value=jn(S.value,m.value,T.value)}),Ce(()=>{e.selectable&&(e.selectedKeys!==void 0?p.value=ht(e.selectedKeys,e):!re&&e.defaultSelectedKeys&&(p.value=ht(e.defaultSelectedKeys,e)))});const{maxLevel:de,levelEntities:q}=qn(K);Ce(()=>{if(e.checkable){let i;if(e.checkedKeys!==void 0?i=Ze(e.checkedKeys)||{}:!re&&e.defaultCheckedKeys?i=Ze(e.defaultCheckedKeys)||{}:S.value&&(i=Ze(e.checkedKeys)||{checkedKeys:f.value,halfCheckedKeys:y.value}),i){let{checkedKeys:d=[],halfCheckedKeys:u=[]}=i;e.checkStrictly||({checkedKeys:d,halfCheckedKeys:u}=et(d,!0,K.value,de.value,q.value)),f.value=d,y.value=u}}}),Ce(()=>{e.loadedKeys&&(h.value=e.loadedKeys)});const Z=()=>{O(g,{dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})},oe=i=>{U.value.scrollTo(i)};Ke(()=>e.activeKey,()=>{e.activeKey!==void 0&&(A.value=e.activeKey)},{immediate:!0}),Ke(A,i=>{Lt(()=>{i!==null&&oe({key:i})})},{immediate:!0,flush:"post"});const ce=i=>{e.expandedKeys===void 0&&(m.value=i)},pe=()=>{g.draggingNodeKey!==null&&O(g,{draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),W=null,R=null},Se=(i,d)=>{const{onDragend:u}=e;g.dragOverNodeKey=null,pe(),u==null||u({event:i,node:d.eventData}),F=null},Oe=i=>{Se(i,null),window.removeEventListener("dragend",Oe)},He=(i,d)=>{const{onDragstart:u}=e,{eventKey:x,eventData:E}=d;F=d,W={x:i.clientX,y:i.clientY};const C=fe(m.value,x);g.draggingNodeKey=x,g.dragChildrenKeys=Ln(x,K.value),v.value=U.value.getIndentWidth(),ce(C),window.addEventListener("dragend",Oe),u&&u({event:i,node:E})},Re=(i,d)=>{const{onDragenter:u,onExpand:x,allowDrop:E,direction:C}=e,{pos:k,eventKey:I}=d;if(R!==I&&(R=I),!F){Z();return}const{dropPosition:B,dropLevelOffset:D,dropTargetKey:L,dropContainerKey:j,dropTargetPos:ee,dropAllowed:ue,dragOverNodeKey:ie}=yt(i,F,d,v.value,W,E,z.value,K.value,V.value,C);if(g.dragChildrenKeys.indexOf(L)!==-1||!ue){Z();return}if(l||(l={}),Object.keys(l).forEach(te=>{clearTimeout(l[te])}),F.eventKey!==d.eventKey&&(l[k]=window.setTimeout(()=>{if(g.draggingNodeKey===null)return;let te=m.value.slice();const ye=K.value[d.eventKey];ye&&(ye.children||[]).length&&(te=he(m.value,d.eventKey)),ce(te),x&&x(te,{node:d.eventData,expanded:!0,nativeEvent:i})},800)),F.eventKey===L&&D===0){Z();return}O(g,{dragOverNodeKey:ie,dropPosition:B,dropLevelOffset:D,dropTargetKey:L,dropContainerKey:j,dropTargetPos:ee,dropAllowed:ue}),u&&u({event:i,node:d.eventData,expandedKeys:m.value})},ze=(i,d)=>{const{onDragover:u,allowDrop:x,direction:E}=e;if(!F)return;const{dropPosition:C,dropLevelOffset:k,dropTargetKey:I,dropContainerKey:B,dropAllowed:D,dropTargetPos:L,dragOverNodeKey:j}=yt(i,F,d,v.value,W,x,z.value,K.value,V.value,E);g.dragChildrenKeys.indexOf(I)!==-1||!D||(F.eventKey===I&&k===0?g.dropPosition===null&&g.dropLevelOffset===null&&g.dropTargetKey===null&&g.dropContainerKey===null&&g.dropTargetPos===null&&g.dropAllowed===!1&&g.dragOverNodeKey===null||Z():C===g.dropPosition&&k===g.dropLevelOffset&&I===g.dropTargetKey&&B===g.dropContainerKey&&L===g.dropTargetPos&&D===g.dropAllowed&&j===g.dragOverNodeKey||O(g,{dropPosition:C,dropLevelOffset:k,dropTargetKey:I,dropContainerKey:B,dropTargetPos:L,dropAllowed:D,dragOverNodeKey:j}),u&&u({event:i,node:d.eventData}))},Ve=(i,d)=>{R===d.eventKey&&!i.currentTarget.contains(i.relatedTarget)&&(Z(),R=null);const{onDragleave:u}=e;u&&u({event:i,node:d.eventData})},qe=function(i,d){let u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var x;const{dragChildrenKeys:E,dropPosition:C,dropTargetKey:k,dropTargetPos:I,dropAllowed:B}=g;if(!B)return;const{onDrop:D}=e;if(g.dragOverNodeKey=null,pe(),k===null)return;const L=O(O({},Fe(k,ct(X.value))),{active:((x=M.value)===null||x===void 0?void 0:x.key)===k,data:K.value[k].node});E.indexOf(k);const j=ot(I),ee={event:i,node:je(L),dragNode:F?F.eventData:null,dragNodesKeys:[F.eventKey].concat(E),dropToGap:C!==0,dropPosition:C+Number(j[j.length-1])};u||D==null||D(ee),F=null},Pe=(i,d)=>{const{expanded:u,key:x}=d,E=z.value.filter(k=>k.key===x)[0],C=je(O(O({},Fe(x,X.value)),{data:E.data}));ce(u?fe(m.value,x):he(m.value,x)),be(i,C)},Ge=(i,d)=>{const{onClick:u,expandAction:x}=e;x==="click"&&Pe(i,d),u&&u(i,d)},We=(i,d)=>{const{onDblclick:u,expandAction:x}=e;(x==="doubleclick"||x==="dblclick")&&Pe(i,d),u&&u(i,d)},Te=(i,d)=>{let u=p.value;const{onSelect:x,multiple:E}=e,{selected:C}=d,k=d[T.value.key],I=!C;I?E?u=he(u,k):u=[k]:u=fe(u,k);const B=K.value,D=u.map(L=>{const j=B[L];return j?j.node:null}).filter(L=>L);e.selectedKeys===void 0&&(p.value=u),x&&x(u,{event:"select",selected:I,node:d,selectedNodes:D,nativeEvent:i})},Ie=(i,d,u)=>{const{checkStrictly:x,onCheck:E}=e,C=d[T.value.key];let k;const I={event:"check",node:d,checked:u,nativeEvent:i},B=K.value;if(x){const D=u?he(f.value,C):fe(f.value,C),L=fe(y.value,C);k={checked:D,halfChecked:L},I.checkedNodes=D.map(j=>B[j]).filter(j=>j).map(j=>j.node),e.checkedKeys===void 0&&(f.value=D)}else{let{checkedKeys:D,halfCheckedKeys:L}=et([...f.value,C],!0,B,de.value,q.value);if(!u){const j=new Set(D);j.delete(C),{checkedKeys:D,halfCheckedKeys:L}=et(Array.from(j),{halfCheckedKeys:L},B,de.value,q.value)}k=D,I.checkedNodes=[],I.checkedNodesPositions=[],I.halfCheckedKeys=L,D.forEach(j=>{const ee=B[j];if(!ee)return;const{node:ue,pos:ie}=ee;I.checkedNodes.push(ue),I.checkedNodesPositions.push({node:ue,pos:ie})}),e.checkedKeys===void 0&&(f.value=D,y.value=L)}E&&E(k,I)},Le=i=>{const d=i[T.value.key],u=new Promise((x,E)=>{const{loadData:C,onLoad:k}=e;if(!C||Q.value.has(d)||ve.value.has(d))return null;C(i).then(()=>{const B=he(h.value,d),D=fe(b.value,d);k&&k(B,{event:"load",node:i}),e.loadedKeys===void 0&&(h.value=B),b.value=D,x()}).catch(B=>{const D=fe(b.value,d);if(b.value=D,w[d]=(w[d]||0)+1,w[d]>=Qn){const L=he(h.value,d);e.loadedKeys===void 0&&(h.value=L),x()}E(B)}),b.value=he(b.value,d)});return u.catch(()=>{}),u},Xe=(i,d)=>{const{onMouseenter:u}=e;u&&u({event:i,node:d})},Ae=(i,d)=>{const{onMouseleave:u}=e;u&&u({event:i,node:d})},Ue=(i,d)=>{const{onRightClick:u}=e;u&&(i.preventDefault(),u({event:i,node:d}))},Ye=i=>{const{onFocus:d}=e;_.value=!0,d&&d(i)},_e=i=>{const{onBlur:d}=e;_.value=!1,c(null),d&&d(i)},be=(i,d)=>{let u=m.value;const{onExpand:x,loadData:E}=e,{expanded:C}=d,k=d[T.value.key];if(P.value)return;u.indexOf(k);const I=!C;if(I?u=he(u,k):u=fe(u,k),ce(u),x&&x(u,{node:d,expanded:I,nativeEvent:i}),I&&E){const B=Le(d);B&&B.then(()=>{}).catch(D=>{const L=fe(m.value,k);ce(L),Promise.reject(D)})}},Je=()=>{P.value=!0},r=()=>{setTimeout(()=>{P.value=!1})},c=i=>{const{onActiveChange:d}=e;A.value!==i&&(e.activeKey!==void 0&&(A.value=i),i!==null&&oe({key:i}),d&&d(i))},M=$(()=>A.value===null?null:z.value.find(i=>{let{key:d}=i;return d===A.value})||null),G=i=>{let d=z.value.findIndex(x=>{let{key:E}=x;return E===A.value});d===-1&&i<0&&(d=z.value.length),d=(d+i+z.value.length)%z.value.length;const u=z.value[d];if(u){const{key:x}=u;c(x)}else c(null)},Y=$(()=>je(O(O({},Fe(A.value,X.value)),{data:M.value.data,active:!0}))),J=i=>{const{onKeydown:d,checkable:u,selectable:x}=e;switch(i.which){case we.UP:{G(-1),i.preventDefault();break}case we.DOWN:{G(1),i.preventDefault();break}}const E=M.value;if(E&&E.data){const C=E.data.isLeaf===!1||!!(E.data.children||[]).length,k=Y.value;switch(i.which){case we.LEFT:{C&&V.value.has(A.value)?be({},k):E.parent&&c(E.parent.key),i.preventDefault();break}case we.RIGHT:{C&&!V.value.has(A.value)?be({},k):E.children&&E.children.length&&c(E.children[0].key),i.preventDefault();break}case we.ENTER:case we.SPACE:{u&&!k.disabled&&k.checkable!==!1&&!k.disableCheckbox?Ie({},k,!le.value.has(A.value)):!u&&x&&!k.disabled&&k.selectable!==!1&&Te({},k);break}}}d&&d(i)};return a({onNodeExpand:be,scrollTo:oe,onKeydown:J,selectedKeys:$(()=>p.value),checkedKeys:$(()=>f.value),halfCheckedKeys:$(()=>y.value),loadedKeys:$(()=>h.value),loadingKeys:$(()=>b.value),expandedKeys:$(()=>m.value)}),xn(()=>{window.removeEventListener("dragend",Oe),s.value=!0}),En({expandedKeys:m,selectedKeys:p,loadedKeys:h,loadingKeys:b,checkedKeys:f,halfCheckedKeys:y,expandedKeysSet:V,selectedKeysSet:ne,loadedKeysSet:Q,loadingKeysSet:ve,checkedKeysSet:le,halfCheckedKeysSet:xe,flattenNodes:z}),()=>{const{draggingNodeKey:i,dropLevelOffset:d,dropContainerKey:u,dropTargetKey:x,dropPosition:E,dragOverNodeKey:C}=g,{prefixCls:k,showLine:I,focusable:B,tabindex:D=0,selectable:L,showIcon:j,icon:ee=o.icon,switcherIcon:ue,draggable:ie,checkable:te,checkStrictly:ye,disabled:Ee,motion:Qe,loadData:Rt,filterTreeNode:zt,height:Vt,itemHeight:qt,virtual:Gt,dropIndicatorRender:Wt,onContextmenu:Xt,onScroll:Ut,direction:Yt,rootClassName:Jt,rootStyle:Qt}=e,{class:Zt,style:en}=n,tn=Nt(O(O({},e),n),{aria:!0,data:!0});let Ne;return ie?typeof ie=="object"?Ne=ie:typeof ie=="function"?Ne={nodeDraggable:ie}:Ne={}:Ne=!1,N(On,{value:{prefixCls:k,selectable:L,showIcon:j,icon:ee,switcherIcon:ue,draggable:Ne,draggingNodeKey:i,checkable:te,customCheckable:o.checkable,checkStrictly:ye,disabled:Ee,keyEntities:K.value,dropLevelOffset:d,dropContainerKey:u,dropTargetKey:x,dropPosition:E,dragOverNodeKey:C,dragging:i!==null,indent:v.value,direction:Yt,dropIndicatorRender:Wt,loadData:Rt,filterTreeNode:zt,onNodeClick:Ge,onNodeDoubleClick:We,onNodeExpand:be,onNodeSelect:Te,onNodeCheck:Ie,onNodeLoad:Le,onNodeMouseEnter:Xe,onNodeMouseLeave:Ae,onNodeContextMenu:Ue,onNodeDragStart:He,onNodeDragEnter:Re,onNodeDragOver:ze,onNodeDragLeave:Ve,onNodeDragEnd:Se,onNodeDrop:qe,slots:o}},{default:()=>[N("div",{role:"tree",class:ge(k,Zt,Jt,{[`${k}-show-line`]:I,[`${k}-focused`]:_.value,[`${k}-active-focused`]:A.value!==null}),style:Qt},[N(Yn,ae({ref:U,prefixCls:k,style:en,disabled:Ee,selectable:L,checkable:!!te,motion:Qe,height:Vt,itemHeight:qt,virtual:Gt,focusable:B,focused:_.value,tabindex:D,activeItem:M.value,onFocus:Ye,onBlur:_e,onKeydown:J,onActiveChange:c,onListChangeStart:Je,onListChangeEnd:r,onContextmenu:Xt,onScroll:Ut},tn),null)])]})}}});var Zn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};function wt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){eo(e,a,n[a])})}return e}function eo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var lt=function(t,n){var o=wt({},t,n.attrs);return N(Be,wt({},o,{icon:Zn}),null)};lt.displayName="FileOutlined";lt.inheritAttrs=!1;var to={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};function Ct(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){no(e,a,n[a])})}return e}function no(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var rt=function(t,n){var o=Ct({},t,n.attrs);return N(Be,Ct({},o,{icon:to}),null)};rt.displayName="MinusSquareOutlined";rt.inheritAttrs=!1;var oo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};function Ot(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){ao(e,a,n[a])})}return e}function ao(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var it=function(t,n){var o=Ot({},t,n.attrs);return N(Be,Ot({},o,{icon:oo}),null)};it.displayName="PlusSquareOutlined";it.inheritAttrs=!1;var lo={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};function Et(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){ro(e,a,n[a])})}return e}function ro(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var st=function(t,n){var o=Et({},t,n.attrs);return N(Be,Et({},o,{icon:lo}),null)};st.displayName="CaretDownFilled";st.inheritAttrs=!1;function Ko(e,t,n,o,a){const{isLeaf:s,expanded:l,loading:v}=n;let p=t;if(v)return N(cn,{class:`${e}-switcher-loading-icon`},null);let f;a&&typeof a=="object"&&(f=a.showLeafIcon);let y=null;const h=`${e}-switcher-icon`;return s?a?f&&o?o(n):(typeof a=="object"&&!f?y=N("span",{class:`${e}-switcher-leaf-line`},null):y=N(lt,{class:`${e}-switcher-line-icon`},null),y):null:(y=N(st,{class:h},null),a&&(y=l?N(rt,{class:`${e}-switcher-line-icon`},null):N(it,{class:`${e}-switcher-line-icon`},null)),typeof t=="function"?p=t(O(O({},n),{defaultIcon:y,switcherCls:h})):un(p)&&(p=Sn(p,{class:h})),p||y)}const io=new yn("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),so=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),co=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${t.lineWidthBold}px solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),uo=(e,t)=>{const{treeCls:n,treeNodeCls:o,treeNodePadding:a,treeTitleHeight:s}=t,l=(s-t.fontSizeLG)/2,v=t.paddingXS;return{[n]:O(O({},vn(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,[`&${n}-rtl`]:{[`${n}-switcher`]:{"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(90deg)"}}}}},[`&-focused:not(:hover):not(${n}-active-focused)`]:O({},dt(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${o}.dragging`]:{position:"relative","&:after":{position:"absolute",top:0,insetInlineEnd:0,bottom:a,insetInlineStart:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:io,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none"}}}},[`${o}`]:{display:"flex",alignItems:"flex-start",padding:`0 0 ${a}px 0`,outline:"none","&-rtl":{direction:"rtl"},"&-disabled":{[`${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}}},[`&-active ${n}-node-content-wrapper`]:O({},dt(t)),[`&:not(${o}-disabled).filter-node ${n}-title`]:{color:"inherit",fontWeight:500},"&-draggable":{[`${n}-draggable-icon`]:{width:s,lineHeight:`${s}px`,textAlign:"center",visibility:"visible",opacity:.2,transition:`opacity ${t.motionDurationSlow}`,[`${o}:hover &`]:{opacity:.45}},[`&${o}-disabled`]:{[`${n}-draggable-icon`]:{visibility:"hidden"}}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:s}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher`]:O(O({},so(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:s,margin:0,lineHeight:`${s}px`,textAlign:"center",cursor:"pointer",userSelect:"none","&-noop":{cursor:"default"},"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(-90deg)"}}},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:s/2,bottom:-a,marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:s/2*.8,height:s/2,borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-checkbox`]:{top:"initial",marginInlineEnd:v,marginBlockStart:l},[`${n}-node-content-wrapper, ${n}-checkbox + span`]:{position:"relative",zIndex:"auto",minHeight:s,margin:0,padding:`0 ${t.paddingXS/2}px`,color:"inherit",lineHeight:`${s}px`,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,"&:hover":{backgroundColor:t.controlItemBgHover},[`&${n}-node-selected`]:{backgroundColor:t.controlItemBgActive},[`${n}-iconEle`]:{display:"inline-block",width:s,height:s,lineHeight:`${s}px`,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}},[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}-node-content-wrapper`]:O({lineHeight:`${s}px`,userSelect:"none"},co(e,t)),[`${o}.drop-container`]:{"> [draggable]":{boxShadow:`0 0 0 2px ${t.colorPrimary}`}},"&-show-line":{[`${n}-indent`]:{"&-unit":{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:s/2,bottom:-a,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end":{"&:before":{display:"none"}}}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${o}-leaf-last`]:{[`${n}-switcher`]:{"&-leaf-line":{"&:before":{top:"auto !important",bottom:"auto !important",height:`${s/2}px !important`}}}}})}},fo=e=>{const{treeCls:t,treeNodeCls:n,treeNodePadding:o}=e;return{[`${t}${t}-directory`]:{[n]:{position:"relative","&:before":{position:"absolute",top:0,insetInlineEnd:0,bottom:o,insetInlineStart:0,transition:`background-color ${e.motionDurationMid}`,content:'""',pointerEvents:"none"},"&:hover":{"&:before":{background:e.controlItemBgHover}},"> *":{zIndex:1},[`${t}-switcher`]:{transition:`color ${e.motionDurationMid}`},[`${t}-node-content-wrapper`]:{borderRadius:0,userSelect:"none","&:hover":{background:"transparent"},[`&${t}-node-selected`]:{color:e.colorTextLightSolid,background:"transparent"}},"&-selected":{"\n            &:hover::before,\n            &::before\n          ":{background:e.colorPrimary},[`${t}-switcher`]:{color:e.colorTextLightSolid},[`${t}-node-content-wrapper`]:{color:e.colorTextLightSolid,background:"transparent"}}}}}},vo=(e,t)=>{const n=`.${e}`,o=`${n}-treenode`,a=t.paddingXS/2,s=t.controlHeightSM,l=fn(t,{treeCls:n,treeNodeCls:o,treeNodePadding:a,treeTitleHeight:s});return[uo(e,l),fo(l)]},ko=hn("Tree",(e,t)=>{let{prefixCls:n}=t;return[{[e.componentCls]:Cn(`${n}-checkbox`,e)},vo(n,e),gn(e)]});export{lt as F,mo as T,vt as V,Hn as a,gt as b,Fn as c,et as d,qn as e,at as f,vo as g,Ko as r,$n as t,ko as u};
