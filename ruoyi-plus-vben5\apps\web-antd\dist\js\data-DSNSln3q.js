import{U as l,a as m}from"../jse/index-index-C-MnMZEz.js";import{_ as p}from"./options-tag.vue_vue_type_script_setup_true_lang-k3ySxERw.js";import{r as u}from"./render-BxXtQdeV.js";import{a as o}from"./get-popup-container-P4S1sr5h.js";import{ar as c}from"./bootstrap-DCMzVRvD.js";const s=[{label:"病假",value:"1"},{label:"事假",value:"2"},{label:"年假",value:"3"},{label:"婚假",value:"4"},{label:"产假",value:"5"},{label:"其他",value:"7"}],d=[{label:"请假流程-普通",value:"leave1"},{label:"请假流程-排他网关",value:"leave2"},{label:"请假流程-并行网关",value:"leave3"},{label:"请假流程-会签",value:"leave4"},{label:"请假申请-并行会签网关",value:"leave5"},{label:"请假申请-排他并行网关",value:"leave6"}],Y=()=>[{component:"InputNumber",componentProps:{min:1},fieldName:"startLeaveDays",label:"请假天数"},{component:"InputNumber",componentProps:{min:1},fieldName:"endLeaveDays",label:"至",labelClass:"justify-center"}],N=[{type:"checkbox",width:60},{title:"请假类型",field:"leaveType",slots:{default:({row:e})=>m(p,{options:s,value:e.leaveType},null)}},{title:"开始时间",field:"startDate",formatter:({cellValue:e})=>l(e).format("YYYY-MM-DD")},{title:"结束时间",field:"endDate",formatter:({cellValue:e})=>l(e).format("YYYY-MM-DD")},{title:"请假天数",field:"leaveDays",formatter:({cellValue:e})=>`${e}天`},{title:"请假原因",field:"remark"},{title:"流程状态",field:"status",slots:{default:({row:e})=>u(e.status,c.WF_BUSINESS_STATUS)}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],h=e=>[{label:"主键",fieldName:"id",component:"Input",dependencies:{show:()=>!1,triggerFields:[""]}},{label:"流程类型",fieldName:"flowType",component:"Select",help:"这里仅仅为了发起流程方便, 实际不应该包含此字段",componentProps:{options:d,getPopupContainer:o},defaultValue:"leave1",rules:"selectRequired",dependencies:{show:()=>e,triggerFields:[""]}},{label:"请假类型",fieldName:"leaveType",component:"Select",componentProps:{options:s,getPopupContainer:o},rules:"selectRequired",formItemClass:"col-span-1"},{label:"开始时间",fieldName:"dateRange",component:"RangePicker",componentProps(a){return{format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD HH:mm:ss",onChange:t=>{if(!t){a.leaveDays=null;return}const[r,n]=t,i=l(n).diff(l(r),"day")+1;a.leaveDays=i}}},rules:"required",formItemClass:"col-span-1"},{label:"请假天数",fieldName:"leaveDays",component:"Input",componentProps:{disabled:!0},rules:"required"},{label:"请假原因",fieldName:"remark",component:"Textarea",formItemClass:"items-start"}];export{N as c,s as l,h as m,Y as q};
