2025-06-15 21:09:03 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:09:03 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 46216 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:09:03 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-15 21:09:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-15 21:09:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:09:05 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:09:05 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:09:05 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:09:06 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:09:06 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:09:26 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway *************:8080 register finished
2025-06-15 21:09:26 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-15 21:09:26 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 25.577 seconds (process running for 26.294)
2025-06-15 21:09:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-15 21:09:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:15:12 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:15:13 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1107]毫秒
2025-06-15 21:15:13 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:15:13 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[113]毫秒
2025-06-15 21:15:13 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-15 21:15:13 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[29]毫秒
2025-06-15 21:15:14 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:15:14 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:15:14 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-15 21:15:14 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[154]毫秒
2025-06-15 21:15:14 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[313]毫秒
2025-06-15 21:15:14 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:15:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[49]毫秒
2025-06-15 21:15:15 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[877]毫秒
2025-06-15 21:16:02 [boundedElastic-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1934177478799532034","dataName":"master","tableName":"wms_warehouse","tableComment":"仓库表","subTableName":null,"subTableFkName":null,"className":"WmsWarehouse","tplCategory":"crud","packageName":"org.dromara.system","moduleName":"system","businessName":"warehouse","functionName":"仓库","functionAuthor":"奚翔","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939010","tableId":"1934177478799532034","columnName":"warehouse_id","columnComment":"仓库ID","columnType":"bigint","javaType":"Long","javaField":"warehouseId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":"0","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":1,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseId","query":false,"increment":true,"id":"row_34"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939011","tableId":"1934177478799532034","columnName":"tenant_id","columnComment":"租户编号","columnType":"varchar(20)","javaType":"String","javaField":"tenantId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":2,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"TenantId","query":false,"increment":true,"id":"row_35"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939012","tableId":"1934177478799532034","columnName":"dept_id","columnComment":"部门ID","columnType":"bigint","javaType":"Long","javaField":"deptId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"DeptId","query":true,"increment":true,"id":"row_36"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939013","tableId":"1934177478799532034","columnName":"warehouse_number","columnComment":"仓库编码","columnType":"varchar(30)","javaType":"String","javaField":"warehouseNumber","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":4,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseNumber","query":true,"increment":true,"id":"row_37"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939014","tableId":"1934177478799532034","columnName":"warehouse_name","columnComment":"仓库名称","columnType":"varchar(30)","javaType":"String","javaField":"warehouseName","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":5,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseName","query":true,"increment":true,"id":"row_38"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939015","tableId":"1934177478799532034","columnName":"warehouse_type","columnComment":"仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）","columnType":"char(1)","javaType":"String","javaField":"warehouseType","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"","sort":6,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseType","query":true,"increment":true,"id":"row_39"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939016","tableId":"1934177478799532034","columnName":"warehouse_inventory_status","columnComment":"库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseInventoryStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"radio","dictType":"","sort":7,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseInventoryStatus","query":true,"increment":true,"id":"row_40"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939017","tableId":"1934177478799532034","columnName":"warehouse_receving_status","columnComment":"收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseRecevingStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"radio","dictType":"","sort":8,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseRecevingStatus","query":true,"increment":true,"id":"row_41"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939018","tableId":"1934177478799532034","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":9,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"CreateDept","query":false,"increment":true,"id":"row_42"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939019","tableId":"1934177478799532034","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":10,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateBy","query":false,"increment":true,"id":"row_43"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939020","tableId":"1934177478799532034","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":11,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateTime","query":false,"increment":true,"id":"row_44"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939021","tableId":"1934177478799532034","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":12,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateBy","query":false,"increment":true,"id":"row_45"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939022","tableId":"1934177478799532034","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":13,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateTime","query":false,"increment":true,"id":"row_46"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939023","tableId":"1934177478799532034","columnName":"remark","columnComment":"备注","columnType":"varchar(500)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"textarea","dictType":"","sort":14,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true,"capJavaField":"Remark","query":false,"increment":true,"id":"row_47"}],"options":"{\"parentMenuId\":\"1931961869597331458\",\"popupComponent\":\"modal\",\"formComponent\":\"useForm\"}","menuIds":null,"parentMenuId":"1931961869597331458","parentMenuName":null,"crud":true,"tree":false,"popupComponent":"drawer","formComponent":"native","params":{"parentMenuId":"1931961869597331458","popupComponent":"drawer","formComponent":"native"}}]
2025-06-15 21:16:02 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[284]毫秒
2025-06-15 21:16:03 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:16:03 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[12]毫秒
2025-06-15 21:16:03 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:16:03 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[143]毫秒
2025-06-15 21:16:10 [boundedElastic-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_yes_no],无参数
2025-06-15 21:16:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_yes_no],耗时:[50]毫秒
2025-06-15 21:16:10 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/list],耗时:[63]毫秒
2025-06-15 21:16:13 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:13 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:13 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[20]毫秒
2025-06-15 21:16:13 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[23]毫秒
2025-06-15 21:16:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-15 21:16:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[27]毫秒
2025-06-15 21:16:25 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:16:25 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[15]毫秒
2025-06-15 21:16:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:16:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-15 21:16:25 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-15 21:16:25 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[15]毫秒
2025-06-15 21:16:25 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[43]毫秒
2025-06-15 21:16:25 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[57]毫秒
2025-06-15 21:16:25 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:25 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[33]毫秒
2025-06-15 21:16:26 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:16:26 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[16]毫秒
2025-06-15 21:16:26 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[25]毫秒
2025-06-15 21:16:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:16:26 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[13]毫秒
2025-06-15 21:16:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:26 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[17]毫秒
2025-06-15 21:16:27 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:16:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[17]毫秒
2025-06-15 21:16:27 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[26]毫秒
2025-06-15 21:16:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:16:27 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-15 21:16:27 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:16:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[23]毫秒
2025-06-15 21:17:03 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/type],参数类型[json],参数:[{"dictType":"wms_warehouse_type","dictName":"仓库属性"}]
2025-06-15 21:17:03 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/type],耗时:[124]毫秒
2025-06-15 21:17:04 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-15 21:17:04 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[19]毫秒
2025-06-15 21:17:10 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/1934238728431386625],无参数
2025-06-15 21:17:10 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/1934238728431386625],耗时:[17]毫秒
2025-06-15 21:17:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dict/type],参数类型[json],参数:[{"dictType":"wms_warehouse_type","dictId":"1934238728431386625","dictName":"仓库属性","remark":"仓库属性"}]
2025-06-15 21:17:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dict/type],耗时:[65]毫秒
2025-06-15 21:17:14 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-15 21:17:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[16]毫秒
2025-06-15 21:17:34 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:17:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[20]毫秒
2025-06-15 21:17:53 [boundedElastic-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"","dictLabel":"普通仓库","dictValue":"0","dictSort":0,"remark":"普通仓库"}]
2025-06-15 21:17:53 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[41]毫秒
2025-06-15 21:17:53 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:17:53 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[15]毫秒
2025-06-15 21:18:12 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"","dictLabel":"车间仓库","dictValue":"1","dictSort":1,"remark":"车间仓库"}]
2025-06-15 21:18:12 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[30]毫秒
2025-06-15 21:18:12 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:18:12 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[16]毫秒
2025-06-15 21:18:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/1934238935864885250],无参数
2025-06-15 21:18:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/1934238935864885250],耗时:[15]毫秒
2025-06-15 21:18:31 [boundedElastic-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dict/data],参数类型[json],参数:[{"dictCode":"1934238935864885250","dictType":"wms_warehouse_type","listClass":"primary","dictLabel":"普通仓库","dictValue":"0","dictSort":0,"remark":"普通仓库"}]
2025-06-15 21:18:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dict/data],耗时:[29]毫秒
2025-06-15 21:18:31 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:18:31 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[15]毫秒
2025-06-15 21:18:34 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/1934238935864885250],无参数
2025-06-15 21:18:34 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/1934238935864885250],耗时:[12]毫秒
2025-06-15 21:18:39 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dict/data],参数类型[json],参数:[{"dictCode":"1934238935864885250","dictType":"wms_warehouse_type","listClass":"green","dictLabel":"普通仓库","dictValue":"0","dictSort":0,"remark":"普通仓库"}]
2025-06-15 21:18:39 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dict/data],耗时:[34]毫秒
2025-06-15 21:18:39 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:18:39 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[15]毫秒
2025-06-15 21:18:40 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/1934239017548955649],无参数
2025-06-15 21:18:40 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/1934239017548955649],耗时:[15]毫秒
2025-06-15 21:18:46 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dict/data],参数类型[json],参数:[{"dictCode":"1934239017548955649","dictType":"wms_warehouse_type","listClass":"primary","dictLabel":"车间仓库","dictValue":"1","dictSort":1,"remark":"车间仓库"}]
2025-06-15 21:18:46 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dict/data],耗时:[33]毫秒
2025-06-15 21:18:46 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:18:46 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[14]毫秒
2025-06-15 21:19:25 [boundedElastic-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"orange","dictLabel":"供应商仓库","dictValue":"2","dictSort":2,"remark":"供应商仓库"}]
2025-06-15 21:19:26 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[26]毫秒
2025-06-15 21:19:26 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:19:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[15]毫秒
2025-06-15 21:19:40 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"orange","dictLabel":"客户仓库","dictValue":"3","dictSort":3,"remark":"客户仓库"}]
2025-06-15 21:19:40 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[27]毫秒
2025-06-15 21:19:40 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:19:40 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[14]毫秒
2025-06-15 21:19:56 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"warning","dictLabel":"第三方仓储","dictValue":"4","dictSort":4,"remark":"第三方仓储"}]
2025-06-15 21:19:56 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[33]毫秒
2025-06-15 21:19:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-15 21:19:56 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[13]毫秒
2025-06-15 21:20:27 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/type],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","dictName":"库存状态类型"}]
2025-06-15 21:20:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/type],耗时:[23]毫秒
2025-06-15 21:20:28 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-15 21:20:28 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[12]毫秒
2025-06-15 21:20:31 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/1934239584253952001],无参数
2025-06-15 21:20:31 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/1934239584253952001],耗时:[10]毫秒
2025-06-15 21:20:34 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dict/type],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","dictId":"1934239584253952001","dictName":"库存状态类型","remark":"库存状态类型"}]
2025-06-15 21:20:34 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dict/type],耗时:[33]毫秒
2025-06-15 21:20:34 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-15 21:20:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[14]毫秒
2025-06-15 21:20:35 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:20:35 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[12]毫秒
2025-06-15 21:20:35 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/1934239584253952001],无参数
2025-06-15 21:20:35 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/1934239584253952001],耗时:[11]毫秒
2025-06-15 21:21:04 [boundedElastic-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"green","dictLabel":"可用","dictValue":"0","dictSort":0,"remark":"可用"}]
2025-06-15 21:21:04 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[30]毫秒
2025-06-15 21:21:04 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:21:04 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[13]毫秒
2025-06-15 21:21:22 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"orange","dictLabel":"待检","dictValue":"1","dictSort":1,"remark":"待检"}]
2025-06-15 21:21:22 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[26]毫秒
2025-06-15 21:21:22 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:21:22 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[14]毫秒
2025-06-15 21:21:44 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"冻结","dictValue":"2","dictSort":2,"remark":"冻结"}]
2025-06-15 21:21:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[29]毫秒
2025-06-15 21:21:44 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:21:44 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[12]毫秒
2025-06-15 21:22:00 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"退回冻结","dictValue":"3","dictSort":3,"remark":"退回冻结"}]
2025-06-15 21:22:01 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[37]毫秒
2025-06-15 21:22:01 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:22:01 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[17]毫秒
2025-06-15 21:22:27 [boundedElastic-26] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"primary","dictLabel":"在途","dictValue":"4","dictSort":4,"remark":"在途"}]
2025-06-15 21:22:27 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[26]毫秒
2025-06-15 21:22:27 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:22:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[13]毫秒
2025-06-15 21:22:56 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"收货冻结","dictValue":"5","dictSort":5,"remark":"收货冻结"}]
2025-06-15 21:22:57 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[28]毫秒
2025-06-15 21:22:57 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:22:57 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[16]毫秒
2025-06-15 21:23:18 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"废品","dictValue":"6","dictSort":6,"remark":"废品"}]
2025-06-15 21:23:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[30]毫秒
2025-06-15 21:23:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:23:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[13]毫秒
2025-06-15 21:23:31 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"不良","dictValue":"7","dictSort":7,"remark":"不良"}]
2025-06-15 21:23:31 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[31]毫秒
2025-06-15 21:23:31 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:23:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[11]毫秒
2025-06-15 21:23:46 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"info","dictLabel":"不参与核算","dictValue":"8","dictSort":8,"remark":"不参与核算"}]
2025-06-15 21:23:46 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[26]毫秒
2025-06-15 21:23:46 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-15 21:23:46 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[12]毫秒
2025-06-15 21:24:03 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-15 21:24:03 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[33]毫秒
2025-06-15 21:24:03 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:24:03 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[17]毫秒
2025-06-15 21:24:23 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1934177478799532034","dataName":"master","tableName":"wms_warehouse","tableComment":"仓库表","subTableName":null,"subTableFkName":null,"className":"WmsWarehouse","tplCategory":"crud","packageName":"org.dromara.system","moduleName":"system","businessName":"warehouse","functionName":"仓库","functionAuthor":"奚翔","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939010","tableId":"1934177478799532034","columnName":"warehouse_id","columnComment":"仓库ID","columnType":"bigint","javaType":"Long","javaField":"warehouseId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":"0","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":1,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseId","query":false,"increment":true,"id":"row_279"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939011","tableId":"1934177478799532034","columnName":"tenant_id","columnComment":"租户编号","columnType":"varchar(20)","javaType":"String","javaField":"tenantId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":2,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"TenantId","query":false,"increment":true,"id":"row_280"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939012","tableId":"1934177478799532034","columnName":"dept_id","columnComment":"部门ID","columnType":"bigint","javaType":"Long","javaField":"deptId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"DeptId","query":true,"increment":true,"id":"row_281"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939013","tableId":"1934177478799532034","columnName":"warehouse_number","columnComment":"仓库编码","columnType":"varchar(30)","javaType":"String","javaField":"warehouseNumber","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":4,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseNumber","query":true,"increment":true,"id":"row_282"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939014","tableId":"1934177478799532034","columnName":"warehouse_name","columnComment":"仓库名称","columnType":"varchar(30)","javaType":"String","javaField":"warehouseName","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":5,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseName","query":true,"increment":true,"id":"row_283"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939015","tableId":"1934177478799532034","columnName":"warehouse_type","columnComment":"仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）","columnType":"char(1)","javaType":"String","javaField":"warehouseType","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"","sort":6,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseType","query":true,"increment":true,"id":"row_284"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939016","tableId":"1934177478799532034","columnName":"warehouse_inventory_status","columnComment":"库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseInventoryStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"radio","dictType":"","sort":7,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseInventoryStatus","query":true,"increment":true,"id":"row_285"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939017","tableId":"1934177478799532034","columnName":"warehouse_receving_status","columnComment":"收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseRecevingStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"radio","dictType":"","sort":8,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseRecevingStatus","query":true,"increment":true,"id":"row_286"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939018","tableId":"1934177478799532034","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":9,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"CreateDept","query":false,"increment":true,"id":"row_287"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939019","tableId":"1934177478799532034","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":10,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateBy","query":false,"increment":true,"id":"row_288"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939020","tableId":"1934177478799532034","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":11,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateTime","query":false,"increment":true,"id":"row_289"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939021","tableId":"1934177478799532034","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":12,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateBy","query":false,"increment":true,"id":"row_290"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939022","tableId":"1934177478799532034","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":13,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateTime","query":false,"increment":true,"id":"row_291"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939023","tableId":"1934177478799532034","columnName":"remark","columnComment":"备注","columnType":"varchar(500)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"textarea","dictType":"","sort":14,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true,"capJavaField":"Remark","query":false,"increment":true,"id":"row_292"}],"options":"{\"parentMenuId\":\"1931961869597331458\",\"popupComponent\":\"drawer\",\"formComponent\":\"native\"}","menuIds":null,"parentMenuId":"1931961869597331458","parentMenuName":null,"crud":true,"tree":false,"popupComponent":"drawer","formComponent":"native","params":{"parentMenuId":"1931961869597331458","popupComponent":"drawer","formComponent":"native"}}]
2025-06-15 21:24:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[58]毫秒
2025-06-15 21:24:28 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/synchDb/1934177478799532034],无参数
2025-06-15 21:24:28 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/synchDb/1934177478799532034],耗时:[266]毫秒
2025-06-15 21:24:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:24:28 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[21]毫秒
2025-06-15 21:24:30 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-15 21:24:30 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[23]毫秒
2025-06-15 21:24:30 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:24:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[15]毫秒
2025-06-15 21:24:33 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1934177478799532034","dataName":"master","tableName":"wms_warehouse","tableComment":"仓库表","subTableName":null,"subTableFkName":null,"className":"WmsWarehouse","tplCategory":"crud","packageName":"org.dromara.system","moduleName":"system","businessName":"warehouse","functionName":"仓库","functionAuthor":"奚翔","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939010","tableId":"1934177478799532034","columnName":"warehouse_id","columnComment":"仓库ID","columnType":"bigint","javaType":"Long","javaField":"warehouseId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":"0","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":1,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseId","query":false,"increment":true,"id":"row_340"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939011","tableId":"1934177478799532034","columnName":"tenant_id","columnComment":"租户编号","columnType":"varchar(20)","javaType":"String","javaField":"tenantId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":2,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"TenantId","query":false,"increment":true,"id":"row_341"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939012","tableId":"1934177478799532034","columnName":"dept_id","columnComment":"部门ID","columnType":"bigint","javaType":"Long","javaField":"deptId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"DeptId","query":true,"increment":true,"id":"row_342"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939013","tableId":"1934177478799532034","columnName":"warehouse_number","columnComment":"仓库编码","columnType":"varchar(30)","javaType":"String","javaField":"warehouseNumber","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":4,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseNumber","query":true,"increment":true,"id":"row_343"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939014","tableId":"1934177478799532034","columnName":"warehouse_name","columnComment":"仓库名称","columnType":"varchar(30)","javaType":"String","javaField":"warehouseName","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":5,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseName","query":true,"increment":true,"id":"row_344"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939015","tableId":"1934177478799532034","columnName":"warehouse_type","columnComment":"仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）","columnType":"char(1)","javaType":"String","javaField":"warehouseType","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"","sort":6,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseType","query":true,"increment":true,"id":"row_345"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939016","tableId":"1934177478799532034","columnName":"warehouse_inventory_status","columnComment":"库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseInventoryStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"radio","dictType":"","sort":7,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseInventoryStatus","query":true,"increment":true,"id":"row_346"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939017","tableId":"1934177478799532034","columnName":"warehouse_receving_status","columnComment":"收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseRecevingStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"radio","dictType":"","sort":8,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseRecevingStatus","query":true,"increment":true,"id":"row_347"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939018","tableId":"1934177478799532034","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":9,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"CreateDept","query":false,"increment":true,"id":"row_348"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939019","tableId":"1934177478799532034","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":10,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateBy","query":false,"increment":true,"id":"row_349"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939020","tableId":"1934177478799532034","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":11,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateTime","query":false,"increment":true,"id":"row_350"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939021","tableId":"1934177478799532034","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":12,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateBy","query":false,"increment":true,"id":"row_351"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939022","tableId":"1934177478799532034","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":13,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateTime","query":false,"increment":true,"id":"row_352"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939023","tableId":"1934177478799532034","columnName":"remark","columnComment":"备注","columnType":"varchar(500)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"textarea","dictType":"","sort":14,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true,"capJavaField":"Remark","query":false,"increment":true,"id":"row_353"}],"options":"{\"parentMenuId\":\"1931961869597331458\",\"popupComponent\":\"drawer\",\"formComponent\":\"native\"}","menuIds":null,"parentMenuId":"1931961869597331458","parentMenuName":null,"crud":true,"tree":false,"popupComponent":"drawer","formComponent":"native","params":{"parentMenuId":"1931961869597331458","popupComponent":"drawer","formComponent":"native"}}]
2025-06-15 21:24:33 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[56]毫秒
2025-06-15 21:25:04 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/synchDb/1934177478799532034],无参数
2025-06-15 21:25:04 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/synchDb/1934177478799532034],耗时:[110]毫秒
2025-06-15 21:25:04 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:25:04 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[15]毫秒
2025-06-15 21:25:06 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-15 21:25:06 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[24]毫秒
2025-06-15 21:25:06 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:25:06 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[12]毫秒
2025-06-15 21:25:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1934177478799532034","dataName":"master","tableName":"wms_warehouse","tableComment":"仓库表","subTableName":null,"subTableFkName":null,"className":"WmsWarehouse","tplCategory":"crud","packageName":"org.dromara.system","moduleName":"system","businessName":"warehouse","functionName":"仓库","functionAuthor":"奚翔","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939010","tableId":"1934177478799532034","columnName":"warehouse_id","columnComment":"仓库ID","columnType":"bigint","javaType":"Long","javaField":"warehouseId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":"0","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":1,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseId","query":false,"increment":true,"id":"row_400"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939011","tableId":"1934177478799532034","columnName":"tenant_id","columnComment":"租户编号","columnType":"varchar(20)","javaType":"String","javaField":"tenantId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":2,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"TenantId","query":false,"increment":true,"id":"row_401"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939013","tableId":"1934177478799532034","columnName":"warehouse_number","columnComment":"仓库编码","columnType":"varchar(30)","javaType":"String","javaField":"warehouseNumber","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseNumber","query":true,"increment":true,"id":"row_402"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939014","tableId":"1934177478799532034","columnName":"warehouse_name","columnComment":"仓库名称","columnType":"varchar(30)","javaType":"String","javaField":"warehouseName","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":4,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseName","query":true,"increment":true,"id":"row_403"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939015","tableId":"1934177478799532034","columnName":"warehouse_type","columnComment":"仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）","columnType":"char(1)","javaType":"String","javaField":"warehouseType","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"","sort":5,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseType","query":true,"increment":true,"id":"row_404"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939016","tableId":"1934177478799532034","columnName":"warehouse_inventory_status","columnComment":"库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseInventoryStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"","sort":6,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseInventoryStatus","query":true,"increment":true,"id":"row_405"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939017","tableId":"1934177478799532034","columnName":"warehouse_receving_status","columnComment":"收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseRecevingStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"","sort":7,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseRecevingStatus","query":true,"increment":true,"id":"row_406"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939018","tableId":"1934177478799532034","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":8,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"CreateDept","query":false,"increment":true,"id":"row_407"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939019","tableId":"1934177478799532034","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":9,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateBy","query":false,"increment":true,"id":"row_408"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939020","tableId":"1934177478799532034","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateTime","query":false,"increment":true,"id":"row_409"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939021","tableId":"1934177478799532034","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":11,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateBy","query":false,"increment":true,"id":"row_410"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939022","tableId":"1934177478799532034","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":12,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateTime","query":false,"increment":true,"id":"row_411"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939023","tableId":"1934177478799532034","columnName":"remark","columnComment":"备注","columnType":"varchar(500)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"textarea","dictType":"","sort":13,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true,"capJavaField":"Remark","query":false,"increment":true,"id":"row_412"}],"options":"{\"parentMenuId\":\"1931961869597331458\",\"popupComponent\":\"drawer\",\"formComponent\":\"native\"}","menuIds":null,"parentMenuId":"1931961869597331458","parentMenuName":null,"crud":true,"tree":false,"popupComponent":"drawer","formComponent":"native","params":{"parentMenuId":"1931961869597331458","popupComponent":"drawer","formComponent":"native"}}]
2025-06-15 21:25:30 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[46]毫秒
2025-06-15 21:25:34 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:25:34 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[22]毫秒
2025-06-15 21:25:34 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:25:34 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-15 21:25:34 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:25:34 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:25:34 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[8]毫秒
2025-06-15 21:25:34 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-06-15 21:25:34 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:25:34 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[18]毫秒
2025-06-15 21:25:34 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:25:34 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[16]毫秒
2025-06-15 21:25:36 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-15 21:25:36 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[13]毫秒
2025-06-15 21:25:37 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-15 21:25:37 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[18]毫秒
2025-06-15 21:25:37 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:25:37 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[13]毫秒
2025-06-15 21:26:01 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1934177478799532034","dataName":"master","tableName":"wms_warehouse","tableComment":"仓库表","subTableName":null,"subTableFkName":null,"className":"WmsWarehouse","tplCategory":"crud","packageName":"org.dromara.system","moduleName":"system","businessName":"warehouse","functionName":"仓库","functionAuthor":"奚翔","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939010","tableId":"1934177478799532034","columnName":"warehouse_id","columnComment":"仓库ID","columnType":"bigint","javaType":"Long","javaField":"warehouseId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":"0","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":1,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseId","query":false,"increment":true,"id":"row_69"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939011","tableId":"1934177478799532034","columnName":"tenant_id","columnComment":"租户编号","columnType":"varchar(20)","javaType":"String","javaField":"tenantId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":2,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"TenantId","query":false,"increment":true,"id":"row_70"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939013","tableId":"1934177478799532034","columnName":"warehouse_number","columnComment":"仓库编码","columnType":"varchar(30)","javaType":"String","javaField":"warehouseNumber","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseNumber","query":true,"increment":true,"id":"row_71"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939014","tableId":"1934177478799532034","columnName":"warehouse_name","columnComment":"仓库名称","columnType":"varchar(30)","javaType":"String","javaField":"warehouseName","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":4,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseName","query":true,"increment":true,"id":"row_72"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939015","tableId":"1934177478799532034","columnName":"warehouse_type","columnComment":"仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）","columnType":"char(1)","javaType":"String","javaField":"warehouseType","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"wms_warehouse_type","sort":5,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseType","query":true,"increment":true,"id":"row_73"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939016","tableId":"1934177478799532034","columnName":"warehouse_inventory_status","columnComment":"库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseInventoryStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"wms_warehouse_instocktype","sort":6,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseInventoryStatus","query":true,"increment":true,"id":"row_74"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939017","tableId":"1934177478799532034","columnName":"warehouse_receving_status","columnComment":"收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseRecevingStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"wms_warehouse_instocktype","sort":7,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseRecevingStatus","query":true,"increment":true,"id":"row_75"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939018","tableId":"1934177478799532034","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":8,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"CreateDept","query":false,"increment":true,"id":"row_76"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939019","tableId":"1934177478799532034","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":9,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateBy","query":false,"increment":true,"id":"row_77"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939020","tableId":"1934177478799532034","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateTime","query":false,"increment":true,"id":"row_78"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939021","tableId":"1934177478799532034","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":11,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateBy","query":false,"increment":true,"id":"row_79"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939022","tableId":"1934177478799532034","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":12,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateTime","query":false,"increment":true,"id":"row_80"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939023","tableId":"1934177478799532034","columnName":"remark","columnComment":"备注","columnType":"varchar(500)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"textarea","dictType":"","sort":13,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true,"capJavaField":"Remark","query":false,"increment":true,"id":"row_81"}],"options":"{\"parentMenuId\":\"1931961869597331458\",\"popupComponent\":\"drawer\",\"formComponent\":\"native\"}","menuIds":null,"parentMenuId":"1931961869597331458","parentMenuName":null,"crud":true,"tree":false,"popupComponent":"drawer","formComponent":"native","params":{"parentMenuId":"1931961869597331458","popupComponent":"drawer","formComponent":"native"}}]
2025-06-15 21:26:01 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[42]毫秒
2025-06-15 21:26:04 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/batchGenCode],参数类型[param],参数:[{"tableIdStr":["1934177478799532034"]}]
2025-06-15 21:26:04 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/batchGenCode],耗时:[151]毫秒
2025-06-15 21:26:18 [boundedElastic-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 21:26:18 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:26:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[9]毫秒
2025-06-15 21:26:18 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[9]毫秒
2025-06-15 21:26:18 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:26:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[17]毫秒
2025-06-15 21:26:45 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:26:45 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:26:45 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[13]毫秒
2025-06-15 21:26:45 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-06-15 21:26:47 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:26:47 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[8]毫秒
2025-06-15 21:26:47 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:26:47 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[13]毫秒
2025-06-15 21:26:55 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:26:55 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[7]毫秒
2025-06-15 21:26:55 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:26:55 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[16]毫秒
2025-06-15 21:26:58 [boundedElastic-30] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:26:58 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[25]毫秒
2025-06-15 21:26:58 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:26:58 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-15 21:26:59 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:26:59 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:26:59 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[7]毫秒
2025-06-15 21:26:59 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[18]毫秒
2025-06-15 21:26:59 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:26:59 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[17]毫秒
2025-06-15 21:26:59 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:26:59 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-15 21:27:04 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:27:04 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[7]毫秒
2025-06-15 21:27:04 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:27:04 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[13]毫秒
2025-06-15 21:27:06 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-15 21:27:06 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[13]毫秒
2025-06-15 21:27:07 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-15 21:27:07 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[17]毫秒
2025-06-15 21:27:07 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:27:07 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[11]毫秒
2025-06-15 21:27:38 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1934177478799532034","dataName":"master","tableName":"wms_warehouse","tableComment":"仓库表","subTableName":null,"subTableFkName":null,"className":"WmsWarehouse","tplCategory":"crud","packageName":"org.dromara.wms","moduleName":"wms","businessName":"warehouse","functionName":"仓库","functionAuthor":"奚翔","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939010","tableId":"1934177478799532034","columnName":"warehouse_id","columnComment":"仓库ID","columnType":"bigint","javaType":"Long","javaField":"warehouseId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":"0","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":1,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseId","query":false,"increment":true,"id":"row_105"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939011","tableId":"1934177478799532034","columnName":"tenant_id","columnComment":"租户编号","columnType":"varchar(20)","javaType":"String","javaField":"tenantId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":2,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"TenantId","query":false,"increment":true,"id":"row_106"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939013","tableId":"1934177478799532034","columnName":"warehouse_number","columnComment":"仓库编码","columnType":"varchar(30)","javaType":"String","javaField":"warehouseNumber","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseNumber","query":true,"increment":true,"id":"row_107"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939014","tableId":"1934177478799532034","columnName":"warehouse_name","columnComment":"仓库名称","columnType":"varchar(30)","javaType":"String","javaField":"warehouseName","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":4,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseName","query":true,"increment":true,"id":"row_108"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939015","tableId":"1934177478799532034","columnName":"warehouse_type","columnComment":"仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）","columnType":"char(1)","javaType":"String","javaField":"warehouseType","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"wms_warehouse_type","sort":5,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseType","query":true,"increment":true,"id":"row_109"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939016","tableId":"1934177478799532034","columnName":"warehouse_inventory_status","columnComment":"库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseInventoryStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"wms_warehouse_instocktype","sort":6,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseInventoryStatus","query":true,"increment":true,"id":"row_110"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939017","tableId":"1934177478799532034","columnName":"warehouse_receving_status","columnComment":"收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）","columnType":"char(1)","javaType":"String","javaField":"warehouseRecevingStatus","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"wms_warehouse_instocktype","sort":7,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false,"capJavaField":"WarehouseRecevingStatus","query":true,"increment":true,"id":"row_111"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939018","tableId":"1934177478799532034","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":8,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false,"capJavaField":"CreateDept","query":false,"increment":true,"id":"row_112"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939019","tableId":"1934177478799532034","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":9,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateBy","query":false,"increment":true,"id":"row_113"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939020","tableId":"1934177478799532034","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"CreateTime","query":false,"increment":true,"id":"row_114"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939021","tableId":"1934177478799532034","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"input","dictType":"","sort":11,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateBy","query":false,"increment":true,"id":"row_115"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939022","tableId":"1934177478799532034","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"0","isEdit":"0","isList":"0","isQuery":"0","queryType":"EQ","htmlType":"datetime","dictType":"","sort":12,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false,"capJavaField":"UpdateTime","query":false,"increment":true,"id":"row_116"},{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"columnId":"1934177479088939023","tableId":"1934177478799532034","columnName":"remark","columnComment":"备注","columnType":"varchar(500)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"0","queryType":"EQ","htmlType":"textarea","dictType":"","sort":13,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true,"capJavaField":"Remark","query":false,"increment":true,"id":"row_117"}],"options":"{\"parentMenuId\":\"1931961869597331458\",\"popupComponent\":\"drawer\",\"formComponent\":\"native\"}","menuIds":null,"parentMenuId":"1931961869597331458","parentMenuName":null,"crud":true,"tree":false,"popupComponent":"drawer","formComponent":"native","params":{"parentMenuId":"1931961869597331458","popupComponent":"drawer","formComponent":"native"}}]
2025-06-15 21:27:38 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[42]毫秒
2025-06-15 21:27:41 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/batchGenCode],参数类型[param],参数:[{"tableIdStr":["1934177478799532034"]}]
2025-06-15 21:27:41 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/batchGenCode],耗时:[48]毫秒
2025-06-15 21:29:36 [boundedElastic-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:29:36 [boundedElastic-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:29:36 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-15 21:29:36 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-15 21:29:38 [boundedElastic-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:29:38 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-15 21:29:38 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:29:38 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-15 21:29:39 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:29:39 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:29:39 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:29:39 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-06-15 21:29:39 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-15 21:29:39 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-15 21:29:39 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:29:39 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-15 21:29:50 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:29:50 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-15 21:29:50 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-15 21:29:50 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:29:50 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-15 21:29:50 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[7]毫秒
2025-06-15 21:29:50 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[7]毫秒
2025-06-15 21:29:50 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-15 21:29:50 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:29:50 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[19]毫秒
2025-06-15 21:29:53 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:29:53 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-15 21:29:53 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:29:53 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[16]毫秒
2025-06-15 21:29:54 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:29:54 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:29:54 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 21:29:54 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-15 21:29:54 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-15 21:29:54 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[6]毫秒
2025-06-15 21:29:54 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[6]毫秒
2025-06-15 21:29:54 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[6]毫秒
2025-06-15 21:29:54 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-15 21:29:54 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-15 21:29:54 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:29:54 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[18]毫秒
2025-06-15 21:29:54 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:29:54 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-15 21:29:57 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 21:29:57 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[28]毫秒
2025-06-15 21:30:07 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-15 21:30:07 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-15 21:30:07 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[168]毫秒
2025-06-15 21:30:07 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[337]毫秒
2025-06-15 21:30:09 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/preview/1934177478799532034],无参数
2025-06-15 21:30:09 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/preview/1934177478799532034],耗时:[212]毫秒
2025-06-15 21:30:17 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/batchGenCode],参数类型[param],参数:[{"tableIdStr":["1934177478799532034"]}]
2025-06-15 21:30:17 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/batchGenCode],耗时:[106]毫秒
2025-06-15 21:30:59 [reactor-http-nio-2] WARN  reactor.netty.channel.FluxReceive - [10fd16d8-1, L:/[0:0:0:0:0:0:0:1]:8080 - R:/[0:0:0:0:0:0:0:1]:57054] An exception has been observed post termination, use DEBUG level to see the full stack: java.net.SocketException: Connection reset
2025-06-15 21:40:30 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 21:40:30 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 21:40:30 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 21:43:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:43:47 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 48904 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:43:47 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-15 21:43:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-15 21:43:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:43:49 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:43:49 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:43:49 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:43:49 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:43:49 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:44:10 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway *************:8080 register finished
2025-06-15 21:44:10 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-15 21:44:10 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 25.666 seconds (process running for 26.429)
2025-06-15 21:44:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-15 21:44:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:47:00 [boundedElastic-23] ERROR c.a.c.n.d.NacosDiscoveryClient - get service name from nacos server failed.
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:70)
	at com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient.getServices(NacosDiscoveryClient.java:80)
	at org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClient.getServices(CompositeDiscoveryClient.java:68)
	at org.springframework.cloud.client.discovery.health.DiscoveryClientHealthIndicator.health(DiscoveryClientHealthIndicator.java:73)
	at reactor.core.publisher.MonoCallable.call(MonoCallable.java:72)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:228)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 21:47:10 [boundedElastic-23] ERROR c.a.c.n.d.r.NacosReactiveDiscoveryClient - get services from nacos server fail,
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:70)
	at com.alibaba.cloud.nacos.discovery.reactive.NacosReactiveDiscoveryClient.lambda$getServices$3(NacosReactiveDiscoveryClient.java:87)
	at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:46)
	at reactor.core.publisher.FluxSubscribeOn$SubscribeOnSubscriber.run(FluxSubscribeOn.java:194)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 21:47:10 [boundedElastic-17] ERROR c.a.c.n.d.r.NacosReactiveDiscoveryClient - get services from nacos server fail,
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:70)
	at com.alibaba.cloud.nacos.discovery.reactive.NacosReactiveDiscoveryClient.lambda$getServices$3(NacosReactiveDiscoveryClient.java:87)
	at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:46)
	at reactor.core.publisher.FluxSubscribeOn$SubscribeOnSubscriber.run(FluxSubscribeOn.java:194)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 21:55:25 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:55:25 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[73]毫秒
2025-06-15 21:55:25 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:55:25 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-15 21:55:25 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:55:25 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:55:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[11]毫秒
2025-06-15 21:55:26 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[942]毫秒
2025-06-15 21:55:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:55:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:55:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-15 21:55:28 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-15 21:55:34 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:55:34 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 21:55:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[8]毫秒
2025-06-15 21:55:34 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[8]毫秒
2025-06-15 21:55:34 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:55:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[13]毫秒
2025-06-15 21:55:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:55:38 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-15 21:55:38 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:55:38 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-15 21:55:38 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:55:38 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:55:38 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 21:55:38 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[7]毫秒
2025-06-15 21:55:38 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-15 21:55:38 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-15 21:55:38 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:55:38 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[13]毫秒
2025-06-15 21:55:38 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:55:38 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[11]毫秒
2025-06-15 21:57:41 [boundedElastic-33] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 21:57:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[32]毫秒
2025-06-15 21:57:41 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 21:57:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-15 21:57:41 [boundedElastic-33] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 21:57:41 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 21:57:41 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 21:57:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[6]毫秒
2025-06-15 21:57:41 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[7]毫秒
2025-06-15 21:57:41 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[18]毫秒
2025-06-15 21:57:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 21:57:42 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[18]毫秒
2025-06-15 21:57:42 [boundedElastic-33] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMN2Y1Z05OZFBXVVpLb0NJalduUzlCMlU3a3o3QzM0OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.sDBEaGq-ZXAXaPc6ekhSCI-rNUFYqwLz1FlGNG76v1g"]}]
2025-06-15 21:57:42 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-15 21:57:44 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-15 21:57:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-15 21:57:44 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-15 21:57:44 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[10]毫秒
2025-06-15 21:57:44 [boundedElastic-33] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-wms
2025-06-15 21:57:44 [boundedElastic-33] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/wms/warehouse/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-wms"
2025-06-15 22:34:20 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:34:20 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:34:20 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=[240e:3a1:bc85:beb1:187:bb4c:9c3e:8a7b], username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:34:20 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:43:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:43:19 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 47648 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:43:19 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-15 22:43:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-15 22:43:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:43:20 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:43:21 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:43:21 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:43:21 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:43:21 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:43:41 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway *************:8080 register finished
2025-06-15 22:43:41 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-15 22:43:41 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 25.692 seconds (process running for 26.305)
2025-06-15 22:43:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-15 22:43:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 22:53:39 [boundedElastic-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-15 22:53:39 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[138]毫秒
2025-06-15 22:53:39 [boundedElastic-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-15 22:53:39 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[99]毫秒
2025-06-15 22:53:40 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-15 22:53:40 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:53:40 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[218]毫秒
2025-06-15 22:53:40 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[452]毫秒
2025-06-15 22:53:45 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-15 22:53:46 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1097]毫秒
2025-06-15 22:53:46 [boundedElastic-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 22:53:47 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[808]毫秒
2025-06-15 22:53:47 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 22:53:47 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[54]毫秒
2025-06-15 22:53:48 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:53:48 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-06-15 22:53:48 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 22:53:48 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[60]毫秒
2025-06-15 22:53:50 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-15 22:53:50 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-15 22:53:50 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[24]毫秒
2025-06-15 22:53:50 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[25]毫秒
2025-06-15 22:53:50 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 22:53:51 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1115]毫秒
2025-06-15 22:54:03 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-15 22:54:03 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[77]毫秒
2025-06-15 22:54:07 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931682136745902081],无参数
2025-06-15 22:54:07 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931682136745902081],耗时:[21]毫秒
2025-06-15 22:54:33 [boundedElastic-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/cache],无参数
2025-06-15 22:54:33 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/cache],耗时:[39]毫秒
2025-06-15 22:56:00 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 22:56:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[37]毫秒
2025-06-15 22:56:00 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 22:56:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-06-15 22:56:01 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:56:01 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/cache],无参数
2025-06-15 22:56:01 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 22:56:01 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/cache],耗时:[17]毫秒
2025-06-15 22:56:01 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[22]毫秒
2025-06-15 22:56:01 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[42]毫秒
2025-06-15 22:57:28 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 22:57:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[37]毫秒
2025-06-15 22:57:28 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 22:57:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[20]毫秒
2025-06-15 22:57:28 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:57:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/cache],无参数
2025-06-15 22:57:28 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 22:57:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/cache],耗时:[16]毫秒
2025-06-15 22:57:28 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[13]毫秒
2025-06-15 22:57:28 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[36]毫秒
2025-06-15 22:58:00 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 22:58:00 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[32]毫秒
2025-06-15 22:58:00 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 22:58:00 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[23]毫秒
2025-06-15 22:58:01 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:58:01 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/cache],无参数
2025-06-15 22:58:01 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 22:58:01 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/cache],耗时:[10]毫秒
2025-06-15 22:58:01 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[13]毫秒
2025-06-15 22:58:01 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-06-15 22:58:41 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 22:58:41 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[32]毫秒
2025-06-15 22:58:41 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 22:58:41 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[20]毫秒
2025-06-15 22:58:41 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:58:41 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/cache],无参数
2025-06-15 22:58:41 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/cache],耗时:[13]毫秒
2025-06-15 22:58:41 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 22:58:41 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-06-15 22:58:41 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-15 22:59:11 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 22:59:11 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[27]毫秒
2025-06-15 22:59:11 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 22:59:11 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[21]毫秒
2025-06-15 22:59:11 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 22:59:11 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/cache],无参数
2025-06-15 22:59:11 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/cache],耗时:[11]毫秒
2025-06-15 22:59:11 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 22:59:11 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-15 22:59:11 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-06-15 22:59:31 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 22:59:31 [boundedElastic-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 22:59:31 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[13]毫秒
2025-06-15 22:59:31 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[14]毫秒
2025-06-15 22:59:32 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 22:59:32 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[34]毫秒
2025-06-15 22:59:43 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 22:59:43 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[19]毫秒
2025-06-15 22:59:43 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/111],无参数
2025-06-15 22:59:43 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/111],耗时:[18]毫秒
2025-06-15 23:01:31 [boundedElastic-27] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 23:01:31 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[26]毫秒
2025-06-15 23:01:31 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/111],无参数
2025-06-15 23:01:31 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/111],耗时:[11]毫秒
2025-06-15 23:11:57 [boundedElastic-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 23:11:57 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[31]毫秒
2025-06-15 23:11:57 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 23:11:57 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-15 23:11:58 [boundedElastic-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 23:11:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 23:11:58 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 23:11:58 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[8]毫秒
2025-06-15 23:11:58 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[9]毫秒
2025-06-15 23:11:58 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-06-15 23:11:58 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 23:11:58 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[19]毫秒
2025-06-15 23:11:58 [boundedElastic-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 23:11:58 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[12]毫秒
2025-06-15 23:12:52 [boundedElastic-32] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 23:12:52 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[31]毫秒
2025-06-15 23:12:52 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 23:12:52 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[17]毫秒
2025-06-15 23:12:52 [boundedElastic-32] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 23:12:52 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-15 23:12:52 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-15 23:12:52 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[7]毫秒
2025-06-15 23:12:52 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-15 23:12:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-06-15 23:12:52 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-15 23:12:52 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[19]毫秒
2025-06-15 23:12:52 [boundedElastic-32] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJoSE9TRllrQmFYc2tqcEtzV1V2bWxxejBuNWh3dTZPTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.wFprRX-tRAvNr7gP0RkvgsutSRQjYKaPeSBq-eEnRmI"]}]
2025-06-15 23:12:52 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[11]毫秒
2025-06-15 23:13:14 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-15 23:13:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-15 23:13:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-15 23:13:14 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[10]毫秒
2025-06-15 23:13:14 [boundedElastic-33] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:13:14 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[28]毫秒
2025-06-15 23:13:17 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-15 23:13:17 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[17]毫秒
2025-06-15 23:13:20 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931682136745902081],无参数
2025-06-15 23:13:20 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931682136745902081],耗时:[20]毫秒
2025-06-15 23:13:30 [boundedElastic-31] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],无参数
2025-06-15 23:13:31 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],耗时:[34]毫秒
2025-06-15 23:13:31 [boundedElastic-31] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:13:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[926]毫秒
2025-06-15 23:13:37 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /resource/oss/1931959306865008642],无参数
2025-06-15 23:13:37 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /resource/oss/1931959306865008642],耗时:[410]毫秒
2025-06-15 23:13:37 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:13:37 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[18]毫秒
2025-06-15 23:13:50 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /resource/oss/upload],无参数
2025-06-15 23:13:50 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /resource/oss/upload],耗时:[208]毫秒
2025-06-15 23:13:53 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:13:53 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[29]毫秒
2025-06-15 23:13:58 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:13:58 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[36]毫秒
2025-06-15 23:13:59 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:13:59 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[25]毫秒
2025-06-15 23:14:07 [boundedElastic-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/download/1934268116996808705],无参数
2025-06-15 23:14:07 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/download/1934268116996808705],耗时:[136]毫秒
2025-06-15 23:14:27 [boundedElastic-35] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_device_type],无参数
2025-06-15 23:14:27 [boundedElastic-27] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_grant_type],无参数
2025-06-15 23:14:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_device_type],耗时:[24]毫秒
2025-06-15 23:14:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_grant_type],耗时:[24]毫秒
2025-06-15 23:14:27 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/client/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:14:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/client/list],耗时:[57]毫秒
2025-06-15 23:14:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-06-15 23:14:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[13]毫秒
2025-06-15 23:14:32 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:14:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[39]毫秒
2025-06-15 23:14:32 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-06-15 23:14:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[13]毫秒
2025-06-15 23:14:36 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/notice/2],无参数
2025-06-15 23:14:36 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/notice/2],耗时:[68]毫秒
2025-06-15 23:14:36 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:14:36 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[18]毫秒
2025-06-15 23:14:38 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/notice/1],无参数
2025-06-15 23:14:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/notice/1],耗时:[22]毫秒
2025-06-15 23:14:38 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:14:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[11]毫秒
2025-06-15 23:14:40 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:14:40 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[13]毫秒
2025-06-15 23:14:42 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-15 23:14:42 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 23:14:42 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-15 23:14:42 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[8]毫秒
2025-06-15 23:14:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[8]毫秒
2025-06-15 23:14:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[26]毫秒
2025-06-15 23:14:42 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:14:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[23]毫秒
2025-06-15 23:14:44 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["103"]}]
2025-06-15 23:14:44 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[31]毫秒
2025-06-15 23:15:37 [boundedElastic-33] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["103"]}]
2025-06-15 23:15:37 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[21]毫秒
2025-06-15 23:15:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-15 23:15:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[36]毫秒
2025-06-15 23:15:41 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-15 23:15:41 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[13]毫秒
2025-06-15 23:15:44 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/dept/103],无参数
2025-06-15 23:15:44 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/dept/103],耗时:[32]毫秒
2025-06-15 23:15:48 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-15 23:15:48 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-15 23:15:48 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:15:48 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[24]毫秒
2025-06-15 23:15:50 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"belongDeptId":["103"]}]
2025-06-15 23:15:50 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[23]毫秒
2025-06-15 23:15:53 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/post/1],无参数
2025-06-15 23:15:53 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/post/1],耗时:[23]毫秒
2025-06-15 23:16:25 [boundedElastic-38] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/dept/103],无参数
2025-06-15 23:16:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/dept/103],耗时:[39]毫秒
2025-06-15 23:16:25 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-15 23:16:25 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[10]毫秒
2025-06-15 23:16:26 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/dept/101],无参数
2025-06-15 23:16:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/dept/101],耗时:[40]毫秒
2025-06-15 23:16:26 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-15 23:16:26 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[11]毫秒
2025-06-15 23:16:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/100],无参数
2025-06-15 23:16:28 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/100],耗时:[8]毫秒
2025-06-15 23:16:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list/dept/100],无参数
2025-06-15 23:16:28 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list/dept/100],耗时:[32]毫秒
2025-06-15 23:16:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list/exclude/100],无参数
2025-06-15 23:16:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list/exclude/100],耗时:[13]毫秒
2025-06-15 23:16:40 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dept],参数类型[json],参数:[{"status":"0","deptId":100,"parentId":0,"deptName":"WMS系统管理","orderNum":0,"phone":"15888888888","email":"<EMAIL>"}]
2025-06-15 23:16:40 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dept],耗时:[88]毫秒
2025-06-15 23:16:40 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-15 23:16:40 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[11]毫秒
2025-06-15 23:19:32 [boundedElastic-42] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931682136745902081],无参数
2025-06-15 23:19:32 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931682136745902081],耗时:[19]毫秒
2025-06-15 23:19:36 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-15 23:19:36 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[17]毫秒
2025-06-15 23:45:59 [boundedElastic-60] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_oper_type],无参数
2025-06-15 23:45:59 [boundedElastic-66] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_common_status],无参数
2025-06-15 23:45:59 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_common_status],耗时:[17]毫秒
2025-06-15 23:45:59 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_oper_type],耗时:[19]毫秒
2025-06-15 23:45:59 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/operlog/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:45:59 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/operlog/list],耗时:[30]毫秒
2025-06-15 23:46:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/logininfor/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/logininfor/list],耗时:[19]毫秒
2025-06-15 23:46:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_yes_no],无参数
2025-06-15 23:46:23 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_yes_no],耗时:[7]毫秒
2025-06-15 23:46:23 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:23 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/list],耗时:[21]毫秒
2025-06-15 23:46:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:26 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[16]毫秒
2025-06-15 23:46:26 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[19]毫秒
2025-06-15 23:46:27 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-15 23:46:27 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[12]毫秒
2025-06-15 23:46:27 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:27 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[12]毫秒
2025-06-15 23:46:27 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[14]毫秒
2025-06-15 23:46:30 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-15 23:46:30 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[17]毫秒
2025-06-15 23:46:33 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-15 23:46:33 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-15 23:46:33 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[12]毫秒
2025-06-15 23:46:33 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[12]毫秒
2025-06-15 23:46:33 [boundedElastic-32] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:33 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[24]毫秒
2025-06-15 23:46:49 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:49 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-15 23:46:53 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-15 23:46:53 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[15]毫秒
2025-06-15 23:53:44 [boundedElastic-77] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-15 23:53:44 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[12]毫秒
2025-06-15 23:53:44 [boundedElastic-77] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-15 23:53:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[50]毫秒
2025-06-15 23:53:44 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-15 23:53:44 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 23:53:44 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-06-15 23:53:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[32]毫秒
2025-06-15 23:53:51 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-15 23:53:51 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[177]毫秒
2025-06-15 23:53:51 [boundedElastic-75] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-15 23:53:51 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-15 23:53:51 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-15 23:53:51 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-15 23:53:51 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-15 23:53:51 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-15 23:53:51 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-15 23:53:51 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
