2025-06-17 08:15:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-17 08:15:17 [main] INFO  o.d.system.RuoYiSystemApplication - Starting RuoYiSystemApplication using Java 17.0.14 with PID 37348 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-system\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:15:17 [main] INFO  o.d.system.RuoYiSystemApplication - The following 1 profile is active: "dev"
2025-06-17 08:15:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-17 08:15:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-17 08:15:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-17 08:15:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-17 08:15:25 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-17 08:15:25 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-17 08:15:25 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:15:25 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:15:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-17 08:15:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-17 08:15:26 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@44808c29
2025-06-17 08:15:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-17 08:15:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-17 08:15:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-17 08:15:27 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-17 08:15:32 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@343fddd9
2025-06-17 08:15:34 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-17 08:15:34 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-17 08:15:34 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-17 08:15:34 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-17 08:15:36 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-system 162.168.2.139:9201 register finished
2025-06-17 08:15:41 [main] INFO  o.d.system.RuoYiSystemApplication - Started RuoYiSystemApplication in 27.451 seconds (process running for 28.405)
2025-06-17 08:15:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-17 08:15:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-17 08:15:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
2025-06-17 08:15:42 [RMI TCP Connection(5)-162.168.2.139] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 08:18:53 [DubboServerHandler-162.168.2.139:20880-thread-8] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 08:18:53 [DubboServerHandler-162.168.2.139:20880-thread-8] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[290ms]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[33ms]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[6ms]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[86ms]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 08:19:08 [DubboServerHandler-162.168.2.139:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[19ms]
2025-06-17 08:19:09 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-17 08:19:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 08:19:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 08:19:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 08:19:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-06-17 08:19:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 08:19:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 08:19:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 08:19:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 08:23:23 [DubboServerHandler-162.168.2.139:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:23:23 [DubboServerHandler-162.168.2.139:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-17 08:23:23 [DubboServerHandler-162.168.2.139:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 08:23:23 [DubboServerHandler-162.168.2.139:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-17 08:23:24 [DubboServerHandler-162.168.2.139:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 08:23:24 [DubboServerHandler-162.168.2.139:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[17ms]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[5ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 08:23:30 [DubboServerHandler-162.168.2.139:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 09:13:28 [DubboServerHandler-162.168.2.139:20880-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:13:28 [DubboServerHandler-162.168.2.139:20880-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-17 09:13:35 [DubboServerHandler-162.168.2.139:20880-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 09:13:35 [DubboServerHandler-162.168.2.139:20880-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-17 09:13:35 [DubboServerHandler-162.168.2.139:20880-thread-26] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 09:13:35 [DubboServerHandler-162.168.2.139:20880-thread-26] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[15ms]
2025-06-17 09:13:35 [DubboServerHandler-162.168.2.139:20880-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 09:13:35 [DubboServerHandler-162.168.2.139:20880-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 09:13:36 [DubboServerHandler-162.168.2.139:20880-thread-28] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 09:13:36 [DubboServerHandler-162.168.2.139:20880-thread-28] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-17 09:13:36 [DubboServerHandler-162.168.2.139:20880-thread-29] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 09:13:36 [DubboServerHandler-162.168.2.139:20880-thread-29] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[5ms]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 09:13:36 [DubboServerHandler-162.168.2.139:20880-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:13:36 [DubboServerHandler-162.168.2.139:20880-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-17 09:16:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 09:16:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 09:16:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 09:16:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 09:16:21 [DubboServerHandler-162.168.2.139:20880-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:16:21 [DubboServerHandler-162.168.2.139:20880-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-17 09:17:05 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 09:17:05 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 09:17:05 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 09:17:05 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 09:17:05 [DubboServerHandler-162.168.2.139:20880-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:17:05 [DubboServerHandler-162.168.2.139:20880-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-17 10:28:35 [DubboServerHandler-162.168.2.139:20880-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 10:28:35 [DubboServerHandler-162.168.2.139:20880-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-34] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-34] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-35] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-35] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[14ms]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-36] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-36] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-37] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-37] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-38] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-38] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[4ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-39] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 10:28:44 [DubboServerHandler-162.168.2.139:20880-thread-39] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-17 10:31:35 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 10:31:35 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 10:31:35 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 10:31:35 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-17 10:31:36 [DubboServerHandler-162.168.2.139:20880-thread-40] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 10:31:36 [DubboServerHandler-162.168.2.139:20880-thread-40] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-17 11:00:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 11:00:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 11:00:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 11:00:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 11:00:20 [DubboServerHandler-162.168.2.139:20880-thread-41] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 11:00:20 [DubboServerHandler-162.168.2.139:20880-thread-41] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 15:01:16 [DubboServerHandler-162.168.2.139:20880-thread-42] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:01:16 [DubboServerHandler-162.168.2.139:20880-thread-42] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-43] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-43] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-44] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-44] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[15ms]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-45] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-45] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-46] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-46] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-47] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-47] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[4ms]
2025-06-17 15:01:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:01:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-48] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:01:25 [DubboServerHandler-162.168.2.139:20880-thread-48] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-17 15:01:38 [DubboServerHandler-162.168.2.139:20880-thread-49] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:38 [DubboServerHandler-162.168.2.139:20880-thread-49] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 15:01:38 [DubboServerHandler-162.168.2.139:20880-thread-50] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:01:38 [DubboServerHandler-162.168.2.139:20880-thread-50] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-06-17 15:01:38 [DubboServerHandler-162.168.2.139:20880-thread-51] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:01:38 [DubboServerHandler-162.168.2.139:20880-thread-51] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[3ms]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-52] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-52] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-53] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-53] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[2ms]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-54] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-54] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[23ms]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-55] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-55] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-56] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-56] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-57] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 15:01:44 [DubboServerHandler-162.168.2.139:20880-thread-57] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[5ms]
2025-06-17 15:01:44 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:44 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:44 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:01:44 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:01:49 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:49 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:49 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:01:49 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 15:01:53 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:53 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:53 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:01:53 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:01:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:01:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:01:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:02:05 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:02:05 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:02:05 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:02:05 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:02:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:02:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:02:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:02:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-17 15:05:43 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:43 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:43 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 15:05:43 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 15:05:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:05:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:05:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:05:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 15:05:59 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:59 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:05:59 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:05:59 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:06:07 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:06:07 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:06:07 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:06:07 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 15:12:06 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:06 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:06 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:12:06 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:12:10 [DubboServerHandler-162.168.2.139:20880-thread-58] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:12:10 [DubboServerHandler-162.168.2.139:20880-thread-58] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-17 15:12:10 [DubboServerHandler-162.168.2.139:20880-thread-59] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:12:10 [DubboServerHandler-162.168.2.139:20880-thread-59] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-17 15:12:10 [DubboServerHandler-162.168.2.139:20880-thread-60] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:10 [DubboServerHandler-162.168.2.139:20880-thread-60] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[4ms]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-61] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-61] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-62] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-62] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[13ms]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-63] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-63] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-64] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-64] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-65] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-65] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[4ms]
2025-06-17 15:12:16 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:16 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:16 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:12:16 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-66] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:16 [DubboServerHandler-162.168.2.139:20880-thread-66] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-17 15:12:19 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:19 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:19 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:12:19 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-17 15:12:19 [DubboServerHandler-162.168.2.139:20880-thread-67] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:19 [DubboServerHandler-162.168.2.139:20880-thread-67] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-17 15:12:22 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:22 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-17 15:12:22 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-17 15:12:22 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-17 15:12:22 [DubboServerHandler-162.168.2.139:20880-thread-68] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:22 [DubboServerHandler-162.168.2.139:20880-thread-68] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-17 15:12:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
