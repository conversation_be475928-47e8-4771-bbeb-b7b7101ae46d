2025-06-16 00:05:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:05:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:05:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 00:05:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 00:05:08 [DubboServerHandler-192.168.0.112:20880-thread-37] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:08 [DubboServerHandler-192.168.0.112:20880-thread-37] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:05:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:05:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:05:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-16 00:05:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 00:05:28 [DubboServerHandler-192.168.0.112:20880-thread-38] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:28 [DubboServerHandler-192.168.0.112:20880-thread-38] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-16 00:05:32 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:05:32 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:05:32 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-16 00:05:32 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 00:05:33 [DubboServerHandler-192.168.0.112:20880-thread-39] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:33 [DubboServerHandler-192.168.0.112:20880-thread-39] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-16 00:05:55 [DubboServerHandler-192.168.0.112:20880-thread-40] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:05:55 [DubboServerHandler-192.168.0.112:20880-thread-40] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-16 00:05:55 [DubboServerHandler-192.168.0.112:20880-thread-41] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 00:05:55 [DubboServerHandler-192.168.0.112:20880-thread-41] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-16 00:05:55 [DubboServerHandler-192.168.0.112:20880-thread-42] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:55 [DubboServerHandler-192.168.0.112:20880-thread-42] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[3ms]
2025-06-16 00:09:28 [DubboServerHandler-192.168.0.112:20880-thread-43] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:09:28 [DubboServerHandler-192.168.0.112:20880-thread-43] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 00:10:12 [DubboServerHandler-192.168.0.112:20880-thread-44] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:10:12 [DubboServerHandler-192.168.0.112:20880-thread-44] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[3ms]
2025-06-16 00:10:17 [DubboServerHandler-192.168.0.112:20880-thread-45] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:10:17 [DubboServerHandler-192.168.0.112:20880-thread-45] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[4ms]
2025-06-16 00:10:20 [DubboServerHandler-192.168.0.112:20880-thread-46] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:10:20 [DubboServerHandler-192.168.0.112:20880-thread-46] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-16 00:11:11 [DubboServerHandler-192.168.0.112:20880-thread-47] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:11 [DubboServerHandler-192.168.0.112:20880-thread-47] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-16 00:11:16 [DubboServerHandler-192.168.0.112:20880-thread-48] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:16 [DubboServerHandler-192.168.0.112:20880-thread-48] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[4ms]
2025-06-16 00:11:19 [DubboServerHandler-192.168.0.112:20880-thread-49] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:19 [DubboServerHandler-192.168.0.112:20880-thread-49] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[3ms]
2025-06-16 00:11:21 [DubboServerHandler-192.168.0.112:20880-thread-50] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:21 [DubboServerHandler-192.168.0.112:20880-thread-50] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[4ms]
2025-06-16 00:11:24 [DubboServerHandler-192.168.0.112:20880-thread-51] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:24 [DubboServerHandler-192.168.0.112:20880-thread-51] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-16 00:11:28 [DubboServerHandler-192.168.0.112:20880-thread-52] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:28 [DubboServerHandler-192.168.0.112:20880-thread-52] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[4ms]
2025-06-16 00:11:40 [DubboServerHandler-192.168.0.112:20880-thread-53] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:40 [DubboServerHandler-192.168.0.112:20880-thread-53] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-16 00:11:40 [DubboServerHandler-192.168.0.112:20880-thread-54] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:40 [DubboServerHandler-192.168.0.112:20880-thread-54] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-16 00:11:40 [DubboServerHandler-192.168.0.112:20880-thread-55] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 00:11:40 [DubboServerHandler-192.168.0.112:20880-thread-55] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-56] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-56] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-57] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-57] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[16ms]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-58] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-58] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-59] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-59] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-60] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 00:11:44 [DubboServerHandler-192.168.0.112:20880-thread-60] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[5ms]
2025-06-16 00:11:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:11:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 00:11:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-16 00:11:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 00:11:45 [DubboServerHandler-192.168.0.112:20880-thread-61] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:45 [DubboServerHandler-192.168.0.112:20880-thread-61] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 08:25:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:25:32 [main] INFO  o.d.system.RuoYiSystemApplication - Starting RuoYiSystemApplication using Java 17.0.14 with PID 33184 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-system\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:25:32 [main] INFO  o.d.system.RuoYiSystemApplication - The following 1 profile is active: "dev"
2025-06-16 08:25:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-16 08:25:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:25:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:25:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:25:39 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:25:39 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:25:39 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:25:39 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:25:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 08:25:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 08:25:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@18fb5226
2025-06-16 08:25:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 08:25:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 08:25:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 08:25:41 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:25:46 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@712ac7e6
2025-06-16 08:25:48 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:25:48 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:25:48 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:25:48 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:25:49 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-system 162.168.2.50:9201 register finished
2025-06-16 08:25:55 [main] INFO  o.d.system.RuoYiSystemApplication - Started RuoYiSystemApplication in 25.798 seconds (process running for 26.568)
2025-06-16 08:25:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:25:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:25:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
2025-06-16 08:25:55 [RMI TCP Connection(2)-162.168.2.50] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:00:57 [DubboServerHandler-162.168.2.50:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:00:57 [DubboServerHandler-162.168.2.50:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[265ms]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[39ms]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[82ms]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 09:01:03 [DubboServerHandler-162.168.2.50:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[32ms]
2025-06-16 09:01:04 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:01:05 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:01:05 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:01:05 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:01:05 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-06-16 09:01:05 [DubboServerHandler-162.168.2.50:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:01:05 [DubboServerHandler-162.168.2.50:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[18ms]
2025-06-16 09:06:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:06:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:06:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:06:19 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:06:32 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:06:32 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:06:32 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:06:32 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:06:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:06:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:06:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:06:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:07:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:07:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:07:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:07:07 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:07:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:07:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:07:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:07:20 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:07:23 [DubboServerHandler-162.168.2.50:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:07:23 [DubboServerHandler-162.168.2.50:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[18ms]
2025-06-16 09:11:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:11:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:11:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:11:52 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:12:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:12:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:12:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:12:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:12:26 [DubboServerHandler-162.168.2.50:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:12:26 [DubboServerHandler-162.168.2.50:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-16 09:14:33 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:14:33 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:14:33 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:14:33 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:15:13 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:15:13 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:15:13 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:15:13 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:15:14 [DubboServerHandler-162.168.2.50:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:15:14 [DubboServerHandler-162.168.2.50:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 09:15:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:15:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:15:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:15:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:15:27 [DubboServerHandler-162.168.2.50:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:15:27 [DubboServerHandler-162.168.2.50:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 09:15:42 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:15:42 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:15:42 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:15:42 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:15:44 [DubboServerHandler-162.168.2.50:20880-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:15:44 [DubboServerHandler-162.168.2.50:20880-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 09:16:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:16:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:16:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:16:09 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:16:10 [DubboServerHandler-162.168.2.50:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:16:10 [DubboServerHandler-162.168.2.50:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[12ms]
2025-06-16 09:16:14 [DubboServerHandler-162.168.2.50:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 09:16:14 [DubboServerHandler-162.168.2.50:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-16 09:18:19 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:18:19 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:18:19 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:18:19 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:18:20 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:18:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:18:20 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 09:18:20 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 09:18:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 09:18:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 09:32:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:32:38 [main] INFO  o.d.system.RuoYiSystemApplication - Starting RuoYiSystemApplication using Java 17.0.14 with PID 33016 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-system\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:32:38 [main] INFO  o.d.system.RuoYiSystemApplication - The following 1 profile is active: "dev"
2025-06-16 09:32:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:32:46 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:32:46 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:32:47 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:47 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 09:32:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 09:32:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@67ee26af
2025-06-16 09:32:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 09:32:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 09:32:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 09:32:50 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:32:56 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2e62ead7
2025-06-16 09:32:58 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 09:32:58 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 09:32:58 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 09:32:58 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 09:32:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-system 162.168.2.50:9201 register finished
2025-06-16 09:33:00 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:33:05 [main] INFO  o.d.system.RuoYiSystemApplication - Started RuoYiSystemApplication in 30.462 seconds (process running for 31.466)
2025-06-16 09:33:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 09:33:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:33:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
2025-06-16 09:33:13 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:33:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:33:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:33:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[32ms]
2025-06-16 09:33:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[88ms]
2025-06-16 09:33:15 [DubboServerHandler-162.168.2.50:20880-thread-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:33:15 [DubboServerHandler-162.168.2.50:20880-thread-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[23ms]
2025-06-16 09:33:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:33:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:33:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-16 09:33:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:33:28 [DubboServerHandler-162.168.2.50:20880-thread-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:33:28 [DubboServerHandler-162.168.2.50:20880-thread-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-16 09:35:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:35:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:35:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:35:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-16 09:35:27 [DubboServerHandler-162.168.2.50:20880-thread-6] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:35:27 [DubboServerHandler-162.168.2.50:20880-thread-6] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[12ms]
2025-06-16 09:35:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:35:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-16 09:35:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-16 09:35:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-16 09:35:57 [DubboServerHandler-162.168.2.50:20880-thread-7] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:35:57 [DubboServerHandler-162.168.2.50:20880-thread-7] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 09:38:00 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
