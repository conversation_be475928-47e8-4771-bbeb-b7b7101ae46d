var f=(h,m,s)=>new Promise((c,r)=>{var d=e=>{try{i(s.next(e))}catch(t){r(t)}},p=e=>{try{i(s.throw(e))}catch(t){r(t)}},i=e=>e.done?c(e.value):Promise.resolve(e.value).then(d,p);i((s=s.apply(h,m)).next())});import{aB as E,br as k,$ as o,aN as _,al as v,D as B,ap as V,am as x,bp as $}from"./bootstrap-DCMzVRvD.js";import{s as F}from"./captcha-Bo71W0x_.js";import{A as P}from"./index-kC0HFDdy.js";import{T as D}from"./auth-title-CzJiGVH3.js";import{d as C,s as R,B as A,c as I,o as S,a as u,w as g,r as w,k as b,t as T,b as l,j as q,n as M,p as y,E as j,v as z}from"../jse/index-index-C-MnMZEz.js";const G={class:"text-muted-foreground"},H=C({name:"AuthenticationCodeLogin",__name:"code-login",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(h,{expose:m,emit:s}){const c=h,r=s,d=E(),[p,i]=k(R({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:A(()=>c.formSchema),showDefaultActions:!1}));function e(){return f(this,null,function*(){const{valid:a}=yield i.validate(),n=yield i.getValues();a&&r("submit",{tenantId:n==null?void 0:n.tenantId,code:n==null?void 0:n.code,phoneNumber:n==null?void 0:n.phoneNumber})})}function t(){d.push(c.loginPath)}return m({getFormApi:()=>i}),(a,n)=>(S(),I("div",null,[u(D,null,{desc:g(()=>[q("span",G,[w(a.$slots,"subTitle",{},()=>[b(T(a.subTitle||l(o)("authentication.codeSubtitle")),1)])])]),default:g(()=>[w(a.$slots,"title",{},()=>[b(T(a.title||l(o)("authentication.welcomeBack"))+" 📲 ",1)])]),_:3}),u(l(p)),u(l(_),{class:M([{"cursor-wait":a.loading},"w-full"]),loading:a.loading,onClick:e},{default:g(()=>[w(a.$slots,"submitButtonText",{},()=>[b(T(a.submitButtonText||l(o)("common.login")),1)])]),_:3},8,["class","loading"]),u(l(_),{class:"mt-4 w-full",variant:"outline",onClick:n[0]||(n[0]=L=>t())},{default:g(()=>[b(T(l(o)("common.back")),1)]),_:1})]))}}),N=4,X=C({name:"CodeLogin",__name:"code-login",setup(h){const m=y(!1),s=y({tenantEnabled:!1,voList:[]}),c=j("codeLoginRef");function r(){return f(this,null,function*(){var t;const e=yield $();if(s.value=e,e.tenantEnabled&&e.voList.length>0){const a=e.voList[0].tenantId;(t=c.value)==null||t.getFormApi().setFieldValue("tenantId",a)}})}z(r);const d=A(()=>{var e;return[{component:"VbenSelect",componentProps:{class:"bg-background h-[40px] focus:border-primary",contentClass:"max-h-[256px] overflow-y-auto",options:(e=s.value.voList)==null?void 0:e.map(t=>({label:t.companyName,value:t.tenantId})),placeholder:o("authentication.selectAccount")},defaultValue:B,dependencies:{if:()=>s.value.tenantEnabled,triggerFields:[""]},fieldName:"tenantId",label:o("authentication.selectAccount"),rules:v().min(1,{message:o("authentication.selectAccount")})},{component:"VbenInput",componentProps:{placeholder:o("authentication.mobile")},fieldName:"phoneNumber",label:o("authentication.mobile"),rules:v().min(1,{message:o("authentication.mobileTip")}).refine(t=>/^\d{11}$/.test(t),{message:o("authentication.mobileErrortip")})},{component:"VbenPinInput",componentProps(t,a){return{createText:n=>n>0?o("authentication.sendText",[n]):o("authentication.sendCode"),codeLength:N,placeholder:o("authentication.code"),handleSendCode:()=>f(null,null,function*(){const{valid:n,value:L}=yield a.validateField("phoneNumber");if(!n)throw new Error("未填写手机号");yield F(L),V.success("验证码发送成功")})}},fieldName:"code",label:o("authentication.code"),rules:v().length(N,{message:o("authentication.codeTip",[N])})}]}),p=x();function i(e){return f(this,null,function*(){try{const t={tenantId:e.tenantId,phonenumber:e.phoneNumber,smsCode:e.code,grantType:"sms"};console.log("login params",t),yield p.authLogin(t)}catch(t){console.error(t)}})}return(e,t)=>(S(),I("div",null,[u(l(P),{class:"mb-4","how-icon":"",message:"测试手机号: 15888888888 正确验证码: 1234 演示使用 不会真的发送",type:"info"}),u(l(H),{ref_key:"codeLoginRef",ref:c,"form-schema":d.value,loading:m.value,onSubmit:i},null,8,["form-schema","loading"])]))}});export{X as default};
