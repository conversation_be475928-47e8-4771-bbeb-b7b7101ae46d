---
description: 
globs: apps/web-antd/src/views/**/*.vue,apps/web-antd/src/components/**/*.vue,apps/web-antd/src/components/**/*.ts,apps/web-antd/src/layouts/**/*.vue,packages/@core/ui-kit/**/*.vue,packages/@core/ui-kit/**/*.ts,packages/components/**/*.vue,packages/components/**/*.ts
alwaysApply: false
---
# 组件模式和开发约定

## 组件架构

### 页面组件结构
典型的页面组件（如 [apps/web-antd/src/views/system/user/index.vue](mdc:apps/web-antd/src/views/system/user/index.vue)）包含：
- 主页面组件 `index.vue`
- 表格配置 `data.tsx`
- 表单抽屉 `xxx-drawer.vue`
- 信息弹窗 `xxx-info-modal.vue`
- 部门树等辅助组件

### 业务组件
- 数据字典：[apps/web-antd/src/components/dict/](mdc:apps/web-antd/src/components/dict)
- 文件上传：[apps/web-antd/src/components/upload/](mdc:apps/web-antd/src/components/upload)
- 表格组件：[apps/web-antd/src/components/table/](mdc:apps/web-antd/src/components/table)

## Vue 3 组合式API约定

### 基本模式
```vue
<script setup lang="ts">
// 1. 导入
import { ref, computed, onMounted } from 'vue';

// 2. Props定义
interface Props {
  visible: boolean;
  userId?: string | number;
}
const props = defineProps<Props>();

// 3. Emits定义
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'submit', data: any): void;
}
const emit = defineEmits<Emits>();

// 4. 响应式数据
const loading = ref(false);
const formData = ref({});

// 5. 计算属性
const isEdit = computed(() => !!props.userId);

// 6. 方法
const handleSubmit = async () => {
  // 实现逻辑
};

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>
```

### 状态管理模式
使用Pinia store，参考主要的store：
- 认证状态：通过 `useAuthStore()` 访问
- 权限状态：通过 `useAccessStore()` 访问
- 配置状态：通过 `useConfigStore()` 访问

### 权限控制
```vue
<template>
  <!-- 使用v-access指令 -->
  <a-button v-access="'system:user:add'" type="primary">
    新增用户
  </a-button>
</template>
```

## 表单处理模式

### 表单验证
使用 vee-validate + zod：
```typescript
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';

const schema = z.object({
  userName: z.string().min(2).max(20),
  email: z.string().email(),
});

const { handleSubmit, errors } = useForm({
  validationSchema: toTypedSchema(schema),
});
```

### 数据字典使用
```vue
<template>
  <DictTag dict-type="sys_user_sex" :dict-value="record.sex" />
</template>

<script setup lang="ts">
import { useDictData } from '@/hooks/dict';

const { dictData: sexOptions } = useDictData('sys_user_sex');
</script>
```

## 表格处理模式

### 表格配置
在 `data.tsx` 文件中定义表格列配置：
```typescript
export const columns: TableColumn[] = [
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    customRender: ({ record }) => (
      <DictTag dictType="sys_normal_disable" dictValue={record.status} />
    ),
  },
];
```

### 分页处理
```typescript
const pagination = ref<PageQuery>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});

const loadData = async () => {
  const result = await xxxList(pagination.value);
  dataSource.value = result.rows;
  pagination.value.total = result.total;
};
```

## 文件上传模式
参考 [apps/web-antd/src/components/upload/](mdc:apps/web-antd/src/components/upload) 下的组件：
- 图片上传：`image-upload.vue`
- 文件上传：`file-upload.vue`
- 头像上传：`avatar-upload.vue`
