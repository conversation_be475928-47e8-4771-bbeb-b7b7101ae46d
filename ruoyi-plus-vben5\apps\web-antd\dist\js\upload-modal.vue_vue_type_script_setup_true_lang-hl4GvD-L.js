var d=(u,r,s)=>new Promise((c,t)=>{var m=a=>{try{e(s.next(a))}catch(i){t(i)}},o=a=>{try{e(s.throw(a))}catch(i){t(i)}},e=a=>a.done?c(a.value):Promise.resolve(a.value).then(m,o);e((s=s.apply(u,r)).next())});import{aj as _,an as x}from"./bootstrap-DCMzVRvD.js";import V from"./index-BeyziwLP.js";import{_ as b}from"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import{d as C,l as g,h as I,o as h,w as n,j as k,a as l,b as f,k as p,M as w}from"../jse/index-index-C-MnMZEz.js";import{u as B}from"./use-modal-CeMSCP2m.js";const N={class:"flex flex-col"},P=C({__name:"upload-modal",setup(u){const[r,s]=_({layout:"vertical",schema:[{label:"图片上传多图",component:"ImageUpload",fieldName:"ossIds",componentProps:{maxCount:3}},{label:"图片上传单图",component:"ImageUpload",fieldName:"ossId",componentProps:{maxCount:1}}],showDefaultActions:!1});function c(){return d(this,null,function*(){try{const o=yield s.getValues();console.log(o),x.info({content:()=>w(b,{data:o})})}catch(o){console.error(o)}})}function t(){return d(this,null,function*(){const o=["1908761290673315841","1907738568539332610"];yield s.setValues({ossIds:o,ossId:o[0]})})}const[m]=B({title:"上传",footer:!1});return(o,e)=>{const a=g("a-button");return h(),I(f(m),null,{default:n(()=>[k("div",N,[l(f(V),null,{default:n(()=>[l(a,{onClick:t},{default:n(()=>e[0]||(e[0]=[p("赋值")])),_:1,__:[0]}),l(a,{onClick:c},{default:n(()=>e[1]||(e[1]=[p("获取值")])),_:1,__:[1]})]),_:1}),l(f(r))])]),_:1})}}});export{P as _};
