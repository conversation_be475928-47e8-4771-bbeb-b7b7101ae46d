{"doc": " <h1>Excel表格下拉选操作</h1>\n 考虑到下拉选过多可能导致Excel打开缓慢的问题，只校验前1000行\n <p>\n 即只有前1000行的数据可以用下拉框，超出的自行通过限制数据量的形式，第二次输出\n\n <AUTHOR>\n", "fields": [{"name": "EXCEL_COLUMN_NAME", "doc": " Excel表格中的列名英文\n 仅为了解析列英文，禁止修改\n"}, {"name": "OPTIONS_SHEET_NAME", "doc": " 单选数据Sheet名\n"}, {"name": "LINKED_OPTIONS_SHEET_NAME", "doc": " 联动选择数据Sheet名的头\n"}, {"name": "dropDownOptions", "doc": " 下拉可选项\n"}, {"name": "currentOptionsColumnIndex", "doc": " 当前单选进度\n"}, {"name": "currentLinkedOptionsSheetIndex", "doc": " 当前联动选择进度\n"}], "enumConstants": [], "methods": [{"name": "afterSheetCreate", "paramTypes": ["cn.idev.excel.write.metadata.holder.WriteWorkbookHolder", "cn.idev.excel.write.metadata.holder.WriteSheetHolder"], "doc": " <h2>开始创建下拉数据</h2>\n 1.通过解析传入的@ExcelProperty同级是否标注有@DropDown选项\n 如果有且设置了value值，则将其直接置为下拉可选项\n <p>\n 2.或者在调用ExcelUtil时指定了可选项，将依据传入的可选项做下拉\n <p>\n 3.二者并存，注意调用方式\n"}, {"name": "dropDownWithSimple", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "java.util.List"], "doc": " <h2>简单下拉框</h2>\n 直接将可选项拼接为指定列的数据校验值\n\n @param celIndex 列index\n @param value    下拉选可选值\n"}, {"name": "dropDownLinkedOptions", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Workbook", "org.apache.poi.ss.usermodel.Sheet", "org.dromara.common.excel.core.DropDownOptions"], "doc": " <h2>额外表格形式的级联下拉框</h2>\n\n @param options 额外表格形式存储的下拉可选项\n"}, {"name": "dropDownWithSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Workbook", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "java.util.List"], "doc": " <h2>额外表格形式的普通下拉框</h2>\n 由于下拉框可选值数量过多，为提升Excel打开效率，使用额外表格形式做下拉\n\n @param celIndex 下拉选\n @param value    下拉选可选值\n"}, {"name": "markOptionsToSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "org.apache.poi.ss.usermodel.DataValidationConstraint"], "doc": " 挂载下拉的列，仅限一级选项\n"}, {"name": "markLinkedOptionsToSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "java.lang.Integer", "org.apache.poi.ss.usermodel.DataValidationConstraint"], "doc": " 挂载下拉的列，仅限二级选项\n"}, {"name": "markDataValidationToSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "org.apache.poi.ss.usermodel.DataValidationConstraint", "org.apache.poi.ss.util.CellRangeAddressList"], "doc": " 应用数据校验\n"}, {"name": "getExcelColumnName", "paramTypes": ["int"], "doc": " <h2>依据列index获取列名英文</h2>\n 依据列index转换为Excel中的列名英文\n <p>例如第1列，index为0，解析出来为A列</p>\n 第27列，index为26，解析为AA列\n <p>第28列，index为27，解析为AB列</p>\n\n @param columnIndex 列index\n @return 列index所在得英文名\n"}], "constructors": []}