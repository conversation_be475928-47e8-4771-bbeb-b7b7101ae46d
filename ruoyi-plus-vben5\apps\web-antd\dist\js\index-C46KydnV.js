var q=Object.defineProperty;var x=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var N=(t,o,e)=>o in t?q(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,D=(t,o)=>{for(var e in o||(o={}))B.call(o,e)&&N(t,e,o[e]);if(x)for(var e of x(o))M.call(o,e)&&N(t,e,o[e]);return t};var d=(t,o,e)=>new Promise((w,m)=>{var k=r=>{try{p(e.next(r))}catch(l){m(l)}},c=r=>{try{p(e.throw(r))}catch(l){m(l)}},p=r=>r.done?w(r.value):Promise.resolve(r.value).then(k,c);p((e=e.apply(t,o)).next())});import{ar as S,as as Y,an as A}from"./bootstrap-DCMzVRvD.js";import{v as z}from"./vxe-table-DzEj5Fop.js";import{_ as R,n as j,a as $}from"./notice-modal.vue_vue_type_script_setup_true_lang-Ba0lSSAx.js";import{g as F}from"./dict-BLkXAGS5.js";import{r as I}from"./render-BxXtQdeV.js";import{a as G,g as L}from"./get-popup-container-P4S1sr5h.js";import P from"./index-BeyziwLP.js";import{_ as U}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as W,l as E,S as H,h as u,o as f,w as n,a as g,b as a,T as y,k as C,t as b}from"../jse/index-index-C-MnMZEz.js";import{u as J}from"./use-vxe-grid-BC7vZzEr.js";import{u as K}from"./use-modal-CeMSCP2m.js";import{P as Q}from"./index-DNdMANjv.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-CDHRR1Za.js";import"./index-Ollxi7Rl.js";import"./popup-D6rC6QBG.js";import"./pick-CyUZAAhv.js";import"./index-BxBCzu2M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./Group-oWwucTzK.js";import"./Checkbox-DRV8G-PI.js";import"./helper-Bc7QQ92Q.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const X=()=>[{component:"Input",fieldName:"noticeTitle",label:"公告标题"},{component:"Input",fieldName:"createBy",label:"创建人"},{component:"Select",componentProps:{getPopupContainer:G,options:F(S.SYS_NOTICE_TYPE)},fieldName:"noticeType",label:"公告类型"}],Z=[{type:"checkbox",width:60},{title:"公告标题",field:"noticeTitle"},{title:"公告类型",field:"noticeType",width:120,slots:{default:({row:t})=>I(t.noticeType,S.SYS_NOTICE_TYPE)}},{title:"状态",field:"status",width:120,slots:{default:({row:t})=>I(t.status,S.SYS_NOTICE_STATUS)}},{title:"创建人",field:"createByName",width:150},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],Ae=W({__name:"index",setup(t){const o={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:X(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={checkboxConfig:{highlight:!0,reserve:!0},columns:Z,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(h,...v)=>d(null,[h,...v],function*({page:i},s={}){return yield j(D({pageNum:i.currentPage,pageSize:i.pageSize},s))})}},rowConfig:{keyField:"noticeId"},id:"system-notice-index"},[w,m]=J({formOptions:o,gridOptions:e}),[k,c]=K({connectedComponent:R});function p(){c.setData({}),c.open()}function r(i){return d(this,null,function*(){c.setData({id:i.noticeId}),c.open()})}function l(i){return d(this,null,function*(){yield $([i.noticeId]),yield m.query()})}function O(){const s=m.grid.getCheckboxRecords().map(h=>h.noticeId);A.confirm({title:"提示",okType:"danger",content:`确认删除选中的${s.length}条记录吗？`,onOk:()=>d(null,null,function*(){yield $(s),yield m.query()})})}return(i,s)=>{const h=E("a-button"),v=E("ghost-button"),_=H("access");return f(),u(a(U),{"auto-content-height":!0},{default:n(()=>[g(a(w),{"table-title":"通知公告列表"},{"toolbar-tools":n(()=>[g(a(P),null,{default:n(()=>[y((f(),u(h,{disabled:!a(z)(a(m)),danger:"",type:"primary",onClick:O},{default:n(()=>[C(b(i.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[_,["system:notice:remove"],"code"]]),y((f(),u(h,{type:"primary",onClick:p},{default:n(()=>[C(b(i.$t("pages.common.add")),1)]),_:1})),[[_,["system:notice:add"],"code"]])]),_:1})]),action:n(({row:T})=>[g(a(P),null,{default:n(()=>[y((f(),u(v,{onClick:V=>r(T)},{default:n(()=>[C(b(i.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[_,["system:notice:edit"],"code"]]),g(a(Q),{"get-popup-container":a(L),placement:"left",title:"确认删除？",onConfirm:V=>l(T)},{default:n(()=>[y((f(),u(v,{danger:"",onClick:s[0]||(s[0]=Y(()=>{},["stop"]))},{default:n(()=>[C(b(i.$t("pages.common.delete")),1)]),_:1})),[[_,["system:notice:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),g(a(k),{onReload:s[1]||(s[1]=T=>a(m).query())})]),_:1})}}});export{Ae as default};
