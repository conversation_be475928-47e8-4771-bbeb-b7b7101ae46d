import{ah as m,z as f}from"./bootstrap-DCMzVRvD.js";import"./index-BxBCzu2M.js";import{I as c}from"./Search-ClCped_G.js";import{d as v,m as b,u as _,l as h,h as I,o as k,a2 as x,w as r,a as s,j as l,b as n}from"../jse/index-index-C-MnMZEz.js";import{b as y}from"./uuid-B0AYzFfo.js";import"./index-CFj2VWFk.js";import"./statusUtils-d85DZFMd.js";import"./index-CHpIOV4R.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./SearchOutlined-BOD_ZIye.js";const B={class:"flex items-center gap-[4px]"},C=v({name:"SecretInput",__name:"secret-input",props:b({disabled:{type:Boolean,default:!1},placeholder:{default:"请输入密钥或随机生成"}},{value:{required:!1},valueModifiers:{}}),emits:["update:value"],setup(p,{expose:d}){const a=_(p,"value");function t(){a.value=y()}return d({refreshSecret:t}),(o,e)=>{const i=h("a-button");return k(),I(n(c),{value:a.value,"onUpdate:value":e[0]||(e[0]=u=>a.value=u),disabled:o.disabled,placeholder:o.placeholder},x({_:2},[o.disabled?void 0:{name:"addonAfter",fn:r(()=>[s(i,{type:"primary",onClick:t},{default:r(()=>[l("div",B,[s(n(m),{icon:"charm:refresh"}),e[1]||(e[1]=l("span",null,"随机生成",-1))])]),_:1})]),key:"0"}]),1032,["value","disabled","placeholder"])}}}),D=f(C,[["__scopeId","data-v-b655f75f"]]);export{D as default};
