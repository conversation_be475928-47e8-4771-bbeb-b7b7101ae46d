{"doc": " 扩展 hutool TreeUtil 封装系统树构建\n\n <AUTHOR> Li\n", "fields": [{"name": "DEFAULT_CONFIG", "doc": " 根据前端定制差异化字段\n"}], "enumConstants": [], "methods": [{"name": "build", "paramTypes": ["java.util.List", "cn.hutool.core.lang.tree.parser.NodeParser"], "doc": " 构建树形结构\n\n @param <T>        输入节点的类型\n @param <K>        节点ID的类型\n @param list       节点列表，其中包含了要构建树形结构的所有节点\n @param nodeParser 解析器，用于将输入节点转换为树节点\n @return 构建好的树形结构列表\n"}, {"name": "build", "paramTypes": ["java.util.List", "java.lang.Object", "cn.hutool.core.lang.tree.parser.NodeParser"], "doc": " 构建树形结构\n\n @param <T>        输入节点的类型\n @param <K>        节点ID的类型\n @param parentId   顶级节点\n @param list       节点列表，其中包含了要构建树形结构的所有节点\n @param nodeParser 解析器，用于将输入节点转换为树节点\n @return 构建好的树形结构列表\n"}, {"name": "getLeafNodes", "paramTypes": ["java.util.List"], "doc": " 获取节点列表中所有节点的叶子节点\n\n @param <K>   节点ID的类型\n @param nodes 节点列表\n @return 包含所有叶子节点的列表\n"}, {"name": "extractLeafNodes", "paramTypes": ["cn.hutool.core.lang.tree.Tree"], "doc": " 获取指定节点下的所有叶子节点\n\n @param <K>  节点ID的类型\n @param node 要查找叶子节点的根节点\n @return 包含所有叶子节点的列表\n"}], "constructors": []}