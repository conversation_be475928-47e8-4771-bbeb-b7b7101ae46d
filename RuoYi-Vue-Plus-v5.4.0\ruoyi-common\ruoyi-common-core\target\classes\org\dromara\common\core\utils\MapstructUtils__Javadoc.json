{"doc": " Mapstruct 工具类\n <p>参考文档：<a href=\"https://mapstruct.plus/introduction/quick-start.html\">mapstruct-plus</a></p>\n\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "convert", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": " 将 T 类型对象，转换为 desc 类型的对象并返回\n\n @param source 数据来源实体\n @param desc   描述对象 转换后的对象\n @return desc\n"}, {"name": "convert", "paramTypes": ["java.lang.Object", "java.lang.Object"], "doc": " 将 T 类型对象，按照配置的映射字段规则，给 desc 类型的对象赋值并返回 desc 对象\n\n @param source 数据来源实体\n @param desc   转换后的对象\n @return desc\n"}, {"name": "convert", "paramTypes": ["java.util.List", "java.lang.Class"], "doc": " 将 T 类型的集合，转换为 desc 类型的集合并返回\n\n @param sourceList 数据来源实体列表\n @param desc       描述对象 转换后的对象\n @return desc\n"}, {"name": "convert", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": " 将 Map 转换为 beanClass 类型的集合并返回\n\n @param map       数据来源\n @param beanClass bean类\n @return bean对象\n"}], "constructors": []}