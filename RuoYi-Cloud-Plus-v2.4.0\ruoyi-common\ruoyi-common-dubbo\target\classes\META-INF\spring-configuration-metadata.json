{"groups": [{"name": "dubbo.custom", "type": "org.dromara.common.dubbo.properties.DubboCustomProperties", "sourceType": "org.dromara.common.dubbo.properties.DubboCustomProperties"}], "properties": [{"name": "dubbo.custom.log-level", "type": "org.dromara.common.dubbo.enumd.RequestLogEnum", "description": "日志级别", "sourceType": "org.dromara.common.dubbo.properties.DubboCustomProperties"}, {"name": "dubbo.custom.request-log", "type": "java.lang.Bo<PERSON>an", "description": "是否开启请求日志记录", "sourceType": "org.dromara.common.dubbo.properties.DubboCustomProperties"}], "hints": []}