package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__11;
import org.dromara.system.domain.vo.SysTenantPackageVo;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysTenantPackageBoToSysTenantPackageMapper__11.class,SysTenantPackageVoToSysTenantPackageMapper__11.class},
    imports = {}
)
public interface SysTenantPackageToSysTenantPackageVoMapper__11 extends BaseMapper<SysTenantPackage, SysTenantPackageVo> {
}
