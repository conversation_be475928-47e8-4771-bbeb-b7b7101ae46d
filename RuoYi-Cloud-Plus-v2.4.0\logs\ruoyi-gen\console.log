2025-06-17 08:16:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-17 08:16:47 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 39932 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:16:47 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-17 08:16:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-17 08:16:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-17 08:16:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-17 08:16:53 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-17 08:16:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-17 08:16:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-17 08:16:54 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@760dc63
2025-06-17 08:16:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-17 08:16:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-17 08:16:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-17 08:16:55 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-17 08:16:57 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-17 08:16:57 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-17 08:16:57 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:16:57 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:16:57 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@58658f63
2025-06-17 08:17:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-17 08:17:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-17 08:17:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-17 08:17:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-17 08:17:04 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-17 08:17:08 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 24.208 seconds (process running for 24.796)
2025-06-17 08:17:08 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-17 08:17:08 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-17 08:17:08 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-17 08:17:08 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 08:17:59 [metrics-collector-sync-job-thread-1] WARN  i.m.core.instrument.MeterRegistry - This FunctionCounter has been already registered (MeterId{name='dubbo.application.info.total', tags=[tag(application.module.id=1.1),tag(application.name=ruoyi-gen),tag(application.version=3.3.4),tag(git.commit.id=82a03c3721c68e20226e7cd4f3031b74a8263e41),tag(hostname=奚翔),tag(ip=*************)]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-06-17 15:12:33 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xa3746d43, L:/*************:64043 - R:/*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-17 15:12:33 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-17 15:12:33 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Waiting for modules(Dubbo Application[1.1](ruoyi-gen)) managed by Spring to be shutdown., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) has completed., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo wait for application(Dubbo Application[1.1](ruoyi-gen)) managed by Spring to be shutdown failed, time usage: 15ms, dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](ruoyi-gen), dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:34 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:34 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:35 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:35 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:35 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:36 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:36 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:37 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:37 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:38 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:38 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unregister service org.dromara.system.api.RemoteClientService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-17 15:12:38 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:39 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:39 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:39 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:40 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:40 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:41 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:41 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:42 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:42 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:43 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:43 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unsubscribe service org.dromara.system.api.RemoteClientService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteClientServiceDubboProxy1.$destroy(RemoteClientServiceDubboProxy1.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-17 15:12:43 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:43 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:44 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:44 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:45 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:45 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:46 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:46 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:47 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:48 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:48 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:48 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unregister service org.dromara.system.api.RemoteDataScopeService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-17 15:12:49 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:49 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:50 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:50 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:51 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:51 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:52 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:52 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:53 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xa3746d43, L:/*************:64043 - R:/*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:53 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xa3746d43, L:/*************:64043 ! R:/*************:20880] of *************:64043 -> *************:20880 is disconnected., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:53 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:53 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:54 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:54 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unsubscribe service org.dromara.system.api.RemoteDataScopeService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteDataScopeServiceDubboProxy2.$destroy(RemoteDataScopeServiceDubboProxy2.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-17 15:12:54 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:55 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:56 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:56 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Initial connection to HeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/*************:64043 -> /*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:56 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xa3746d43, L:/*************:64043 ! R:/*************:20880], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:56 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:57 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:58 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:58 [dubbo-client-heartbeat-reconnect-thread-1] ERROR o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Failed to connect to provider server by other reason., dubbo version: 3.3.4, current host: *************, error code: 6-1. This may be caused by network disconnected, go to https://dubbo.apache.org/faq/6/1 to find instructions. 
io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /*************:20880
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:58 [dubbo-client-heartbeat-reconnect-thread-1] ERROR o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Fail to connect toHeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/*************:64043 -> /*************:20880]], dubbo version: 3.3.4, current host: *************, error code: 6-16. This may be caused by , go to https://dubbo.apache.org/faq/6/16 to find instructions. 
org.apache.dubbo.remoting.RemotingException: client(url: dubbo://*************:20880/org.dromara.system.api.RemoteClientService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&background=false&cache=false&check=false&codec=dubbo&deprecated=false&dubbo=2.0.2&dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}]&dubbo.metadata.revision=7092d8208bd8d2ca5be86b5c320c6886&dubbo.metadata.storage-type=remote&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&heartbeat=60000&interface=org.dromara.system.api.RemoteClientService&logger=slf4j&meta-v=1.0.0&metadata-type=remote&methods=queryByClientId&pid=39932&prefer.serialization=hessian2,fastjson2&qos.enable=false&register-mode=instance&register.ip=*************&release=3.3.4&retries=0&service-name-mapping=true&side=consumer&sticky=false&timeout=3000&timestamp=1750119339228&unloadClusterRelated=false&validation=jvalidationNew) failed to connect to server /*************:20880, error message is:Connection refused: no further information: /*************:20880
	at org.apache.dubbo.remoting.transport.netty4.NettyClient.doConnect(NettyClient.java:231)
	at org.apache.dubbo.remoting.transport.netty4.NettyClient.doConnect(NettyClient.java:175)
	at org.apache.dubbo.remoting.transport.AbstractClient.connect(AbstractClient.java:266)
	at org.apache.dubbo.remoting.transport.AbstractClient.reconnect(AbstractClient.java:347)
	at org.apache.dubbo.remoting.exchange.support.header.HeaderExchangeClient.reconnect(HeaderExchangeClient.java:189)
	at org.apache.dubbo.remoting.exchange.support.header.ReconnectTimerTask.doTask(ReconnectTimerTask.java:56)
	at org.apache.dubbo.remoting.exchange.support.header.AbstractTimerTask.run(AbstractTimerTask.java:93)
	at org.apache.dubbo.common.timer.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:654)
	at org.apache.dubbo.common.timer.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:733)
	at org.apache.dubbo.common.timer.HashedWheelTimer$Worker.run(HashedWheelTimer.java:455)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /*************:20880
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:58 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:59 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:12:59 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:00 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:00 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:00 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unregister service org.dromara.system.api.RemoteLogService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:317)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-17 15:13:01 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 1, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:01 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 2, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:02 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 3, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:02 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 4, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:03 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 5, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:03 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 6, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:04 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 7, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:04 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 8, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:04 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 9, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:05 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Dubbo will try to retry in 10. Try times: 10, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:05 [SpringApplicationShutdownHook] WARN  o.a.d.r.n.NacosNamingServiceWrapper -  [DUBBO] Failed to request nacos naming server. Exceed retry max times.Try times: 11, dubbo version: 3.3.4, current host: *************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:05 [SpringApplicationShutdownHook] WARN  o.a.d.r.integration.DynamicDirectory -  [DUBBO] unexpected error when unsubscribe service org.dromara.system.api.RemoteLogService from registry: nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************, error code: 1-8. This may be caused by , go to https://dubbo.apache.org/faq/1/8 to find instructions. 
java.lang.RuntimeException: ErrCode:-401, ErrMsg:Client not connected, current status:UNHEALTHY
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:54)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:69)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:143)
	at org.apache.dubbo.registry.client.ServiceDiscovery.isAvailable(ServiceDiscovery.java:100)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistry.isAvailable(ServiceDiscoveryRegistry.java:319)
	at org.apache.dubbo.registry.ListenerRegistryWrapper.isAvailable(ListenerRegistryWrapper.java:49)
	at org.apache.dubbo.registry.integration.DynamicDirectory.destroy(DynamicDirectory.java:332)
	at org.apache.dubbo.registry.client.ServiceDiscoveryRegistryDirectory.destroy(ServiceDiscoveryRegistryDirectory.java:179)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.destroy(AbstractClusterInvoker.java:130)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.destroy(MockClusterInvoker.java:90)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.destroy(ScopeClusterInvoker.java:124)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.destroy(MigrationInvoker.java:343)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:61)
	at org.dromara.system.api.RemoteLogServiceDubboProxy0.$destroy(RemoteLogServiceDubboProxy0.java)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyReference(SimpleReferenceCache.java:296)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.lambda$destroyAll$4(SimpleReferenceCache.java:285)
	at java.base/java.util.concurrent.ConcurrentHashMap.forEach(ConcurrentHashMap.java:1603)
	at org.apache.dubbo.config.utils.SimpleReferenceCache.destroyAll(SimpleReferenceCache.java:283)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.unreferServices(DefaultModuleDeployer.java:591)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.postDestroy(DefaultModuleDeployer.java:306)
	at org.apache.dubbo.rpc.model.ModuleModel.onDestroy(ModuleModel.java:111)
	at org.apache.dubbo.rpc.model.ScopeModel.destroy(ScopeModel.java:123)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onContextClosedEvent(DubboDeployApplicationListener.java:199)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:155)
	at org.apache.dubbo.config.spring.context.DubboDeployApplicationListener.onApplicationEvent(DubboDeployApplicationListener.java:52)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.lambda$getServicesOfServer$20(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.apply(NacosNamingServiceWrapper.java:449)
	at org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper.getServicesOfServer(NacosNamingServiceWrapper.java:292)
	at org.apache.dubbo.registry.nacos.NacosServiceDiscovery.lambda$getServices$3(NacosServiceDiscovery.java:144)
	at org.apache.dubbo.common.function.ThrowableFunction.execute(ThrowableFunction.java:52)
	... 36 common frames omitted
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xa3746d43, L:/*************:64043 ! R:/*************:20880], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) has completed., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](ruoyi-gen) to null, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.registry.nacos.NacosRegistry -  [DUBBO] Destroy registry:nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=39932&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-system], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.a.d.c.s.c.DubboSpringInitializer -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@208205ed, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-17 15:13:05 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:13:06 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gen', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=9202, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-17 15:13:06 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-17 15:13:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-17 15:13:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-17 15:13:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-17 15:13:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-17 15:13:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
