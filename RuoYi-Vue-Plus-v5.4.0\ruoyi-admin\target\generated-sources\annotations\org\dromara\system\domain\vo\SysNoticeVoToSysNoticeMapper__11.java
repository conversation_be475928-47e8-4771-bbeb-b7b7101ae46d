package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysNoticeToSysNoticeVoMapper__11.class},
    imports = {}
)
public interface SysNoticeVoToSysNoticeMapper__11 extends BaseMapper<SysNoticeVo, SysNotice> {
}
