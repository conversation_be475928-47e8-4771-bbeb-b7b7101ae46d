package org.dromara.wms.service;

import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.bo.WmsWarehouseBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 仓库Service接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface IWmsWarehouseService {

    /**
     * 查询仓库
     *
     * @param warehouseId 主键
     * @return 仓库
     */
    WmsWarehouseVo queryById(Long warehouseId);

    /**
     * 分页查询仓库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 仓库分页列表
     */
    TableDataInfo<WmsWarehouseVo> queryPageList(WmsWarehouseBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的仓库列表
     *
     * @param bo 查询条件
     * @return 仓库列表
     */
    List<WmsWarehouseVo> queryList(WmsWarehouseBo bo);

    /**
     * 新增仓库
     *
     * @param bo 仓库
     * @return 是否新增成功
     */
    Boolean insertByBo(WmsWarehouseBo bo);

    /**
     * 修改仓库
     *
     * @param bo 仓库
     * @return 是否修改成功
     */
    Boolean updateByBo(WmsWarehouseBo bo);

    /**
     * 校验并批量删除仓库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询用户可访问的仓库列表
     *
     * @param userId 用户ID
     * @return 仓库列表
     */
    List<WmsWarehouseVo> getUserAccessibleWarehouses(Long userId);
}
