{"doc": " 测试批量方法\n\n <AUTHOR>\n @date 2021-05-30\n", "fields": [{"name": "testDemoMapper", "doc": " 为了便于测试 直接引入mapper\n"}], "enumConstants": [], "methods": [{"name": "add", "paramTypes": [], "doc": " 新增批量方法 可完美替代 saveBatch 秒级插入上万数据 (对mysql负荷较大)\n <p>\n 3.5.0 版本 增加 rewriteBatchedStatements=true 批处理参数 使 MP 原生批处理可以达到同样的速度\n"}, {"name": "addOrUpdate", "paramTypes": [], "doc": " 新增或更新 可完美替代 saveOrUpdateBatch 高性能\n <p>\n 3.5.0 版本 增加 rewriteBatchedStatements=true 批处理参数 使 MP 原生批处理可以达到同样的速度\n"}, {"name": "remove", "paramTypes": [], "doc": " 删除批量方法\n"}], "constructors": []}