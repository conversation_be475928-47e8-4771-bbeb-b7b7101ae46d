<template>
  <div v-if="!isDestroyed" class="warehouse-toggle">
    <Select
      v-model:value="currentWarehouseId"
      :loading="loading"
      :options="warehouseOptions"
      :placeholder="$t('选择仓库')"
      class="w-[160px]"
      @change="handleWarehouseChange"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { $t } from '@vben/locales';

import { Select, message } from 'ant-design-vue';

import { useWarehouseStore } from '#/store/warehouse';

const warehouseStore = useWarehouseStore();
const loading = ref(false);
const isDestroyed = ref(false);

// 当前选择的仓库ID
const currentWarehouseId = computed({
  get: () => warehouseStore.currentWarehouseId,
  set: (_value) => {
    // 这里不直接设置，而是通过 handleWarehouseChange 处理
  },
});

// 仓库选项
const warehouseOptions = computed(() => {
  return warehouseStore.accessibleWarehouses
    .slice() // 创建副本避免修改原数组
    .sort((a, b) => {
      // 按仓库编码排序
      return a.warehouseNumber.localeCompare(b.warehouseNumber);
    })
    .map(warehouse => ({
      label: warehouse.warehouseName,
      value: warehouse.warehouseId,
    }));
});

/**
 * 处理仓库切换
 */
async function handleWarehouseChange(value: any) {
  if (isDestroyed.value || !value || value === warehouseStore.currentWarehouseId) {
    return;
  }

  const warehouseId = typeof value === 'string' ? parseInt(value, 10) : value;
  if (isNaN(warehouseId)) {
    return;
  }

  loading.value = true;
  try {
    await warehouseStore.switchWarehouse(warehouseId);
    if (isDestroyed.value) return; // 检查组件是否已销毁

    const warehouseInfo = warehouseStore.getCurrentWarehouseInfo();
    message.success($t('warehouse.switchSuccess', { name: warehouseInfo?.warehouseName }));

    // 刷新页面以应用新的仓库上下文
    window.location.reload();
  } catch (error) {
    if (!isDestroyed.value) {
      console.error('仓库切换失败:', error);
      message.error($t('warehouse.switchFailed'));
    }
  } finally {
    if (!isDestroyed.value) {
      loading.value = false;
    }
  }
}

onMounted(async () => {
  if (isDestroyed.value || warehouseStore.initialized) {
    return;
  }

  loading.value = true;
  try {
    await warehouseStore.initWarehouse();
  } catch (error) {
    if (!isDestroyed.value) {
      console.error('仓库初始化失败:', error);
      message.error($t('warehouse.initFailed'));
    }
  } finally {
    if (!isDestroyed.value) {
      loading.value = false;
    }
  }
});

onUnmounted(() => {
  isDestroyed.value = true;
});
</script>

<style scoped>
.warehouse-toggle {
  display: flex;
  align-items: center;
}
</style>
