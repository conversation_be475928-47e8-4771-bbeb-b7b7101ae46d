<template>
  <div class="warehouse-toggle">
    <Select
      v-model:value="currentWarehouseId"
      :loading="loading"
      :options="warehouseOptions"
      :placeholder="$t('warehouse.selectPlaceholder')"
      class="w-[160px]"
      @change="handleWarehouseChange"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { $t } from '@vben/locales';

import { Select, message } from 'ant-design-vue';

import { useWarehouseStore } from '#/store/warehouse';

const warehouseStore = useWarehouseStore();
const loading = ref(false);

// 当前选择的仓库ID
const currentWarehouseId = computed({
  get: () => warehouseStore.currentWarehouseId,
  set: (value) => {
    // 这里不直接设置，而是通过 handleWarehouseChange 处理
  },
});

// 仓库选项
const warehouseOptions = computed(() => {
  return warehouseStore.accessibleWarehouses.map(warehouse => ({
    label: warehouse.warehouseName,
    value: warehouse.warehouseId,
  }));
});

/**
 * 处理仓库切换
 */
async function handleWarehouseChange(warehouseId: number) {
  if (warehouseId === warehouseStore.currentWarehouseId) {
    return;
  }
  
  loading.value = true;
  try {
    await warehouseStore.switchWarehouse(warehouseId);
    const warehouseInfo = warehouseStore.getCurrentWarehouseInfo();
    message.success($t('warehouse.switchSuccess', { name: warehouseInfo?.warehouseName }));
    
    // 刷新页面以应用新的仓库上下文
    window.location.reload();
  } catch (error) {
    message.error($t('warehouse.switchFailed'));
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  if (!warehouseStore.initialized) {
    loading.value = true;
    try {
      await warehouseStore.initWarehouse();
    } catch (error) {
      message.error($t('warehouse.initFailed'));
    } finally {
      loading.value = false;
    }
  }
});
</script>

<style scoped>
.warehouse-toggle {
  display: flex;
  align-items: center;
}
</style>
