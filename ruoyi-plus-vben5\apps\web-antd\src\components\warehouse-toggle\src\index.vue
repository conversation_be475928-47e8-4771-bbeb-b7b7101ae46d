<script setup lang="ts">
import { computed, onMounted } from 'vue';

import { Select } from 'ant-design-vue';
import { WarehouseOutlined } from '@ant-design/icons-vue';

import { useWarehouseStore } from '#/store/warehouse';

/**
 * 仓库切换组件
 */
defineOptions({
  name: 'WarehouseToggle',
});

const warehouseStore = useWarehouseStore();

// 计算属性：选项列表
const options = computed(() =>
  warehouseStore.accessibleWarehouses.map((warehouse) => ({
    label: warehouse.warehouseName,
    value: warehouse.warehouseId,
  })),
);

// 计算属性：当前选择的值
const currentValue = computed({
  get: () => warehouseStore.currentWarehouseId,
  set: (value: number | null) => {
    if (value) {
      warehouseStore.selectWarehouse(value);
    }
  },
});

// 组件挂载时初始化数据
onMounted(async () => {
  await warehouseStore.initWarehouse();
});

/**
 * 处理仓库选择变化
 */
function handleChange(value: number) {
  warehouseStore.selectWarehouse(value);
}
</script>

<template>
  <div class="warehouse-toggle">
    <Select
      v-model:value="currentValue"
      :options="options"
      class="warehouse-select"
      placeholder="请选择仓库"
      size="small"
      @change="handleChange"
    >
      <template #suffixIcon>
        <WarehouseOutlined />
      </template>
    </Select>
  </div>
</template>

<style scoped>
.warehouse-toggle {
  display: flex;
  align-items: center;
}

.warehouse-select {
  min-width: 120px;
  max-width: 200px;
}

.warehouse-select :deep(.ant-select-selector) {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
}

.warehouse-select :deep(.ant-select-selector:hover) {
  border-color: #40a9ff;
}

.warehouse-select :deep(.ant-select-focused .ant-select-selector) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
