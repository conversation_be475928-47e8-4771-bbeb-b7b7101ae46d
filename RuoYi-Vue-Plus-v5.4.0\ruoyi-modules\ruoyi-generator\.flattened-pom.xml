<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-modules</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>ruoyi-generator</artifactId>
  <version>5.4.0</version>
  <description>generator 代码生成</description>
  <dependencies>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-log</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.anyline</groupId>
      <artifactId>anyline-environment-spring-data-jdbc</artifactId>
      <version>${anyline.version}</version>
    </dependency>
    <dependency>
      <groupId>org.anyline</groupId>
      <artifactId>anyline-data-jdbc-mysql</artifactId>
      <version>${anyline.version}</version>
    </dependency>
  </dependencies>
</project>
