package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenantPackage;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysTenantPackageToSysTenantPackageVoMapper__11.class},
    imports = {}
)
public interface SysTenantPackageVoToSysTenantPackageMapper__11 extends BaseMapper<SysTenantPackageVo, SysTenantPackage> {
}
