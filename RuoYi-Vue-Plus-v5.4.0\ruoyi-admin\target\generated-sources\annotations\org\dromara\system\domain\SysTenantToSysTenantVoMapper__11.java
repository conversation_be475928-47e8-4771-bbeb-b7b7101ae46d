package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__11;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__11;
import org.dromara.system.domain.vo.SysTenantVoToTenantListVoMapper__13;
import org.dromara.web.domain.vo.TenantListVoToSysTenantVoMapper__13;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {TenantListVoToSysTenantVoMapper__13.class,SysTenantVoToTenantListVoMapper__13.class,SysTenantBoToSysTenantMapper__11.class,SysTenantVoToSysTenantMapper__11.class},
    imports = {}
)
public interface SysTenantToSysTenantVoMapper__11 extends BaseMapper<SysTenant, SysTenantVo> {
}
