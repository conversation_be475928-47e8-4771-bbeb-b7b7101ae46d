var y=Object.defineProperty;var C=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable;var b=(r,e,o)=>e in r?y(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,h=(r,e)=>{for(var o in e||(e={}))k.call(e,o)&&b(r,o,e[o]);if(C)for(var o of C(e))w.call(e,o)&&b(r,o,e[o]);return r};var d=(r,e,o)=>new Promise((m,s)=>{var l=t=>{try{n(o.next(t))}catch(i){s(i)}},c=t=>{try{n(o.throw(t))}catch(i){s(i)}},n=t=>t.done?m(t.value):Promise.resolve(t.value).then(l,c);n((o=o.apply(r,e)).next())});import"./vxe-table-DzEj5Fop.js";import{c as v,q as V,a as q,b as B}from"./data-DW-5ziL3.js";import{_ as N}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as P,p as O,l as S,h as $,o as j,w as a,a as f,b as p,k as g,j as _,t as F}from"../jse/index-index-C-MnMZEz.js";import{u as I}from"./use-vxe-grid-BC7vZzEr.js";import{P as L}from"./index-DNdMANjv.js";import{g as T}from"./get-popup-container-P4S1sr5h.js";import"./bootstrap-DCMzVRvD.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./index-qvRUEWLR.js";const A={class:"mr-1 pl-1 text-[1rem]"},D={class:"text-primary font-bold"},lo=P({__name:"index",setup(r){const e={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:V(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},o=O(0),m={columns:v,height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:(i,...x)=>d(null,[i,...x],function*(n,t={}){const u=yield q(h({},t));return o.value=u.total,u})}},scrollY:{enabled:!0,gt:0},rowConfig:{keyField:"tokenId"},id:"monitor-online-index"},[s,l]=I({formOptions:e,gridOptions:m});function c(n){return d(this,null,function*(){yield B(n.tokenId),yield l.query()})}return(n,t)=>{const i=S("ghost-button");return j(),$(p(N),{"auto-content-height":!0},{default:a(()=>[f(p(s),null,{"toolbar-actions":a(()=>[_("div",A,[_("div",null,[t[0]||(t[0]=g(" 在线用户列表 (共 ")),_("span",D,F(o.value),1),t[1]||(t[1]=g(" 人在线) "))])])]),action:a(({row:x})=>[f(p(L),{"get-popup-container":p(T),title:`确认强制下线[${x.userName}]?`,placement:"left",onConfirm:u=>c(x)},{default:a(()=>[f(i,{danger:""},{default:a(()=>t[2]||(t[2]=[g("强制下线")])),_:1,__:[2]})]),_:2},1032,["get-popup-container","title","onConfirm"])]),_:1})]),_:1})}}});export{lo as default};
