package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__11;
import org.dromara.system.domain.vo.SysOssConfigVo;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOssConfigBoToSysOssConfigMapper__11.class,SysOssConfigVoToSysOssConfigMapper__11.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__11 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
