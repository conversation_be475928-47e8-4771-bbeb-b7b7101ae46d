import * as vue from 'vue';
import { IconifyIcon } from '@iconify/vue';
export { Icon as IconifyIcon, IconifyIcon as IconifyIconStructure, addCollection, addIcon, listIcons } from '@iconify/vue';
export { ArrowDown, ArrowLeft, ArrowLeftToLine, ArrowRightLeft, ArrowRightToLine, ArrowUp, ArrowUpToLine, Bell, BookOpenText, Check, ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Circle, CircleAlert, CircleCheckBig, CircleHelp, CircleX, Copy, CornerDownLeft, Ellipsis, Expand, ExternalLink, Eye, EyeOff, FoldHorizontal, Fullscreen, Github, Grip, GripVertical, Menu as IconDefault, Info, InspectionPanel, Languages, LoaderCircle, LockKeyhole, LogOut, MailCheck, Maximize, ArrowRightFromLine as MdiMenuClose, ArrowLeftFromLine as MdiMenuOpen, <PERSON>u, <PERSON>mize, Minimize2, MoonStar, <PERSON>lette, PanelLeft, PanelRight, Pin, PinOff, Plus, RotateCw, Search, SearchX, Settings, Shrink, Square, SquareCheckBig, SquareMinus, Sun, SunMoon, SwatchBook, UserRoundPen, X } from 'lucide-vue-next';

declare function createIconifyIcon(icon: string): vue.DefineComponent<{}, () => vue.VNode<vue.RendererNode, vue.RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, vue.ComponentOptionsMixin, vue.ComponentOptionsMixin, {}, string, vue.PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, vue.ComponentProvideOptions, true, {}, any>;
declare function createIconifyOfflineIcon(icon: string, iconComponent: IconifyIcon): vue.DefineComponent<{}, () => vue.VNode<vue.RendererNode, vue.RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, vue.ComponentOptionsMixin, vue.ComponentOptionsMixin, {}, string, vue.PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, vue.ComponentProvideOptions, true, {}, any>;

export { createIconifyIcon, createIconifyOfflineIcon };
