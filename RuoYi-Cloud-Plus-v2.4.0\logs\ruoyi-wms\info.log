2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown initiated...
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown completed.
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [wms] success,
2025-06-16 00:12:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 08:28:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:28:22 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 41884 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:28:22 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 08:28:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 08:28:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:28:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:28:28 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:28:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 08:28:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 08:28:29 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@5312c883
2025-06-16 08:28:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 08:28:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 08:28:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 08:28:29 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:28:34 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:28:34 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:28:35 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:28:35 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:28:35 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1624775
2025-06-16 08:28:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:28:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:28:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:28:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:28:38 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms ************:9220 register finished
2025-06-16 08:28:42 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 23.453 seconds (process running for 24.545)
2025-06-16 08:28:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:28:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:28:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 08:28:43 [RMI TCP Connection(3)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:01:09 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:16:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 09:16:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[29ms]
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown initiated...
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown completed.
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [wms] success,
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 09:32:53 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:32:53 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 42184 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:32:53 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 09:32:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:33:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:33:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 09:33:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 09:33:01 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@2554bfa
2025-06-16 09:33:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 09:33:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 09:33:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 09:33:02 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:33:07 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:33:07 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:33:08 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:33:08 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:33:08 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2b4d4327
2025-06-16 09:33:10 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 09:33:10 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 09:33:10 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 09:33:10 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 09:33:11 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms ************:9220 register finished
2025-06-16 09:33:15 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:33:15 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:33:16 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 26.721 seconds (process running for 28.147)
2025-06-16 09:33:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 09:33:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:33:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown initiated...
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown completed.
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [wms] success,
2025-06-16 09:37:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 09:43:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:43:22 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 50076 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:43:22 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 09:43:23 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 09:43:23 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:43:23 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:43:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:43:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 09:43:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 09:43:31 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@67d99612
2025-06-16 09:43:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 09:43:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 09:43:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 09:43:32 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:43:38 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:43:38 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:43:39 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:43:39 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:43:39 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4faf104
2025-06-16 09:43:41 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 09:43:41 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 09:43:41 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 09:43:41 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 09:43:43 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms ************:9220 register finished
2025-06-16 09:43:48 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 30.079 seconds (process running for 31.262)
2025-06-16 09:43:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 09:43:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:43:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 09:43:49 [RMI TCP Connection(1)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:43:59 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:46:39 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:46:39 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:46:39 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:46:39 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown initiated...
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown completed.
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [wms] success,
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 09:59:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:59:48 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 45620 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:59:48 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 09:59:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:55 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:59:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 09:59:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 09:59:56 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@4349f52c
2025-06-16 09:59:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 09:59:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 09:59:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 09:59:56 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:00:02 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:00:02 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:00:02 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:00:02 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:00:02 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4faf104
2025-06-16 10:00:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:00:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:00:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:00:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:00:06 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms ************:9220 register finished
2025-06-16 10:00:11 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 26.232 seconds (process running for 27.153)
2025-06-16 10:00:11 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 10:00:11 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:00:11 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 10:00:11 [RMI TCP Connection(4)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:00:19 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 10:01:23 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 10:01:23 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-06-16 10:01:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 10:01:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-16 10:01:37 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 10:01:37 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-16 10:31:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:31:45 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 16516 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:31:45 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 10:31:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 10:31:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 10:31:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:31:54 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 10:31:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 10:31:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 10:31:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@5fa73f3a
2025-06-16 10:31:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 10:31:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 10:31:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 10:31:56 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:32:03 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:32:03 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:32:03 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:32:03 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:32:03 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@16da1abc
2025-06-16 10:32:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:32:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:32:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:32:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:32:08 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms ************:9220 register finished
2025-06-16 10:32:13 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 32.704 seconds (process running for 34.042)
2025-06-16 10:32:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 10:32:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:32:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 10:32:14 [RMI TCP Connection(7)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:32:20 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown initiated...
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - wms - Shutdown completed.
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [wms] success,
2025-06-16 10:34:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 10:50:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:50:17 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 5860 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:50:17 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 10:50:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 10:50:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 10:50:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:50:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 10:50:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 10:50:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 10:50:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@57a815a
2025-06-16 10:50:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 10:50:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 10:50:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 10:50:29 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:50:35 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:50:35 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:50:36 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:50:36 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:50:36 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@671ea6ff
2025-06-16 10:50:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:50:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:50:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:50:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:50:40 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms ************:9220 register finished
2025-06-16 10:50:47 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 34.235 seconds (process running for 35.546)
2025-06-16 10:50:47 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 10:50:47 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:50:47 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 10:50:47 [RMI TCP Connection(8)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:50:54 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 10:51:14 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 10:51:14 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[87ms]
2025-06-16 17:31:59 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:31:59 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-16 17:32:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:32:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 17:33:01 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:33:01 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-06-16 17:35:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:35:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 17:35:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:35:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 17:36:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:36:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 17:37:17 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:37:17 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-16 17:37:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:37:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 17:37:57 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:37:57 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 17:38:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 17:38:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 18:00:16 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 18:00:16 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 39492 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 18:00:16 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-16 18:00:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-16 18:00:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 18:00:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 18:00:22 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 18:00:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 18:00:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-16 18:00:23 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@5fa73f3a
2025-06-16 18:00:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-16 18:00:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-16 18:00:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-16 18:00:23 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 18:00:25 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 18:00:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 18:00:26 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 18:00:26 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 18:00:26 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@16da1abc
2025-06-16 18:00:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 18:00:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 18:00:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 18:00:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 18:00:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms *************:9220 register finished
2025-06-16 18:00:32 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 17.406 seconds (process running for 18.177)
2025-06-16 18:00:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 18:00:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 18:00:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-16 18:00:32 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 18:00:42 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 18:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[49ms]
2025-06-16 18:02:15 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:15 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 18:02:27 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:27 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 18:02:32 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:32 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-16 18:02:37 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:37 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[8ms]
2025-06-16 18:02:42 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:42 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-16 18:02:46 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:46 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-16 18:02:51 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:51 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[8ms]
2025-06-16 18:02:55 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:55 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-16 18:02:59 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-16 18:02:59 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[13ms]
