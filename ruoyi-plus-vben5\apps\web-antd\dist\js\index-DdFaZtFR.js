import{e as ne,cL as ue,p as le,_ as b,aJ as fe,v as J,g as _,c as ae,bO as j,h as X,o as x,bj as ve,bP as S,cx as R,bx as ye,aC as oe,bL as pe}from"./bootstrap-DCMzVRvD.js";import{u as he,T as be,r as Ke,t as me,c as Y,f as xe,a as ke,b as ge,F as Oe,V as Ee}from"./index-BM5PBg44.js";import{a as g,d as ce,p as E,B as h,D as Ne,q as B,x as Pe,I as Se}from"../jse/index-index-C-MnMZEz.js";const Z=4;function we(e){const{dropPosition:o,dropLevelOffset:c,prefixCls:l,indent:n,direction:u="ltr"}=e,d=u==="ltr"?"left":"right",s=u==="ltr"?"right":"left",v={[d]:`${-c*n+Z}px`,[s]:0};switch(o){case-1:v.top="-3px";break;case 1:v.bottom="-3px";break;default:v.bottom="-3px",v[d]=`${n+Z}px`;break}return g("div",{style:v,class:`${l}-drop-indicator`},null)}const re=()=>{const e=me();return b(b({},e),{showLine:R([Boolean,Object]),multiple:x(),autoExpandParent:x(),checkStrictly:x(),checkable:x(),disabled:x(),defaultExpandAll:x(),defaultExpandParent:x(),defaultExpandedKeys:S(),expandedKeys:S(),checkedKeys:R([Array,Object]),defaultCheckedKeys:S(),selectedKeys:S(),defaultSelectedKeys:S(),selectable:x(),loadedKeys:S(),draggable:x(),showIcon:x(),icon:j(),switcherIcon:X.any,prefixCls:String,replaceFields:ve(),blockNode:x(),openAnimation:X.any,onDoubleclick:e.onDblclick,"onUpdate:selectedKeys":j(),"onUpdate:checkedKeys":j(),"onUpdate:expandedKeys":j()})},I=ce({compatConfig:{MODE:3},name:"ATree",inheritAttrs:!1,props:ne(re(),{checkable:!1,selectable:!0,showIcon:!1,blockNode:!1}),slots:Object,setup(e,o){let{attrs:c,expose:l,emit:n,slots:u}=o;ue(!(e.treeData===void 0&&u.default));const{prefixCls:d,direction:s,virtual:v}=le("tree",e),[y,O]=he(d),i=E();l({treeRef:i,onNodeExpand:function(){var a;(a=i.value)===null||a===void 0||a.onNodeExpand(...arguments)},scrollTo:a=>{var f;(f=i.value)===null||f===void 0||f.scrollTo(a)},selectedKeys:h(()=>{var a;return(a=i.value)===null||a===void 0?void 0:a.selectedKeys}),checkedKeys:h(()=>{var a;return(a=i.value)===null||a===void 0?void 0:a.checkedKeys}),halfCheckedKeys:h(()=>{var a;return(a=i.value)===null||a===void 0?void 0:a.halfCheckedKeys}),loadedKeys:h(()=>{var a;return(a=i.value)===null||a===void 0?void 0:a.loadedKeys}),loadingKeys:h(()=>{var a;return(a=i.value)===null||a===void 0?void 0:a.loadingKeys}),expandedKeys:h(()=>{var a;return(a=i.value)===null||a===void 0?void 0:a.expandedKeys})}),Ne(()=>{ye(e.replaceFields===void 0,"Tree","`replaceFields` is deprecated, please use fieldNames instead")});const F=(a,f)=>{n("update:checkedKeys",a),n("check",a,f)},w=(a,f)=>{n("update:expandedKeys",a),n("expand",a,f)},N=(a,f)=>{n("update:selectedKeys",a),n("select",a,f)};return()=>{const{showIcon:a,showLine:f,switcherIcon:A=u.switcherIcon,icon:L=u.icon,blockNode:H,checkable:V,selectable:C,fieldNames:T=e.replaceFields,motion:U=e.openAnimation,itemHeight:t=28,onDoubleclick:r,onDblclick:p}=e,D=b(b(b({},c),fe(e,["onUpdate:checkedKeys","onUpdate:expandedKeys","onUpdate:selectedKeys","onDoubleclick"])),{showLine:!!f,dropIndicatorRender:we,fieldNames:T,icon:L,itemHeight:t}),K=u.default?J(u.default()):void 0;return y(g(be,_(_({},D),{},{virtual:v.value,motion:U,ref:i,prefixCls:d.value,class:ae({[`${d.value}-icon-hide`]:!a,[`${d.value}-block-node`]:H,[`${d.value}-unselectable`]:!C,[`${d.value}-rtl`]:s.value==="rtl"},c.class,O.value),direction:s.value,checkable:V,selectable:C,switcherIcon:P=>Ke(d.value,A,P,u.leafIcon,f),onCheck:F,onExpand:w,onSelect:N,onDblclick:p||r,children:K}),b(b({},u),{checkable:()=>g("span",{class:`${d.value}-checkbox-inner`},null)})))}}});var Te={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};function ee(e){for(var o=1;o<arguments.length;o++){var c=arguments[o]!=null?Object(arguments[o]):{},l=Object.keys(c);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(c).filter(function(n){return Object.getOwnPropertyDescriptor(c,n).enumerable}))),l.forEach(function(n){De(e,n,c[n])})}return e}function De(e,o,c){return o in e?Object.defineProperty(e,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):e[o]=c,e}var W=function(o,c){var l=ee({},o,c.attrs);return g(oe,ee({},l,{icon:Te}),null)};W.displayName="FolderOpenOutlined";W.inheritAttrs=!1;var _e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};function te(e){for(var o=1;o<arguments.length;o++){var c=arguments[o]!=null?Object(arguments[o]):{},l=Object.keys(c);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(c).filter(function(n){return Object.getOwnPropertyDescriptor(c,n).enumerable}))),l.forEach(function(n){Ce(e,n,c[n])})}return e}function Ce(e,o,c){return o in e?Object.defineProperty(e,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):e[o]=c,e}var G=function(o,c){var l=te({},o,c.attrs);return g(oe,te({},l,{icon:_e}),null)};G.displayName="FolderOutlined";G.inheritAttrs=!1;var k;(function(e){e[e.None=0]="None",e[e.Start=1]="Start",e[e.End=2]="End"})(k||(k={}));function Q(e,o,c){function l(n){const u=n[o.key],d=n[o.children];c(u,n)!==!1&&Q(d||[],o,c)}e.forEach(l)}function $e(e){let{treeData:o,expandedKeys:c,startKey:l,endKey:n,fieldNames:u={title:"title",key:"key",children:"children"}}=e;const d=[];let s=k.None;if(l&&l===n)return[l];if(!l||!n)return[];function v(y){return y===l||y===n}return Q(o,u,y=>{if(s===k.End)return!1;if(v(y)){if(d.push(y),s===k.None)s=k.Start;else if(s===k.Start)return s=k.End,!1}else s===k.Start&&d.push(y);return c.includes(y)}),d}function M(e,o,c){const l=[...o],n=[];return Q(e,c,(u,d)=>{const s=l.indexOf(u);return s!==-1&&(n.push(d),l.splice(s,1)),!!l.length}),n}var je=function(e,o){var c={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&o.indexOf(l)<0&&(c[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)o.indexOf(l[n])<0&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(c[l[n]]=e[l[n]]);return c};const Ie=()=>b(b({},re()),{expandAction:R([Boolean,String])});function Fe(e){const{isLeaf:o,expanded:c}=e;return o?g(Oe,null,null):c?g(W,null,null):g(G,null,null)}const z=ce({compatConfig:{MODE:3},name:"ADirectoryTree",inheritAttrs:!1,props:ne(Ie(),{showIcon:!0,expandAction:"click"}),slots:Object,setup(e,o){let{attrs:c,slots:l,emit:n,expose:u}=o;var d;const s=E(e.treeData||Y(J((d=l.default)===null||d===void 0?void 0:d.call(l))));B(()=>e.treeData,()=>{s.value=e.treeData}),Pe(()=>{Se(()=>{var t;e.treeData===void 0&&l.default&&(s.value=Y(J((t=l.default)===null||t===void 0?void 0:t.call(l))))})});const v=E(),y=E(),O=h(()=>xe(e.fieldNames)),i=E();u({scrollTo:t=>{var r;(r=i.value)===null||r===void 0||r.scrollTo(t)},selectedKeys:h(()=>{var t;return(t=i.value)===null||t===void 0?void 0:t.selectedKeys}),checkedKeys:h(()=>{var t;return(t=i.value)===null||t===void 0?void 0:t.checkedKeys}),halfCheckedKeys:h(()=>{var t;return(t=i.value)===null||t===void 0?void 0:t.halfCheckedKeys}),loadedKeys:h(()=>{var t;return(t=i.value)===null||t===void 0?void 0:t.loadedKeys}),loadingKeys:h(()=>{var t;return(t=i.value)===null||t===void 0?void 0:t.loadingKeys}),expandedKeys:h(()=>{var t;return(t=i.value)===null||t===void 0?void 0:t.expandedKeys})});const F=()=>{const{keyEntities:t}=ke(s.value,{fieldNames:O.value});let r;return e.defaultExpandAll?r=Object.keys(t):e.defaultExpandParent?r=ge(e.expandedKeys||e.defaultExpandedKeys||[],t):r=e.expandedKeys||e.defaultExpandedKeys,r},w=E(e.selectedKeys||e.defaultSelectedKeys||[]),N=E(F());B(()=>e.selectedKeys,()=>{e.selectedKeys!==void 0&&(w.value=e.selectedKeys)},{immediate:!0}),B(()=>e.expandedKeys,()=>{e.expandedKeys!==void 0&&(N.value=e.expandedKeys)},{immediate:!0});const f=pe((t,r)=>{const{isLeaf:p}=r;p||t.shiftKey||t.metaKey||t.ctrlKey||i.value.onNodeExpand(t,r)},200,{leading:!0}),A=(t,r)=>{e.expandedKeys===void 0&&(N.value=t),n("update:expandedKeys",t),n("expand",t,r)},L=(t,r)=>{const{expandAction:p}=e;p==="click"&&f(t,r),n("click",t,r)},H=(t,r)=>{const{expandAction:p}=e;(p==="dblclick"||p==="doubleclick")&&f(t,r),n("doubleclick",t,r),n("dblclick",t,r)},V=(t,r)=>{const{multiple:p}=e,{node:D,nativeEvent:K}=r,P=D[O.value.key],$=b(b({},r),{selected:!0}),se=(K==null?void 0:K.ctrlKey)||(K==null?void 0:K.metaKey),ie=K==null?void 0:K.shiftKey;let m;p&&se?(m=t,v.value=P,y.value=m,$.selectedNodes=M(s.value,m,O.value)):p&&ie?(m=Array.from(new Set([...y.value||[],...$e({treeData:s.value,expandedKeys:N.value,startKey:P,endKey:v.value,fieldNames:O.value})])),$.selectedNodes=M(s.value,m,O.value)):(m=[P],v.value=P,y.value=m,$.selectedNodes=M(s.value,m,O.value)),n("update:selectedKeys",m),n("select",m,$),e.selectedKeys===void 0&&(w.value=m)},C=(t,r)=>{n("update:checkedKeys",t),n("check",t,r)},{prefixCls:T,direction:U}=le("tree",e);return()=>{const t=ae(`${T.value}-directory`,{[`${T.value}-directory-rtl`]:U.value==="rtl"},c.class),{icon:r=l.icon,blockNode:p=!0}=e,D=je(e,["icon","blockNode"]);return g(I,_(_(_({},c),{},{icon:r||Fe,ref:i,blockNode:p},D),{},{prefixCls:T.value,class:t,expandedKeys:N.value,selectedKeys:w.value,onSelect:V,onClick:L,onDblclick:H,onExpand:A,onCheck:C}),l)}}}),q=Ee,Ve=b(I,{DirectoryTree:z,TreeNode:q,install:e=>(e.component(I.name,I),e.component(q.name,q),e.component(z.name,z),e)});export{Ve as T};
