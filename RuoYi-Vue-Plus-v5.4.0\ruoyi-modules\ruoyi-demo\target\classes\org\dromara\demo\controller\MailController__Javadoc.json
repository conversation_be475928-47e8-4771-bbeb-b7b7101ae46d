{"doc": " 邮件发送案例\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendSimpleMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送邮件\n\n @param to      接收人\n @param subject 标题\n @param text    内容\n"}, {"name": "sendMessageWithAttachment", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送邮件（带附件）\n\n @param to       接收人\n @param subject  标题\n @param text     内容\n @param filePath 附件路径\n"}, {"name": "sendMessageWithAttachments", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String[]"], "doc": " 发送邮件（多附件）\n\n @param to       接收人\n @param subject  标题\n @param text     内容\n @param paths    附件路径\n"}], "constructors": []}