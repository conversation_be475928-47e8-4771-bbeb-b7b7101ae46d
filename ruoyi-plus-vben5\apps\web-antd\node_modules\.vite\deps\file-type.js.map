{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ieee754@1.2.1/node_modules/ieee754/index.js", "../../../../../node_modules/.pnpm/token-types@6.0.0/node_modules/token-types/lib/index.js", "../../../../../node_modules/.pnpm/peek-readable@5.4.2/node_modules/peek-readable/lib/EndOfStreamError.js", "../../../../../node_modules/.pnpm/peek-readable@5.4.2/node_modules/peek-readable/lib/AbstractStreamReader.js", "../../../../../node_modules/.pnpm/peek-readable@5.4.2/node_modules/peek-readable/lib/WebStreamReader.js", "../../../../../node_modules/.pnpm/strtok3@9.1.1/node_modules/strtok3/lib/AbstractTokenizer.js", "../../../../../node_modules/.pnpm/strtok3@9.1.1/node_modules/strtok3/lib/ReadStreamTokenizer.js", "../../../../../node_modules/.pnpm/strtok3@9.1.1/node_modules/strtok3/lib/BufferTokenizer.js", "../../../../../node_modules/.pnpm/strtok3@9.1.1/node_modules/strtok3/lib/core.js", "../../../../../node_modules/.pnpm/uint8array-extras@1.4.0/node_modules/uint8array-extras/index.js", "../../../../../node_modules/.pnpm/file-type@19.6.0/node_modules/file-type/util.js", "../../../../../node_modules/.pnpm/file-type@19.6.0/node_modules/file-type/supported.js", "../../../../../node_modules/.pnpm/file-type@19.6.0/node_modules/file-type/core.js"], "sourcesContent": ["/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "import * as ieee754 from 'ieee754';\n// Primitive types\nfunction dv(array) {\n    return new DataView(array.buffer, array.byteOffset);\n}\n/**\n * 8-bit unsigned integer\n */\nexport const UINT8 = {\n    len: 1,\n    get(array, offset) {\n        return dv(array).getUint8(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setUint8(offset, value);\n        return offset + 1;\n    }\n};\n/**\n * 16-bit unsigned integer, Little Endian byte order\n */\nexport const UINT16_LE = {\n    len: 2,\n    get(array, offset) {\n        return dv(array).getUint16(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setUint16(offset, value, true);\n        return offset + 2;\n    }\n};\n/**\n * 16-bit unsigned integer, Big Endian byte order\n */\nexport const UINT16_BE = {\n    len: 2,\n    get(array, offset) {\n        return dv(array).getUint16(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setUint16(offset, value);\n        return offset + 2;\n    }\n};\n/**\n * 24-bit unsigned integer, Little Endian byte order\n */\nexport const UINT24_LE = {\n    len: 3,\n    get(array, offset) {\n        const dataView = dv(array);\n        return dataView.getUint8(offset) + (dataView.getUint16(offset + 1, true) << 8);\n    },\n    put(array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint8(offset, value & 0xff);\n        dataView.setUint16(offset + 1, value >> 8, true);\n        return offset + 3;\n    }\n};\n/**\n * 24-bit unsigned integer, Big Endian byte order\n */\nexport const UINT24_BE = {\n    len: 3,\n    get(array, offset) {\n        const dataView = dv(array);\n        return (dataView.getUint16(offset) << 8) + dataView.getUint8(offset + 2);\n    },\n    put(array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint16(offset, value >> 8);\n        dataView.setUint8(offset + 2, value & 0xff);\n        return offset + 3;\n    }\n};\n/**\n * 32-bit unsigned integer, Little Endian byte order\n */\nexport const UINT32_LE = {\n    len: 4,\n    get(array, offset) {\n        return dv(array).getUint32(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setUint32(offset, value, true);\n        return offset + 4;\n    }\n};\n/**\n * 32-bit unsigned integer, Big Endian byte order\n */\nexport const UINT32_BE = {\n    len: 4,\n    get(array, offset) {\n        return dv(array).getUint32(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setUint32(offset, value);\n        return offset + 4;\n    }\n};\n/**\n * 8-bit signed integer\n */\nexport const INT8 = {\n    len: 1,\n    get(array, offset) {\n        return dv(array).getInt8(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setInt8(offset, value);\n        return offset + 1;\n    }\n};\n/**\n * 16-bit signed integer, Big Endian byte order\n */\nexport const INT16_BE = {\n    len: 2,\n    get(array, offset) {\n        return dv(array).getInt16(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setInt16(offset, value);\n        return offset + 2;\n    }\n};\n/**\n * 16-bit signed integer, Little Endian byte order\n */\nexport const INT16_LE = {\n    len: 2,\n    get(array, offset) {\n        return dv(array).getInt16(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setInt16(offset, value, true);\n        return offset + 2;\n    }\n};\n/**\n * 24-bit signed integer, Little Endian byte order\n */\nexport const INT24_LE = {\n    len: 3,\n    get(array, offset) {\n        const unsigned = UINT24_LE.get(array, offset);\n        return unsigned > 0x7fffff ? unsigned - 0x1000000 : unsigned;\n    },\n    put(array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint8(offset, value & 0xff);\n        dataView.setUint16(offset + 1, value >> 8, true);\n        return offset + 3;\n    }\n};\n/**\n * 24-bit signed integer, Big Endian byte order\n */\nexport const INT24_BE = {\n    len: 3,\n    get(array, offset) {\n        const unsigned = UINT24_BE.get(array, offset);\n        return unsigned > 0x7fffff ? unsigned - 0x1000000 : unsigned;\n    },\n    put(array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint16(offset, value >> 8);\n        dataView.setUint8(offset + 2, value & 0xff);\n        return offset + 3;\n    }\n};\n/**\n * 32-bit signed integer, Big Endian byte order\n */\nexport const INT32_BE = {\n    len: 4,\n    get(array, offset) {\n        return dv(array).getInt32(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setInt32(offset, value);\n        return offset + 4;\n    }\n};\n/**\n * 32-bit signed integer, Big Endian byte order\n */\nexport const INT32_LE = {\n    len: 4,\n    get(array, offset) {\n        return dv(array).getInt32(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setInt32(offset, value, true);\n        return offset + 4;\n    }\n};\n/**\n * 64-bit unsigned integer, Little Endian byte order\n */\nexport const UINT64_LE = {\n    len: 8,\n    get(array, offset) {\n        return dv(array).getBigUint64(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setBigUint64(offset, value, true);\n        return offset + 8;\n    }\n};\n/**\n * 64-bit signed integer, Little Endian byte order\n */\nexport const INT64_LE = {\n    len: 8,\n    get(array, offset) {\n        return dv(array).getBigInt64(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setBigInt64(offset, value, true);\n        return offset + 8;\n    }\n};\n/**\n * 64-bit unsigned integer, Big Endian byte order\n */\nexport const UINT64_BE = {\n    len: 8,\n    get(array, offset) {\n        return dv(array).getBigUint64(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setBigUint64(offset, value);\n        return offset + 8;\n    }\n};\n/**\n * 64-bit signed integer, Big Endian byte order\n */\nexport const INT64_BE = {\n    len: 8,\n    get(array, offset) {\n        return dv(array).getBigInt64(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setBigInt64(offset, value);\n        return offset + 8;\n    }\n};\n/**\n * IEEE 754 16-bit (half precision) float, big endian\n */\nexport const Float16_BE = {\n    len: 2,\n    get(dataView, offset) {\n        return ieee754.read(dataView, offset, false, 10, this.len);\n    },\n    put(dataView, offset, value) {\n        ieee754.write(dataView, value, offset, false, 10, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * IEEE 754 16-bit (half precision) float, little endian\n */\nexport const Float16_LE = {\n    len: 2,\n    get(array, offset) {\n        return ieee754.read(array, offset, true, 10, this.len);\n    },\n    put(array, offset, value) {\n        ieee754.write(array, value, offset, true, 10, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * IEEE 754 32-bit (single precision) float, big endian\n */\nexport const Float32_BE = {\n    len: 4,\n    get(array, offset) {\n        return dv(array).getFloat32(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setFloat32(offset, value);\n        return offset + 4;\n    }\n};\n/**\n * IEEE 754 32-bit (single precision) float, little endian\n */\nexport const Float32_LE = {\n    len: 4,\n    get(array, offset) {\n        return dv(array).getFloat32(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setFloat32(offset, value, true);\n        return offset + 4;\n    }\n};\n/**\n * IEEE 754 64-bit (double precision) float, big endian\n */\nexport const Float64_BE = {\n    len: 8,\n    get(array, offset) {\n        return dv(array).getFloat64(offset);\n    },\n    put(array, offset, value) {\n        dv(array).setFloat64(offset, value);\n        return offset + 8;\n    }\n};\n/**\n * IEEE 754 64-bit (double precision) float, little endian\n */\nexport const Float64_LE = {\n    len: 8,\n    get(array, offset) {\n        return dv(array).getFloat64(offset, true);\n    },\n    put(array, offset, value) {\n        dv(array).setFloat64(offset, value, true);\n        return offset + 8;\n    }\n};\n/**\n * IEEE 754 80-bit (extended precision) float, big endian\n */\nexport const Float80_BE = {\n    len: 10,\n    get(array, offset) {\n        return ieee754.read(array, offset, false, 63, this.len);\n    },\n    put(array, offset, value) {\n        ieee754.write(array, value, offset, false, 63, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * IEEE 754 80-bit (extended precision) float, little endian\n */\nexport const Float80_LE = {\n    len: 10,\n    get(array, offset) {\n        return ieee754.read(array, offset, true, 63, this.len);\n    },\n    put(array, offset, value) {\n        ieee754.write(array, value, offset, true, 63, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * Ignore a given number of bytes\n */\nexport class IgnoreType {\n    /**\n     * @param len number of bytes to ignore\n     */\n    constructor(len) {\n        this.len = len;\n    }\n    // ToDo: don't read, but skip data\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    get(array, off) {\n    }\n}\nexport class Uint8ArrayType {\n    constructor(len) {\n        this.len = len;\n    }\n    get(array, offset) {\n        return array.subarray(offset, offset + this.len);\n    }\n}\n/**\n * Consume a fixed number of bytes from the stream and return a string with a specified encoding.\n */\nexport class StringType {\n    constructor(len, encoding) {\n        this.len = len;\n        this.encoding = encoding;\n        this.textDecoder = new TextDecoder(encoding);\n    }\n    get(uint8Array, offset) {\n        return this.textDecoder.decode(uint8Array.subarray(offset, offset + this.len));\n    }\n}\n/**\n * ANSI Latin 1 String\n * Using windows-1252 / ISO 8859-1 decoding\n */\nexport class AnsiStringType {\n    constructor(len) {\n        this.len = len;\n        this.textDecoder = new TextDecoder('windows-1252');\n    }\n    get(uint8Array, offset = 0) {\n        return this.textDecoder.decode(uint8Array.subarray(offset, offset + this.len));\n    }\n}\n", "export const defaultMessages = 'End-Of-Stream';\n/**\n * Thrown on read operation of the end of file or stream has been reached\n */\nexport class EndOfStreamError extends Error {\n    constructor() {\n        super(defaultMessages);\n    }\n}\n", "import { EndOfStreamError } from \"./EndOfStreamError.js\";\nexport class AbstractStreamReader {\n    constructor() {\n        /**\n         * Maximum request length on read-stream operation\n         */\n        this.maxStreamReadSize = 1 * 1024 * 1024;\n        this.endOfStream = false;\n        /**\n         * Store peeked data\n         * @type {Array}\n         */\n        this.peekQueue = [];\n    }\n    async peek(uint8Array, offset, length) {\n        const bytesRead = await this.read(uint8Array, offset, length);\n        this.peekQueue.push(uint8Array.subarray(offset, offset + bytesRead)); // Put read data back to peek buffer\n        return bytesRead;\n    }\n    async read(buffer, offset, length) {\n        if (length === 0) {\n            return 0;\n        }\n        let bytesRead = this.readFromPeekBuffer(buffer, offset, length);\n        bytesRead += await this.readRemainderFromStream(buffer, offset + bytesRead, length - bytesRead);\n        if (bytesRead === 0) {\n            throw new EndOfStreamError();\n        }\n        return bytesRead;\n    }\n    /**\n     * Read chunk from stream\n     * @param buffer - Target Uint8Array (or Buffer) to store data read from stream in\n     * @param offset - Offset target\n     * @param length - Number of bytes to read\n     * @returns Number of bytes read\n     */\n    readFromPeekBuffer(buffer, offset, length) {\n        let remaining = length;\n        let bytesRead = 0;\n        // consume peeked data first\n        while (this.peekQueue.length > 0 && remaining > 0) {\n            const peekData = this.peekQueue.pop(); // Front of queue\n            if (!peekData)\n                throw new Error('peekData should be defined');\n            const lenCopy = Math.min(peekData.length, remaining);\n            buffer.set(peekData.subarray(0, lenCopy), offset + bytesRead);\n            bytesRead += lenCopy;\n            remaining -= lenCopy;\n            if (lenCopy < peekData.length) {\n                // remainder back to queue\n                this.peekQueue.push(peekData.subarray(lenCopy));\n            }\n        }\n        return bytesRead;\n    }\n    async readRemainderFromStream(buffer, offset, initialRemaining) {\n        let remaining = initialRemaining;\n        let bytesRead = 0;\n        // Continue reading from stream if required\n        while (remaining > 0 && !this.endOfStream) {\n            const reqLen = Math.min(remaining, this.maxStreamReadSize);\n            const chunkLen = await this.readFromStream(buffer, offset + bytesRead, reqLen);\n            if (chunkLen === 0)\n                break;\n            bytesRead += chunkLen;\n            remaining -= chunkLen;\n        }\n        return bytesRead;\n    }\n}\n", "import { EndOfStreamError } from './EndOfStreamError.js';\nexport { EndOfStreamError } from './EndOfStreamError.js';\nimport { AbstractStreamReader } from \"./AbstractStreamReader.js\";\n/**\n * Read from a WebStream\n * Reference: https://nodejs.org/api/webstreams.html#class-readablestreambyobreader\n */\nexport class WebStreamReader extends AbstractStreamReader {\n    constructor(stream) {\n        super();\n        this.reader = stream.getReader({ mode: 'byob' });\n    }\n    async readFromStream(buffer, offset, length) {\n        if (this.endOfStream) {\n            throw new EndOfStreamError();\n        }\n        const result = await this.reader.read(new Uint8Array(length));\n        if (result.done) {\n            this.endOfStream = result.done;\n        }\n        if (result.value) {\n            buffer.set(result.value, offset);\n            return result.value.byteLength;\n        }\n        return 0;\n    }\n    abort() {\n        return this.reader.cancel(); // Signals a loss of interest in the stream by a consumer\n    }\n    async close() {\n        await this.abort();\n        this.reader.releaseLock();\n    }\n}\n", "import { EndOfStreamError } from 'peek-readable';\n/**\n * Core tokenizer\n */\nexport class AbstractTokenizer {\n    /**\n     * Constructor\n     * @param options Tokenizer options\n     * @protected\n     */\n    constructor(options) {\n        this.numBuffer = new Uint8Array(8);\n        /**\n         * Tokenizer-stream position\n         */\n        this.position = 0;\n        this.onClose = options?.onClose;\n        if (options?.abortSignal) {\n            options.abortSignal.addEventListener('abort', () => {\n                this.abort();\n            });\n        }\n    }\n    /**\n     * Read a token from the tokenizer-stream\n     * @param token - The token to read\n     * @param position - If provided, the desired position in the tokenizer-stream\n     * @returns Promise with token data\n     */\n    async readToken(token, position = this.position) {\n        const uint8Array = new Uint8Array(token.len);\n        const len = await this.readBuffer(uint8Array, { position });\n        if (len < token.len)\n            throw new EndOfStreamError();\n        return token.get(uint8Array, 0);\n    }\n    /**\n     * Peek a token from the tokenizer-stream.\n     * @param token - Token to peek from the tokenizer-stream.\n     * @param position - Offset where to begin reading within the file. If position is null, data will be read from the current file position.\n     * @returns Promise with token data\n     */\n    async peekToken(token, position = this.position) {\n        const uint8Array = new Uint8Array(token.len);\n        const len = await this.peekBuffer(uint8Array, { position });\n        if (len < token.len)\n            throw new EndOfStreamError();\n        return token.get(uint8Array, 0);\n    }\n    /**\n     * Read a numeric token from the stream\n     * @param token - Numeric token\n     * @returns Promise with number\n     */\n    async readNumber(token) {\n        const len = await this.readBuffer(this.numBuffer, { length: token.len });\n        if (len < token.len)\n            throw new EndOfStreamError();\n        return token.get(this.numBuffer, 0);\n    }\n    /**\n     * Read a numeric token from the stream\n     * @param token - Numeric token\n     * @returns Promise with number\n     */\n    async peekNumber(token) {\n        const len = await this.peekBuffer(this.numBuffer, { length: token.len });\n        if (len < token.len)\n            throw new EndOfStreamError();\n        return token.get(this.numBuffer, 0);\n    }\n    /**\n     * Ignore number of bytes, advances the pointer in under tokenizer-stream.\n     * @param length - Number of bytes to ignore\n     * @return resolves the number of bytes ignored, equals length if this available, otherwise the number of bytes available\n     */\n    async ignore(length) {\n        if (this.fileInfo.size !== undefined) {\n            const bytesLeft = this.fileInfo.size - this.position;\n            if (length > bytesLeft) {\n                this.position += bytesLeft;\n                return bytesLeft;\n            }\n        }\n        this.position += length;\n        return length;\n    }\n    async close() {\n        await this.abort();\n        await this.onClose?.();\n    }\n    normalizeOptions(uint8Array, options) {\n        if (options && options.position !== undefined && options.position < this.position) {\n            throw new Error('`options.position` must be equal or greater than `tokenizer.position`');\n        }\n        if (options) {\n            return {\n                mayBeLess: options.mayBeLess === true,\n                offset: options.offset ? options.offset : 0,\n                length: options.length ? options.length : (uint8Array.length - (options.offset ? options.offset : 0)),\n                position: options.position ? options.position : this.position\n            };\n        }\n        return {\n            mayBeLess: false,\n            offset: 0,\n            length: uint8Array.length,\n            position: this.position\n        };\n    }\n    abort() {\n        return Promise.resolve(); // Ignore abort signal\n    }\n}\n", "import { AbstractTokenizer } from './AbstractTokenizer.js';\nimport { EndOfStreamError } from 'peek-readable';\nconst maxBufferSize = 256000;\nexport class ReadStreamTokenizer extends AbstractTokenizer {\n    /**\n     * Constructor\n     * @param streamReader stream-reader to read from\n     * @param options Tokenizer options\n     */\n    constructor(streamReader, options) {\n        super(options);\n        this.streamReader = streamReader;\n        this.fileInfo = options?.fileInfo ?? {};\n    }\n    /**\n     * Read buffer from tokenizer\n     * @param uint8Array - Target Uint8Array to fill with data read from the tokenizer-stream\n     * @param options - Read behaviour options\n     * @returns Promise with number of bytes read\n     */\n    async readBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const skipBytes = normOptions.position - this.position;\n        if (skipBytes > 0) {\n            await this.ignore(skipBytes);\n            return this.readBuffer(uint8Array, options);\n        }\n        if (skipBytes < 0) {\n            throw new Error('`options.position` must be equal or greater than `tokenizer.position`');\n        }\n        if (normOptions.length === 0) {\n            return 0;\n        }\n        const bytesRead = await this.streamReader.read(uint8Array, normOptions.offset, normOptions.length);\n        this.position += bytesRead;\n        if ((!options || !options.mayBeLess) && bytesRead < normOptions.length) {\n            throw new EndOfStreamError();\n        }\n        return bytesRead;\n    }\n    /**\n     * Peek (read ahead) buffer from tokenizer\n     * @param uint8Array - Uint8Array (or Buffer) to write data to\n     * @param options - Read behaviour options\n     * @returns Promise with number of bytes peeked\n     */\n    async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        let bytesRead = 0;\n        if (normOptions.position) {\n            const skipBytes = normOptions.position - this.position;\n            if (skipBytes > 0) {\n                const skipBuffer = new Uint8Array(normOptions.length + skipBytes);\n                bytesRead = await this.peekBuffer(skipBuffer, { mayBeLess: normOptions.mayBeLess });\n                uint8Array.set(skipBuffer.subarray(skipBytes), normOptions.offset);\n                return bytesRead - skipBytes;\n            }\n            if (skipBytes < 0) {\n                throw new Error('Cannot peek from a negative offset in a stream');\n            }\n        }\n        if (normOptions.length > 0) {\n            try {\n                bytesRead = await this.streamReader.peek(uint8Array, normOptions.offset, normOptions.length);\n            }\n            catch (err) {\n                if (options?.mayBeLess && err instanceof EndOfStreamError) {\n                    return 0;\n                }\n                throw err;\n            }\n            if ((!normOptions.mayBeLess) && bytesRead < normOptions.length) {\n                throw new EndOfStreamError();\n            }\n        }\n        return bytesRead;\n    }\n    async ignore(length) {\n        // debug(`ignore ${this.position}...${this.position + length - 1}`);\n        const bufSize = Math.min(maxBufferSize, length);\n        const buf = new Uint8Array(bufSize);\n        let totBytesRead = 0;\n        while (totBytesRead < length) {\n            const remaining = length - totBytesRead;\n            const bytesRead = await this.readBuffer(buf, { length: Math.min(bufSize, remaining) });\n            if (bytesRead < 0) {\n                return bytesRead;\n            }\n            totBytesRead += bytesRead;\n        }\n        return totBytesRead;\n    }\n    abort() {\n        return this.streamReader.abort();\n    }\n    supportsRandomAccess() {\n        return false;\n    }\n}\n", "import { EndOfStreamError } from 'peek-readable';\nimport { AbstractTokenizer } from './AbstractTokenizer.js';\nexport class BufferTokenizer extends AbstractTokenizer {\n    /**\n     * Construct BufferTokenizer\n     * @param uint8Array - Uint8Array to tokenize\n     * @param options Tokenizer options\n     */\n    constructor(uint8Array, options) {\n        super(options);\n        this.uint8Array = uint8Array;\n        this.fileInfo = { ...options?.fileInfo ?? {}, ...{ size: uint8Array.length } };\n    }\n    /**\n     * Read buffer from tokenizer\n     * @param uint8Array - Uint8Array to tokenize\n     * @param options - Read behaviour options\n     * @returns {Promise<number>}\n     */\n    async readBuffer(uint8Array, options) {\n        if (options?.position) {\n            if (options.position < this.position) {\n                throw new Error('`options.position` must be equal or greater than `tokenizer.position`');\n            }\n            this.position = options.position;\n        }\n        const bytesRead = await this.peekBuffer(uint8Array, options);\n        this.position += bytesRead;\n        return bytesRead;\n    }\n    /**\n     * Peek (read ahead) buffer from tokenizer\n     * @param uint8Array\n     * @param options - Read behaviour options\n     * @returns {Promise<number>}\n     */\n    async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const bytes2read = Math.min(this.uint8Array.length - normOptions.position, normOptions.length);\n        if ((!normOptions.mayBeLess) && bytes2read < normOptions.length) {\n            throw new EndOfStreamError();\n        }\n        uint8Array.set(this.uint8Array.subarray(normOptions.position, normOptions.position + bytes2read), normOptions.offset);\n        return bytes2read;\n    }\n    close() {\n        return super.close();\n    }\n    supportsRandomAccess() {\n        return true;\n    }\n    setPosition(position) {\n        this.position = position;\n    }\n}\n", "import { StreamReader, WebStreamReader } from 'peek-readable';\nimport { ReadStreamTokenizer } from './ReadStreamTokenizer.js';\nimport { BufferTokenizer } from './BufferTokenizer.js';\nexport { EndOfStreamError } from 'peek-readable';\nexport { AbstractTokenizer } from './AbstractTokenizer.js';\n/**\n * Construct ReadStreamTokenizer from given Stream.\n * Will set fileSize, if provided given Stream has set the .path property/\n * @param stream - Read from Node.js Stream.Readable\n * @param options - Tokenizer options\n * @returns ReadStreamTokenizer\n */\nexport function fromStream(stream, options) {\n    return new ReadStreamTokenizer(new StreamReader(stream), options);\n}\n/**\n * Construct ReadStreamTokenizer from given ReadableStream (WebStream API).\n * Will set fileSize, if provided given Stream has set the .path property/\n * @param webStream - Read from Node.js Stream.Readable (must be a byte stream)\n * @param options - Tokenizer options\n * @returns ReadStreamTokenizer\n */\nexport function fromWebStream(webStream, options) {\n    return new ReadStreamTokenizer(new WebStreamReader(webStream), options);\n}\n/**\n * Construct ReadStreamTokenizer from given Buffer.\n * @param uint8Array - Uint8Array to tokenize\n * @param options - Tokenizer options\n * @returns BufferTokenizer\n */\nexport function fromBuffer(uint8Array, options) {\n    return new BufferTokenizer(uint8Array, options);\n}\n", "const objectToString = Object.prototype.toString;\nconst uint8ArrayStringified = '[object Uint8Array]';\nconst arrayBufferStringified = '[object ArrayBuffer]';\n\nfunction isType(value, typeConstructor, typeStringified) {\n\tif (!value) {\n\t\treturn false;\n\t}\n\n\tif (value.constructor === typeConstructor) {\n\t\treturn true;\n\t}\n\n\treturn objectToString.call(value) === typeStringified;\n}\n\nexport function isUint8Array(value) {\n\treturn isType(value, Uint8Array, uint8ArrayStringified);\n}\n\nfunction isArrayBuffer(value) {\n\treturn isType(value, ArrayBuffer, arrayBufferStringified);\n}\n\nfunction isUint8ArrayOrArrayBuffer(value) {\n\treturn isUint8Array(value) || isArrayBuffer(value);\n}\n\nexport function assertUint8Array(value) {\n\tif (!isUint8Array(value)) {\n\t\tthrow new TypeError(`Expected \\`Uint8Array\\`, got \\`${typeof value}\\``);\n\t}\n}\n\nexport function assertUint8ArrayOrArrayBuffer(value) {\n\tif (!isUint8ArrayOrArrayBuffer(value)) {\n\t\tthrow new TypeError(`Expected \\`Uint8Array\\` or \\`ArrayBuffer\\`, got \\`${typeof value}\\``);\n\t}\n}\n\nexport function toUint8Array(value) {\n\tif (value instanceof ArrayBuffer) {\n\t\treturn new Uint8Array(value);\n\t}\n\n\tif (ArrayBuffer.isView(value)) {\n\t\treturn new Uint8Array(value.buffer, value.byteOffset, value.byteLength);\n\t}\n\n\tthrow new TypeError(`Unsupported value, got \\`${typeof value}\\`.`);\n}\n\nexport function concatUint8Arrays(arrays, totalLength) {\n\tif (arrays.length === 0) {\n\t\treturn new Uint8Array(0);\n\t}\n\n\ttotalLength ??= arrays.reduce((accumulator, currentValue) => accumulator + currentValue.length, 0);\n\n\tconst returnValue = new Uint8Array(totalLength);\n\n\tlet offset = 0;\n\tfor (const array of arrays) {\n\t\tassertUint8Array(array);\n\t\treturnValue.set(array, offset);\n\t\toffset += array.length;\n\t}\n\n\treturn returnValue;\n}\n\nexport function areUint8ArraysEqual(a, b) {\n\tassertUint8Array(a);\n\tassertUint8Array(b);\n\n\tif (a === b) {\n\t\treturn true;\n\t}\n\n\tif (a.length !== b.length) {\n\t\treturn false;\n\t}\n\n\t// eslint-disable-next-line unicorn/no-for-loop\n\tfor (let index = 0; index < a.length; index++) {\n\t\tif (a[index] !== b[index]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\nexport function compareUint8Arrays(a, b) {\n\tassertUint8Array(a);\n\tassertUint8Array(b);\n\n\tconst length = Math.min(a.length, b.length);\n\n\tfor (let index = 0; index < length; index++) {\n\t\tconst diff = a[index] - b[index];\n\t\tif (diff !== 0) {\n\t\t\treturn Math.sign(diff);\n\t\t}\n\t}\n\n\t// At this point, all the compared elements are equal.\n\t// The shorter array should come first if the arrays are of different lengths.\n\treturn Math.sign(a.length - b.length);\n}\n\nconst cachedDecoders = {\n\tutf8: new globalThis.TextDecoder('utf8'),\n};\n\nexport function uint8ArrayToString(array, encoding = 'utf8') {\n\tassertUint8ArrayOrArrayBuffer(array);\n\tcachedDecoders[encoding] ??= new globalThis.TextDecoder(encoding);\n\treturn cachedDecoders[encoding].decode(array);\n}\n\nfunction assertString(value) {\n\tif (typeof value !== 'string') {\n\t\tthrow new TypeError(`Expected \\`string\\`, got \\`${typeof value}\\``);\n\t}\n}\n\nconst cachedEncoder = new globalThis.TextEncoder();\n\nexport function stringToUint8Array(string) {\n\tassertString(string);\n\treturn cachedEncoder.encode(string);\n}\n\nfunction base64ToBase64Url(base64) {\n\treturn base64.replaceAll('+', '-').replaceAll('/', '_').replace(/=+$/, '');\n}\n\nfunction base64UrlToBase64(base64url) {\n\treturn base64url.replaceAll('-', '+').replaceAll('_', '/');\n}\n\n// Reference: https://phuoc.ng/collection/this-vs-that/concat-vs-push/\nconst MAX_BLOCK_SIZE = 65_535;\n\nexport function uint8ArrayToBase64(array, {urlSafe = false} = {}) {\n\tassertUint8Array(array);\n\n\tlet base64;\n\n\tif (array.length < MAX_BLOCK_SIZE) {\n\t// Required as `btoa` and `atob` don't properly support Unicode: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n\t\tbase64 = globalThis.btoa(String.fromCodePoint.apply(this, array));\n\t} else {\n\t\tbase64 = '';\n\t\tfor (const value of array) {\n\t\t\tbase64 += String.fromCodePoint(value);\n\t\t}\n\n\t\tbase64 = globalThis.btoa(base64);\n\t}\n\n\treturn urlSafe ? base64ToBase64Url(base64) : base64;\n}\n\nexport function base64ToUint8Array(base64String) {\n\tassertString(base64String);\n\treturn Uint8Array.from(globalThis.atob(base64UrlToBase64(base64String)), x => x.codePointAt(0));\n}\n\nexport function stringToBase64(string, {urlSafe = false} = {}) {\n\tassertString(string);\n\treturn uint8ArrayToBase64(stringToUint8Array(string), {urlSafe});\n}\n\nexport function base64ToString(base64String) {\n\tassertString(base64String);\n\treturn uint8ArrayToString(base64ToUint8Array(base64String));\n}\n\nconst byteToHexLookupTable = Array.from({length: 256}, (_, index) => index.toString(16).padStart(2, '0'));\n\nexport function uint8ArrayToHex(array) {\n\tassertUint8Array(array);\n\n\t// Concatenating a string is faster than using an array.\n\tlet hexString = '';\n\n\t// eslint-disable-next-line unicorn/no-for-loop -- Max performance is critical.\n\tfor (let index = 0; index < array.length; index++) {\n\t\thexString += byteToHexLookupTable[array[index]];\n\t}\n\n\treturn hexString;\n}\n\nconst hexToDecimalLookupTable = {\n\t0: 0,\n\t1: 1,\n\t2: 2,\n\t3: 3,\n\t4: 4,\n\t5: 5,\n\t6: 6,\n\t7: 7,\n\t8: 8,\n\t9: 9,\n\ta: 10,\n\tb: 11,\n\tc: 12,\n\td: 13,\n\te: 14,\n\tf: 15,\n\tA: 10,\n\tB: 11,\n\tC: 12,\n\tD: 13,\n\tE: 14,\n\tF: 15,\n};\n\nexport function hexToUint8Array(hexString) {\n\tassertString(hexString);\n\n\tif (hexString.length % 2 !== 0) {\n\t\tthrow new Error('Invalid Hex string length.');\n\t}\n\n\tconst resultLength = hexString.length / 2;\n\tconst bytes = new Uint8Array(resultLength);\n\n\tfor (let index = 0; index < resultLength; index++) {\n\t\tconst highNibble = hexToDecimalLookupTable[hexString[index * 2]];\n\t\tconst lowNibble = hexToDecimalLookupTable[hexString[(index * 2) + 1]];\n\n\t\tif (highNibble === undefined || lowNibble === undefined) {\n\t\t\tthrow new Error(`Invalid Hex character encountered at position ${index * 2}`);\n\t\t}\n\n\t\tbytes[index] = (highNibble << 4) | lowNibble; // eslint-disable-line no-bitwise\n\t}\n\n\treturn bytes;\n}\n\n/**\n@param {DataView} view\n@returns {number}\n*/\nexport function getUintBE(view) {\n\tconst {byteLength} = view;\n\n\tif (byteLength === 6) {\n\t\treturn (view.getUint16(0) * (2 ** 32)) + view.getUint32(2);\n\t}\n\n\tif (byteLength === 5) {\n\t\treturn (view.getUint8(0) * (2 ** 32)) + view.getUint32(1);\n\t}\n\n\tif (byteLength === 4) {\n\t\treturn view.getUint32(0);\n\t}\n\n\tif (byteLength === 3) {\n\t\treturn (view.getUint8(0) * (2 ** 16)) + view.getUint16(1);\n\t}\n\n\tif (byteLength === 2) {\n\t\treturn view.getUint16(0);\n\t}\n\n\tif (byteLength === 1) {\n\t\treturn view.getUint8(0);\n\t}\n}\n\n/**\n@param {Uint8Array} array\n@param {Uint8Array} value\n@returns {number}\n*/\nexport function indexOf(array, value) {\n\tconst arrayLength = array.length;\n\tconst valueLength = value.length;\n\n\tif (valueLength === 0) {\n\t\treturn -1;\n\t}\n\n\tif (valueLength > arrayLength) {\n\t\treturn -1;\n\t}\n\n\tconst validOffsetLength = arrayLength - valueLength;\n\n\tfor (let index = 0; index <= validOffsetLength; index++) {\n\t\tlet isMatch = true;\n\t\tfor (let index2 = 0; index2 < valueLength; index2++) {\n\t\t\tif (array[index + index2] !== value[index2]) {\n\t\t\t\tisMatch = false;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (isMatch) {\n\t\t\treturn index;\n\t\t}\n\t}\n\n\treturn -1;\n}\n\n/**\n@param {Uint8Array} array\n@param {Uint8Array} value\n@returns {boolean}\n*/\nexport function includes(array, value) {\n\treturn indexOf(array, value) !== -1;\n}\n", "import {StringType} from 'token-types';\n\nexport function stringToBytes(string) {\n\treturn [...string].map(character => character.charCodeAt(0)); // eslint-disable-line unicorn/prefer-code-point\n}\n\n/**\nChecks whether the TAR checksum is valid.\n\n@param {Uint8Array} arrayBuffer - The TAR header `[offset ... offset + 512]`.\n@param {number} offset - TAR header offset.\n@returns {boolean} `true` if the TAR checksum is valid, otherwise `false`.\n*/\nexport function tarHeaderChecksumMatches(arrayBuffer, offset = 0) {\n\tconst readSum = Number.parseInt(new StringType(6).get(array<PERSON>uffer, 148).replace(/\\0.*$/, '').trim(), 8); // Read sum in header\n\tif (Number.isNaN(readSum)) {\n\t\treturn false;\n\t}\n\n\tlet sum = 8 * 0x20; // Initialize signed bit sum\n\n\tfor (let index = offset; index < offset + 148; index++) {\n\t\tsum += arrayBuffer[index];\n\t}\n\n\tfor (let index = offset + 156; index < offset + 512; index++) {\n\t\tsum += arrayBuffer[index];\n\t}\n\n\treturn readSum === sum;\n}\n\n/**\nID3 UINT32 sync-safe tokenizer token.\n28 bits (representing up to 256MB) integer, the msb is 0 to avoid \"false syncsignals\".\n*/\nexport const uint32SyncSafeToken = {\n\tget: (buffer, offset) => (buffer[offset + 3] & 0x7F) | ((buffer[offset + 2]) << 7) | ((buffer[offset + 1]) << 14) | ((buffer[offset]) << 21),\n\tlen: 4,\n};\n", "export const extensions = [\n\t'jpg',\n\t'png',\n\t'apng',\n\t'gif',\n\t'webp',\n\t'flif',\n\t'xcf',\n\t'cr2',\n\t'cr3',\n\t'orf',\n\t'arw',\n\t'dng',\n\t'nef',\n\t'rw2',\n\t'raf',\n\t'tif',\n\t'bmp',\n\t'icns',\n\t'jxr',\n\t'psd',\n\t'indd',\n\t'zip',\n\t'tar',\n\t'rar',\n\t'gz',\n\t'bz2',\n\t'7z',\n\t'dmg',\n\t'mp4',\n\t'mid',\n\t'mkv',\n\t'webm',\n\t'mov',\n\t'avi',\n\t'mpg',\n\t'mp2',\n\t'mp3',\n\t'm4a',\n\t'oga',\n\t'ogg',\n\t'ogv',\n\t'opus',\n\t'flac',\n\t'wav',\n\t'spx',\n\t'amr',\n\t'pdf',\n\t'epub',\n\t'elf',\n\t'macho',\n\t'exe',\n\t'swf',\n\t'rtf',\n\t'wasm',\n\t'woff',\n\t'woff2',\n\t'eot',\n\t'ttf',\n\t'otf',\n\t'ico',\n\t'flv',\n\t'ps',\n\t'xz',\n\t'sqlite',\n\t'nes',\n\t'crx',\n\t'xpi',\n\t'cab',\n\t'deb',\n\t'ar',\n\t'rpm',\n\t'Z',\n\t'lz',\n\t'cfb',\n\t'mxf',\n\t'mts',\n\t'blend',\n\t'bpg',\n\t'docx',\n\t'pptx',\n\t'xlsx',\n\t'3gp',\n\t'3g2',\n\t'j2c',\n\t'jp2',\n\t'jpm',\n\t'jpx',\n\t'mj2',\n\t'aif',\n\t'qcp',\n\t'odt',\n\t'ods',\n\t'odp',\n\t'xml',\n\t'mobi',\n\t'heic',\n\t'cur',\n\t'ktx',\n\t'ape',\n\t'wv',\n\t'dcm',\n\t'ics',\n\t'glb',\n\t'pcap',\n\t'dsf',\n\t'lnk',\n\t'alias',\n\t'voc',\n\t'ac3',\n\t'm4v',\n\t'm4p',\n\t'm4b',\n\t'f4v',\n\t'f4p',\n\t'f4b',\n\t'f4a',\n\t'mie',\n\t'asf',\n\t'ogm',\n\t'ogx',\n\t'mpc',\n\t'arrow',\n\t'shp',\n\t'aac',\n\t'mp1',\n\t'it',\n\t's3m',\n\t'xm',\n\t'ai',\n\t'skp',\n\t'avif',\n\t'eps',\n\t'lzh',\n\t'pgp',\n\t'asar',\n\t'stl',\n\t'chm',\n\t'3mf',\n\t'zst',\n\t'jxl',\n\t'vcf',\n\t'jls',\n\t'pst',\n\t'dwg',\n\t'parquet',\n\t'class',\n\t'arj',\n\t'cpio',\n\t'ace',\n\t'avro',\n\t'icc',\n\t'fbx',\n\t'vsdx',\n\t'vtt',\n\t'apk',\n];\n\nexport const mimeTypes = [\n\t'image/jpeg',\n\t'image/png',\n\t'image/gif',\n\t'image/webp',\n\t'image/flif',\n\t'image/x-xcf',\n\t'image/x-canon-cr2',\n\t'image/x-canon-cr3',\n\t'image/tiff',\n\t'image/bmp',\n\t'image/vnd.ms-photo',\n\t'image/vnd.adobe.photoshop',\n\t'application/x-indesign',\n\t'application/epub+zip',\n\t'application/x-xpinstall',\n\t'application/vnd.oasis.opendocument.text',\n\t'application/vnd.oasis.opendocument.spreadsheet',\n\t'application/vnd.oasis.opendocument.presentation',\n\t'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n\t'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n\t'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t'application/zip',\n\t'application/x-tar',\n\t'application/x-rar-compressed',\n\t'application/gzip',\n\t'application/x-bzip2',\n\t'application/x-7z-compressed',\n\t'application/x-apple-diskimage',\n\t'application/x-apache-arrow',\n\t'video/mp4',\n\t'audio/midi',\n\t'video/x-matroska',\n\t'video/webm',\n\t'video/quicktime',\n\t'video/vnd.avi',\n\t'audio/wav',\n\t'audio/qcelp',\n\t'audio/x-ms-asf',\n\t'video/x-ms-asf',\n\t'application/vnd.ms-asf',\n\t'video/mpeg',\n\t'video/3gpp',\n\t'audio/mpeg',\n\t'audio/mp4', // RFC 4337\n\t'video/ogg',\n\t'audio/ogg',\n\t'audio/ogg; codecs=opus',\n\t'application/ogg',\n\t'audio/x-flac',\n\t'audio/ape',\n\t'audio/wavpack',\n\t'audio/amr',\n\t'application/pdf',\n\t'application/x-elf',\n\t'application/x-mach-binary',\n\t'application/x-msdownload',\n\t'application/x-shockwave-flash',\n\t'application/rtf',\n\t'application/wasm',\n\t'font/woff',\n\t'font/woff2',\n\t'application/vnd.ms-fontobject',\n\t'font/ttf',\n\t'font/otf',\n\t'image/x-icon',\n\t'video/x-flv',\n\t'application/postscript',\n\t'application/eps',\n\t'application/x-xz',\n\t'application/x-sqlite3',\n\t'application/x-nintendo-nes-rom',\n\t'application/x-google-chrome-extension',\n\t'application/vnd.ms-cab-compressed',\n\t'application/x-deb',\n\t'application/x-unix-archive',\n\t'application/x-rpm',\n\t'application/x-compress',\n\t'application/x-lzip',\n\t'application/x-cfb',\n\t'application/x-mie',\n\t'application/mxf',\n\t'video/mp2t',\n\t'application/x-blender',\n\t'image/bpg',\n\t'image/j2c',\n\t'image/jp2',\n\t'image/jpx',\n\t'image/jpm',\n\t'image/mj2',\n\t'audio/aiff',\n\t'application/xml',\n\t'application/x-mobipocket-ebook',\n\t'image/heif',\n\t'image/heif-sequence',\n\t'image/heic',\n\t'image/heic-sequence',\n\t'image/icns',\n\t'image/ktx',\n\t'application/dicom',\n\t'audio/x-musepack',\n\t'text/calendar',\n\t'text/vcard',\n\t'text/vtt',\n\t'model/gltf-binary',\n\t'application/vnd.tcpdump.pcap',\n\t'audio/x-dsf', // Non-standard\n\t'application/x.ms.shortcut', // Invented by us\n\t'application/x.apple.alias', // Invented by us\n\t'audio/x-voc',\n\t'audio/vnd.dolby.dd-raw',\n\t'audio/x-m4a',\n\t'image/apng',\n\t'image/x-olympus-orf',\n\t'image/x-sony-arw',\n\t'image/x-adobe-dng',\n\t'image/x-nikon-nef',\n\t'image/x-panasonic-rw2',\n\t'image/x-fujifilm-raf',\n\t'video/x-m4v',\n\t'video/3gpp2',\n\t'application/x-esri-shape',\n\t'audio/aac',\n\t'audio/x-it',\n\t'audio/x-s3m',\n\t'audio/x-xm',\n\t'video/MP1S',\n\t'video/MP2P',\n\t'application/vnd.sketchup.skp',\n\t'image/avif',\n\t'application/x-lzh-compressed',\n\t'application/pgp-encrypted',\n\t'application/x-asar',\n\t'model/stl',\n\t'application/vnd.ms-htmlhelp',\n\t'model/3mf',\n\t'image/jxl',\n\t'application/zstd',\n\t'image/jls',\n\t'application/vnd.ms-outlook',\n\t'image/vnd.dwg',\n\t'application/x-parquet',\n\t'application/java-vm',\n\t'application/x-arj',\n\t'application/x-cpio',\n\t'application/x-ace-compressed',\n\t'application/avro',\n\t'application/vnd.iccprofile',\n\t'application/x.autodesk.fbx', // Invented by us\n\t'application/vnd.visio',\n\t'application/vnd.android.package-archive',\n];\n", "/**\nPrimary entry point, Node.js specific entry point is index.js\n*/\n\nimport * as Token from 'token-types';\nimport * as strtok3 from 'strtok3/core';\nimport {includes, indexOf, getUintBE} from 'uint8array-extras';\nimport {\n\tstringToBytes,\n\ttarHeaderChecksumMatches,\n\tuint32SyncSafeToken,\n} from './util.js';\nimport {extensions, mimeTypes} from './supported.js';\n\nexport const reasonableDetectionSizeInBytes = 4100; // A fair amount of file-types are detectable within this range.\n\nexport async function fileTypeFromStream(stream) {\n\treturn new FileTypeParser().fromStream(stream);\n}\n\nexport async function fileTypeFromBuffer(input) {\n\treturn new FileTypeParser().fromBuffer(input);\n}\n\nexport async function fileTypeFromBlob(blob) {\n\treturn new FileTypeParser().fromBlob(blob);\n}\n\nfunction _check(buffer, headers, options) {\n\toptions = {\n\t\toffset: 0,\n\t\t...options,\n\t};\n\n\tfor (const [index, header] of headers.entries()) {\n\t\t// If a bitmask is set\n\t\tif (options.mask) {\n\t\t\t// If header doesn't equal `buf` with bits masked off\n\t\t\tif (header !== (options.mask[index] & buffer[index + options.offset])) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t} else if (header !== buffer[index + options.offset]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\nexport async function fileTypeFromTokenizer(tokenizer) {\n\treturn new FileTypeParser().fromTokenizer(tokenizer);\n}\n\nexport async function fileTypeStream(webStream, options) {\n\treturn new FileTypeParser(options).toDetectionStream(webStream, options);\n}\n\nexport class FileTypeParser {\n\tconstructor(options) {\n\t\tthis.detectors = options?.customDetectors;\n\t\tthis.tokenizerOptions = {\n\t\t\tabortSignal: options?.signal,\n\t\t};\n\t\tthis.fromTokenizer = this.fromTokenizer.bind(this);\n\t\tthis.fromBuffer = this.fromBuffer.bind(this);\n\t\tthis.parse = this.parse.bind(this);\n\t}\n\n\tasync fromTokenizer(tokenizer) {\n\t\tconst initialPosition = tokenizer.position;\n\n\t\tfor (const detector of this.detectors || []) {\n\t\t\tconst fileType = await detector(tokenizer);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\n\t\t\tif (initialPosition !== tokenizer.position) {\n\t\t\t\treturn undefined; // Cannot proceed scanning of the tokenizer is at an arbitrary position\n\t\t\t}\n\t\t}\n\n\t\treturn this.parse(tokenizer);\n\t}\n\n\tasync fromBuffer(input) {\n\t\tif (!(input instanceof Uint8Array || input instanceof ArrayBuffer)) {\n\t\t\tthrow new TypeError(`Expected the \\`input\\` argument to be of type \\`Uint8Array\\` or \\`ArrayBuffer\\`, got \\`${typeof input}\\``);\n\t\t}\n\n\t\tconst buffer = input instanceof Uint8Array ? input : new Uint8Array(input);\n\n\t\tif (!(buffer?.length > 1)) {\n\t\t\treturn;\n\t\t}\n\n\t\treturn this.fromTokenizer(strtok3.fromBuffer(buffer, this.tokenizerOptions));\n\t}\n\n\tasync fromBlob(blob) {\n\t\treturn this.fromStream(blob.stream());\n\t}\n\n\tasync fromStream(stream) {\n\t\tconst tokenizer = await strtok3.fromWebStream(stream, this.tokenizerOptions);\n\t\ttry {\n\t\t\treturn await this.fromTokenizer(tokenizer);\n\t\t} finally {\n\t\t\tawait tokenizer.close();\n\t\t}\n\t}\n\n\tasync toDetectionStream(stream, options) {\n\t\tconst {sampleSize = reasonableDetectionSizeInBytes} = options;\n\t\tlet detectedFileType;\n\t\tlet firstChunk;\n\n\t\tconst reader = stream.getReader({mode: 'byob'});\n\t\ttry {\n\t\t\t// Read the first chunk from the stream\n\t\t\tconst {value: chunk, done} = await reader.read(new Uint8Array(sampleSize));\n\t\t\tfirstChunk = chunk;\n\t\t\tif (!done && chunk) {\n\t\t\t\ttry {\n\t\t\t\t\t// Attempt to detect the file type from the chunk\n\t\t\t\t\tdetectedFileType = await this.fromBuffer(chunk.slice(0, sampleSize));\n\t\t\t\t} catch (error) {\n\t\t\t\t\tif (!(error instanceof strtok3.EndOfStreamError)) {\n\t\t\t\t\t\tthrow error; // Re-throw non-EndOfStreamError\n\t\t\t\t\t}\n\n\t\t\t\t\tdetectedFileType = undefined;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfirstChunk = chunk;\n\t\t} finally {\n\t\t\treader.releaseLock(); // Ensure the reader is released\n\t\t}\n\n\t\t// Create a new ReadableStream to manage locking issues\n\t\tconst transformStream = new TransformStream({\n\t\t\tasync start(controller) {\n\t\t\t\tcontroller.enqueue(firstChunk); // Enqueue the initial chunk\n\t\t\t},\n\t\t\ttransform(chunk, controller) {\n\t\t\t\t// Pass through the chunks without modification\n\t\t\t\tcontroller.enqueue(chunk);\n\t\t\t},\n\t\t});\n\n\t\tconst newStream = stream.pipeThrough(transformStream);\n\t\tnewStream.fileType = detectedFileType;\n\n\t\treturn newStream;\n\t}\n\n\tcheck(header, options) {\n\t\treturn _check(this.buffer, header, options);\n\t}\n\n\tcheckString(header, options) {\n\t\treturn this.check(stringToBytes(header), options);\n\t}\n\n\tasync parse(tokenizer) {\n\t\tthis.buffer = new Uint8Array(reasonableDetectionSizeInBytes);\n\n\t\t// Keep reading until EOF if the file size is unknown.\n\t\tif (tokenizer.fileInfo.size === undefined) {\n\t\t\ttokenizer.fileInfo.size = Number.MAX_SAFE_INTEGER;\n\t\t}\n\n\t\tthis.tokenizer = tokenizer;\n\n\t\tawait tokenizer.peekBuffer(this.buffer, {length: 12, mayBeLess: true});\n\n\t\t// -- 2-byte signatures --\n\n\t\tif (this.check([0x42, 0x4D])) {\n\t\t\treturn {\n\t\t\t\text: 'bmp',\n\t\t\t\tmime: 'image/bmp',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x0B, 0x77])) {\n\t\t\treturn {\n\t\t\t\text: 'ac3',\n\t\t\t\tmime: 'audio/vnd.dolby.dd-raw',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x78, 0x01])) {\n\t\t\treturn {\n\t\t\t\text: 'dmg',\n\t\t\t\tmime: 'application/x-apple-diskimage',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4D, 0x5A])) {\n\t\t\treturn {\n\t\t\t\text: 'exe',\n\t\t\t\tmime: 'application/x-msdownload',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x25, 0x21])) {\n\t\t\tawait tokenizer.peekBuffer(this.buffer, {length: 24, mayBeLess: true});\n\n\t\t\tif (\n\t\t\t\tthis.checkString('PS-Adobe-', {offset: 2})\n\t\t\t\t&& this.checkString(' EPSF-', {offset: 14})\n\t\t\t) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'eps',\n\t\t\t\t\tmime: 'application/eps',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'ps',\n\t\t\t\tmime: 'application/postscript',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x1F, 0xA0])\n\t\t\t|| this.check([0x1F, 0x9D])\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'Z',\n\t\t\t\tmime: 'application/x-compress',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xC7, 0x71])) {\n\t\t\treturn {\n\t\t\t\text: 'cpio',\n\t\t\t\tmime: 'application/x-cpio',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x60, 0xEA])) {\n\t\t\treturn {\n\t\t\t\text: 'arj',\n\t\t\t\tmime: 'application/x-arj',\n\t\t\t};\n\t\t}\n\n\t\t// -- 3-byte signatures --\n\n\t\tif (this.check([0xEF, 0xBB, 0xBF])) { // UTF-8-BOM\n\t\t\t// Strip off UTF-8-BOM\n\t\t\tthis.tokenizer.ignore(3);\n\t\t\treturn this.parse(tokenizer);\n\t\t}\n\n\t\tif (this.check([0x47, 0x49, 0x46])) {\n\t\t\treturn {\n\t\t\t\text: 'gif',\n\t\t\t\tmime: 'image/gif',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x49, 0x49, 0xBC])) {\n\t\t\treturn {\n\t\t\t\text: 'jxr',\n\t\t\t\tmime: 'image/vnd.ms-photo',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x1F, 0x8B, 0x8])) {\n\t\t\treturn {\n\t\t\t\text: 'gz',\n\t\t\t\tmime: 'application/gzip',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x42, 0x5A, 0x68])) {\n\t\t\treturn {\n\t\t\t\text: 'bz2',\n\t\t\t\tmime: 'application/x-bzip2',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('ID3')) {\n\t\t\tawait tokenizer.ignore(6); // Skip ID3 header until the header size\n\t\t\tconst id3HeaderLength = await tokenizer.readToken(uint32SyncSafeToken);\n\t\t\tif (tokenizer.position + id3HeaderLength > tokenizer.fileInfo.size) {\n\t\t\t\t// Guess file type based on ID3 header for backward compatibility\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp3',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tawait tokenizer.ignore(id3HeaderLength);\n\t\t\treturn this.fromTokenizer(tokenizer); // Skip ID3 header, recursion\n\t\t}\n\n\t\t// Musepack, SV7\n\t\tif (this.checkString('MP+')) {\n\t\t\treturn {\n\t\t\t\text: 'mpc',\n\t\t\t\tmime: 'audio/x-musepack',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\t(this.buffer[0] === 0x43 || this.buffer[0] === 0x46)\n\t\t\t&& this.check([0x57, 0x53], {offset: 1})\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'swf',\n\t\t\t\tmime: 'application/x-shockwave-flash',\n\t\t\t};\n\t\t}\n\n\t\t// -- 4-byte signatures --\n\n\t\t// Requires a sample size of 4 bytes\n\t\tif (this.check([0xFF, 0xD8, 0xFF])) {\n\t\t\tif (this.check([0xF7], {offset: 3})) { // JPG7/SOF55, indicating a ISO/IEC 14495 / JPEG-LS file\n\t\t\t\treturn {\n\t\t\t\t\text: 'jls',\n\t\t\t\t\tmime: 'image/jls',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'jpg',\n\t\t\t\tmime: 'image/jpeg',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4F, 0x62, 0x6A, 0x01])) {\n\t\t\treturn {\n\t\t\t\text: 'avro',\n\t\t\t\tmime: 'application/avro',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('FLIF')) {\n\t\t\treturn {\n\t\t\t\text: 'flif',\n\t\t\t\tmime: 'image/flif',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('8BPS')) {\n\t\t\treturn {\n\t\t\t\text: 'psd',\n\t\t\t\tmime: 'image/vnd.adobe.photoshop',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('WEBP', {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'webp',\n\t\t\t\tmime: 'image/webp',\n\t\t\t};\n\t\t}\n\n\t\t// Musepack, SV8\n\t\tif (this.checkString('MPCK')) {\n\t\t\treturn {\n\t\t\t\text: 'mpc',\n\t\t\t\tmime: 'audio/x-musepack',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('FORM')) {\n\t\t\treturn {\n\t\t\t\text: 'aif',\n\t\t\t\tmime: 'audio/aiff',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('icns', {offset: 0})) {\n\t\t\treturn {\n\t\t\t\text: 'icns',\n\t\t\t\tmime: 'image/icns',\n\t\t\t};\n\t\t}\n\n\t\t// Zip-based file formats\n\t\t// Need to be before the `zip` check\n\t\tif (this.check([0x50, 0x4B, 0x3, 0x4])) { // Local file header signature\n\t\t\ttry {\n\t\t\t\twhile (tokenizer.position + 30 < tokenizer.fileInfo.size) {\n\t\t\t\t\tawait tokenizer.readBuffer(this.buffer, {length: 30});\n\n\t\t\t\t\tconst view = new DataView(this.buffer.buffer);\n\n\t\t\t\t\t// https://en.wikipedia.org/wiki/Zip_(file_format)#File_headers\n\t\t\t\t\tconst zipHeader = {\n\t\t\t\t\t\tcompressedSize: view.getUint32(18, true),\n\t\t\t\t\t\tuncompressedSize: view.getUint32(22, true),\n\t\t\t\t\t\tfilenameLength: view.getUint16(26, true),\n\t\t\t\t\t\textraFieldLength: view.getUint16(28, true),\n\t\t\t\t\t};\n\n\t\t\t\t\tzipHeader.filename = await tokenizer.readToken(new Token.StringType(zipHeader.filenameLength, 'utf-8'));\n\t\t\t\t\tawait tokenizer.ignore(zipHeader.extraFieldLength);\n\n\t\t\t\t\tif (/classes\\d*\\.dex/.test(zipHeader.filename)) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'apk',\n\t\t\t\t\t\t\tmime: 'application/vnd.android.package-archive',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\t// Assumes signed `.xpi` from addons.mozilla.org\n\t\t\t\t\tif (zipHeader.filename === 'META-INF/mozilla.rsa') {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'xpi',\n\t\t\t\t\t\t\tmime: 'application/x-xpinstall',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (zipHeader.filename.endsWith('.rels') || zipHeader.filename.endsWith('.xml')) {\n\t\t\t\t\t\tconst type = zipHeader.filename.split('/')[0];\n\t\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\t\tcase '_rels':\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 'word':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'docx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'ppt':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'pptx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'xl':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'xlsx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'visio':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'vsdx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.visio',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (zipHeader.filename.startsWith('xl/')) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'xlsx',\n\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (zipHeader.filename.startsWith('3D/') && zipHeader.filename.endsWith('.model')) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: '3mf',\n\t\t\t\t\t\t\tmime: 'model/3mf',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\t// The docx, xlsx and pptx file types extend the Office Open XML file format:\n\t\t\t\t\t// https://en.wikipedia.org/wiki/Office_Open_XML_file_formats\n\t\t\t\t\t// We look for:\n\t\t\t\t\t// - one entry named '[Content_Types].xml' or '_rels/.rels',\n\t\t\t\t\t// - one entry indicating specific type of file.\n\t\t\t\t\t// MS Office, OpenOffice and LibreOffice may put the parts in different order, so the check should not rely on it.\n\t\t\t\t\tif (zipHeader.filename === 'mimetype' && zipHeader.compressedSize === zipHeader.uncompressedSize) {\n\t\t\t\t\t\tlet mimeType = await tokenizer.readToken(new Token.StringType(zipHeader.compressedSize, 'utf-8'));\n\t\t\t\t\t\tmimeType = mimeType.trim();\n\n\t\t\t\t\t\tswitch (mimeType) {\n\t\t\t\t\t\t\tcase 'application/epub+zip':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'epub',\n\t\t\t\t\t\t\t\t\tmime: 'application/epub+zip',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'application/vnd.oasis.opendocument.text':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'odt',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.oasis.opendocument.text',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'application/vnd.oasis.opendocument.spreadsheet':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'ods',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.oasis.opendocument.spreadsheet',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'application/vnd.oasis.opendocument.presentation':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'odp',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.oasis.opendocument.presentation',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Try to find next header manually when current one is corrupted\n\t\t\t\t\tif (zipHeader.compressedSize === 0) {\n\t\t\t\t\t\tlet nextHeaderIndex = -1;\n\n\t\t\t\t\t\twhile (nextHeaderIndex < 0 && (tokenizer.position < tokenizer.fileInfo.size)) {\n\t\t\t\t\t\t\tawait tokenizer.peekBuffer(this.buffer, {mayBeLess: true});\n\n\t\t\t\t\t\t\tnextHeaderIndex = indexOf(this.buffer, new Uint8Array([0x50, 0x4B, 0x03, 0x04]));\n\n\t\t\t\t\t\t\t// Move position to the next header if found, skip the whole buffer otherwise\n\t\t\t\t\t\t\tawait tokenizer.ignore(nextHeaderIndex >= 0 ? nextHeaderIndex : this.buffer.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait tokenizer.ignore(zipHeader.compressedSize);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (!(error instanceof strtok3.EndOfStreamError)) {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'zip',\n\t\t\t\tmime: 'application/zip',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('OggS')) {\n\t\t\t// This is an OGG container\n\t\t\tawait tokenizer.ignore(28);\n\t\t\tconst type = new Uint8Array(8);\n\t\t\tawait tokenizer.readBuffer(type);\n\n\t\t\t// Needs to be before `ogg` check\n\t\t\tif (_check(type, [0x4F, 0x70, 0x75, 0x73, 0x48, 0x65, 0x61, 0x64])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'opus',\n\t\t\t\t\tmime: 'audio/ogg; codecs=opus',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If ' theora' in header.\n\t\t\tif (_check(type, [0x80, 0x74, 0x68, 0x65, 0x6F, 0x72, 0x61])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ogv',\n\t\t\t\t\tmime: 'video/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If '\\x01video' in header.\n\t\t\tif (_check(type, [0x01, 0x76, 0x69, 0x64, 0x65, 0x6F, 0x00])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ogm',\n\t\t\t\t\tmime: 'video/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If ' FLAC' in header  https://xiph.org/flac/faq.html\n\t\t\tif (_check(type, [0x7F, 0x46, 0x4C, 0x41, 0x43])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'oga',\n\t\t\t\t\tmime: 'audio/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// 'Speex  ' in header https://en.wikipedia.org/wiki/Speex\n\t\t\tif (_check(type, [0x53, 0x70, 0x65, 0x65, 0x78, 0x20, 0x20])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'spx',\n\t\t\t\t\tmime: 'audio/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If '\\x01vorbis' in header\n\t\t\tif (_check(type, [0x01, 0x76, 0x6F, 0x72, 0x62, 0x69, 0x73])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ogg',\n\t\t\t\t\tmime: 'audio/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// Default OGG container https://www.iana.org/assignments/media-types/application/ogg\n\t\t\treturn {\n\t\t\t\text: 'ogx',\n\t\t\t\tmime: 'application/ogg',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x50, 0x4B])\n\t\t\t&& (this.buffer[2] === 0x3 || this.buffer[2] === 0x5 || this.buffer[2] === 0x7)\n\t\t\t&& (this.buffer[3] === 0x4 || this.buffer[3] === 0x6 || this.buffer[3] === 0x8)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'zip',\n\t\t\t\tmime: 'application/zip',\n\t\t\t};\n\t\t}\n\n\t\t//\n\n\t\t// File Type Box (https://en.wikipedia.org/wiki/ISO_base_media_file_format)\n\t\t// It's not required to be first, but it's recommended to be. Almost all ISO base media files start with `ftyp` box.\n\t\t// `ftyp` box must contain a brand major identifier, which must consist of ISO 8859-1 printable characters.\n\t\t// Here we check for 8859-1 printable characters (for simplicity, it's a mask which also catches one non-printable character).\n\t\tif (\n\t\t\tthis.checkString('ftyp', {offset: 4})\n\t\t\t&& (this.buffer[8] & 0x60) !== 0x00 // Brand major, first character ASCII?\n\t\t) {\n\t\t\t// They all can have MIME `video/mp4` except `application/mp4` special-case which is hard to detect.\n\t\t\t// For some cases, we're specific, everything else falls to `video/mp4` with `mp4` extension.\n\t\t\tconst brandMajor = new Token.StringType(4, 'latin1').get(this.buffer, 8).replace('\\0', ' ').trim();\n\t\t\tswitch (brandMajor) {\n\t\t\t\tcase 'avif':\n\t\t\t\tcase 'avis':\n\t\t\t\t\treturn {ext: 'avif', mime: 'image/avif'};\n\t\t\t\tcase 'mif1':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heif'};\n\t\t\t\tcase 'msf1':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heif-sequence'};\n\t\t\t\tcase 'heic':\n\t\t\t\tcase 'heix':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heic'};\n\t\t\t\tcase 'hevc':\n\t\t\t\tcase 'hevx':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heic-sequence'};\n\t\t\t\tcase 'qt':\n\t\t\t\t\treturn {ext: 'mov', mime: 'video/quicktime'};\n\t\t\t\tcase 'M4V':\n\t\t\t\tcase 'M4VH':\n\t\t\t\tcase 'M4VP':\n\t\t\t\t\treturn {ext: 'm4v', mime: 'video/x-m4v'};\n\t\t\t\tcase 'M4P':\n\t\t\t\t\treturn {ext: 'm4p', mime: 'video/mp4'};\n\t\t\t\tcase 'M4B':\n\t\t\t\t\treturn {ext: 'm4b', mime: 'audio/mp4'};\n\t\t\t\tcase 'M4A':\n\t\t\t\t\treturn {ext: 'm4a', mime: 'audio/x-m4a'};\n\t\t\t\tcase 'F4V':\n\t\t\t\t\treturn {ext: 'f4v', mime: 'video/mp4'};\n\t\t\t\tcase 'F4P':\n\t\t\t\t\treturn {ext: 'f4p', mime: 'video/mp4'};\n\t\t\t\tcase 'F4A':\n\t\t\t\t\treturn {ext: 'f4a', mime: 'audio/mp4'};\n\t\t\t\tcase 'F4B':\n\t\t\t\t\treturn {ext: 'f4b', mime: 'audio/mp4'};\n\t\t\t\tcase 'crx':\n\t\t\t\t\treturn {ext: 'cr3', mime: 'image/x-canon-cr3'};\n\t\t\t\tdefault:\n\t\t\t\t\tif (brandMajor.startsWith('3g')) {\n\t\t\t\t\t\tif (brandMajor.startsWith('3g2')) {\n\t\t\t\t\t\t\treturn {ext: '3g2', mime: 'video/3gpp2'};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {ext: '3gp', mime: 'video/3gpp'};\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {ext: 'mp4', mime: 'video/mp4'};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('MThd')) {\n\t\t\treturn {\n\t\t\t\text: 'mid',\n\t\t\t\tmime: 'audio/midi',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('wOFF')\n\t\t\t&& (\n\t\t\t\tthis.check([0x00, 0x01, 0x00, 0x00], {offset: 4})\n\t\t\t\t|| this.checkString('OTTO', {offset: 4})\n\t\t\t)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'woff',\n\t\t\t\tmime: 'font/woff',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('wOF2')\n\t\t\t&& (\n\t\t\t\tthis.check([0x00, 0x01, 0x00, 0x00], {offset: 4})\n\t\t\t\t|| this.checkString('OTTO', {offset: 4})\n\t\t\t)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'woff2',\n\t\t\t\tmime: 'font/woff2',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xD4, 0xC3, 0xB2, 0xA1]) || this.check([0xA1, 0xB2, 0xC3, 0xD4])) {\n\t\t\treturn {\n\t\t\t\text: 'pcap',\n\t\t\t\tmime: 'application/vnd.tcpdump.pcap',\n\t\t\t};\n\t\t}\n\n\t\t// Sony DSD Stream File (DSF)\n\t\tif (this.checkString('DSD ')) {\n\t\t\treturn {\n\t\t\t\text: 'dsf',\n\t\t\t\tmime: 'audio/x-dsf', // Non-standard\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('LZIP')) {\n\t\t\treturn {\n\t\t\t\text: 'lz',\n\t\t\t\tmime: 'application/x-lzip',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('fLaC')) {\n\t\t\treturn {\n\t\t\t\text: 'flac',\n\t\t\t\tmime: 'audio/x-flac',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x42, 0x50, 0x47, 0xFB])) {\n\t\t\treturn {\n\t\t\t\text: 'bpg',\n\t\t\t\tmime: 'image/bpg',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('wvpk')) {\n\t\t\treturn {\n\t\t\t\text: 'wv',\n\t\t\t\tmime: 'audio/wavpack',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('%PDF')) {\n\t\t\ttry {\n\t\t\t\tawait tokenizer.ignore(1350);\n\t\t\t\tconst maxBufferSize = 10 * 1024 * 1024;\n\t\t\t\tconst buffer = new Uint8Array(Math.min(maxBufferSize, tokenizer.fileInfo.size));\n\t\t\t\tawait tokenizer.readBuffer(buffer, {mayBeLess: true});\n\n\t\t\t\t// Check if this is an Adobe Illustrator file\n\t\t\t\tif (includes(buffer, new TextEncoder().encode('AIPrivateData'))) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'ai',\n\t\t\t\t\t\tmime: 'application/postscript',\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\t// Swallow end of stream error if file is too small for the Adobe AI check\n\t\t\t\tif (!(error instanceof strtok3.EndOfStreamError)) {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Assume this is just a normal PDF\n\t\t\treturn {\n\t\t\t\text: 'pdf',\n\t\t\t\tmime: 'application/pdf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x61, 0x73, 0x6D])) {\n\t\t\treturn {\n\t\t\t\text: 'wasm',\n\t\t\t\tmime: 'application/wasm',\n\t\t\t};\n\t\t}\n\n\t\t// TIFF, little-endian type\n\t\tif (this.check([0x49, 0x49])) {\n\t\t\tconst fileType = await this.readTiffHeader(false);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\t\t}\n\n\t\t// TIFF, big-endian type\n\t\tif (this.check([0x4D, 0x4D])) {\n\t\t\tconst fileType = await this.readTiffHeader(true);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('MAC ')) {\n\t\t\treturn {\n\t\t\t\text: 'ape',\n\t\t\t\tmime: 'audio/ape',\n\t\t\t};\n\t\t}\n\n\t\t// https://github.com/file/file/blob/master/magic/Magdir/matroska\n\t\tif (this.check([0x1A, 0x45, 0xDF, 0xA3])) { // Root element: EBML\n\t\t\tasync function readField() {\n\t\t\t\tconst msb = await tokenizer.peekNumber(Token.UINT8);\n\t\t\t\tlet mask = 0x80;\n\t\t\t\tlet ic = 0; // 0 = A, 1 = B, 2 = C, 3 = D\n\n\t\t\t\twhile ((msb & mask) === 0 && mask !== 0) {\n\t\t\t\t\t++ic;\n\t\t\t\t\tmask >>= 1;\n\t\t\t\t}\n\n\t\t\t\tconst id = new Uint8Array(ic + 1);\n\t\t\t\tawait tokenizer.readBuffer(id);\n\t\t\t\treturn id;\n\t\t\t}\n\n\t\t\tasync function readElement() {\n\t\t\t\tconst idField = await readField();\n\t\t\t\tconst lengthField = await readField();\n\n\t\t\t\tlengthField[0] ^= 0x80 >> (lengthField.length - 1);\n\t\t\t\tconst nrLength = Math.min(6, lengthField.length); // JavaScript can max read 6 bytes integer\n\n\t\t\t\tconst idView = new DataView(idField.buffer);\n\t\t\t\tconst lengthView = new DataView(lengthField.buffer, lengthField.length - nrLength, nrLength);\n\n\t\t\t\treturn {\n\t\t\t\t\tid: getUintBE(idView),\n\t\t\t\t\tlen: getUintBE(lengthView),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tasync function readChildren(children) {\n\t\t\t\twhile (children > 0) {\n\t\t\t\t\tconst element = await readElement();\n\t\t\t\t\tif (element.id === 0x42_82) {\n\t\t\t\t\t\tconst rawValue = await tokenizer.readToken(new Token.StringType(element.len));\n\t\t\t\t\t\treturn rawValue.replaceAll(/\\00.*$/g, ''); // Return DocType\n\t\t\t\t\t}\n\n\t\t\t\t\tawait tokenizer.ignore(element.len); // ignore payload\n\t\t\t\t\t--children;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst re = await readElement();\n\t\t\tconst docType = await readChildren(re.len);\n\n\t\t\tswitch (docType) {\n\t\t\t\tcase 'webm':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'webm',\n\t\t\t\t\t\tmime: 'video/webm',\n\t\t\t\t\t};\n\n\t\t\t\tcase 'matroska':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'mkv',\n\t\t\t\t\t\tmime: 'video/x-matroska',\n\t\t\t\t\t};\n\n\t\t\t\tdefault:\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\t// RIFF file format which might be AVI, WAV, QCP, etc\n\t\tif (this.check([0x52, 0x49, 0x46, 0x46])) {\n\t\t\tif (this.check([0x41, 0x56, 0x49], {offset: 8})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'avi',\n\t\t\t\t\tmime: 'video/vnd.avi',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (this.check([0x57, 0x41, 0x56, 0x45], {offset: 8})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'wav',\n\t\t\t\t\tmime: 'audio/wav',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// QLCM, QCP file\n\t\t\tif (this.check([0x51, 0x4C, 0x43, 0x4D], {offset: 8})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'qcp',\n\t\t\t\t\tmime: 'audio/qcelp',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('SQLi')) {\n\t\t\treturn {\n\t\t\t\text: 'sqlite',\n\t\t\t\tmime: 'application/x-sqlite3',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4E, 0x45, 0x53, 0x1A])) {\n\t\t\treturn {\n\t\t\t\text: 'nes',\n\t\t\t\tmime: 'application/x-nintendo-nes-rom',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Cr24')) {\n\t\t\treturn {\n\t\t\t\text: 'crx',\n\t\t\t\tmime: 'application/x-google-chrome-extension',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('MSCF')\n\t\t\t|| this.checkString('ISc(')\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'cab',\n\t\t\t\tmime: 'application/vnd.ms-cab-compressed',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xED, 0xAB, 0xEE, 0xDB])) {\n\t\t\treturn {\n\t\t\t\text: 'rpm',\n\t\t\t\tmime: 'application/x-rpm',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xC5, 0xD0, 0xD3, 0xC6])) {\n\t\t\treturn {\n\t\t\t\text: 'eps',\n\t\t\t\tmime: 'application/eps',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x28, 0xB5, 0x2F, 0xFD])) {\n\t\t\treturn {\n\t\t\t\text: 'zst',\n\t\t\t\tmime: 'application/zstd',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x7F, 0x45, 0x4C, 0x46])) {\n\t\t\treturn {\n\t\t\t\text: 'elf',\n\t\t\t\tmime: 'application/x-elf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x21, 0x42, 0x44, 0x4E])) {\n\t\t\treturn {\n\t\t\t\text: 'pst',\n\t\t\t\tmime: 'application/vnd.ms-outlook',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('PAR1')) {\n\t\t\treturn {\n\t\t\t\text: 'parquet',\n\t\t\t\tmime: 'application/x-parquet',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xCF, 0xFA, 0xED, 0xFE])) {\n\t\t\treturn {\n\t\t\t\text: 'macho',\n\t\t\t\tmime: 'application/x-mach-binary',\n\t\t\t};\n\t\t}\n\n\t\t// -- 5-byte signatures --\n\n\t\tif (this.check([0x4F, 0x54, 0x54, 0x4F, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'otf',\n\t\t\t\tmime: 'font/otf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('#!AMR')) {\n\t\t\treturn {\n\t\t\t\text: 'amr',\n\t\t\t\tmime: 'audio/amr',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('{\\\\rtf')) {\n\t\t\treturn {\n\t\t\t\text: 'rtf',\n\t\t\t\tmime: 'application/rtf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x46, 0x4C, 0x56, 0x01])) {\n\t\t\treturn {\n\t\t\t\text: 'flv',\n\t\t\t\tmime: 'video/x-flv',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('IMPM')) {\n\t\t\treturn {\n\t\t\t\text: 'it',\n\t\t\t\tmime: 'audio/x-it',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('-lh0-', {offset: 2})\n\t\t\t|| this.checkString('-lh1-', {offset: 2})\n\t\t\t|| this.checkString('-lh2-', {offset: 2})\n\t\t\t|| this.checkString('-lh3-', {offset: 2})\n\t\t\t|| this.checkString('-lh4-', {offset: 2})\n\t\t\t|| this.checkString('-lh5-', {offset: 2})\n\t\t\t|| this.checkString('-lh6-', {offset: 2})\n\t\t\t|| this.checkString('-lh7-', {offset: 2})\n\t\t\t|| this.checkString('-lzs-', {offset: 2})\n\t\t\t|| this.checkString('-lz4-', {offset: 2})\n\t\t\t|| this.checkString('-lz5-', {offset: 2})\n\t\t\t|| this.checkString('-lhd-', {offset: 2})\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'lzh',\n\t\t\t\tmime: 'application/x-lzh-compressed',\n\t\t\t};\n\t\t}\n\n\t\t// MPEG program stream (PS or MPEG-PS)\n\t\tif (this.check([0x00, 0x00, 0x01, 0xBA])) {\n\t\t\t//  MPEG-PS, MPEG-1 Part 1\n\t\t\tif (this.check([0x21], {offset: 4, mask: [0xF1]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mpg', // May also be .ps, .mpeg\n\t\t\t\t\tmime: 'video/MP1S',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// MPEG-PS, MPEG-2 Part 1\n\t\t\tif (this.check([0x44], {offset: 4, mask: [0xC4]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mpg', // May also be .mpg, .m2p, .vob or .sub\n\t\t\t\t\tmime: 'video/MP2P',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('ITSF')) {\n\t\t\treturn {\n\t\t\t\text: 'chm',\n\t\t\t\tmime: 'application/vnd.ms-htmlhelp',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xCA, 0xFE, 0xBA, 0xBE])) {\n\t\t\treturn {\n\t\t\t\text: 'class',\n\t\t\t\tmime: 'application/java-vm',\n\t\t\t};\n\t\t}\n\n\t\t// -- 6-byte signatures --\n\n\t\tif (this.check([0xFD, 0x37, 0x7A, 0x58, 0x5A, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'xz',\n\t\t\t\tmime: 'application/x-xz',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('<?xml ')) {\n\t\t\treturn {\n\t\t\t\text: 'xml',\n\t\t\t\tmime: 'application/xml',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C])) {\n\t\t\treturn {\n\t\t\t\text: '7z',\n\t\t\t\tmime: 'application/x-7z-compressed',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x52, 0x61, 0x72, 0x21, 0x1A, 0x7])\n\t\t\t&& (this.buffer[6] === 0x0 || this.buffer[6] === 0x1)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'rar',\n\t\t\t\tmime: 'application/x-rar-compressed',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('solid ')) {\n\t\t\treturn {\n\t\t\t\text: 'stl',\n\t\t\t\tmime: 'model/stl',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('AC')) {\n\t\t\tconst version = new Token.StringType(4, 'latin1').get(this.buffer, 2);\n\t\t\tif (version.match('^d*') && version >= 1000 && version <= 1050) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'dwg',\n\t\t\t\t\tmime: 'image/vnd.dwg',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('070707')) {\n\t\t\treturn {\n\t\t\t\text: 'cpio',\n\t\t\t\tmime: 'application/x-cpio',\n\t\t\t};\n\t\t}\n\n\t\t// -- 7-byte signatures --\n\n\t\tif (this.checkString('BLENDER')) {\n\t\t\treturn {\n\t\t\t\text: 'blend',\n\t\t\t\tmime: 'application/x-blender',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('!<arch>')) {\n\t\t\tawait tokenizer.ignore(8);\n\t\t\tconst string = await tokenizer.readToken(new Token.StringType(13, 'ascii'));\n\t\t\tif (string === 'debian-binary') {\n\t\t\t\treturn {\n\t\t\t\t\text: 'deb',\n\t\t\t\t\tmime: 'application/x-deb',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'ar',\n\t\t\t\tmime: 'application/x-unix-archive',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('WEBVTT')\n\t\t\t&&\t(\n\t\t\t\t// One of LF, CR, tab, space, or end of file must follow \"WEBVTT\" per the spec (see `fixture/fixture-vtt-*.vtt` for examples). Note that `\\0` is technically the null character (there is no such thing as an EOF character). However, checking for `\\0` gives us the same result as checking for the end of the stream.\n\t\t\t\t(['\\n', '\\r', '\\t', ' ', '\\0'].some(char7 => this.checkString(char7, {offset: 6}))))\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'vtt',\n\t\t\t\tmime: 'text/vtt',\n\t\t\t};\n\t\t}\n\n\t\t// -- 8-byte signatures --\n\n\t\tif (this.check([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\t\t// APNG format (https://wiki.mozilla.org/APNG_Specification)\n\t\t\t// 1. Find the first IDAT (image data) chunk (49 44 41 54)\n\t\t\t// 2. Check if there is an \"acTL\" chunk before the IDAT one (61 63 54 4C)\n\n\t\t\t// Offset calculated as follows:\n\t\t\t// - 8 bytes: PNG signature\n\t\t\t// - 4 (length) + 4 (chunk type) + 13 (chunk data) + 4 (CRC): IHDR chunk\n\n\t\t\tawait tokenizer.ignore(8); // ignore PNG signature\n\n\t\t\tasync function readChunkHeader() {\n\t\t\t\treturn {\n\t\t\t\t\tlength: await tokenizer.readToken(Token.INT32_BE),\n\t\t\t\t\ttype: await tokenizer.readToken(new Token.StringType(4, 'latin1')),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tdo {\n\t\t\t\tconst chunk = await readChunkHeader();\n\t\t\t\tif (chunk.length < 0) {\n\t\t\t\t\treturn; // Invalid chunk length\n\t\t\t\t}\n\n\t\t\t\tswitch (chunk.type) {\n\t\t\t\t\tcase 'IDAT':\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'png',\n\t\t\t\t\t\t\tmime: 'image/png',\n\t\t\t\t\t\t};\n\t\t\t\t\tcase 'acTL':\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'apng',\n\t\t\t\t\t\t\tmime: 'image/apng',\n\t\t\t\t\t\t};\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tawait tokenizer.ignore(chunk.length + 4); // Ignore chunk-data + CRC\n\t\t\t\t}\n\t\t\t} while (tokenizer.position + 8 < tokenizer.fileInfo.size);\n\n\t\t\treturn {\n\t\t\t\text: 'png',\n\t\t\t\tmime: 'image/png',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x41, 0x52, 0x52, 0x4F, 0x57, 0x31, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'arrow',\n\t\t\t\tmime: 'application/x-apache-arrow',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x67, 0x6C, 0x54, 0x46, 0x02, 0x00, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'glb',\n\t\t\t\tmime: 'model/gltf-binary',\n\t\t\t};\n\t\t}\n\n\t\t// `mov` format variants\n\t\tif (\n\t\t\tthis.check([0x66, 0x72, 0x65, 0x65], {offset: 4}) // `free`\n\t\t\t|| this.check([0x6D, 0x64, 0x61, 0x74], {offset: 4}) // `mdat` MJPEG\n\t\t\t|| this.check([0x6D, 0x6F, 0x6F, 0x76], {offset: 4}) // `moov`\n\t\t\t|| this.check([0x77, 0x69, 0x64, 0x65], {offset: 4}) // `wide`\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mov',\n\t\t\t\tmime: 'video/quicktime',\n\t\t\t};\n\t\t}\n\n\t\t// -- 9-byte signatures --\n\n\t\tif (this.check([0x49, 0x49, 0x52, 0x4F, 0x08, 0x00, 0x00, 0x00, 0x18])) {\n\t\t\treturn {\n\t\t\t\text: 'orf',\n\t\t\t\tmime: 'image/x-olympus-orf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('gimp xcf ')) {\n\t\t\treturn {\n\t\t\t\text: 'xcf',\n\t\t\t\tmime: 'image/x-xcf',\n\t\t\t};\n\t\t}\n\n\t\t// -- 12-byte signatures --\n\n\t\tif (this.check([0x49, 0x49, 0x55, 0x00, 0x18, 0x00, 0x00, 0x00, 0x88, 0xE7, 0x74, 0xD8])) {\n\t\t\treturn {\n\t\t\t\text: 'rw2',\n\t\t\t\tmime: 'image/x-panasonic-rw2',\n\t\t\t};\n\t\t}\n\n\t\t// ASF_Header_Object first 80 bytes\n\t\tif (this.check([0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11, 0xA6, 0xD9])) {\n\t\t\tasync function readHeader() {\n\t\t\t\tconst guid = new Uint8Array(16);\n\t\t\t\tawait tokenizer.readBuffer(guid);\n\t\t\t\treturn {\n\t\t\t\t\tid: guid,\n\t\t\t\t\tsize: Number(await tokenizer.readToken(Token.UINT64_LE)),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tawait tokenizer.ignore(30);\n\t\t\t// Search for header should be in first 1KB of file.\n\t\t\twhile (tokenizer.position + 24 < tokenizer.fileInfo.size) {\n\t\t\t\tconst header = await readHeader();\n\t\t\t\tlet payload = header.size - 24;\n\t\t\t\tif (_check(header.id, [0x91, 0x07, 0xDC, 0xB7, 0xB7, 0xA9, 0xCF, 0x11, 0x8E, 0xE6, 0x00, 0xC0, 0x0C, 0x20, 0x53, 0x65])) {\n\t\t\t\t\t// Sync on Stream-Properties-Object (B7DC0791-A9B7-11CF-8EE6-00C00C205365)\n\t\t\t\t\tconst typeId = new Uint8Array(16);\n\t\t\t\t\tpayload -= await tokenizer.readBuffer(typeId);\n\n\t\t\t\t\tif (_check(typeId, [0x40, 0x9E, 0x69, 0xF8, 0x4D, 0x5B, 0xCF, 0x11, 0xA8, 0xFD, 0x00, 0x80, 0x5F, 0x5C, 0x44, 0x2B])) {\n\t\t\t\t\t\t// Found audio:\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'asf',\n\t\t\t\t\t\t\tmime: 'audio/x-ms-asf',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (_check(typeId, [0xC0, 0xEF, 0x19, 0xBC, 0x4D, 0x5B, 0xCF, 0x11, 0xA8, 0xFD, 0x00, 0x80, 0x5F, 0x5C, 0x44, 0x2B])) {\n\t\t\t\t\t\t// Found video:\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'asf',\n\t\t\t\t\t\t\tmime: 'video/x-ms-asf',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tawait tokenizer.ignore(payload);\n\t\t\t}\n\n\t\t\t// Default to ASF generic extension\n\t\t\treturn {\n\t\t\t\text: 'asf',\n\t\t\t\tmime: 'application/vnd.ms-asf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\t\treturn {\n\t\t\t\text: 'ktx',\n\t\t\t\tmime: 'image/ktx',\n\t\t\t};\n\t\t}\n\n\t\tif ((this.check([0x7E, 0x10, 0x04]) || this.check([0x7E, 0x18, 0x04])) && this.check([0x30, 0x4D, 0x49, 0x45], {offset: 4})) {\n\t\t\treturn {\n\t\t\t\text: 'mie',\n\t\t\t\tmime: 'application/x-mie',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x27, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], {offset: 2})) {\n\t\t\treturn {\n\t\t\t\text: 'shp',\n\t\t\t\tmime: 'application/x-esri-shape',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xFF, 0x4F, 0xFF, 0x51])) {\n\t\t\treturn {\n\t\t\t\text: 'j2c',\n\t\t\t\tmime: 'image/j2c',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x00, 0x00, 0x0C, 0x6A, 0x50, 0x20, 0x20, 0x0D, 0x0A, 0x87, 0x0A])) {\n\t\t\t// JPEG-2000 family\n\n\t\t\tawait tokenizer.ignore(20);\n\t\t\tconst type = await tokenizer.readToken(new Token.StringType(4, 'ascii'));\n\t\t\tswitch (type) {\n\t\t\t\tcase 'jp2 ':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'jp2',\n\t\t\t\t\t\tmime: 'image/jp2',\n\t\t\t\t\t};\n\t\t\t\tcase 'jpx ':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'jpx',\n\t\t\t\t\t\tmime: 'image/jpx',\n\t\t\t\t\t};\n\t\t\t\tcase 'jpm ':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'jpm',\n\t\t\t\t\t\tmime: 'image/jpm',\n\t\t\t\t\t};\n\t\t\t\tcase 'mjp2':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'mj2',\n\t\t\t\t\t\tmime: 'image/mj2',\n\t\t\t\t\t};\n\t\t\t\tdefault:\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0xFF, 0x0A])\n\t\t\t|| this.check([0x00, 0x00, 0x00, 0x0C, 0x4A, 0x58, 0x4C, 0x20, 0x0D, 0x0A, 0x87, 0x0A])\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'jxl',\n\t\t\t\tmime: 'image/jxl',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xFE, 0xFF])) { // UTF-16-BOM-LE\n\t\t\tif (this.check([0, 60, 0, 63, 0, 120, 0, 109, 0, 108], {offset: 2})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'xml',\n\t\t\t\t\tmime: 'application/xml',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn undefined; // Some unknown text based format\n\t\t}\n\n\t\t// -- Unsafe signatures --\n\n\t\tif (\n\t\t\tthis.check([0x0, 0x0, 0x1, 0xBA])\n\t\t\t|| this.check([0x0, 0x0, 0x1, 0xB3])\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mpg',\n\t\t\t\tmime: 'video/mpeg',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x01, 0x00, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'ttf',\n\t\t\t\tmime: 'font/ttf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x00, 0x01, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'ico',\n\t\t\t\tmime: 'image/x-icon',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x00, 0x02, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'cur',\n\t\t\t\tmime: 'image/x-icon',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1])) {\n\t\t\t// Detected Microsoft Compound File Binary File (MS-CFB) Format.\n\t\t\treturn {\n\t\t\t\text: 'cfb',\n\t\t\t\tmime: 'application/x-cfb',\n\t\t\t};\n\t\t}\n\n\t\t// Increase sample size from 12 to 256.\n\t\tawait tokenizer.peekBuffer(this.buffer, {length: Math.min(256, tokenizer.fileInfo.size), mayBeLess: true});\n\n\t\tif (this.check([0x61, 0x63, 0x73, 0x70], {offset: 36})) {\n\t\t\treturn {\n\t\t\t\text: 'icc',\n\t\t\t\tmime: 'application/vnd.iccprofile',\n\t\t\t};\n\t\t}\n\n\t\t// ACE: requires 14 bytes in the buffer\n\t\tif (this.checkString('**ACE', {offset: 7}) && this.checkString('**', {offset: 12})) {\n\t\t\treturn {\n\t\t\t\text: 'ace',\n\t\t\t\tmime: 'application/x-ace-compressed',\n\t\t\t};\n\t\t}\n\n\t\t// -- 15-byte signatures --\n\n\t\tif (this.checkString('BEGIN:')) {\n\t\t\tif (this.checkString('VCARD', {offset: 6})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'vcf',\n\t\t\t\t\tmime: 'text/vcard',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (this.checkString('VCALENDAR', {offset: 6})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ics',\n\t\t\t\t\tmime: 'text/calendar',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\t// `raf` is here just to keep all the raw image detectors together.\n\t\tif (this.checkString('FUJIFILMCCD-RAW')) {\n\t\t\treturn {\n\t\t\t\text: 'raf',\n\t\t\t\tmime: 'image/x-fujifilm-raf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Extended Module:')) {\n\t\t\treturn {\n\t\t\t\text: 'xm',\n\t\t\t\tmime: 'audio/x-xm',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Creative Voice File')) {\n\t\t\treturn {\n\t\t\t\text: 'voc',\n\t\t\t\tmime: 'audio/x-voc',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x04, 0x00, 0x00, 0x00]) && this.buffer.length >= 16) { // Rough & quick check Pickle/ASAR\n\t\t\tconst jsonSize = new DataView(this.buffer.buffer).getUint32(12, true);\n\n\t\t\tif (jsonSize > 12 && this.buffer.length >= jsonSize + 16) {\n\t\t\t\ttry {\n\t\t\t\t\tconst header = new TextDecoder().decode(this.buffer.slice(16, jsonSize + 16));\n\t\t\t\t\tconst json = JSON.parse(header);\n\t\t\t\t\t// Check if Pickle is ASAR\n\t\t\t\t\tif (json.files) { // Final check, assuring Pickle/ASAR format\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'asar',\n\t\t\t\t\t\t\tmime: 'application/x-asar',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t} catch {}\n\t\t\t}\n\t\t}\n\n\t\tif (this.check([0x06, 0x0E, 0x2B, 0x34, 0x02, 0x05, 0x01, 0x01, 0x0D, 0x01, 0x02, 0x01, 0x01, 0x02])) {\n\t\t\treturn {\n\t\t\t\text: 'mxf',\n\t\t\t\tmime: 'application/mxf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('SCRM', {offset: 44})) {\n\t\t\treturn {\n\t\t\t\text: 's3m',\n\t\t\t\tmime: 'audio/x-s3m',\n\t\t\t};\n\t\t}\n\n\t\t// Raw MPEG-2 transport stream (188-byte packets)\n\t\tif (this.check([0x47]) && this.check([0x47], {offset: 188})) {\n\t\t\treturn {\n\t\t\t\text: 'mts',\n\t\t\t\tmime: 'video/mp2t',\n\t\t\t};\n\t\t}\n\n\t\t// Blu-ray Disc Audio-Video (BDAV) MPEG-2 transport stream has 4-byte TP_extra_header before each 188-byte packet\n\t\tif (this.check([0x47], {offset: 4}) && this.check([0x47], {offset: 196})) {\n\t\t\treturn {\n\t\t\t\text: 'mts',\n\t\t\t\tmime: 'video/mp2t',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x42, 0x4F, 0x4F, 0x4B, 0x4D, 0x4F, 0x42, 0x49], {offset: 60})) {\n\t\t\treturn {\n\t\t\t\text: 'mobi',\n\t\t\t\tmime: 'application/x-mobipocket-ebook',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x44, 0x49, 0x43, 0x4D], {offset: 128})) {\n\t\t\treturn {\n\t\t\t\text: 'dcm',\n\t\t\t\tmime: 'application/dicom',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4C, 0x00, 0x00, 0x00, 0x01, 0x14, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46])) {\n\t\t\treturn {\n\t\t\t\text: 'lnk',\n\t\t\t\tmime: 'application/x.ms.shortcut', // Invented by us\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x62, 0x6F, 0x6F, 0x6B, 0x00, 0x00, 0x00, 0x00, 0x6D, 0x61, 0x72, 0x6B, 0x00, 0x00, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'alias',\n\t\t\t\tmime: 'application/x.apple.alias', // Invented by us\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Kaydara FBX Binary  \\u0000')) {\n\t\t\treturn {\n\t\t\t\text: 'fbx',\n\t\t\t\tmime: 'application/x.autodesk.fbx', // Invented by us\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x4C, 0x50], {offset: 34})\n\t\t\t&& (\n\t\t\t\tthis.check([0x00, 0x00, 0x01], {offset: 8})\n\t\t\t\t|| this.check([0x01, 0x00, 0x02], {offset: 8})\n\t\t\t\t|| this.check([0x02, 0x00, 0x02], {offset: 8})\n\t\t\t)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'eot',\n\t\t\t\tmime: 'application/vnd.ms-fontobject',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x06, 0x06, 0xED, 0xF5, 0xD8, 0x1D, 0x46, 0xE5, 0xBD, 0x31, 0xEF, 0xE7, 0xFE, 0x74, 0xB7, 0x1D])) {\n\t\t\treturn {\n\t\t\t\text: 'indd',\n\t\t\t\tmime: 'application/x-indesign',\n\t\t\t};\n\t\t}\n\n\t\t// Increase sample size from 256 to 512\n\t\tawait tokenizer.peekBuffer(this.buffer, {length: Math.min(512, tokenizer.fileInfo.size), mayBeLess: true});\n\n\t\t// Requires a buffer size of 512 bytes\n\t\tif (tarHeaderChecksumMatches(this.buffer)) {\n\t\t\treturn {\n\t\t\t\text: 'tar',\n\t\t\t\tmime: 'application/x-tar',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xFF, 0xFE])) { // UTF-16-BOM-BE\n\t\t\tif (this.check([60, 0, 63, 0, 120, 0, 109, 0, 108, 0], {offset: 2})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'xml',\n\t\t\t\t\tmime: 'application/xml',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (this.check([0xFF, 0x0E, 0x53, 0x00, 0x6B, 0x00, 0x65, 0x00, 0x74, 0x00, 0x63, 0x00, 0x68, 0x00, 0x55, 0x00, 0x70, 0x00, 0x20, 0x00, 0x4D, 0x00, 0x6F, 0x00, 0x64, 0x00, 0x65, 0x00, 0x6C, 0x00], {offset: 2})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'skp',\n\t\t\t\t\tmime: 'application/vnd.sketchup.skp',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn undefined; // Some text based format\n\t\t}\n\n\t\tif (this.checkString('-----BEGIN PGP MESSAGE-----')) {\n\t\t\treturn {\n\t\t\t\text: 'pgp',\n\t\t\t\tmime: 'application/pgp-encrypted',\n\t\t\t};\n\t\t}\n\n\t\t// Check MPEG 1 or 2 Layer 3 header, or 'layer 0' for ADTS (MPEG sync-word 0xFFE)\n\t\tif (this.buffer.length >= 2 && this.check([0xFF, 0xE0], {offset: 0, mask: [0xFF, 0xE0]})) {\n\t\t\tif (this.check([0x10], {offset: 1, mask: [0x16]})) {\n\t\t\t\t// Check for (ADTS) MPEG-2\n\t\t\t\tif (this.check([0x08], {offset: 1, mask: [0x08]})) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'aac',\n\t\t\t\t\t\tmime: 'audio/aac',\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\t// Must be (ADTS) MPEG-4\n\t\t\t\treturn {\n\t\t\t\t\text: 'aac',\n\t\t\t\t\tmime: 'audio/aac',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// MPEG 1 or 2 Layer 3 header\n\t\t\t// Check for MPEG layer 3\n\t\t\tif (this.check([0x02], {offset: 1, mask: [0x06]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp3',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// Check for MPEG layer 2\n\t\t\tif (this.check([0x04], {offset: 1, mask: [0x06]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp2',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// Check for MPEG layer 1\n\t\t\tif (this.check([0x06], {offset: 1, mask: [0x06]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp1',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\tasync readTiffTag(bigEndian) {\n\t\tconst tagId = await this.tokenizer.readToken(bigEndian ? Token.UINT16_BE : Token.UINT16_LE);\n\t\tthis.tokenizer.ignore(10);\n\t\tswitch (tagId) {\n\t\t\tcase 50_341:\n\t\t\t\treturn {\n\t\t\t\t\text: 'arw',\n\t\t\t\t\tmime: 'image/x-sony-arw',\n\t\t\t\t};\n\t\t\tcase 50_706:\n\t\t\t\treturn {\n\t\t\t\t\text: 'dng',\n\t\t\t\t\tmime: 'image/x-adobe-dng',\n\t\t\t\t};\n\t\t\tdefault:\n\t\t}\n\t}\n\n\tasync readTiffIFD(bigEndian) {\n\t\tconst numberOfTags = await this.tokenizer.readToken(bigEndian ? Token.UINT16_BE : Token.UINT16_LE);\n\t\tfor (let n = 0; n < numberOfTags; ++n) {\n\t\t\tconst fileType = await this.readTiffTag(bigEndian);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\t\t}\n\t}\n\n\tasync readTiffHeader(bigEndian) {\n\t\tconst version = (bigEndian ? Token.UINT16_BE : Token.UINT16_LE).get(this.buffer, 2);\n\t\tconst ifdOffset = (bigEndian ? Token.UINT32_BE : Token.UINT32_LE).get(this.buffer, 4);\n\n\t\tif (version === 42) {\n\t\t\t// TIFF file header\n\t\t\tif (ifdOffset >= 6) {\n\t\t\t\tif (this.checkString('CR', {offset: 8})) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'cr2',\n\t\t\t\t\t\tmime: 'image/x-canon-cr2',\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (ifdOffset >= 8 && (this.check([0x1C, 0x00, 0xFE, 0x00], {offset: 8}) || this.check([0x1F, 0x00, 0x0B, 0x00], {offset: 8}))) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'nef',\n\t\t\t\t\t\tmime: 'image/x-nikon-nef',\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tawait this.tokenizer.ignore(ifdOffset);\n\t\t\tconst fileType = await this.readTiffIFD(bigEndian);\n\t\t\treturn fileType ?? {\n\t\t\t\text: 'tif',\n\t\t\t\tmime: 'image/tiff',\n\t\t\t};\n\t\t}\n\n\t\tif (version === 43) {\t// Big TIFF file header\n\t\t\treturn {\n\t\t\t\text: 'tif',\n\t\t\t\tmime: 'image/tiff',\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport const supportedExtensions = new Set(extensions);\nexport const supportedMimeTypes = new Set(mimeTypes);\n"], "mappings": ";;;;;;AAAA;AAAA;AACA,YAAQ,OAAO,SAAU,QAAQ,QAAQ,MAAM,MAAM,QAAQ;AAC3D,UAAI,GAAG;AACP,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,QAAQ;AACZ,UAAI,IAAI,OAAQ,SAAS,IAAK;AAC9B,UAAI,IAAI,OAAO,KAAK;AACpB,UAAI,IAAI,OAAO,SAAS,CAAC;AAEzB,WAAK;AAEL,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,MAAC;AAE3E,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,MAAC;AAE3E,UAAI,MAAM,GAAG;AACX,YAAI,IAAI;AAAA,MACV,WAAW,MAAM,MAAM;AACrB,eAAO,IAAI,OAAQ,IAAI,KAAK,KAAK;AAAA,MACnC,OAAO;AACL,YAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,YAAI,IAAI;AAAA,MACV;AACA,cAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;AAAA,IAChD;AAEA,YAAQ,QAAQ,SAAU,QAAQ,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACnE,UAAI,GAAG,GAAG;AACV,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,KAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9D,UAAI,IAAI,OAAO,IAAK,SAAS;AAC7B,UAAI,IAAI,OAAO,IAAI;AACnB,UAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;AAE1D,cAAQ,KAAK,IAAI,KAAK;AAEtB,UAAI,MAAM,KAAK,KAAK,UAAU,UAAU;AACtC,YAAI,MAAM,KAAK,IAAI,IAAI;AACvB,YAAI;AAAA,MACN,OAAO;AACL,YAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG;AACzC,YAAI,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,eAAK;AAAA,QACP;AACA,YAAI,IAAI,SAAS,GAAG;AAClB,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,mBAAS,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK;AAAA,QACrC;AACA,YAAI,QAAQ,KAAK,GAAG;AAClB;AACA,eAAK;AAAA,QACP;AAEA,YAAI,IAAI,SAAS,MAAM;AACrB,cAAI;AACJ,cAAI;AAAA,QACN,WAAW,IAAI,SAAS,GAAG;AACzB,eAAM,QAAQ,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI;AACxC,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,QAAQ,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AACrD,cAAI;AAAA,QACN;AAAA,MACF;AAEA,aAAO,QAAQ,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,MAAC;AAE/E,UAAK,KAAK,OAAQ;AAClB,cAAQ;AACR,aAAO,OAAO,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,MAAC;AAE9E,aAAO,SAAS,IAAI,CAAC,KAAK,IAAI;AAAA,IAChC;AAAA;AAAA;;;ACpFA,cAAyB;AAEzB,SAAS,GAAG,OAAO;AACf,SAAO,IAAI,SAAS,MAAM,QAAQ,MAAM,UAAU;AACtD;AAIO,IAAM,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,SAAS,MAAM;AAAA,EACpC;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,SAAS,QAAQ,KAAK;AAChC,WAAO,SAAS;AAAA,EACpB;AACJ;AAIO,IAAM,YAAY;AAAA,EACrB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,UAAU,QAAQ,IAAI;AAAA,EAC3C;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,UAAU,QAAQ,OAAO,IAAI;AACvC,WAAO,SAAS;AAAA,EACpB;AACJ;AAIO,IAAM,YAAY;AAAA,EACrB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,UAAU,MAAM;AAAA,EACrC;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,UAAU,QAAQ,KAAK;AACjC,WAAO,SAAS;AAAA,EACpB;AACJ;AAoCO,IAAM,YAAY;AAAA,EACrB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,UAAU,QAAQ,IAAI;AAAA,EAC3C;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,UAAU,QAAQ,OAAO,IAAI;AACvC,WAAO,SAAS;AAAA,EACpB;AACJ;AAIO,IAAM,YAAY;AAAA,EACrB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,UAAU,MAAM;AAAA,EACrC;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,UAAU,QAAQ,KAAK;AACjC,WAAO,SAAS;AAAA,EACpB;AACJ;AA2EO,IAAM,WAAW;AAAA,EACpB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,SAAS,MAAM;AAAA,EACpC;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,SAAS,QAAQ,KAAK;AAChC,WAAO,SAAS;AAAA,EACpB;AACJ;AAiBO,IAAM,YAAY;AAAA,EACrB,KAAK;AAAA,EACL,IAAI,OAAO,QAAQ;AACf,WAAO,GAAG,KAAK,EAAE,aAAa,QAAQ,IAAI;AAAA,EAC9C;AAAA,EACA,IAAI,OAAO,QAAQ,OAAO;AACtB,OAAG,KAAK,EAAE,aAAa,QAAQ,OAAO,IAAI;AAC1C,WAAO,SAAS;AAAA,EACpB;AACJ;AA0KO,IAAM,aAAN,MAAiB;AAAA,EACpB,YAAY,KAAK,UAAU;AACvB,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,cAAc,IAAI,YAAY,QAAQ;AAAA,EAC/C;AAAA,EACA,IAAI,YAAY,QAAQ;AACpB,WAAO,KAAK,YAAY,OAAO,WAAW,SAAS,QAAQ,SAAS,KAAK,GAAG,CAAC;AAAA,EACjF;AACJ;;;ACtYO,IAAM,kBAAkB;AAIxB,IAAM,mBAAN,cAA+B,MAAM;AAAA,EACxC,cAAc;AACV,UAAM,eAAe;AAAA,EACzB;AACJ;;;ACPO,IAAM,uBAAN,MAA2B;AAAA,EAC9B,cAAc;AAIV,SAAK,oBAAoB,IAAI,OAAO;AACpC,SAAK,cAAc;AAKnB,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA,EACA,MAAM,KAAK,YAAY,QAAQ,QAAQ;AACnC,UAAM,YAAY,MAAM,KAAK,KAAK,YAAY,QAAQ,MAAM;AAC5D,SAAK,UAAU,KAAK,WAAW,SAAS,QAAQ,SAAS,SAAS,CAAC;AACnE,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,QAAQ,QAAQ,QAAQ;AAC/B,QAAI,WAAW,GAAG;AACd,aAAO;AAAA,IACX;AACA,QAAI,YAAY,KAAK,mBAAmB,QAAQ,QAAQ,MAAM;AAC9D,iBAAa,MAAM,KAAK,wBAAwB,QAAQ,SAAS,WAAW,SAAS,SAAS;AAC9F,QAAI,cAAc,GAAG;AACjB,YAAM,IAAI,iBAAiB;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,QAAQ,QAAQ,QAAQ;AACvC,QAAI,YAAY;AAChB,QAAI,YAAY;AAEhB,WAAO,KAAK,UAAU,SAAS,KAAK,YAAY,GAAG;AAC/C,YAAM,WAAW,KAAK,UAAU,IAAI;AACpC,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,4BAA4B;AAChD,YAAM,UAAU,KAAK,IAAI,SAAS,QAAQ,SAAS;AACnD,aAAO,IAAI,SAAS,SAAS,GAAG,OAAO,GAAG,SAAS,SAAS;AAC5D,mBAAa;AACb,mBAAa;AACb,UAAI,UAAU,SAAS,QAAQ;AAE3B,aAAK,UAAU,KAAK,SAAS,SAAS,OAAO,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,wBAAwB,QAAQ,QAAQ,kBAAkB;AAC5D,QAAI,YAAY;AAChB,QAAI,YAAY;AAEhB,WAAO,YAAY,KAAK,CAAC,KAAK,aAAa;AACvC,YAAM,SAAS,KAAK,IAAI,WAAW,KAAK,iBAAiB;AACzD,YAAM,WAAW,MAAM,KAAK,eAAe,QAAQ,SAAS,WAAW,MAAM;AAC7E,UAAI,aAAa;AACb;AACJ,mBAAa;AACb,mBAAa;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AACJ;;;AC/DO,IAAM,kBAAN,cAA8B,qBAAqB;AAAA,EACtD,YAAY,QAAQ;AAChB,UAAM;AACN,SAAK,SAAS,OAAO,UAAU,EAAE,MAAM,OAAO,CAAC;AAAA,EACnD;AAAA,EACA,MAAM,eAAe,QAAQ,QAAQ,QAAQ;AACzC,QAAI,KAAK,aAAa;AAClB,YAAM,IAAI,iBAAiB;AAAA,IAC/B;AACA,UAAM,SAAS,MAAM,KAAK,OAAO,KAAK,IAAI,WAAW,MAAM,CAAC;AAC5D,QAAI,OAAO,MAAM;AACb,WAAK,cAAc,OAAO;AAAA,IAC9B;AACA,QAAI,OAAO,OAAO;AACd,aAAO,IAAI,OAAO,OAAO,MAAM;AAC/B,aAAO,OAAO,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,WAAO,KAAK,OAAO,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,QAAQ;AACV,UAAM,KAAK,MAAM;AACjB,SAAK,OAAO,YAAY;AAAA,EAC5B;AACJ;;;AC7BO,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,YAAY,SAAS;AACjB,SAAK,YAAY,IAAI,WAAW,CAAC;AAIjC,SAAK,WAAW;AAChB,SAAK,UAAU,mCAAS;AACxB,QAAI,mCAAS,aAAa;AACtB,cAAQ,YAAY,iBAAiB,SAAS,MAAM;AAChD,aAAK,MAAM;AAAA,MACf,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,UAAU,OAAO,WAAW,KAAK,UAAU;AAC7C,UAAM,aAAa,IAAI,WAAW,MAAM,GAAG;AAC3C,UAAM,MAAM,MAAM,KAAK,WAAW,YAAY,EAAE,SAAS,CAAC;AAC1D,QAAI,MAAM,MAAM;AACZ,YAAM,IAAI,iBAAiB;AAC/B,WAAO,MAAM,IAAI,YAAY,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,UAAU,OAAO,WAAW,KAAK,UAAU;AAC7C,UAAM,aAAa,IAAI,WAAW,MAAM,GAAG;AAC3C,UAAM,MAAM,MAAM,KAAK,WAAW,YAAY,EAAE,SAAS,CAAC;AAC1D,QAAI,MAAM,MAAM;AACZ,YAAM,IAAI,iBAAiB;AAC/B,WAAO,MAAM,IAAI,YAAY,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW,OAAO;AACpB,UAAM,MAAM,MAAM,KAAK,WAAW,KAAK,WAAW,EAAE,QAAQ,MAAM,IAAI,CAAC;AACvE,QAAI,MAAM,MAAM;AACZ,YAAM,IAAI,iBAAiB;AAC/B,WAAO,MAAM,IAAI,KAAK,WAAW,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW,OAAO;AACpB,UAAM,MAAM,MAAM,KAAK,WAAW,KAAK,WAAW,EAAE,QAAQ,MAAM,IAAI,CAAC;AACvE,QAAI,MAAM,MAAM;AACZ,YAAM,IAAI,iBAAiB;AAC/B,WAAO,MAAM,IAAI,KAAK,WAAW,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,QAAQ;AACjB,QAAI,KAAK,SAAS,SAAS,QAAW;AAClC,YAAM,YAAY,KAAK,SAAS,OAAO,KAAK;AAC5C,UAAI,SAAS,WAAW;AACpB,aAAK,YAAY;AACjB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ;AAvFlB;AAwFQ,UAAM,KAAK,MAAM;AACjB,YAAM,UAAK,YAAL;AAAA,EACV;AAAA,EACA,iBAAiB,YAAY,SAAS;AAClC,QAAI,WAAW,QAAQ,aAAa,UAAa,QAAQ,WAAW,KAAK,UAAU;AAC/E,YAAM,IAAI,MAAM,uEAAuE;AAAA,IAC3F;AACA,QAAI,SAAS;AACT,aAAO;AAAA,QACH,WAAW,QAAQ,cAAc;AAAA,QACjC,QAAQ,QAAQ,SAAS,QAAQ,SAAS;AAAA,QAC1C,QAAQ,QAAQ,SAAS,QAAQ,SAAU,WAAW,UAAU,QAAQ,SAAS,QAAQ,SAAS;AAAA,QAClG,UAAU,QAAQ,WAAW,QAAQ,WAAW,KAAK;AAAA,MACzD;AAAA,IACJ;AACA,WAAO;AAAA,MACH,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ,WAAW;AAAA,MACnB,UAAU,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AACJ;;;AC/GA,IAAM,gBAAgB;AACf,IAAM,sBAAN,cAAkC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvD,YAAY,cAAc,SAAS;AAC/B,UAAM,OAAO;AACb,SAAK,eAAe;AACpB,SAAK,YAAW,mCAAS,aAAY,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,YAAY,SAAS;AAClC,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,UAAM,YAAY,YAAY,WAAW,KAAK;AAC9C,QAAI,YAAY,GAAG;AACf,YAAM,KAAK,OAAO,SAAS;AAC3B,aAAO,KAAK,WAAW,YAAY,OAAO;AAAA,IAC9C;AACA,QAAI,YAAY,GAAG;AACf,YAAM,IAAI,MAAM,uEAAuE;AAAA,IAC3F;AACA,QAAI,YAAY,WAAW,GAAG;AAC1B,aAAO;AAAA,IACX;AACA,UAAM,YAAY,MAAM,KAAK,aAAa,KAAK,YAAY,YAAY,QAAQ,YAAY,MAAM;AACjG,SAAK,YAAY;AACjB,SAAK,CAAC,WAAW,CAAC,QAAQ,cAAc,YAAY,YAAY,QAAQ;AACpE,YAAM,IAAI,iBAAiB;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,YAAY,SAAS;AAClC,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,QAAI,YAAY;AAChB,QAAI,YAAY,UAAU;AACtB,YAAM,YAAY,YAAY,WAAW,KAAK;AAC9C,UAAI,YAAY,GAAG;AACf,cAAM,aAAa,IAAI,WAAW,YAAY,SAAS,SAAS;AAChE,oBAAY,MAAM,KAAK,WAAW,YAAY,EAAE,WAAW,YAAY,UAAU,CAAC;AAClF,mBAAW,IAAI,WAAW,SAAS,SAAS,GAAG,YAAY,MAAM;AACjE,eAAO,YAAY;AAAA,MACvB;AACA,UAAI,YAAY,GAAG;AACf,cAAM,IAAI,MAAM,gDAAgD;AAAA,MACpE;AAAA,IACJ;AACA,QAAI,YAAY,SAAS,GAAG;AACxB,UAAI;AACA,oBAAY,MAAM,KAAK,aAAa,KAAK,YAAY,YAAY,QAAQ,YAAY,MAAM;AAAA,MAC/F,SACO,KAAK;AACR,aAAI,mCAAS,cAAa,eAAe,kBAAkB;AACvD,iBAAO;AAAA,QACX;AACA,cAAM;AAAA,MACV;AACA,UAAK,CAAC,YAAY,aAAc,YAAY,YAAY,QAAQ;AAC5D,cAAM,IAAI,iBAAiB;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,OAAO,QAAQ;AAEjB,UAAM,UAAU,KAAK,IAAI,eAAe,MAAM;AAC9C,UAAM,MAAM,IAAI,WAAW,OAAO;AAClC,QAAI,eAAe;AACnB,WAAO,eAAe,QAAQ;AAC1B,YAAM,YAAY,SAAS;AAC3B,YAAM,YAAY,MAAM,KAAK,WAAW,KAAK,EAAE,QAAQ,KAAK,IAAI,SAAS,SAAS,EAAE,CAAC;AACrF,UAAI,YAAY,GAAG;AACf,eAAO;AAAA,MACX;AACA,sBAAgB;AAAA,IACpB;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,WAAO,KAAK,aAAa,MAAM;AAAA,EACnC;AAAA,EACA,uBAAuB;AACnB,WAAO;AAAA,EACX;AACJ;;;AChGO,IAAM,kBAAN,cAA8B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnD,YAAY,YAAY,SAAS;AAC7B,UAAM,OAAO;AACb,SAAK,aAAa;AAClB,SAAK,WAAW,EAAE,IAAG,mCAAS,aAAY,CAAC,GAAG,GAAG,EAAE,MAAM,WAAW,OAAO,EAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,YAAY,SAAS;AAClC,QAAI,mCAAS,UAAU;AACnB,UAAI,QAAQ,WAAW,KAAK,UAAU;AAClC,cAAM,IAAI,MAAM,uEAAuE;AAAA,MAC3F;AACA,WAAK,WAAW,QAAQ;AAAA,IAC5B;AACA,UAAM,YAAY,MAAM,KAAK,WAAW,YAAY,OAAO;AAC3D,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,YAAY,SAAS;AAClC,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,UAAM,aAAa,KAAK,IAAI,KAAK,WAAW,SAAS,YAAY,UAAU,YAAY,MAAM;AAC7F,QAAK,CAAC,YAAY,aAAc,aAAa,YAAY,QAAQ;AAC7D,YAAM,IAAI,iBAAiB;AAAA,IAC/B;AACA,eAAW,IAAI,KAAK,WAAW,SAAS,YAAY,UAAU,YAAY,WAAW,UAAU,GAAG,YAAY,MAAM;AACpH,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,WAAO,MAAM,MAAM;AAAA,EACvB;AAAA,EACA,uBAAuB;AACnB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,UAAU;AAClB,SAAK,WAAW;AAAA,EACpB;AACJ;;;AChCO,SAAS,cAAc,WAAW,SAAS;AAC9C,SAAO,IAAI,oBAAoB,IAAI,gBAAgB,SAAS,GAAG,OAAO;AAC1E;AAOO,SAAS,WAAW,YAAY,SAAS;AAC5C,SAAO,IAAI,gBAAgB,YAAY,OAAO;AAClD;;;AC8EA,IAAM,iBAAiB;AAAA,EACtB,MAAM,IAAI,WAAW,YAAY,MAAM;AACxC;AAcA,IAAM,gBAAgB,IAAI,WAAW,YAAY;AAqDjD,IAAM,uBAAuB,MAAM,KAAK,EAAC,QAAQ,IAAG,GAAG,CAAC,GAAG,UAAU,MAAM,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAqEjG,SAAS,UAAU,MAAM;AAC/B,QAAM,EAAC,WAAU,IAAI;AAErB,MAAI,eAAe,GAAG;AACrB,WAAQ,KAAK,UAAU,CAAC,IAAK,KAAK,KAAO,KAAK,UAAU,CAAC;AAAA,EAC1D;AAEA,MAAI,eAAe,GAAG;AACrB,WAAQ,KAAK,SAAS,CAAC,IAAK,KAAK,KAAO,KAAK,UAAU,CAAC;AAAA,EACzD;AAEA,MAAI,eAAe,GAAG;AACrB,WAAO,KAAK,UAAU,CAAC;AAAA,EACxB;AAEA,MAAI,eAAe,GAAG;AACrB,WAAQ,KAAK,SAAS,CAAC,IAAK,KAAK,KAAO,KAAK,UAAU,CAAC;AAAA,EACzD;AAEA,MAAI,eAAe,GAAG;AACrB,WAAO,KAAK,UAAU,CAAC;AAAA,EACxB;AAEA,MAAI,eAAe,GAAG;AACrB,WAAO,KAAK,SAAS,CAAC;AAAA,EACvB;AACD;AAOO,SAAS,QAAQ,OAAO,OAAO;AACrC,QAAM,cAAc,MAAM;AAC1B,QAAM,cAAc,MAAM;AAE1B,MAAI,gBAAgB,GAAG;AACtB,WAAO;AAAA,EACR;AAEA,MAAI,cAAc,aAAa;AAC9B,WAAO;AAAA,EACR;AAEA,QAAM,oBAAoB,cAAc;AAExC,WAAS,QAAQ,GAAG,SAAS,mBAAmB,SAAS;AACxD,QAAI,UAAU;AACd,aAAS,SAAS,GAAG,SAAS,aAAa,UAAU;AACpD,UAAI,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,GAAG;AAC5C,kBAAU;AACV;AAAA,MACD;AAAA,IACD;AAEA,QAAI,SAAS;AACZ,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAOO,SAAS,SAAS,OAAO,OAAO;AACtC,SAAO,QAAQ,OAAO,KAAK,MAAM;AAClC;;;AC9TO,SAAS,cAAc,QAAQ;AACrC,SAAO,CAAC,GAAG,MAAM,EAAE,IAAI,eAAa,UAAU,WAAW,CAAC,CAAC;AAC5D;AASO,SAAS,yBAAyB,aAAa,SAAS,GAAG;AACjE,QAAM,UAAU,OAAO,SAAS,IAAI,WAAW,CAAC,EAAE,IAAI,aAAa,GAAG,EAAE,QAAQ,SAAS,EAAE,EAAE,KAAK,GAAG,CAAC;AACtG,MAAI,OAAO,MAAM,OAAO,GAAG;AAC1B,WAAO;AAAA,EACR;AAEA,MAAI,MAAM,IAAI;AAEd,WAAS,QAAQ,QAAQ,QAAQ,SAAS,KAAK,SAAS;AACvD,WAAO,YAAY,KAAK;AAAA,EACzB;AAEA,WAAS,QAAQ,SAAS,KAAK,QAAQ,SAAS,KAAK,SAAS;AAC7D,WAAO,YAAY,KAAK;AAAA,EACzB;AAEA,SAAO,YAAY;AACpB;AAMO,IAAM,sBAAsB;AAAA,EAClC,KAAK,CAAC,QAAQ,WAAY,OAAO,SAAS,CAAC,IAAI,MAAU,OAAO,SAAS,CAAC,KAAM,IAAO,OAAO,SAAS,CAAC,KAAM,KAAQ,OAAO,MAAM,KAAM;AAAA,EACzI,KAAK;AACN;;;ACvCO,IAAM,aAAa;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA;AACD;;;ACvSO,IAAM,iCAAiC;AAE9C,eAAsB,mBAAmB,QAAQ;AAChD,SAAO,IAAI,eAAe,EAAE,WAAW,MAAM;AAC9C;AAEA,eAAsB,mBAAmB,OAAO;AAC/C,SAAO,IAAI,eAAe,EAAE,WAAW,KAAK;AAC7C;AAEA,eAAsB,iBAAiB,MAAM;AAC5C,SAAO,IAAI,eAAe,EAAE,SAAS,IAAI;AAC1C;AAEA,SAAS,OAAO,QAAQ,SAAS,SAAS;AACzC,YAAU;AAAA,IACT,QAAQ;AAAA,IACR,GAAG;AAAA,EACJ;AAEA,aAAW,CAAC,OAAO,MAAM,KAAK,QAAQ,QAAQ,GAAG;AAEhD,QAAI,QAAQ,MAAM;AAEjB,UAAI,YAAY,QAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,QAAQ,MAAM,IAAI;AACtE,eAAO;AAAA,MACR;AAAA,IACD,WAAW,WAAW,OAAO,QAAQ,QAAQ,MAAM,GAAG;AACrD,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,sBAAsB,WAAW;AACtD,SAAO,IAAI,eAAe,EAAE,cAAc,SAAS;AACpD;AAEA,eAAsB,eAAe,WAAW,SAAS;AACxD,SAAO,IAAI,eAAe,OAAO,EAAE,kBAAkB,WAAW,OAAO;AACxE;AAEO,IAAM,iBAAN,MAAqB;AAAA,EAC3B,YAAY,SAAS;AACpB,SAAK,YAAY,mCAAS;AAC1B,SAAK,mBAAmB;AAAA,MACvB,aAAa,mCAAS;AAAA,IACvB;AACA,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EAClC;AAAA,EAEA,MAAM,cAAc,WAAW;AAC9B,UAAM,kBAAkB,UAAU;AAElC,eAAW,YAAY,KAAK,aAAa,CAAC,GAAG;AAC5C,YAAM,WAAW,MAAM,SAAS,SAAS;AACzC,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAEA,UAAI,oBAAoB,UAAU,UAAU;AAC3C,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,KAAK,MAAM,SAAS;AAAA,EAC5B;AAAA,EAEA,MAAM,WAAW,OAAO;AACvB,QAAI,EAAE,iBAAiB,cAAc,iBAAiB,cAAc;AACnE,YAAM,IAAI,UAAU,0FAA0F,OAAO,KAAK,IAAI;AAAA,IAC/H;AAEA,UAAM,SAAS,iBAAiB,aAAa,QAAQ,IAAI,WAAW,KAAK;AAEzE,QAAI,GAAE,iCAAQ,UAAS,IAAI;AAC1B;AAAA,IACD;AAEA,WAAO,KAAK,cAAsB,WAAW,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EAEA,MAAM,SAAS,MAAM;AACpB,WAAO,KAAK,WAAW,KAAK,OAAO,CAAC;AAAA,EACrC;AAAA,EAEA,MAAM,WAAW,QAAQ;AACxB,UAAM,YAAY,MAAc,cAAc,QAAQ,KAAK,gBAAgB;AAC3E,QAAI;AACH,aAAO,MAAM,KAAK,cAAc,SAAS;AAAA,IAC1C,UAAE;AACD,YAAM,UAAU,MAAM;AAAA,IACvB;AAAA,EACD;AAAA,EAEA,MAAM,kBAAkB,QAAQ,SAAS;AACxC,UAAM,EAAC,aAAa,+BAA8B,IAAI;AACtD,QAAI;AACJ,QAAI;AAEJ,UAAM,SAAS,OAAO,UAAU,EAAC,MAAM,OAAM,CAAC;AAC9C,QAAI;AAEH,YAAM,EAAC,OAAO,OAAO,KAAI,IAAI,MAAM,OAAO,KAAK,IAAI,WAAW,UAAU,CAAC;AACzE,mBAAa;AACb,UAAI,CAAC,QAAQ,OAAO;AACnB,YAAI;AAEH,6BAAmB,MAAM,KAAK,WAAW,MAAM,MAAM,GAAG,UAAU,CAAC;AAAA,QACpE,SAAS,OAAO;AACf,cAAI,EAAE,iBAAyB,mBAAmB;AACjD,kBAAM;AAAA,UACP;AAEA,6BAAmB;AAAA,QACpB;AAAA,MACD;AAEA,mBAAa;AAAA,IACd,UAAE;AACD,aAAO,YAAY;AAAA,IACpB;AAGA,UAAM,kBAAkB,IAAI,gBAAgB;AAAA,MAC3C,MAAM,MAAM,YAAY;AACvB,mBAAW,QAAQ,UAAU;AAAA,MAC9B;AAAA,MACA,UAAU,OAAO,YAAY;AAE5B,mBAAW,QAAQ,KAAK;AAAA,MACzB;AAAA,IACD,CAAC;AAED,UAAM,YAAY,OAAO,YAAY,eAAe;AACpD,cAAU,WAAW;AAErB,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,QAAQ,SAAS;AACtB,WAAO,OAAO,KAAK,QAAQ,QAAQ,OAAO;AAAA,EAC3C;AAAA,EAEA,YAAY,QAAQ,SAAS;AAC5B,WAAO,KAAK,MAAM,cAAc,MAAM,GAAG,OAAO;AAAA,EACjD;AAAA,EAEA,MAAM,MAAM,WAAW;AACtB,SAAK,SAAS,IAAI,WAAW,8BAA8B;AAG3D,QAAI,UAAU,SAAS,SAAS,QAAW;AAC1C,gBAAU,SAAS,OAAO,OAAO;AAAA,IAClC;AAEA,SAAK,YAAY;AAEjB,UAAM,UAAU,WAAW,KAAK,QAAQ,EAAC,QAAQ,IAAI,WAAW,KAAI,CAAC;AAIrE,QAAI,KAAK,MAAM,CAAC,IAAM,EAAI,CAAC,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,GAAI,CAAC,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,CAAI,CAAC,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,EAAI,CAAC,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,EAAI,CAAC,GAAG;AAC7B,YAAM,UAAU,WAAW,KAAK,QAAQ,EAAC,QAAQ,IAAI,WAAW,KAAI,CAAC;AAErE,UACC,KAAK,YAAY,aAAa,EAAC,QAAQ,EAAC,CAAC,KACtC,KAAK,YAAY,UAAU,EAAC,QAAQ,GAAE,CAAC,GACzC;AACD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,MAAM,CAAC,IAAM,GAAI,CAAC,KACpB,KAAK,MAAM,CAAC,IAAM,GAAI,CAAC,GACzB;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,GAAI,CAAC,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,GAAI,CAAC,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,GAAI,CAAC,GAAG;AAEnC,WAAK,UAAU,OAAO,CAAC;AACvB,aAAO,KAAK,MAAM,SAAS;AAAA,IAC5B;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,EAAI,CAAC,GAAG;AACnC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,GAAI,CAAC,GAAG;AACnC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,KAAM,CAAG,CAAC,GAAG;AAClC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,GAAI,CAAC,GAAG;AACnC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,KAAK,GAAG;AAC5B,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,kBAAkB,MAAM,UAAU,UAAU,mBAAmB;AACrE,UAAI,UAAU,WAAW,kBAAkB,UAAU,SAAS,MAAM;AAEnE,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,YAAM,UAAU,OAAO,eAAe;AACtC,aAAO,KAAK,cAAc,SAAS;AAAA,IACpC;AAGA,QAAI,KAAK,YAAY,KAAK,GAAG;AAC5B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,SACE,KAAK,OAAO,CAAC,MAAM,MAAQ,KAAK,OAAO,CAAC,MAAM,OAC5C,KAAK,MAAM,CAAC,IAAM,EAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GACtC;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAKA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,GAAI,CAAC,GAAG;AACnC,UAAI,KAAK,MAAM,CAAC,GAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AACpC,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,KAAM,CAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,EAAC,QAAQ,EAAC,CAAC,GAAG;AAC1C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,EAAC,QAAQ,EAAC,CAAC,GAAG;AAC1C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,GAAK,CAAG,CAAC,GAAG;AACvC,UAAI;AACH,eAAO,UAAU,WAAW,KAAK,UAAU,SAAS,MAAM;AACzD,gBAAM,UAAU,WAAW,KAAK,QAAQ,EAAC,QAAQ,GAAE,CAAC;AAEpD,gBAAM,OAAO,IAAI,SAAS,KAAK,OAAO,MAAM;AAG5C,gBAAM,YAAY;AAAA,YACjB,gBAAgB,KAAK,UAAU,IAAI,IAAI;AAAA,YACvC,kBAAkB,KAAK,UAAU,IAAI,IAAI;AAAA,YACzC,gBAAgB,KAAK,UAAU,IAAI,IAAI;AAAA,YACvC,kBAAkB,KAAK,UAAU,IAAI,IAAI;AAAA,UAC1C;AAEA,oBAAU,WAAW,MAAM,UAAU,UAAU,IAAU,WAAW,UAAU,gBAAgB,OAAO,CAAC;AACtG,gBAAM,UAAU,OAAO,UAAU,gBAAgB;AAEjD,cAAI,kBAAkB,KAAK,UAAU,QAAQ,GAAG;AAC/C,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAGA,cAAI,UAAU,aAAa,wBAAwB;AAClD,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAEA,cAAI,UAAU,SAAS,SAAS,OAAO,KAAK,UAAU,SAAS,SAAS,MAAM,GAAG;AAChF,kBAAM,OAAO,UAAU,SAAS,MAAM,GAAG,EAAE,CAAC;AAC5C,oBAAQ,MAAM;AAAA,cACb,KAAK;AACJ;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD;AACC;AAAA,YACF;AAAA,UACD;AAEA,cAAI,UAAU,SAAS,WAAW,KAAK,GAAG;AACzC,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAEA,cAAI,UAAU,SAAS,WAAW,KAAK,KAAK,UAAU,SAAS,SAAS,QAAQ,GAAG;AAClF,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAQA,cAAI,UAAU,aAAa,cAAc,UAAU,mBAAmB,UAAU,kBAAkB;AACjG,gBAAI,WAAW,MAAM,UAAU,UAAU,IAAU,WAAW,UAAU,gBAAgB,OAAO,CAAC;AAChG,uBAAW,SAAS,KAAK;AAEzB,oBAAQ,UAAU;AAAA,cACjB,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD,KAAK;AACJ,uBAAO;AAAA,kBACN,KAAK;AAAA,kBACL,MAAM;AAAA,gBACP;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAGA,cAAI,UAAU,mBAAmB,GAAG;AACnC,gBAAI,kBAAkB;AAEtB,mBAAO,kBAAkB,KAAM,UAAU,WAAW,UAAU,SAAS,MAAO;AAC7E,oBAAM,UAAU,WAAW,KAAK,QAAQ,EAAC,WAAW,KAAI,CAAC;AAEzD,gCAAkB,QAAQ,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAM,IAAM,GAAM,CAAI,CAAC,CAAC;AAG/E,oBAAM,UAAU,OAAO,mBAAmB,IAAI,kBAAkB,KAAK,OAAO,MAAM;AAAA,YACnF;AAAA,UACD,OAAO;AACN,kBAAM,UAAU,OAAO,UAAU,cAAc;AAAA,UAChD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,EAAE,iBAAyB,mBAAmB;AACjD,gBAAM;AAAA,QACP;AAAA,MACD;AAEA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAE7B,YAAM,UAAU,OAAO,EAAE;AACzB,YAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,YAAM,UAAU,WAAW,IAAI;AAG/B,UAAI,OAAO,MAAM,CAAC,IAAM,KAAM,KAAM,KAAM,IAAM,KAAM,IAAM,GAAI,CAAC,GAAG;AACnE,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,OAAO,MAAM,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,EAAI,CAAC,GAAG;AAC7D,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,OAAO,MAAM,CAAC,GAAM,KAAM,KAAM,KAAM,KAAM,KAAM,CAAI,CAAC,GAAG;AAC7D,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,OAAO,MAAM,CAAC,KAAM,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AACjD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,OAAO,MAAM,CAAC,IAAM,KAAM,KAAM,KAAM,KAAM,IAAM,EAAI,CAAC,GAAG;AAC7D,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,OAAO,MAAM,CAAC,GAAM,KAAM,KAAM,KAAM,IAAM,KAAM,GAAI,CAAC,GAAG;AAC7D,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,MAAM,CAAC,IAAM,EAAI,CAAC,MACnB,KAAK,OAAO,CAAC,MAAM,KAAO,KAAK,OAAO,CAAC,MAAM,KAAO,KAAK,OAAO,CAAC,MAAM,OACvE,KAAK,OAAO,CAAC,MAAM,KAAO,KAAK,OAAO,CAAC,MAAM,KAAO,KAAK,OAAO,CAAC,MAAM,IAC1E;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAQA,QACC,KAAK,YAAY,QAAQ,EAAC,QAAQ,EAAC,CAAC,MAChC,KAAK,OAAO,CAAC,IAAI,QAAU,GAC9B;AAGD,YAAM,aAAa,IAAU,WAAW,GAAG,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,EAAE,QAAQ,MAAM,GAAG,EAAE,KAAK;AACjG,cAAQ,YAAY;AAAA,QACnB,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,EAAC,KAAK,QAAQ,MAAM,aAAY;AAAA,QACxC,KAAK;AACJ,iBAAO,EAAC,KAAK,QAAQ,MAAM,aAAY;AAAA,QACxC,KAAK;AACJ,iBAAO,EAAC,KAAK,QAAQ,MAAM,sBAAqB;AAAA,QACjD,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,EAAC,KAAK,QAAQ,MAAM,aAAY;AAAA,QACxC,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,EAAC,KAAK,QAAQ,MAAM,sBAAqB;AAAA,QACjD,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,kBAAiB;AAAA,QAC5C,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,cAAa;AAAA,QACxC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,QACtC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,QACtC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,cAAa;AAAA,QACxC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,QACtC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,QACtC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,QACtC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,QACtC,KAAK;AACJ,iBAAO,EAAC,KAAK,OAAO,MAAM,oBAAmB;AAAA,QAC9C;AACC,cAAI,WAAW,WAAW,IAAI,GAAG;AAChC,gBAAI,WAAW,WAAW,KAAK,GAAG;AACjC,qBAAO,EAAC,KAAK,OAAO,MAAM,cAAa;AAAA,YACxC;AAEA,mBAAO,EAAC,KAAK,OAAO,MAAM,aAAY;AAAA,UACvC;AAEA,iBAAO,EAAC,KAAK,OAAO,MAAM,YAAW;AAAA,MACvC;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,YAAY,MAAM,MAEtB,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAC7C,KAAK,YAAY,QAAQ,EAAC,QAAQ,EAAC,CAAC,IAEvC;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,YAAY,MAAM,MAEtB,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAC7C,KAAK,YAAY,QAAQ,EAAC,QAAQ,EAAC,CAAC,IAEvC;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,CAAC,GAAG;AACjF,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,UAAI;AACH,cAAM,UAAU,OAAO,IAAI;AAC3B,cAAMA,iBAAgB,KAAK,OAAO;AAClC,cAAM,SAAS,IAAI,WAAW,KAAK,IAAIA,gBAAe,UAAU,SAAS,IAAI,CAAC;AAC9E,cAAM,UAAU,WAAW,QAAQ,EAAC,WAAW,KAAI,CAAC;AAGpD,YAAI,SAAS,QAAQ,IAAI,YAAY,EAAE,OAAO,eAAe,CAAC,GAAG;AAChE,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AAEf,YAAI,EAAE,iBAAyB,mBAAmB;AACjD,gBAAM;AAAA,QACP;AAAA,MACD;AAGA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,IAAM,KAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,IAAM,EAAI,CAAC,GAAG;AAC7B,YAAM,WAAW,MAAM,KAAK,eAAe,KAAK;AAChD,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,IAAM,EAAI,CAAC,GAAG;AAC7B,YAAM,WAAW,MAAM,KAAK,eAAe,IAAI;AAC/C,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,KAAM,GAAI,CAAC,GAAG;AACzC,qBAAe,YAAY;AAC1B,cAAM,MAAM,MAAM,UAAU,WAAiB,KAAK;AAClD,YAAI,OAAO;AACX,YAAI,KAAK;AAET,gBAAQ,MAAM,UAAU,KAAK,SAAS,GAAG;AACxC,YAAE;AACF,mBAAS;AAAA,QACV;AAEA,cAAM,KAAK,IAAI,WAAW,KAAK,CAAC;AAChC,cAAM,UAAU,WAAW,EAAE;AAC7B,eAAO;AAAA,MACR;AAEA,qBAAe,cAAc;AAC5B,cAAM,UAAU,MAAM,UAAU;AAChC,cAAM,cAAc,MAAM,UAAU;AAEpC,oBAAY,CAAC,KAAK,OAAS,YAAY,SAAS;AAChD,cAAM,WAAW,KAAK,IAAI,GAAG,YAAY,MAAM;AAE/C,cAAM,SAAS,IAAI,SAAS,QAAQ,MAAM;AAC1C,cAAM,aAAa,IAAI,SAAS,YAAY,QAAQ,YAAY,SAAS,UAAU,QAAQ;AAE3F,eAAO;AAAA,UACN,IAAI,UAAU,MAAM;AAAA,UACpB,KAAK,UAAU,UAAU;AAAA,QAC1B;AAAA,MACD;AAEA,qBAAe,aAAa,UAAU;AACrC,eAAO,WAAW,GAAG;AACpB,gBAAM,UAAU,MAAM,YAAY;AAClC,cAAI,QAAQ,OAAO,OAAS;AAC3B,kBAAM,WAAW,MAAM,UAAU,UAAU,IAAU,WAAW,QAAQ,GAAG,CAAC;AAC5E,mBAAO,SAAS,WAAW,WAAW,EAAE;AAAA,UACzC;AAEA,gBAAM,UAAU,OAAO,QAAQ,GAAG;AAClC,YAAE;AAAA,QACH;AAAA,MACD;AAEA,YAAM,KAAK,MAAM,YAAY;AAC7B,YAAM,UAAU,MAAM,aAAa,GAAG,GAAG;AAEzC,cAAQ,SAAS;AAAA,QAChB,KAAK;AACJ,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QAED,KAAK;AACJ,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QAED;AACC;AAAA,MACF;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AACzC,UAAI,KAAK,MAAM,CAAC,IAAM,IAAM,EAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AAChD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,UAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AACtD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AACtD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,YAAY,MAAM,KACpB,KAAK,YAAY,MAAM,GACzB;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,KAAM,IAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,IAAM,CAAI,CAAC,GAAG;AAC/C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,OAAO,GAAG;AAC9B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,GAAG;AAC/B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,CAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KAClC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KACrC,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,GACvC;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,GAAI,CAAC,GAAG;AAEzC,UAAI,KAAK,MAAM,CAAC,EAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,GAAI,EAAC,CAAC,GAAG;AAClD,eAAO;AAAA,UACN,KAAK;AAAA;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,KAAK,MAAM,CAAC,EAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,GAAI,EAAC,CAAC,GAAG;AAClD,eAAO;AAAA,UACN,KAAK;AAAA;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,MAAM,GAAG;AAC7B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,KAAM,IAAM,KAAM,IAAM,IAAM,CAAI,CAAC,GAAG;AACrD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,GAAG;AAC/B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,KAAM,KAAM,KAAM,IAAM,EAAI,CAAC,GAAG;AACrD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,MAAM,CAAC,IAAM,IAAM,KAAM,IAAM,IAAM,CAAG,CAAC,MAC1C,KAAK,OAAO,CAAC,MAAM,KAAO,KAAK,OAAO,CAAC,MAAM,IAChD;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,GAAG;AAC/B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,IAAI,GAAG;AAC3B,YAAM,UAAU,IAAU,WAAW,GAAG,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC;AACpE,UAAI,QAAQ,MAAM,KAAK,KAAK,WAAW,OAAQ,WAAW,MAAM;AAC/D,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,GAAG;AAC/B,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,YAAY,SAAS,GAAG;AAChC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,SAAS,GAAG;AAChC,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,SAAS,MAAM,UAAU,UAAU,IAAU,WAAW,IAAI,OAAO,CAAC;AAC1E,UAAI,WAAW,iBAAiB;AAC/B,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,YAAY,QAAQ;AAAA,IAGvB,CAAC,MAAM,MAAM,KAAM,KAAK,IAAI,EAAE,KAAK,WAAS,KAAK,YAAY,OAAO,EAAC,QAAQ,EAAC,CAAC,CAAC,GACjF;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AASjE,YAAM,UAAU,OAAO,CAAC;AAExB,qBAAe,kBAAkB;AAChC,eAAO;AAAA,UACN,QAAQ,MAAM,UAAU,UAAgB,QAAQ;AAAA,UAChD,MAAM,MAAM,UAAU,UAAU,IAAU,WAAW,GAAG,QAAQ,CAAC;AAAA,QAClE;AAAA,MACD;AAEA,SAAG;AACF,cAAM,QAAQ,MAAM,gBAAgB;AACpC,YAAI,MAAM,SAAS,GAAG;AACrB;AAAA,QACD;AAEA,gBAAQ,MAAM,MAAM;AAAA,UACnB,KAAK;AACJ,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD,KAAK;AACJ,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AACC,kBAAM,UAAU,OAAO,MAAM,SAAS,CAAC;AAAA,QACzC;AAAA,MACD,SAAS,UAAU,WAAW,IAAI,UAAU,SAAS;AAErD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,CAAI,CAAC,GAAG;AACjE,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,IAAM,IAAM,GAAM,GAAM,GAAM,CAAI,CAAC,GAAG;AACjE,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QACC,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAC7C,KAAK,MAAM,CAAC,KAAM,KAAM,IAAM,GAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAChD,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAChD,KAAK,MAAM,CAAC,KAAM,KAAM,KAAM,GAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAClD;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,EAAI,CAAC,GAAG;AACvE,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,WAAW,GAAG;AAClC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,GAAM,IAAM,GAAM,GAAM,GAAM,KAAM,KAAM,KAAM,GAAI,CAAC,GAAG;AACzF,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAM,KAAM,GAAI,CAAC,GAAG;AAC7E,qBAAe,aAAa;AAC3B,cAAM,OAAO,IAAI,WAAW,EAAE;AAC9B,cAAM,UAAU,WAAW,IAAI;AAC/B,eAAO;AAAA,UACN,IAAI;AAAA,UACJ,MAAM,OAAO,MAAM,UAAU,UAAgB,SAAS,CAAC;AAAA,QACxD;AAAA,MACD;AAEA,YAAM,UAAU,OAAO,EAAE;AAEzB,aAAO,UAAU,WAAW,KAAK,UAAU,SAAS,MAAM;AACzD,cAAM,SAAS,MAAM,WAAW;AAChC,YAAI,UAAU,OAAO,OAAO;AAC5B,YAAI,OAAO,OAAO,IAAI,CAAC,KAAM,GAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAM,KAAM,KAAM,GAAM,KAAM,IAAM,IAAM,IAAM,GAAI,CAAC,GAAG;AAExH,gBAAM,SAAS,IAAI,WAAW,EAAE;AAChC,qBAAW,MAAM,UAAU,WAAW,MAAM;AAE5C,cAAI,OAAO,QAAQ,CAAC,IAAM,KAAM,KAAM,KAAM,IAAM,IAAM,KAAM,IAAM,KAAM,KAAM,GAAM,KAAM,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AAErH,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAEA,cAAI,OAAO,QAAQ,CAAC,KAAM,KAAM,IAAM,KAAM,IAAM,IAAM,KAAM,IAAM,KAAM,KAAM,GAAM,KAAM,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AAErH,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAEA;AAAA,QACD;AAEA,cAAM,UAAU,OAAO,OAAO;AAAA,MAC/B;AAGA,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KAAM,IAAM,IAAM,IAAM,EAAI,CAAC,GAAG;AACzF,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,SAAK,KAAK,MAAM,CAAC,KAAM,IAAM,CAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAM,IAAM,CAAI,CAAC,MAAM,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AAC5H,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AACtG,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,IAAM,KAAM,EAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,IAAM,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KAAM,EAAI,CAAC,GAAG;AAGzF,YAAM,UAAU,OAAO,EAAE;AACzB,YAAM,OAAO,MAAM,UAAU,UAAU,IAAU,WAAW,GAAG,OAAO,CAAC;AACvE,cAAQ,MAAM;AAAA,QACb,KAAK;AACJ,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD,KAAK;AACJ,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD,KAAK;AACJ,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD,KAAK;AACJ,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD;AACC;AAAA,MACF;AAAA,IACD;AAEA,QACC,KAAK,MAAM,CAAC,KAAM,EAAI,CAAC,KACpB,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KAAM,EAAI,CAAC,GACrF;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,GAAI,CAAC,GAAG;AAC7B,UAAI,KAAK,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AACpE,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAIA,QACC,KAAK,MAAM,CAAC,GAAK,GAAK,GAAK,GAAI,CAAC,KAC7B,KAAK,MAAM,CAAC,GAAK,GAAK,GAAK,GAAI,CAAC,GAClC;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,GAAM,CAAI,CAAC,GAAG;AAC/C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,CAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,CAAI,CAAC,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,KAAM,IAAM,KAAM,KAAM,KAAM,IAAM,GAAI,CAAC,GAAG;AAEjE,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,UAAM,UAAU,WAAW,KAAK,QAAQ,EAAC,QAAQ,KAAK,IAAI,KAAK,UAAU,SAAS,IAAI,GAAG,WAAW,KAAI,CAAC;AAEzG,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,KAAM,GAAI,GAAG,EAAC,QAAQ,GAAE,CAAC,GAAG;AACvD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,KAAK,KAAK,YAAY,MAAM,EAAC,QAAQ,GAAE,CAAC,GAAG;AACnF,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAIA,QAAI,KAAK,YAAY,QAAQ,GAAG;AAC/B,UAAI,KAAK,YAAY,SAAS,EAAC,QAAQ,EAAC,CAAC,GAAG;AAC3C,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,UAAI,KAAK,YAAY,aAAa,EAAC,QAAQ,EAAC,CAAC,GAAG;AAC/C,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAGA,QAAI,KAAK,YAAY,iBAAiB,GAAG;AACxC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,kBAAkB,GAAG;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,qBAAqB,GAAG;AAC5C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,GAAM,CAAI,CAAC,KAAK,KAAK,OAAO,UAAU,IAAI;AACrE,YAAM,WAAW,IAAI,SAAS,KAAK,OAAO,MAAM,EAAE,UAAU,IAAI,IAAI;AAEpE,UAAI,WAAW,MAAM,KAAK,OAAO,UAAU,WAAW,IAAI;AACzD,YAAI;AACH,gBAAM,SAAS,IAAI,YAAY,EAAE,OAAO,KAAK,OAAO,MAAM,IAAI,WAAW,EAAE,CAAC;AAC5E,gBAAM,OAAO,KAAK,MAAM,MAAM;AAE9B,cAAI,KAAK,OAAO;AACf,mBAAO;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,YACP;AAAA,UACD;AAAA,QACD,QAAQ;AAAA,QAAC;AAAA,MACV;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,GAAM,GAAM,CAAI,CAAC,GAAG;AACrG,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,QAAQ,EAAC,QAAQ,GAAE,CAAC,GAAG;AAC3C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,EAAI,CAAC,KAAK,KAAK,MAAM,CAAC,EAAI,GAAG,EAAC,QAAQ,IAAG,CAAC,GAAG;AAC5D,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,MAAM,CAAC,EAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAAK,KAAK,MAAM,CAAC,EAAI,GAAG,EAAC,QAAQ,IAAG,CAAC,GAAG;AACzE,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,EAAI,GAAG,EAAC,QAAQ,GAAE,CAAC,GAAG;AAC/E,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,IAAM,IAAM,EAAI,GAAG,EAAC,QAAQ,IAAG,CAAC,GAAG;AACxD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,KAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,EAAI,CAAC,GAAG;AACzI,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,IAAM,KAAM,KAAM,KAAM,GAAM,GAAM,GAAM,GAAM,KAAM,IAAM,KAAM,KAAM,GAAM,GAAM,GAAM,CAAI,CAAC,GAAG;AACjH,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,YAAY,wBAA4B,GAAG;AACnD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA;AAAA,MACP;AAAA,IACD;AAEA,QACC,KAAK,MAAM,CAAC,IAAM,EAAI,GAAG,EAAC,QAAQ,GAAE,CAAC,MAEpC,KAAK,MAAM,CAAC,GAAM,GAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KACvC,KAAK,MAAM,CAAC,GAAM,GAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAC1C,KAAK,MAAM,CAAC,GAAM,GAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,IAE7C;AACD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,GAAM,GAAM,KAAM,KAAM,KAAM,IAAM,IAAM,KAAM,KAAM,IAAM,KAAM,KAAM,KAAM,KAAM,KAAM,EAAI,CAAC,GAAG;AACjH,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,UAAM,UAAU,WAAW,KAAK,QAAQ,EAAC,QAAQ,KAAK,IAAI,KAAK,UAAU,SAAS,IAAI,GAAG,WAAW,KAAI,CAAC;AAGzG,QAAI,yBAAyB,KAAK,MAAM,GAAG;AAC1C,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,KAAK,MAAM,CAAC,KAAM,GAAI,CAAC,GAAG;AAC7B,UAAI,KAAK,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AACpE,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,UAAI,KAAK,MAAM,CAAC,KAAM,IAAM,IAAM,GAAM,KAAM,GAAM,KAAM,GAAM,KAAM,GAAM,IAAM,GAAM,KAAM,GAAM,IAAM,GAAM,KAAM,GAAM,IAAM,GAAM,IAAM,GAAM,KAAM,GAAM,KAAM,GAAM,KAAM,GAAM,KAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,GAAG;AAClN,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,YAAY,6BAA6B,GAAG;AACpD,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,KAAK,OAAO,UAAU,KAAK,KAAK,MAAM,CAAC,KAAM,GAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,KAAM,GAAI,EAAC,CAAC,GAAG;AACzF,UAAI,KAAK,MAAM,CAAC,EAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,EAAI,EAAC,CAAC,GAAG;AAElD,YAAI,KAAK,MAAM,CAAC,CAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,CAAI,EAAC,CAAC,GAAG;AAClD,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD;AAGA,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAIA,UAAI,KAAK,MAAM,CAAC,CAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,CAAI,EAAC,CAAC,GAAG;AAClD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,KAAK,MAAM,CAAC,CAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,CAAI,EAAC,CAAC,GAAG;AAClD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAGA,UAAI,KAAK,MAAM,CAAC,CAAI,GAAG,EAAC,QAAQ,GAAG,MAAM,CAAC,CAAI,EAAC,CAAC,GAAG;AAClD,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,YAAY,WAAW;AAC5B,UAAM,QAAQ,MAAM,KAAK,UAAU,UAAU,YAAkB,YAAkB,SAAS;AAC1F,SAAK,UAAU,OAAO,EAAE;AACxB,YAAQ,OAAO;AAAA,MACd,KAAK;AACJ,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD,KAAK;AACJ,eAAO;AAAA,UACN,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,YAAY,WAAW;AAC5B,UAAM,eAAe,MAAM,KAAK,UAAU,UAAU,YAAkB,YAAkB,SAAS;AACjG,aAAS,IAAI,GAAG,IAAI,cAAc,EAAE,GAAG;AACtC,YAAM,WAAW,MAAM,KAAK,YAAY,SAAS;AACjD,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,eAAe,WAAW;AAC/B,UAAM,WAAW,YAAkB,YAAkB,WAAW,IAAI,KAAK,QAAQ,CAAC;AAClF,UAAM,aAAa,YAAkB,YAAkB,WAAW,IAAI,KAAK,QAAQ,CAAC;AAEpF,QAAI,YAAY,IAAI;AAEnB,UAAI,aAAa,GAAG;AACnB,YAAI,KAAK,YAAY,MAAM,EAAC,QAAQ,EAAC,CAAC,GAAG;AACxC,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD;AAEA,YAAI,aAAa,MAAM,KAAK,MAAM,CAAC,IAAM,GAAM,KAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,KAAK,KAAK,MAAM,CAAC,IAAM,GAAM,IAAM,CAAI,GAAG,EAAC,QAAQ,EAAC,CAAC,IAAI;AAC/H,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,QACD;AAAA,MACD;AAEA,YAAM,KAAK,UAAU,OAAO,SAAS;AACrC,YAAM,WAAW,MAAM,KAAK,YAAY,SAAS;AACjD,aAAO,YAAY;AAAA,QAClB,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAEA,QAAI,YAAY,IAAI;AACnB,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACD;AAEO,IAAM,sBAAsB,IAAI,IAAI,UAAU;AAC9C,IAAM,qBAAqB,IAAI,IAAI,SAAS;", "names": ["maxBufferSize"]}