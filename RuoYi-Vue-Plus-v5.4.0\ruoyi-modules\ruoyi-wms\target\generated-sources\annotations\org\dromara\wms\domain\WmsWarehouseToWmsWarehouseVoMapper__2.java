package org.dromara.wms.domain;

import io.github.linpeilie.AutoMapperConfig__1039;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.bo.WmsWarehouseBoToWmsWarehouseMapper__4;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.vo.WmsWarehouseVoToWmsWarehouseMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1039.class,
    uses = {WmsWarehouseBoToWmsWarehouseMapper__4.class,WmsWarehouseVoToWmsWarehouseMapper__2.class},
    imports = {}
)
public interface WmsWarehouseToWmsWarehouseVoMapper__2 extends BaseMapper<WmsWarehouse, WmsWarehouseVo> {
}
