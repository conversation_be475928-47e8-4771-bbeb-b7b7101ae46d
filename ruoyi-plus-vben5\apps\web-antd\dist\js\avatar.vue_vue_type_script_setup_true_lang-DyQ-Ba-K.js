import{d as i,h as p,o as l,w as c,r as h,n,b as t,G as y,e as _,g,O as b,B as u,c as f,H as m,a as d,f as z,k as x,t as C}from"../jse/index-index-C-MnMZEz.js";import{cc as k,cd as w,ce as B,cf as S}from"./bootstrap-DCMzVRvD.js";const $=k("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{shape:{circle:"rounded-full",square:"rounded-md"},size:{base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl",sm:"h-10 w-10 text-xs"}}}),j=i({__name:"Avatar",props:{class:{},shape:{default:"circle"},size:{default:"sm"}},setup(a){const e=a;return(s,o)=>(l(),p(t(w),{class:n(t(y)(t($)({size:s.size,shape:s.shape}),e.class))},{default:c(()=>[h(s.$slots,"default")]),_:3},8,["class"]))}}),F=i({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(a){const e=a;return(s,o)=>(l(),p(t(B),_(g(e)),{default:c(()=>[h(s.$slots,"default")]),_:3},16))}}),P=i({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(a){const e=a;return(s,o)=>(l(),p(t(S),b(e,{class:"h-full w-full object-cover"}),null,16))}}),N=i({inheritAttrs:!1,__name:"avatar",props:{alt:{default:"avatar"},class:{},dot:{type:Boolean,default:!1},dotClass:{default:"bg-green-500"},fit:{default:"cover"},size:{},delayMs:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"button"},src:{},referrerPolicy:{}},setup(a){const e=a,s=u(()=>{const{fit:r}=e;return r?{objectFit:r}:{}}),o=u(()=>e.alt.slice(-2).toUpperCase()),v=u(()=>e.size!==void 0&&e.size>0?{height:`${e.size}px`,width:`${e.size}px`}:{});return(r,A)=>(l(),f("div",{class:n([e.class,"relative flex flex-shrink-0 items-center"]),style:m(v.value)},[d(t(j),{class:n([e.class,"size-full"])},{default:c(()=>[d(t(P),{alt:r.alt,src:r.src,style:m(s.value)},null,8,["alt","src","style"]),d(t(F),null,{default:c(()=>[x(C(o.value),1)]),_:1})]),_:1},8,["class"]),r.dot?(l(),f("span",{key:0,class:n([r.dotClass,"border-background absolute bottom-0 right-0 size-3 rounded-full border-2"])},null,2)):z("",!0)],6))}});export{N as _};
