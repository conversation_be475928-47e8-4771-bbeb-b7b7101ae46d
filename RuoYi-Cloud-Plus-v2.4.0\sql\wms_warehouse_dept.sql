-- 创建仓库部门关联表
CREATE TABLE `wms_warehouse_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `tenant_id` varchar(20) NOT NULL DEFAULT '000000' COMMENT '租户编号',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_warehouse_dept_tenant` (`warehouse_id`, `dept_id`, `tenant_id`) COMMENT '仓库部门租户唯一索引',
  KEY `idx_warehouse_id` (`warehouse_id`) COMMENT '仓库ID索引',
  KEY `idx_dept_id` (`dept_id`) COMMENT '部门ID索引',
  KEY `idx_tenant_id` (`tenant_id`) COMMENT '租户ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='仓库部门关联表';

-- 示例数据（假设仓库ID为1的仓库关联到部门ID为100和101的部门）
-- INSERT INTO `wms_warehouse_dept` (`warehouse_id`, `dept_id`) VALUES (1, 100);
-- INSERT INTO `wms_warehouse_dept` (`warehouse_id`, `dept_id`) VALUES (1, 101);
