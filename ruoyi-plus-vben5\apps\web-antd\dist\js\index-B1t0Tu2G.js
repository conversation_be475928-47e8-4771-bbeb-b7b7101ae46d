var j=Object.defineProperty;var S=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var I=(s,n,t)=>n in s?j(s,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[n]=t,A=(s,n)=>{for(var t in n||(n={}))M.call(n,t)&&I(s,t,n[t]);if(S)for(var t of S(n))G.call(n,t)&&I(s,t,n[t]);return s};var f=(s,n,t)=>new Promise((v,d)=>{var $=l=>{try{h(t.next(l))}catch(k){d(k)}},u=l=>{try{h(t.throw(l))}catch(k){d(k)}},h=l=>l.done?v(l.value):Promise.resolve(l.value).then($,u);h((t=t.apply(s,n)).next())});import{at as U,$ as g,as as W,T as Y}from"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import{d as z,e as H,f as J}from"./index-C5dPwGGG.js";import{q as K,c as Q,_ as X}from"./menu-drawer.vue_vue_type_script_setup_true_lang-nFhHe5XJ.js";import V from"./index-BeyziwLP.js";import{_ as Z}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{_ as ee}from"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import{t as B,e as te}from"./tree-DFBawhPd.js";import{d as oe,p as ne,B as ae,l as N,S as se,h as C,o as _,w as i,a as p,b as a,T,f as re,k as x,t as y,c as ie,j as me}from"../jse/index-index-C-MnMZEz.js";import{u as de}from"./use-vxe-grid-BC7vZzEr.js";import{u as le}from"./use-drawer-6qcpK-D1.js";import{P as ce}from"./index-DNdMANjv.js";import{g as pe}from"./get-popup-container-P4S1sr5h.js";import ue from"./index-D-hwdOI6.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./popup-D6rC6QBG.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./render-BxXtQdeV.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./rotate-cw-DzZTu9nW.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";function R(s,n){return f(this,null,function*(){const t=s.grid.getScroll(),v=s.grid.getTreeExpandRecords();yield n(),s.grid.setTreeExpand(v,!0),s.grid.scrollTo(t.scrollLeft,t.scrollTop)})}const fe={class:"flex items-center"},He=oe({__name:"index",setup(s){const n={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:K(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},t={columns:Q,height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:(r,...w)=>f(null,[r,...w],function*(o,e={}){return{rows:yield z(A({},e))}})}},rowConfig:{keyField:"menuId"},scrollY:{enabled:!0,gt:0},treeConfig:{parentField:"parentId",rowField:"menuId",transform:!0},id:"system-menu-index"},[v,d]=de({formOptions:n,gridOptions:t,gridEvents:{cellDblclick:o=>{const{row:e={}}=o;if(!(e!=null&&e.children))return;const r=e==null?void 0:e.expand;d.grid.setTreeExpand(e,!r),e.expand=!r},toggleTreeExpand:o=>{const{row:e={},expanded:r}=o;e.expand=r}}}),[$,u]=le({connectedComponent:X});function h(){u.setData({}),u.open()}function l(o){const{menuId:e}=o;u.setData({id:e,update:!1}),u.open()}function k(o){return f(this,null,function*(){u.setData({id:o.menuId,update:!0}),u.open()})}const b=ne(!1);function q(o){return f(this,null,function*(){yield R(d,()=>f(null,null,function*(){if(b.value){const e=B([o],{id:"menuId"});yield H(e.map(r=>r.menuId))}else yield J([o.menuId]);yield d.query()}))})}function F(o){const e=g(o.menuName);if(!b.value)return`是否确认删除 [${e}] ?`;const r=B([o],{id:"menuId"});return r.length===1?`是否确认删除 [${e}] ?`:`是否确认删除 [${e}] 及 [${r.length-1}]个子项目 ?`}function O(){return f(this,null,function*(){yield R(d,()=>d.query())})}function D(o){var e;te(d.grid.getData(),r=>r.expand=o),(e=d.grid)==null||e.setAllTreeExpand(o)}const{hasAccessByRoles:P}=U(),L=ae(()=>P(["admin","superadmin"]));return(o,e)=>{const r=N("a-button"),w=N("ghost-button"),m=se("access");return L.value?(_(),C(a(Z),{key:0,"auto-content-height":!0},{default:i(()=>[p(a(v),{"table-title":"菜单列表","table-title-help":"双击展开/收起子菜单"},{"toolbar-tools":i(()=>[p(a(V),null,{default:i(()=>[p(a(Y),{title:"删除菜单以及子菜单"},{default:i(()=>[T((_(),ie("div",fe,[e[4]||(e[4]=me("span",{class:"mr-2 text-sm text-[#666666]"},"级联删除",-1)),p(a(ue),{checked:b.value,"onUpdate:checked":e[0]||(e[0]=c=>b.value=c)},null,8,["checked"])])),[[m,["superadmin"],"role"],[m,["system:menu:remove"],"code"]])]),_:1}),p(r,{onClick:e[1]||(e[1]=c=>D(!1))},{default:i(()=>[x(y(a(g)("pages.common.collapse")),1)]),_:1}),p(r,{onClick:e[2]||(e[2]=c=>D(!0))},{default:i(()=>[x(y(a(g)("pages.common.expand")),1)]),_:1}),T((_(),C(r,{type:"primary",onClick:h},{default:i(()=>[x(y(a(g)("pages.common.add")),1)]),_:1})),[[m,["system:menu:add"],"code"],[m,["superadmin"],"role"]])]),_:1})]),action:i(({row:c})=>[p(a(V),null,{default:i(()=>[T((_(),C(w,{onClick:E=>k(c)},{default:i(()=>[x(y(a(g)("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[m,["system:menu:edit"],"code"],[m,["superadmin"],"role"]]),c.menuType!=="F"?T((_(),C(w,{key:0,class:"btn-success",onClick:E=>l(c)},{default:i(()=>[x(y(a(g)("pages.common.add")),1)]),_:2},1032,["onClick"])),[[m,["system:menu:add"],"code"],[m,["superadmin"],"role"]]):re("",!0),p(a(ce),{"get-popup-container":a(pe),placement:"left",title:F(c),onConfirm:E=>q(c)},{default:i(()=>[T((_(),C(w,{danger:"",onClick:e[3]||(e[3]=W(()=>{},["stop"]))},{default:i(()=>[x(y(a(g)("pages.common.delete")),1)]),_:1})),[[m,["system:menu:remove"],"code"],[m,["superadmin"],"role"]])]),_:2},1032,["get-popup-container","title","onConfirm"])]),_:2},1024)]),_:1}),p(a($),{onReload:O})]),_:1})):(_(),C(a(ee),{key:1,description:"您没有菜单管理的访问权限",status:"403"}))}}});export{He as default};
