2025-06-13 08:09:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 08:09:56 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.14 with PID 14828 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 08:09:56 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-13 08:10:01 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-13 08:10:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-13 08:10:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-13 08:10:02 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@b21cbbf
2025-06-13 08:10:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-13 08:10:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-13 08:10:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-13 08:10:04 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-13 08:10:04 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-13 08:10:04 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-13 08:10:04 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-13 08:10:05 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-13 08:10:05 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 08:20:37 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-13 08:20:41 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-13 08:20:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-13 08:20:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-13 08:20:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ad80206
2025-06-13 08:20:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-13 08:20:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-13 08:20:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-13 08:20:43 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-13 08:20:43 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-13 08:20:43 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-13 08:20:43 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-13 08:20:44 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-13 08:20:44 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 08:23:03 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-13 08:23:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-13 08:23:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-13 08:23:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-13 08:23:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ad80206
2025-06-13 08:23:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-13 08:23:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-13 08:23:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-13 08:23:08 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-13 08:23:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-13 08:23:09 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-13 08:23:09 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-13 08:23:09 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-13 08:23:09 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\"code\":\"0\",\"message\":\"success\",\"data\":null,\"request_id\":\"20250613084459544B92321B3E123AE009\"}","message":"Task executed successfully"}]
2025-06-13 08:45:10 [snail-job-grpc-client-executor-127.0.0.1-155] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[155]
2025-06-13 09:07:50 [snail-job-grpc-server-executor-2] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:07:50 [snail-grpc-server-2] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[252] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:07:50 [snail-job-job-252-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:07:50 [snail-job-job-252-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[252] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776877106},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"21877\",\"posY\":\"42072\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776877106},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44887\",\"posY\":\"20238\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"802\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776877107},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776877107}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:07:55 [snail-job-grpc-server-executor-4] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:07:55 [snail-grpc-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[253] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:07:55 [snail-job-job-253-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:07:55 [snail-job-job-253-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[253] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776882182},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[21873,42075,0]\",\"[29815,41301,0]\",\"[36233,41301,0]\",\"[36233,41301,-90]\",\"[36233,39024,-90]\",\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"21939\",\"posY\":\"42072\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"97\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776882182},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44886\",\"posY\":\"24154\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"793\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776882183},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776882183}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:07:55 [snail-job-grpc-client-executor-127.0.0.1-317] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[317]
2025-06-13 09:08:00 [snail-job-grpc-server-executor-6] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:00 [snail-grpc-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[254] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:00 [snail-job-job-254-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:00 [snail-job-job-254-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[254] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776887182},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[21873,42075,0]\",\"[29815,41301,0]\",\"[36233,41301,0]\",\"[36233,41301,-90]\",\"[36233,39024,-90]\",\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"26079\",\"posY\":\"41647\",\"robotCode\":\"4192\",\"robotDir\":\"-10\",\"robotIp\":\"************\",\"speed\":\"1207\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776887183},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44898\",\"posY\":\"28243\",\"robotCode\":\"4193\",\"robotDir\":\"91\",\"robotIp\":\"************\",\"speed\":\"803\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776887183},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776887183}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:00 [snail-job-grpc-client-executor-127.0.0.1-320] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[320]
2025-06-13 09:08:05 [snail-job-grpc-server-executor-8] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:05 [snail-grpc-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[255] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:05 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:05 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[255] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776892303},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[29815,41301,0]\",\"[36233,41301,0]\",\"[36233,41301,-90]\",\"[36233,39024,-90]\",\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"31135\",\"posY\":\"41303\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"1197\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776892303},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44870\",\"posY\":\"32475\",\"robotCode\":\"4193\",\"robotDir\":\"89\",\"robotIp\":\"************\",\"speed\":\"807\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776892304},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776892304}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:05 [snail-job-grpc-client-executor-127.0.0.1-322] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[322]
2025-06-13 09:08:10 [snail-job-grpc-server-executor-10] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:10 [snail-grpc-server-6] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[256] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:10 [snail-job-job-256-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:10 [snail-job-job-256-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[256] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776897316},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[29815,41301,0]\",\"[36233,41301,0]\",\"[36233,41301,-90]\",\"[36233,39024,-90]\",\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36009\",\"posY\":\"41319\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"309\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776897317},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44890\",\"posY\":\"36485\",\"robotCode\":\"4193\",\"robotDir\":\"91\",\"robotIp\":\"************\",\"speed\":\"802\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776897317},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776897317}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:10 [snail-job-grpc-client-executor-127.0.0.1-325] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[325]
2025-06-13 09:08:15 [snail-job-grpc-server-executor-12] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:15 [snail-grpc-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[257] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:15 [snail-job-job-257-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:15 [snail-job-job-257-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[257] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776902323},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,41301,0]\",\"[36233,41301,-90]\",\"[36233,39024,-90]\",\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36236\",\"posY\":\"41305\",\"robotCode\":\"4192\",\"robotDir\":\"-20\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776902324},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44895\",\"posY\":\"40407\",\"robotCode\":\"4193\",\"robotDir\":\"91\",\"robotIp\":\"************\",\"speed\":\"791\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776902325},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776902325}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:15 [snail-job-grpc-client-executor-127.0.0.1-327] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[327]
2025-06-13 09:08:20 [snail-job-grpc-server-executor-14] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:20 [snail-grpc-server-8] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[258] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:20 [snail-job-job-258-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:20 [snail-job-job-258-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[258] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776907460},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,41301,-90]\",\"[36233,39024,-90]\",\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36208\",\"posY\":\"41267\",\"robotCode\":\"4192\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"102\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776907461},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44888\",\"posY\":\"44580\",\"robotCode\":\"4193\",\"robotDir\":\"91\",\"robotIp\":\"************\",\"speed\":\"796\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776907461},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776907461}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:25 [snail-job-grpc-server-executor-16] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:25 [snail-grpc-server-9] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[259] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:25 [snail-job-job-259-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:25 [snail-job-job-259-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[259] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776912478},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,37491,-90]\",\"[36233,36006,-90]\",\"[36233,34504,-90]\",\"[36233,33064,-90]\",\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36218\",\"posY\":\"36587\",\"robotCode\":\"4192\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"1194\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776912479},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44894\",\"posY\":\"47927\",\"robotCode\":\"4193\",\"robotDir\":\"91\",\"robotIp\":\"************\",\"speed\":\"808\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776912479},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776912479}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:25 [snail-job-grpc-client-executor-127.0.0.1-332] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[332]
2025-06-13 09:08:30 [snail-job-grpc-server-executor-18] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:30 [snail-grpc-server-10] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[260] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:30 [snail-job-job-260-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:30 [snail-job-job-260-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[260] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776917514},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,31560,-90]\",\"[36233,30059,-90]\",\"[36233,28588,-90]\",\"[36233,27076,-90]\",\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36227\",\"posY\":\"30408\",\"robotCode\":\"4192\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1203\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776917515},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44887\",\"posY\":\"50962\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776917515},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776917515}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:30 [snail-job-grpc-client-executor-127.0.0.1-335] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[335]
2025-06-13 09:08:35 [snail-job-grpc-server-executor-21] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:35 [snail-grpc-server-11] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[261] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:35 [snail-job-job-261-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:35 [snail-job-job-261-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[261] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776922548},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,25533,-90]\",\"[36233,23966,-90]\",\"[36233,22440,-90]\",\"[36233,20986,-90]\",\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36231\",\"posY\":\"24179\",\"robotCode\":\"4192\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1196\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776922548},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44883\",\"posY\":\"54140\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"792\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776922549},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776922549}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:40 [snail-job-grpc-server-executor-23] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:40 [snail-grpc-server-12] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[262] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:40 [snail-job-job-262-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:40 [snail-job-job-262-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[262] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776927424},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,19474,-90]\",\"[36233,17965,-90]\",\"[36233,16479,-90]\",\"[36233,15001,-90]\",\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36234\",\"posY\":\"18594\",\"robotCode\":\"4192\",\"robotDir\":\"-91\",\"robotIp\":\"************\",\"speed\":\"1198\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776927424},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44885\",\"posY\":\"58015\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"801\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776927425},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776927425}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:40 [snail-job-grpc-client-executor-127.0.0.1-339] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[339]
2025-06-13 09:08:45 [snail-job-grpc-server-executor-25] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:45 [snail-grpc-server-13] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[263] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:45 [snail-job-job-263-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:45 [snail-job-job-263-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[263] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776932436},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,13498,-90]\",\"[36233,12033,-90]\",\"[36233,10529,-90]\",\"[36233,9018,-90]\",\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36238\",\"posY\":\"12455\",\"robotCode\":\"4192\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1195\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776932436},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44881\",\"posY\":\"62128\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"789\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776932436},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776932436}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:45 [snail-job-grpc-client-executor-127.0.0.1-341] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[341]
2025-06-13 09:08:50 [snail-job-grpc-server-executor-27] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:50 [snail-grpc-server-14] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[264] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:50 [snail-job-job-264-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:50 [snail-job-job-264-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[264] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776937455},{\"battery\":\"70\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,7522,-90]\",\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36236\",\"posY\":\"6760\",\"robotCode\":\"4192\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"645\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776937455},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,65224,90]\",\"[44879,66689,90]\",\"[44879,68181,90]\",\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44893\",\"posY\":\"66113\",\"robotCode\":\"4193\",\"robotDir\":\"91\",\"robotIp\":\"************\",\"speed\":\"808\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776937455},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776937455}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:50 [snail-job-grpc-client-executor-127.0.0.1-344] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[344]
2025-06-13 09:08:55 [snail-job-grpc-server-executor-29] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:08:55 [snail-grpc-server-15] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[265] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:08:55 [snail-job-job-265-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:08:55 [snail-job-job-265-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[265] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776941975},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36232\",\"posY\":\"6308\",\"robotCode\":\"4192\",\"robotDir\":\"-86\",\"robotIp\":\"************\",\"speed\":\"290\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776941975},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,69688,90]\",\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44881\",\"posY\":\"69820\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"727\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776941976},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776941976}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:08:55 [snail-job-grpc-client-executor-127.0.0.1-346] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[346]
2025-06-13 09:09:00 [snail-job-grpc-server-executor-31] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:00 [snail-grpc-server-16] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[266] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:00 [snail-job-job-266-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:00 [snail-job-job-266-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[266] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776947148},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,6091,-90]\",\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35538\",\"posY\":\"6746\",\"robotCode\":\"4192\",\"robotDir\":\"2\",\"robotIp\":\"************\",\"speed\":\"90\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776947149},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44876\",\"posY\":\"71110\",\"robotCode\":\"4193\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"100\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776947149},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776947149}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:00 [snail-job-grpc-client-executor-127.0.0.1-349] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[349]
2025-06-13 09:09:05 [snail-job-grpc-server-executor-33] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:1ms
2025-06-13 09:09:05 [snail-grpc-server-1] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[267] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:05 [snail-job-job-267-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:05 [snail-job-job-267-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[267] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776952096},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"35863\",\"posY\":\"6754\",\"robotCode\":\"4192\",\"robotDir\":\"8\",\"robotIp\":\"************\",\"speed\":\"239\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776952097},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,71164,90]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44583\",\"posY\":\"70324\",\"robotCode\":\"4193\",\"robotDir\":\"8\",\"robotIp\":\"************\",\"speed\":\"213\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776952097},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776952097}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:05 [snail-job-grpc-client-executor-127.0.0.1-351] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[351]
2025-06-13 09:09:10 [snail-job-grpc-server-executor-35] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:10 [snail-grpc-server-2] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[268] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:10 [snail-job-job-268-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:10 [snail-job-job-268-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[268] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776957095},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[35520,6719,0]\",\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36191\",\"posY\":\"7521\",\"robotCode\":\"4192\",\"robotDir\":\"87\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776957095},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44300,70328,0]\",\"[42970,70328,0]\",\"[42904,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44300\",\"posY\":\"70311\",\"robotCode\":\"4193\",\"robotDir\":\"-2\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749776957095},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776957095}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:15 [snail-job-grpc-server-executor-37] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:15 [snail-grpc-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[269] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:15 [snail-job-job-269-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:15 [snail-job-job-269-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[269] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776962115},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,7522,90]\",\"[36233,6091,90]\",\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36208\",\"posY\":\"7079\",\"robotCode\":\"4192\",\"robotDir\":\"92\",\"robotIp\":\"************\",\"speed\":\"297\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776962115},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44300,70328,0]\",\"[42970,70328,0]\",\"[42904,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"43417\",\"posY\":\"70324\",\"robotCode\":\"4193\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"200\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749776962115},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776962115}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:15 [snail-job-grpc-client-executor-127.0.0.1-355] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[355]
2025-06-13 09:09:20 [snail-job-grpc-server-executor-39] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:20 [snail-grpc-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[270] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:20 [snail-job-job-270-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:20 [snail-job-job-270-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[270] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776967171},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5582,90]\",\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36242\",\"posY\":\"5608\",\"robotCode\":\"4192\",\"robotDir\":\"89\",\"robotIp\":\"************\",\"speed\":\"298\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776967171},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[42970,70328,0]\",\"[42904,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"42903\",\"posY\":\"70322\",\"robotCode\":\"4193\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749776967171},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776967171}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:20 [snail-job-grpc-client-executor-127.0.0.1-359] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[359]
2025-06-13 09:09:25 [snail-job-grpc-server-executor-41] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:25 [snail-grpc-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[271] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:25 [snail-job-job-271-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:25 [snail-job-job-271-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[271] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776972146},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,90]\",\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36249\",\"posY\":\"5125\",\"robotCode\":\"4192\",\"robotDir\":\"76\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776972147},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"42903\",\"posY\":\"70322\",\"robotCode\":\"4193\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"152\",\"stop\":\"0\",\"timestamp\":1749776972147},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776972147}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:25 [snail-job-grpc-client-executor-127.0.0.1-361] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[361]
2025-06-13 09:09:30 [snail-job-grpc-server-executor-44] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:30 [snail-grpc-server-6] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[272] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:30 [snail-job-job-272-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:30 [snail-job-job-272-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[272] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776977585},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36245\",\"posY\":\"5086\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"139\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776977585},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[42970,70328,0]\",\"[44300,70328,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"43710\",\"posY\":\"70312\",\"robotCode\":\"4193\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"197\",\"status\":\"151\",\"stop\":\"0\",\"timestamp\":1749776977585},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776977586}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:30 [snail-job-grpc-client-executor-127.0.0.1-364] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[364]
2025-06-13 09:09:35 [snail-job-grpc-server-executor-46] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:35 [snail-grpc-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[273] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:35 [snail-job-job-273-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:35 [snail-job-job-273-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[273] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776982597},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"34780\",\"posY\":\"5113\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"299\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776982598},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44300,70328,0]\",\"[44879,69688,-90]\",\"[44879,68181,-90]\",\"[44879,66689,-90]\",\"[44879,65224,-90]\",\"[44879,63724,-90]\",\"[44879,62224,-90]\",\"[44879,60766,-90]\",\"[44879,59233,-90]\",\"[44879,57781,-90]\",\"[44879,56269,-90]\",\"[44879,54787,-90]\",\"[44879,53324,-90]\",\"[44879,51764,-90]\",\"[44879,50339,-90]\",\"[44879,48744,-90]\",\"[44879,47319,-90]\",\"[44879,45878,-90]\",\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44346\",\"posY\":\"70309\",\"robotCode\":\"4193\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"200\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776982598},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776982598}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:40 [snail-job-grpc-server-executor-48] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:40 [snail-grpc-server-8] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[274] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:40 [snail-job-job-274-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:40 [snail-job-job-274-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[274] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776987583},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"33339\",\"posY\":\"5107\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"293\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776987583},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44300,70328,0]\",\"[44879,69688,-90]\",\"[44879,68181,-90]\",\"[44879,66689,-90]\",\"[44879,65224,-90]\",\"[44879,63724,-90]\",\"[44879,62224,-90]\",\"[44879,60766,-90]\",\"[44879,59233,-90]\",\"[44879,57781,-90]\",\"[44879,56269,-90]\",\"[44879,54787,-90]\",\"[44879,53324,-90]\",\"[44879,51764,-90]\",\"[44879,50339,-90]\",\"[44879,48744,-90]\",\"[44879,47319,-90]\",\"[44879,45878,-90]\",\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44863\",\"posY\":\"69671\",\"robotCode\":\"4193\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"573\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776987584},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776987584}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:40 [snail-job-grpc-client-executor-127.0.0.1-368] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[368]
2025-06-13 09:09:45 [snail-job-grpc-server-executor-50] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:45 [snail-grpc-server-9] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[275] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:45 [snail-job-job-275-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:45 [snail-job-job-275-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[275] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776992607},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[33063,5125,0]\",\"[30826,5125,0]\",\"[30760,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"33044\",\"posY\":\"5109\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"124\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776992607},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,65224,-90]\",\"[44879,63724,-90]\",\"[44879,62224,-90]\",\"[44879,60766,-90]\",\"[44879,59233,-90]\",\"[44879,57781,-90]\",\"[44879,56269,-90]\",\"[44879,54787,-90]\",\"[44879,53324,-90]\",\"[44879,51764,-90]\",\"[44879,50339,-90]\",\"[44879,48744,-90]\",\"[44879,47319,-90]\",\"[44879,45878,-90]\",\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44871\",\"posY\":\"64070\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1186\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776992608},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776992608}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:45 [snail-job-grpc-client-executor-127.0.0.1-370] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[370]
2025-06-13 09:09:50 [snail-job-grpc-server-executor-52] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:50 [snail-grpc-server-10] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[276] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:50 [snail-job-job-276-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:50 [snail-job-job-276-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[276] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749776997624},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[33063,5125,0]\",\"[30826,5125,0]\",\"[30760,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"31519\",\"posY\":\"5130\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"298\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776997625},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,59233,-90]\",\"[44879,57781,-90]\",\"[44879,56269,-90]\",\"[44879,54787,-90]\",\"[44879,53324,-90]\",\"[44879,51764,-90]\",\"[44879,50339,-90]\",\"[44879,48744,-90]\",\"[44879,47319,-90]\",\"[44879,45878,-90]\",\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44872\",\"posY\":\"58013\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1190\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749776997625},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749776997625}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:50 [snail-job-grpc-client-executor-127.0.0.1-373] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[373]
2025-06-13 09:09:55 [snail-job-grpc-server-executor-54] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:09:55 [snail-grpc-server-11] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[277] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:09:55 [snail-job-job-277-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:09:55 [snail-job-job-277-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[277] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777002160},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[30826,5125,0]\",\"[30760,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"30781\",\"posY\":\"5121\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"49\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777002160},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,53324,-90]\",\"[44879,51764,-90]\",\"[44879,50339,-90]\",\"[44879,48744,-90]\",\"[44879,47319,-90]\",\"[44879,45878,-90]\",\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44879\",\"posY\":\"52647\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"1199\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777002161},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777002161}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:09:55 [snail-job-grpc-client-executor-127.0.0.1-375] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[375]
2025-06-13 09:10:00 [snail-job-grpc-server-executor-56] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:00 [snail-grpc-server-12] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[278] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:00 [snail-job-job-278-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:00 [snail-job-job-278-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[278] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777007127},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[30826,5125,0]\",\"[30760,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"30716\",\"posY\":\"5117\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"6\",\"stop\":\"0\",\"timestamp\":1749777007127},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,47319,-90]\",\"[44879,45878,-90]\",\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44870\",\"posY\":\"46623\",\"robotCode\":\"4193\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"1205\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777007128},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777007128}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:05 [snail-job-grpc-server-executor-58] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:05 [snail-grpc-server-13] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[279] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:05 [snail-job-job-279-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:05 [snail-job-job-279-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[279] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777012148},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[30826,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"31105\",\"posY\":\"5117\",\"robotCode\":\"4192\",\"robotDir\":\"1\",\"robotIp\":\"************\",\"speed\":\"299\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777012148},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,44140,-90]\",\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44879\",\"posY\":\"42676\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777012149},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777012149}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:05 [snail-job-grpc-client-executor-127.0.0.1-379] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[379]
2025-06-13 09:10:10 [snail-job-grpc-server-executor-60] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:10 [snail-grpc-server-14] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[280] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:10 [snail-job-job-280-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:10 [snail-job-job-280-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[280] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777017264},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[30826,5125,0]\",\"[33063,5125,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"32701\",\"posY\":\"5123\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"298\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777017264},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,42681,-90]\",\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44879\",\"posY\":\"42674\",\"robotCode\":\"4193\",\"robotDir\":\"-160\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777017264},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777017264}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:10 [snail-job-grpc-client-executor-127.0.0.1-382] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[382]
2025-06-13 09:10:15 [snail-job-grpc-server-executor-62] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:15 [snail-grpc-server-15] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[281] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:15 [snail-job-job-281-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:15 [snail-job-job-281-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[281] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777022351},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[33063,5125,0]\",\"[36233,5125,0]\",\"[44879,5113,0]\",\"[44879,5113,90]\",\"[44879,5673,90]\",\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"33068\",\"posY\":\"5128\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"45\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777022352},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,42681,180]\",\"[43503,42681,180]\",\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"43832\",\"posY\":\"42681\",\"robotCode\":\"4193\",\"robotDir\":\"180\",\"robotIp\":\"************\",\"speed\":\"938\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777022352},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777022353}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:15 [snail-job-grpc-client-executor-127.0.0.1-384] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[384]
2025-06-13 09:10:20 [snail-job-grpc-server-executor-64] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:20 [snail-grpc-server-16] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[282] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:20 [snail-job-job-282-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:20 [snail-job-job-282-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[282] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777027351},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,0]\",\"[44879,5113,0]\",\"[44879,5113,90]\",\"[44879,5673,90]\",\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"36385\",\"posY\":\"5123\",\"robotCode\":\"4192\",\"robotDir\":\"0\",\"robotIp\":\"************\",\"speed\":\"799\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777027351},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[37640,42681,180]\",\"[36233,42681,180]\",\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"37735\",\"posY\":\"42676\",\"robotCode\":\"4193\",\"robotDir\":\"180\",\"robotIp\":\"************\",\"speed\":\"1200\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777027351},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777027352}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:20 [snail-job-grpc-client-executor-127.0.0.1-388] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[388]
2025-06-13 09:10:25 [snail-job-grpc-server-executor-66] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:25 [snail-grpc-server-1] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[283] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:25 [snail-job-job-283-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:25 [snail-job-job-283-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[283] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777032402},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,0]\",\"[44879,5113,0]\",\"[44879,5113,90]\",\"[44879,5673,90]\",\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"40545\",\"posY\":\"5129\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"797\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777032402},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[34643,42681,180]\",\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"31599\",\"posY\":\"42690\",\"robotCode\":\"4193\",\"robotDir\":\"180\",\"robotIp\":\"************\",\"speed\":\"1196\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777032403},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777032403}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:30 [snail-job-grpc-server-executor-68] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:30 [snail-grpc-server-2] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[284] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:30 [snail-job-job-284-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:30 [snail-job-job-284-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[284] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777037414},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[36233,5125,0]\",\"[44879,5113,0]\",\"[44879,5113,90]\",\"[44879,5673,90]\",\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44168\",\"posY\":\"5131\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"499\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777037415},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"25873\",\"posY\":\"42453\",\"robotCode\":\"4193\",\"robotDir\":\"-158\",\"robotIp\":\"************\",\"speed\":\"914\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777037415},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777037415}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:30 [snail-job-grpc-client-executor-127.0.0.1-392] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[392]
2025-06-13 09:10:35 [snail-job-grpc-server-executor-70] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:35 [snail-grpc-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[285] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:35 [snail-job-job-285-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:35 [snail-job-job-285-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[285] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777042406},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,5113,0]\",\"[44879,5113,90]\",\"[44879,5673,90]\",\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44881\",\"posY\":\"5125\",\"robotCode\":\"4192\",\"robotDir\":\"-1\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777042406},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[27883,42681,180]\",\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24750\",\"posY\":\"40324\",\"robotCode\":\"4193\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"27\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777042407},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777042407}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:35 [snail-job-grpc-client-executor-127.0.0.1-394] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[394]
2025-06-13 09:10:40 [snail-job-grpc-server-executor-72] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:40 [snail-grpc-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[286] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:40 [snail-job-job-286-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:40 [snail-job-job-286-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[286] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777047392},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,5113,0]\",\"[44879,5113,90]\",\"[44879,5673,90]\",\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44873\",\"posY\":\"5173\",\"robotCode\":\"4192\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777047392},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24644\",\"posY\":\"41301\",\"robotCode\":\"4193\",\"robotDir\":\"-69\",\"robotIp\":\"************\",\"speed\":\"293\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777047393},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777047393}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:40 [snail-job-grpc-client-executor-127.0.0.1-397] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[397]
2025-06-13 09:10:45 [snail-job-grpc-server-executor-75] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:45 [snail-grpc-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[287] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:45 [snail-job-job-287-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:45 [snail-job-job-287-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[287] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777052432},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,6091,90]\",\"[44879,7493,90]\",\"[44879,8989,90]\",\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44871\",\"posY\":\"7135\",\"robotCode\":\"4192\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"801\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777052432},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"23468\",\"posY\":\"42037\",\"robotCode\":\"4193\",\"robotDir\":\"-6\",\"robotIp\":\"************\",\"speed\":\"301\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777052433},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777052433}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:45 [snail-job-grpc-client-executor-127.0.0.1-399] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[399]
2025-06-13 09:10:50 [snail-job-grpc-server-executor-77] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:10:50 [snail-grpc-server-6] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[288] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:10:50 [snail-job-job-288-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:10:50 [snail-job-job-288-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[288] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777057410},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,10543,90]\",\"[44879,11967,90]\",\"[44879,13451,90]\",\"[44879,14974,90]\",\"[44879,16428,90]\",\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44874\",\"posY\":\"11185\",\"robotCode\":\"4192\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"798\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777057411},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[24753,40314,-90]\",\"[21873,42075,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"22015\",\"posY\":\"42089\",\"robotCode\":\"4193\",\"robotDir\":\"1\",\"robotIp\":\"************\",\"speed\":\"221\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777057411},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777057411}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:10:50 [snail-job-grpc-client-executor-127.0.0.1-402] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[402]
2025-06-13 09:11:00 [snail-job-grpc-server-executor-79] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:0ms
2025-06-13 09:11:00 [snail-grpc-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[2] Task batch:[289] Workflow batch:[null] Task scheduled successfully.
2025-06-13 09:11:00 [snail-job-job-289-1] INFO  c.a.s.c.j.c.e.b.AbstractHttpExecutor - [snail-job] Request URL: http://*************:8181/rcms-dps/rest/queryAgvStatus
Using request method: POST
Request timeout: 60000 seconds
2025-06-13 09:11:00 [snail-job-job-289-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[289] [{"status":1,"result":"{\"code\":\"0\",\"data\":[{\"battery\":\"74\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24749\",\"posY\":\"36102\",\"robotCode\":\"4191\",\"robotDir\":\"-89\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777067650},{\"battery\":\"69\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[\"[44879,17914,90]\",\"[44879,19442,90]\",\"[44879,20916,90]\",\"[44879,22405,90]\",\"[44879,23952,90]\",\"[44879,25543,90]\",\"[44879,27119,90]\",\"[44879,28539,90]\",\"[44879,30009,90]\",\"[44879,31503,90]\",\"[44879,33032,90]\",\"[44879,34510,90]\",\"[44879,35991,90]\",\"[44879,37569,90]\",\"[44879,39060,90]\",\"[44879,41301,90]\",\"[44879,42681,90]\",\"[44879,44140,90]\",\"[44879,45878,90]\",\"[44879,47319,90]\",\"[44879,48744,90]\",\"[44879,50339,90]\",\"[44879,51764,90]\",\"[44879,53324,90]\",\"[44879,54787,90]\",\"[44879,56269,90]\",\"[44879,57781,90]\",\"[44879,59233,90]\",\"[44879,60766,90]\",\"[44879,62224,90]\",\"[44879,63724,90]\",\"[44879,65224,90]\",\"[44300,64366,0]\"],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"44871\",\"posY\":\"19230\",\"robotCode\":\"4192\",\"robotDir\":\"90\",\"robotIp\":\"************\",\"speed\":\"799\",\"status\":\"2\",\"stop\":\"0\",\"timestamp\":1749777067651},{\"battery\":\"71\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"21878\",\"posY\":\"42089\",\"robotCode\":\"4193\",\"robotDir\":\"1\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"4\",\"stop\":\"0\",\"timestamp\":1749777067651},{\"battery\":\"78\",\"exclType\":\"0\",\"mapCode\":\"BB\",\"online\":true,\"path\":[],\"podCode\":\"\",\"podDir\":\"0\",\"posX\":\"24759\",\"posY\":\"50471\",\"robotCode\":\"4194\",\"robotDir\":\"-90\",\"robotIp\":\"************\",\"speed\":\"0\",\"status\":\"246\",\"stop\":\"0\",\"timestamp\":1749777067651}],\"interrupt\":false,\"message\":\"Success\",\"msgErrCode\":\"\",\"reqCode\":\"123456\"}","message":"Task executed successfully"}]
2025-06-13 09:11:01 [snail-job-grpc-client-executor-127.0.0.1-405] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[405]
2025-06-13 09:11:06 [snail-job-grpc-client-executor-127.0.0.1-406] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[406]
2025-06-13 10:52:21 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:21 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[21]毫秒
2025-06-13 10:52:21 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /error],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:22 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /error],耗时:[7]毫秒
2025-06-13 10:52:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[3]毫秒
2025-06-13 10:52:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /error],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /error],耗时:[0]毫秒
2025-06-13 10:52:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[0]毫秒
2025-06-13 10:52:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /error],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /error],耗时:[0]毫秒
2025-06-13 10:52:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-13 10:52:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[2]毫秒
2025-06-13 10:52:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJVaDBORTU5b3VpZkNWakJoM2lZT0hUbkwycUNCdlhtYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.atcNlOtUgfXcY2h9WbdGJcN88hhEQ-1RI1jkCbWZE1s"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 10:52:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[5]毫秒
2025-06-13 10:52:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-13 10:52:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[0]毫秒
2025-06-13 10:52:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-13 10:52:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-13 10:52:38 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-13 10:52:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-06-13 10:52:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-13 11:07:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"956561","username":"admin","password":"admin123","grantType":"password","code":"0","uuid":"ee751d8905e541f9b10a5272ceee6191","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e"}]
2025-06-13 11:07:51 [schedule-pool-4] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Error][验证码已失效]
2025-06-13 11:07:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[17]毫秒
2025-06-13 11:07:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-13 11:07:51 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-13 11:07:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[7]毫秒
2025-06-13 11:07:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"956561","username":"admin","password":"admin123","grantType":"password","code":"1","uuid":"bf3ec74213764a1891f3eb0660580a65","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e"}]
2025-06-13 11:07:55 [XNIO-1 task-4] INFO  o.d.w.s.impl.PasswordAuthStrategy - 登录用户：admin 不存在.
2025-06-13 11:07:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[8]毫秒
2025-06-13 11:07:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-13 11:07:55 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-13 11:07:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[8]毫秒
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","grantType":"password","code":"2","uuid":"8bc402a9880540178f21f567516ca9f7","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e"}]
2025-06-13 11:08:00 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJIQXhUOXM2ZEg4Qk5ndWVYODNWMWoxSzFCdUJkQmxOdSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.8yhFTRWy7dMw2doaEsgXjAr2YBIjRjx7kdJa4lmBn8k
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[94]毫秒
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[9]毫秒
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-13 11:08:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[6]毫秒
2025-06-13 11:08:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-13 11:08:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-06-13 11:08:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJIQXhUOXM2ZEg4Qk5ndWVYODNWMWoxSzFCdUJkQmxOdSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.8yhFTRWy7dMw2doaEsgXjAr2YBIjRjx7kdJa4lmBn8k"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 11:08:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/ware_warehouse_intype],无参数
2025-06-13 11:08:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/ware_warehouse_type],无参数
2025-06-13 11:08:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/ware_warehouse_intype],耗时:[3]毫秒
2025-06-13 11:08:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/ware_warehouse_type],耗时:[3]毫秒
2025-06-13 11:08:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-06-13 11:08:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[7]毫秒
2025-06-13 11:08:05 [schedule-pool-5] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录WMS仓库管理系统
2025-06-13 11:08:05 [redisson-3-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录WMS仓库管理系统
2025-06-13 14:49:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJIQXhUOXM2ZEg4Qk5ndWVYODNWMWoxSzFCdUJkQmxOdSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.8yhFTRWy7dMw2doaEsgXjAr2YBIjRjx7kdJa4lmBn8k"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 14:49:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1]毫秒
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[0]毫秒
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[0]毫秒
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-13 14:49:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-13 14:49:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-06-13 14:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","grantType":"password","code":"1","uuid":"0296c139c3ac4ebdbeac5c50a99e4c7a","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e"}]
2025-06-13 14:49:21 [schedule-pool-3] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ1aG1WaWRyU3JTTFpHOHhaOVRwYml1dlZXRFFiVUh1NSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ecNKFHd1k5iNneYUEFZCF4UF-zArceBZBY2JTDO1Wps
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[110]毫秒
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[8]毫秒
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-13 14:49:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[9]毫秒
2025-06-13 14:49:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-13 14:49:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-06-13 14:49:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ1aG1WaWRyU3JTTFpHOHhaOVRwYml1dlZXRFFiVUh1NSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ecNKFHd1k5iNneYUEFZCF4UF-zArceBZBY2JTDO1Wps"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-13 14:49:25 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/ware_warehouse_intype],无参数
2025-06-13 14:49:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/ware_warehouse_type],无参数
2025-06-13 14:49:25 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/ware_warehouse_intype],耗时:[2]毫秒
2025-06-13 14:49:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/ware_warehouse_type],耗时:[2]毫秒
2025-06-13 14:49:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-06-13 14:49:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[7]毫秒
2025-06-13 14:49:26 [schedule-pool-6] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录WMS仓库管理系统
2025-06-13 14:49:26 [redisson-3-4] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录WMS仓库管理系统
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-13 16:36:32 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-13 16:37:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-13 16:40:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-13 16:40:33 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.14 with PID 26092 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 16:40:33 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-13 16:40:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-13 16:40:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-13 16:40:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-13 16:40:37 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@56f569e
2025-06-13 16:40:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-13 16:40:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-13 16:40:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-13 16:40:38 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-13 16:40:38 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-13 16:40:39 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-13 16:40:39 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-13 16:40:39 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-13 16:40:39 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-13 16:51:25 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-13 16:51:29 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-13 16:51:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-13 16:51:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-13 16:51:29 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6e6ec71f
2025-06-13 16:51:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-13 16:51:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-13 16:51:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-13 16:51:31 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-13 16:51:31 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-13 16:51:31 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-13 16:51:31 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-13 16:51:32 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-13 16:51:32 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>