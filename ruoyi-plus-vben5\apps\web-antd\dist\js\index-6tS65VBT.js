var G=Object.defineProperty;var v=Object.getOwnPropertySymbols;var X=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var w=(l,a,o)=>a in l?G(l,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):l[a]=o,J=(l,a)=>{for(var o in a||(a={}))X.call(a,o)&&w(l,o,a[o]);if(v)for(var o of v(a))O.call(a,o)&&w(l,o,a[o]);return l};var Q=(l,a,o)=>new Promise((I,s)=>{var f=g=>{try{B(o.next(g))}catch(C){s(C)}},b=g=>{try{B(o.throw(g))}catch(C){s(C)}},B=g=>g.done?I(g.value):Promise.resolve(g.value).then(f,b);B((o=o.apply(l,a)).next())});import{aB as V,$ as N,as as K,T as W,ch as $,ap as R,ci as ee,cj as M,an as oe}from"./bootstrap-DCMzVRvD.js";import{v as te,a as ne}from"./vxe-table-DzEj5Fop.js";import{h as ae}from"./index-D1nLcUEe.js";import{d as ie}from"./download-UJak946_.js";import{_ as se}from"./file-upload-modal.vue_vue_type_script_setup_true_lang-DAsX8Vpz.js";import{_ as Ae}from"./image-upload-modal.vue_vue_type_script_setup_true_lang-Bchj-bfc.js";import Y from"./index-BeyziwLP.js";import{I as le}from"./index-BY49C_DM.js";import{S as re}from"./index-Ollxi7Rl.js";import{_ as ce}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as de,p as U,v as ge,l as x,S as me,h as E,o as d,w as A,a as m,b as e,T as u,k as p,t as S,c as j,j as Ee}from"../jse/index-index-C-MnMZEz.js";import{u as Qe}from"./use-vxe-grid-BC7vZzEr.js";import{u as D}from"./use-modal-CeMSCP2m.js";import{P as ue}from"./index-DNdMANjv.js";import{g as pe}from"./get-popup-container-P4S1sr5h.js";import fe from"./index-D-hwdOI6.js";import"./init-C8TKSdFQ.js";import"./helper-Bc7QQ92Q.js";import"./file-upload.vue_vue_type_script_setup_true_lang-Cat_icI0.js";import"./image-upload.vue_vue_type_style_index_0_lang-DExXFAky.js";import"./image-upload.vue_vue_type_script_setup_true_lang-Bn5a9jyU.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";function T(l,a=!1){const o=["B","KB","MB","GB","TB"];let s=0;for(;l>=1024&&s<o.length-1;)l/=1024,s++;const f=a?0:Math.min(s,3);return`${l.toFixed(f)}${o[s]}`}const Be=["jpg","jpeg","png","gif","webp"],Ce=()=>[{component:"Input",fieldName:"fileName",label:"文件名"},{component:"Input",fieldName:"originalName",label:"原名"},{component:"Input",fieldName:"fileSuffix",label:"拓展名"},{component:"Input",fieldName:"service",label:"服务商"},{component:"RangePicker",fieldName:"createTime",label:"创建时间"}],he=[{type:"checkbox",width:60},{title:"文件名",field:"fileName",showOverflow:!0},{title:"文件原名",field:"originalName",showOverflow:!0},{title:"文件拓展名",field:"fileSuffix"},{title:"文件预览",field:"url",showOverflow:!0,slots:{default:"url"}},{title:"创建时间",field:"createTime",sortable:!0},{title:"上传人",field:"createByName"},{title:"服务商",field:"service"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],Ie=`data:image/png;base64,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
`,ke={class:"flex size-full items-center justify-center"},ye=["onClick"],Ne={key:2},We=de({__name:"index",setup(l){const a={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:Ce(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",fieldMappingTime:[["createTime",["params[beginCreateTime]","params[endCreateTime]"],["YYYY-MM-DD 00:00:00","YYYY-MM-DD 23:59:59"]]]},o={checkboxConfig:{highlight:!0,reserve:!0},columns:he,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(k,...r)=>Q(null,[k,...r],function*({page:n,sorts:t},c={}){const i=J({pageNum:n.currentPage,pageSize:n.pageSize},c);return ne(i,t),yield $(i)})}},headerCellConfig:{height:44},cellConfig:{height:65},rowConfig:{keyField:"ossId"},sortConfig:{remote:!0,multiple:!1},id:"system-oss-index"},[I,s]=Qe({formOptions:a,gridOptions:o,gridEvents:{sortChange:()=>s.query()}});function f(n){return Q(this,null,function*(){const t=U(N("pages.common.downloadLoading")),c=R.loading({content:()=>t.value,duration:0});try{const k=yield ee(n.ossId,r=>{const i=Math.floor(r.loaded/r.total*100),y=T(r.loaded),_=T(r.total);t.value=`已下载: ${y}/${_} (${i}%)`});ie(k,n.originalName),R.success("下载完成")}finally{c()}})}function b(n){return Q(this,null,function*(){yield M([n.ossId]),yield s.query()})}function B(){const t=s.grid.getCheckboxRecords().map(c=>c.ossId);oe.confirm({title:"提示",okType:"danger",content:`确认删除选中的${t.length}条记录吗？`,onOk:()=>Q(null,null,function*(){yield M(t),yield s.query()})})}const g=V();function C(){g.push("/system/oss-config/index")}const h=U(!1);ge(()=>Q(null,null,function*(){const n=yield ae("sys.oss.previewListResource");h.value=n==="true"}));function z(n){return Be.some(t=>n.toLocaleLowerCase().includes(t))}function P(n){return n.toLocaleLowerCase().includes("pdf")}function L(n){window.open(n)}const[F,H]=D({connectedComponent:Ae}),[q,Z]=D({connectedComponent:se});return(n,t)=>{const c=x("a-button"),k=x("ghost-button"),r=me("access");return d(),E(e(ce),{"auto-content-height":!0},{default:A(()=>[m(e(I),{"table-title":"文件列表"},{"toolbar-tools":A(()=>[m(e(Y),null,{default:A(()=>[m(e(W),{title:"预览图片"},{default:A(()=>[m(e(fe),{checked:h.value,"onUpdate:checked":t[0]||(t[0]=i=>h.value=i)},null,8,["checked"])]),_:1}),u((d(),E(c,{onClick:C},{default:A(()=>t[2]||(t[2]=[p(" 配置管理 ")])),_:1,__:[2]})),[[r,["system:ossConfig:list"],"code"]]),u((d(),E(c,{disabled:!e(te)(e(s)),danger:"",type:"primary",onClick:B},{default:A(()=>[p(S(e(N)("pages.common.delete")),1)]),_:1},8,["disabled"])),[[r,["system:oss:remove"],"code"]]),u((d(),E(c,{onClick:e(Z).open},{default:A(()=>t[3]||(t[3]=[p(" 文件上传 ")])),_:1,__:[3]},8,["onClick"])),[[r,["system:oss:upload"],"code"]]),u((d(),E(c,{onClick:e(H).open},{default:A(()=>t[4]||(t[4]=[p(" 图片上传 ")])),_:1,__:[4]},8,["onClick"])),[[r,["system:oss:upload"],"code"]])]),_:1})]),url:A(({row:i})=>[h.value&&z(i.url)?(d(),E(e(le),{key:i.ossId,src:i.url,height:"50px",fallback:e(Ie)},{placeholder:A(()=>[Ee("div",ke,[m(e(re))])]),_:2},1032,["src","fallback"])):h.value&&P(i.url)?(d(),j("span",{key:1,class:"icon-[vscode-icons--file-type-pdf2] size-10 cursor-pointer",onClick:K(y=>L(i.url),["stop"])},null,8,ye)):(d(),j("span",Ne,S(i.url),1))]),action:A(({row:i})=>[m(e(Y),null,{default:A(()=>[u((d(),E(k,{onClick:y=>f(i)},{default:A(()=>[p(S(e(N)("pages.common.download")),1)]),_:2},1032,["onClick"])),[[r,["system:oss:download"],"code"]]),m(e(ue),{"get-popup-container":e(pe),placement:"left",title:"确认删除？",onConfirm:y=>b(i)},{default:A(()=>[u((d(),E(k,{danger:"",onClick:t[1]||(t[1]=K(()=>{},["stop"]))},{default:A(()=>[p(S(e(N)("pages.common.delete")),1)]),_:1})),[[r,["system:oss:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),m(e(F),{onReload:e(s).query},null,8,["onReload"]),m(e(q),{onReload:e(s).query},null,8,["onReload"])]),_:1})}}});export{We as default};
