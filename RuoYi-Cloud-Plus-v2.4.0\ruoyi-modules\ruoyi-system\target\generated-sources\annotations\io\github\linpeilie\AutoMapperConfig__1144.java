package io.github.linpeilie;

import org.dromara.system.domain.convert.SysClientVoConvert;
import org.dromara.system.domain.convert.SysDictDataVoConvert;
import org.dromara.system.domain.convert.SysLogininforBoConvert;
import org.dromara.system.domain.convert.SysOperLogBoConvert;
import org.dromara.system.domain.convert.SysSocialBoConvert;
import org.dromara.system.domain.convert.SysSocialVoConvert;
import org.dromara.system.domain.convert.SysTenantVoConvert;
import org.dromara.system.domain.convert.SysUserBoConvert;
import org.dromara.system.domain.convert.SysUserVoConvert;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__1144.class, SysClientVoConvert.class, SysSocialVoConvert.class, SysLogininforBoConvert.class, SysUserBoConvert.class, SysSocialBoConvert.class, SysTenantVoConvert.class, SysDictDataVoConvert.class, SysOperLogBoConvert.class, SysUserVoConvert.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__1144 {
}
