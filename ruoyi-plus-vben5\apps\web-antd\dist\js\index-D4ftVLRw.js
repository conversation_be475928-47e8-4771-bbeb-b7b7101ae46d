var n=(p,o,e)=>new Promise((r,s)=>{var a=t=>{try{m(e.next(t))}catch(i){s(i)}},u=t=>{try{m(e.throw(t))}catch(i){s(i)}},m=t=>t.done?r(t.value):Promise.resolve(t.value).then(a,u);m((e=e.apply(p,o)).next())});import{r as l}from"./index-C5dPwGGG.js";import{M as d}from"./menu-select-table-C_Crb3c1.js";import{_ as f}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as _,p as h,C as k,v,h as y,o as w,w as x,a as M,b as c}from"../jse/index-index-C-MnMZEz.js";import"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./menu-select-table.vue_vue_type_style_index_0_scoped_96b02cc5_lang-Be00nslj.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./tree-DFBawhPd.js";import"./uniq-CCKK3QTo.js";import"./index-D2_dYV5Z.js";import"./Checkbox-DRV8G-PI.js";import"./index-DabkQ3D7.js";import"./index-BeyziwLP.js";import"./use-vxe-grid-BC7vZzEr.js";import"./Group-oWwucTzK.js";import"./index-kC0HFDdy.js";const L=_({__name:"index",setup(p){const o=h([]),e=k([]);return v(()=>n(null,null,function*(){const r=yield l(3);e.value=r.menus,o.value=r.checkedKeys})),(r,s)=>(w(),y(c(f),{"auto-content-height":!0},{default:x(()=>[M(c(d),{menus:e.value,"checked-keys":o.value,"onUpdate:checkedKeys":s[0]||(s[0]=a=>o.value=a),association:!0},null,8,["menus","checked-keys"])]),_:1}))}});export{L as default};
