const { createJiti } = require("../../../node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/jiti.cjs")

const jiti = createJiti(__filename, {
  "interopDefault": true,
  "alias": {
    "@vben/tailwind-config": "D:/kwsywms_test/ruoyi-plus-vben5/internal/tailwind-config"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("D:/kwsywms_test/ruoyi-plus-vben5/internal/tailwind-config/src/index.js")} */
module.exports = jiti("D:/kwsywms_test/ruoyi-plus-vben5/internal/tailwind-config/src/index.ts")