{"doc": " 导出下拉框Excel示例\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getProvinceList", "paramTypes": [], "doc": " 模拟查询数据库操作\n\n @return /\n"}, {"name": "getCityList", "paramTypes": ["java.util.List"], "doc": " 模拟查找数据库操作，需要连带查询出省的数据\n\n @param provinceList 模拟的父省数据\n @return /\n"}, {"name": "getAreaList", "paramTypes": ["java.util.List"], "doc": " 模拟查找数据库操作，需要连带查询出市的数据\n\n @param cityList 模拟的父市数据\n @return /\n"}, {"name": "selectParentData", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 模拟数据库的查询父数据操作\n\n @param parentList /\n @param sonList    /\n"}], "constructors": []}