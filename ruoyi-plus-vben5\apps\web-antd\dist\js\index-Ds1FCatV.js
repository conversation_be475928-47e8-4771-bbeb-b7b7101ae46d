import{c as s}from"./helper-Bc7QQ92Q.js";import{y as t}from"./bootstrap-DCMzVRvD.js";function n(e){return t.get("/system/role/list",{params:e})}function u(e){return s("/system/role/export",e)}function a(e){return t.get(`/system/role/${e}`)}function i(e){return t.postWithMsg("/system/role",e)}function c(e){return t.putWithMsg("/system/role",e)}function m(e){const r={roleId:e.roleId,status:e.status};return t.putWithMsg("/system/role/changeStatus",r)}function f(e){return t.deleteWithMsg(`/system/role/${e}`)}function h(e){return t.putWithMsg("/system/role/dataScope",e)}function p(e){return t.get("/system/role/authUser/allocatedList",{params:e})}function d(e){return t.get("/system/role/authUser/unallocatedList",{params:e})}function g(e){return t.putWithMsg("/system/role/authUser/cancel",e)}function y(e,r){return t.putWithMsg(`/system/role/authUser/cancelAll?roleId=${e}&userIds=${r.join(",")}`)}function $(e,r){return t.putWithMsg(`/system/role/authUser/selectAll?roleId=${e}&userIds=${r.join(",")}`)}function A(e){return t.get(`/system/role/deptTree/${e}`)}export{g as a,y as b,a as c,c as d,i as e,$ as f,d as g,m as h,n as i,f as j,u as k,h as l,A as m,p as r};
