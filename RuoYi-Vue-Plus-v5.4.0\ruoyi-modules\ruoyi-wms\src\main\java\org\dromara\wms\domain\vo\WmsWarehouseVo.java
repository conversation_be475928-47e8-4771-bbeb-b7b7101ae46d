package org.dromara.wms.domain.vo;

import java.io.Serial;
import java.io.Serializable;

import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.wms.domain.WmsWarehouse;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;



/**
 * 仓库列表视图对象 wms_warehouse
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WmsWarehouse.class)
public class WmsWarehouseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 仓库id
     */
    @ExcelProperty(value = "仓库id")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码")
    private String warehouseNumber;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）
     */
    @ExcelProperty(value = "仓库属性", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ware_warehouse_type")
    private String warehouseType;

    /**
     * 库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
     */
    @ExcelProperty(value = "库存状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ware_warehouse_intype")
    private String warehouseInventoryStatus;

    /**
     * 收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
     */
    @ExcelProperty(value = "收料状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ware_warehouse_intype")
    private String warehouseRecevingStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
