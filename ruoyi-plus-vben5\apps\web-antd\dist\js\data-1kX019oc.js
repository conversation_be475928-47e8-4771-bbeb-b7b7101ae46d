import{T as c}from"./index-B6iusSRX.js";import{a as n,N as t}from"../jse/index-index-C-MnMZEz.js";function s(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!t(e)}const o={cyan:{color:"cyan",label:"cyan"},danger:{color:"error",label:"危险(danger)"},default:{color:"default",label:"默认(default)"},green:{color:"green",label:"green"},info:{color:"default",label:"信息(info)"},orange:{color:"orange",label:"orange"},pink:{color:"pink",label:"pink"},primary:{color:"processing",label:"主要(primary)"},purple:{color:"purple",label:"purple"},red:{color:"red",label:"red"},success:{color:"success",label:"成功(success)"},warning:{color:"warning",label:"警告(warning)"}};function b(){const e=[];return Object.keys(o).forEach(l=>{if(!o[l])return;const r=o[l].label,a=o[l].color;e.push({label:n(c,{color:a},s(r)?r:{default:()=>[r]}),value:l})}),e}export{b as a,o as t};
