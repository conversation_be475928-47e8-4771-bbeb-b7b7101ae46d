import{j as y,m as S,b1 as w,_ as d,b2 as B,r as T,b3 as k,aD as _,e as D,b4 as N,p as z,c as A,g as h,b5 as I,aJ as W,T as E,b6 as H,b7 as j,b8 as $,v as C}from"./bootstrap-DCMzVRvD.js";import{d as M,p as R,B as F,a as b,F as L}from"../jse/index-index-C-MnMZEz.js";const O=e=>{const{componentCls:o,popoverBg:r,popoverColor:t,width:a,fontWeightStrong:s,popoverPadding:l,boxShadowSecondary:c,colorTextHeading:g,borderRadiusLG:u,zIndexPopup:p,marginXS:v,colorBgElevated:n}=e;return[{[o]:d(d({},T(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:p,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--antd-arrow-background-color":n,"&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:r,backgroundClip:"padding-box",borderRadius:u,boxShadow:c,padding:l},[`${o}-title`]:{minWidth:a,marginBottom:v,color:g,fontWeight:s},[`${o}-inner-content`]:{color:t}})},B(e,{colorBg:"var(--antd-arrow-background-color)"}),{[`${o}-pure`]:{position:"relative",maxWidth:"none",[`${o}-content`]:{display:"inline-block"}}}]},G=e=>{const{componentCls:o}=e;return{[o]:k.map(r=>{const t=e[`${r}-6`];return{[`&${o}-${r}`]:{"--antd-arrow-background-color":t,[`${o}-inner`]:{backgroundColor:t},[`${o}-arrow`]:{background:"transparent"}}}})}},J=e=>{const{componentCls:o,lineWidth:r,lineType:t,colorSplit:a,paddingSM:s,controlHeight:l,fontSize:c,lineHeight:g,padding:u}=e,p=l-Math.round(c*g),v=p/2,n=p/2-r,i=u;return{[o]:{[`${o}-inner`]:{padding:0},[`${o}-title`]:{margin:0,padding:`${v}px ${i}px ${n}px`,borderBottom:`${r}px ${t} ${a}`},[`${o}-inner-content`]:{padding:`${s}px ${i}px`}}}},V=y("Popover",e=>{const{colorBgElevated:o,colorText:r,wireframe:t}=e,a=S(e,{popoverBg:o,popoverColor:r,popoverPadding:12});return[O(a),G(a),t&&J(a),w(a,"zoom-big")]},e=>{let{zIndexPopupBase:o}=e;return{zIndexPopup:o+30,width:177}}),X=()=>d(d({},j()),{content:$(),title:$()}),Z=M({compatConfig:{MODE:3},name:"APopover",inheritAttrs:!1,props:D(X(),d(d({},H()),{trigger:"hover",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1})),setup(e,o){let{expose:r,slots:t,attrs:a}=o;const s=R();N(e.visible===void 0),r({getPopupDomNode:()=>{var n,i;return(i=(n=s.value)===null||n===void 0?void 0:n.getPopupDomNode)===null||i===void 0?void 0:i.call(n)}});const{prefixCls:l,configProvider:c}=z("popover",e),[g,u]=V(l),p=F(()=>c.getPrefixCls()),v=()=>{var n,i;const{title:m=C((n=t.title)===null||n===void 0?void 0:n.call(t)),content:f=C((i=t.content)===null||i===void 0?void 0:i.call(t))}=e,x=!!(Array.isArray(m)?m.length:m),P=!!(Array.isArray(f)?f.length:m);return!x&&!P?null:b(L,null,[x&&b("div",{class:`${l.value}-title`},[m]),b("div",{class:`${l.value}-inner-content`},[f])])};return()=>{const n=A(e.overlayClassName,u.value);return g(b(E,h(h(h({},W(e,["title","content"])),a),{},{prefixCls:l.value,ref:s,overlayClassName:n,transitionName:I(p.value,"zoom-big",e.transitionName),"data-popover-inject":!0}),{title:v,default:t.default}))}}}),U=_(Z);export{U as P};
