var g=(d,w,m)=>new Promise((l,n)=>{var k=c=>{try{u(m.next(c))}catch(s){n(s)}},C=c=>{try{u(m.throw(c))}catch(s){n(s)}},u=c=>c.done?l(c.value):Promise.resolve(c.value).then(k,C);u((m=m.apply(d,w)).next())});import{aj as z}from"./bootstrap-DCMzVRvD.js";import{c as G,l as U,m as _}from"./index-Ds1FCatV.js";import"./vxe-table-DzEj5Fop.js";import"./menu-select-table.vue_vue_type_style_index_0_scoped_96b02cc5_lang-Be00nslj.js";import{u as F}from"./uniq-CCKK3QTo.js";import K from"./index-D2_dYV5Z.js";import{d as N,m as j,p as S,B as O,u as L,q as R,v as E,I as q,a1 as $,c as H,o as T,j as y,n as J,t as M,k as I,a as B,w as b,b as p,h as P,f as Q,a2 as W,K as X,r as Y,e as Z,g as ee,P as te,O as ae}from"../jse/index-index-C-MnMZEz.js";import{t as le,f as se}from"./tree-DFBawhPd.js";import{T as ne}from"./index-DdFaZtFR.js";import{u as re,d as oe}from"./popup-D6rC6QBG.js";import{a as ce}from"./data-CPbGrVe8.js";import{u as ie}from"./use-modal-CeMSCP2m.js";const de={class:"bg-background w-full rounded-lg border-[1px] p-[12px]"},ue={class:"flex items-center justify-between gap-2 border-b-[1px] pb-2"},fe={class:"text-primary mx-1 font-semibold"},pe={class:"flex flex-wrap items-center justify-between border-b-[1px] py-2"},me={class:"py-2"},he=N({inheritAttrs:!1,__name:"tree-select-panel",props:j({checkStrictly:{default:!0,type:Boolean},expandAllOnInit:{default:!1,type:Boolean},fieldNames:{default:()=>({key:"id",title:"label"}),type:Object},resetOnStrictlyChange:{default:!0,type:Boolean},treeData:{default:()=>[],type:Array}},{value:{default:()=>[],type:Array},valueModifiers:{}}),emits:j(["checkStrictlyChange"],["update:value"]),setup(d,{expose:w,emit:m}){const l=d,n=m,k=S(!1),C=S(!1),u=O(()=>!l.checkStrictly),c=O(()=>l.checkStrictly?"父子节点关联":"父子节点独立"),s=L(d,"value"),v=O(()=>{const t=l.fieldNames.key;return le(l.treeData).map(e=>e[t])}),i=S([]),f=R([s,()=>l.treeData],()=>{if(l.checkStrictly&&s.value.length>0&&l.treeData.length>0){const t=se(l.treeData,s.value,{id:l.fieldNames.key});i.value=F([...t,...s.value]),f()}!l.checkStrictly&&s.value.length>0&&(i.value=s.value,f())});function D(t,e){if(Array.isArray(t)){const a=e.halfCheckedKeys||[];i.value=[...a,...t]}else i.value=[...t.checked],s.value=[...t.checked]}function A(t){s.value=t.target.checked?v.value:[],i.value=t.target.checked?v.value:[]}const x=S([]);function V(t){const e=t.target.checked;x.value=e?v.value:[]}function r(t){n("checkStrictlyChange",t.target.checked),l.resetOnStrictlyChange&&(s.value=[],i.value=[])}w({getCheckedKeys:()=>F(i.value)}),E(()=>g(null,null,function*(){l.expandAllOnInit&&(yield q(),x.value=v.value)}));const o=$();return(t,e)=>(T(),H("div",de,[y("div",ue,[y("div",null,[e[5]||(e[5]=y("span",null,"节点状态: ",-1)),y("span",{class:J([l.checkStrictly?"text-primary":"text-red-500"])},M(c.value),3)]),y("div",null,[e[6]||(e[6]=I(" 已选中 ")),y("span",fe,M(i.value.length),1),e[7]||(e[7]=I(" 个节点 "))])]),y("div",pe,[B(p(K),{checked:k.value,"onUpdate:checked":e[0]||(e[0]=a=>k.value=a),onChange:V},{default:b(()=>e[8]||(e[8]=[I(" 展开/折叠全部 ")])),_:1,__:[8]},8,["checked"]),B(p(K),{checked:C.value,"onUpdate:checked":e[1]||(e[1]=a=>C.value=a),onChange:A},{default:b(()=>e[9]||(e[9]=[I(" 全选/取消全选 ")])),_:1,__:[9]},8,["checked"]),B(p(K),{checked:d.checkStrictly,onChange:r},{default:b(()=>e[10]||(e[10]=[I(" 父子节点关联 ")])),_:1,__:[10]},8,["checked"])]),y("div",me,[d.treeData.length>0?(T(),P(p(ne),{key:0,"check-strictly":u.value,"onUpdate:checkStrictly":e[2]||(e[2]=a=>u.value=a),"checked-keys":s.value,"onUpdate:checkedKeys":e[3]||(e[3]=a=>s.value=a),"expanded-keys":x.value,"onUpdate:expandedKeys":e[4]||(e[4]=a=>x.value=a),checkable:!0,"field-names":d.fieldNames,selectable:!1,"tree-data":d.treeData,onCheck:D},W({_:2},[X(Object.keys(p(o)),a=>({name:a,fn:b(h=>[Y(t.$slots,a,Z(ee(h!=null?h:{})))])}))]),1032,["check-strictly","checked-keys","expanded-keys","field-names","tree-data"])):Q("",!0)])]))}}),Ve=N({__name:"role-auth-modal",emits:["reload"],setup(d,{emit:w}){const m=w,[l,n]=z({commonConfig:{componentProps:{class:"w-full"}},layout:"vertical",schema:ce(),showDefaultActions:!1}),k=S([]);function C(r){return g(this,null,function*(){const o=yield _(r);n.setFieldValue("deptIds",o.checkedKeys),k.value=o.depts})}function u(){return g(this,null,function*(){var e,a,h;const r=yield oe(n)(),o=(h=(a=(e=D.value)==null?void 0:e[0])==null?void 0:a.getCheckedKeys())!=null?h:[];return r+o.join(",")})}const{onBeforeClose:c,markInitialized:s,resetInitialized:v}=re({initializedGetter:u,currentGetter:u}),[i,f]=ie({fullscreenButton:!1,onBeforeClose:c,onClosed:x,onConfirm:A,onOpenChange:r=>g(null,null,function*(){if(!r)return null;f.modalLoading(!0);const{id:o}=f.getData();C(o);const t=yield G(o);yield n.setValues(t),s(),f.modalLoading(!1)})}),D=S();function A(){return g(this,null,function*(){var r,o,t;try{f.lock(!0);const{valid:e}=yield n.validate();if(!e)return;const a=te(yield n.getValues());if(a.dataScope==="2"){const h=(t=(o=(r=D.value)==null?void 0:r[0])==null?void 0:o.getCheckedKeys())!=null?t:[];a.deptIds=h}else a.deptIds=[];yield U(a),v(),m("reload"),f.close()}catch(e){console.error(e)}finally{f.lock(!1)}})}function x(){return g(this,null,function*(){yield n.resetForm(),v()})}function V(r){n.setFieldValue("deptCheckStrictly",r)}return(r,o)=>(T(),P(p(i),{class:"min-h-[600px] w-[550px]",title:"分配权限"},{default:b(()=>[B(p(l),null,{deptIds:b(t=>[B(p(he),ae({ref_key:"deptSelectRef",ref:D},t,{"check-strictly":p(n).form.values.deptCheckStrictly,"expand-all-on-init":!0,"tree-data":k.value,onCheckStrictlyChange:V}),null,16,["check-strictly","tree-data"])]),_:1})]),_:1}))}});export{Ve as _};
