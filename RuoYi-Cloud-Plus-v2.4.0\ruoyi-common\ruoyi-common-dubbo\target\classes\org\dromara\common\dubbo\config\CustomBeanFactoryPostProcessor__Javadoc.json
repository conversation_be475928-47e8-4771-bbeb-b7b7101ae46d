{"doc": " dubbo自定义IP注入(避免IP不正确问题)\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "getOrder", "paramTypes": [], "doc": " 获取该 BeanFactoryPostProcessor 的顺序，确保它在容器初始化过程中具有最高优先级\n\n @return 优先级顺序值，越小优先级越高\n"}, {"name": "postProcessBeanFactory", "paramTypes": ["org.springframework.beans.factory.config.ConfigurableListableBeanFactory"], "doc": " 在 Spring 容器初始化过程中对 Bean 工厂进行后置处理\n\n @param beanFactory 可配置的 Bean 工厂\n @throws BeansException 如果在处理过程中发生错误\n"}], "constructors": []}