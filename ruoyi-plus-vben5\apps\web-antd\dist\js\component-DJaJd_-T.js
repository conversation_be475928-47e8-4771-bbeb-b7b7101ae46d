const e={selectImage:"选择图片",uploadSuccess:"上传成功",imageTooBig:"图片超限",modalTitle:"头像上传",okText:"确认并上传",btn_reset:"重置",btn_rotate_left:"逆时针旋转",btn_rotate_right:"顺时针旋转",btn_scale_x:"水平翻转",btn_scale_y:"垂直翻转",btn_zoom_in:"放大",btn_zoom_out:"缩小",preview:"预览"},o={placeholder:"选择租户",switch:"切换当前租户为: ",reset:"还原为默认租户"},t={title:"消息",received:"收到新消息"},a={save:"保存",upload:"上传",imgUpload:"图片上传",uploaded:"已上传",operating:"操作",del:"删除",download:"下载",saveWarn:"请等待文件上传后，保存!",saveError:"没有上传成功的文件，无法保存!",preview:"预览",choose:"选择文件",accept:"支持{0}格式",acceptUpload:"只能上传{0}格式文件",maxSize:"单个文件不超过{0}MB",maxSizeMultiple:"只能上传不超过{0}MB的文件!",maxNumber:"最多只能上传{0}个文件",legend:"略缩图",fileName:"文件名",fileSize:"文件大小",fileStatue:"状态",pending:"待上传",startUpload:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",uploading:"上传中",uploadWait:"请等待文件上传结束后操作",reUploadFailed:"重新上传失败文件",uploadHelpMessage:"请上传不超过{size}的{ext}格式文件",unknownFileType:"未知的文件类型, 无法上传",confirmDelete:"确认删除文件 {0}?",clickOrDrag:"点击或拖动文件到这个区域上传"},l={cropper:e,tenantToggle:o,notice:t,upload:a};export{e as cropper,l as default,t as notice,o as tenantToggle,a as upload};
