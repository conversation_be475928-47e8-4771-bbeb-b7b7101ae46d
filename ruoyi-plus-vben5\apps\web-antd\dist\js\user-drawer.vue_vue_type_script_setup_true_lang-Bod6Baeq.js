var l=(u,N,i)=>new Promise((s,f)=>{var w=a=>{try{m(i.next(a))}catch(c){f(c)}},t=a=>{try{m(i.throw(a))}catch(c){f(c)}},m=a=>a.done?s(a.value):Promise.resolve(a.value).then(w,t);m((i=i.apply(u,N)).next())});import{al as _,bq as D,ar as P,$ as L,aj as E}from"./bootstrap-DCMzVRvD.js";import{h as G}from"./index-D1nLcUEe.js";import{f as z}from"./index-ErD4UjKl.js";import{f as M,i as U,j as Y,g as $}from"./index-ocPq22VW.js";import{u as j,d as V}from"./popup-D6rC6QBG.js";import{b as K}from"./data-CPbGrVe8.js";import{g as y}from"./dict-BLkXAGS5.js";import{a as b}from"./get-popup-container-P4S1sr5h.js";import{T as X}from"./index-B6iusSRX.js";import{d as H,p as F,B as J,v as Q,P as Z,h as ee,o as te,w as oe,a as ae,b as k,M as S}from"../jse/index-index-C-MnMZEz.js";import{u as le}from"./use-drawer-6qcpK-D1.js";import{a as se}from"./tree-DFBawhPd.js";const ge=()=>[{component:"Input",fieldName:"userName",label:"用户账号"},{component:"Input",fieldName:"nickName",label:"用户昵称"},{component:"Input",fieldName:"phonenumber",label:"手机号码"},{component:"Select",componentProps:{getPopupContainer:b,options:y(P.SYS_NORMAL_DISABLE)},fieldName:"status",label:"用户状态"},{component:"RangePicker",fieldName:"createTime",label:"创建时间"}],Se=[{type:"checkbox",width:60},{field:"userName",title:"名称",minWidth:80},{field:"nickName",title:"昵称",minWidth:130},{field:"avatar",title:"头像",slots:{default:"avatar"},minWidth:80},{field:"deptName",title:"部门",minWidth:120},{field:"phonenumber",title:"手机号",formatter({cellValue:u}){return u||"暂无"},minWidth:120},{field:"status",title:"状态",slots:{default:"status"},minWidth:100},{field:"createTime",title:"创建时间",minWidth:150},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],ne=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"userId"},{component:"Input",fieldName:"userName",label:"用户账号",rules:"required"},{component:"InputPassword",fieldName:"password",label:"用户密码",rules:"required"},{component:"Input",fieldName:"nickName",label:"用户昵称",rules:"required"},{component:"TreeSelect",defaultValue:void 0,fieldName:"deptId",label:"所属部门",rules:"selectRequired"},{component:"Input",fieldName:"phonenumber",label:"手机号码",defaultValue:void 0,rules:_().regex(/^1[3-9]\d{9}$/,"请输入正确的手机号码").optional().or(D(""))},{component:"Input",fieldName:"email",defaultValue:void 0,label:"邮箱",rules:_().email("请输入正确的邮箱").optional().or(D(""))},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:y(P.SYS_USER_SEX),optionType:"button"},defaultValue:"0",fieldName:"sex",formItemClass:"col-span-2 lg:col-span-1",label:"性别"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:y(P.SYS_NORMAL_DISABLE),optionType:"button"},defaultValue:"0",fieldName:"status",formItemClass:"col-span-2 lg:col-span-1",label:"状态"},{component:"Select",componentProps:{getPopupContainer:b,mode:"multiple",optionFilterProp:"label",optionLabelProp:"label",placeholder:"请先选择部门"},fieldName:"postIds",help:"选择部门后, 将自动加载该部门下所有的岗位",label:"岗位"},{component:"Select",componentProps:{getPopupContainer:b,mode:"multiple",optionFilterProp:"title",optionLabelProp:"title"},fieldName:"roleIds",label:"角色"},{component:"Textarea",fieldName:"remark",formItemClass:"items-start",label:"备注"}],Pe=H({__name:"user-drawer",emits:["reload"],setup(u,{emit:N}){const i=N,s=F(!1),f=J(()=>s.value?L("pages.common.edit"):L("pages.common.add")),[w,t]=E({commonConfig:{formItemClass:"col-span-2",componentProps:{class:"w-full"},labelWidth:80},schema:ne(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function m(e){const o=K.find(p=>p.value===e.dataScope);return o?S("div",{class:"flex items-center gap-[6px]"},[S("span",null,e.roleName),S(X,{color:o.color},()=>o.label)]):e.roleName}function a(e){return l(this,null,function*(){const p=(yield z(e)).map(h=>({label:h.postName,value:h.postId})),n=p.length>0?"请选择":"该部门下暂无岗位";t.updateSchema([{componentProps:{options:p,placeholder:n},fieldName:"postIds"}])})}function c(){return l(this,null,function*(){const e=yield $();se(e,"label"," / "),t.updateSchema([{componentProps:o=>({class:"w-full",fieldNames:{key:"id",value:"id",children:"children"},getPopupContainer:b,onSelect(n){return l(this,null,function*(){yield a(n),o.postIds=[]})},placeholder:"请选择",showSearch:!0,treeData:e,treeDefaultExpandAll:!0,treeLine:{showLeafIcon:!1},treeNodeFilterProp:"label",treeNodeLabelProp:"fullName"}),fieldName:"deptId"}])})}const I=F("");Q(()=>l(null,null,function*(){const e=yield G("sys.user.initPassword");e&&(I.value=e)}));function x(e){return l(this,null,function*(){!e&&I.value&&t.setFieldValue("password",I.value)})}const{onBeforeClose:T,markInitialized:B,resetInitialized:v}=j({initializedGetter:V(t),currentGetter:V(t)}),[R,r]=le({onBeforeClose:T,onClosed:O,onConfirm:A,onOpenChange(e){return l(this,null,function*(){if(!e)return t.updateSchema([{componentProps:{options:[],placeholder:"请先选择部门"},fieldName:"postIds"}]),null;r.drawerLoading(!0);const{id:o}=r.getData();s.value=!!o,t.updateSchema([{componentProps:{disabled:s.value},fieldName:"userName"},{dependencies:{show:()=>!s.value,triggerFields:["id"]},fieldName:"password"}]);const{postIds:p,posts:n,roleIds:h,roles:W,user:g}=yield M(o),q=(n!=null?n:[]).map(d=>({label:d.postName,value:d.postId}));t.updateSchema([{componentProps:{optionLabelProp:"title",options:W.map(d=>({label:m(d),title:d.roleName,value:d.roleId}))},fieldName:"roleIds"},{componentProps:{options:q},fieldName:"postIds"}]);const C=[c(),x(s.value)];g&&C.push(t.setValues(g),t.setFieldValue("postIds",p),t.setFieldValue("roleIds",h),a(g.deptId)),yield Promise.all(C),yield B(),r.drawerLoading(!1)})}});function A(){return l(this,null,function*(){try{r.lock(!0);const{valid:e}=yield t.validate();if(!e)return;const o=Z(yield t.getValues());yield s.value?U(o):Y(o),v(),i("reload"),r.close()}catch(e){console.error(e)}finally{r.lock(!1)}})}function O(){return l(this,null,function*(){t.resetForm(),v()})}return(e,o)=>(te(),ee(k(R),{title:f.value,class:"w-[600px]"},{default:oe(()=>[ae(k(w))]),_:1},8,["title"]))}});export{Pe as _,Se as c,ge as q};
