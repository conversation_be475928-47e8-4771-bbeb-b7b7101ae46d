{"doc": " 测试国际化\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String"], "doc": " 通过code获取国际化内容\n code为 messages.properties 中的 key\n <p>\n 测试使用 user.register.success\n\n @param code 国际化code\n"}, {"name": "test1", "paramTypes": ["java.lang.String"], "doc": " Validator 校验国际化\n 不传值 分别查看异常返回\n <p>\n 测试使用 not.null\n"}, {"name": "test2", "paramTypes": ["org.dromara.demo.controller.TestI18nController.TestI18nBo"], "doc": " Bean 校验国际化\n 不传值 分别查看异常返回\n <p>\n 测试使用 not.null\n"}], "constructors": []}