{"groups": [{"name": "security.captcha", "type": "org.dromara.auth.properties.CaptchaProperties", "sourceType": "org.dromara.auth.properties.CaptchaProperties"}, {"name": "user.password", "type": "org.dromara.auth.properties.UserPasswordProperties", "sourceType": "org.dromara.auth.properties.UserPasswordProperties"}], "properties": [{"name": "security.captcha.category", "type": "org.dromara.auth.enums.CaptchaCategory", "description": "验证码类别", "sourceType": "org.dromara.auth.properties.CaptchaProperties"}, {"name": "security.captcha.char-length", "type": "java.lang.Integer", "description": "字符验证码长度", "sourceType": "org.dromara.auth.properties.CaptchaProperties"}, {"name": "security.captcha.enabled", "type": "java.lang.Bo<PERSON>an", "description": "验证码开关", "sourceType": "org.dromara.auth.properties.CaptchaProperties"}, {"name": "security.captcha.number-length", "type": "java.lang.Integer", "description": "数字验证码位数", "sourceType": "org.dromara.auth.properties.CaptchaProperties"}, {"name": "security.captcha.type", "type": "org.dromara.auth.enums.CaptchaType", "description": "验证码类型", "sourceType": "org.dromara.auth.properties.CaptchaProperties"}, {"name": "user.password.lock-time", "type": "java.lang.Integer", "description": "密码锁定时间（默认10分钟）", "sourceType": "org.dromara.auth.properties.UserPasswordProperties"}, {"name": "user.password.max-retry-count", "type": "java.lang.Integer", "description": "密码最大错误次数", "sourceType": "org.dromara.auth.properties.UserPasswordProperties"}], "hints": []}