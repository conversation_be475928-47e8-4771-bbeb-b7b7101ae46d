#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules/nitropack/dist/cli/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules/nitropack/dist/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules/nitropack/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules/nitropack/dist/cli/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules/nitropack/dist/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules/nitropack/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/nitropack@2.11.12_encoding@0.1.13/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nitropack/dist/cli/index.mjs" "$@"
else
  exec node  "$basedir/../nitropack/dist/cli/index.mjs" "$@"
fi
