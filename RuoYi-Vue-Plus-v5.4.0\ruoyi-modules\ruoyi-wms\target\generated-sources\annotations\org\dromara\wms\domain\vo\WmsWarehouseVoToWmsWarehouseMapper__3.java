package org.dromara.wms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1039;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.WmsWarehouseToWmsWarehouseVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1039.class,
    uses = {WmsWarehouseToWmsWarehouseVoMapper__3.class},
    imports = {}
)
public interface WmsWarehouseVoToWmsWarehouseMapper__3 extends BaseMapper<WmsWarehouseVo, WmsWarehouse> {
}
