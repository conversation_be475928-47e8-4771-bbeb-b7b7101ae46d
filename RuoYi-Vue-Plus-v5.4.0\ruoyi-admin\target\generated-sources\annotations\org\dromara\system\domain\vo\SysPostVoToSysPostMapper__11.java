package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysPostToSysPostVoMapper__11.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__11 extends BaseMapper<SysPostVo, SysPost> {
}
