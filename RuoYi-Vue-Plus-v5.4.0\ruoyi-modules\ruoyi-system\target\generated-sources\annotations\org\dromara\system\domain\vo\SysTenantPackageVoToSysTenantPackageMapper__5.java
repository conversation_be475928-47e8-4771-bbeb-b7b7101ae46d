package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenantPackage;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysTenantPackageToSysTenantPackageVoMapper__5.class},
    imports = {}
)
public interface SysTenantPackageVoToSysTenantPackageMapper__5 extends BaseMapper<SysTenantPackageVo, SysTenantPackage> {
}
