package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__11;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysLogininforBoToSysLogininforMapper__11.class,SysLogininforVoToSysLogininforMapper__11.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__11 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
