{"version": 3, "sources": ["../../../../../node_modules/.pnpm/zod-defaults@0.1.3_zod@3.25.56/node_modules/zod-defaults/dist/zod-defaults.js"], "sourcesContent": ["\"use strict\";import{z as t}from\"zod\";const a=(e,o)=>e.constructor.name===o.name,n=new Map;n.set(t.ZodBoolean.name,()=>!1),n.set(t.ZodNumber.name,()=>0),n.set(t.ZodString.name,()=>\"\"),n.set(t.ZodArray.name,()=>[]),n.set(t.ZodRecord.name,()=>({})),n.set(t.ZodDefault.name,e=>e._def.defaultValue()),n.set(t.ZodEffects.name,e=>c(e._def.schema)),n.set(t.ZodOptional.name,e=>a(e._def.innerType,t.ZodDefault)?e._def.innerType._def.defaultValue():void 0),n.set(t.ZodTuple.name,e=>{const o=[];for(const d of e._def.items)o.push(c(d));return o}),n.set(t.ZodEffects.name,e=>c(e._def.schema)),n.set(t.ZodUnion.name,e=>c(e._def.options[0])),n.set(t.ZodObject.name,e=>r(e)),n.set(t.ZodRecord.name,e=>r(e)),n.set(t.ZodIntersection.name,e=>r(e));function c(e){const o=e.constructor.name;if(!n.has(o)){console.warn(\"getSchemaDefaultForField: Unhandled type\",e.constructor.name);return}return n.get(o)(e)}function r(e){if(a(e,t.ZodRecord))return{};if(a(e,t.ZodEffects))return r(e._def.schema);if(a(e,t.ZodIntersection))return{...r(e._def.left),...r(e._def.right)};if(a(e,t.ZodUnion)){for(const o of e._def.options)if(a(o,t.ZodObject))return r(o);return console.warn(\"getSchemaDefaultObject: No object found in union, returning empty object\"),{}}return a(e,t.ZodObject)?Object.fromEntries(Object.entries(e.shape).map(([o,d])=>[o,c(d)]).filter(o=>o[1]!==void 0)):(console.warn(`getSchemaDefaultObject: Expected object schema, got ${e.constructor.name}`),{})}function s(e){return r(e)}export{s as getDefaultsForSchema};\n"], "mappings": ";;;;;;AAAqC,IAAM,IAAE,CAAC,GAAE,MAAI,EAAE,YAAY,SAAO,EAAE;AAAtC,IAA2C,IAAE,oBAAI;AAAI,EAAE,IAAI,iBAAE,WAAW,MAAK,MAAI,KAAE,GAAE,EAAE,IAAI,iBAAE,UAAU,MAAK,MAAI,CAAC,GAAE,EAAE,IAAI,iBAAE,UAAU,MAAK,MAAI,EAAE,GAAE,EAAE,IAAI,iBAAE,SAAS,MAAK,MAAI,CAAC,CAAC,GAAE,EAAE,IAAI,iBAAE,UAAU,MAAK,OAAK,CAAC,EAAE,GAAE,EAAE,IAAI,iBAAE,WAAW,MAAK,OAAG,EAAE,KAAK,aAAa,CAAC,GAAE,EAAE,IAAI,iBAAE,WAAW,MAAK,OAAG,EAAE,EAAE,KAAK,MAAM,CAAC,GAAE,EAAE,IAAI,iBAAE,YAAY,MAAK,OAAG,EAAE,EAAE,KAAK,WAAU,iBAAE,UAAU,IAAE,EAAE,KAAK,UAAU,KAAK,aAAa,IAAE,MAAM,GAAE,EAAE,IAAI,iBAAE,SAAS,MAAK,OAAG;AAAC,QAAM,IAAE,CAAC;AAAE,aAAU,KAAK,EAAE,KAAK,MAAM,GAAE,KAAK,EAAE,CAAC,CAAC;AAAE,SAAO;AAAC,CAAC,GAAE,EAAE,IAAI,iBAAE,WAAW,MAAK,OAAG,EAAE,EAAE,KAAK,MAAM,CAAC,GAAE,EAAE,IAAI,iBAAE,SAAS,MAAK,OAAG,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAE,EAAE,IAAI,iBAAE,UAAU,MAAK,OAAG,EAAE,CAAC,CAAC,GAAE,EAAE,IAAI,iBAAE,UAAU,MAAK,OAAG,EAAE,CAAC,CAAC,GAAE,EAAE,IAAI,iBAAE,gBAAgB,MAAK,OAAG,EAAE,CAAC,CAAC;AAAE,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,YAAY;AAAK,MAAG,CAAC,EAAE,IAAI,CAAC,GAAE;AAAC,YAAQ,KAAK,4CAA2C,EAAE,YAAY,IAAI;AAAE;AAAA,EAAM;AAAC,SAAO,EAAE,IAAI,CAAC,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,EAAE,GAAE,iBAAE,SAAS,EAAE,QAAM,CAAC;AAAE,MAAG,EAAE,GAAE,iBAAE,UAAU,EAAE,QAAO,EAAE,EAAE,KAAK,MAAM;AAAE,MAAG,EAAE,GAAE,iBAAE,eAAe,EAAE,QAAM,EAAC,GAAG,EAAE,EAAE,KAAK,IAAI,GAAE,GAAG,EAAE,EAAE,KAAK,KAAK,EAAC;AAAE,MAAG,EAAE,GAAE,iBAAE,QAAQ,GAAE;AAAC,eAAU,KAAK,EAAE,KAAK,QAAQ,KAAG,EAAE,GAAE,iBAAE,SAAS,EAAE,QAAO,EAAE,CAAC;AAAE,WAAO,QAAQ,KAAK,0EAA0E,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,GAAE,iBAAE,SAAS,IAAE,OAAO,YAAY,OAAO,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAE,CAAC,MAAI,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,OAAG,EAAE,CAAC,MAAI,MAAM,CAAC,KAAG,QAAQ,KAAK,uDAAuD,EAAE,YAAY,IAAI,EAAE,GAAE,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC;AAAC;", "names": []}