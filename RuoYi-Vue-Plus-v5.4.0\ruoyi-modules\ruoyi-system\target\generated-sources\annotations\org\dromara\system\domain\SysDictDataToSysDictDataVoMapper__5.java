package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__5;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysDictDataBoToSysDictDataMapper__5.class,SysDictDataVoToSysDictDataMapper__5.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__5 extends BaseMapper<SysDictData, SysDictDataVo> {
}
