var Je=Object.defineProperty,Ge=Object.defineProperties;var Ze=Object.getOwnPropertyDescriptors;var Z=Object.getOwnPropertySymbols;var Ce=Object.prototype.hasOwnProperty,be=Object.prototype.propertyIsEnumerable;var de=(s,t,o)=>t in s?Je(s,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[t]=o,f=(s,t)=>{for(var o in t||(t={}))Ce.call(t,o)&&de(s,o,t[o]);if(Z)for(var o of Z(t))be.call(t,o)&&de(s,o,t[o]);return s},L=(s,t)=>Ge(s,Ze(t));var R=(s,t)=>{var o={};for(var n in s)Ce.call(s,n)&&t.indexOf(n)<0&&(o[n]=s[n]);if(s!=null&&Z)for(var n of Z(s))t.indexOf(n)<0&&be.call(s,n)&&(o[n]=s[n]);return o};var N=(s,t,o)=>de(s,typeof t!="symbol"?t+"":t,o);var X=(s,t,o)=>new Promise((n,d)=>{var c=i=>{try{v(o.next(i))}catch(r){d(r)}},m=i=>{try{v(o.throw(i))}catch(r){d(r)}},v=i=>i.done?n(i.value):Promise.resolve(i.value).then(c,m);v((o=o.apply(s,t)).next())});import{d as M,h,o as p,b as e,e as Qe,g as et,w as y,r as b,a5 as xe,c as ue,B as A,p as _,a as V,f as k,H as tt,n as I,G as O,O as pe,s as De,v as ot,D as st,ac as nt,ax as ce,ay as at,q as lt,j as rt,k as Y,t as j,L as Be,I as fe,a4 as $e,ah as it,ai as dt,az as ct,M as ke}from"../jse/index-index-C-MnMZEz.js";import{w as Se,bG as Ae,cV as ut,cW as pt,cX as ft,a_ as mt,cY as ht,cZ as yt,cu as Te,c_ as vt,c$ as gt,d0 as Ct,d1 as bt,d2 as Bt,co as kt,d3 as wt,cq as _t,d4 as Ot,cs as Mt,bC as xt,aN as we,cn as Dt,ct as $t}from"./bootstrap-DCMzVRvD.js";import{X as St}from"./x-Bfkqqjgb.js";const At=Se("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const Tt=Se("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]),Et=M({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:t}){const d=Ae(s,t);return(c,m)=>(p(),h(e(ut),Qe(et(e(d))),{default:y(()=>[b(c.$slots,"default")]),_:3},16))}}),Lt=["data-dismissable-modal"],It=M({__name:"DialogOverlay",setup(s){pt();const t=xe("DISMISSABLE_MODAL_ID");return(o,n)=>(p(),ue("div",{"data-dismissable-modal":e(t),class:"bg-overlay z-popup inset-0"},null,8,Lt))}}),Pt=M({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},appendTo:{default:"body"},class:{},closeClass:{},closeDisabled:{type:Boolean,default:!1},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},showClose:{type:Boolean,default:!0},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(s,{expose:t,emit:o}){const n=s,d=o,c=A(()=>{const T=n,{class:l,modal:u,open:B,showClose:x}=T;return R(T,["class","modal","open","showClose"])});function m(){return n.appendTo==="body"||n.appendTo===document.body||!n.appendTo}const v=A(()=>m()?"fixed":"absolute"),i=Ae(c,d),r=_(null);function g(l){var u;l.target===((u=r.value)==null?void 0:u.$el)&&(n.open?d("opened"):d("closed"))}return t({getContentRef:()=>r.value}),(l,u)=>(p(),h(e(ft),{to:l.appendTo},{default:y(()=>[V(mt,{name:"fade"},{default:y(()=>[l.open&&l.modal?(p(),h(It,{key:0,style:tt(L(f({},l.zIndex?{zIndex:l.zIndex}:{}),{position:v.value,backdropFilter:l.overlayBlur&&l.overlayBlur>0?`blur(${l.overlayBlur}px)`:"none"})),onClick:u[0]||(u[0]=()=>d("close"))},null,8,["style"])):k("",!0)]),_:1}),V(e(yt),pe({ref_key:"contentRef",ref:r,style:L(f({},l.zIndex?{zIndex:l.zIndex}:{}),{position:v.value}),onAnimationend:g},e(i),{class:e(O)("z-popup bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] w-full p-6 shadow-lg outline-none sm:rounded-xl",n.class)}),{default:y(()=>[b(l.$slots,"default"),l.showClose?(p(),h(e(ht),{key:0,disabled:l.closeDisabled,class:I(e(O)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",n.closeClass)),onClick:u[1]||(u[1]=()=>d("close"))},{default:y(()=>[V(e(St),{class:"h-4 w-4"})]),_:1},8,["disabled","class"])):k("",!0)]),_:3},16,["style","class"])]),_:3},8,["to"]))}}),_e=M({__name:"DialogDescription",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const t=s,o=A(()=>{const m=t,{class:d}=m;return R(m,["class"])}),n=Te(o);return(d,c)=>(p(),h(e(vt),pe(e(n),{class:e(O)("text-muted-foreground text-sm",t.class)}),{default:y(()=>[b(d.$slots,"default")]),_:3},16,["class"]))}}),zt=M({__name:"DialogFooter",props:{class:{}},setup(s){const t=s;return(o,n)=>(p(),ue("div",{class:I(e(O)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[b(o.$slots,"default")],2))}}),Ft=M({__name:"DialogHeader",props:{class:{}},setup(s){const t=s;return(o,n)=>(p(),ue("div",{class:I(e(O)("flex flex-col gap-y-1.5 text-center sm:text-left",t.class))},[b(o.$slots,"default")],2))}}),Oe=M({__name:"DialogTitle",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const t=s,o=A(()=>{const m=t,{class:d}=m;return R(m,["class"])}),n=Te(o);return(d,c)=>(p(),h(e(gt),pe(e(n),{class:e(O)("text-lg font-semibold leading-none tracking-tight",t.class)}),{default:y(()=>[b(d.$slots,"default")]),_:3},16,["class"]))}});function Vt(s,t,o){const n=De({offsetX:0,offsetY:0}),d=_(!1),c=r=>{const g=r.clientX,l=r.clientY;if(!s.value)return;const u=s.value.getBoundingClientRect(),{offsetX:B,offsetY:x}=n,w=u.left,T=u.top,Q=u.width,H=u.height,q=document.documentElement,ee=q.clientWidth,te=q.clientHeight,oe=-w+B,se=-T+x,P=ee-w-Q+B,ne=te-T-H+x,K=W=>{let $=B+W.clientX-g,z=x+W.clientY-l;$=Math.min(Math.max($,oe),P),z=Math.min(Math.max(z,se),ne),n.offsetX=$,n.offsetY=z,s.value&&(s.value.style.transform=`translate(${$}px, ${z}px)`,d.value=!0)},U=()=>{d.value=!1,document.removeEventListener("mousemove",K),document.removeEventListener("mouseup",U)};document.addEventListener("mousemove",K),document.addEventListener("mouseup",U)},m=()=>{const r=ce(t);r&&s.value&&r.addEventListener("mousedown",c)},v=()=>{const r=ce(t);r&&s.value&&r.removeEventListener("mousedown",c)},i=()=>{n.offsetX=0,n.offsetY=0;const r=ce(s);r&&(r.style.transform="none")};return ot(()=>{st(()=>{o.value?m():v()})}),nt(()=>{v()}),{dragging:d,resetPosition:i,transform:n}}const Rt=M({__name:"modal",props:{modalApi:{default:void 0},appendToMain:{type:Boolean,default:!1},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmDisabled:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean},title:{},titleTooltip:{},zIndex:{}},setup(s){var G,ge;const t=s,o=Ct.getComponents(),n=_(),d=_(),c=_(),m=_(),v=_(),i=at();$e("DISMISSABLE_MODAL_ID",i);const{$t:r}=bt(),{isMobile:g}=Bt(),l=(ge=(G=t.modalApi)==null?void 0:G.useStore)==null?void 0:ge.call(G),{appendToMain:u,bordered:B,cancelText:x,centered:w,class:T,closable:Q,closeOnClickModal:H,closeOnPressEscape:q,confirmDisabled:ee,confirmLoading:te,confirmText:oe,contentClass:se,description:P,destroyOnClose:ne,draggable:K,footer:U,footerClass:W,fullscreen:$,fullscreenButton:z,header:ae,headerClass:Ee,loading:me,modal:Le,openAutoFocus:Ie,overlayBlur:Pe,showCancelButton:ze,showConfirmButton:Fe,submitting:D,title:J,titleTooltip:he,zIndex:Ve}=kt(t,l),le=A(()=>$.value&&ae.value||g.value),ye=A(()=>K.value&&!le.value&&ae.value),{dragging:Re,transform:Ne}=Vt(c,m,ye),re=_(!1),ie=_(!0);lt(()=>{var a;return(a=l==null?void 0:l.value)==null?void 0:a.isOpen},a=>X(null,null,function*(){if(a){if(ie.value=!1,re.value||(re.value=!0),yield fe(),!n.value)return;const C=n.value.getContentRef();c.value=C.$el;const{offsetX:F,offsetY:E}=Ne;c.value.style.transform=`translate(${F}px, ${E}px)`}}),{immediate:!0});function Xe(){var a;(a=t.modalApi)==null||a.setState(C=>L(f({},C),{fullscreen:!$.value}))}function Ye(a){(!H.value||D.value)&&(a.preventDefault(),a.stopPropagation())}function je(a){(!q.value||D.value)&&a.preventDefault()}function He(a){Ie.value||a==null||a.preventDefault()}function qe(a){const C=a.target,F=C==null?void 0:C.dataset.dismissableModal;(!H.value||F!==i||D.value)&&(a.preventDefault(),a.stopPropagation())}function ve(a){a.preventDefault(),a.stopPropagation()}const Ke=A(()=>u.value?`#${wt}>div:not(.absolute)>div`:void 0),Ue=A(()=>!e(ne)&&e(re));function We(){var a;ie.value=!0,(a=t.modalApi)==null||a.onClosed()}return(a,C)=>{var F;return p(),h(e(Et),{modal:!1,open:(F=e(l))==null?void 0:F.isOpen,"onUpdate:open":C[3]||(C[3]=()=>{var E;return e(D)||(E=a.modalApi)==null?void 0:E.close()})},{default:y(()=>{var E;return[V(e(Pt),{ref_key:"contentRef",ref:n,"append-to":Ke.value,class:I(e(O)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0 sm:rounded-[var(--radius)]",e(T),{"border-border border":e(B),"shadow-3xl":!e(B),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":le.value,"top-1/2 !-translate-y-1/2":e(w)&&!le.value,"duration-300":!e(Re),hidden:ie.value})),"force-mount":Ue.value,modal:e(Le),open:(E=e(l))==null?void 0:E.isOpen,"show-close":e(Q),"z-index":e(Ve),"overlay-blur":e(Pe),"close-class":"top-3",onCloseAutoFocus:ve,onClosed:We,"close-disabled":e(D),onEscapeKeyDown:je,onFocusOutside:ve,onInteractOutside:Ye,onOpenAutoFocus:He,onOpened:C[2]||(C[2]=()=>{var S;return(S=a.modalApi)==null?void 0:S.onOpened()}),onPointerDownOutside:qe},{default:y(()=>[V(e(Ft),{ref_key:"headerRef",ref:m,class:I(e(O)("px-5 py-4",{"border-b":e(B),hidden:!e(ae),"cursor-move select-none":ye.value},e(Ee)))},{default:y(()=>[e(J)?(p(),h(e(Oe),{key:0,class:"text-left"},{default:y(()=>[b(a.$slots,"title",{},()=>[Y(j(e(J))+" ",1),e(he)?b(a.$slots,"titleTooltip",{key:0},()=>[V(e(_t),{"trigger-class":"pb-1"},{default:y(()=>[Y(j(e(he)),1)]),_:1})]):k("",!0)])]),_:3})):k("",!0),e(P)?(p(),h(e(_e),{key:1},{default:y(()=>[b(a.$slots,"description",{},()=>[Y(j(e(P)),1)])]),_:3})):k("",!0),!e(J)||!e(P)?(p(),h(e(Ot),{key:2},{default:y(()=>[e(J)?k("",!0):(p(),h(e(Oe),{key:0})),e(P)?k("",!0):(p(),h(e(_e),{key:1}))]),_:1})):k("",!0)]),_:3},8,["class"]),rt("div",{ref_key:"wrapperRef",ref:d,class:I(e(O)("relative min-h-40 flex-1 overflow-y-auto p-3",e(se),{"pointer-events-none":e(me)||e(D)}))},[b(a.$slots,"default")],2),e(me)||e(D)?(p(),h(e(Mt),{key:0,spinning:""})):k("",!0),e(z)?(p(),h(e(xt),{key:1,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:Xe},{default:y(()=>[e($)?(p(),h(e(Tt),{key:0,class:"size-3.5"})):(p(),h(e(At),{key:1,class:"size-3.5"}))]),_:1})):k("",!0),e(U)?(p(),h(e(zt),{key:2,ref_key:"footerRef",ref:v,class:I(e(O)("flex-row items-center justify-end p-2",{"border-t":e(B)},e(W)))},{default:y(()=>[b(a.$slots,"prepend-footer"),b(a.$slots,"footer",{},()=>[e(ze)?(p(),h(Be(e(o).DefaultButton||e(we)),{key:0,variant:"ghost",disabled:e(D),onClick:C[0]||(C[0]=()=>{var S;return(S=a.modalApi)==null?void 0:S.onCancel()})},{default:y(()=>[b(a.$slots,"cancelText",{},()=>[Y(j(e(x)||e(r)("cancel")),1)])]),_:3},8,["disabled"])):k("",!0),b(a.$slots,"center-footer"),e(Fe)?(p(),h(Be(e(o).PrimaryButton||e(we)),{key:1,disabled:e(ee),loading:e(te)||e(D),onClick:C[1]||(C[1]=()=>{var S;return(S=a.modalApi)==null?void 0:S.onConfirm()})},{default:y(()=>[b(a.$slots,"confirmText",{},()=>[Y(j(e(oe)||e(r)("confirm")),1)])]),_:3},8,["disabled","loading"])):k("",!0)]),b(a.$slots,"append-footer")]),_:3},8,["class"])):k("",!0)]),_:3},8,["append-to","class","force-mount","modal","open","show-close","z-index","overlay-blur","close-disabled"])]}),_:3},8,["open"])}}});class Nt{constructor(t={}){N(this,"sharedData",{payload:{}});N(this,"store");N(this,"api");N(this,"state");const l=t,{connectedComponent:o,onBeforeClose:n,onCancel:d,onClosed:c,onConfirm:m,onOpenChange:v,onOpened:i}=l,r=R(l,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),g={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmDisabled:!1,confirmLoading:!1,contentClass:"",destroyOnClose:!0,draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new Dt(f(f({},g),r),{onUpdate:()=>{var B,x,w;const u=this.store.state;(u==null?void 0:u.isOpen)===((B=this.state)==null?void 0:B.isOpen)?this.state=u:(this.state=u,(w=(x=this.api).onOpenChange)==null||w.call(x,!!(u!=null&&u.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:n,onCancel:d,onClosed:c,onConfirm:m,onOpenChange:v,onOpened:i},it(this)}close(){return X(this,null,function*(){var o,n,d;((d=yield(n=(o=this.api).onBeforeClose)==null?void 0:n.call(o))!=null?d:!0)&&this.store.setState(c=>L(f({},c),{isOpen:!1,submitting:!1}))})}getData(){var t,o;return(o=(t=this.sharedData)==null?void 0:t.payload)!=null?o:{}}lock(t=!0){return this.setState({submitting:t})}modalLoading(t){this.setState({confirmLoading:t,loading:t})}onCancel(){var t,o;this.api.onCancel?(o=(t=this.api).onCancel)==null||o.call(t):this.close()}onClosed(){var t,o;this.state.isOpen||(o=(t=this.api).onClosed)==null||o.call(t)}onConfirm(){var t,o;(o=(t=this.api).onConfirm)==null||o.call(t)}onOpened(){var t,o;this.state.isOpen&&((o=(t=this.api).onOpened)==null||o.call(t))}open(){this.store.setState(t=>L(f({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return dt(t)?this.store.setState(t):this.store.setState(o=>f(f({},o),t)),this}unlock(){return this.lock(!1)}}const Me=Symbol("VBEN_MODAL_INJECT"),Xt={};function Ut(s={}){var v;const{connectedComponent:t}=s;if(t){const i=De({}),r=_(!0),g=M((l,{attrs:u,slots:B})=>($e(Me,{extendApi(w){Object.setPrototypeOf(i,w)},options:s,reCreateModal(){return X(this,null,function*(){r.value=!1,yield fe(),r.value=!0})}}),Yt(i,f(f(f({},l),u),B)),()=>ke(r.value?t:"div",f(f({},l),u),B)),{name:"VbenParentModal",inheritAttrs:!1});return ct(()=>{var l;(l=i==null?void 0:i.close)==null||l.call(i)}),[g,i]}const o=xe(Me,{}),n=f(f(f({},Xt),o.options),s);n.onOpenChange=i=>{var r,g,l;(r=s.onOpenChange)==null||r.call(s,i),(l=(g=o.options)==null?void 0:g.onOpenChange)==null||l.call(g,i)},n.onClosed=()=>{var i,r;(i=s.onClosed)==null||i.call(s),n.destroyOnClose&&((r=o.reCreateModal)==null||r.call(o))};const d=new Nt(n),c=d;c.useStore=i=>$t(d.store,i);const m=M((i,{attrs:r,slots:g})=>()=>ke(Rt,L(f(f({},i),r),{modalApi:c}),g),{name:"VbenModal",inheritAttrs:!1});return(v=o.extendApi)==null||v.call(o,c),[m,c]}function Yt(s,t){return X(this,null,function*(){var d;if(!t||Object.keys(t).length===0)return;yield fe();const o=(d=s==null?void 0:s.store)==null?void 0:d.state;if(!o)return;const n=new Set(Object.keys(o));for(const c of Object.keys(t))n.has(c)&&!["class"].includes(c)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${c}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}export{Ut as u};
