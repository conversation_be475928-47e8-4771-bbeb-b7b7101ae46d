import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-ph@1.2.5/node_modules/@iconify/icons-ph/users-light.js
var require_users_light = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-ph@1.2.5/node_modules/@iconify/icons-ph/users-light.js"(exports) {
    var data = {
      "width": 256,
      "height": 256,
      "body": '<path fill="currentColor" d="M112.6 158.43a58 58 0 1 0-57.2 0a93.83 93.83 0 0 0-50.19 38.29a6 6 0 0 0 10.05 6.56a82 82 0 0 1 137.48 0a6 6 0 0 0 10-6.56a93.83 93.83 0 0 0-50.14-38.29ZM38 108a46 46 0 1 1 46 46a46.06 46.06 0 0 1-46-46Zm211 97a6 6 0 0 1-8.3-1.74A81.8 81.8 0 0 0 172 166a6 6 0 0 1 0-12a46 46 0 1 0-17.08-88.73a6 6 0 1 1-4.46-11.14a58 58 0 0 1 50.14 104.3a93.83 93.83 0 0 1 50.19 38.29A6 6 0 0 1 249 205Z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_users_light();
//# sourceMappingURL=@iconify_icons-ph_users-light.js.map
