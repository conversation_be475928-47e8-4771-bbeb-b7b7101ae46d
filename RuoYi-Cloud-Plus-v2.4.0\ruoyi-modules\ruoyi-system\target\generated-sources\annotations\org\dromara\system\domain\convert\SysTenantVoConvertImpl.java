package org.dromara.system.domain.convert;

import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.vo.RemoteTenantVo;
import org.dromara.system.domain.vo.SysTenantVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T10:28:19+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class SysTenantVoConvertImpl implements SysTenantVoConvert {

    @Override
    public RemoteTenantVo convert(SysTenantVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteTenantVo remoteTenantVo = new RemoteTenantVo();

        remoteTenantVo.setId( arg0.getId() );
        remoteTenantVo.setTenantId( arg0.getTenantId() );
        remoteTenantVo.setContactUserName( arg0.getContactUserName() );
        remoteTenantVo.setContactPhone( arg0.getContactPhone() );
        remoteTenantVo.setCompanyName( arg0.getCompanyName() );
        remoteTenantVo.setLicenseNumber( arg0.getLicenseNumber() );
        remoteTenantVo.setAddress( arg0.getAddress() );
        remoteTenantVo.setDomain( arg0.getDomain() );
        remoteTenantVo.setIntro( arg0.getIntro() );
        remoteTenantVo.setRemark( arg0.getRemark() );
        remoteTenantVo.setPackageId( arg0.getPackageId() );
        remoteTenantVo.setExpireTime( arg0.getExpireTime() );
        remoteTenantVo.setAccountCount( arg0.getAccountCount() );
        remoteTenantVo.setStatus( arg0.getStatus() );

        return remoteTenantVo;
    }

    @Override
    public RemoteTenantVo convert(SysTenantVo arg0, RemoteTenantVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setContactUserName( arg0.getContactUserName() );
        arg1.setContactPhone( arg0.getContactPhone() );
        arg1.setCompanyName( arg0.getCompanyName() );
        arg1.setLicenseNumber( arg0.getLicenseNumber() );
        arg1.setAddress( arg0.getAddress() );
        arg1.setDomain( arg0.getDomain() );
        arg1.setIntro( arg0.getIntro() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setPackageId( arg0.getPackageId() );
        arg1.setExpireTime( arg0.getExpireTime() );
        arg1.setAccountCount( arg0.getAccountCount() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
