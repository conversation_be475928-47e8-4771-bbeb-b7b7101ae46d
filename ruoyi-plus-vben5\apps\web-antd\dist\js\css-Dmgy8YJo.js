function c(){const n=document.documentElement.clientWidth,t=window.innerHeight||document.documentElement.clientHeight;return{width:n,height:t}}function i(n){const t=n.getBoundingClientRect(),e=document.documentElement;return{left:t.left+(window.scrollX||e.scrollLeft)-(e.clientLeft||document.body.clientLeft||0),top:t.top+(window.scrollY||e.scrollTop)-(e.clientTop||document.body.clientTop||0)}}function l(n){return Object.keys(n).reduce((t,e)=>{const o=n[e];return typeof o=="undefined"||o===null||(t+=`${e}: ${n[e]};`),t},"")}export{i as a,c as g,l as s};
