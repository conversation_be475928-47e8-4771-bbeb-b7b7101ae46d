2025-06-15 08:42:26 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 20608 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 08:42:26 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 08:42:27 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 08:42:27 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 08:42:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1078 ms
2025-06-15 08:42:27 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 08:42:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 08:42:27 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 08:42:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 08:42:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 08:42:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 08:42:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 08:42:28 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 08:42:28 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 3.068 seconds (process running for 3.632)
2025-06-15 08:42:28 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 08:42:28 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 08:42:28 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 08:42:29 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 08:42:29 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 08:42:38 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 08:43:00 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 08:46:37 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 12996 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 08:46:37 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 08:46:38 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 08:46:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 08:46:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 910 ms
2025-06-15 08:46:39 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 08:46:39 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 08:46:39 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 08:46:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 08:46:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 08:46:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 08:46:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 08:46:39 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 08:46:39 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.532 seconds (process running for 3.053)
2025-06-15 08:46:40 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 08:46:40 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 08:46:40 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 08:46:40 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 08:46:40 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 08:46:48 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 08:47:10 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 14:09:42 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 21064 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:09:42 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 14:09:43 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 14:09:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:09:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 923 ms
2025-06-15 14:09:43 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 14:09:43 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 14:09:43 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 14:09:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 14:09:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 14:09:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 14:09:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 14:09:44 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 14:09:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.562 seconds (process running for 3.056)
2025-06-15 14:09:44 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 14:09:44 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 14:09:44 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 14:09:44 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 14:09:44 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 14:09:52 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 14:10:11 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 14:11:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 26368 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:11:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 14:11:44 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 14:11:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:11:44 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 872 ms
2025-06-15 14:11:45 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 14:11:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 14:11:45 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 14:11:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 14:11:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 14:11:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 14:11:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 14:11:46 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 14:11:46 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.428 seconds (process running for 2.846)
2025-06-15 14:11:46 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 14:11:46 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 14:11:46 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 14:11:46 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 14:11:46 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 14:11:55 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 14:12:15 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
