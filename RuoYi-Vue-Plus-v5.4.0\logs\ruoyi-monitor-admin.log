2025-06-15 08:42:26 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 20608 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 08:42:26 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 08:42:27 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 08:42:27 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 08:42:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1078 ms
2025-06-15 08:42:27 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 08:42:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 08:42:27 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 08:42:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 08:42:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 08:42:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 08:42:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 08:42:28 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 08:42:28 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 3.068 seconds (process running for 3.632)
2025-06-15 08:42:28 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 08:42:28 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 08:42:28 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 08:42:29 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 08:42:29 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 08:42:38 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 08:43:00 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 08:44:11 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 08:46:37 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 12996 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 08:46:37 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 08:46:38 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 08:46:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 08:46:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 910 ms
2025-06-15 08:46:39 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 08:46:39 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 08:46:39 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 08:46:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 08:46:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 08:46:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 08:46:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 08:46:39 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 08:46:39 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.532 seconds (process running for 3.053)
2025-06-15 08:46:40 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 08:46:40 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 08:46:40 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 08:46:40 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 08:46:40 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 08:46:48 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 08:47:10 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 13:46:46 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 14:09:42 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 21064 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:09:42 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 14:09:43 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 14:09:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:09:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 923 ms
2025-06-15 14:09:43 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 14:09:43 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 14:09:43 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 14:09:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 14:09:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 14:09:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 14:09:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 14:09:44 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 14:09:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.562 seconds (process running for 3.056)
2025-06-15 14:09:44 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 14:09:44 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 14:09:44 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 14:09:44 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 14:09:44 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 14:09:52 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 14:10:11 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 14:10:29 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 14:11:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 26368 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:11:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 14:11:44 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 14:11:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:11:44 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 872 ms
2025-06-15 14:11:45 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 14:11:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 14:11:45 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 14:11:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 14:11:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 14:11:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 14:11:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 14:11:46 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 14:11:46 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.428 seconds (process running for 2.846)
2025-06-15 14:11:46 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 14:11:46 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 14:11:46 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 14:11:46 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 14:11:46 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 14:11:55 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 14:12:15 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 14:16:15 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 14:49:17 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 31472 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 14:49:17 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 14:49:18 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 14:49:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 14:49:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 922 ms
2025-06-15 14:49:19 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 14:49:19 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 14:49:19 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 14:49:19 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 14:49:19 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 14:49:19 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 14:49:19 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 14:49:19 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 14:49:19 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.555 seconds (process running for 2.995)
2025-06-15 14:49:20 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 14:49:20 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 14:49:20 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 14:49:20 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 14:49:20 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 14:49:31 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 14:49:58 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 14:51:18 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 14:51:18 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 14:51:18 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 14:51:18 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 14:51:19 [reactor-http-nio-5] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=b8d2fbf2a416, version=3, registration=Registration(name=ruoyi-snailjob-server, managementUrl=http://162.168.2.37:8800/snail-job/actuator, healthUrl=http://162.168.2.37:8800/snail-job/actuator/health, serviceUrl=http://162.168.2.37:8800/snail-job, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=755766726656, free=271551901696, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-06-15T06:49:31.020068500Z, info=Info(values={build={artifact=snail-job-server-starter, name=snail-job-server-starter, time=2025-05-17T02:09:17.034Z, version=1.5.0, group=com.aizuda}}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.37:8800/snail-job/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.37:8800/snail-job/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.37:8800/snail-job/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.37:8800/snail-job/actuator/health), env=Endpoint(id=env, url=http://162.168.2.37:8800/snail-job/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.37:8800/snail-job/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.37:8800/snail-job/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.37:8800/snail-job/actuator/mappings), beans=Endpoint(id=beans, url=http://162.168.2.37:8800/snail-job/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.37:8800/snail-job/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.37:8800/snail-job/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.37:8800/snail-job/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.37:8800/snail-job/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.37:8800/snail-job/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.37:8800/snail-job/actuator/info)}), buildVersion=1.5.0, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 14:51:19 [reactor-http-nio-9] WARN  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=560a8b7238d8, version=2, registration=Registration(name=ruoyi-monitor-admin, managementUrl=http://162.168.2.37:9090/actuator, healthUrl=http://162.168.2.37:9090/actuator/health, serviceUrl=http://162.168.2.37:9090/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, diskSpace={status=UP, details={total=755766726656, free=271551905792, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-06-15T06:49:20.577142200Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.37:9090/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.37:9090/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.37:9090/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.37:9090/actuator/health), env=Endpoint(id=env, url=http://162.168.2.37:9090/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.37:9090/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.37:9090/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.37:9090/actuator/mappings), beans=Endpoint(id=beans, url=http://162.168.2.37:9090/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.37:9090/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.37:9090/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.37:9090/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.37:9090/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.37:9090/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.37:9090/actuator/info)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET info [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 14:51:19 [reactor-http-nio-6] WARN  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=d1605a265aba, version=2, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://162.168.2.37:8080/actuator, healthUrl=http://162.168.2.37:8080/actuator/health, serviceUrl=http://162.168.2.37:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=755766726656, free=271551885312, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=3.0.504}}}), statusTimestamp=2025-06-15T06:49:58.134225900Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.37:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.37:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.37:8080/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.37:8080/actuator/health), env=Endpoint(id=env, url=http://162.168.2.37:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.37:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.37:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.37:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://162.168.2.37:8080/actuator/startup), beans=Endpoint(id=beans, url=http://162.168.2.37:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.37:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.37:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.37:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.37:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.37:8080/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.37:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET info [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 14:51:19 [reactor-http-nio-7] WARN  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=b8d2fbf2a416, version=3, registration=Registration(name=ruoyi-snailjob-server, managementUrl=http://162.168.2.37:8800/snail-job/actuator, healthUrl=http://162.168.2.37:8800/snail-job/actuator/health, serviceUrl=http://162.168.2.37:8800/snail-job, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=755766726656, free=271551901696, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-06-15T06:49:31.020068500Z, info=Info(values={build={artifact=snail-job-server-starter, name=snail-job-server-starter, time=2025-05-17T02:09:17.034Z, version=1.5.0, group=com.aizuda}}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.37:8800/snail-job/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.37:8800/snail-job/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.37:8800/snail-job/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.37:8800/snail-job/actuator/health), env=Endpoint(id=env, url=http://162.168.2.37:8800/snail-job/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.37:8800/snail-job/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.37:8800/snail-job/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.37:8800/snail-job/actuator/mappings), beans=Endpoint(id=beans, url=http://162.168.2.37:8800/snail-job/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.37:8800/snail-job/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.37:8800/snail-job/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.37:8800/snail-job/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.37:8800/snail-job/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.37:8800/snail-job/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.37:8800/snail-job/actuator/info)}), buildVersion=1.5.0, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET info [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 14:51:19 [reactor-http-nio-8] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=560a8b7238d8, version=2, registration=Registration(name=ruoyi-monitor-admin, managementUrl=http://162.168.2.37:9090/actuator, healthUrl=http://162.168.2.37:9090/actuator/health, serviceUrl=http://162.168.2.37:9090/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, diskSpace={status=UP, details={total=755766726656, free=271551905792, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-06-15T06:49:20.577142200Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.37:9090/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.37:9090/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.37:9090/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.37:9090/actuator/health), env=Endpoint(id=env, url=http://162.168.2.37:9090/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.37:9090/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.37:9090/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.37:9090/actuator/mappings), beans=Endpoint(id=beans, url=http://162.168.2.37:9090/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.37:9090/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.37:9090/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.37:9090/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.37:9090/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.37:9090/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.37:9090/actuator/info)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 14:51:19 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【OFFLINE】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 14:51:19 [reactor-http-nio-8] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【OFFLINE】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 15:39:34 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 30336 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-15 15:39:34 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-15 15:39:35 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-15 15:39:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 15:39:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 952 ms
2025-06-15 15:39:35 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-15 15:39:35 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-15 15:39:35 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-15 15:39:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 15:39:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 15:39:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 15:39:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 15:39:36 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-15 15:39:36 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.715 seconds (process running for 3.167)
2025-06-15 15:39:36 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 15:39:36 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 15:39:36 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 15:39:36 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 560a8b7238d8
2025-06-15 15:39:36 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【560a8b7238d8】, 状态【UP】, 服务URL【http://162.168.2.37:9090/】
2025-06-15 15:39:50 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【b8d2fbf2a416】, 状态【UP】, 服务URL【http://162.168.2.37:8800/snail-job】
2025-06-15 15:40:17 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【d1605a265aba】, 状态【UP】, 服务URL【http://162.168.2.37:8080/】
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 16:11:58 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
