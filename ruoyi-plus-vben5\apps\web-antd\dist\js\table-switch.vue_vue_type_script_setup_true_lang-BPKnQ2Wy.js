var m=(d,h,a)=>new Promise((r,i)=>{var f=t=>{try{n(a.next(t))}catch(o){i(o)}},c=t=>{try{n(a.throw(t))}catch(o){i(o)}},n=t=>t.done?r(t.value):Promise.resolve(t.value).then(f,c);n((a=a.apply(d,h)).next())});import{$ as g,i as p,an as b}from"./bootstrap-DCMzVRvD.js";import T from"./index-D-hwdOI6.js";import{d as B,m as C,B as y,u as x,p as V,h as w,o as M,b as S,O as N}from"../jse/index-index-C-MnMZEz.js";const _=B({__name:"table-switch",props:C({checkedText:{default:void 0},unCheckedText:{default:void 0},checkedValue:{type:[Boolean,Number,String],default:"0"},unCheckedValue:{type:[Boolean,Number,String],default:"1"},disabled:{type:Boolean},api:{},confirm:{type:Boolean,default:!1},confirmText:{type:Function,default:void 0}},{value:{type:[Boolean,Number,String],default:!1},valueModifiers:{}}),emits:C(["reload"],["update:value"]),setup(d,{emit:h}){const a=d,r=h,i=y(()=>{var e;return(e=a.checkedText)!=null?e:g("pages.common.enable")}),f=y(()=>{var e;return(e=a.unCheckedText)!=null?e:g("pages.common.disable")}),c=x(d,"value"),n=V(!1);function t(e,l){const s=p(a.confirmText)?a.confirmText(e):"确认要更新状态吗？";b.confirm({title:"提示",content:s,centered:!0,onOk:()=>m(null,null,function*(){try{n.value=!0;const{api:u}=a;p(u)&&(yield u()),r("reload")}catch(u){c.value=l}finally{n.value=!1}}),onCancel:()=>{c.value=l}})}function o(e,l){return m(this,null,function*(){l.stopPropagation();const{checkedValue:s,unCheckedValue:u}=a,k=e===s?u:s;c.value=e;const{api:v}=a;try{if(n.value=!0,a.confirm){t(e,k);return}p(v)&&(yield v()),r("reload")}catch($){c.value=k}finally{n.value=!1}})}return(e,l)=>(M(),w(S(T),N(e.$attrs,{loading:n.value,disabled:e.disabled,checked:c.value,"checked-children":i.value,"checked-value":e.checkedValue,"un-checked-children":f.value,"un-checked-value":e.unCheckedValue,onChange:o}),null,16,["loading","disabled","checked","checked-children","checked-value","un-checked-children","un-checked-value"]))}});export{_};
