{"doc": " 任务视图\n\n <AUTHOR>\n", "fields": [{"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "tenantId", "doc": " 租户ID\n"}, {"name": "delFlag", "doc": " 删除标记\n"}, {"name": "definitionId", "doc": " 对应flow_definition表的id\n"}, {"name": "instanceId", "doc": " 流程实例表id\n"}, {"name": "flowName", "doc": " 流程定义名称\n"}, {"name": "businessId", "doc": " 业务id\n"}, {"name": "nodeCode", "doc": " 节点编码\n"}, {"name": "nodeName", "doc": " 节点名称\n"}, {"name": "nodeType", "doc": " 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\n"}, {"name": "permissionList", "doc": " 权限标识 permissionFlag的list形式\n"}, {"name": "userList", "doc": " 流程用户列表\n"}, {"name": "formCustom", "doc": " 审批表单是否自定义（Y是 N否）\n"}, {"name": "formPath", "doc": " 审批表单\n"}, {"name": "flowCode", "doc": " 流程定义编码\n"}, {"name": "version", "doc": " 流程版本号\n"}, {"name": "flowStatus", "doc": " 流程状态\n"}, {"name": "category", "doc": " 流程分类id\n"}, {"name": "categoryName", "doc": " 流程分类名称\n"}, {"name": "flowStatusName", "doc": " 流程状态\n"}, {"name": "type", "doc": " 办理人类型\n"}, {"name": "assigneeIds", "doc": " 办理人ids\n"}, {"name": "assignee<PERSON><PERSON>s", "doc": " 办理人名称\n"}, {"name": "processedBy", "doc": " 抄送人id\n"}, {"name": "processedByName", "doc": " 抄送人名称\n"}, {"name": "nodeRatio", "doc": " 流程签署比例值 大于0为票签，会签\n"}, {"name": "createBy", "doc": " 申请人id\n"}, {"name": "createByName", "doc": " 申请人名称\n"}, {"name": "applyNode", "doc": " 是否为申请人节点\n"}, {"name": "buttonList", "doc": " 按钮权限\n"}], "enumConstants": [], "methods": [], "constructors": []}