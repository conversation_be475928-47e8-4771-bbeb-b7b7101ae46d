var re=(e,t,r)=>new Promise((n,o)=>{var d=s=>{try{u(r.next(s))}catch(c){o(c)}},i=s=>{try{u(r.throw(s))}catch(c){o(c)}},u=s=>s.done?n(s.value):Promise.resolve(s.value).then(d,i);u((r=r.apply(e,t)).next())});import"./vxe-table-DzEj5Fop.js";import{c as Ne,n as Ae}from"./menu-select-table.vue_vue_type_style_index_0_scoped_96b02cc5_lang-Be00nslj.js";import{bT as je,bU as Me,bV as Fe,dC as De,dD as pe,dE as Le,cR as fe,_ as T,cx as R,bj as ue,bk as ke,bO as _,g as y,c as E,dr as ze,o as ie,dF as $e,e as Ve,bd as ve,b9 as Ge,dG as He,b<PERSON> as Ke,aW as We,ak as he,dH as ge,bf as Ue,dI as Xe,j as qe,m as Je,b2 as Ze,dJ as Ye,r as Qe,aD as et,p as tt,cB as nt,dK as me,dL as be,z as ot}from"./bootstrap-DCMzVRvD.js";import{e as rt,t as lt,f as st}from"./tree-DFBawhPd.js";import{D as Se,v as Pe,q as L,ac as at,B as P,d as j,a as l,k as B,F as te,p as U,aA as ce,C as Te,X as J,aB as it,m as ut,u as ct,P as dt,I as pt,l as ft,c as ye,o as le,w as A,j as se,K as vt,h as ht,b as D,t as Z}from"../jse/index-index-C-MnMZEz.js";import{u as Y}from"./uniq-CCKK3QTo.js";import gt from"./index-D2_dYV5Z.js";import mt from"./index-BeyziwLP.js";import{u as bt}from"./use-vxe-grid-BC7vZzEr.js";import{a as yt}from"./Group-oWwucTzK.js";import{A as Ct}from"./index-kC0HFDdy.js";var xt=200;function wt(e,t,r,n){var o=-1,d=Me,i=!0,u=e.length,s=[],c=t.length;if(!u)return s;t.length>=xt&&(d=Fe,i=!1,t=new je(t));e:for(;++o<u;){var a=e[o],v=a;if(a=a!==0?a:0,i&&v===v){for(var b=c;b--;)if(t[b]===v)continue e;s.push(a)}else d(t,v,n)||s.push(a)}return s}var kt=De(function(e,t){return pe(e)?wt(e,Le(t,1,pe,!0)):[]});function Ce(e){return e===void 0}function $t(e){const t=window.innerWidth||document.documentElement.clientWidth,r=window.innerHeight||document.documentElement.clientHeight,{top:n,right:o,bottom:d,left:i}=e.getBoundingClientRect();return n>=0&&i>=0&&o<=t&&d<=r}function St(e,t,r,n){const[o,d]=fe(void 0);Se(()=>{const a=typeof e.value=="function"?e.value():e.value;d(a||null)},{flush:"post"});const[i,u]=fe(null),s=()=>{if(!t.value){u(null);return}if(o.value){!$t(o.value)&&t.value&&o.value.scrollIntoView(n.value);const{left:a,top:v,width:b,height:S}=o.value.getBoundingClientRect(),C={left:a,top:v,width:b,height:S,radius:0};JSON.stringify(i.value)!==JSON.stringify(C)&&u(C)}else u(null)};return Pe(()=>{L([t,o],()=>{s()},{flush:"post",immediate:!0}),window.addEventListener("resize",s)}),at(()=>{window.removeEventListener("resize",s)}),[P(()=>{var a,v;if(!i.value)return i.value;const b=((a=r.value)===null||a===void 0?void 0:a.offset)||6,S=((v=r.value)===null||v===void 0?void 0:v.radius)||2;return{left:i.value.left-b,top:i.value.top-b,width:i.value.width+b*2,height:i.value.height+b*2,radius:S}}),o]}const Pt=()=>({arrow:R([Boolean,Object]),target:R([String,Function,Object]),title:R([String,Object]),description:R([String,Object]),placement:ke(),mask:R([Object,Boolean],!0),className:{type:String},style:ue(),scrollIntoViewOptions:R([Boolean,Object])}),de=()=>T(T({},Pt()),{prefixCls:{type:String},total:{type:Number},current:{type:Number},onClose:_(),onFinish:_(),renderPanel:_(),onPrev:_(),onNext:_()}),Tt=j({name:"DefaultPanel",inheritAttrs:!1,props:de(),setup(e,t){let{attrs:r}=t;return()=>{const{prefixCls:n,current:o,total:d,title:i,description:u,onClose:s,onPrev:c,onNext:a,onFinish:v}=e;return l("div",y(y({},r),{},{class:E(`${n}-content`,r.class)}),[l("div",{class:`${n}-inner`},[l("button",{type:"button",onClick:s,"aria-label":"Close",class:`${n}-close`},[l("span",{class:`${n}-close-x`},[B("×")])]),l("div",{class:`${n}-header`},[l("div",{class:`${n}-title`},[i])]),l("div",{class:`${n}-description`},[u]),l("div",{class:`${n}-footer`},[l("div",{class:`${n}-sliders`},[d>1?[...Array.from({length:d}).keys()].map((b,S)=>l("span",{key:b,class:S===o?"active":""},null)):null]),l("div",{class:`${n}-buttons`},[o!==0?l("button",{class:`${n}-prev-btn`,onClick:c},[B("Prev")]):null,o===d-1?l("button",{class:`${n}-finish-btn`,onClick:v},[B("Finish")]):l("button",{class:`${n}-next-btn`,onClick:a},[B("Next")])])])])])}}}),It=j({name:"TourStep",inheritAttrs:!1,props:de(),setup(e,t){let{attrs:r}=t;return()=>{const{current:n,renderPanel:o}=e;return l(te,null,[typeof o=="function"?o(T(T({},r),e),n):l(Tt,y(y({},r),e),null)])}}});let xe=0;const Ot=ze();function Rt(){let e;return Ot?(e=xe,xe+=1):e="TEST_OR_SSR",e}function _t(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:U("");const t=`vc_unique_${Rt()}`;return e.value||t}const Q={fill:"transparent","pointer-events":"auto"},Bt=j({name:"TourMask",props:{prefixCls:{type:String},pos:ue(),rootClassName:{type:String},showMask:ie(),fill:{type:String,default:"rgba(0,0,0,0.5)"},open:ie(),animated:R([Boolean,Object]),zIndex:{type:Number}},setup(e,t){let{attrs:r}=t;const n=_t();return()=>{const{prefixCls:o,open:d,rootClassName:i,pos:u,showMask:s,fill:c,animated:a,zIndex:v}=e,b=`${o}-mask-${n}`,S=typeof a=="object"?a==null?void 0:a.placeholder:a;return l($e,{visible:d,autoLock:!0},{default:()=>d&&l("div",y(y({},r),{},{class:E(`${o}-mask`,i,r.class),style:[{position:"fixed",left:0,right:0,top:0,bottom:0,zIndex:v,pointerEvents:"none"},r.style]}),[s?l("svg",{style:{width:"100%",height:"100%"}},[l("defs",null,[l("mask",{id:b},[l("rect",{x:"0",y:"0",width:"100vw",height:"100vh",fill:"white"},null),u&&l("rect",{x:u.left,y:u.top,rx:u.radius,width:u.width,height:u.height,fill:"black",class:S?`${o}-placeholder-animated`:""},null)])]),l("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:c,mask:`url(#${b})`},null),u&&l(te,null,[l("rect",y(y({},Q),{},{x:"0",y:"0",width:"100%",height:u.top}),null),l("rect",y(y({},Q),{},{x:"0",y:"0",width:u.left,height:"100%"}),null),l("rect",y(y({},Q),{},{x:"0",y:u.top+u.height,width:"100%",height:`calc(100vh - ${u.top+u.height}px)`}),null),l("rect",y(y({},Q),{},{x:u.left+u.width,y:"0",width:`calc(100vw - ${u.left+u.width}px)`,height:"100%"}),null)])]):null])})}}}),Et=[0,0],we={left:{points:["cr","cl"],offset:[-8,0]},right:{points:["cl","cr"],offset:[8,0]},top:{points:["bc","tc"],offset:[0,-8]},bottom:{points:["tc","bc"],offset:[0,8]},topLeft:{points:["bl","tl"],offset:[0,-8]},leftTop:{points:["tr","tl"],offset:[-8,0]},topRight:{points:["br","tr"],offset:[0,-8]},rightTop:{points:["tl","tr"],offset:[8,0]},bottomRight:{points:["tr","br"],offset:[0,8]},rightBottom:{points:["bl","br"],offset:[8,0]},bottomLeft:{points:["tl","bl"],offset:[0,8]},leftBottom:{points:["br","bl"],offset:[-8,0]}};function Ie(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const t={};return Object.keys(we).forEach(r=>{t[r]=T(T({},we[r]),{autoArrow:e,targetOffset:Et})}),t}Ie();var Nt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ae={left:"50%",top:"50%",width:"1px",height:"1px"},Oe=()=>{const{builtinPlacements:e,popupAlign:t}=He();return{builtinPlacements:e,popupAlign:t,steps:Ke(),open:ie(),defaultCurrent:{type:Number},current:{type:Number},onChange:_(),onClose:_(),onFinish:_(),mask:R([Boolean,Object],!0),arrow:R([Boolean,Object],!0),rootClassName:{type:String},placement:ke("bottom"),prefixCls:{type:String,default:"rc-tour"},renderPanel:_(),gap:ue(),animated:R([Boolean,Object]),scrollIntoViewOptions:R([Boolean,Object],!0),zIndex:{type:Number,default:1001}}},At=j({name:"Tour",inheritAttrs:!1,props:Ve(Oe(),{}),setup(e){const{defaultCurrent:t,placement:r,mask:n,scrollIntoViewOptions:o,open:d,gap:i,arrow:u}=ce(e),s=U(),[c,a]=ve(0,{value:P(()=>e.current),defaultValue:t.value}),[v,b]=ve(void 0,{value:P(()=>e.open),postState:f=>c.value<0||c.value>=e.steps.length?!1:f!=null?f:!0}),S=Te(v.value);Se(()=>{v.value&&!S.value&&a(0),S.value=v.value});const C=P(()=>e.steps[c.value]||{}),O=P(()=>{var f;return(f=C.value.placement)!==null&&f!==void 0?f:r.value}),x=P(()=>{var f;return v.value&&((f=C.value.mask)!==null&&f!==void 0?f:n.value)}),$=P(()=>{var f;return(f=C.value.scrollIntoViewOptions)!==null&&f!==void 0?f:o.value}),[h,p]=St(P(()=>C.value.target),d,i,$),m=P(()=>p.value?typeof C.value.arrow=="undefined"?u.value:C.value.arrow:!1),g=P(()=>typeof m.value=="object"?m.value.pointAtCenter:!1);L(g,()=>{var f;(f=s.value)===null||f===void 0||f.forcePopupAlign()}),L(c,()=>{var f;(f=s.value)===null||f===void 0||f.forcePopupAlign()});const k=f=>{var w;a(f),(w=e.onChange)===null||w===void 0||w.call(e,f)};return()=>{var f;const{prefixCls:w,steps:I,onClose:N,onFinish:z,rootClassName:V,renderPanel:G,animated:X,zIndex:H}=e,M=Nt(e,["prefixCls","steps","onClose","onFinish","rootClassName","renderPanel","animated","zIndex"]);if(p.value===void 0)return null;const K=()=>{b(!1),N==null||N(c.value)},ne=typeof x.value=="boolean"?x.value:!!x.value,F=typeof x.value=="boolean"?void 0:x.value,_e=()=>p.value||document.body,Be=()=>l(It,y({arrow:m.value,key:"content",prefixCls:w,total:I.length,renderPanel:G,onPrev:()=>{k(c.value-1)},onNext:()=>{k(c.value+1)},onClose:K,current:c.value,onFinish:()=>{K(),z==null||z()}},C.value),null),Ee=P(()=>{const q=h.value||ae,oe={};return Object.keys(q).forEach(W=>{typeof q[W]=="number"?oe[W]=`${q[W]}px`:oe[W]=q[W]}),oe});return v.value?l(te,null,[l(Bt,{zIndex:H,prefixCls:w,pos:h.value,showMask:ne,style:F==null?void 0:F.style,fill:F==null?void 0:F.color,open:v.value,animated:X,rootClassName:V},null),l(Ge,y(y({},M),{},{arrow:!!M.arrow,builtinPlacements:C.value.target?(f=M.builtinPlacements)!==null&&f!==void 0?f:Ie(g.value):void 0,ref:s,popupStyle:C.value.target?C.value.style:T(T({},C.value.style),{position:"fixed",left:ae.left,top:ae.top,transform:"translate(-50%, -50%)"}),popupPlacement:O.value,popupVisible:v.value,popupClassName:E(V,C.value.className),prefixCls:w,popup:Be,forceRender:!1,destroyPopupOnHide:!0,zIndex:H,mask:!1,getTriggerDOMNode:_e}),{default:()=>[l($e,{visible:v.value,autoLock:!0},{default:()=>[l("div",{class:E(V,`${w}-target-placeholder`),style:T(T({},Ee.value),{position:"fixed",pointerEvents:"none"})},null)]})]})]):null}}}),jt=()=>T(T({},Oe()),{steps:{type:Array},prefixCls:{type:String},current:{type:Number},type:{type:String},"onUpdate:current":Function}),Mt=()=>T(T({},de()),{cover:{type:Object},nextButtonProps:{type:Object},prevButtonProps:{type:Object},current:{type:Number},type:{type:String}}),Ft=j({name:"ATourPanel",inheritAttrs:!1,props:Mt(),setup(e,t){let{attrs:r,slots:n}=t;const{current:o,total:d}=ce(e),i=P(()=>o.value===d.value-1),u=c=>{var a;const v=e.prevButtonProps;(a=e.onPrev)===null||a===void 0||a.call(e,c),typeof(v==null?void 0:v.onClick)=="function"&&(v==null||v.onClick())},s=c=>{var a,v;const b=e.nextButtonProps;i.value?(a=e.onFinish)===null||a===void 0||a.call(e,c):(v=e.onNext)===null||v===void 0||v.call(e,c),typeof(b==null?void 0:b.onClick)=="function"&&(b==null||b.onClick())};return()=>{const{prefixCls:c,title:a,onClose:v,cover:b,description:S,type:C,arrow:O}=e,x=e.prevButtonProps,$=e.nextButtonProps;let h;a&&(h=l("div",{class:`${c}-header`},[l("div",{class:`${c}-title`},[a])]));let p;S&&(p=l("div",{class:`${c}-description`},[S]));let m;b&&(m=l("div",{class:`${c}-cover`},[b]));let g;n.indicatorsRender?g=n.indicatorsRender({current:o.value,total:d}):g=[...Array.from({length:d.value}).keys()].map((w,I)=>l("span",{key:w,class:E(I===o.value&&`${c}-indicator-active`,`${c}-indicator`)},null));const k=C==="primary"?"default":"primary",f={type:"default",ghost:C==="primary"};return l(Xe,{componentName:"Tour",defaultLocale:Ue.Tour},{default:w=>{var I;return l("div",y(y({},r),{},{class:E(C==="primary"?`${c}-primary`:"",r.class,`${c}-content`)}),[O&&l("div",{class:`${c}-arrow`,key:"arrow"},null),l("div",{class:`${c}-inner`},[l(We,{class:`${c}-close`,onClick:v},null),m,h,p,l("div",{class:`${c}-footer`},[d.value>1&&l("div",{class:`${c}-indicators`},[g]),l("div",{class:`${c}-buttons`},[o.value!==0?l(he,y(y(y({},f),x),{},{onClick:u,size:"small",class:E(`${c}-prev-btn`,x==null?void 0:x.className)}),{default:()=>[ge(x==null?void 0:x.children)?x.children():(I=x==null?void 0:x.children)!==null&&I!==void 0?I:w.Previous]}):null,l(he,y(y({type:k},$),{},{onClick:s,size:"small",class:E(`${c}-next-btn`,$==null?void 0:$.className)}),{default:()=>[ge($==null?void 0:$.children)?$==null?void 0:$.children():i.value?w.Finish:w.Next]})])])])])}})}}}),Dt=e=>{let{defaultType:t,steps:r,current:n,defaultCurrent:o}=e;const d=U(o==null?void 0:o.value),i=P(()=>n==null?void 0:n.value);L(i,a=>{d.value=a!=null?a:o==null?void 0:o.value},{immediate:!0});const u=a=>{d.value=a},s=P(()=>{var a,v;return typeof d.value=="number"?r&&((v=(a=r.value)===null||a===void 0?void 0:a[d.value])===null||v===void 0?void 0:v.type):t==null?void 0:t.value});return{currentMergedType:P(()=>{var a;return(a=s.value)!==null&&a!==void 0?a:t==null?void 0:t.value}),updateInnerCurrent:u}},Lt=e=>{const{componentCls:t,lineHeight:r,padding:n,paddingXS:o,borderRadius:d,borderRadiusXS:i,colorPrimary:u,colorText:s,colorFill:c,indicatorHeight:a,indicatorWidth:v,boxShadowTertiary:b,tourZIndexPopup:S,fontSize:C,colorBgContainer:O,fontWeightStrong:x,marginXS:$,colorTextLightSolid:h,tourBorderRadius:p,colorWhite:m,colorBgTextHover:g,tourCloseSize:k,motionDurationSlow:f,antCls:w}=e;return[{[t]:T(T({},Qe(e)),{color:s,position:"absolute",zIndex:S,display:"block",visibility:"visible",fontSize:C,lineHeight:r,width:520,"--antd-arrow-background-color":O,"&-pure":{maxWidth:"100%",position:"relative"},[`&${t}-hidden`]:{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{textAlign:"start",textDecoration:"none",borderRadius:p,boxShadow:b,position:"relative",backgroundColor:O,border:"none",backgroundClip:"padding-box",[`${t}-close`]:{position:"absolute",top:n,insetInlineEnd:n,color:e.colorIcon,outline:"none",width:k,height:k,borderRadius:e.borderRadiusSM,transition:`background-color ${e.motionDurationMid}, color ${e.motionDurationMid}`,display:"flex",alignItems:"center",justifyContent:"center","&:hover":{color:e.colorIconHover,backgroundColor:e.wireframe?"transparent":e.colorFillContent}},[`${t}-cover`]:{textAlign:"center",padding:`${n+k+o}px ${n}px 0`,img:{width:"100%"}},[`${t}-header`]:{padding:`${n}px ${n}px ${o}px`,[`${t}-title`]:{lineHeight:r,fontSize:C,fontWeight:x}},[`${t}-description`]:{padding:`0 ${n}px`,lineHeight:r,wordWrap:"break-word"},[`${t}-footer`]:{padding:`${o}px ${n}px ${n}px`,textAlign:"end",borderRadius:`0 0 ${i}px ${i}px`,display:"flex",[`${t}-indicators`]:{display:"inline-block",[`${t}-indicator`]:{width:v,height:a,display:"inline-block",borderRadius:"50%",background:c,"&:not(:last-child)":{marginInlineEnd:a},"&-active":{background:u}}},[`${t}-buttons`]:{marginInlineStart:"auto",[`${w}-btn`]:{marginInlineStart:$}}}},[`${t}-primary, &${t}-primary`]:{"--antd-arrow-background-color":u,[`${t}-inner`]:{color:h,textAlign:"start",textDecoration:"none",backgroundColor:u,borderRadius:d,boxShadow:b,[`${t}-close`]:{color:h},[`${t}-indicators`]:{[`${t}-indicator`]:{background:new J(h).setAlpha(.15).toRgbString(),"&-active":{background:h}}},[`${t}-prev-btn`]:{color:h,borderColor:new J(h).setAlpha(.15).toRgbString(),backgroundColor:u,"&:hover":{backgroundColor:new J(h).setAlpha(.15).toRgbString(),borderColor:"transparent"}},[`${t}-next-btn`]:{color:u,borderColor:"transparent",background:m,"&:hover":{background:new J(g).onBackground(m).toRgbString()}}}}}),[`${t}-mask`]:{[`${t}-placeholder-animated`]:{transition:`all ${f}`}},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${t}-inner`]:{borderRadius:Math.min(p,Ye)}}},Ze(e,{colorBg:"var(--antd-arrow-background-color)",contentRadius:p,limitVerticalRadius:!0})]},zt=qe("Tour",e=>{const{borderRadiusLG:t,fontSize:r,lineHeight:n}=e,o=Je(e,{tourZIndexPopup:e.zIndexPopupBase+70,indicatorWidth:6,indicatorHeight:6,tourBorderRadius:t,tourCloseSize:r*n});return[Lt(o)]});var Vt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Gt=j({name:"ATour",inheritAttrs:!1,props:jt(),setup(e,t){let{attrs:r,emit:n,slots:o}=t;const{current:d,type:i,steps:u,defaultCurrent:s}=ce(e),{prefixCls:c,direction:a}=tt("tour",e),[v,b]=zt(c),{currentMergedType:S,updateInnerCurrent:C}=Dt({defaultType:i,steps:u,current:d,defaultCurrent:s});return()=>{const{steps:O,current:x,type:$,rootClassName:h}=e,p=Vt(e,["steps","current","type","rootClassName"]),m=E({[`${c.value}-primary`]:S.value==="primary",[`${c.value}-rtl`]:a.value==="rtl"},b.value,h),g=(w,I)=>l(Ft,y(y({},w),{},{type:$,current:I}),{indicatorsRender:o.indicatorsRender}),k=w=>{C(w),n("update:current",w),n("change",w)},f=P(()=>nt({arrowPointAtCenter:!0,autoAdjustOverflow:!0}));return v(l(At,y(y(y({},r),p),{},{rootClassName:m,prefixCls:c.value,current:x,defaultCurrent:e.defaultCurrent,animated:!0,renderPanel:g,onChange:k,steps:O,builtinPlacements:f.value}),null))}}}),Ht=et(Gt);function Re(e,t){var r;((r=e==null?void 0:e.permissions)==null?void 0:r.length)>0&&e.permissions.forEach(n=>{n.checked=t})}function ee(e,t){var r,n;Re(e,t),(n=(r=e==null?void 0:e.children)==null?void 0:r.forEach)==null||n.call(r,o=>{ee(o,t)})}function Kt(e){rt(e,t=>{if(Ut(t),t.children&&t.children.length>0){const r=t.children.filter(d=>d.menuType==="F"&&t.menuType!=="M"),n=kt(t.children,r);t.children=n;const o=r.map(d=>({id:d.id,label:d.label,checked:!1}));t.permissions=o}})}function Wt(e,t,r,n){let d=lt(t).filter(i=>e.includes(i.id));if(n||(d=d.filter(i=>Ce(i.children)||me(i.children))),d.forEach(i=>{var u;r.grid.setCheckboxRow(i,!0),((u=i==null?void 0:i.permissions)==null?void 0:u.length)>0&&i.permissions.forEach(s=>{e.includes(s.id)&&(s.checked=!0)})}),!n){const i=d.filter(u=>Ce(u.permissions)||me(u.permissions)?!1:u.permissions.every(s=>s.checked===!1));r.grid.setCheckboxRow(i,!1)}}function Ut(e){var t,r,n,o;e.menuType==="C"&&((r=(t=e.children)==null?void 0:t.forEach)==null||r.call(t,d=>{if(["C","M"].includes(d.menuType)){const i=`错误用法: [${e.label} - 菜单]下不能放 目录/菜单 -> [${d.label}]`;console.warn(i),be.warning({message:"提示",description:i,duration:0})}})),e.menuType==="F"&&((o=(n=e.children)==null?void 0:n.forEach)==null||o.call(n,d=>{if(["C","F","M"].includes(d.menuType)){const i=`错误用法: [${e.label} - 按钮]下不能放置'目录/菜单/按钮' -> [${d.label}]`;console.warn(i),be.warning({message:"提示",description:i,duration:0})}}))}function Xt(){const e=U(!1),t=it("menu_select_fullscreen_read",!1);function r(){t.value||(e.value=!0)}function n(){e.value=!1,t.value=!0}const o=[{title:"提示",description:"点击这里可以全屏",target:()=>document.querySelector('div#menu-select-table .vxe-tools--operate > button[title="全屏"]')}];return{FullScreenGuide:j({name:"FullScreenGuide",inheritAttrs:!1,setup(){return()=>l(Ht,{onClose:n,open:e.value,steps:o,zIndex:9999},null)}}),openGuide:r,closeGuide:n}}const qt={class:"flex h-full flex-col",id:"menu-select-table"},Jt={class:"text-primary mx-1 font-semibold"},Zt={class:"flex flex-wrap gap-x-3 gap-y-1"},Yt=j({name:"MenuSelectTable",inheritAttrs:!1,__name:"menu-select-table",props:ut({checkedKeys:{default:()=>[]},defaultExpandAll:{type:Boolean,default:!0},menus:{}},{association:{type:Boolean,default:!0},associationModifiers:{}}),emits:["update:association"],setup(e,{expose:t}){const r=e,n=ct(e,"association"),o={checkboxConfig:{labelField:"label",checkStrictly:!n.value},size:"small",columns:Ne,height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{enabled:!1},toolbarConfig:{refresh:!1,custom:!1},rowConfig:{isHover:!1,isCurrent:!1,keyField:"id"},scrollY:{enabled:!0,gt:0},treeConfig:{parentField:"parentId",rowField:"id",transform:!1},showOverflow:!1},d=U(0);function i(){d.value=$().length}const[u,s]=bt({gridOptions:o,gridEvents:{checkboxChange:h=>{const p=h.checked,m=h.row;n.value?ee(m,p):Re(m,p),i()},checkboxAll:h=>{h.$grid.getData().forEach(m=>{ee(m,h.checked)}),i()}}});function c(h,p,m){h.forEach(g=>{p.includes(g.id)&&s.grid.setCheckboxRow(g,!0),g.permissions&&g.permissions.length>0&&g.permissions.forEach(k=>{p.includes(k.id)&&(k.checked=!0,m&&O(g))}),g.children&&g.children.length>0&&c(g.children,p,m)})}const{FullScreenGuide:a,openGuide:v}=Xt();Pe(()=>{L(()=>r.menus,h=>re(null,null,function*(){const p=dt(h);Kt(p),yield s.grid.loadData(p),r.defaultExpandAll&&(yield pt(),C(!0))})),L(n,h=>{s.setGridOptions({checkboxConfig:{checkStrictly:!h}})}),L(()=>r.checkedKeys,h=>{const p=Y([...h]),m=s.grid.getData();c(m,p,n.value),i(),setTimeout(v,1e3)})});const b=Te([]);function S(h){return re(this,null,function*(){b.value=$();const p=s.grid.getData();p.forEach(m=>{ee(m,!1)}),yield s.grid.clearCheckboxRow(),yield s.grid.scrollTo(0,0),Wt(b.value,p,s,!h.target.value),i()})}function C(h){var p;(p=s.grid)==null||p.setAllTreeExpand(h)}function O(h){if(n.value){const p=h.permissions.filter(m=>m.checked===!0);p.length>0&&s.grid.setCheckboxRow(h,!0),p.length===0&&s.grid.setCheckboxRow(h,!1)}i()}function x(h,p){const m=[];return h.forEach(g=>{if(g.children&&g.children.length>0){const k=x(g.children,p);m.push(...k)}else if(p&&m.push(g.id),g.permissions&&g.permissions.length>0){const k=g.permissions.filter(f=>f.checked===!0).map(f=>f.id);m.push(...k)}}),Y(m)}function $(){var f,w,I,N,z,V,G,X,H;if(n.value){const M=(I=(w=(f=s==null?void 0:s.grid)==null?void 0:f.getCheckboxRecords)==null?void 0:w.call(f,!0))!=null?I:[],K=x(M,!0),ne=st(r.menus,K);return Y([...ne,...K])}const h=(V=(z=(N=s==null?void 0:s.grid)==null?void 0:N.getCheckboxRecords)==null?void 0:z.call(N,!0))!=null?V:[],p=(H=(X=(G=s==null?void 0:s.grid)==null?void 0:G.getData)==null?void 0:X.call(G))!=null?H:[],m=h.map(M=>M.id),g=x(p,!1);return Y([...m,...g])}return t({getCheckedKeys:$}),(h,p)=>{const m=ft("a-button");return le(),ye("div",qt,[l(D(u),null,{"toolbar-actions":A(()=>[l(D(yt),{value:n.value,"onUpdate:value":p[0]||(p[0]=g=>n.value=g),options:D(Ae),"button-style":"solid","option-type":"button",onChange:S},null,8,["value","options"]),l(D(Ct),{class:"mx-2",type:"info"},{message:A(()=>[se("div",null,[p[3]||(p[3]=B(" 已选中 ")),se("span",Jt,Z(d.value),1),p[4]||(p[4]=B(" 个节点 "))])]),_:1})]),"toolbar-tools":A(()=>[l(D(mt),null,{default:A(()=>[l(m,{onClick:p[1]||(p[1]=g=>C(!1))},{default:A(()=>[B(Z(h.$t("pages.common.collapse")),1)]),_:1}),l(m,{onClick:p[2]||(p[2]=g=>C(!0))},{default:A(()=>[B(Z(h.$t("pages.common.expand")),1)]),_:1})]),_:1})]),permissions:A(({row:g})=>[se("div",Zt,[(le(!0),ye(te,null,vt(g.permissions,k=>(le(),ht(D(gt),{key:k.id,checked:k.checked,"onUpdate:checked":f=>k.checked=f,onChange:()=>O(g)},{default:A(()=>[B(Z(k.label),1)]),_:2},1032,["checked","onUpdate:checked","onChange"]))),128))])]),_:1}),l(D(a))])}}}),pn=ot(Yt,[["__scopeId","data-v-96b02cc5"]]);export{pn as M};
