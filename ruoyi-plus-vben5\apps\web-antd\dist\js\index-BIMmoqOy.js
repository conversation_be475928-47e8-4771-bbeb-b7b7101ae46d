import{o as w,bj as L,h as J,cx as ce,cy as ue,_ as d,bu as me,j as ge,cz as ve,m as be,b1 as $e,r as fe,cA as we,aF as V,e as Z,p as q,ak as E,g as H,c as j,bx as U,aY as F,cB as he,aJ as ye,aX as Y}from"./bootstrap-DCMzVRvD.js";import{D as Ce}from"./Dropdown-BOZk78PH.js";import{E as Se,u as xe}from"./index-Bg2oL4a6.js";import{i as k}from"./move-DLDqWE9R.js";import{i as G,s as Pe,a as _e,b as Oe,c as De}from"./slide-B82O6h2Y.js";import{d as K,B as P,a as $}from"../jse/index-index-C-MnMZEz.js";import{R as W}from"./LeftOutlined-DE4sX_Jv.js";const Q=()=>({arrow:ce([<PERSON><PERSON><PERSON>,Object]),trigger:{type:[Array,String]},menu:L(),overlay:J.any,visible:w(),open:w(),disabled:w(),danger:w(),autofocus:w(),align:L(),getPopupContainer:Function,prefixCls:String,transitionName:String,placement:String,overlayClassName:String,overlayStyle:L(),forceRender:w(),mouseEnterDelay:Number,mouseLeaveDelay:Number,openClassName:String,minOverlayWidthMatchTrigger:w(),destroyPopupOnHide:w(),onVisibleChange:{type:Function},"onUpdate:visible":{type:Function},onOpenChange:{type:Function},"onUpdate:open":{type:Function}}),z=ue(),Ie=()=>d(d({},Q()),{type:z.type,size:String,htmlType:z.htmlType,href:String,disabled:w(),prefixCls:String,icon:J.any,title:String,loading:z.loading,onClick:me()}),Re=e=>{const{componentCls:o,antCls:n,paddingXS:i,opacityLoading:r}=e;return{[`${o}-button`]:{whiteSpace:"nowrap",[`&${n}-btn-group > ${n}-btn`]:{[`&-loading, &-loading + ${n}-btn`]:{cursor:"default",pointerEvents:"none",opacity:r},[`&:last-child:not(:first-child):not(${n}-btn-icon-only)`]:{paddingInline:i}}}}},Be=e=>{const{componentCls:o,menuCls:n,colorError:i,colorTextLightSolid:r}=e,l=`${n}-item`;return{[`${o}, ${o}-menu-submenu`]:{[`${n} ${l}`]:{[`&${l}-danger:not(${l}-disabled)`]:{color:i,"&:hover":{color:r,backgroundColor:i}}}}}},Ne=e=>{const{componentCls:o,menuCls:n,zIndexPopup:i,dropdownArrowDistance:r,dropdownArrowOffset:l,sizePopupArrow:v,antCls:t,iconCls:c,motionDurationMid:b,dropdownPaddingVertical:h,fontSize:y,dropdownEdgeChildPadding:p,colorTextDisabled:m,fontSizeIcon:g,controlPaddingHorizontal:C,colorBgElevated:a,boxShadowPopoverArrow:s}=e;return[{[o]:d(d({},fe(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:i,display:"block","&::before":{position:"absolute",insetBlock:-r+v/2,zIndex:-9999,opacity:1e-4,content:'""'},[`${o}-wrap`]:{position:"relative",[`${t}-btn > ${c}-down`]:{fontSize:g},[`${c}-down::before`]:{transition:`transform ${b}`}},[`${o}-wrap-open`]:{[`${c}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`
        &-show-arrow${o}-placement-topLeft,
        &-show-arrow${o}-placement-top,
        &-show-arrow${o}-placement-topRight
      `]:{paddingBottom:r},[`
        &-show-arrow${o}-placement-bottomLeft,
        &-show-arrow${o}-placement-bottom,
        &-show-arrow${o}-placement-bottomRight
      `]:{paddingTop:r},[`${o}-arrow`]:d({position:"absolute",zIndex:1,display:"block"},we(v,e.borderRadiusXS,e.borderRadiusOuter,a,s)),[`
        &-placement-top > ${o}-arrow,
        &-placement-topLeft > ${o}-arrow,
        &-placement-topRight > ${o}-arrow
      `]:{bottom:r,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${o}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft > ${o}-arrow`]:{left:{_skip_check_:!0,value:l}},[`&-placement-topRight > ${o}-arrow`]:{right:{_skip_check_:!0,value:l}},[`
          &-placement-bottom > ${o}-arrow,
          &-placement-bottomLeft > ${o}-arrow,
          &-placement-bottomRight > ${o}-arrow
        `]:{top:r,transform:"translateY(-100%)"},[`&-placement-bottom > ${o}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateY(-100%) translateX(-50%)"},[`&-placement-bottomLeft > ${o}-arrow`]:{left:{_skip_check_:!0,value:l}},[`&-placement-bottomRight > ${o}-arrow`]:{right:{_skip_check_:!0,value:l}},[`&${t}-slide-down-enter${t}-slide-down-enter-active${o}-placement-bottomLeft,
          &${t}-slide-down-appear${t}-slide-down-appear-active${o}-placement-bottomLeft,
          &${t}-slide-down-enter${t}-slide-down-enter-active${o}-placement-bottom,
          &${t}-slide-down-appear${t}-slide-down-appear-active${o}-placement-bottom,
          &${t}-slide-down-enter${t}-slide-down-enter-active${o}-placement-bottomRight,
          &${t}-slide-down-appear${t}-slide-down-appear-active${o}-placement-bottomRight`]:{animationName:De},[`&${t}-slide-up-enter${t}-slide-up-enter-active${o}-placement-topLeft,
          &${t}-slide-up-appear${t}-slide-up-appear-active${o}-placement-topLeft,
          &${t}-slide-up-enter${t}-slide-up-enter-active${o}-placement-top,
          &${t}-slide-up-appear${t}-slide-up-appear-active${o}-placement-top,
          &${t}-slide-up-enter${t}-slide-up-enter-active${o}-placement-topRight,
          &${t}-slide-up-appear${t}-slide-up-appear-active${o}-placement-topRight`]:{animationName:Oe},[`&${t}-slide-down-leave${t}-slide-down-leave-active${o}-placement-bottomLeft,
          &${t}-slide-down-leave${t}-slide-down-leave-active${o}-placement-bottom,
          &${t}-slide-down-leave${t}-slide-down-leave-active${o}-placement-bottomRight`]:{animationName:_e},[`&${t}-slide-up-leave${t}-slide-up-leave-active${o}-placement-topLeft,
          &${t}-slide-up-leave${t}-slide-up-leave-active${o}-placement-top,
          &${t}-slide-up-leave${t}-slide-up-leave-active${o}-placement-topRight`]:{animationName:Pe}})},{[`${o} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:i,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul,li":{listStyle:"none"},ul:{marginInline:"0.3em"}},[`${o}, ${o}-menu-submenu`]:{[n]:d(d({padding:p,listStyleType:"none",backgroundColor:a,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},V(e)),{[`${n}-item-group-title`]:{padding:`${h}px ${C}px`,color:e.colorTextDescription,transition:`all ${b}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center",borderRadius:e.borderRadiusSM},[`${n}-item-icon`]:{minWidth:y,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","> a":{color:"inherit",transition:`all ${b}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}}},[`${n}-item, ${n}-submenu-title`]:d(d({clear:"both",margin:0,padding:`${h}px ${C}px`,color:e.colorText,fontWeight:"normal",fontSize:y,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${b}`,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},V(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:a,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${e.marginXXS}px 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${o}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${o}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:g,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${e.marginXS}px`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:C+e.fontSizeSM},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${o}-menu-submenu-title`]:{[`&, ${o}-menu-submenu-arrow-icon`]:{color:m,backgroundColor:a,cursor:"not-allowed"}},[`${n}-submenu-selected ${o}-menu-submenu-title`]:{color:e.colorPrimary}})}},[G(e,"slide-up"),G(e,"slide-down"),k(e,"move-up"),k(e,"move-down"),$e(e,"zoom-big")]]},ee=ge("Dropdown",(e,o)=>{let{rootPrefixCls:n}=o;const{marginXXS:i,sizePopupArrow:r,controlHeight:l,fontSize:v,lineHeight:t,paddingXXS:c,componentCls:b,borderRadiusOuter:h,borderRadiusLG:y}=e,p=(l-v*t)/2,{dropdownArrowOffset:m}=ve({sizePopupArrow:r,contentRadius:y,borderRadiusOuter:h}),g=be(e,{menuCls:`${b}-menu`,rootPrefixCls:n,dropdownArrowDistance:r/2+i,dropdownArrowOffset:m,dropdownPaddingVertical:p,dropdownEdgeChildPadding:c});return[Ne(g),Re(g),Be(g)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));var Te=function(e,o){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&o.indexOf(i)<0&&(n[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)o.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n};const Ae=E.Group,B=K({compatConfig:{MODE:3},name:"ADropdownButton",inheritAttrs:!1,__ANT_BUTTON:!0,props:Z(Ie(),{trigger:"hover",placement:"bottomRight",type:"default"}),slots:Object,setup(e,o){let{slots:n,attrs:i,emit:r}=o;const l=p=>{r("update:visible",p),r("visibleChange",p),r("update:open",p),r("openChange",p)},{prefixCls:v,direction:t,getPopupContainer:c}=q("dropdown",e),b=P(()=>`${v.value}-button`),[h,y]=ee(v);return()=>{var p,m;const g=d(d({},e),i),{type:C="default",disabled:a,danger:s,loading:f,htmlType:_,class:u="",overlay:S=(p=n.overlay)===null||p===void 0?void 0:p.call(n),trigger:x,align:O,open:D,visible:I,onVisibleChange:N,placement:T=t.value==="rtl"?"bottomLeft":"bottomRight",href:A,title:oe,icon:te=((m=n.icon)===null||m===void 0?void 0:m.call(n))||$(Se,null,null),mouseEnterDelay:ne,mouseLeaveDelay:ae,overlayClassName:re,overlayStyle:ie,destroyPopupOnHide:le,onClick:se,"onUpdate:open":Le}=g,de=Te(g,["type","disabled","danger","loading","htmlType","class","overlay","trigger","align","open","visible","onVisibleChange","placement","href","title","icon","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","onClick","onUpdate:open"]),pe={align:O,disabled:a,trigger:a?[]:x,placement:T,getPopupContainer:c==null?void 0:c.value,onOpenChange:l,mouseEnterDelay:ne,mouseLeaveDelay:ae,open:D!=null?D:I,overlayClassName:re,overlayStyle:ie,destroyPopupOnHide:le},M=$(E,{danger:s,type:C,disabled:a,loading:f,onClick:se,htmlType:_,href:A,title:oe},{default:n.default}),X=$(E,{danger:s,type:C,icon:te},null);return h($(Ae,H(H({},de),{},{class:j(b.value,u,y.value)}),{default:()=>[n.leftButton?n.leftButton({button:M}):M,$(R,pe,{default:()=>[n.rightButton?n.rightButton({button:X}):X],overlay:()=>S})]}))}}}),R=K({compatConfig:{MODE:3},name:"ADropdown",inheritAttrs:!1,props:Z(Q(),{mouseEnterDelay:.15,mouseLeaveDelay:.1,placement:"bottomLeft",trigger:"hover"}),slots:Object,setup(e,o){let{slots:n,attrs:i,emit:r}=o;const{prefixCls:l,rootPrefixCls:v,direction:t,getPopupContainer:c}=q("dropdown",e),[b,h]=ee(l),y=P(()=>{const{placement:a="",transitionName:s}=e;return s!==void 0?s:a.includes("top")?`${v.value}-slide-down`:`${v.value}-slide-up`});xe({prefixCls:P(()=>`${l.value}-menu`),expandIcon:P(()=>$("span",{class:`${l.value}-menu-submenu-arrow`},[$(W,{class:`${l.value}-menu-submenu-arrow-icon`},null)])),mode:P(()=>"vertical"),selectable:P(()=>!1),onClick:()=>{},validator:a=>{let{mode:s}=a}});const p=()=>{var a,s,f;const _=e.overlay||((a=n.overlay)===null||a===void 0?void 0:a.call(n)),u=Array.isArray(_)?_[0]:_;if(!u)return null;const S=u.props||{};U(!S.mode||S.mode==="vertical","Dropdown",`mode="${S.mode}" is not supported for Dropdown's Menu.`);const{selectable:x=!1,expandIcon:O=(f=(s=u.children)===null||s===void 0?void 0:s.expandIcon)===null||f===void 0?void 0:f.call(s)}=S,D=typeof O!="undefined"&&Y(O)?O:$("span",{class:`${l.value}-menu-submenu-arrow`},[$(W,{class:`${l.value}-menu-submenu-arrow-icon`},null)]);return Y(u)?F(u,{mode:"vertical",selectable:x,expandIcon:()=>D}):u},m=P(()=>{const a=e.placement;if(!a)return t.value==="rtl"?"bottomRight":"bottomLeft";if(a.includes("Center")){const s=a.slice(0,a.indexOf("Center"));return U(!a.includes("Center"),"Dropdown",`You are using '${a}' placement in Dropdown, which is deprecated. Try to use '${s}' instead.`),s}return a}),g=P(()=>typeof e.visible=="boolean"?e.visible:e.open),C=a=>{r("update:visible",a),r("visibleChange",a),r("update:open",a),r("openChange",a)};return()=>{var a,s;const{arrow:f,trigger:_,disabled:u,overlayClassName:S}=e,x=(a=n.default)===null||a===void 0?void 0:a.call(n)[0],O=F(x,d({class:j((s=x==null?void 0:x.props)===null||s===void 0?void 0:s.class,{[`${l.value}-rtl`]:t.value==="rtl"},`${l.value}-trigger`)},u?{disabled:u}:{})),D=j(S,h.value,{[`${l.value}-rtl`]:t.value==="rtl"}),I=u?[]:_;let N;I&&I.includes("contextmenu")&&(N=!0);const T=he({arrowPointAtCenter:typeof f=="object"&&f.pointAtCenter,autoAdjustOverflow:!0}),A=ye(d(d(d({},e),i),{visible:g.value,builtinPlacements:T,overlayClassName:D,arrow:!!f,alignPoint:N,prefixCls:l.value,getPopupContainer:c==null?void 0:c.value,transitionName:y.value,trigger:I,onVisibleChange:C,placement:m.value}),["overlay","onUpdate:visible"]);return b($(Ce,A,{default:()=>[O],overlay:p}))}}});R.Button=B;R.Button=B;R.install=function(e){return e.component(R.name,R),e.component(B.name,B),e};export{R as D};
