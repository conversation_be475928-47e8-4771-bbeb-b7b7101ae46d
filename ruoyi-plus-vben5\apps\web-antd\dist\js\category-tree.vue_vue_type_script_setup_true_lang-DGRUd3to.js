var g=(u,y,o)=>new Promise((n,e)=>{var d=s=>{try{l(o.next(s))}catch(a){e(a)}},c=s=>{try{l(o.throw(s))}catch(a){e(a)}},l=s=>s.done?n(s.value):Promise.resolve(s.value).then(d,c);l((o=o.apply(u,y)).next())});import{c as S}from"./index-B4NcjlQn.js";import{S as V}from"./SyncOutlined-GoH9kFJY.js";import{S as T}from"./index-BLwHKR_M.js";import{d as $,m as b,u as _,p as x,v as B,l as M,c as k,o as i,a as p,w as f,j as m,b as v,h as O,f as N,O as z,k as C,t as h,n as A}from"../jse/index-index-C-MnMZEz.js";import{a as D}from"./Search-ClCped_G.js";import{T as I}from"./index-DdFaZtFR.js";const U={class:"bg-background flex h-full flex-col overflow-y-auto rounded-lg"},j={class:"bg-background z-100 sticky left-0 top-0 p-[8px]"},q={class:"h-full overflow-x-hidden px-[8px]"},E={key:0},K={style:{color:"#f50"}},L={key:1},W=$({inheritAttrs:!1,__name:"category-tree",props:{selectCode:{required:!0,type:Array},selectCodeModifiers:{},searchValue:{type:String,default:""},searchValueModifiers:{}},emits:b(["reload","select"],["update:selectCode","update:searchValue"]),setup(u,{emit:y}){const o=y,n=_(u,"selectCode"),e=_(u,"searchValue"),d=x([]),c=x(!0);function l(){return g(this,null,function*(){c.value=!0,e.value="",n.value=[];const a=yield S();d.value=a,c.value=!1})}function s(){return g(this,null,function*(){yield l(),o("reload")})}return B(l),(a,r)=>{const w=M("a-button");return i(),k("div",{class:A(a.$attrs.class)},[p(v(T),{loading:c.value,paragraph:{rows:8},active:"",class:"p-[8px]"},{default:f(()=>[m("div",U,[m("div",j,[p(v(D),{value:e.value,"onUpdate:value":r[0]||(r[0]=t=>e.value=t),placeholder:a.$t("pages.common.search"),size:"small"},{enterButton:f(()=>[p(w,{onClick:s},{default:f(()=>[p(v(V),{class:"text-primary"})]),_:1})]),_:1},8,["value","placeholder"])]),m("div",q,[d.value.length>0?(i(),O(v(I),z({key:0},a.$attrs,{"selected-keys":n.value,"onUpdate:selectedKeys":r[1]||(r[1]=t=>n.value=t),class:a.$attrs.class,"field-names":{title:"label",key:"id"},"show-line":{showLeafIcon:!1},"tree-data":d.value,virtual:!1,"default-expand-all":"",onSelect:r[2]||(r[2]=t=>a.$emit("select"))}),{title:f(({label:t})=>[t.indexOf(e.value)>-1?(i(),k("span",E,[C(h(t.substring(0,t.indexOf(e.value)))+" ",1),m("span",K,h(e.value),1),C(" "+h(t.substring(t.indexOf(e.value)+e.value.length)),1)])):(i(),k("span",L,h(t),1))]),_:1},16,["selected-keys","class","tree-data"])):N("",!0)])])]),_:1},8,["loading"])],2)}}});export{W as _};
