{"doc": " 用户配置Service接口\n\n <AUTHOR>\n @date 2025-06-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserConfig", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 获取用户配置值\n\n @param userId 用户ID\n @param configKey 配置键\n @param tenantId 租户ID\n @return 配置值\n"}, {"name": "setUserConfig", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 设置用户配置值\n\n @param userId 用户ID\n @param configKey 配置键\n @param configValue 配置值\n @param tenantId 租户ID\n"}, {"name": "getCurrentUserWarehouse", "paramTypes": [], "doc": " 获取当前用户的仓库选择\n\n @return 仓库ID\n"}, {"name": "setCurrentUserWarehouse", "paramTypes": ["java.lang.Long"], "doc": " 设置当前用户的仓库选择\n\n @param warehouseId 仓库ID\n"}], "constructors": []}