{"doc": " 缓存操作工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 获取缓存值\n\n @param cacheNames 缓存组名称\n @param key        缓存key\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Object"], "doc": " 保存缓存值\n\n @param cacheNames 缓存组名称\n @param key        缓存key\n @param value      缓存值\n"}, {"name": "evict", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 删除缓存值\n\n @param cacheNames 缓存组名称\n @param key        缓存key\n"}, {"name": "clear", "paramTypes": ["java.lang.String"], "doc": " 清空缓存值\n\n @param cacheNames 缓存组名称\n"}], "constructors": []}