{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@lezer+xml@1.0.6/node_modules/@lezer/xml/dist/index.js", "../../../../../node_modules/.pnpm/@codemirror+lang-xml@6.1.0/node_modules/@codemirror/lang-xml/dist/index.js"], "sourcesContent": ["import { ContextTracker, ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst StartTag = 1,\n  StartCloseTag = 2,\n  MissingCloseTag = 3,\n  mismatchedStartCloseTag = 4,\n  incompleteStartCloseTag = 5,\n  commentContent$1 = 36,\n  piContent$1 = 37,\n  cdataContent$1 = 38,\n  Element = 11,\n  OpenTag = 13;\n\n/* Hand-written tokenizer for XML tag matching. */\n\nfunction nameChar(ch) {\n  return ch == 45 || ch == 46 || ch == 58 || ch >= 65 && ch <= 90 || ch == 95 || ch >= 97 && ch <= 122 || ch >= 161\n}\n\nfunction isSpace(ch) {\n  return ch == 9 || ch == 10 || ch == 13 || ch == 32\n}\n\nlet cachedName = null, cachedInput = null, cachedPos = 0;\nfunction tagNameAfter(input, offset) {\n  let pos = input.pos + offset;\n  if (cachedInput == input && cachedPos == pos) return cachedName\n  while (isSpace(input.peek(offset))) offset++;\n  let name = \"\";\n  for (;;) {\n    let next = input.peek(offset);\n    if (!nameChar(next)) break\n    name += String.fromCharCode(next);\n    offset++;\n  }\n  cachedInput = input; cachedPos = pos;\n  return cachedName = name || null\n}\n\nfunction ElementContext(name, parent) {\n  this.name = name;\n  this.parent = parent;\n}\n\nconst elementContext = new ContextTracker({\n  start: null,\n  shift(context, term, stack, input) {\n    return term == StartTag ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  reduce(context, term) {\n    return term == Element && context ? context.parent : context\n  },\n  reuse(context, node, _stack, input) {\n    let type = node.type.id;\n    return type == StartTag || type == OpenTag\n      ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  strict: false\n});\n\nconst startTag = new ExternalTokenizer((input, stack) => {\n  if (input.next != 60 /* '<' */) return\n  input.advance();\n  if (input.next == 47 /* '/' */) {\n    input.advance();\n    let name = tagNameAfter(input, 0);\n    if (!name) return input.acceptToken(incompleteStartCloseTag)\n    if (stack.context && name == stack.context.name) return input.acceptToken(StartCloseTag)\n    for (let cx = stack.context; cx; cx = cx.parent) if (cx.name == name) return input.acceptToken(MissingCloseTag, -2)\n    input.acceptToken(mismatchedStartCloseTag);\n  } else if (input.next != 33 /* '!' */ && input.next != 63 /* '?' */) {\n    return input.acceptToken(StartTag)\n  }\n}, {contextual: true});\n\nfunction scanTo(type, end) {\n  return new ExternalTokenizer(input => {\n    let len = 0, first = end.charCodeAt(0);\n    scan: for (;; input.advance(), len++) {\n      if (input.next < 0) break\n      if (input.next == first) {\n        for (let i = 1; i < end.length; i++)\n          if (input.peek(i) != end.charCodeAt(i)) continue scan\n        break\n      }\n    }\n    if (len) input.acceptToken(type);\n  })\n}\n\nconst commentContent = scanTo(commentContent$1, \"-->\");\nconst piContent = scanTo(piContent$1, \"?>\");\nconst cdataContent = scanTo(cdataContent$1, \"]]>\");\n\nconst xmlHighlighting = styleTags({\n  Text: tags.content,\n  \"StartTag StartCloseTag EndTag SelfCloseEndTag\": tags.angleBracket,\n  TagName: tags.tagName,\n  \"MismatchedCloseTag/TagName\": [tags.tagName, tags.invalid],\n  AttributeName: tags.attributeName,\n  AttributeValue: tags.attributeValue,\n  Is: tags.definitionOperator,\n  \"EntityReference CharacterReference\": tags.character,\n  Comment: tags.blockComment,\n  ProcessingInst: tags.processingInstruction,\n  DoctypeDecl: tags.documentMeta,\n  Cdata: tags.special(tags.string)\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \",lOQOaOOOrOxO'#CfOzOpO'#CiO!tOaO'#CgOOOP'#Cg'#CgO!{OrO'#CrO#TOtO'#CsO#]OpO'#CtOOOP'#DT'#DTOOOP'#Cv'#CvQQOaOOOOOW'#Cw'#CwO#eOxO,59QOOOP,59Q,59QOOOO'#Cx'#CxO#mOpO,59TO#uO!bO,59TOOOP'#C|'#C|O$TOaO,59RO$[OpO'#CoOOOP,59R,59ROOOQ'#C}'#C}O$dOrO,59^OOOP,59^,59^OOOS'#DO'#DOO$lOtO,59_OOOP,59_,59_O$tOpO,59`O$|OpO,59`OOOP-E6t-E6tOOOW-E6u-E6uOOOP1G.l1G.lOOOO-E6v-E6vO%UO!bO1G.oO%UO!bO1G.oO%dOpO'#CkO%lO!bO'#CyO%zO!bO1G.oOOOP1G.o1G.oOOOP1G.w1G.wOOOP-E6z-E6zOOOP1G.m1G.mO&VOpO,59ZO&_OpO,59ZOOOQ-E6{-E6{OOOP1G.x1G.xOOOS-E6|-E6|OOOP1G.y1G.yO&gOpO1G.zO&gOpO1G.zOOOP1G.z1G.zO&oO!bO7+$ZO&}O!bO7+$ZOOOP7+$Z7+$ZOOOP7+$c7+$cO'YOpO,59VO'bOpO,59VO'mO!bO,59eOOOO-E6w-E6wO'{OpO1G.uO'{OpO1G.uOOOP1G.u1G.uO(TOpO7+$fOOOP7+$f7+$fO(]O!bO<<GuOOOP<<Gu<<GuOOOP<<G}<<G}O'bOpO1G.qO'bOpO1G.qO(hO#tO'#CnO(vO&jO'#CnOOOO1G.q1G.qO)UOpO7+$aOOOP7+$a7+$aOOOP<<HQ<<HQOOOPAN=aAN=aOOOPAN=iAN=iO'bOpO7+$]OOOO7+$]7+$]OOOO'#Cz'#CzO)^O#tO,59YOOOO,59Y,59YOOOO'#C{'#C{O)lO&jO,59YOOOP<<G{<<G{OOOO<<Gw<<GwOOOO-E6x-E6xOOOO1G.t1G.tOOOO-E6y-E6y\",\n  stateData: \")z~OPQOSVOTWOVWOWWOXWOiXOyPO!QTO!SUO~OvZOx]O~O^`Oz^O~OPQOQcOSVOTWOVWOWWOXWOyPO!QTO!SUO~ORdO~P!SOteO!PgO~OuhO!RjO~O^lOz^O~OvZOxoO~O^qOz^O~O[vO`sOdwOz^O~ORyO~P!SO^{Oz^O~OteO!P}O~OuhO!R!PO~O^!QOz^O~O[!SOz^O~O[!VO`sOd!WOz^O~Oa!YOz^O~Oz^O[mX`mXdmX~O[!VO`sOd!WO~O^!]Oz^O~O[!_Oz^O~O[!aOz^O~O[!cO`sOd!dOz^O~O[!cO`sOd!dO~Oa!eOz^O~Oz^O{!gO}!hO~Oz^O[ma`madma~O[!kOz^O~O[!lOz^O~O[!mO`sOd!nO~OW!qOX!qO{!sO|!qO~OW!tOX!tO}!sO!O!tO~O[!vOz^O~OW!qOX!qO{!yO|!qO~OW!tOX!tO}!yO!O!tO~O\",\n  goto: \"%cxPPPPPPPPPPyyP!PP!VPP!`!jP!pyyyP!v!|#S$[$k$q$w$}%TPPPP%ZXWORYbXRORYb_t`qru!T!U!bQ!i!YS!p!e!fR!w!oQdRRybXSORYbQYORmYQ[PRn[Q_QQkVjp_krz!R!T!X!Z!^!`!f!j!oQr`QzcQ!RlQ!TqQ!XsQ!ZtQ!^{Q!`!QQ!f!YQ!j!]R!o!eQu`S!UqrU![u!U!bR!b!TQ!r!gR!x!rQ!u!hR!z!uQbRRxbQfTR|fQiUR!OiSXOYTaRb\",\n  nodeNames: \"⚠ StartTag StartCloseTag MissingCloseTag StartCloseTag StartCloseTag Document Text EntityReference CharacterReference Cdata Element EndTag OpenTag TagName Attribute AttributeName Is AttributeValue CloseTag SelfCloseEndTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag DoctypeDecl\",\n  maxTerm: 50,\n  context: elementContext,\n  nodeProps: [\n    [\"closedBy\", 1,\"SelfCloseEndTag EndTag\",13,\"CloseTag MissingCloseTag\"],\n    [\"openedBy\", 12,\"StartTag StartCloseTag\",19,\"OpenTag\",20,\"StartTag\"],\n    [\"isolate\", -6,13,18,19,21,22,24,\"\"]\n  ],\n  propSources: [xmlHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 9,\n  tokenData: \"!)v~R!YOX$qXY)iYZ)iZ]$q]^)i^p$qpq)iqr$qrs*vsv$qvw+fwx/ix}$q}!O0[!O!P$q!P!Q2z!Q![$q![!]4n!]!^$q!^!_8U!_!`!#t!`!a!$l!a!b!%d!b!c$q!c!}4n!}#P$q#P#Q!'W#Q#R$q#R#S4n#S#T$q#T#o4n#o%W$q%W%o4n%o%p$q%p&a4n&a&b$q&b1p4n1p4U$q4U4d4n4d4e$q4e$IS4n$IS$I`$q$I`$Ib4n$Ib$Kh$q$Kh%#t4n%#t&/x$q&/x&Et4n&Et&FV$q&FV;'S4n;'S;:j8O;:j;=`)c<%l?&r$q?&r?Ah4n?Ah?BY$q?BY?Mn4n?MnO$qi$zXVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qa%nVVP!O`Ov%gwx&Tx!^%g!^!_&o!_;'S%g;'S;=`'W<%lO%gP&YTVPOv&Tw!^&T!_;'S&T;'S;=`&i<%lO&TP&lP;=`<%l&T`&tS!O`Ov&ox;'S&o;'S;=`'Q<%lO&o`'TP;=`<%l&oa'ZP;=`<%l%gX'eWVP|WOr'^rs&Tsv'^w!^'^!^!_'}!_;'S'^;'S;=`(i<%lO'^W(ST|WOr'}sv'}w;'S'};'S;=`(c<%lO'}W(fP;=`<%l'}X(lP;=`<%l'^h(vV|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oh)`P;=`<%l(oi)fP;=`<%l$qo)t`VP|W!O`zUOX$qXY)iYZ)iZ]$q]^)i^p$qpq)iqr$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk+PV{YVP!O`Ov%gwx&Tx!^%g!^!_&o!_;'S%g;'S;=`'W<%lO%g~+iast,n![!]-r!c!}-r#R#S-r#T#o-r%W%o-r%p&a-r&b1p-r4U4d-r4e$IS-r$I`$Ib-r$Kh%#t-r&/x&Et-r&FV;'S-r;'S;:j/c?&r?Ah-r?BY?Mn-r~,qQ!Q![,w#l#m-V~,zQ!Q![,w!]!^-Q~-VOX~~-YR!Q![-c!c!i-c#T#Z-c~-fS!Q![-c!]!^-Q!c!i-c#T#Z-c~-ug}!O-r!O!P-r!Q![-r![!]-r!]!^/^!c!}-r#R#S-r#T#o-r$}%O-r%W%o-r%p&a-r&b1p-r1p4U-r4U4d-r4e$IS-r$I`$Ib-r$Je$Jg-r$Kh%#t-r&/x&Et-r&FV;'S-r;'S;:j/c?&r?Ah-r?BY?Mn-r~/cOW~~/fP;=`<%l-rk/rW}bVP|WOr'^rs&Tsv'^w!^'^!^!_'}!_;'S'^;'S;=`(i<%lO'^k0eZVP|W!O`Or$qrs%gsv$qwx'^x}$q}!O1W!O!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk1aZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a2S!a;'S$q;'S;=`)c<%lO$qk2_X!PQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qm3TZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a3v!a;'S$q;'S;=`)c<%lO$qm4RXdSVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qo4{!P`S^QVP|W!O`Or$qrs%gsv$qwx'^x}$q}!O4n!O!P4n!P!Q$q!Q![4n![!]4n!]!^$q!^!_(o!_!c$q!c!}4n!}#R$q#R#S4n#S#T$q#T#o4n#o$}$q$}%O4n%O%W$q%W%o4n%o%p$q%p&a4n&a&b$q&b1p4n1p4U4n4U4d4n4d4e$q4e$IS4n$IS$I`$q$I`$Ib4n$Ib$Je$q$Je$Jg4n$Jg$Kh$q$Kh%#t4n%#t&/x$q&/x&Et4n&Et&FV$q&FV;'S4n;'S;:j8O;:j;=`)c<%l?&r$q?&r?Ah4n?Ah?BY$q?BY?Mn4n?MnO$qo8RP;=`<%l4ni8]Y|W!O`Oq(oqr8{rs&osv(owx'}x!a(o!a!b!#U!b;'S(o;'S;=`)]<%lO(oi9S_|W!O`Or(ors&osv(owx'}x}(o}!O:R!O!f(o!f!g;e!g!}(o!}#ODh#O#W(o#W#XLp#X;'S(o;'S;=`)]<%lO(oi:YX|W!O`Or(ors&osv(owx'}x}(o}!O:u!O;'S(o;'S;=`)]<%lO(oi;OV!QP|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oi;lX|W!O`Or(ors&osv(owx'}x!q(o!q!r<X!r;'S(o;'S;=`)]<%lO(oi<`X|W!O`Or(ors&osv(owx'}x!e(o!e!f<{!f;'S(o;'S;=`)]<%lO(oi=SX|W!O`Or(ors&osv(owx'}x!v(o!v!w=o!w;'S(o;'S;=`)]<%lO(oi=vX|W!O`Or(ors&osv(owx'}x!{(o!{!|>c!|;'S(o;'S;=`)]<%lO(oi>jX|W!O`Or(ors&osv(owx'}x!r(o!r!s?V!s;'S(o;'S;=`)]<%lO(oi?^X|W!O`Or(ors&osv(owx'}x!g(o!g!h?y!h;'S(o;'S;=`)]<%lO(oi@QY|W!O`Or?yrs@psv?yvwA[wxBdx!`?y!`!aCr!a;'S?y;'S;=`Db<%lO?ya@uV!O`Ov@pvxA[x!`@p!`!aAy!a;'S@p;'S;=`B^<%lO@pPA_TO!`A[!`!aAn!a;'SA[;'S;=`As<%lOA[PAsOiPPAvP;=`<%lA[aBQSiP!O`Ov&ox;'S&o;'S;=`'Q<%lO&oaBaP;=`<%l@pXBiX|WOrBdrsA[svBdvwA[w!`Bd!`!aCU!a;'SBd;'S;=`Cl<%lOBdXC]TiP|WOr'}sv'}w;'S'};'S;=`(c<%lO'}XCoP;=`<%lBdiC{ViP|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oiDeP;=`<%l?yiDoZ|W!O`Or(ors&osv(owx'}x!e(o!e!fEb!f#V(o#V#WIr#W;'S(o;'S;=`)]<%lO(oiEiX|W!O`Or(ors&osv(owx'}x!f(o!f!gFU!g;'S(o;'S;=`)]<%lO(oiF]X|W!O`Or(ors&osv(owx'}x!c(o!c!dFx!d;'S(o;'S;=`)]<%lO(oiGPX|W!O`Or(ors&osv(owx'}x!v(o!v!wGl!w;'S(o;'S;=`)]<%lO(oiGsX|W!O`Or(ors&osv(owx'}x!c(o!c!dH`!d;'S(o;'S;=`)]<%lO(oiHgX|W!O`Or(ors&osv(owx'}x!}(o!}#OIS#O;'S(o;'S;=`)]<%lO(oiI]V|W!O`yPOr(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oiIyX|W!O`Or(ors&osv(owx'}x#W(o#W#XJf#X;'S(o;'S;=`)]<%lO(oiJmX|W!O`Or(ors&osv(owx'}x#T(o#T#UKY#U;'S(o;'S;=`)]<%lO(oiKaX|W!O`Or(ors&osv(owx'}x#h(o#h#iK|#i;'S(o;'S;=`)]<%lO(oiLTX|W!O`Or(ors&osv(owx'}x#T(o#T#UH`#U;'S(o;'S;=`)]<%lO(oiLwX|W!O`Or(ors&osv(owx'}x#c(o#c#dMd#d;'S(o;'S;=`)]<%lO(oiMkX|W!O`Or(ors&osv(owx'}x#V(o#V#WNW#W;'S(o;'S;=`)]<%lO(oiN_X|W!O`Or(ors&osv(owx'}x#h(o#h#iNz#i;'S(o;'S;=`)]<%lO(oi! RX|W!O`Or(ors&osv(owx'}x#m(o#m#n! n#n;'S(o;'S;=`)]<%lO(oi! uX|W!O`Or(ors&osv(owx'}x#d(o#d#e!!b#e;'S(o;'S;=`)]<%lO(oi!!iX|W!O`Or(ors&osv(owx'}x#X(o#X#Y?y#Y;'S(o;'S;=`)]<%lO(oi!#_V!SP|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(ok!$PXaQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qo!$wX[UVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk!%mZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a!&`!a;'S$q;'S;=`)c<%lO$qk!&kX!RQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk!'aZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_#P$q#P#Q!(S#Q;'S$q;'S;=`)c<%lO$qk!(]ZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a!)O!a;'S$q;'S;=`)c<%lO$qk!)ZXxQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$q\",\n  tokenizers: [startTag, commentContent, piContent, cdataContent, 0, 1, 2, 3, 4],\n  topRules: {\"Document\":[0,6]},\n  tokenPrec: 0\n});\n\nexport { parser };\n", "import { parser } from '@lezer/xml';\nimport { syntaxTree, LRLanguage, indentNodeProp, foldNodeProp, bracketMatchingHandle, LanguageSupport } from '@codemirror/language';\nimport { EditorSelection } from '@codemirror/state';\nimport { EditorView } from '@codemirror/view';\n\nfunction tagName(doc, tag) {\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, name.to) : \"\";\n}\nfunction elementName$1(doc, tree) {\n    let tag = tree && tree.firstChild;\n    return !tag || tag.name != \"OpenTag\" ? \"\" : tagName(doc, tag);\n}\nfunction attrName(doc, tag, pos) {\n    let attr = tag && tag.getChildren(\"Attribute\").find(a => a.from <= pos && a.to >= pos);\n    let name = attr && attr.getChild(\"AttributeName\");\n    return name ? doc.sliceString(name.from, name.to) : \"\";\n}\nfunction findParentElement(tree) {\n    for (let cur = tree && tree.parent; cur; cur = cur.parent)\n        if (cur.name == \"Element\")\n            return cur;\n    return null;\n}\nfunction findLocation(state, pos) {\n    var _a;\n    let at = syntaxTree(state).resolveInner(pos, -1), inTag = null;\n    for (let cur = at; !inTag && cur.parent; cur = cur.parent)\n        if (cur.name == \"OpenTag\" || cur.name == \"CloseTag\" || cur.name == \"SelfClosingTag\" || cur.name == \"MismatchedCloseTag\")\n            inTag = cur;\n    if (inTag && (inTag.to > pos || inTag.lastChild.type.isError)) {\n        let elt = inTag.parent;\n        if (at.name == \"TagName\")\n            return inTag.name == \"CloseTag\" || inTag.name == \"MismatchedCloseTag\"\n                ? { type: \"closeTag\", from: at.from, context: elt }\n                : { type: \"openTag\", from: at.from, context: findParentElement(elt) };\n        if (at.name == \"AttributeName\")\n            return { type: \"attrName\", from: at.from, context: inTag };\n        if (at.name == \"AttributeValue\")\n            return { type: \"attrValue\", from: at.from, context: inTag };\n        let before = at == inTag || at.name == \"Attribute\" ? at.childBefore(pos) : at;\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"StartTag\")\n            return { type: \"openTag\", from: pos, context: findParentElement(elt) };\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"StartCloseTag\" && before.to <= pos)\n            return { type: \"closeTag\", from: pos, context: elt };\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"Is\")\n            return { type: \"attrValue\", from: pos, context: inTag };\n        if (before)\n            return { type: \"attrName\", from: pos, context: inTag };\n        return null;\n    }\n    else if (at.name == \"StartCloseTag\") {\n        return { type: \"closeTag\", from: pos, context: at.parent };\n    }\n    while (at.parent && at.to == pos && !((_a = at.lastChild) === null || _a === void 0 ? void 0 : _a.type.isError))\n        at = at.parent;\n    if (at.name == \"Element\" || at.name == \"Text\" || at.name == \"Document\")\n        return { type: \"tag\", from: pos, context: at.name == \"Element\" ? at : findParentElement(at) };\n    return null;\n}\nclass Element {\n    constructor(spec, attrs, attrValues) {\n        this.attrs = attrs;\n        this.attrValues = attrValues;\n        this.children = [];\n        this.name = spec.name;\n        this.completion = Object.assign(Object.assign({ type: \"type\" }, spec.completion || {}), { label: this.name });\n        this.openCompletion = Object.assign(Object.assign({}, this.completion), { label: \"<\" + this.name });\n        this.closeCompletion = Object.assign(Object.assign({}, this.completion), { label: \"</\" + this.name + \">\", boost: 2 });\n        this.closeNameCompletion = Object.assign(Object.assign({}, this.completion), { label: this.name + \">\" });\n        this.text = spec.textContent ? spec.textContent.map(s => ({ label: s, type: \"text\" })) : [];\n    }\n}\nconst Identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction attrCompletion(spec) {\n    return Object.assign(Object.assign({ type: \"property\" }, spec.completion || {}), { label: spec.name });\n}\nfunction valueCompletion(spec) {\n    return typeof spec == \"string\" ? { label: `\"${spec}\"`, type: \"constant\" }\n        : /^\"/.test(spec.label) ? spec\n            : Object.assign(Object.assign({}, spec), { label: `\"${spec.label}\"` });\n}\n/**\nCreate a completion source for the given schema.\n*/\nfunction completeFromSchema(eltSpecs, attrSpecs) {\n    let allAttrs = [], globalAttrs = [];\n    let attrValues = Object.create(null);\n    for (let s of attrSpecs) {\n        let completion = attrCompletion(s);\n        allAttrs.push(completion);\n        if (s.global)\n            globalAttrs.push(completion);\n        if (s.values)\n            attrValues[s.name] = s.values.map(valueCompletion);\n    }\n    let allElements = [], topElements = [];\n    let byName = Object.create(null);\n    for (let s of eltSpecs) {\n        let attrs = globalAttrs, attrVals = attrValues;\n        if (s.attributes)\n            attrs = attrs.concat(s.attributes.map(s => {\n                if (typeof s == \"string\")\n                    return allAttrs.find(a => a.label == s) || { label: s, type: \"property\" };\n                if (s.values) {\n                    if (attrVals == attrValues)\n                        attrVals = Object.create(attrVals);\n                    attrVals[s.name] = s.values.map(valueCompletion);\n                }\n                return attrCompletion(s);\n            }));\n        let elt = new Element(s, attrs, attrVals);\n        byName[elt.name] = elt;\n        allElements.push(elt);\n        if (s.top)\n            topElements.push(elt);\n    }\n    if (!topElements.length)\n        topElements = allElements;\n    for (let i = 0; i < allElements.length; i++) {\n        let s = eltSpecs[i], elt = allElements[i];\n        if (s.children) {\n            for (let ch of s.children)\n                if (byName[ch])\n                    elt.children.push(byName[ch]);\n        }\n        else {\n            elt.children = allElements;\n        }\n    }\n    return cx => {\n        var _a;\n        let { doc } = cx.state, loc = findLocation(cx.state, cx.pos);\n        if (!loc || (loc.type == \"tag\" && !cx.explicit))\n            return null;\n        let { type, from, context } = loc;\n        if (type == \"openTag\") {\n            let children = topElements;\n            let parentName = elementName$1(doc, context);\n            if (parentName) {\n                let parent = byName[parentName];\n                children = (parent === null || parent === void 0 ? void 0 : parent.children) || allElements;\n            }\n            return {\n                from,\n                options: children.map(ch => ch.completion),\n                validFor: Identifier\n            };\n        }\n        else if (type == \"closeTag\") {\n            let parentName = elementName$1(doc, context);\n            return parentName ? {\n                from,\n                to: cx.pos + (doc.sliceString(cx.pos, cx.pos + 1) == \">\" ? 1 : 0),\n                options: [((_a = byName[parentName]) === null || _a === void 0 ? void 0 : _a.closeNameCompletion) || { label: parentName + \">\", type: \"type\" }],\n                validFor: Identifier\n            } : null;\n        }\n        else if (type == \"attrName\") {\n            let parent = byName[tagName(doc, context)];\n            return {\n                from,\n                options: (parent === null || parent === void 0 ? void 0 : parent.attrs) || globalAttrs,\n                validFor: Identifier\n            };\n        }\n        else if (type == \"attrValue\") {\n            let attr = attrName(doc, context, from);\n            if (!attr)\n                return null;\n            let parent = byName[tagName(doc, context)];\n            let values = ((parent === null || parent === void 0 ? void 0 : parent.attrValues) || attrValues)[attr];\n            if (!values || !values.length)\n                return null;\n            return {\n                from,\n                to: cx.pos + (doc.sliceString(cx.pos, cx.pos + 1) == '\"' ? 1 : 0),\n                options: values,\n                validFor: /^\"[^\"]*\"?$/\n            };\n        }\n        else if (type == \"tag\") {\n            let parentName = elementName$1(doc, context), parent = byName[parentName];\n            let closing = [], last = context && context.lastChild;\n            if (parentName && (!last || last.name != \"CloseTag\" || tagName(doc, last) != parentName))\n                closing.push(parent ? parent.closeCompletion : { label: \"</\" + parentName + \">\", type: \"type\", boost: 2 });\n            let options = closing.concat(((parent === null || parent === void 0 ? void 0 : parent.children) || (context ? allElements : topElements)).map(e => e.openCompletion));\n            if (context && (parent === null || parent === void 0 ? void 0 : parent.text.length)) {\n                let openTag = context.firstChild;\n                if (openTag.to > cx.pos - 20 && !/\\S/.test(cx.state.sliceDoc(openTag.to, cx.pos)))\n                    options = options.concat(parent.text);\n            }\n            return {\n                from,\n                options,\n                validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/\n            };\n        }\n        else {\n            return null;\n        }\n    };\n}\n\n/**\nA language provider based on the [Lezer XML\nparser](https://github.com/lezer-parser/xml), extended with\nhighlighting and indentation information.\n*/\nconst xmlLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"xml\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Element(context) {\n                    let closed = /^\\s*<\\//.test(context.textAfter);\n                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                }\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                Element(subtree) {\n                    let first = subtree.firstChild, last = subtree.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : subtree.to };\n                }\n            }),\n            /*@__PURE__*/bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/$/\n    }\n});\n/**\nXML language support. Includes schema-based autocompletion when\nconfigured.\n*/\nfunction xml(conf = {}) {\n    let support = [xmlLanguage.data.of({\n            autocomplete: completeFromSchema(conf.elements || [], conf.attributes || [])\n        })];\n    if (conf.autoCloseTags !== false)\n        support.push(autoCloseTags);\n    return new LanguageSupport(xmlLanguage, support);\n}\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !xmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let { head } = range;\n        let didType = state.doc.sliceString(head - 1, head) == text;\n        let after = syntaxTree(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head))) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"StartCloseTag\") {\n            let base = after.parent;\n            if (after.from == head - 2 && ((_c = base.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, base, head))) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\nexport { autoCloseTags, completeFromSchema, xml, xmlLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA,IAAM,WAAW;AAAjB,IACE,gBAAgB;AADlB,IAEE,kBAAkB;AAFpB,IAGE,0BAA0B;AAH5B,IAIE,0BAA0B;AAJ5B,IAKE,mBAAmB;AALrB,IAME,cAAc;AANhB,IAOE,iBAAiB;AAPnB,IAQE,UAAU;AARZ,IASE,UAAU;AAIZ,SAAS,SAAS,IAAI;AACpB,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAChH;AAEA,SAAS,QAAQ,IAAI;AACnB,SAAO,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AAClD;AAEA,IAAI,aAAa;AAAjB,IAAuB,cAAc;AAArC,IAA2C,YAAY;AACvD,SAAS,aAAa,OAAO,QAAQ;AACnC,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI,eAAe,SAAS,aAAa,IAAK,QAAO;AACrD,SAAO,QAAQ,MAAM,KAAK,MAAM,CAAC,EAAG;AACpC,MAAI,OAAO;AACX,aAAS;AACP,QAAI,OAAO,MAAM,KAAK,MAAM;AAC5B,QAAI,CAAC,SAAS,IAAI,EAAG;AACrB,YAAQ,OAAO,aAAa,IAAI;AAChC;AAAA,EACF;AACA,gBAAc;AAAO,cAAY;AACjC,SAAO,aAAa,QAAQ;AAC9B;AAEA,SAAS,eAAe,MAAM,QAAQ;AACpC,OAAK,OAAO;AACZ,OAAK,SAAS;AAChB;AAEA,IAAM,iBAAiB,IAAI,eAAe;AAAA,EACxC,OAAO;AAAA,EACP,MAAM,SAAS,MAAM,OAAO,OAAO;AACjC,WAAO,QAAQ,WAAW,IAAI,eAAe,aAAa,OAAO,CAAC,KAAK,IAAI,OAAO,IAAI;AAAA,EACxF;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,WAAO,QAAQ,WAAW,UAAU,QAAQ,SAAS;AAAA,EACvD;AAAA,EACA,MAAM,SAAS,MAAM,QAAQ,OAAO;AAClC,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,QAAQ,YAAY,QAAQ,UAC/B,IAAI,eAAe,aAAa,OAAO,CAAC,KAAK,IAAI,OAAO,IAAI;AAAA,EAClE;AAAA,EACA,QAAQ;AACV,CAAC;AAED,IAAM,WAAW,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACvD,MAAI,MAAM,QAAQ,GAAc;AAChC,QAAM,QAAQ;AACd,MAAI,MAAM,QAAQ,IAAc;AAC9B,UAAM,QAAQ;AACd,QAAI,OAAO,aAAa,OAAO,CAAC;AAChC,QAAI,CAAC,KAAM,QAAO,MAAM,YAAY,uBAAuB;AAC3D,QAAI,MAAM,WAAW,QAAQ,MAAM,QAAQ,KAAM,QAAO,MAAM,YAAY,aAAa;AACvF,aAAS,KAAK,MAAM,SAAS,IAAI,KAAK,GAAG,OAAQ,KAAI,GAAG,QAAQ,KAAM,QAAO,MAAM,YAAY,iBAAiB,EAAE;AAClH,UAAM,YAAY,uBAAuB;AAAA,EAC3C,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,IAAc;AACnE,WAAO,MAAM,YAAY,QAAQ;AAAA,EACnC;AACF,GAAG,EAAC,YAAY,KAAI,CAAC;AAErB,SAAS,OAAO,MAAM,KAAK;AACzB,SAAO,IAAI,kBAAkB,WAAS;AACpC,QAAI,MAAM,GAAG,QAAQ,IAAI,WAAW,CAAC;AACrC,SAAM,UAAQ,MAAM,QAAQ,GAAG,OAAO;AACpC,UAAI,MAAM,OAAO,EAAG;AACpB,UAAI,MAAM,QAAQ,OAAO;AACvB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC9B,cAAI,MAAM,KAAK,CAAC,KAAK,IAAI,WAAW,CAAC,EAAG,UAAS;AACnD;AAAA,MACF;AAAA,IACF;AACA,QAAI,IAAK,OAAM,YAAY,IAAI;AAAA,EACjC,CAAC;AACH;AAEA,IAAM,iBAAiB,OAAO,kBAAkB,KAAK;AACrD,IAAM,YAAY,OAAO,aAAa,IAAI;AAC1C,IAAM,eAAe,OAAO,gBAAgB,KAAK;AAEjD,IAAM,kBAAkB,UAAU;AAAA,EAChC,MAAM,KAAK;AAAA,EACX,iDAAiD,KAAK;AAAA,EACtD,SAAS,KAAK;AAAA,EACd,8BAA8B,CAAC,KAAK,SAAS,KAAK,OAAO;AAAA,EACzD,eAAe,KAAK;AAAA,EACpB,gBAAgB,KAAK;AAAA,EACrB,IAAI,KAAK;AAAA,EACT,sCAAsC,KAAK;AAAA,EAC3C,SAAS,KAAK;AAAA,EACd,gBAAgB,KAAK;AAAA,EACrB,aAAa,KAAK;AAAA,EAClB,OAAO,KAAK,QAAQ,KAAK,MAAM;AACjC,CAAC;AAGD,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,YAAY,GAAE,0BAAyB,IAAG,0BAA0B;AAAA,IACrE,CAAC,YAAY,IAAG,0BAAyB,IAAG,WAAU,IAAG,UAAU;AAAA,IACnE,CAAC,WAAW,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAA,EACrC;AAAA,EACA,aAAa,CAAC,eAAe;AAAA,EAC7B,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,UAAU,gBAAgB,WAAW,cAAc,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7E,UAAU,EAAC,YAAW,CAAC,GAAE,CAAC,EAAC;AAAA,EAC3B,WAAW;AACb,CAAC;;;AC/HD,SAAS,QAAQ,KAAK,KAAK;AACvB,MAAI,OAAO,OAAO,IAAI,SAAS,SAAS;AACxC,SAAO,OAAO,IAAI,YAAY,KAAK,MAAM,KAAK,EAAE,IAAI;AACxD;AACA,SAAS,cAAc,KAAK,MAAM;AAC9B,MAAI,MAAM,QAAQ,KAAK;AACvB,SAAO,CAAC,OAAO,IAAI,QAAQ,YAAY,KAAK,QAAQ,KAAK,GAAG;AAChE;AACA,SAAS,SAAS,KAAK,KAAK,KAAK;AAC7B,MAAI,OAAO,OAAO,IAAI,YAAY,WAAW,EAAE,KAAK,OAAK,EAAE,QAAQ,OAAO,EAAE,MAAM,GAAG;AACrF,MAAI,OAAO,QAAQ,KAAK,SAAS,eAAe;AAChD,SAAO,OAAO,IAAI,YAAY,KAAK,MAAM,KAAK,EAAE,IAAI;AACxD;AACA,SAAS,kBAAkB,MAAM;AAC7B,WAAS,MAAM,QAAQ,KAAK,QAAQ,KAAK,MAAM,IAAI;AAC/C,QAAI,IAAI,QAAQ;AACZ,aAAO;AACf,SAAO;AACX;AACA,SAAS,aAAa,OAAO,KAAK;AAC9B,MAAI;AACJ,MAAI,KAAK,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE,GAAG,QAAQ;AAC1D,WAAS,MAAM,IAAI,CAAC,SAAS,IAAI,QAAQ,MAAM,IAAI;AAC/C,QAAI,IAAI,QAAQ,aAAa,IAAI,QAAQ,cAAc,IAAI,QAAQ,oBAAoB,IAAI,QAAQ;AAC/F,cAAQ;AAChB,MAAI,UAAU,MAAM,KAAK,OAAO,MAAM,UAAU,KAAK,UAAU;AAC3D,QAAI,MAAM,MAAM;AAChB,QAAI,GAAG,QAAQ;AACX,aAAO,MAAM,QAAQ,cAAc,MAAM,QAAQ,uBAC3C,EAAE,MAAM,YAAY,MAAM,GAAG,MAAM,SAAS,IAAI,IAChD,EAAE,MAAM,WAAW,MAAM,GAAG,MAAM,SAAS,kBAAkB,GAAG,EAAE;AAC5E,QAAI,GAAG,QAAQ;AACX,aAAO,EAAE,MAAM,YAAY,MAAM,GAAG,MAAM,SAAS,MAAM;AAC7D,QAAI,GAAG,QAAQ;AACX,aAAO,EAAE,MAAM,aAAa,MAAM,GAAG,MAAM,SAAS,MAAM;AAC9D,QAAI,SAAS,MAAM,SAAS,GAAG,QAAQ,cAAc,GAAG,YAAY,GAAG,IAAI;AAC3E,SAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AACjE,aAAO,EAAE,MAAM,WAAW,MAAM,KAAK,SAAS,kBAAkB,GAAG,EAAE;AACzE,SAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,mBAAmB,OAAO,MAAM;AACjG,aAAO,EAAE,MAAM,YAAY,MAAM,KAAK,SAAS,IAAI;AACvD,SAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AACjE,aAAO,EAAE,MAAM,aAAa,MAAM,KAAK,SAAS,MAAM;AAC1D,QAAI;AACA,aAAO,EAAE,MAAM,YAAY,MAAM,KAAK,SAAS,MAAM;AACzD,WAAO;AAAA,EACX,WACS,GAAG,QAAQ,iBAAiB;AACjC,WAAO,EAAE,MAAM,YAAY,MAAM,KAAK,SAAS,GAAG,OAAO;AAAA,EAC7D;AACA,SAAO,GAAG,UAAU,GAAG,MAAM,OAAO,GAAG,KAAK,GAAG,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AACnG,SAAK,GAAG;AACZ,MAAI,GAAG,QAAQ,aAAa,GAAG,QAAQ,UAAU,GAAG,QAAQ;AACxD,WAAO,EAAE,MAAM,OAAO,MAAM,KAAK,SAAS,GAAG,QAAQ,YAAY,KAAK,kBAAkB,EAAE,EAAE;AAChG,SAAO;AACX;AACA,IAAMA,WAAN,MAAc;AAAA,EACV,YAAY,MAAM,OAAO,YAAY;AACjC,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,WAAW,CAAC;AACjB,SAAK,OAAO,KAAK;AACjB,SAAK,aAAa,OAAO,OAAO,OAAO,OAAO,EAAE,MAAM,OAAO,GAAG,KAAK,cAAc,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,KAAK,CAAC;AAC5G,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,UAAU,GAAG,EAAE,OAAO,MAAM,KAAK,KAAK,CAAC;AAClG,SAAK,kBAAkB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,UAAU,GAAG,EAAE,OAAO,OAAO,KAAK,OAAO,KAAK,OAAO,EAAE,CAAC;AACpH,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,UAAU,GAAG,EAAE,OAAO,KAAK,OAAO,IAAI,CAAC;AACvG,SAAK,OAAO,KAAK,cAAc,KAAK,YAAY,IAAI,QAAM,EAAE,OAAO,GAAG,MAAM,OAAO,EAAE,IAAI,CAAC;AAAA,EAC9F;AACJ;AACA,IAAM,aAAa;AACnB,SAAS,eAAe,MAAM;AAC1B,SAAO,OAAO,OAAO,OAAO,OAAO,EAAE,MAAM,WAAW,GAAG,KAAK,cAAc,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,KAAK,CAAC;AACzG;AACA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,OAAO,QAAQ,WAAW,EAAE,OAAO,IAAI,IAAI,KAAK,MAAM,WAAW,IAClE,KAAK,KAAK,KAAK,KAAK,IAAI,OACpB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,IAAI,CAAC;AACjF;AAIA,SAAS,mBAAmB,UAAU,WAAW;AAC7C,MAAI,WAAW,CAAC,GAAG,cAAc,CAAC;AAClC,MAAI,aAAa,uBAAO,OAAO,IAAI;AACnC,WAAS,KAAK,WAAW;AACrB,QAAI,aAAa,eAAe,CAAC;AACjC,aAAS,KAAK,UAAU;AACxB,QAAI,EAAE;AACF,kBAAY,KAAK,UAAU;AAC/B,QAAI,EAAE;AACF,iBAAW,EAAE,IAAI,IAAI,EAAE,OAAO,IAAI,eAAe;AAAA,EACzD;AACA,MAAI,cAAc,CAAC,GAAG,cAAc,CAAC;AACrC,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,WAAS,KAAK,UAAU;AACpB,QAAI,QAAQ,aAAa,WAAW;AACpC,QAAI,EAAE;AACF,cAAQ,MAAM,OAAO,EAAE,WAAW,IAAI,CAAAC,OAAK;AACvC,YAAI,OAAOA,MAAK;AACZ,iBAAO,SAAS,KAAK,OAAK,EAAE,SAASA,EAAC,KAAK,EAAE,OAAOA,IAAG,MAAM,WAAW;AAC5E,YAAIA,GAAE,QAAQ;AACV,cAAI,YAAY;AACZ,uBAAW,OAAO,OAAO,QAAQ;AACrC,mBAASA,GAAE,IAAI,IAAIA,GAAE,OAAO,IAAI,eAAe;AAAA,QACnD;AACA,eAAO,eAAeA,EAAC;AAAA,MAC3B,CAAC,CAAC;AACN,QAAI,MAAM,IAAID,SAAQ,GAAG,OAAO,QAAQ;AACxC,WAAO,IAAI,IAAI,IAAI;AACnB,gBAAY,KAAK,GAAG;AACpB,QAAI,EAAE;AACF,kBAAY,KAAK,GAAG;AAAA,EAC5B;AACA,MAAI,CAAC,YAAY;AACb,kBAAc;AAClB,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,QAAI,IAAI,SAAS,CAAC,GAAG,MAAM,YAAY,CAAC;AACxC,QAAI,EAAE,UAAU;AACZ,eAAS,MAAM,EAAE;AACb,YAAI,OAAO,EAAE;AACT,cAAI,SAAS,KAAK,OAAO,EAAE,CAAC;AAAA,IACxC,OACK;AACD,UAAI,WAAW;AAAA,IACnB;AAAA,EACJ;AACA,SAAO,QAAM;AACT,QAAI;AACJ,QAAI,EAAE,IAAI,IAAI,GAAG,OAAO,MAAM,aAAa,GAAG,OAAO,GAAG,GAAG;AAC3D,QAAI,CAAC,OAAQ,IAAI,QAAQ,SAAS,CAAC,GAAG;AAClC,aAAO;AACX,QAAI,EAAE,MAAM,MAAM,QAAQ,IAAI;AAC9B,QAAI,QAAQ,WAAW;AACnB,UAAI,WAAW;AACf,UAAI,aAAa,cAAc,KAAK,OAAO;AAC3C,UAAI,YAAY;AACZ,YAAI,SAAS,OAAO,UAAU;AAC9B,oBAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aAAa;AAAA,MACpF;AACA,aAAO;AAAA,QACH;AAAA,QACA,SAAS,SAAS,IAAI,QAAM,GAAG,UAAU;AAAA,QACzC,UAAU;AAAA,MACd;AAAA,IACJ,WACS,QAAQ,YAAY;AACzB,UAAI,aAAa,cAAc,KAAK,OAAO;AAC3C,aAAO,aAAa;AAAA,QAChB;AAAA,QACA,IAAI,GAAG,OAAO,IAAI,YAAY,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,IAAI;AAAA,QAC/D,SAAS,GAAG,KAAK,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,wBAAwB,EAAE,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAAA,QAC9I,UAAU;AAAA,MACd,IAAI;AAAA,IACR,WACS,QAAQ,YAAY;AACzB,UAAI,SAAS,OAAO,QAAQ,KAAK,OAAO,CAAC;AACzC,aAAO;AAAA,QACH;AAAA,QACA,UAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU;AAAA,QAC3E,UAAU;AAAA,MACd;AAAA,IACJ,WACS,QAAQ,aAAa;AAC1B,UAAI,OAAO,SAAS,KAAK,SAAS,IAAI;AACtC,UAAI,CAAC;AACD,eAAO;AACX,UAAI,SAAS,OAAO,QAAQ,KAAK,OAAO,CAAC;AACzC,UAAI,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,eAAe,YAAY,IAAI;AACrG,UAAI,CAAC,UAAU,CAAC,OAAO;AACnB,eAAO;AACX,aAAO;AAAA,QACH;AAAA,QACA,IAAI,GAAG,OAAO,IAAI,YAAY,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,IAAI;AAAA,QAC/D,SAAS;AAAA,QACT,UAAU;AAAA,MACd;AAAA,IACJ,WACS,QAAQ,OAAO;AACpB,UAAI,aAAa,cAAc,KAAK,OAAO,GAAG,SAAS,OAAO,UAAU;AACxE,UAAI,UAAU,CAAC,GAAG,OAAO,WAAW,QAAQ;AAC5C,UAAI,eAAe,CAAC,QAAQ,KAAK,QAAQ,cAAc,QAAQ,KAAK,IAAI,KAAK;AACzE,gBAAQ,KAAK,SAAS,OAAO,kBAAkB,EAAE,OAAO,OAAO,aAAa,KAAK,MAAM,QAAQ,OAAO,EAAE,CAAC;AAC7G,UAAI,UAAU,QAAQ,SAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,cAAc,UAAU,cAAc,cAAc,IAAI,OAAK,EAAE,cAAc,CAAC;AACpK,UAAI,YAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,SAAS;AACjF,YAAI,UAAU,QAAQ;AACtB,YAAI,QAAQ,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK,KAAK,GAAG,MAAM,SAAS,QAAQ,IAAI,GAAG,GAAG,CAAC;AAC5E,oBAAU,QAAQ,OAAO,OAAO,IAAI;AAAA,MAC5C;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,UAAU;AAAA,MACd;AAAA,IACJ,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAOA,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,QAAQ,SAAS;AACb,cAAI,SAAS,UAAU,KAAK,QAAQ,SAAS;AAC7C,iBAAO,QAAQ,WAAW,QAAQ,KAAK,IAAI,KAAK,SAAS,IAAI,QAAQ;AAAA,QACzE;AAAA,QACA,kCAAkC,SAAS;AACvC,iBAAO,QAAQ,OAAO,QAAQ,KAAK,IAAI,IAAI,QAAQ;AAAA,QACvD;AAAA,MACJ,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,QAAQ,SAAS;AACb,cAAI,QAAQ,QAAQ,YAAY,OAAO,QAAQ;AAC/C,cAAI,CAAC,SAAS,MAAM,QAAQ;AACxB,mBAAO;AACX,iBAAO,EAAE,MAAM,MAAM,IAAI,IAAI,KAAK,QAAQ,aAAa,KAAK,OAAO,QAAQ,GAAG;AAAA,QAClF;AAAA,MACJ,CAAC;AAAA,MACY,sBAAsB,IAAI;AAAA,QACnC,oBAAoB,UAAQ,KAAK,SAAS,SAAS;AAAA,MACvD,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,OAAO,EAAE,MAAM,QAAQ,OAAO,MAAM,EAAE;AAAA,IACvD,eAAe;AAAA,EACnB;AACJ,CAAC;AAKD,SAAS,IAAI,OAAO,CAAC,GAAG;AACpB,MAAI,UAAU,CAAC,YAAY,KAAK,GAAG;AAAA,IAC3B,cAAc,mBAAmB,KAAK,YAAY,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,EAC/E,CAAC,CAAC;AACN,MAAI,KAAK,kBAAkB;AACvB,YAAQ,KAAK,aAAa;AAC9B,SAAO,IAAI,gBAAgB,aAAa,OAAO;AACnD;AACA,SAAS,YAAY,KAAK,MAAM,MAAM,IAAI,QAAQ;AAC9C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,KAAK;AACf,MAAI,OAAO,OAAO,IAAI,SAAS,SAAS;AACxC,SAAO,OAAO,IAAI,YAAY,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AACvE;AAKA,IAAM,gBAA6B,WAAW,aAAa,GAAG,CAAC,MAAM,MAAM,IAAI,MAAM,sBAAsB;AACvG,MAAI,KAAK,aAAa,KAAK,MAAM,YAAY,QAAQ,MAAO,QAAQ,OAAO,QAAQ,OAC/E,CAAC,YAAY,WAAW,KAAK,OAAO,MAAM,EAAE;AAC5C,WAAO;AACX,MAAI,OAAO,kBAAkB,GAAG,EAAE,MAAM,IAAI;AAC5C,MAAI,YAAY,MAAM,cAAc,WAAS;AACzC,QAAI,IAAI,IAAI;AACZ,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,UAAU,MAAM,IAAI,YAAY,OAAO,GAAG,IAAI,KAAK;AACvD,QAAI,QAAQ,WAAW,KAAK,EAAE,aAAa,MAAM,EAAE,GAAG;AACtD,QAAI,WAAW,QAAQ,OAAO,MAAM,QAAQ,UAAU;AAClD,UAAI,MAAM,MAAM;AAChB,YAAM,MAAM,KAAK,IAAI,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,eAC5H,OAAO,YAAY,MAAM,KAAK,IAAI,QAAQ,IAAI,IAAI;AACnD,YAAIE,MAAK,QAAQ,MAAM,IAAI,YAAY,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI;AACrE,YAAI,SAAS,KAAK,IAAI;AACtB,eAAO,EAAE,OAAO,SAAS,EAAE,MAAM,MAAM,IAAAA,KAAI,OAAO,EAAE;AAAA,MACxD;AAAA,IACJ,WACS,WAAW,QAAQ,OAAO,MAAM,QAAQ,iBAAiB;AAC9D,UAAIC,QAAO,MAAM;AACjB,UAAI,MAAM,QAAQ,OAAO,OAAO,KAAKA,MAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,eACjG,OAAO,YAAY,MAAM,KAAKA,OAAM,IAAI,IAAI;AAC7C,YAAID,MAAK,QAAQ,MAAM,IAAI,YAAY,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI;AACrE,YAAI,SAAS,GAAG,IAAI;AACpB,eAAO;AAAA,UACH,OAAO,gBAAgB,OAAO,OAAO,OAAO,QAAQ,EAAE;AAAA,UACtD,SAAS,EAAE,MAAM,MAAM,IAAAA,KAAI,OAAO;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,EAAE,MAAM;AAAA,EACnB,CAAC;AACD,MAAI,UAAU,QAAQ;AAClB,WAAO;AACX,OAAK,SAAS;AAAA,IACV;AAAA,IACA,MAAM,OAAO,WAAW;AAAA,MACpB,WAAW;AAAA,MACX,gBAAgB;AAAA,IACpB,CAAC;AAAA,EACL,CAAC;AACD,SAAO;AACX,CAAC;", "names": ["Element", "s", "to", "base"]}