package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__11;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysDictDataBoToSysDictDataMapper__11.class,SysDictDataVoToSysDictDataMapper__11.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__11 extends BaseMapper<SysDictData, SysDictDataVo> {
}
