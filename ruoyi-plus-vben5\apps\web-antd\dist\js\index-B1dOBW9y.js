import{aM as ce,g as U,_ as p,v as at,d6 as nt,e as Ye,bd as Ue,aJ as be,h as ue,j as ot,m as Ze,bO as Ae,bk as Oe,bj as rt,cx as st,o as it,cL as ct,bx as _e,aG as ut,bQ as dt,p as vt,ck as ft,aH as ht,cS as pt,c as ze,aE as mt}from"./bootstrap-DCMzVRvD.js";import{u as gt}from"./useMemo-BwJyMulH.js";import{T as yt,a as bt,d as He,e as Ct,g as St,r as wt}from"./index-BM5PBg44.js";import{h as xt,u as It,a as Vt,j as kt,t as Ge,B as Tt,d as Nt,e as Lt,g as Et}from"./index-B-GBMyZJ.js";import{a5 as Dt,a4 as Pt,d as Re,p as ge,B as y,q as ye,C as j,a as z,I as Kt,A as te,D as $e,$ as ne,aA as At}from"../jse/index-index-C-MnMZEz.js";import{g as Ot,a as _t}from"./statusUtils-d85DZFMd.js";import{g as Ft}from"./index-DabkQ3D7.js";import"./List-DFkqSBvs.js";import"./index-CHpIOV4R.js";import"./eagerComputed-CeBU4kWY.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./Overflow-DmNzxpBy.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CERO2SW5.js";import"./SearchOutlined-BOD_ZIye.js";import"./move-DLDqWE9R.js";import"./slide-B82O6h2Y.js";function Ht(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}function Mt(e){const{label:i,value:a,children:t}=e||{},l=a||"value";return{_title:i?[i]:["title","label"],value:l,key:l,children:t||"children"}}function Me(e){return e.disabled||e.disableCheckbox||e.checkable===!1}function jt(e,i){const a=[];function t(l){l.forEach(n=>{a.push(n[i.value]);const o=n[i.children];o&&t(o)})}return t(e),a}function Xe(e){return e==null}const et=Symbol("TreeSelectContextPropsKey");function Rt(e){return Pt(et,e)}function $t(){return Dt(et,{})}const Bt={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Wt=Re({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,setup(e,i){let{slots:a,expose:t}=i;const l=It(),n=xt(),o=$t(),c=ge(),v=gt(()=>o.treeData,[()=>l.open,()=>o.treeData],d=>d[0]),r=y(()=>{const{checkable:d,halfCheckedKeys:b,checkedKeys:E}=n;return d?{checked:E,halfChecked:b}:null});ye(()=>l.open,()=>{Kt(()=>{var d;l.open&&!l.multiple&&n.checkedKeys.length&&((d=c.value)===null||d===void 0||d.scrollTo({key:n.checkedKeys[0]}))})},{immediate:!0,flush:"post"});const u=y(()=>String(l.searchValue).toLowerCase()),g=d=>u.value?String(d[n.treeNodeFilterProp]).toLowerCase().includes(u.value):!1,V=j(n.treeDefaultExpandedKeys),S=j(null);ye(()=>l.searchValue,()=>{l.searchValue&&(S.value=jt(te(o.treeData),te(o.fieldNames)))},{immediate:!0});const I=y(()=>n.treeExpandedKeys?n.treeExpandedKeys.slice():l.searchValue?S.value:V.value),w=d=>{var b;V.value=d,S.value=d,(b=n.onTreeExpand)===null||b===void 0||b.call(n,d)},k=d=>{d.preventDefault()},_=(d,b)=>{let{node:E}=b;var K,N;const{checkable:W,checkedKeys:G}=n;W&&Me(E)||((K=o.onSelect)===null||K===void 0||K.call(o,E.key,{selected:!G.includes(E.key)}),l.multiple||(N=l.toggleOpen)===null||N===void 0||N.call(l,!1))},A=ge(null),O=y(()=>n.keyEntities[A.value]),F=d=>{A.value=d};return t({scrollTo:function(){for(var d,b,E=arguments.length,K=new Array(E),N=0;N<E;N++)K[N]=arguments[N];return(b=(d=c.value)===null||d===void 0?void 0:d.scrollTo)===null||b===void 0?void 0:b.call(d,...K)},onKeydown:d=>{var b;const{which:E}=d;switch(E){case ce.UP:case ce.DOWN:case ce.LEFT:case ce.RIGHT:(b=c.value)===null||b===void 0||b.onKeydown(d);break;case ce.ENTER:{if(O.value){const{selectable:K,value:N}=O.value.node||{};K!==!1&&_(null,{node:{key:A.value},selected:!n.checkedKeys.includes(N)})}break}case ce.ESC:l.toggleOpen(!1)}},onKeyup:()=>{}}),()=>{var d;const{prefixCls:b,multiple:E,searchValue:K,open:N,notFoundContent:W=(d=a.notFoundContent)===null||d===void 0?void 0:d.call(a)}=l,{listHeight:G,listItemHeight:R,virtual:le,dropdownMatchSelectWidth:X,treeExpandAction:oe}=o,{checkable:re,treeDefaultExpandAll:se,treeIcon:ae,showTreeIcon:q,switcherIcon:ve,treeLine:fe,loadData:he,treeLoadedKeys:ie,treeMotion:m,onTreeLoad:D,checkedKeys:H}=n;if(v.value.length===0)return z("div",{role:"listbox",class:`${b}-empty`,onMousedown:k},[W]);const J={fieldNames:o.fieldNames};return ie&&(J.loadedKeys=ie),I.value&&(J.expandedKeys=I.value),z("div",{onMousedown:k},[O.value&&N&&z("span",{style:Bt,"aria-live":"assertive"},[O.value.node.value]),z(yt,U(U({ref:c,focusable:!1,prefixCls:`${b}-tree`,treeData:v.value,height:G,itemHeight:R,virtual:le!==!1&&X!==!1,multiple:E,icon:ae,showIcon:q,switcherIcon:ve,showLine:fe,loadData:K?null:he,motion:m,activeKey:A.value,checkable:re,checkStrictly:!0,checkedKeys:r.value,selectedKeys:re?[]:H,defaultExpandAll:se},J),{},{onActiveChange:F,onSelect:_,onCheck:_,onExpand:w,onLoad:D,filterTreeNode:g,expandAction:oe}),p(p({},a),{checkable:n.customSlots.treeCheckable}))])}}}),Ut="SHOW_ALL",tt="SHOW_PARENT",Be="SHOW_CHILD";function qe(e,i,a,t){const l=new Set(e);return i===Be?e.filter(n=>{const o=a[n];return!(o&&o.children&&o.children.some(c=>{let{node:v}=c;return l.has(v[t.value])})&&o.children.every(c=>{let{node:v}=c;return Me(v)||l.has(v[t.value])}))}):i===tt?e.filter(n=>{const o=a[n],c=o?o.parent:null;return!(c&&!Me(c.node)&&l.has(c.key))}):e}const de=()=>null;de.inheritAttrs=!1;de.displayName="ATreeSelectNode";de.isTreeSelectNode=!0;var zt=function(e,i){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&i.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)i.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(a[t[l]]=e[t[l]]);return a};function Gt(e){return e&&e.type&&e.type.isTreeSelectNode}function Xt(e){function i(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return at(a).map(t=>{var l,n,o;if(!Gt(t))return null;const c=t.children||{},v=t.key,r={};for(const[E,K]of Object.entries(t.props))r[nt(E)]=K;const{isLeaf:u,checkable:g,selectable:V,disabled:S,disableCheckbox:I}=r,w={isLeaf:u||u===""||void 0,checkable:g||g===""||void 0,selectable:V||V===""||void 0,disabled:S||S===""||void 0,disableCheckbox:I||I===""||void 0},k=p(p({},r),w),{title:_=(l=c.title)===null||l===void 0?void 0:l.call(c,k),switcherIcon:A=(n=c.switcherIcon)===null||n===void 0?void 0:n.call(c,k)}=r,O=zt(r,["title","switcherIcon"]),F=(o=c.default)===null||o===void 0?void 0:o.call(c),d=p(p(p({},O),{title:_,switcherIcon:A,key:v,isLeaf:u}),w),b=i(F);return b.length&&(d.children=b),d})}return i(e)}function je(e){if(!e)return e;const i=p({},e);return"props"in i||Object.defineProperty(i,"props",{get(){return i}}),i}function qt(e,i,a,t,l,n){let o=null,c=null;function v(){function r(u){let g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"0",V=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return u.map((S,I)=>{const w=`${g}-${I}`,k=S[n.value],_=a.includes(k),A=r(S[n.children]||[],w,_),O=z(de,S,{default:()=>[A.map(F=>F.node)]});if(i===k&&(o=O),_){const F={pos:w,node:O,children:A};return V||c.push(F),F}return null}).filter(S=>S)}c||(c=[],r(t),c.sort((u,g)=>{let{node:{props:{value:V}}}=u,{node:{props:{value:S}}}=g;const I=a.indexOf(V),w=a.indexOf(S);return I-w}))}Object.defineProperty(e,"triggerNode",{get(){return v(),o}}),Object.defineProperty(e,"allCheckedNodes",{get(){return v(),l?c:c.map(r=>{let{node:u}=r;return u})}})}function Jt(e,i){let{id:a,pId:t,rootPId:l}=i;const n={},o=[];return e.map(v=>{const r=p({},v),u=r[a];return n[u]=r,r.key=r.key||u,r}).forEach(v=>{const r=v[t],u=n[r];u&&(u.children=u.children||[],u.children.push(v)),(r===l||!u&&l===null)&&o.push(v)}),o}function Qt(e,i,a){const t=j();return ye([a,e,i],()=>{const l=a.value;e.value?t.value=a.value?Jt(te(e.value),p({id:"id",pId:"pId",rootPId:null},l!==!0?l:{})):te(e.value).slice():t.value=Xt(te(i.value))},{immediate:!0,deep:!0}),t}const Yt=e=>{const i=j({valueLabels:new Map}),a=j();return ye(e,()=>{a.value=te(e.value)},{immediate:!0}),[y(()=>{const{valueLabels:l}=i.value,n=new Map,o=a.value.map(c=>{var v;const{value:r}=c,u=(v=c.label)!==null&&v!==void 0?v:l.get(r);return n.set(r,u),p(p({},c),{label:u})});return i.value.valueLabels=n,o})]},Zt=(e,i)=>{const a=j(new Map),t=j({});return $e(()=>{const l=i.value,n=bt(e.value,{fieldNames:l,initWrapper:o=>p(p({},o),{valueEntities:new Map}),processEntity:(o,c)=>{const v=o.node[l.value];c.valueEntities.set(v,o)}});a.value=n.valueEntities,t.value=n.keyEntities}),{valueEntities:a,keyEntities:t}},el=(e,i,a,t,l,n)=>{const o=j([]),c=j([]);return $e(()=>{let v=e.value.map(g=>{let{value:V}=g;return V}),r=i.value.map(g=>{let{value:V}=g;return V});const u=v.filter(g=>!t.value[g]);a.value&&({checkedKeys:v,halfCheckedKeys:r}=He(v,!0,t.value,l.value,n.value)),o.value=Array.from(new Set([...u,...v])),c.value=r}),[o,c]},tl=(e,i,a)=>{let{treeNodeFilterProp:t,filterTreeNode:l,fieldNames:n}=a;return y(()=>{const{children:o}=n.value,c=i.value,v=t==null?void 0:t.value;if(!c||l.value===!1)return e.value;let r;if(typeof l.value=="function")r=l.value;else{const g=c.toUpperCase();r=(V,S)=>{const I=S[v];return String(I).toUpperCase().includes(g)}}function u(g){let V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const S=[];for(let I=0,w=g.length;I<w;I++){const k=g[I],_=k[o],A=V||r(c,je(k)),O=u(_||[],A);(A||O.length)&&S.push(p(p({},k),{[o]:O}))}return S}return u(e.value)})};function lt(){return p(p({},be(Nt(),["mode"])),{prefixCls:String,id:String,value:{type:[String,Number,Object,Array]},defaultValue:{type:[String,Number,Object,Array]},onChange:{type:Function},searchValue:String,inputValue:String,onSearch:{type:Function},autoClearSearchValue:{type:Boolean,default:void 0},filterTreeNode:{type:[Boolean,Function],default:void 0},treeNodeFilterProp:String,onSelect:Function,onDeselect:Function,showCheckedStrategy:{type:String},treeNodeLabelProp:String,fieldNames:{type:Object},multiple:{type:Boolean,default:void 0},treeCheckable:{type:Boolean,default:void 0},treeCheckStrictly:{type:Boolean,default:void 0},labelInValue:{type:Boolean,default:void 0},treeData:{type:Array},treeDataSimpleMode:{type:[Boolean,Object],default:void 0},loadData:{type:Function},treeLoadedKeys:{type:Array},onTreeLoad:{type:Function},treeDefaultExpandAll:{type:Boolean,default:void 0},treeExpandedKeys:{type:Array},treeDefaultExpandedKeys:{type:Array},onTreeExpand:{type:Function},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,onDropdownVisibleChange:{type:Function},treeLine:{type:[Boolean,Object],default:void 0},treeIcon:ue.any,showTreeIcon:{type:Boolean,default:void 0},switcherIcon:ue.any,treeMotion:ue.any,children:Array,treeExpandAction:String,showArrow:{type:Boolean,default:void 0},showSearch:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},placeholder:ue.any,maxTagPlaceholder:{type:Function},dropdownPopupAlign:ue.any,customSlots:Object})}function ll(e){return!e||typeof e!="object"}const al=Re({compatConfig:{MODE:3},name:"TreeSelect",inheritAttrs:!1,props:Ye(lt(),{treeNodeFilterProp:"value",autoClearSearchValue:!0,showCheckedStrategy:Be,listHeight:200,listItemHeight:20,prefixCls:"vc-tree-select"}),setup(e,i){let{attrs:a,expose:t,slots:l}=i;const n=Vt(ne(e,"id")),o=y(()=>e.treeCheckable&&!e.treeCheckStrictly),c=y(()=>e.treeCheckable||e.treeCheckStrictly),v=y(()=>e.treeCheckStrictly||e.labelInValue),r=y(()=>c.value||e.multiple),u=y(()=>Mt(e.fieldNames)),[g,V]=Ue("",{value:y(()=>e.searchValue!==void 0?e.searchValue:e.inputValue),postState:s=>s||""}),S=s=>{var f;V(s),(f=e.onSearch)===null||f===void 0||f.call(e,s)},I=Qt(ne(e,"treeData"),ne(e,"children"),ne(e,"treeDataSimpleMode")),{keyEntities:w,valueEntities:k}=Zt(I,u),_=s=>{const f=[],h=[];return s.forEach(x=>{k.value.has(x)?h.push(x):f.push(x)}),{missingRawValues:f,existRawValues:h}},A=tl(I,g,{fieldNames:u,treeNodeFilterProp:ne(e,"treeNodeFilterProp"),filterTreeNode:ne(e,"filterTreeNode")}),O=s=>{if(s){if(e.treeNodeLabelProp)return s[e.treeNodeLabelProp];const{_title:f}=u.value;for(let h=0;h<f.length;h+=1){const x=s[f[h]];if(x!==void 0)return x}}},F=s=>Ht(s).map(h=>ll(h)?{value:h}:h),d=s=>F(s).map(h=>{let{label:x}=h;const{value:P,halfChecked:L}=h;let C;const T=k.value.get(P);return T&&(x=x!=null?x:O(T.node),C=T.node.disabled),{label:x,value:P,halfChecked:L,disabled:C}}),[b,E]=Ue(e.defaultValue,{value:ne(e,"value")}),K=y(()=>F(b.value)),N=j([]),W=j([]);$e(()=>{const s=[],f=[];K.value.forEach(h=>{h.halfChecked?f.push(h):s.push(h)}),N.value=s,W.value=f});const G=y(()=>N.value.map(s=>s.value)),{maxLevel:R,levelEntities:le}=Ct(w),[X,oe]=el(N,W,o,w,R,le),re=y(()=>{const h=qe(X.value,e.showCheckedStrategy,w.value,u.value).map(L=>{var C,T,M;return(M=(T=(C=w.value[L])===null||C===void 0?void 0:C.node)===null||T===void 0?void 0:T[u.value.value])!==null&&M!==void 0?M:L}).map(L=>{const C=N.value.find(T=>T.value===L);return{value:L,label:C==null?void 0:C.label}}),x=d(h),P=x[0];return!r.value&&P&&Xe(P.value)&&Xe(P.label)?[]:x.map(L=>{var C;return p(p({},L),{label:(C=L.label)!==null&&C!==void 0?C:L.value})})}),[se]=Yt(re),ae=(s,f,h)=>{const x=d(s);if(E(x),e.autoClearSearchValue&&V(""),e.onChange){let P=s;o.value&&(P=qe(s,e.showCheckedStrategy,w.value,u.value).map(ee=>{const me=k.value.get(ee);return me?me.node[u.value.value]:ee}));const{triggerValue:L,selected:C}=f||{triggerValue:void 0,selected:void 0};let T=P;if(e.treeCheckStrictly){const B=W.value.filter(ee=>!P.includes(ee.value));T=[...T,...B]}const M=d(T),Y={preValue:N.value,triggerValue:L};let Z=!0;(e.treeCheckStrictly||h==="selection"&&!C)&&(Z=!1),qt(Y,L,s,I.value,Z,u.value),c.value?Y.checked=C:Y.selected=C;const $=v.value?M:M.map(B=>B.value);e.onChange(r.value?$:$[0],v.value?null:M.map(B=>B.label),Y)}},q=(s,f)=>{let{selected:h,source:x}=f;var P,L,C;const T=te(w.value),M=te(k.value),Y=T[s],Z=Y==null?void 0:Y.node,$=(P=Z==null?void 0:Z[u.value.value])!==null&&P!==void 0?P:s;if(!r.value)ae([$],{selected:!0,triggerValue:$},"option");else{let B=h?[...G.value,$]:X.value.filter(ee=>ee!==$);if(o.value){const{missingRawValues:ee,existRawValues:me}=_(B),We=me.map(Ke=>M.get(Ke).key);let Pe;h?{checkedKeys:Pe}=He(We,!0,T,R.value,le.value):{checkedKeys:Pe}=He(We,{halfCheckedKeys:oe.value},T,R.value,le.value),B=[...ee,...Pe.map(Ke=>T[Ke].node[u.value.value])]}ae(B,{selected:h,triggerValue:$},x||"option")}h||!r.value?(L=e.onSelect)===null||L===void 0||L.call(e,$,je(Z)):(C=e.onDeselect)===null||C===void 0||C.call(e,$,je(Z))},ve=s=>{if(e.onDropdownVisibleChange){const f={};Object.defineProperty(f,"documentClickClose",{get(){return!1}}),e.onDropdownVisibleChange(s,f)}},fe=(s,f)=>{const h=s.map(x=>x.value);if(f.type==="clear"){ae(h,{},"selection");return}f.values.length&&q(f.values[0].value,{selected:!1,source:"selection"})},{treeNodeFilterProp:he,loadData:ie,treeLoadedKeys:m,onTreeLoad:D,treeDefaultExpandAll:H,treeExpandedKeys:J,treeDefaultExpandedKeys:Ce,onTreeExpand:Se,virtual:we,listHeight:xe,listItemHeight:Ie,treeLine:Ve,treeIcon:pe,showTreeIcon:ke,switcherIcon:Te,treeMotion:Ne,customSlots:Le,dropdownMatchSelectWidth:Ee,treeExpandAction:De}=At(e);kt(Ge({checkable:c,loadData:ie,treeLoadedKeys:m,onTreeLoad:D,checkedKeys:X,halfCheckedKeys:oe,treeDefaultExpandAll:H,treeExpandedKeys:J,treeDefaultExpandedKeys:Ce,onTreeExpand:Se,treeIcon:pe,treeMotion:Ne,showTreeIcon:ke,switcherIcon:Te,treeLine:Ve,treeNodeFilterProp:he,keyEntities:w,customSlots:Le})),Rt(Ge({virtual:we,listHeight:xe,listItemHeight:Ie,treeData:A,fieldNames:u,onSelect:q,dropdownMatchSelectWidth:Ee,treeExpandAction:De}));const Q=ge();return t({focus(){var s;(s=Q.value)===null||s===void 0||s.focus()},blur(){var s;(s=Q.value)===null||s===void 0||s.blur()},scrollTo(s){var f;(f=Q.value)===null||f===void 0||f.scrollTo(s)}}),()=>{var s;const f=be(e,["id","prefixCls","customSlots","value","defaultValue","onChange","onSelect","onDeselect","searchValue","inputValue","onSearch","autoClearSearchValue","filterTreeNode","treeNodeFilterProp","showCheckedStrategy","treeNodeLabelProp","multiple","treeCheckable","treeCheckStrictly","labelInValue","fieldNames","treeDataSimpleMode","treeData","children","loadData","treeLoadedKeys","onTreeLoad","treeDefaultExpandAll","treeExpandedKeys","treeDefaultExpandedKeys","onTreeExpand","virtual","listHeight","listItemHeight","onDropdownVisibleChange","treeLine","treeIcon","showTreeIcon","switcherIcon","treeMotion"]);return z(Tt,U(U(U({ref:Q},a),f),{},{id:n,prefixCls:e.prefixCls,mode:r.value?"multiple":void 0,displayValues:se.value,onDisplayValuesChange:fe,searchValue:g.value,onSearch:S,OptionList:Wt,emptyOptions:!I.value.length,onDropdownVisibleChange:ve,tagRender:e.tagRender||l.tagRender,dropdownMatchSelectWidth:(s=e.dropdownMatchSelectWidth)!==null&&s!==void 0?s:!0}),l)}}}),nl=e=>{const{componentCls:i,treePrefixCls:a,colorBgElevated:t}=e,l=`.${a}`;return[{[`${i}-dropdown`]:[{padding:`${e.paddingXS}px ${e.paddingXS/2}px`},St(a,Ze(e,{colorBgContainer:t})),{[l]:{borderRadius:0,"&-list-holder-inner":{alignItems:"stretch",[`${l}-treenode`]:{[`${l}-node-content-wrapper`]:{flex:"auto"}}}}},Ft(`${a}-checkbox`,e),{"&-rtl":{direction:"rtl",[`${l}-switcher${l}-switcher_close`]:{[`${l}-switcher-icon svg`]:{transform:"rotate(90deg)"}}}}]}]};function ol(e,i){return ot("TreeSelect",a=>{const t=Ze(a,{treePrefixCls:i.value});return[nl(t)]})(e)}const Je=(e,i,a)=>a!==void 0?a:`${e}-${i}`;function rl(){return p(p({},be(lt(),["showTreeIcon","treeMotion","inputIcon","getInputElement","treeLine","customSlots"])),{suffixIcon:ue.any,size:Oe(),bordered:it(),treeLine:st([Boolean,Object]),replaceFields:rt(),placement:Oe(),status:Oe(),popupClassName:String,dropdownClassName:String,"onUpdate:value":Ae(),"onUpdate:treeExpandedKeys":Ae(),"onUpdate:searchValue":Ae()})}const Fe=Re({compatConfig:{MODE:3},name:"ATreeSelect",inheritAttrs:!1,props:Ye(rl(),{choiceTransitionName:"",listHeight:256,treeIcon:!1,listItemHeight:26,bordered:!0}),slots:Object,setup(e,i){let{attrs:a,slots:t,expose:l,emit:n}=i;ct(!(e.treeData===void 0&&t.default)),_e(e.multiple!==!1||!e.treeCheckable,"TreeSelect","`multiple` will always be `true` when `treeCheckable` is true"),_e(e.replaceFields===void 0,"TreeSelect","`replaceFields` is deprecated, please use fieldNames instead"),_e(!e.dropdownClassName,"TreeSelect","`dropdownClassName` is deprecated. Please use `popupClassName` instead.");const o=ut(),c=dt.useInject(),v=y(()=>Ot(c.status,e.status)),{prefixCls:r,renderEmpty:u,direction:g,virtual:V,dropdownMatchSelectWidth:S,size:I,getPopupContainer:w,getPrefixCls:k,disabled:_}=vt("select",e),{compactSize:A,compactItemClassnames:O}=ft(r,g),F=y(()=>A.value||I.value),d=ht(),b=y(()=>{var m;return(m=_.value)!==null&&m!==void 0?m:d.value}),E=y(()=>k()),K=y(()=>e.placement!==void 0?e.placement:g.value==="rtl"?"bottomRight":"bottomLeft"),N=y(()=>Je(E.value,pt(K.value),e.transitionName)),W=y(()=>Je(E.value,"",e.choiceTransitionName)),G=y(()=>k("select-tree",e.prefixCls)),R=y(()=>k("tree-select",e.prefixCls)),[le,X]=Lt(r),[oe]=ol(R,G),re=y(()=>ze(e.popupClassName||e.dropdownClassName,`${R.value}-dropdown`,{[`${R.value}-dropdown-rtl`]:g.value==="rtl"},X.value)),se=y(()=>!!(e.treeCheckable||e.multiple)),ae=y(()=>e.showArrow!==void 0?e.showArrow:e.loading||!se.value),q=ge();l({focus(){var m,D;(D=(m=q.value).focus)===null||D===void 0||D.call(m)},blur(){var m,D;(D=(m=q.value).blur)===null||D===void 0||D.call(m)}});const ve=function(){for(var m=arguments.length,D=new Array(m),H=0;H<m;H++)D[H]=arguments[H];n("update:value",D[0]),n("change",...D),o.onFieldChange()},fe=m=>{n("update:treeExpandedKeys",m),n("treeExpand",m)},he=m=>{n("update:searchValue",m),n("search",m)},ie=m=>{n("blur",m),o.onFieldBlur()};return()=>{var m,D,H;const{notFoundContent:J=(m=t.notFoundContent)===null||m===void 0?void 0:m.call(t),prefixCls:Ce,bordered:Se,listHeight:we,listItemHeight:xe,multiple:Ie,treeIcon:Ve,treeLine:pe,showArrow:ke,switcherIcon:Te=(D=t.switcherIcon)===null||D===void 0?void 0:D.call(t),fieldNames:Ne=e.replaceFields,id:Le=o.id.value,placeholder:Ee=(H=t.placeholder)===null||H===void 0?void 0:H.call(t)}=e,{isFormItemInput:De,hasFeedback:Q,feedbackIcon:s}=c,{suffixIcon:f,removeIcon:h,clearIcon:x}=Et(p(p({},e),{multiple:se.value,showArrow:ae.value,hasFeedback:Q,feedbackIcon:s,prefixCls:r.value}),t);let P;J!==void 0?P=J:P=u("Select");const L=be(e,["suffixIcon","itemIcon","removeIcon","clearIcon","switcherIcon","bordered","status","onUpdate:value","onUpdate:treeExpandedKeys","onUpdate:searchValue"]),C=ze(!Ce&&R.value,{[`${r.value}-lg`]:F.value==="large",[`${r.value}-sm`]:F.value==="small",[`${r.value}-rtl`]:g.value==="rtl",[`${r.value}-borderless`]:!Se,[`${r.value}-in-form-item`]:De},_t(r.value,v.value,Q),O.value,a.class,X.value),T={};return e.treeData===void 0&&t.default&&(T.children=mt(t.default())),le(oe(z(al,U(U(U(U({},a),L),{},{disabled:b.value,virtual:V.value,dropdownMatchSelectWidth:S.value,id:Le,fieldNames:Ne,ref:q,prefixCls:r.value,class:C,listHeight:we,listItemHeight:xe,treeLine:!!pe,inputIcon:f,multiple:Ie,removeIcon:h,clearIcon:x,switcherIcon:M=>wt(G.value,Te,M,t.leafIcon,pe),showTreeIcon:Ve,notFoundContent:P,getPopupContainer:w==null?void 0:w.value,treeMotion:null,dropdownClassName:re.value,choiceTransitionName:W.value,onChange:ve,onBlur:ie,onSearch:he,onTreeExpand:fe},T),{},{transitionName:N.value,customSlots:p(p({},t),{treeCheckable:()=>z("span",{class:`${r.value}-tree-checkbox-inner`},null)}),maxTagPlaceholder:e.maxTagPlaceholder||t.maxTagPlaceholder,placement:K.value,showArrow:Q||ke,placeholder:Ee}),p(p({},t),{treeCheckable:()=>z("span",{class:`${r.value}-tree-checkbox-inner`},null)}))))}}}),Qe=de,Vl=p(Fe,{TreeNode:de,SHOW_ALL:Ut,SHOW_PARENT:tt,SHOW_CHILD:Be,install:e=>(e.component(Fe.name,Fe),e.component(Qe.displayName,Qe),e)});export{Qe as TreeSelectNode,Vl as default,rl as treeSelectProps};
