var c=(e,u,a)=>new Promise((n,m)=>{var f=o=>{try{s(a.next(o))}catch(d){m(d)}},l=o=>{try{s(a.throw(o))}catch(d){m(d)}},s=o=>o.done?n(o.value):Promise.resolve(o.value).then(f,l);s((a=a.apply(e,u)).next())});import{y as r,$ as h,aj as w}from"./bootstrap-DCMzVRvD.js";import{c as v}from"./helper-Bc7QQ92Q.js";import{d as y,p as N,B as C,P as I,h as _,o as x,w as k,a as q,b as g}from"../jse/index-index-C-MnMZEz.js";import{u as B}from"./use-modal-CeMSCP2m.js";function E(e){return r.get("/demo/demo/list",{params:e})}function P(e){return v("/demo/demo/export",e!=null?e:{})}function L(e){return r.get(`/demo/demo/${e}`)}function M(e){return r.postWithMsg("/demo/demo",e)}function V(e){return r.putWithMsg("/demo/demo",e)}function S(e){return r.deleteWithMsg(`/demo/demo/${e}`)}const U=()=>[{component:"Input",fieldName:"orderNum",label:"排序号"},{component:"Input",fieldName:"testKey",label:"key键"},{component:"Input",fieldName:"value",label:"值"},{component:"Input",fieldName:"version",label:"版本"}],j=[{type:"checkbox",width:60},{title:"主键",field:"id"},{title:"排序号",field:"orderNum"},{title:"key键",field:"testKey"},{title:"值",field:"value"},{title:"版本",field:"version"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],A=()=>[{label:"主键",fieldName:"id",component:"Input",dependencies:{show:()=>!1,triggerFields:[""]}},{label:"排序号",fieldName:"orderNum",component:"Input",rules:"required"},{label:"key键",fieldName:"testKey",component:"Input",rules:"required"},{label:"值",fieldName:"value",component:"Input",rules:"required"},{label:"版本",fieldName:"version",component:"Input",rules:"required"}],z=y({__name:"demo-modal",emits:["reload"],setup(e,{emit:u}){const a=u,n=N(!1),m=C(()=>n.value?h("pages.common.edit"):h("pages.common.add")),[f,l]=w({commonConfig:{formItemClass:"col-span-2",labelWidth:80,componentProps:{class:"w-full"}},schema:A(),showDefaultActions:!1,wrapperClass:"grid-cols-2"}),[s,o]=B({fullscreenButton:!1,onCancel:p,onConfirm:d,onOpenChange:i=>c(null,null,function*(){if(!i)return null;o.modalLoading(!0);const{id:t}=o.getData();if(n.value=!!t,n.value&&t){const b=yield L(t);yield l.setValues(b)}o.modalLoading(!1)})});function d(){return c(this,null,function*(){try{o.modalLoading(!0);const{valid:i}=yield l.validate();if(!i)return;const t=I(yield l.getValues());yield n.value?V(t):M(t),a("reload"),yield p()}catch(i){console.error(i)}finally{o.modalLoading(!1)}})}function p(){return c(this,null,function*(){o.close(),yield l.resetForm()})}return(i,t)=>(x(),_(g(s),{"close-on-click-modal":!1,title:m.value,class:"w-[550px]"},{default:k(()=>[q(g(f))]),_:1},8,["title"]))}});export{z as _,E as a,S as b,j as c,P as d,U as q};
