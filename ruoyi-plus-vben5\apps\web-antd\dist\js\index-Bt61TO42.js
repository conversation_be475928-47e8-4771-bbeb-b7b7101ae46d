import{c as t}from"./helper-Bc7QQ92Q.js";import{y as o}from"./bootstrap-DCMzVRvD.js";function n(e){return o.get("/workflow/leave/list",{params:e})}function f(e){return t("/workflow/leave/export",e!=null?e:{})}function a(e){return o.get(`/workflow/leave/${e}`)}function i(e){return o.postWithMsg("/workflow/leave",e)}function u(e){return o.putWithMsg("/workflow/leave",e)}function v(e){return o.deleteWithMsg(`/workflow/leave/${e}`)}export{v as a,f as b,a as c,u as d,i as e,n as l};
