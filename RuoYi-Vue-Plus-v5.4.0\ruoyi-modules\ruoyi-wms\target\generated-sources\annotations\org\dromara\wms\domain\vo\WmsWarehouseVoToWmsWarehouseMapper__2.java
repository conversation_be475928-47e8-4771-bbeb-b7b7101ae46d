package org.dromara.wms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1039;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.WmsWarehouseToWmsWarehouseVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1039.class,
    uses = {WmsWarehouseToWmsWarehouseVoMapper__2.class},
    imports = {}
)
public interface WmsWarehouseVoToWmsWarehouseMapper__2 extends BaseMapper<WmsWarehouseVo, WmsWarehouse> {
}
