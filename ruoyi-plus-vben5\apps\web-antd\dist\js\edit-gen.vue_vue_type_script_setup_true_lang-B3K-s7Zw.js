var j=Object.defineProperty,z=Object.defineProperties;var K=Object.getOwnPropertyDescriptors;var w=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var S=(a,e,t)=>e in a?j(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,V=(a,e)=>{for(var t in e||(e={}))L.call(e,t)&&S(a,t,e[t]);if(w)for(var t of w(e))O.call(e,t)&&S(a,t,e[t]);return a},x=(a,e)=>z(a,K(e));var C=(a,e,t)=>new Promise((I,_)=>{var m=o=>{try{f(t.next(o))}catch(g){_(g)}},c=o=>{try{f(t.throw(o))}catch(g){_(g)}},f=o=>o.done?I(o.value):Promise.resolve(o.value).then(m,c);f((t=t.apply(a,e)).next())});import{av as Q,aB as U}from"./bootstrap-DCMzVRvD.js";import{a as A,e as G}from"./index-BntC6MFc.js";import{_ as H}from"./basic-setting.vue_vue_type_script_setup_true_lang-BcMqupsn.js";import{_ as J}from"./gen-config.vue_vue_type_script_setup_true_lang-DnyGmMf_.js";import{_ as W}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{C as X}from"./index-C1KbofmV.js";import{a as Y,T as N}from"./index-BaVK9zYh.js";import{S as Z}from"./index-BLwHKR_M.js";import{d as ee,p as M,v as te,E as q,l as ae,h as k,o as T,b as s,w as p,a as d,k as ne,P as oe,a4 as re}from"../jse/index-index-C-MnMZEz.js";import{u as se}from"./use-tabs-Zz_nc_n2.js";function ie(a){const e=Number(a);return Number.isSafeInteger(e)?e:a}const Ce=ee({__name:"edit-gen",setup(a){const{setTabTitle:e,closeCurrentTab:t}=se(),_=Q().params.tableId,m=M();re("genInfoData",m),te(()=>C(null,null,function*(){const i=yield A(_);i.info.parentMenuId=ie(i.info.parentMenuId),m.value=i.info,e(`生成配置: ${i.info.tableName}`)}));const c=M("setting"),f=q("basicSettingRef"),o=q("genConfigRef"),g=U();function D(){return C(this,null,function*(){var i,u,b,v,R;try{if(!(yield(i=f.value)==null?void 0:i.validateForm())){c.value="setting";return}if(!(yield(u=o.value)==null?void 0:u.validateTable())){c.value="fields";return}const n=oe(s(m)),E=yield(b=f.value)==null?void 0:b.getFormValues();if(Object.assign(n,E),n.columns=(R=(v=o.value)==null?void 0:v.getTableRecords())!=null?R:[],n&&n.tplCategory==="tree"){const{treeCode:l,treeName:r,treeParentCode:y}=n;n.params={treeCode:l,treeName:r,treeParentCode:y}}if(n){const l=r=>r?"1":"0";n.columns.forEach(r=>{const{edit:y,insert:P,query:$,required:B,list:F}=r;r.isInsert=l(P),r.isEdit=l(y),r.isList=l(F),r.isQuery=l($),r.isRequired=l(B)}),n.params=x(V({},n.params),{parentMenuId:n.parentMenuId,popupComponent:n.popupComponent,formComponent:n.formComponent})}yield G(n),yield t(),g.push({path:"/tool/gen",replace:!0})}catch(h){console.error(h)}})}return(i,u)=>{const b=ae("a-button");return T(),k(s(W),{"auto-content-height":!0},{default:p(()=>[m.value?(T(),k(s(X),{key:0,class:"h-full","body-style":{padding:"0 16px 16px"}},{default:p(()=>[d(s(Y),{"active-key":c.value,"onUpdate:activeKey":u[0]||(u[0]=v=>c.value=v),size:"middle"},{rightExtra:p(()=>[d(b,{type:"primary",onClick:D},{default:p(()=>u[1]||(u[1]=[ne("保存配置")])),_:1,__:[1]})]),default:p(()=>[d(s(N),{key:"setting",tab:"生成信息","force-render":!0},{default:p(()=>[d(s(H),{ref_key:"basicSettingRef",ref:f},null,512)]),_:1}),d(s(N),{key:"fields",tab:"字段信息","force-render":!0},{default:p(()=>[d(s(J),{ref_key:"genConfigRef",ref:o},null,512)]),_:1})]),_:1},8,["active-key"])]),_:1})):(T(),k(s(Z),{key:1,active:!0}))]),_:1})}}});export{Ce as _};
