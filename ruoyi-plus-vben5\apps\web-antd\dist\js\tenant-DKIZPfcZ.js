var f=(t,e,a)=>new Promise((o,u)=>{var i=s=>{try{c(a.next(s))}catch(m){u(m)}},r=s=>{try{c(a.throw(s))}catch(m){u(m)}},c=s=>s.done?o(s.value):Promise.resolve(s.value).then(i,r);c((a=a.apply(t,e)).next())});import{c as g}from"./helper-Bc7QQ92Q.js";import{y as n,bo as p,bp as d}from"./bootstrap-DCMzVRvD.js";import{p as y}from"../jse/index-index-C-MnMZEz.js";function $(t){return n.get("/system/tenant/list",{params:t})}function S(t){return g("/system/tenant/export",t)}function T(t){return n.get(`/system/tenant/${t}`)}function b(t){return n.postWithMsg("/system/tenant",t,{encrypt:!0})}function k(t){return n.putWithMsg("/system/tenant",t)}function x(t){const e={id:t.id,tenantId:t.tenantId,status:t.status};return n.putWithMsg("/system/tenant/changeStatus",e)}function C(t){return n.deleteWithMsg(`/system/tenant/${t}`)}function D(t){return n.get(`/system/tenant/dynamic/${t}`)}function E(){return n.get("/system/tenant/dynamic/clear")}function L(t,e){return n.get("/system/tenant/syncTenantPackage",{params:{packageId:e,tenantId:t},successMessageMode:"message"})}function W(t){return n.get("/system/tenant/syncTenantDict",{params:{tenantId:t},successMessageMode:"message"})}const I=p("app-tenant",()=>{const t=y(!1),e=y(!0),a=y([]);function o(){return f(this,null,function*(){const{tenantEnabled:r,voList:c}=yield d();e.value=r,a.value=c})}function u(r){return f(this,null,function*(){t.value=r})}function i(){t.value=!1,e.value=!0,a.value=[]}return{$reset:i,checked:t,initTenant:o,setChecked:u,tenantEnable:e,tenantList:a}});export{$ as a,L as b,C as c,S as d,W as e,T as f,k as g,b as h,D as i,E as j,x as t,I as u};
