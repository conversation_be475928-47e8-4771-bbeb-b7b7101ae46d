2025-06-16 00:12:28 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 00:12:28 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 00:12:28 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 00:12:28 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 00:12:37 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 08:21:52 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 08:21:52 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 08:21:52 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 08:21:52 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 08:21:52 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 29764 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:21:52 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 08:21:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 08:21:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 08:21:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 08:21:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 08:21:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 08:21:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1860 ms
2025-06-16 08:21:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 08:21:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 08:21:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 08:21:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 08:21:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d12f065, org.springframework.security.web.context.SecurityContextPersistenceFilter@77d58f3a, org.springframework.security.web.header.HeaderWriterFilter@32ddcca, org.springframework.security.web.csrf.CsrfFilter@2634d000, org.springframework.security.web.authentication.logout.LogoutFilter@4d66cb, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@57fc6759, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@738ed8f5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5f4f6f89, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33a7331, org.springframework.security.web.access.ExceptionTranslationFilter@76b019c4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6636448b]
2025-06-16 08:21:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 08:21:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 08:21:56 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 4.641 seconds (JVM running for 5.92)
2025-06-16 08:21:56 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 08:21:56 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 08:21:57 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 08:21:57 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 08:22:06 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 08:22:07 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 08:22:07 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10966 millSeconds
2025-06-16 09:16:40 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 09:16:40 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 09:16:50 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:31:10 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 09:31:10 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 09:31:10 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 09:31:10 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 09:31:10 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 43088 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:31:10 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:31:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 09:31:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 09:31:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:31:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:31:11 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:31:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1000 ms
2025-06-16 09:31:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:31:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:31:12 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 09:31:12 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 09:31:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@411933, org.springframework.security.web.context.SecurityContextPersistenceFilter@25d23478, org.springframework.security.web.header.HeaderWriterFilter@51e1e058, org.springframework.security.web.csrf.CsrfFilter@18d1d137, org.springframework.security.web.authentication.logout.LogoutFilter@7fdff56b, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@393e7546, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@39c1e7b7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@221cdd87, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6b43b101, org.springframework.security.web.access.ExceptionTranslationFilter@34065642, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@acb1c9c]
2025-06-16 09:31:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 09:31:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 09:31:12 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 2.663 seconds (JVM running for 3.388)
2025-06-16 09:31:13 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 09:31:13 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 09:31:13 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 09:31:13 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:31:22 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:31:22 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 09:31:22 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10253 millSeconds
2025-06-16 09:36:22 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:36:22 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 09:36:22 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:36:22 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 09:36:32 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:41:26 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 09:41:26 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 09:41:26 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 09:41:26 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 09:41:26 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 31980 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:41:26 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:41:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 09:41:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 09:41:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:41:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:41:28 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:41:28 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1360 ms
2025-06-16 09:41:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:41:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:41:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 09:41:29 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 09:41:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ab39942, org.springframework.security.web.context.SecurityContextPersistenceFilter@1764e45e, org.springframework.security.web.header.HeaderWriterFilter@6033f36c, org.springframework.security.web.csrf.CsrfFilter@70eb94d7, org.springframework.security.web.authentication.logout.LogoutFilter@3cab07dd, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@64186f60, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4745bcc6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@50008974, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@de40aa1, org.springframework.security.web.access.ExceptionTranslationFilter@1e2b37cd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@49f41c2e]
2025-06-16 09:41:29 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 09:41:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 09:41:29 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 3.416 seconds (JVM running for 4.449)
2025-06-16 09:41:29 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 09:41:29 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 09:41:30 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 09:41:30 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:41:39 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:41:39 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 09:41:39 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10366 millSeconds
2025-06-16 09:46:38 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:46:38 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:46:38 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 09:46:38 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 09:46:49 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:48:52 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 09:48:52 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 09:48:52 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 09:48:52 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 09:48:53 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 38204 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:48:53 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:48:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 09:48:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 09:48:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:48:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:48:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:48:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1357 ms
2025-06-16 09:48:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:48:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:48:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 09:48:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 09:48:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5da1a97f, org.springframework.security.web.context.SecurityContextPersistenceFilter@c478e4e, org.springframework.security.web.header.HeaderWriterFilter@f575d2b, org.springframework.security.web.csrf.CsrfFilter@6d0bcf8c, org.springframework.security.web.authentication.logout.LogoutFilter@18d1d137, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@4b5f022f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ba08f26, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4745bcc6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7a569672, org.springframework.security.web.access.ExceptionTranslationFilter@599310ab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3450bd13]
2025-06-16 09:48:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 09:48:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 09:48:55 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 3.507 seconds (JVM running for 4.519)
2025-06-16 09:48:55 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 09:48:55 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 09:48:56 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 09:48:56 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:49:05 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:49:05 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 09:49:05 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10484 millSeconds
2025-06-16 09:49:21 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:49:21 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:49:21 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 09:49:21 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 09:49:23 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 09:56:38 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 09:56:38 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 09:56:38 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 09:56:38 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 09:56:38 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 14496 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:56:38 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:56:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 09:56:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 09:56:39 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:56:39 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:56:39 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:56:39 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 959 ms
2025-06-16 09:56:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:56:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:56:39 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 09:56:39 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 09:56:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 09:56:40 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@75fa9254, org.springframework.security.web.context.SecurityContextPersistenceFilter@6f36e806, org.springframework.security.web.header.HeaderWriterFilter@2245ccaa, org.springframework.security.web.csrf.CsrfFilter@49f41c2e, org.springframework.security.web.authentication.logout.LogoutFilter@37cc6017, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@6f25ed2b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4c24c40a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@33a7331, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f69e2d8, org.springframework.security.web.access.ExceptionTranslationFilter@6cb43fd3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@747e8659]
2025-06-16 09:56:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 09:56:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 09:56:40 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 2.454 seconds (JVM running for 3.2)
2025-06-16 09:56:40 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 09:56:40 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 09:56:40 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 09:56:40 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:56:50 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 09:56:50 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 09:56:50 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10093 millSeconds
2025-06-16 10:04:43 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 10:04:43 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 10:04:43 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 10:04:43 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 10:04:55 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 10:10:53 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 10:10:53 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 10:10:53 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 10:10:53 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 10:10:54 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 42284 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:10:54 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 10:10:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 10:10:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 10:10:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 10:10:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 10:10:56 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 10:10:56 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1768 ms
2025-06-16 10:10:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 10:10:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 10:10:56 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 10:10:57 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 10:10:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6f36e806, org.springframework.security.web.context.SecurityContextPersistenceFilter@38874eb5, org.springframework.security.web.header.HeaderWriterFilter@55202ba6, org.springframework.security.web.csrf.CsrfFilter@6033f36c, org.springframework.security.web.authentication.logout.LogoutFilter@1c14d24d, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@4088702d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@221961af, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@738ed8f5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4c24c40a, org.springframework.security.web.access.ExceptionTranslationFilter@7d5d77a6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2245ccaa]
2025-06-16 10:10:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 10:10:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 10:10:57 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 4.15 seconds (JVM running for 5.6)
2025-06-16 10:10:57 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 10:10:57 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 10:10:58 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 10:10:58 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 10:11:07 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 10:11:07 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 10:11:07 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10561 millSeconds
2025-06-16 10:13:25 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 10:13:25 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 10:13:25 [Thread-17] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 10:13:25 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 10:13:26 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 10:13:36 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 10:29:53 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 10:29:53 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 10:29:53 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 10:29:53 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 10:29:54 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 18996 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:29:54 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 10:29:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 10:29:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 10:29:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 10:29:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 10:29:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 10:29:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1448 ms
2025-06-16 10:29:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 10:29:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 10:29:56 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 10:29:56 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 10:29:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5d202dc4, org.springframework.security.web.context.SecurityContextPersistenceFilter@50b4e7b2, org.springframework.security.web.header.HeaderWriterFilter@1642968c, org.springframework.security.web.csrf.CsrfFilter@63243c8f, org.springframework.security.web.authentication.logout.LogoutFilter@234bfc8c, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@71e409f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5274830e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@520ec7a7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@14c18a6a, org.springframework.security.web.access.ExceptionTranslationFilter@4745bcc6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@57f89680]
2025-06-16 10:29:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 10:29:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 10:29:56 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 3.688 seconds (JVM running for 4.862)
2025-06-16 10:29:57 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 10:29:57 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 10:29:57 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 10:29:57 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 10:30:07 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 10:30:07 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 10:30:07 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10565 millSeconds
2025-06-16 17:38:58 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 17:38:58 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 17:38:58 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-16 17:38:58 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-16 17:38:59 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 17:39:08 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 17:56:44 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 17:56:44 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 17:56:44 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 17:56:44 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 17:56:44 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 28888 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 17:56:44 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 17:56:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 17:56:45 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 17:56:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 17:56:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 17:56:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 17:56:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1235 ms
2025-06-16 17:56:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 17:56:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 17:56:46 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 17:56:46 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 17:56:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3e18e693, org.springframework.security.web.context.SecurityContextPersistenceFilter@1ddc8fc, org.springframework.security.web.header.HeaderWriterFilter@221961af, org.springframework.security.web.csrf.CsrfFilter@4745bcc6, org.springframework.security.web.authentication.logout.LogoutFilter@221cdd87, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@3d12f065, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1e288c76, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@18d1d137, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@57fc6759, org.springframework.security.web.access.ExceptionTranslationFilter@38874eb5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4c24c40a]
2025-06-16 17:56:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 17:56:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 17:56:46 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 3.109 seconds (JVM running for 4.153)
2025-06-16 17:56:46 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 17:56:46 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 17:56:47 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 17:56:47 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 17:56:47 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 17:56:47 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 17:56:47 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 840 millSeconds
