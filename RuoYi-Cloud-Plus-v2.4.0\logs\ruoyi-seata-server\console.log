2025-06-17 08:13:24 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-17 08:13:24 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-17 08:13:24 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-17 08:13:24 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-17 08:13:25 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 15548 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:13:25 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-17 08:13:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-17 08:13:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-17 08:13:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 08:13:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-17 08:13:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 08:13:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1580 ms
2025-06-17 08:13:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-17 08:13:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-17 08:13:27 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/version.json']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/health']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/error']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-17 08:13:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/vgroup/v1/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-17 08:13:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3fcc7f5c, org.springframework.security.web.context.SecurityContextPersistenceFilter@28515443, org.springframework.security.web.header.HeaderWriterFilter@39c1e7b7, org.springframework.security.web.csrf.CsrfFilter@18bc1064, org.springframework.security.web.authentication.logout.LogoutFilter@22b10124, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@7512a9a6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1820f274, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2761156e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@32fd5bc, org.springframework.security.web.access.ExceptionTranslationFilter@25d23478, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6b43b101]
2025-06-17 08:13:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-17 08:13:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-17 08:13:28 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 3.974 seconds (JVM running for 5.08)
2025-06-17 08:13:28 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-17 08:13:28 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-17 08:13:28 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-17 08:13:28 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-17 08:13:38 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-17 08:13:38 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-17 08:13:38 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10470 millSeconds
2025-06-17 15:12:33 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-17 15:12:33 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-17 15:12:33 [Thread-18] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-17 15:12:33 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-17 15:12:43 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
