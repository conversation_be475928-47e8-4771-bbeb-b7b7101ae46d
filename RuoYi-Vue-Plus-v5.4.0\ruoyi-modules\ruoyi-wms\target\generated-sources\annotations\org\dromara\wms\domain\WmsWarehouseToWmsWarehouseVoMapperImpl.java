package org.dromara.wms.domain;

import javax.annotation.processing.Generated;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-15T14:52:54+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WmsWarehouseToWmsWarehouseVoMapperImpl implements WmsWarehouseToWmsWarehouseVoMapper {

    @Override
    public WmsWarehouseVo convert(WmsWarehouse arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WmsWarehouseVo wmsWarehouseVo = new WmsWarehouseVo();

        wmsWarehouseVo.setRemark( arg0.getRemark() );
        wmsWarehouseVo.setWarehouseId( arg0.getWarehouseId() );
        wmsWarehouseVo.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wmsWarehouseVo.setWarehouseName( arg0.getWarehouseName() );
        wmsWarehouseVo.setWarehouseNumber( arg0.getWarehouseNumber() );
        wmsWarehouseVo.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wmsWarehouseVo.setWarehouseType( arg0.getWarehouseType() );

        return wmsWarehouseVo;
    }

    @Override
    public WmsWarehouseVo convert(WmsWarehouse arg0, WmsWarehouseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setRemark( arg0.getRemark() );
        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setWarehouseType( arg0.getWarehouseType() );

        return arg1;
    }
}
