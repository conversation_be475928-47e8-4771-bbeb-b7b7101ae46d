2025-06-17 08:13:32 [main] INFO  c.a.c.s.d.DashboardApplication - Starting DashboardApplication using Java 17.0.14 on 奚翔 with PID 29228 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-sentinel-dashboard\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:13:32 [main] INFO  c.a.c.s.d.DashboardApplication - The following 1 profile is active: "dev"
2025-06-17 08:13:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP] success
2025-06-17 08:13:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-17 08:13:33 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8718"]
2025-06-17 08:13:33 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 08:13:33 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-17 08:13:33 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 08:13:33 [main] INFO  c.a.c.s.dashboard.config.WebConfig - Sentinel servlet CommonFilter registered
2025-06-17 08:13:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8718"]
2025-06-17 08:13:37 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-sentinel-dashboard *************:8718 register finished
2025-06-17 08:13:39 [main] INFO  c.a.c.s.d.DashboardApplication - Started DashboardApplication in 10.813 seconds (JVM running for 11.492)
2025-06-17 08:13:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-17 08:13:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP
2025-06-17 08:13:39 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 08:13:39 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 08:13:39 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-17 15:12:33 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-06-17 15:12:33 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [c1218c3a-9825-4fc0-b8f2-c72d36930b68_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-06-17 15:12:33 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:33 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [c1218c3a-9825-4fc0-b8f2-c72d36930b68_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:33 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:33 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [c1218c3a-9825-4fc0-b8f2-c72d36930b68_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:34 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:34 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [c1218c3a-9825-4fc0-b8f2-c72d36930b68_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:34 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:34 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [c1218c3a-9825-4fc0-b8f2-c72d36930b68_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:12:34 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-17 15:12:34 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:34 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [c1218c3a-9825-4fc0-b8f2-c72d36930b68_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@8240194[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1750119217648_127.0.0.1_62724
2025-06-17 15:12:35 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [7f371248-90fc-42e5-b344-1877d794a121] Client is shutdown, stop reconnect to server
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3fd99398[Running, pool size = 14, active threads = 0, queued tasks = 0, completed tasks = 5128]
