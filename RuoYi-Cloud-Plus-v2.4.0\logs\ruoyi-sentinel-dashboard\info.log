2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:12:29 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 08:22:19 [main] INFO  c.a.c.s.d.DashboardApplication - Starting DashboardApplication using Java 17.0.14 on 奚翔 with PID 20332 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-sentinel-dashboard\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:22:19 [main] INFO  c.a.c.s.d.DashboardApplication - The following 1 profile is active: "dev"
2025-06-16 08:22:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP] success
2025-06-16 08:22:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:22:20 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8718"]
2025-06-16 08:22:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 08:22:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 08:22:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 08:22:20 [main] INFO  c.a.c.s.dashboard.config.WebConfig - Sentinel servlet CommonFilter registered
2025-06-16 08:22:24 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8718"]
2025-06-16 08:22:24 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-sentinel-dashboard ************:8718 register finished
2025-06-16 08:22:25 [main] INFO  c.a.c.s.d.DashboardApplication - Started DashboardApplication in 10.877 seconds (JVM running for 11.686)
2025-06-16 08:22:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:22:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP
2025-06-16 08:22:26 [RMI TCP Connection(15)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 08:22:26 [RMI TCP Connection(15)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 08:22:26 [RMI TCP Connection(15)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [663b5cc5-9a14-4ae1-8c45-5194cd7b8c7a_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [663b5cc5-9a14-4ae1-8c45-5194cd7b8c7a_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [663b5cc5-9a14-4ae1-8c45-5194cd7b8c7a_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:40 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [663b5cc5-9a14-4ae1-8c45-5194cd7b8c7a_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:41 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [663b5cc5-9a14-4ae1-8c45-5194cd7b8c7a_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:41 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:16:41 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:41 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [663b5cc5-9a14-4ae1-8c45-5194cd7b8c7a_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-06-16 09:16:42 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-06-16 09:16:42 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@52df85af[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-06-16 09:16:42 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1750033344275_127.0.0.1_57932
2025-06-16 09:16:42 [com.alibaba.nacos.client.remote.worker] INFO  c.alibaba.nacos.common.remote.client - [66db5105-c5a6-48a9-982f-00d799951fcf] Client is shutdown, stop reconnect to server
2025-06-16 09:16:42 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3a6aabb0[Running, pool size = 15, active threads = 0, queued tasks = 0, completed tasks = 683]
2025-06-16 09:31:15 [main] INFO  c.a.c.s.d.DashboardApplication - Starting DashboardApplication using Java 17.0.14 on 奚翔 with PID 40964 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-sentinel-dashboard\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:31:15 [main] INFO  c.a.c.s.d.DashboardApplication - The following 1 profile is active: "dev"
2025-06-16 09:31:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP] success
2025-06-16 09:31:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:31:16 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8718"]
2025-06-16 09:31:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:31:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:31:16 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:31:16 [main] INFO  c.a.c.s.dashboard.config.WebConfig - Sentinel servlet CommonFilter registered
2025-06-16 09:31:20 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8718"]
2025-06-16 09:31:20 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-sentinel-dashboard ************:8718 register finished
2025-06-16 09:31:21 [main] INFO  c.a.c.s.d.DashboardApplication - Started DashboardApplication in 8.679 seconds (JVM running for 9.186)
2025-06-16 09:31:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:31:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP
2025-06-16 09:31:21 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:31:21 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:31:21 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:41:34 [main] INFO  c.a.c.s.d.DashboardApplication - Starting DashboardApplication using Java 17.0.14 on 奚翔 with PID 39724 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-sentinel-dashboard\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:41:34 [main] INFO  c.a.c.s.d.DashboardApplication - The following 1 profile is active: "dev"
2025-06-16 09:41:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP] success
2025-06-16 09:41:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:41:35 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8718"]
2025-06-16 09:41:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:41:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:41:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:41:35 [main] INFO  c.a.c.s.dashboard.config.WebConfig - Sentinel servlet CommonFilter registered
2025-06-16 09:41:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8718"]
2025-06-16 09:41:39 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-sentinel-dashboard ************:8718 register finished
2025-06-16 09:41:41 [main] INFO  c.a.c.s.d.DashboardApplication - Started DashboardApplication in 10.444 seconds (JVM running for 11.136)
2025-06-16 09:41:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:41:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP
2025-06-16 09:41:41 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:41:41 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:41:41 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:46:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:49:04 [main] INFO  c.a.c.s.d.DashboardApplication - Starting DashboardApplication using Java 17.0.14 on 奚翔 with PID 31272 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-sentinel-dashboard\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:49:04 [main] INFO  c.a.c.s.d.DashboardApplication - The following 1 profile is active: "dev"
2025-06-16 09:49:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP] success
2025-06-16 09:49:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:49:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8718"]
2025-06-16 09:49:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:49:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:49:06 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:49:06 [main] INFO  c.a.c.s.dashboard.config.WebConfig - Sentinel servlet CommonFilter registered
2025-06-16 09:49:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8718"]
2025-06-16 09:49:11 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-sentinel-dashboard ************:8718 register finished
2025-06-16 09:49:12 [main] INFO  c.a.c.s.d.DashboardApplication - Started DashboardApplication in 12.296 seconds (JVM running for 12.949)
2025-06-16 09:49:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:49:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP
2025-06-16 09:49:13 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:49:13 [RMI TCP Connection(7)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:49:13 [RMI TCP Connection(7)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-16 09:49:23 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:49:23 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:56:58 [main] INFO  c.a.c.s.d.DashboardApplication - Starting DashboardApplication using Java 17.0.14 on 奚翔 with PID 19908 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-sentinel-dashboard\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:56:58 [main] INFO  c.a.c.s.d.DashboardApplication - The following 1 profile is active: "dev"
2025-06-16 09:56:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP] success
2025-06-16 09:56:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:56:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8718"]
2025-06-16 09:56:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:56:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 09:57:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:57:00 [main] INFO  c.a.c.s.dashboard.config.WebConfig - Sentinel servlet CommonFilter registered
2025-06-16 09:57:04 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8718"]
2025-06-16 09:57:04 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-sentinel-dashboard ************:8718 register finished
2025-06-16 09:57:05 [main] INFO  c.a.c.s.d.DashboardApplication - Started DashboardApplication in 10.341 seconds (JVM running for 10.927)
2025-06-16 09:57:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:57:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-sentinel-dashboard.yml, group=DEFAULT_GROUP
2025-06-16 09:57:06 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:57:06 [RMI TCP Connection(1)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:57:06 [RMI TCP Connection(1)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
