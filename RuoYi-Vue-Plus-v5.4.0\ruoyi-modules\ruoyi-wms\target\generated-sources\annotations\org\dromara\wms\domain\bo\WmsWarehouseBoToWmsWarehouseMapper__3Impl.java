package org.dromara.wms.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.wms.domain.WmsWarehouse;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-15T13:55:33+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WmsWarehouseBoToWmsWarehouseMapper__3Impl implements WmsWarehouseBoToWmsWarehouseMapper__3 {

    @Override
    public WmsWarehouse convert(WmsWarehouseBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WmsWarehouse wmsWarehouse = new WmsWarehouse();

        wmsWarehouse.setCreateBy( arg0.getCreateBy() );
        wmsWarehouse.setCreateDept( arg0.getCreateDept() );
        wmsWarehouse.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            wmsWarehouse.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        wmsWarehouse.setSearchValue( arg0.getSearchValue() );
        wmsWarehouse.setUpdateBy( arg0.getUpdateBy() );
        wmsWarehouse.setUpdateTime( arg0.getUpdateTime() );
        wmsWarehouse.setRemark( arg0.getRemark() );
        wmsWarehouse.setWarehouseId( arg0.getWarehouseId() );
        wmsWarehouse.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wmsWarehouse.setWarehouseName( arg0.getWarehouseName() );
        wmsWarehouse.setWarehouseNumber( arg0.getWarehouseNumber() );
        wmsWarehouse.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wmsWarehouse.setWarehouseType( arg0.getWarehouseType() );

        return wmsWarehouse;
    }

    @Override
    public WmsWarehouse convert(WmsWarehouseBo arg0, WmsWarehouse arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setWarehouseType( arg0.getWarehouseType() );

        return arg1;
    }
}
