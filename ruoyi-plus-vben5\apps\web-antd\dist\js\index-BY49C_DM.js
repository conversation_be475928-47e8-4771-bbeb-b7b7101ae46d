import{d8 as Ke,d9 as et,_ as a,cN as Ae,bd as Be,g as x,c as W,da as tt,db as nt,dc as F,aM as De,h as rt,aC as oe,j as ot,m as Le,dd as it,de as at,b1 as lt,c4 as st,r as ut,b8 as ct,p as Ve,b5 as ge,aW as vt,bf as ft,cO as pt}from"./bootstrap-DCMzVRvD.js";import{g as gt,a as Ye}from"./css-Dmgy8YJo.js";import{p as R,s as re,v as Pe,d as ie,B as v,q as J,D as dt,C as Z,a as s,F as He,a5 as mt,a4 as wt,y as Ue,ad as ht,X as q}from"../jse/index-index-C-MnMZEz.js";import{R as bt,L as Ct}from"./LeftOutlined-DE4sX_Jv.js";var yt="[object Number]";function St(e){return typeof e=="number"||Ke(e)&&et(e)==yt}function Ot(e){const t=R(null),n=re(a({},e)),r=R([]),o=u=>{t.value===null&&(r.value=[],t.value=Ae(()=>{let f;r.value.forEach(g=>{f=a(a({},f),g)}),a(n,f),t.value=null})),r.value.push(u)};return Pe(()=>{t.value&&Ae.cancel(t.value)}),[n,o]}function ze(e,t,n,r){const o=t+n,u=(n-r)/2;if(n>r){if(t>0)return{[e]:u};if(t<0&&o<r)return{[e]:-u}}else if(t<0||o>r)return{[e]:t<0?u:-u};return{}}function Pt(e,t,n,r){const{width:o,height:u}=gt();let f=null;return e<=o&&t<=u?f={x:0,y:0}:(e>o||t>u)&&(f=a(a({},ze("x",n,e,o)),ze("y",r,t,u))),f}var xt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ee=Symbol("previewGroupContext"),xe={provide:e=>{wt(Ee,e)},inject:()=>mt(Ee,{isPreviewGroup:Z(!1),previewUrls:v(()=>new Map),setPreviewUrls:()=>{},current:R(null),setCurrent:()=>{},setShowPreview:()=>{},setMousePosition:()=>{},registerImage:null,rootClassName:""})},Mt=()=>({previewPrefixCls:String,preview:{type:[Boolean,Object],default:!0},icons:{type:Object,default:()=>({})}}),Xe=ie({compatConfig:{MODE:3},name:"PreviewGroup",inheritAttrs:!1,props:Mt(),setup(e,t){let{slots:n}=t;const r=v(()=>{const l={visible:void 0,onVisibleChange:()=>{},getContainer:void 0,current:0};return typeof e.preview=="object"?Fe(e.preview,l):l}),o=re(new Map),u=R(),f=v(()=>r.value.visible),g=v(()=>r.value.getContainer),S=(l,P)=>{var y,L;(L=(y=r.value).onVisibleChange)===null||L===void 0||L.call(y,l,P)},[M,p]=Be(!!f.value,{value:f,onChange:S}),m=R(null),A=v(()=>f.value!==void 0),d=v(()=>Array.from(o.keys())),h=v(()=>d.value[r.value.current]),b=v(()=>new Map(Array.from(o).filter(l=>{let[,{canPreview:P}]=l;return!!P}).map(l=>{let[P,{url:y}]=l;return[P,y]}))),w=function(l,P){let y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;o.set(l,{url:P,canPreview:y})},C=l=>{u.value=l},D=l=>{m.value=l},T=function(l,P){let y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;const L=()=>{o.delete(l)};return o.set(l,{url:P,canPreview:y}),L},O=l=>{l==null||l.stopPropagation(),p(!1),D(null)};return J(h,l=>{C(l)},{immediate:!0,flush:"post"}),dt(()=>{M.value&&A.value&&C(h.value)},{flush:"post"}),xe.provide({isPreviewGroup:Z(!0),previewUrls:b,setPreviewUrls:w,current:u,setCurrent:C,setShowPreview:p,setMousePosition:D,registerImage:T}),()=>{const l=xt(r.value,[]);return s(He,null,[n.default&&n.default(),s(Ze,x(x({},l),{},{"ria-hidden":!M.value,visible:M.value,prefixCls:e.previewPrefixCls,onClose:O,mousePosition:m.value,src:b.value.get(u.value),icons:e.icons,getContainer:g.value}),null)])}}}),X={x:0,y:0},It=a(a({},nt()),{src:String,alt:String,rootClassName:String,icons:{type:Object,default:()=>({})}}),Ze=ie({compatConfig:{MODE:3},name:"Preview",inheritAttrs:!1,props:It,emits:["close","afterClose"],setup(e,t){let{emit:n,attrs:r}=t;const{rotateLeft:o,rotateRight:u,zoomIn:f,zoomOut:g,close:S,left:M,right:p,flipX:m,flipY:A}=re(e.icons),d=Z(1),h=Z(0),b=re({x:1,y:1}),[w,C]=Ot(X),D=()=>n("close"),T=Z(),O=re({originX:0,originY:0,deltaX:0,deltaY:0}),l=Z(!1),P=xe.inject(),{previewUrls:y,current:L,isPreviewGroup:K,setCurrent:$}=P,V=v(()=>y.value.size),Y=v(()=>Array.from(y.value.keys())),I=v(()=>Y.value.indexOf(L.value)),me=v(()=>K.value?y.value.get(L.value):e.src),z=v(()=>K.value&&V.value>1),k=Z({wheelDirection:0}),ee=()=>{d.value=1,h.value=0,b.x=1,b.y=1,C(X),n("afterClose")},c=i=>{i?d.value+=.5:d.value++,C(X)},E=i=>{d.value>1&&(i?d.value-=.5:d.value--),C(X)},j=()=>{h.value+=90},H=()=>{h.value-=90},ae=()=>{b.x=-b.x},we=()=>{b.y=-b.y},le=i=>{i.preventDefault(),i.stopPropagation(),I.value>0&&$(Y.value[I.value-1])},se=i=>{i.preventDefault(),i.stopPropagation(),I.value<V.value-1&&$(Y.value[I.value+1])},te=W({[`${e.prefixCls}-moving`]:l.value}),he=`${e.prefixCls}-operations-operation`,be=`${e.prefixCls}-operations-icon`,ue=[{icon:S,onClick:D,type:"close"},{icon:f,onClick:()=>c(),type:"zoomIn"},{icon:g,onClick:()=>E(),type:"zoomOut",disabled:v(()=>d.value===1)},{icon:u,onClick:j,type:"rotateRight"},{icon:o,onClick:H,type:"rotateLeft"},{icon:m,onClick:ae,type:"flipX"},{icon:A,onClick:we,type:"flipY"}],ce=()=>{if(e.visible&&l.value){const i=T.value.offsetWidth*d.value,N=T.value.offsetHeight*d.value,{left:_,top:U}=Ye(T.value),G=h.value%180!==0;l.value=!1;const B=Pt(G?N:i,G?i:N,_,U);B&&C(a({},B))}},Ce=i=>{i.button===0&&(i.preventDefault(),i.stopPropagation(),O.deltaX=i.pageX-w.x,O.deltaY=i.pageY-w.y,O.originX=w.x,O.originY=w.y,l.value=!0)},ve=i=>{e.visible&&l.value&&C({x:i.pageX-O.deltaX,y:i.pageY-O.deltaY})},ye=i=>{if(!e.visible)return;i.preventDefault();const N=i.deltaY;k.value={wheelDirection:N}},Se=i=>{!e.visible||!z.value||(i.preventDefault(),i.keyCode===De.LEFT?I.value>0&&$(Y.value[I.value-1]):i.keyCode===De.RIGHT&&I.value<V.value-1&&$(Y.value[I.value+1]))},fe=()=>{e.visible&&(d.value!==1&&(d.value=1),(w.x!==X.x||w.y!==X.y)&&C(X))};let ne=()=>{};return Pe(()=>{J([()=>e.visible,l],()=>{ne();let i,N;const _=F(window,"mouseup",ce,!1),U=F(window,"mousemove",ve,!1),G=F(window,"wheel",ye,{passive:!1}),B=F(window,"keydown",Se,!1);try{window.top!==window.self&&(i=F(window.top,"mouseup",ce,!1),N=F(window.top,"mousemove",ve,!1))}catch($e){}ne=()=>{_.remove(),U.remove(),G.remove(),B.remove(),i&&i.remove(),N&&N.remove()}},{flush:"post",immediate:!0}),J([k],()=>{const{wheelDirection:i}=k.value;i>0?E(!0):i<0&&c(!0)})}),Ue(()=>{ne()}),()=>{const{visible:i,prefixCls:N,rootClassName:_}=e;return s(tt,x(x({},r),{},{transitionName:e.transitionName,maskTransitionName:e.maskTransitionName,closable:!1,keyboard:!0,prefixCls:N,onClose:D,afterClose:ee,visible:i,wrapClassName:te,rootClassName:_,getContainer:e.getContainer}),{default:()=>[s("div",{class:[`${e.prefixCls}-operations-wrapper`,_]},[s("ul",{class:`${e.prefixCls}-operations`},[ue.map(U=>{let{icon:G,onClick:B,type:$e,disabled:pe}=U;return s("li",{class:W(he,{[`${e.prefixCls}-operations-operation-disabled`]:pe&&(pe==null?void 0:pe.value)}),onClick:B,key:$e},[ht(G,{class:be})])})])]),s("div",{class:`${e.prefixCls}-img-wrapper`,style:{transform:`translate3d(${w.x}px, ${w.y}px, 0)`}},[s("img",{onMousedown:Ce,onDblclick:fe,ref:T,class:`${e.prefixCls}-img`,src:me.value,alt:e.alt,style:{transform:`scale3d(${b.x*d.value}, ${b.y*d.value}, 1) rotate(${h.value}deg)`}},null)]),z.value&&s("div",{class:W(`${e.prefixCls}-switch-left`,{[`${e.prefixCls}-switch-left-disabled`]:I.value<=0}),onClick:le},[M]),z.value&&s("div",{class:W(`${e.prefixCls}-switch-right`,{[`${e.prefixCls}-switch-right-disabled`]:I.value>=V.value-1}),onClick:se},[p])]})}}});var jt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const We=()=>({src:String,wrapperClassName:String,wrapperStyle:{type:Object,default:void 0},rootClassName:String,prefixCls:String,previewPrefixCls:String,width:[Number,String],height:[Number,String],previewMask:{type:[Boolean,Function],default:void 0},placeholder:rt.any,fallback:String,preview:{type:[Boolean,Object],default:!0},onClick:{type:Function},onError:{type:Function}}),Fe=(e,t)=>{const n=a({},e);return Object.keys(t).forEach(r=>{e[r]===void 0&&(n[r]=t[r])}),n};let Nt=0;const Qe=ie({compatConfig:{MODE:3},name:"VcImage",inheritAttrs:!1,props:We(),emits:["click","error"],setup(e,t){let{attrs:n,slots:r,emit:o}=t;const u=v(()=>e.prefixCls),f=v(()=>`${u.value}-preview`),g=v(()=>{const c={visible:void 0,onVisibleChange:()=>{},getContainer:void 0};return typeof e.preview=="object"?Fe(e.preview,c):c}),S=v(()=>{var c;return(c=g.value.src)!==null&&c!==void 0?c:e.src}),M=v(()=>e.placeholder&&e.placeholder!==!0||r.placeholder),p=v(()=>g.value.visible),m=v(()=>g.value.getContainer),A=v(()=>p.value!==void 0),d=(c,E)=>{var j,H;(H=(j=g.value).onVisibleChange)===null||H===void 0||H.call(j,c,E)},[h,b]=Be(!!p.value,{value:p,onChange:d}),w=R(M.value?"loading":"normal");J(()=>e.src,()=>{w.value=M.value?"loading":"normal"});const C=R(null),D=v(()=>w.value==="error"),T=xe.inject(),{isPreviewGroup:O,setCurrent:l,setShowPreview:P,setMousePosition:y,registerImage:L}=T,K=R(Nt++),$=v(()=>e.preview&&!D.value),V=()=>{w.value="normal"},Y=c=>{w.value="error",o("error",c)},I=c=>{if(!A.value){const{left:E,top:j}=Ye(c.target);O.value?(l(K.value),y({x:E,y:j})):C.value={x:E,y:j}}O.value?P(!0):b(!0),o("click",c)},me=()=>{b(!1),A.value||(C.value=null)},z=R(null);J(()=>z,()=>{w.value==="loading"&&z.value.complete&&(z.value.naturalWidth||z.value.naturalHeight)&&V()});let k=()=>{};Pe(()=>{J([S,$],()=>{if(k(),!O.value)return()=>{};k=L(K.value,S.value,$.value),$.value||k()},{flush:"post",immediate:!0})}),Ue(()=>{k()});const ee=c=>St(c)?c+"px":c;return()=>{const{prefixCls:c,wrapperClassName:E,fallback:j,src:H,placeholder:ae,wrapperStyle:we,rootClassName:le,width:se,height:te,crossorigin:he,decoding:be,alt:ue,sizes:ce,srcset:Ce,usemap:ve,class:ye,style:Se}=a(a({},e),n),fe=g.value,{icons:ne,maskClassName:i}=fe,N=jt(fe,["icons","maskClassName"]),_=W(c,E,le,{[`${c}-error`]:D.value}),U=D.value&&j?j:S.value,G={crossorigin:he,decoding:be,alt:ue,sizes:ce,srcset:Ce,usemap:ve,width:se,height:te,class:W(`${c}-img`,{[`${c}-img-placeholder`]:ae===!0},ye),style:a({height:ee(te)},Se)};return s(He,null,[s("div",{class:_,onClick:$.value?I:B=>{o("click",B)},style:a({width:ee(se),height:ee(te)},we)},[s("img",x(x(x({},G),D.value&&j?{src:j}:{onLoad:V,onError:Y,src:H}),{},{ref:z}),null),w.value==="loading"&&s("div",{"aria-hidden":"true",class:`${c}-placeholder`},[ae||r.placeholder&&r.placeholder()]),r.previewMask&&$.value&&s("div",{class:[`${c}-mask`,i]},[r.previewMask()])]),!O.value&&$.value&&s(Ze,x(x({},N),{},{"aria-hidden":!h.value,visible:h.value,prefixCls:f.value,onClose:me,mousePosition:C.value,src:U,alt:ue,getContainer:m.value,icons:ne,rootClassName:le}),null)])}}});Qe.PreviewGroup=Xe;var $t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"};function Re(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){At(e,o,n[o])})}return e}function At(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Me=function(t,n){var r=Re({},t,n.attrs);return s(oe,Re({},r,{icon:$t}),null)};Me.displayName="RotateLeftOutlined";Me.inheritAttrs=!1;var Dt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"};function Te(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){Lt(e,o,n[o])})}return e}function Lt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ie=function(t,n){var r=Te({},t,n.attrs);return s(oe,Te({},r,{icon:Dt}),null)};Ie.displayName="RotateRightOutlined";Ie.inheritAttrs=!1;var zt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"};function ke(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){Et(e,o,n[o])})}return e}function Et(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var je=function(t,n){var r=ke({},t,n.attrs);return s(oe,ke({},r,{icon:zt}),null)};je.displayName="ZoomInOutlined";je.inheritAttrs=!1;var Rt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"};function _e(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){Tt(e,o,n[o])})}return e}function Tt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ne=function(t,n){var r=_e({},t,n.attrs);return s(oe,_e({},r,{icon:Rt}),null)};Ne.displayName="ZoomOutOutlined";Ne.inheritAttrs=!1;var kt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};function Ge(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){_t(e,o,n[o])})}return e}function _t(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var de=function(t,n){var r=Ge({},t,n.attrs);return s(oe,Ge({},r,{icon:kt}),null)};de.displayName="SwapOutlined";de.inheritAttrs=!1;const Oe=e=>({position:e||"absolute",inset:0}),Gt=e=>{const{iconCls:t,motionDurationSlow:n,paddingXXS:r,marginXXS:o,prefixCls:u}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:"#fff",background:new q("#000").setAlpha(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${n}`,[`.${u}-mask-info`]:a(a({},st),{padding:`0 ${r}px`,[t]:{marginInlineEnd:o,svg:{verticalAlign:"baseline"}}})}},Bt=e=>{const{previewCls:t,modalMaskBg:n,paddingSM:r,previewOperationColorDisabled:o,motionDurationSlow:u}=e,f=new q(n).setAlpha(.1),g=f.clone().setAlpha(.2);return{[`${t}-operations`]:a(a({},ut(e)),{display:"flex",flexDirection:"row-reverse",alignItems:"center",color:e.previewOperationColor,listStyle:"none",background:f.toRgbString(),pointerEvents:"auto","&-operation":{marginInlineStart:r,padding:r,cursor:"pointer",transition:`all ${u}`,userSelect:"none","&:hover":{background:g.toRgbString()},"&-disabled":{color:o,pointerEvents:"none"},"&:last-of-type":{marginInlineStart:0}},"&-progress":{position:"absolute",left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%)"},"&-icon":{fontSize:e.previewOperationSize}})}},Vt=e=>{const{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:r,previewCls:o,zIndexPopup:u,motionDurationSlow:f}=e,g=new q(t).setAlpha(.1),S=g.clone().setAlpha(.2);return{[`${o}-switch-left, ${o}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:u+1,display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:-e.imagePreviewSwitchSize/2,color:e.previewOperationColor,background:g.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${f}`,pointerEvents:"auto",userSelect:"none","&:hover":{background:S.toRgbString()},"&-disabled":{"&, &:hover":{color:r,background:"transparent",cursor:"not-allowed",[`> ${n}`]:{cursor:"not-allowed"}}},[`> ${n}`]:{fontSize:e.previewOperationSize}},[`${o}-switch-left`]:{insetInlineStart:e.marginSM},[`${o}-switch-right`]:{insetInlineEnd:e.marginSM}}},Yt=e=>{const{motionEaseOut:t,previewCls:n,motionDurationSlow:r,componentCls:o}=e;return[{[`${o}-preview-root`]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${n}-body`]:a(a({},Oe()),{overflow:"hidden"}),[`${n}-img`]:{maxWidth:"100%",maxHeight:"100%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${r} ${t} 0s`,userSelect:"none",pointerEvents:"auto","&-wrapper":a(a({},Oe()),{transition:`transform ${r} ${t} 0s`,display:"flex",justifyContent:"center",alignItems:"center","&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${n}-moving`]:{[`${n}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${o}-preview-root`]:{[`${n}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${o}-preview-operations-wrapper`]:{position:"fixed",insetBlockStart:0,insetInlineEnd:0,zIndex:e.zIndexPopup+1,width:"100%"},"&":[Bt(e),Vt(e)]}]},Ht=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",[`${t}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${t}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${t}-mask`]:a({},Gt(e)),[`${t}-mask:hover`]:{opacity:1},[`${t}-placeholder`]:a({},Oe())}}},Ut=e=>{const{previewCls:t}=e;return{[`${t}-root`]:lt(e,"zoom"),"&":at(e,!0)}},Je=ot("Image",e=>{const t=`${e.componentCls}-preview`,n=Le(e,{previewCls:t,modalMaskBg:new q("#000").setAlpha(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[Ht(n),Yt(n),it(Le(n,{componentCls:t})),Ut(n)]},e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new q(e.colorTextLightSolid).toRgbString(),previewOperationColorDisabled:new q(e.colorTextLightSolid).setAlpha(.25).toRgbString(),previewOperationSize:e.fontSizeIcon*1.5})),qe={rotateLeft:s(Me,null,null),rotateRight:s(Ie,null,null),zoomIn:s(je,null,null),zoomOut:s(Ne,null,null),close:s(vt,null,null),left:s(Ct,null,null),right:s(bt,null,null),flipX:s(de,null,null),flipY:s(de,{rotate:90},null)},Xt=()=>({previewPrefixCls:String,preview:ct()}),Zt=ie({compatConfig:{MODE:3},name:"AImagePreviewGroup",inheritAttrs:!1,props:Xt(),setup(e,t){let{attrs:n,slots:r}=t;const{prefixCls:o,rootPrefixCls:u}=Ve("image",e),f=v(()=>`${o.value}-preview`),[g,S]=Je(o),M=v(()=>{const{preview:p}=e;if(p===!1)return p;const m=typeof p=="object"?p:{};return a(a({},m),{rootClassName:S.value,transitionName:ge(u.value,"zoom",m.transitionName),maskTransitionName:ge(u.value,"fade",m.maskTransitionName)})});return()=>g(s(Xe,x(x({},a(a({},n),e)),{},{preview:M.value,icons:qe,previewPrefixCls:f.value}),r))}}),Q=ie({name:"AImage",inheritAttrs:!1,props:We(),setup(e,t){let{slots:n,attrs:r}=t;const{prefixCls:o,rootPrefixCls:u,configProvider:f}=Ve("image",e),[g,S]=Je(o),M=v(()=>{const{preview:p}=e;if(p===!1)return p;const m=typeof p=="object"?p:{};return a(a({icons:qe},m),{transitionName:ge(u.value,"zoom",m.transitionName),maskTransitionName:ge(u.value,"fade",m.maskTransitionName)})});return()=>{var p,m;const A=((m=(p=f.locale)===null||p===void 0?void 0:p.value)===null||m===void 0?void 0:m.Image)||ft.Image,d=()=>s("div",{class:`${o.value}-mask-info`},[s(pt,null,null),A==null?void 0:A.preview]),{previewMask:h=n.previewMask||d}=e;return g(s(Qe,x(x({},a(a(a({},r),e),{prefixCls:o.value})),{},{preview:M.value,rootClassName:W(e.rootClassName,S.value)}),a(a({},n),{previewMask:typeof h=="function"?h:null})))}}});Q.PreviewGroup=Zt;Q.install=function(e){return e.component(Q.name,Q),e.component(Q.PreviewGroup.name,Q.PreviewGroup),e};export{Q as I,Zt as a};
