{"doc": " 用户信息业务对象 sys_user\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "deptId", "doc": " 部门ID\n"}, {"name": "userName", "doc": " 用户账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "userType", "doc": " 用户类型（sys_user系统用户）\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "phonenumber", "doc": " 手机号码\n"}, {"name": "sex", "doc": " 用户性别（0男 1女 2未知）\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "roleIds", "doc": " 角色组\n"}, {"name": "postIds", "doc": " 岗位组\n"}, {"name": "roleId", "doc": " 数据权限 当前角色ID\n"}, {"name": "userIds", "doc": " 用户ID\n"}, {"name": "excludeUserIds", "doc": " 排除不查询的用户(工作流用)\n"}], "enumConstants": [], "methods": [], "constructors": []}