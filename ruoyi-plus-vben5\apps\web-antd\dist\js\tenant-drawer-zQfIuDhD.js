var c=(e,f,i)=>new Promise((o,m)=>{var u=n=>{try{s(i.next(n))}catch(d){m(d)}},t=n=>{try{s(i.throw(n))}catch(d){m(d)}},s=n=>n.done?o(n.value):Promise.resolve(n.value).then(u,t);s((i=i.apply(e,f)).next())});import{al as b,$ as g,aj as y,z as _}from"./bootstrap-DCMzVRvD.js";import{f as x,u as S,g as Y,h as F}from"./tenant-DKIZPfcZ.js";import{g as L}from"./index-C9ZGxfH6.js";import{u as T,d as w}from"./popup-D6rC6QBG.js";import{U as B,d as q,p as v,B as M,P as V,h as z,o as H,w as U,a as j,b as I}from"../jse/index-index-C-MnMZEz.js";import{a as N}from"./get-popup-container-P4S1sr5h.js";import{u as A}from"./use-drawer-6qcpK-D1.js";const te=()=>[{component:"Input",fieldName:"tenantId",label:"租户编号"},{component:"Input",fieldName:"companyName",label:"租户名称"},{component:"Input",fieldName:"contactUserName",label:"联系人"},{component:"Input",fieldName:"contactPhone",label:"联系电话"}],ne=[{type:"checkbox",width:60},{title:"租户编号",field:"tenantId"},{title:"租户名称",field:"companyName"},{title:"联系人",field:"contactUserName"},{title:"联系电话",field:"contactPhone"},{title:"到期时间",field:"expireTime",formatter:({cellValue:e})=>e||"无期限"},{title:"租户状态",field:"status",slots:{default:"status"}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],O=B().add(365,"days").startOf("day").format("YYYY-MM-DD HH:mm:ss"),$=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"id",label:"id"},{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"tenantId",label:"tenantId"},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider1",hideLabel:!0,renderComponentContent:()=>({default:()=>"基本信息"})},{component:"Input",fieldName:"companyName",label:"企业名称",rules:"required"},{component:"Input",fieldName:"contactUserName",label:"联系人",rules:"required"},{component:"Input",fieldName:"contactPhone",label:"联系电话",rules:b().regex(/^1[3-9]\d{9}$/,{message:"请输入正确的联系电话"})},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider2",hideLabel:!0,renderComponentContent:()=>({default:()=>"管理员信息"}),dependencies:{if:e=>!(e!=null&&e.tenantId),triggerFields:["tenantId"]}},{component:"Input",fieldName:"username",label:"用户账号",rules:"required",dependencies:{if:e=>!(e!=null&&e.tenantId),triggerFields:["tenantId"]}},{component:"InputPassword",fieldName:"password",label:"用户密码",rules:"required",dependencies:{if:e=>!(e!=null&&e.tenantId),triggerFields:["tenantId"]}},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider3",hideLabel:!0,renderComponentContent:()=>({default:()=>"租户设置"})},{component:"Select",componentProps:{getPopupContainer:N},fieldName:"packageId",label:"租户套餐",rules:"selectRequired"},{component:"DatePicker",componentProps:{format:"YYYY-MM-DD HH:mm:ss",showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",getPopupContainer:N},defaultValue:O,fieldName:"expireTime",help:`已经设置过期时间不允许重置为'无期限'
即在开通时未设置无期限 以后都不允许设置`,label:"过期时间"},{component:"InputNumber",componentProps:{min:-1},defaultValue:-1,fieldName:"accountCount",help:"-1不限制用户数量",label:"用户数量",renderComponentContent(e){return{addonBefore:()=>e.accountCount===-1?"不限制数量":"输入数量"}},rules:"required"},{component:"Input",fieldName:"domain",help:"可填写域名/端口 填写域名如: www.test.com 或者 www.test.com:8080 填写ip:端口如: 127.0.0.1:8080",label:"绑定域名",renderComponentContent(){return{addonBefore:()=>"http(s)://"}},rules:b().refine(e=>!(e.startsWith("http://")||e.startsWith("https://")),{message:"请输入正确的域名, 不需要http(s)"}).optional()},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider4",hideLabel:!0,renderComponentContent:()=>({default:()=>"企业信息"})},{component:"Input",fieldName:"address",label:"企业地址"},{component:"Input",fieldName:"licenseNumber",label:"企业代码"},{component:"Textarea",fieldName:"intro",formItemClass:"items-start",label:"企业介绍"},{component:"Textarea",fieldName:"remark",formItemClass:"items-start",label:"备注"}],G=q({__name:"tenant-drawer",emits:["reload"],setup(e,{emit:f}){const i=f,o=v(!1),m=M(()=>o.value?g("pages.common.edit"):g("pages.common.add")),[u,t]=y({commonConfig:{formItemClass:"col-span-2",labelWidth:100,componentProps:{class:"w-full"}},schema:$(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function s(){return c(this,null,function*(){const a=(yield L()).map(p=>({label:p.packageName,value:p.packageId}));t.updateSchema([{componentProps:{optionFilterProp:"label",optionLabelProp:"label",options:a,showSearch:!0},fieldName:"packageId"}])})}const{onBeforeClose:n,markInitialized:d,resetInitialized:h}=T({initializedGetter:w(t),currentGetter:w(t)}),[C,l]=A({onBeforeClose:n,onClosed:k,onConfirm:D,onOpenChange(r){return c(this,null,function*(){if(!r)return null;l.drawerLoading(!0);const{id:a}=l.getData();if(o.value=!!a,yield s(),o.value&&a){const p=yield x(a);yield t.setValues(p)}t.updateSchema([{fieldName:"packageId",componentProps:{disabled:o.value}}]),yield d(),l.drawerLoading(!1)})}}),P=S();function D(){return c(this,null,function*(){try{l.lock(!0);const{valid:r}=yield t.validate();if(!r)return;const a=V(yield t.getValues());yield o.value?Y(a):F(a),h(),i("reload"),l.close(),P.initTenant()}catch(r){console.error(r)}finally{l.lock(!1)}})}function k(){return c(this,null,function*(){yield t.resetForm(),h()})}return(r,a)=>(H(),z(I(C),{title:m.value,class:"w-[600px]"},{default:U(()=>[j(I(u))]),_:1},8,["title"]))}}),W=_(G,[["__scopeId","data-v-81c4f936"]]),ae=Object.freeze(Object.defineProperty({__proto__:null,default:W},Symbol.toStringTag,{value:"Module"}));export{ae as a,ne as c,te as q,W as t};
