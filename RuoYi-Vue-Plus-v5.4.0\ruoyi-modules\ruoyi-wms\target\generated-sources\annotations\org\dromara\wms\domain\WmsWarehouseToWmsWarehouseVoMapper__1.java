package org.dromara.wms.domain;

import io.github.linpeilie.AutoMapperConfig__1059;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.bo.WmsWarehouseBoToWmsWarehouseMapper__1;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.vo.WmsWarehouseVoToWmsWarehouseMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1059.class,
    uses = {WmsWarehouseBoToWmsWarehouseMapper__1.class,WmsWarehouseVoToWmsWarehouseMapper__1.class},
    imports = {}
)
public interface WmsWarehouseToWmsWarehouseVoMapper__1 extends BaseMapper<WmsWarehouse, WmsWarehouseVo> {
}
