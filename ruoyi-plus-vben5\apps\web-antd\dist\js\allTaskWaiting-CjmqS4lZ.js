var fe=Object.defineProperty,ge=Object.defineProperties;var be=Object.getOwnPropertyDescriptors;var q=Object.getOwnPropertySymbols;var he=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var G=(e,t,l)=>t in e?fe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,B=(e,t)=>{for(var l in t||(t={}))he.call(t,l)&&G(e,l,t[l]);if(q)for(var l of q(t))ye.call(t,l)&&G(e,l,t[l]);return e},j=(e,t)=>ge(e,be(t));var P=(e,t,l)=>new Promise((h,u)=>{var d=m=>{try{p(l.next(m))}catch(f){u(f)}},v=m=>{try{p(l.throw(m))}catch(f){u(f)}},p=m=>m.done?h(m.value):Promise.resolve(m.value).then(d,v);p((l=l.apply(e,t)).next())});import{c as Se}from"./index-B4NcjlQn.js";import{i as xe,j as Ce}from"./index-CZhogUxH.js";import $e from"./approval-card-6R4jdcAw.js";import{_ as we}from"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import{j as _e,m as Ie,_ as x,r as Te,c4 as Re,b8 as R,a_ as He,cC as Ne,cD as Pe,e as Ee,p as Me,g as F,c as W,bO as K,bk as Be,o as J,bP as ze,cx as Ae,aD as De,au as Q,aw as Y,bL as Le,cE as Z,T as Fe,aq as je,bn as z,z as Ue}from"./bootstrap-DCMzVRvD.js";import{_ as Oe}from"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{b as We,R as Ve,F as ke}from"./constant-BxyJFj0E.js";import{d as V,p as y,q as Xe,B as H,ac as qe,a as n,I as de,C as ee,E as Ge,v as te,l as Ke,h as U,o as E,w as b,j as _,b as s,k as oe,c as O,f as ae,F as Je,K as Qe,t as Ye}from"../jse/index-index-C-MnMZEz.js";import{P as Ze}from"./index-qvRUEWLR.js";import et from"./index-B1dOBW9y.js";import"./index-BxBCzu2M.js";import{a as tt,I as le}from"./Search-ClCped_G.js";import{S as ot}from"./index-Ollxi7Rl.js";import{_ as at}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{u as lt}from"./use-tabs-Zz_nc_n2.js";import{a as it}from"./tree-DFBawhPd.js";import{a as ie}from"./get-popup-container-P4S1sr5h.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./relativeTime-DEqmspR6.js";import"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import"./index-D59rZjD-.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-DCFckLr6.js";import"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import"./use-modal-CeMSCP2m.js";import"./x-Bfkqqjgb.js";import"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import"./index-BLwHKR_M.js";import"./index-i2_yEmR1.js";import"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import"./user-select-modal-9zMWfXzj.js";import"./index-ocPq22VW.js";import"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./use-vxe-grid-BC7vZzEr.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./LeftOutlined-DE4sX_Jv.js";import"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BeyziwLP.js";import"./index-BIMmoqOy.js";import"./move-DLDqWE9R.js";import"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import"./rotate-cw-DzZTu9nW.js";import"./index-BELOxkuV.js";import"./useMemo-BwJyMulH.js";import"./index-B-GBMyZJ.js";import"./DownOutlined-CERO2SW5.js";function ne(e,t){return{[`${e}, ${e}:hover, ${e}:focus`]:{color:t.colorTextDisabled,cursor:"not-allowed"}}}function re(e){return{backgroundColor:e.bgColorSelected,boxShadow:e.boxShadow}}const nt=x({overflow:"hidden"},Re),rt=e=>{const{componentCls:t}=e;return{[t]:x(x(x(x(x({},Te(e)),{display:"inline-block",padding:e.segmentedContainerPadding,color:e.labelColor,backgroundColor:e.bgColor,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,[`${t}-group`]:{position:"relative",display:"flex",alignItems:"stretch",justifyItems:"flex-start",width:"100%"},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-block`]:{display:"flex"},[`&${t}-block ${t}-item`]:{flex:1,minWidth:0},[`${t}-item`]:{position:"relative",textAlign:"center",cursor:"pointer",transition:`color ${e.motionDurationMid} ${e.motionEaseInOut}`,borderRadius:e.borderRadiusSM,"&-selected":x(x({},re(e)),{color:e.labelColorHover}),"&::after":{content:'""',position:"absolute",width:"100%",height:"100%",top:0,insetInlineStart:0,borderRadius:"inherit",transition:`background-color ${e.motionDurationMid}`,pointerEvents:"none"},[`&:hover:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.labelColorHover,"&::after":{backgroundColor:e.bgColorHover}},"&-label":x({minHeight:e.controlHeight-e.segmentedContainerPadding*2,lineHeight:`${e.controlHeight-e.segmentedContainerPadding*2}px`,padding:`0 ${e.segmentedPaddingHorizontal}px`},nt),"&-icon + *":{marginInlineStart:e.marginSM/2},"&-input":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:0,opacity:0,pointerEvents:"none"}},[`${t}-thumb`]:x(x({},re(e)),{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:"100%",padding:`${e.paddingXXS}px 0`,borderRadius:e.borderRadiusSM,[`& ~ ${t}-item:not(${t}-item-selected):not(${t}-item-disabled)::after`]:{backgroundColor:"transparent"}}),[`&${t}-lg`]:{borderRadius:e.borderRadiusLG,[`${t}-item-label`]:{minHeight:e.controlHeightLG-e.segmentedContainerPadding*2,lineHeight:`${e.controlHeightLG-e.segmentedContainerPadding*2}px`,padding:`0 ${e.segmentedPaddingHorizontal}px`,fontSize:e.fontSizeLG},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadius}},[`&${t}-sm`]:{borderRadius:e.borderRadiusSM,[`${t}-item-label`]:{minHeight:e.controlHeightSM-e.segmentedContainerPadding*2,lineHeight:`${e.controlHeightSM-e.segmentedContainerPadding*2}px`,padding:`0 ${e.segmentedPaddingHorizontalSM}px`},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadiusXS}}}),ne(`&-disabled ${t}-item`,e)),ne(`${t}-item-disabled`,e)),{[`${t}-thumb-motion-appear-active`]:{transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, width ${e.motionDurationSlow} ${e.motionEaseInOut}`,willChange:"transform, width"}})}},st=_e("Segmented",e=>{const{lineWidthBold:t,lineWidth:l,colorTextLabel:h,colorText:u,colorFillSecondary:d,colorBgLayout:v,colorBgElevated:p}=e,m=Ie(e,{segmentedPaddingHorizontal:e.controlPaddingHorizontal-l,segmentedPaddingHorizontalSM:e.controlPaddingHorizontalSM-l,segmentedContainerPadding:t,labelColor:h,labelColorHover:u,bgColor:v,bgColorHover:d,bgColorSelected:p});return[rt(m)]}),se=e=>e?{left:e.offsetLeft,right:e.parentElement.clientWidth-e.clientWidth-e.offsetLeft,width:e.clientWidth}:null,M=e=>e!==void 0?`${e}px`:void 0,dt=V({props:{value:R(),getValueIndex:R(),prefixCls:R(),motionName:R(),onMotionStart:R(),onMotionEnd:R(),direction:R(),containerRef:R()},emits:["motionStart","motionEnd"],setup(e,t){let{emit:l}=t;const h=y(),u=o=>{var i;const w=e.getValueIndex(o),C=(i=e.containerRef.value)===null||i===void 0?void 0:i.querySelectorAll(`.${e.prefixCls}-item`)[w];return(C==null?void 0:C.offsetParent)&&C},d=y(null),v=y(null);Xe(()=>e.value,(o,i)=>{const w=u(i),C=u(o),N=se(w),A=se(C);d.value=N,v.value=A,l(w&&C?"motionStart":"motionEnd")},{flush:"post"});const p=H(()=>{var o,i;return e.direction==="rtl"?M(-((o=d.value)===null||o===void 0?void 0:o.right)):M((i=d.value)===null||i===void 0?void 0:i.left)}),m=H(()=>{var o,i;return e.direction==="rtl"?M(-((o=v.value)===null||o===void 0?void 0:o.right)):M((i=v.value)===null||i===void 0?void 0:i.left)});let f;const I=o=>{clearTimeout(f),de(()=>{o&&(o.style.transform="translateX(var(--thumb-start-left))",o.style.width="var(--thumb-start-width)")})},$=o=>{f=setTimeout(()=>{o&&(Ne(o,`${e.motionName}-appear-active`),o.style.transform="translateX(var(--thumb-active-left))",o.style.width="var(--thumb-active-width)")})},c=o=>{d.value=null,v.value=null,o&&(o.style.transform=null,o.style.width=null,Pe(o,`${e.motionName}-appear-active`)),l("motionEnd")},T=H(()=>{var o,i;return{"--thumb-start-left":p.value,"--thumb-start-width":M((o=d.value)===null||o===void 0?void 0:o.width),"--thumb-active-left":m.value,"--thumb-active-width":M((i=v.value)===null||i===void 0?void 0:i.width)}});return qe(()=>{clearTimeout(f)}),()=>{const o={ref:h,style:T.value,class:[`${e.prefixCls}-thumb`]};return n(He,{appear:!0,onBeforeEnter:I,onEnter:$,onAfterEnter:c},{default:()=>[!d.value||!v.value?null:n("div",o,null)]})}}});function ut(e){return e.map(t=>typeof t=="object"&&t!==null?t:{label:t==null?void 0:t.toString(),title:t==null?void 0:t.toString(),value:t})}const mt=()=>({prefixCls:String,options:ze(),block:J(),disabled:J(),size:Be(),value:x(x({},Ae([String,Number])),{required:!0}),motionName:String,onChange:K(),"onUpdate:value":K()}),ue=(e,t)=>{let{slots:l,emit:h}=t;const{value:u,disabled:d,payload:v,title:p,prefixCls:m,label:f=l.label,checked:I,className:$}=e,c=T=>{d||h("change",T,u)};return n("label",{class:W({[`${m}-item-disabled`]:d},$)},[n("input",{class:`${m}-item-input`,type:"radio",disabled:d,checked:I,onChange:c},null),n("div",{class:`${m}-item-label`,title:typeof p=="string"?p:""},[typeof f=="function"?f({value:u,disabled:d,payload:v,title:p}):f!=null?f:u])])};ue.inheritAttrs=!1;const ct=V({name:"ASegmented",inheritAttrs:!1,props:Ee(mt(),{options:[],motionName:"thumb-motion"}),slots:Object,setup(e,t){let{emit:l,slots:h,attrs:u}=t;const{prefixCls:d,direction:v,size:p}=Me("segmented",e),[m,f]=st(d),I=ee(),$=ee(!1),c=H(()=>ut(e.options)),T=(o,i)=>{e.disabled||(l("update:value",i),l("change",i))};return()=>{const o=d.value;return m(n("div",F(F({},u),{},{class:W(o,{[f.value]:!0,[`${o}-block`]:e.block,[`${o}-disabled`]:e.disabled,[`${o}-lg`]:p.value=="large",[`${o}-sm`]:p.value=="small",[`${o}-rtl`]:v.value==="rtl"},u.class),ref:I}),[n("div",{class:`${o}-group`},[n(dt,{containerRef:I,prefixCls:o,value:e.value,motionName:`${o}-${e.motionName}`,direction:v.value,getValueIndex:i=>c.value.findIndex(w=>w.value===i),onMotionStart:()=>{$.value=!0},onMotionEnd:()=>{$.value=!1}},null),c.value.map(i=>n(ue,F(F({key:i.value,prefixCls:o,checked:i.value===e.value,onChange:T},i),{},{className:W(i.className,`${o}-item`,{[`${o}-item-selected`]:i.value===e.value&&!$.value}),disabled:!!e.disabled||!!i.disabled}),h))])]))}}}),pt=De(ct),vt={class:"flex h-full gap-2"},ft={class:"bg-background relative flex h-full min-w-[320px] max-w-[320px] flex-col rounded-lg"},gt={class:"bg-background z-100 sticky left-0 top-0 w-full rounded-t-lg border-b-[1px] border-solid p-2"},bt={class:"flex items-center gap-1"},ht={class:"flex"},yt={key:2,class:"flex items-center justify-center text-[14px] opacity-50"},St={key:3,class:"absolute left-0 top-0 flex h-full w-full items-center justify-center bg-[rgba(0,0,0,0.1)]"},xt={class:"bg-background sticky bottom-0 w-full rounded-b-lg border-t-[1px] py-2"},Ct={class:"flex items-center justify-center"},$t=V({__name:"allTaskWaiting",setup(e){const t=Q.PRESENTED_IMAGE_SIMPLE,l=y([]),h=y(0),u=y(1),d=y(!1),v=[{label:"待办任务",value:"todo"},{label:"已办任务",value:"done"}],p=y("todo"),m=H(()=>p.value==="todo"?xe:Ce),f=H(()=>p.value==="done"?"readonly":"admin");function I(){return P(this,null,function*(){var g;(g=o.value)==null||g.scroll({top:0,behavior:"auto"}),u.value=1,l.value=[],yield de(),yield i(!0)})}const $={flowName:"",nodeName:"",flowCode:"",createByIds:[],category:null},c=y(Y($)),T=H(()=>l.value.length===h.value),o=Ge("cardContainerRef");function i(g=!1){return P(this,null,function*(){var S;(S=o.value)==null||S.scroll({top:0,behavior:"auto"}),u.value=1,N.value=void 0,h.value=0,C.value="",g&&(c.value=Y($),L.value=[]),d.value=!0;const a=yield m.value(B({pageSize:10,pageNum:u.value},c.value));if(l.value=a.rows.map(r=>j(B({},r),{active:!1,randomId:Z()})),h.value=a.total,d.value=!1,l.value.length>0){const r=l.value[0];N.value=r,A(r)}})}te(i);const w=Le(g=>P(null,null,function*(){if(!g.target)return;const{scrollTop:a,clientHeight:S,scrollHeight:r}=g.target,X=a+S>=r-We;if(console.log("scrollTop + clientHeight",a+S),console.log("scrollHeight",r),X&&!T.value){d.value=!0,u.value+=1;const pe=yield m.value(B({pageSize:10,pageNum:u.value},c.value));l.value.push(...pe.rows.map(ve=>j(B({},ve),{active:!1,randomId:Z()}))),d.value=!1}}),200),C=y(""),N=y();function A(g){return P(this,null,function*(){const{randomId:a}=g;C.value!==a&&(N.value=g,l.value.forEach(S=>{S.active=S.randomId===a}),C.value=a)})}const{refreshTab:me}=lt(),D=y(!1),L=y([]);function ce(g){D.value=!0,L.value=g,c.value.createByIds=g.map(a=>a.userId)}const k=y([]);return te(()=>P(null,null,function*(){const g=yield Se();it(g,"label"," / "),k.value=g})),(g,a)=>{const S=Ke("a-button");return E(),U(s(at),{"auto-content-height":!0},{default:b(()=>[_("div",vt,[_("div",ft,[_("div",gt,[n(s(pt),{value:p.value,"onUpdate:value":a[0]||(a[0]=r=>p.value=r),options:v,block:"",class:"mb-2",onChange:I},null,8,["value"]),_("div",bt,[n(s(tt),{value:c.value.flowName,"onUpdate:value":a[1]||(a[1]=r=>c.value.flowName=r),placeholder:"流程名称搜索",onSearch:a[2]||(a[2]=r=>i(!1))},null,8,["value"]),n(s(Fe),{placement:"top",title:"重置"},{default:b(()=>[n(S,{onClick:a[3]||(a[3]=r=>i(!0))},{default:b(()=>[n(s(Ve))]),_:1})]),_:1}),n(s(Ze),{open:D.value,"onUpdate:open":a[11]||(a[11]=r=>D.value=r),"get-popup-container":s(ie),placement:"rightTop",trigger:"click"},{title:b(()=>a[13]||(a[13]=[_("div",{class:"w-full border-b pb-[12px] text-[16px]"},"搜索",-1)])),content:b(()=>[n(s(je),{colon:!1,"label-col":{span:6},model:c.value,autocomplete:"off",class:"w-[300px]",onFinish:a[10]||(a[10]=()=>i(!1))},{default:b(()=>[n(s(z),{label:"申请人"},{default:b(()=>[n(s(Oe),{"user-list":L.value,"onUpdate:userList":a[4]||(a[4]=r=>L.value=r),onCancel:a[5]||(a[5]=()=>D.value=!0),onFinish:ce},null,8,["user-list"])]),_:1}),n(s(z),{label:"流程分类"},{default:b(()=>[n(s(et),{value:c.value.category,"onUpdate:value":a[6]||(a[6]=r=>c.value.category=r),"allow-clear":!0,"field-names":{label:"label",value:"id"},"get-popup-container":s(ie),"tree-data":k.value,"tree-default-expand-all":!0,"tree-line":{showLeafIcon:!1},placeholder:"请选择","tree-node-filter-prop":"label","tree-node-label-prop":"fullName"},null,8,["value","get-popup-container","tree-data"])]),_:1}),n(s(z),{label:"任务名称"},{default:b(()=>[n(s(le),{value:c.value.nodeName,"onUpdate:value":a[7]||(a[7]=r=>c.value.nodeName=r),placeholder:"请输入"},null,8,["value"])]),_:1}),n(s(z),{label:"流程编码"},{default:b(()=>[n(s(le),{value:c.value.flowCode,"onUpdate:value":a[8]||(a[8]=r=>c.value.flowCode=r),placeholder:"请输入"},null,8,["value"])]),_:1}),n(s(z),null,{default:b(()=>[_("div",ht,[n(S,{block:"","html-type":"submit",type:"primary"},{default:b(()=>a[14]||(a[14]=[oe(" 搜索 ")])),_:1,__:[14]}),n(S,{block:"",class:"ml-2",onClick:a[9]||(a[9]=r=>i(!0))},{default:b(()=>a[15]||(a[15]=[oe(" 重置 ")])),_:1,__:[15]})])]),_:1})]),_:1},8,["model"])]),default:b(()=>[n(S,null,{default:b(()=>[n(s(ke))]),_:1})]),_:1},8,["open","get-popup-container"])])]),_("div",{ref_key:"cardContainerRef",ref:o,class:"thin-scrollbar flex flex-1 flex-col gap-2 overflow-y-auto py-3",onScroll:a[12]||(a[12]=(...r)=>s(w)&&s(w)(...r))},[l.value.length>0?(E(!0),O(Je,{key:0},Qe(l.value,r=>(E(),U(s($e),{key:r.randomId,info:r,class:"mx-2","row-key":"randomId",onClick:X=>A(r)},null,8,["info","onClick"]))),128)):(E(),U(s(Q),{key:1,image:s(t)},null,8,["image"])),T.value&&l.value.length>0?(E(),O("div",yt," 没有更多数据了 ")):ae("",!0),d.value?(E(),O("div",St,[n(s(ot),{tip:"加载中..."})])):ae("",!0)],544),_("div",xt,[_("div",Ct," 共 "+Ye(h.value)+" 条记录 ",1)])]),n(s(we),{task:N.value,type:f.value,onReload:s(me)},null,8,["task","type","onReload"])])]),_:1})}}}),ta=Ue($t,[["__scopeId","data-v-7341fd2a"]]);export{ta as default};
