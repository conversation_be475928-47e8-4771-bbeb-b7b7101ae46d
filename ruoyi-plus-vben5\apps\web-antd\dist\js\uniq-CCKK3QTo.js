import{bR as s,bS as l,bT as w,bU as S,bV as g}from"./bootstrap-DCMzVRvD.js";function A(){}var I=1/0,R=s&&1/l(new s([,-0]))[1]==I?function(n){return new s(n)}:A,T=200;function p(n,q,b){var f=-1,h=S,a=n.length,o=!0,i=[],e=i;if(a>=T){var u=R(n);if(u)return l(u);o=!1,h=g,e=new w}else e=i;n:for(;++f<a;){var t=n[f],r=t;if(t=t!==0?t:0,o&&r===r){for(var c=e.length;c--;)if(e[c]===r)continue n;i.push(t)}else h(e,r,b)||(e!==i&&e.push(r),i.push(t))}return i}function C(n){return n&&n.length?p(n):[]}export{C as u};
