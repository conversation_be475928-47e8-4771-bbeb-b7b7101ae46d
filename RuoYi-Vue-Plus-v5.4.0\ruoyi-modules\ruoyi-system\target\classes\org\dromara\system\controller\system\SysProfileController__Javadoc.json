{"doc": " 个人信息 业务处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "profile", "paramTypes": [], "doc": " 个人信息\n"}, {"name": "updateProfile", "paramTypes": ["org.dromara.system.domain.bo.SysUserProfileBo"], "doc": " 修改用户信息\n"}, {"name": "updatePwd", "paramTypes": ["org.dromara.system.domain.bo.SysUserPasswordBo"], "doc": " 重置密码\n\n @param bo 新旧密码\n"}, {"name": "avatar", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 头像上传\n\n @param avatarfile 用户头像\n"}], "constructors": []}