{"doc": " 权限安全配置\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "addInterceptors", "paramTypes": ["org.springframework.web.servlet.config.annotation.InterceptorRegistry"], "doc": " 注册sa-token的拦截器\n"}, {"name": "getSaServletFilter", "paramTypes": [], "doc": " 校验是否从网关转发\n"}, {"name": "actuatorF<PERSON>er", "paramTypes": [], "doc": " 对 actuator 健康检查接口 做账号密码鉴权\n"}], "constructors": []}