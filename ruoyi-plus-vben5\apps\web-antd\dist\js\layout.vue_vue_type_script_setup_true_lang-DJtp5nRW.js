var zl=Object.defineProperty,Pl=Object.defineProperties;var Hl=Object.getOwnPropertyDescriptors;var yt=Object.getOwnPropertySymbols;var ha=Object.prototype.hasOwnProperty,ba=Object.prototype.propertyIsEnumerable;var ma=(l,o,t)=>o in l?zl(l,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[o]=t,fe=(l,o)=>{for(var t in o||(o={}))ha.call(o,t)&&ma(l,t,o[t]);if(yt)for(var t of yt(o))ba.call(o,t)&&ma(l,t,o[t]);return l},Be=(l,o)=>Pl(l,Hl(o));var Se=(l,o)=>{var t={};for(var a in l)ha.call(l,a)&&o.indexOf(a)<0&&(t[a]=l[a]);if(l!=null&&yt)for(var a of yt(l))o.indexOf(a)<0&&ba.call(l,a)&&(t[a]=l[a]);return t};var oe=(l,o,t)=>new Promise((a,s)=>{var n=i=>{try{d(t.next(i))}catch(m){s(m)}},r=i=>{try{d(t.throw(i))}catch(m){s(m)}},d=i=>i.done?a(i.value):Promise.resolve(i.value).then(n,r);d((t=t.apply(l,o)).next())});import{w as ue,cc as Ul,e9 as Il,ea as It,cu as Qe,eb as Al,ec as Ol,bG as Ne,ed as Wl,ee as Nl,ef as Dl,eg as Fl,eh as Rl,ei as Kl,ej as jl,ek as Gl,el as ql,em as Yl,en as Xl,eo as Ql,ep as Zl,eq as Jl,er as eo,es as to,z as Ce,aN as Ue,et as ao,by as lo,a_ as nt,bm as Le,as as ke,eu as _t,ev as At,ew as oo,ex as no,ey as so,ez as ro,eA as io,eB as uo,bC as Xe,eC as co,cv as po,cp as Ze,aB as st,av as Ge,$ as v,ax as Te,e4 as fo,eD as Ea,az as Je,cG as La,br as za,al as Pa,cW as mo,bE as Ha,eE as ho,cH as rt,dq as et,eF as bo,eG as vo,eH as go,eI as yo,eJ as wo,dn as xo,eK as ko,d0 as Co,dp as Ua,eL as So,cF as Mo,eM as _o,eN as $o,eO as Bo,eP as To,eQ as Vo,eR as Eo,d3 as Lo,eS as Ia,eT as zo,eU as je,eV as Po,d2 as Ho,eW as Uo,eX as Io,eY as Ao}from"./bootstrap-DCMzVRvD.js";import{d as T,c as x,o as u,n as D,b as e,G as re,r as P,h as k,w as c,a as p,B as w,O as me,e as We,g as tt,H as ne,m as he,aQ as Oo,u as y,p as X,q as de,F as Y,K as le,f as H,ai as Aa,C as St,aR as $t,V as Wo,v as Fe,j as $,t as M,k as V,L as Ee,aS as No,aw as Re,aT as Do,y as bt,aB as Fo,aU as Oa,aV as wt,T as Me,aW as Ro,I as Ke,aX as Ht,aY as Ko,aZ as Wa,a_ as pt,aK as Mt,a$ as jo,b0 as xt,s as vt,b1 as Go,i as Ot,a1 as Ie,W as at,X as qo,b2 as Yo,b3 as Xo,ab as Qo,a0 as L,b4 as va,b5 as Zo,b6 as ga,al as Na,E as ya,b7 as Jo,D as Da,b8 as en,b9 as tn,a2 as ot,au as wa,ba as an,N as xa,z as Bt,a4 as Fa,a5 as Ra,at as Ut,ac as Ka,$ as ln,bb as on,ap as nn,a8 as sn,Y as ja,bc as rn,P as ka}from"../jse/index-index-C-MnMZEz.js";import{_ as ft}from"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import{d as Wt,e as Nt,f as Dt,g as ut,h as dn,i as Ga,j as qa,S as un,M as cn,k as pn,a as fn,_ as mn,c as hn}from"./theme-toggle.vue_vue_type_script_setup_true_lang-BfrV53rV.js";import{u as mt}from"./use-modal-CeMSCP2m.js";import{u as Ya}from"./use-drawer-6qcpK-D1.js";import{a as bn,b as vn,_ as gn}from"./TabsList.vue_vue_type_script_setup_true_lang-QZrN9Wp8.js";import{R as Ft}from"./rotate-cw-DzZTu9nW.js";import{a as gt,g as dt,u as Xa}from"./use-tabs-Zz_nc_n2.js";import{X as Tt}from"./x-Bfkqqjgb.js";import{c as yn}from"./index-A0HTSyFu.js";function Rt(l,o){for(const t of l){if(t.path===o)return t;const a=t.children&&Rt(t.children,o);if(a)return a}return null}function ct(l,o,t=0){var r;const a=Rt(l,o),s=(r=a==null?void 0:a.parents)==null?void 0:r[t],n=s?l.find(d=>d.path===s):void 0;return{findMenu:a,rootMenu:n,rootMenuPath:s}}const wn=ue("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const xn=ue("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const kn=ue("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const Cn=ue("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const Sn=ue("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const Mn=ue("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const _n=ue("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);const $n=ue("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);const Bn=ue("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);const Tn=ue("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const Vn=ue("corner-down-left",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const En=ue("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const Ln=ue("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const Qa=ue("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const Za=ue("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const zn=ue("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const Pn=ue("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const Hn=ue("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const Ja=ue("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const Un=ue("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const el=ue("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const Vt=ue("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const In=ue("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const An=ue("search-x",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const Ca=ue("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);const tl=ue("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const On=ue("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),Wn=Ul("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),Nn=T({__name:"Badge",props:{class:{},variant:{}},setup(l){const o=l;return(t,a)=>(u(),x("div",{class:D(e(re)(e(Wn)({variant:t.variant}),o.class))},[P(t.$slots,"default")],2))}}),Dn=T({__name:"Breadcrumb",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("nav",{class:D(o.class),"aria-label":"breadcrumb",role:"navigation"},[P(t.$slots,"default")],2))}}),Fn=T({__name:"BreadcrumbItem",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("li",{class:D(e(re)("hover:text-foreground inline-flex items-center gap-1.5",o.class))},[P(t.$slots,"default")],2))}}),Rn=T({__name:"BreadcrumbLink",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"a"}},setup(l){const o=l;return(t,a)=>(u(),k(e(Il),{as:t.as,"as-child":t.asChild,class:D(e(re)("hover:text-foreground transition-colors",o.class))},{default:c(()=>[P(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Kn=T({__name:"BreadcrumbList",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("ol",{class:D(e(re)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",o.class))},[P(t.$slots,"default")],2))}}),jn=T({__name:"BreadcrumbPage",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("span",{class:D(e(re)("text-foreground font-normal",o.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[P(t.$slots,"default")],2))}}),Gn=T({__name:"BreadcrumbSeparator",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("li",{class:D(e(re)("[&>svg]:size-3.5",o.class)),"aria-hidden":"true",role:"presentation"},[P(t.$slots,"default",{},()=>[p(e(It))])],2))}}),qn=T({__name:"DropdownMenuLabel",props:{class:{},inset:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l,t=w(()=>{const r=o,{class:s}=r;return Se(r,["class"])}),a=Qe(t);return(s,n)=>(u(),k(e(Al),me(e(a),{class:e(re)("px-2 py-1.5 text-sm font-semibold",s.inset&&"pl-8",o.class)}),{default:c(()=>[P(s.$slots,"default")]),_:3},16,["class"]))}}),kt=T({__name:"DropdownMenuSeparator",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l,t=w(()=>{const n=o,{class:a}=n;return Se(n,["class"])});return(a,s)=>(u(),k(e(Ol),me(t.value,{class:e(re)("bg-border -mx-1 my-1 h-px",o.class)}),null,16,["class"]))}}),Sa=T({__name:"DropdownMenuShortcut",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("span",{class:D(e(re)("ml-auto text-xs tracking-widest opacity-60",o.class))},[P(t.$slots,"default")],2))}}),Yn=T({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:o}){const s=Ne(l,o);return(n,r)=>(u(),k(e(Wl),We(tt(e(s))),{default:c(()=>[P(n.$slots,"default")]),_:3},16))}}),Xn=T({__name:"HoverCardContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const o=l,t=w(()=>{const r=o,{class:s}=r;return Se(r,["class"])}),a=Qe(t);return(s,n)=>(u(),k(e(Nl),null,{default:c(()=>[p(e(Dl),me(e(a),{class:e(re)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup w-64 rounded-md border p-4 shadow-md outline-none",o.class)}),{default:c(()=>[P(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Qn=T({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l;return(t,a)=>(u(),k(e(Fl),We(tt(o)),{default:c(()=>[P(t.$slots,"default")]),_:3},16))}}),Zn=T({__name:"NumberField",props:{class:{},defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},emits:["update:modelValue"],setup(l,{emit:o}){const t=l,a=o,s=w(()=>{const i=t,{class:r}=i;return Se(i,["class"])}),n=Ne(s,a);return(r,d)=>(u(),k(e(Rl),me(e(n),{class:e(re)("grid gap-1.5",t.class)}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"]))}}),Jn=T({__name:"NumberFieldContent",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("div",{class:D(e(re)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",o.class))},[P(t.$slots,"default")],2))}}),es=T({__name:"NumberFieldDecrement",props:{class:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l,t=w(()=>{const r=o,{class:s}=r;return Se(r,["class"])}),a=Qe(t);return(s,n)=>(u(),k(e(jl),me({"data-slot":"decrement"},e(a),{class:e(re)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",o.class)}),{default:c(()=>[P(s.$slots,"default",{},()=>[p(e(Kl),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),ts=T({__name:"NumberFieldIncrement",props:{class:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l,t=w(()=>{const r=o,{class:s}=r;return Se(r,["class"])}),a=Qe(t);return(s,n)=>(u(),k(e(Gl),me({"data-slot":"increment"},e(a),{class:e(re)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",o.class)}),{default:c(()=>[P(s.$slots,"default",{},()=>[p(e(In),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),as=T({__name:"NumberFieldInput",setup(l){return(o,t)=>(u(),k(e(ql),{class:D(e(re)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),al=T({__name:"ScrollBar",props:{class:{},orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=w(()=>{const n=o,{class:a}=n;return Se(n,["class"])});return(a,s)=>(u(),k(e(Yl),me(t.value,{class:e(re)("flex touch-none select-none transition-colors",a.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",a.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",o.class)}),{default:c(()=>[p(e(Xl),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),ls=T({__name:"ScrollArea",props:{class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{},type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=w(()=>{const n=o,{class:a}=n;return Se(n,["class"])});return(a,s)=>(u(),k(e(Ql),me(t.value,{class:e(re)("relative overflow-hidden",o.class)}),{default:c(()=>[p(e(Zl),{"as-child":"",class:"h-full w-full rounded-[inherit] focus:outline-none",onScroll:a.onScroll},{default:c(()=>[P(a.$slots,"default")]),_:3},8,["onScroll"]),p(al),p(e(Jl))]),_:3},16,["class"]))}}),os=T({__name:"Switch",props:{class:{},defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},emits:["update:checked"],setup(l,{emit:o}){const t=l,a=o,s=w(()=>{const i=t,{class:r}=i;return Se(i,["class"])}),n=Ne(s,a);return(r,d)=>(u(),k(e(to),me(e(n),{class:e(re)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t.class)}),{default:c(()=>[p(e(eo),{class:D(e(re)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),ns=T({name:"VbenButtonGroup",__name:"button-group",props:{border:{type:Boolean,default:!1},gap:{default:0},size:{default:"middle"}},setup(l){return(o,t)=>(u(),x("div",{class:D(e(re)("vben-button-group rounded-md",`size-${o.size}`,o.gap?"with-gap":"no-gap",o.$attrs.class)),style:ne({gap:o.gap?`${o.gap}px`:"0px"})},[P(o.$slots,"default",{},void 0,!0)],6))}}),ss=Ce(ns,[["__scopeId","data-v-ba11c217"]]),rs={key:0,class:"icon-wrapper"},is=T({__name:"check-button-group",props:he({beforeChange:{},btnClass:{},gap:{default:0},multiple:{type:Boolean,default:!1},options:{},showIcon:{type:Boolean,default:!0},size:{default:"middle"},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:he(["btnClick"],["update:modelValue"]),setup(l,{emit:o}){const t=l,a=o,s=w(()=>Be(fe({},Oo(t,["options","btnClass","size","disabled"])),{class:re(t.btnClass)})),n=y(l,"modelValue"),r=X([]),d=X([]);de(()=>t.multiple,m=>{m?n.value=r.value:n.value=r.value.length>0?r.value[0]:void 0}),de(()=>n.value,m=>{if(Array.isArray(m)){const f=m.filter(g=>g!==void 0);f.length>0?r.value=t.multiple?[...f]:[f[0]]:r.value=[]}else r.value=m===void 0?[]:[m]},{deep:!0,immediate:!0});function i(m){return oe(this,null,function*(){if(t.beforeChange&&Aa(t.beforeChange))try{if(d.value.push(m),(yield t.beforeChange(m,!r.value.includes(m)))===!1)return}finally{d.value.splice(d.value.indexOf(m),1)}t.multiple?(r.value.includes(m)?r.value=r.value.filter(f=>f!==m):r.value.push(m),n.value=r.value):(r.value=[m],n.value=m),a("btnClick",m)})}return(m,f)=>(u(),k(ss,{size:t.size,gap:t.gap,class:"vben-check-button-group"},{default:c(()=>[(u(!0),x(Y,null,le(t.options,(g,b)=>(u(),k(Ue,me({key:b,class:e(re)("border",t.btnClass),disabled:t.disabled||d.value.includes(g.value)||!t.multiple&&d.value.length>0},{ref_for:!0},s.value,{variant:r.value.includes(g.value)?"default":"outline",onClick:h=>i(g.value)}),{default:c(()=>[t.showIcon?(u(),x("div",rs,[d.value.includes(g.value)?(u(),k(e(ao),{key:0,class:"animate-spin"})):r.value.includes(g.value)?(u(),k(e($n),{key:1})):(u(),k(e(Bn),{key:2}))])):H("",!0),P(m.$slots,"option",{label:g.label,value:g.value,data:g},()=>[p(e(lo),{content:g.label},null,8,["content"])],!0)]),_:2},1040,["class","disabled","variant","onClick"]))),128))]),_:3},8,["size","gap"]))}}),ds=Ce(is,[["__scopeId","data-v-e2c423b4"]]),us=l=>{const o=St(),t=St(),a=X(!1),s=()=>{var d;o.value&&(a.value=o.value.scrollTop>=((d=l==null?void 0:l.visibilityHeight)!=null?d:0))},n=()=>{var d;(d=o.value)==null||d.scrollTo({behavior:"smooth",top:0})},r=$t(s,300,!0);return Wo(t,"scroll",r),Fe(()=>{var d;if(t.value=document,o.value=document.documentElement,l.target){if(o.value=(d=document.querySelector(l.target))!=null?d:void 0,!o.value)throw new Error(`target does not exist: ${l.target}`);t.value=o.value}s()}),{handleClick:n,visible:a}},cs=T({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(l){const o=l,t=w(()=>({bottom:`${o.bottom}px`,right:`${o.right}px`})),{handleClick:a,visible:s}=us(o);return(n,r)=>(u(),k(nt,{name:"fade-down"},{default:c(()=>[e(s)?(u(),k(e(Ue),{key:0,style:ne(t.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float z-popup fixed bottom-10 size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(a)},{default:c(()=>[p(e(Sn),{class:"size-4"})]),_:1},8,["style","onClick"])):H("",!0)]),_:1}))}}),ps={class:"flex"},fs=["onClick"],ms={class:"flex-center z-10 h-full"},hs=T({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:o}){const t=o;function a(s,n){!n||s===l.breadcrumbs.length-1||t("select",n)}return(s,n)=>(u(),x("ul",ps,[p(_t,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),x(Y,null,le(s.breadcrumbs,(r,d)=>(u(),x("li",{key:`${r.path}-${r.title}-${d}`},[$("a",{href:"javascript:void 0",onClick:ke(i=>a(d,r.path),["stop"])},[$("span",ms,[s.showIcon?(u(),k(e(Le),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):H("",!0),$("span",{class:D({"text-foreground font-normal":d===s.breadcrumbs.length-1})},M(r.title),3)])],8,fs)]))),128))]),_:1})]))}}),bs=Ce(hs,[["__scopeId","data-v-da1498bb"]]),vs={key:0},gs={class:"flex-center"},ys={class:"flex-center"},ws=T({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(l,{emit:o}){const t=o;function a(s){s&&t("select",s)}return(s,n)=>(u(),k(e(Dn),null,{default:c(()=>[p(e(Kn),null,{default:c(()=>[p(_t,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),x(Y,null,le(s.breadcrumbs,(r,d)=>(u(),k(e(Fn),{key:`${r.path}-${r.title}-${d}`},{default:c(()=>{var i,m;return[(m=(i=r.items)==null?void 0:i.length)!=null&&m?(u(),x("div",vs,[p(e(Wt),null,{default:c(()=>[p(e(Nt),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(u(),k(e(Le),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):H("",!0),V(" "+M(r.title)+" ",1),p(e(At),{class:"size-4"})]),_:2},1024),p(e(Dt),{align:"start"},{default:c(()=>[(u(!0),x(Y,null,le(r.items,f=>(u(),k(e(ut),{key:`sub-${f.path}`,onClick:ke(g=>a(f.path),["stop"])},{default:c(()=>[V(M(f.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):d!==s.breadcrumbs.length-1?(u(),k(e(Rn),{key:1,href:"javascript:void 0",onClick:ke(f=>a(r.path),["stop"])},{default:c(()=>[$("div",gs,[s.showIcon?(u(),k(e(Le),{key:0,class:D([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):H("",!0),V(" "+M(r.title),1)])]),_:2},1032,["onClick"])):(u(),k(e(jn),{key:2},{default:c(()=>[$("div",ys,[s.showIcon?(u(),k(e(Le),{key:0,class:D([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):H("",!0),V(" "+M(r.title),1)])]),_:2},1024)),d<s.breadcrumbs.length-1&&!r.isHome?(u(),k(e(Gn),{key:3})):H("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),xs=T({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:o}){const s=Ne(l,o);return(n,r)=>(u(),x(Y,null,[n.styleType==="normal"?(u(),k(ws,me({key:0},e(s),{class:"vben-breadcrumb"}),null,16)):H("",!0),n.styleType==="background"?(u(),k(bs,me({key:1},e(s),{class:"vben-breadcrumb"}),null,16)):H("",!0)],64))}}),ks=Ce(xs,[["__scopeId","data-v-4cd036dd"]]),Cs=T({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(l,{emit:o}){const s=Ne(l,o);return(n,r)=>(u(),k(e(oo),We(tt(e(s))),{default:c(()=>[P(n.$slots,"default")]),_:3},16))}}),Ss=T({__name:"ContextMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(l,{emit:o}){const t=l,a=o,s=w(()=>{const i=t,{class:r}=i;return Se(i,["class"])}),n=Ne(s,a);return(r,d)=>(u(),k(e(no),null,{default:c(()=>[p(e(so),me(e(n),{class:e(re)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Ms=T({__name:"ContextMenuItem",props:{class:{},inset:{type:Boolean},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},emits:["select"],setup(l,{emit:o}){const t=l,a=o,s=w(()=>{const i=t,{class:r}=i;return Se(i,["class"])}),n=Ne(s,a);return(r,d)=>(u(),k(e(ro),me(e(n),{class:e(re)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"]))}}),_s=T({__name:"ContextMenuSeparator",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l,t=w(()=>{const n=o,{class:a}=n;return Se(n,["class"])});return(a,s)=>(u(),k(e(io),me(t.value,{class:e(re)("bg-border -mx-1 my-1 h-px",o.class)}),null,16,["class"]))}}),$s=T({__name:"ContextMenuShortcut",props:{class:{}},setup(l){const o=l;return(t,a)=>(u(),x("span",{class:D(e(re)("text-muted-foreground ml-auto text-xs tracking-widest",o.class))},[P(t.$slots,"default")],2))}}),Bs=T({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const t=Qe(l);return(a,s)=>(u(),k(e(uo),We(tt(e(t))),{default:c(()=>[P(a.$slots,"default")]),_:3},16))}}),ll=T({__name:"context-menu",props:{dir:{},modal:{type:Boolean},class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function}},emits:["update:open"],setup(l,{emit:o}){const t=l,a=o,s=w(()=>{const h=t,{class:i,contentClass:m,contentProps:f,itemClass:g}=h;return Se(h,["class","contentClass","contentProps","itemClass"])}),n=Ne(s,a),r=w(()=>{var i;return(i=t.menus)==null?void 0:i.call(t,t.handlerData)});function d(i){var m;i.disabled||(m=i==null?void 0:i.handler)==null||m.call(i,t.handlerData)}return(i,m)=>(u(),k(e(Cs),We(tt(e(n))),{default:c(()=>[p(e(Bs),{"as-child":""},{default:c(()=>[P(i.$slots,"default")]),_:3}),p(e(Ss),me({class:i.contentClass},i.contentProps,{class:"side-content z-popup"}),{default:c(()=>[(u(!0),x(Y,null,le(r.value,f=>(u(),x(Y,{key:f.key},[p(e(Ms),{class:D([i.itemClass,"cursor-pointer"]),disabled:f.disabled,inset:f.inset||!f.icon,onClick:g=>d(f)},{default:c(()=>[f.icon?(u(),k(Ee(f.icon),{key:0,class:"mr-2 size-4 text-lg"})):H("",!0),V(" "+M(f.text)+" ",1),f.shortcut?(u(),k(e($s),{key:1},{default:c(()=>[V(M(f.shortcut),1)]),_:2},1024)):H("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),f.separator?(u(),k(e(_s),{key:0})):H("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),Ts=T({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(l){const o=l;function t(a){var s;a.disabled||(s=a==null?void 0:a.handler)==null||s.call(a,o)}return(a,s)=>(u(),k(e(Wt),null,{default:c(()=>[p(e(Nt),{class:"flex h-full items-center gap-1"},{default:c(()=>[P(a.$slots,"default")]),_:3}),p(e(Dt),{align:"start"},{default:c(()=>[p(e(dn),null,{default:c(()=>[(u(!0),x(Y,null,le(a.menus,n=>(u(),x(Y,{key:n.value},[p(e(ut),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>t(n)},{default:c(()=>[n.icon?(u(),k(Ee(n.icon),{key:0,class:"mr-2 size-4"})):H("",!0),V(" "+M(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(u(),k(e(kt),{key:0,class:"bg-border"})):H("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),Vs=T({name:"FullScreen",__name:"full-screen",setup(l){const{isFullscreen:o,toggle:t}=No();return o.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(a,s)=>(u(),k(e(Xe),{onClick:e(t)},{default:c(()=>[e(o)?(u(),k(e(Un),{key:0,class:"text-foreground size-4"})):(u(),k(e(Hn),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),Es={class:"h-full cursor-pointer"},Ls=T({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:o}){const t=l,a=o,s=w(()=>{const f=t,{class:r,contentClass:d,contentProps:i}=f;return Se(f,["class","contentClass","contentProps"])}),n=Ne(s,a);return(r,d)=>(u(),k(e(Yn),We(tt(e(n))),{default:c(()=>[p(e(Qn),{"as-child":"",class:"h-full"},{default:c(()=>[$("div",Es,[P(r.$slots,"trigger")])]),_:3}),p(e(Xn),me({class:r.contentClass},r.contentProps,{class:"side-content z-popup"}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),zs=["href"],Ps={class:"text-foreground truncate text-nowrap font-semibold"},Ma=T({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},fit:{default:"cover"},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(l){return(o,t)=>(u(),x("div",{class:D([o.theme,"flex h-full items-center text-lg"])},[$("a",{class:D([o.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:o.href},[o.src?(u(),k(e(ft),{key:0,alt:o.text,src:o.src,size:o.logoSize,fit:o.fit,class:"relative rounded-none bg-transparent"},null,8,["alt","src","size","fit"])):H("",!0),o.collapsed?H("",!0):P(o.$slots,"text",{key:1},()=>[$("span",Ps,M(o.text),1)])],10,zs)],2))}}),_a=1,Hs=T({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(l,{emit:o}){const t=l,a=o,s=X(!0),n=X(!1),r=X(!1),d=X(!0),i=w(()=>t.shadow&&t.shadowTop),m=w(()=>t.shadow&&t.shadowBottom),f=w(()=>t.shadow&&t.shadowLeft),g=w(()=>t.shadow&&t.shadowRight),b=w(()=>({"both-shadow":!d.value&&!n.value&&f.value&&g.value,"left-shadow":!d.value&&f.value,"right-shadow":!n.value&&g.value}));function h(_){var j,E,B,N,q,ae;const O=_.target,W=(j=O==null?void 0:O.scrollTop)!=null?j:0,U=(E=O==null?void 0:O.scrollLeft)!=null?E:0,R=(B=O==null?void 0:O.clientHeight)!=null?B:0,K=(N=O==null?void 0:O.clientWidth)!=null?N:0,A=(q=O==null?void 0:O.scrollHeight)!=null?q:0,z=(ae=O==null?void 0:O.scrollWidth)!=null?ae:0;s.value=W<=0,d.value=U<=0,r.value=Math.abs(W)+R>=A-_a,n.value=Math.abs(U)+K>=z-_a,a("scrollAt",{bottom:r.value,left:d.value,right:n.value,top:s.value})}return(_,O)=>(u(),k(e(ls),{class:D([[e(re)(t.class),b.value],"vben-scrollbar relative"]),"on-scroll":h},{default:c(()=>[i.value?(u(),x("div",{key:0,class:D([{"opacity-100":!s.value,"border-border border-t":_.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):H("",!0),P(_.$slots,"default",{},void 0,!0),m.value?(u(),x("div",{key:1,class:D([{"opacity-100":!s.value&&!r.value,"border-border border-b":_.shadowBorder&&!s.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):H("",!0),_.horizontal?(u(),k(e(al),{key:2,class:D(_.scrollBarClass),orientation:"horizontal"},null,8,["class"])):H("",!0)]),_:3},8,["class"]))}}),ht=Ce(Hs,[["__scopeId","data-v-c94474ed"]]),Us={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},Is=T({__name:"tabs-indicator",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const o=l,t=w(()=>{const r=o,{class:s}=r;return Se(r,["class"])}),a=Qe(t);return(s,n)=>(u(),k(e(co),me(e(a),{class:e(re)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",o.class)}),{default:c(()=>[$("div",Us,[P(s.$slots,"default")])]),_:3},16,["class"]))}}),As=T({__name:"segmented",props:he({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=l,t=y(l,"modelValue"),a=w(()=>{var r;return o.defaultValue||((r=o.tabs[0])==null?void 0:r.value)}),s=w(()=>({"grid-template-columns":`repeat(${o.tabs.length}, minmax(0, 1fr))`})),n=w(()=>({width:`${(100/o.tabs.length).toFixed(0)}%`}));return(r,d)=>(u(),k(e(gn),{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=i=>t.value=i),"default-value":a.value},{default:c(()=>[p(e(bn),{style:ne(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[p(Is,{style:ne(n.value)},null,8,["style"]),(u(!0),x(Y,null,le(r.tabs,i=>(u(),k(e(po),{key:i.value,value:i.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[V(M(i.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(u(!0),x(Y,null,le(r.tabs,i=>(u(),k(e(vn),{key:i.value,value:i.value},{default:c(()=>[P(r.$slots,i.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}});function ol(){const{contentIsMaximize:l}=Ze();function o(){const t=l.value;Re({header:{hidden:!t},sidebar:{hidden:!t}})}return{contentIsMaximize:l,toggleMaximize:o}}function Os(l,o=500){const t=[],a=X(!1),s=X();(Array.isArray(l)?l:[l]).forEach(f=>{const g=w(()=>{const h=e(f);return h instanceof Element?h:h==null?void 0:h.$el}),b=Do(g);t.push(b)});const r=w(()=>t.every(f=>!f.value));function d(f){s.value&&clearTimeout(s.value),s.value=setTimeout(()=>{a.value=f,s.value=void 0},Aa(o)?o():o)}const i=de(r,f=>{d(!f)},{immediate:!0}),m={enable(){i.resume()},disable(){i.pause()}};return bt(()=>{s.value&&clearTimeout(s.value)}),[a,m]}function nl(){const l=st(),o=gt();function t(){return oe(this,null,function*(){yield o.refresh(l)})}return{refresh:t}}const Ws=T({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(l){const o=l,t=Ge(),a=st(),s=w(()=>{const r=t.matched,d=[];for(const i of r){const{meta:m,path:f}=i,{hideChildrenInMenu:g,hideInBreadcrumb:b,icon:h,name:_,title:O}=m||{};b||g||!f||d.push({icon:h,path:f||t.path,title:O?v(O||_):""})}return o.showHome&&d.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),o.hideWhenOnlyOne&&d.length===1?[]:d});function n(r){a.push(r)}return(r,d)=>(u(),k(e(ks),{breadcrumbs:s.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),Ns=T({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(l){const o=l;let t=!1;const a=X(""),s=X(""),n=X(),[r,d]=mt({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=a.value,window.location.reload()}});function i(){return oe(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const _=yield fetch(o.checkUpdateUrl,{cache:"no-cache",method:"HEAD",redirect:"manual"});return _.headers.get("etag")||_.headers.get("last-modified")}catch(_){return console.error("Failed to fetch version tag"),null}})}function m(){return oe(this,null,function*(){const _=yield i();if(_){if(!s.value){s.value=_;return}s.value!==_&&_&&(clearInterval(n.value),f(_))}})}function f(_){a.value=_,d.open()}function g(){o.checkUpdatesInterval<=0||(n.value=setInterval(m,o.checkUpdatesInterval*60*1e3))}function b(){document.hidden?h():t||(t=!0,m().finally(()=>{t=!1,g()}))}function h(){clearInterval(n.value)}return Fe(()=>{g(),document.addEventListener("visibilitychange",b)}),bt(()=>{h(),document.removeEventListener("visibilitychange",b)}),(_,O)=>(u(),k(e(r),{"cancel-text":e(v)("common.cancel"),"confirm-text":e(v)("common.refresh"),"fullscreen-button":!1,title:e(v)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[V(M(e(v)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),Ds={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},Fs={key:0,class:"text-muted-foreground text-center"},Rs={class:"mb-10 mt-6 text-xs"},Ks={class:"text-foreground text-sm font-medium"},js={key:1,class:"text-muted-foreground text-center"},Gs={class:"my-10 text-xs"},qs={class:"w-full"},Ys={key:0,class:"text-muted-foreground mb-2 text-xs"},Xs=["data-index","data-search-item"],Qs={class:"flex-1"},Zs=["onClick"],Js=T({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(l,{emit:o}){const t=l,a=o,s=st(),n=Fo(`__search-history-${location.hostname}__`,[]),r=X(-1),d=St([]),i=X([]),m=$t(f,200);function f(z){if(z=z.trim(),!z){i.value=[];return}const j=A(z),E=[];Ko(d.value,B=>{var N;j.test((N=B.name)==null?void 0:N.toLowerCase())&&E.push(B)}),i.value=E,E.length>0&&(r.value=0),r.value=0}function g(){const z=document.querySelector(`[data-search-item="${r.value}"]`);z&&z.scrollIntoView({block:"nearest"})}function b(){return oe(this,null,function*(){if(i.value.length===0)return;const z=i.value,j=r.value;if(z.length===0||j<0)return;const E=z[j];E&&(n.value.push(E),O(),yield Ke(),Ht(E.path)?window.open(E.path,"_blank"):s.push({path:E.path,replace:!0}))})}function h(){i.value.length!==0&&(r.value--,r.value<0&&(r.value=i.value.length-1),g())}function _(){i.value.length!==0&&(r.value++,r.value>i.value.length-1&&(r.value=0),g())}function O(){i.value=[],a("close")}function W(z){var E;const j=(E=z.target)==null?void 0:E.dataset.index;r.value=Number(j)}function U(z){t.keyword?i.value.splice(z,1):n.value.splice(z,1),r.value=Math.max(r.value-1,0),g()}const R=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function K(z){return R.has(z)?`\\${z}`:z}function A(z){const j=[...z].map(E=>K(E)).join(".*");return new RegExp(`.*${j}.*`)}return de(()=>t.keyword,z=>{z?m(z):i.value=[...n.value]}),Fe(()=>{d.value=Oa(t.menus,z=>Be(fe({},z),{name:v(z==null?void 0:z.name)})),n.value.length>0&&(i.value=n.value),wt("Enter",b),wt("ArrowUp",h),wt("ArrowDown",_),wt("Escape",O)}),(z,j)=>(u(),k(e(ht),null,{default:c(()=>[$("div",Ds,[z.keyword&&i.value.length===0?(u(),x("div",Fs,[p(e(An),{class:"mx-auto mt-4 size-12"}),$("p",Rs,[V(M(e(v)("ui.widgets.search.noResults"))+" ",1),$("span",Ks,' "'+M(z.keyword)+'" ',1)])])):H("",!0),!z.keyword&&i.value.length===0?(u(),x("div",js,[$("p",Gs,M(e(v)("ui.widgets.search.noRecent")),1)])):H("",!0),Me($("ul",qs,[e(n).length>0&&!z.keyword?(u(),x("li",Ys,M(e(v)("ui.widgets.search.recent")),1)):H("",!0),(u(!0),x(Y,null,le(e(Ro)(i.value,"path"),(E,B)=>(u(),x("li",{key:E.path,class:D([r.value===B?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":B,"data-search-item":B,onClick:b,onMouseenter:W},[p(e(Le),{icon:E.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),$("span",Qs,M(E.name),1),$("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:ke(N=>U(B),["stop"])},[p(e(Tt),{class:"size-4"})],8,Zs)],42,Xs))),128))],512),[[Te,i.value.length>0]])])]),_:1}))}}),er={class:"flex items-center"},tr=["placeholder"],ar={class:"flex w-full justify-start text-xs"},lr={class:"mr-2 flex items-center"},or={class:"mr-2 flex items-center"},nr={class:"flex items-center"},sr={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},rr={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},ir={key:1},dr=T({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(l){const o=l,t=X(""),a=X(),[s,n]=mt({onCancel(){n.close()},onOpenChange(h){h||(t.value="")}}),r=n.useStore(h=>h.isOpen);function d(){n.close(),t.value=""}const i=Wa(),m=pt()?i["ctrl+k"]:i["cmd+k"];Mt(m,()=>{o.enableShortcutKey&&n.open()}),Mt(r,()=>{Ke(()=>{var h;(h=a.value)==null||h.focus()})});const f=h=>{var _;((_=h.key)==null?void 0:_.toLowerCase())==="k"&&(h.metaKey||h.ctrlKey)&&h.preventDefault()},g=()=>{o.enableShortcutKey?window.addEventListener("keydown",f):window.removeEventListener("keydown",f)},b=()=>{r.value?n.close():n.open()};return de(()=>o.enableShortcutKey,g),Fe(()=>{g(),bt(()=>{window.removeEventListener("keydown",f)})}),(h,_)=>(u(),x("div",null,[p(e(s),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[$("div",er,[p(e(Ca),{class:"text-muted-foreground mr-2 size-4"}),Me($("input",{ref_key:"searchInputRef",ref:a,"onUpdate:modelValue":_[0]||(_[0]=O=>t.value=O),placeholder:e(v)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,tr),[[fo,t.value]])])]),footer:c(()=>[$("div",ar,[$("div",lr,[p(e(Vn),{class:"mr-1 size-3"}),V(" "+M(e(v)("ui.widgets.search.select")),1)]),$("div",or,[p(e(Mn),{class:"mr-1 size-3"}),p(e(wn),{class:"mr-1 size-3"}),V(" "+M(e(v)("ui.widgets.search.navigate")),1)]),$("div",nr,[p(e(yn),{class:"mr-1 size-3"}),V(" "+M(e(v)("ui.widgets.search.close")),1)])])]),default:c(()=>[p(Js,{keyword:t.value,menus:h.menus,onClose:d},null,8,["keyword","menus"])]),_:1}),$("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:_[1]||(_[1]=O=>b())},[p(e(Ca),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),$("span",sr,M(e(v)("ui.widgets.search.title")),1),h.enableShortcutKey?(u(),x("span",rr,[V(M(e(pt)()?"Ctrl":"⌘")+" ",1),_[2]||(_[2]=$("kbd",null,"K",-1))])):(u(),x("span",ir))])]))}}),ur={class:"bg-background fixed z-[2000] size-full"},cr={class:"size-full"},pr={class:"flex h-full justify-center px-[10%]"},fr={class:"bg-accent flex-center relative mb-14 mr-20 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},mr={class:"absolute left-4 top-4 text-xl font-semibold"},hr={class:"bg-accent flex-center mb-14 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},br=["onKeydown"],vr={class:"flex-col-center mb-10 w-[300px]"},gr={class:"enter-x mb-2 w-full items-center"},yr={class:"enter-y absolute bottom-5 w-full text-center xl:text-xl 2xl:text-3xl"},wr={key:0,class:"enter-x mb-2 text-3xl"},xr={class:"text-lg"},kr={class:"text-3xl"},Au=T({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(l){const{locale:o}=Ea(),t=Je(),a=jo(),s=xt(a,"A"),n=xt(a,"HH"),r=xt(a,"mm"),d=xt(a,"YYYY-MM-DD dddd",{locales:o.value}),i=X(!1),{lockScreenPassword:m}=La(t),[f,{form:g,validate:b}]=za(vt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:v("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:v("authentication.password"),rules:Pa().min(1,{message:v("authentication.passwordTip")})}]),showDefaultActions:!1})),h=w(()=>{var W;return(m==null?void 0:m.value)===((W=g==null?void 0:g.values)==null?void 0:W.password)});function _(){return oe(this,null,function*(){const{valid:W}=yield b();W&&(h.value?t.unlockScreen():g.setFieldError("password",v("authentication.passwordErrorTip")))})}function O(){i.value=!i.value}return mo(),(W,U)=>(u(),x("div",ur,[p(nt,{name:"slide-left"},{default:c(()=>[Me($("div",cr,[$("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group my-4 cursor-pointer text-xl font-semibold",onClick:O},[p(e(Za),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),$("span",null,M(e(v)("ui.widgets.lockScreen.unlock")),1)]),$("div",pr,[$("div",fr,[$("span",mr,M(e(s)),1),V(" "+M(e(n)),1)]),$("div",hr,M(e(r)),1)])],512),[[Te,!i.value]])]),_:1}),p(nt,{name:"slide-right"},{default:c(()=>[i.value?(u(),x("div",{key:0,class:"flex-center size-full",onKeydown:Ha(ke(_,["prevent"]),["enter"])},[$("div",vr,[p(e(ft),{src:W.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),$("div",gr,[p(e(f))]),p(e(Ue),{class:"enter-x w-full",onClick:_},{default:c(()=>[V(M(e(v)("ui.widgets.lockScreen.entry")),1)]),_:1}),p(e(Ue),{class:"enter-x my-2 w-full",variant:"ghost",onClick:U[0]||(U[0]=R=>W.$emit("toLogin"))},{default:c(()=>[V(M(e(v)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),p(e(Ue),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:O},{default:c(()=>[V(M(e(v)("common.back")),1)]),_:1})])],40,br)):H("",!0)]),_:1}),$("div",yr,[i.value?(u(),x("div",wr,[V(M(e(n))+":"+M(e(r))+" ",1),$("span",xr,M(e(s)),1)])):H("",!0),$("div",kr,M(e(d)),1)])]))}}),Cr=["onKeydown"],Sr={class:"w-full"},Mr={class:"ml-2 flex w-full flex-col items-center"},_r={class:"text-foreground my-6 flex items-center font-medium"},$r=T({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(l,{emit:o}){const t=o,[a,{resetForm:s,validate:n,getValues:r}]=za(vt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:v("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:v("authentication.password"),rules:Pa().min(1,{message:v("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[d]=mt({onConfirm(){i()},onOpenChange(m){m&&s()}});function i(){return oe(this,null,function*(){const{valid:m}=yield n(),f=yield r();m&&t("submit",f==null?void 0:f.lockScreenPassword)})}return(m,f)=>(u(),k(e(d),{footer:!1,"fullscreen-button":!1,title:e(v)("ui.widgets.lockScreen.title")},{default:c(()=>[$("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:Ha(ke(i,["prevent"]),["enter"])},[$("div",Sr,[$("div",Mr,[p(e(ft),{src:m.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),$("div",_r,M(m.text),1)]),p(e(a)),p(e(Ue),{class:"mt-1 w-full",onClick:i},{default:c(()=>[V(M(e(v)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,Cr)]),_:1},8,["title"]))}}),Br={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Tr={class:"relative"},Vr={class:"flex items-center justify-between p-4 py-3"},Er={class:"text-foreground"},Lr={class:"!flex max-h-[360px] w-full flex-col"},zr=["onClick"],Pr={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},Hr={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},Ur=["src"],Ir={class:"flex flex-col gap-1 leading-none"},Ar={class:"font-semibold"},Or={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},Wr={class:"text-muted-foreground line-clamp-2 text-xs"},Nr={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},Dr={class:"border-border flex items-center justify-between border-t px-4 py-3"},Fr=T({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(l,{emit:o}){const t=o,[a,s]=Go();function n(){a.value=!1}function r(){t("viewAll"),n()}function d(){t("makeAll")}function i(){t("clear")}function m(f){t("read",f)}return(f,g)=>(u(),k(e(ho),{open:e(a),"onUpdate:open":g[1]||(g[1]=b=>Ot(a)?a.value=b:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[$("div",{class:"flex-center mr-2 h-full",onClick:g[0]||(g[0]=ke(b=>e(s)(),["stop"]))},[p(e(Xe),{class:"bell-button text-foreground relative"},{default:c(()=>[f.dot?(u(),x("span",Br)):H("",!0),p(e(_n),{class:"size-4"})]),_:1})])]),default:c(()=>[$("div",Tr,[$("div",Vr,[$("div",Er,M(e(v)("ui.widgets.notifications")),1),p(e(Xe),{disabled:f.notifications.length<=0,tooltip:e(v)("ui.widgets.markAllAsRead"),onClick:d},{default:c(()=>[p(e(Pn),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),f.notifications.length>0?(u(),k(e(ht),{key:0},{default:c(()=>[$("ul",Lr,[(u(!0),x(Y,null,le(f.notifications,b=>(u(),x("li",{key:b.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:h=>m(b)},[b.isRead?H("",!0):(u(),x("span",Pr)),$("span",Hr,[$("img",{src:b.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,Ur)]),$("div",Ir,[$("p",Ar,M(b.title),1),$("p",Or,M(b.message),1),$("p",Wr,M(b.date),1)])],8,zr))),128))])]),_:1})):(u(),x("div",Nr,M(e(v)("common.noData")),1)),$("div",Dr,[p(e(Ue),{disabled:f.notifications.length<=0,size:"sm",variant:"ghost",onClick:i},{default:c(()=>[V(M(e(v)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),p(e(Ue),{size:"sm",onClick:r},{default:c(()=>[V(M(e(v)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),Ou=Ce(Fr,[["__scopeId","data-v-fb088fb7"]]),Rr={class:"flex flex-col py-4"},Kr={class:"mb-3 font-semibold leading-none tracking-tight"},xe=T({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(l){return(o,t)=>(u(),x("div",Rr,[$("h3",Kr,M(o.title),1),P(o.$slots,"default")]))}}),jr={class:"flex items-center text-sm"},Gr={key:0,class:"ml-auto mr-2 text-xs opacity-60"},Q=T({name:"PreferenceSwitchItem",__name:"switch-item",props:he({disabled:{type:Boolean,default:!1},tip:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t=Ie();function a(){o.value=!o.value}return(s,n)=>(u(),x("div",{class:D([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:a},[$("span",jr,[P(s.$slots,"default"),e(t).tip||s.tip?(u(),k(e(et),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(s.$slots,"tip",{},()=>[s.tip?(u(!0),x(Y,{key:0},le(s.tip.split(`
`),(r,d)=>(u(),x("p",{key:d},M(r),1))),128)):H("",!0)])]),_:3})):H("",!0)]),s.$slots.shortcut?(u(),x("span",Gr,[P(s.$slots,"shortcut")])):H("",!0),p(e(os),{checked:o.value,"onUpdate:checked":n[0]||(n[0]=r=>o.value=r),onClick:n[1]||(n[1]=ke(()=>{},["stop"]))},null,8,["checked"])],2))}}),qr={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},Yr=["onClick"],Xr=T({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(l){const o=y(l,"transitionProgress"),t=y(l,"transitionName"),a=y(l,"transitionEnable"),s=y(l,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function r(d){t.value=d}return(d,i)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":i[0]||(i[0]=m=>o.value=m)},{default:c(()=>[V(M(e(v)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":i[1]||(i[1]=m=>s.value=m)},{default:c(()=>[V(M(e(v)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":i[2]||(i[2]=m=>a.value=m)},{default:c(()=>[V(M(e(v)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),a.value?(u(),x("div",qr,[(u(),x(Y,null,le(n,m=>$("div",{key:m,class:D([{"outline-box-active":t.value===m},"outline-box p-2"]),onClick:f=>r(m)},[$("div",{class:D([`${m}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,Yr)),64))])):H("",!0)],64))}}),Qr={class:"flex items-center text-sm"},Et=T({name:"PreferenceSelectItem",__name:"select-item",props:he({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t=Ie();return(a,s)=>(u(),x("div",{class:D([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",Qr,[P(a.$slots,"default"),e(t).tip?(u(),k(e(et),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(a.$slots,"tip")]),_:3})):H("",!0)]),p(e(bo),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},{default:c(()=>[p(e(vo),{class:"h-8 w-[165px]"},{default:c(()=>[p(e(go),{placeholder:a.placeholder},null,8,["placeholder"])]),_:1}),p(e(yo),null,{default:c(()=>[(u(!0),x(Y,null,le(a.items,n=>(u(),k(e(wo),{key:n.value,value:n.value},{default:c(()=>[V(M(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),Zr=T({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(l){const o=y(l,"appLocale"),t=y(l,"appDynamicTitle"),a=y(l,"appWatermark"),s=y(l,"appEnableCheckUpdates");return(n,r)=>(u(),x(Y,null,[p(Et,{modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=d=>o.value=d),items:e(xo)},{default:c(()=>[V(M(e(v)("preferences.language")),1)]),_:1},8,["modelValue","items"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=d=>t.value=d)},{default:c(()=>[V(M(e(v)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=d=>a.value=d)},{default:c(()=>[V(M(e(v)("preferences.watermark")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=d=>s.value=d)},{default:c(()=>[V(M(e(v)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),Jr={class:"text-sm"},Kt=T({name:"PreferenceToggleItem",__name:"toggle-item",props:he({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=y(l,"modelValue");return(t,a)=>(u(),x("div",{class:D([{"pointer-events-none opacity-50":t.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[$("span",Jr,[P(t.$slots,"default")]),p(e(Ga),{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=s=>o.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(!0),x(Y,null,le(t.items,s=>(u(),k(e(qa),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[V(M(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),ei=T({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:he({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(l){const o=l,t=y(l,"breadcrumbEnable"),a=y(l,"breadcrumbShowIcon"),s=y(l,"breadcrumbStyleType"),n=y(l,"breadcrumbShowHome"),r=y(l,"breadcrumbHideOnlyOne"),d=[{label:v("preferences.normal"),value:"normal"},{label:v("preferences.breadcrumb.background"),value:"background"}],i=w(()=>!t.value||o.disabled);return(m,f)=>(u(),x(Y,null,[p(Q,{modelValue:t.value,"onUpdate:modelValue":f[0]||(f[0]=g=>t.value=g),disabled:m.disabled},{default:c(()=>[V(M(e(v)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:r.value,"onUpdate:modelValue":f[1]||(f[1]=g=>r.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":f[2]||(f[2]=g=>a.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":f[3]||(f[3]=g=>n.value=g),disabled:i.value||!a.value},{default:c(()=>[V(M(e(v)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),p(Kt,{modelValue:s.value,"onUpdate:modelValue":f[4]||(f[4]=g=>s.value=g),disabled:i.value,items:d},{default:c(()=>[V(M(e(v)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ti={},ai={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function li(l,o){return u(),x("svg",ai,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const sl=Ce(ti,[["render",li]]),oi={},ni={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function si(l,o){return u(),x("svg",ni,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const ri=Ce(oi,[["render",si]]),ii={},di={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function ui(l,o){return u(),x("svg",di,o[0]||(o[0]=[$("g",null,[$("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),$("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),$("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),$("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const ci=Ce(ii,[["render",ui]]),pi={},fi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function mi(l,o){return u(),x("svg",fi,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="35.14924" y="4.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="47.25735" y="4.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="59.23033" y="4.07319"></rect></g>',1)]))}const hi=Ce(pi,[["render",mi]]),bi={},vi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function gi(l,o){return u(),x("svg",vi,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#b2b2b2" height="1.689" rx="1.395" stroke="null" width="6.52486" x="10.08168" y="3.50832"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="2.89362"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="2.89362"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="5.13843" rx="2" stroke="null" width="5.78397" x="1.5327" y="1.081"></rect><rect id="svg_5" fill="hsl(var(--primary))" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path></g>',1)]))}const yi=Ce(bi,[["render",gi]]),wi={},xi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function ki(l,o){return u(),x("svg",xi,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const Ci=Ce(wi,[["render",ki]]),Si={},Mi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function _i(l,o){return u(),x("svg",Mi,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const $i=Ce(Si,[["render",_i]]),Bi={},Ti={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Vi(l,o){return u(),x("svg",Ti,o[0]||(o[0]=[at('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const Ei=Ce(Bi,[["render",Vi]]),Li=sl,zi={class:"flex w-full gap-5"},Pi=["onClick"],Hi={class:"text-muted-foreground mt-2 text-center text-xs"},Ui=T({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t={compact:ri,wide:Li},a=w(()=>[{name:v("preferences.wide"),type:"wide"},{name:v("preferences.compact"),type:"compact"}]);function s(n){return n===o.value?["outline-box-active"]:[]}return(n,r)=>(u(),x("div",zi,[(u(!0),x(Y,null,le(a.value,d=>(u(),x("div",{key:d.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:i=>o.value=d.type},[$("div",{class:D([s(d.type),"outline-box flex-center"])},[(u(),k(Ee(t[d.type])))],2),$("div",Hi,M(d.name),1)],8,Pi))),128))]))}}),Ii={class:"flex items-center text-sm"},it=T({name:"PreferenceSelectItem",__name:"input-item",props:he({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t=Ie();return(a,s)=>(u(),x("div",{class:D([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",Ii,[P(a.$slots,"default"),e(t).tip?(u(),k(e(et),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(a.$slots,"tip")]),_:3})):H("",!0)]),p(e(ko),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),Ai=T({__name:"copyright",props:he({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(l){const o=l,t=y(l,"copyrightEnable"),a=y(l,"copyrightDate"),s=y(l,"copyrightIcp"),n=y(l,"copyrightIcpLink"),r=y(l,"copyrightCompanyName"),d=y(l,"copyrightCompanySiteLink"),i=w(()=>o.disabled||!t.value);return(m,f)=>(u(),x(Y,null,[p(Q,{modelValue:t.value,"onUpdate:modelValue":f[0]||(f[0]=g=>t.value=g),disabled:m.disabled},{default:c(()=>[V(M(e(v)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),p(it,{modelValue:r.value,"onUpdate:modelValue":f[1]||(f[1]=g=>r.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),p(it,{modelValue:d.value,"onUpdate:modelValue":f[2]||(f[2]=g=>d.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),p(it,{modelValue:a.value,"onUpdate:modelValue":f[3]||(f[3]=g=>a.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),p(it,{modelValue:s.value,"onUpdate:modelValue":f[4]||(f[4]=g=>s.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),p(it,{modelValue:n.value,"onUpdate:modelValue":f[5]||(f[5]=g=>n.value=g),disabled:i.value},{default:c(()=>[V(M(e(v)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Oi=T({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(l){const o=y(l,"footerEnable"),t=y(l,"footerFixed");return(a,s)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},{default:c(()=>[V(M(e(v)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Wi=T({__name:"header",props:he({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{}}),emits:["update:headerEnable","update:headerMode","update:headerMenuAlign"],setup(l){const o=y(l,"headerEnable"),t=y(l,"headerMode"),a=y(l,"headerMenuAlign"),s=[{label:v("preferences.header.modeStatic"),value:"static"},{label:v("preferences.header.modeFixed"),value:"fixed"},{label:v("preferences.header.modeAuto"),value:"auto"},{label:v("preferences.header.modeAutoScroll"),value:"auto-scroll"}],n=[{label:v("preferences.header.menuAlignStart"),value:"start"},{label:v("preferences.header.menuAlignCenter"),value:"center"},{label:v("preferences.header.menuAlignEnd"),value:"end"}];return(r,d)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":d[0]||(d[0]=i=>o.value=i),disabled:r.disabled},{default:c(()=>[V(M(e(v)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),p(Et,{modelValue:t.value,"onUpdate:modelValue":d[1]||(d[1]=i=>t.value=i),disabled:!o.value,items:s},{default:c(()=>[V(M(e(v)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"]),p(Kt,{modelValue:a.value,"onUpdate:modelValue":d[2]||(d[2]=i=>a.value=i),disabled:!o.value,items:n},{default:c(()=>[V(M(e(v)("preferences.header.menuAlign")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Ni={class:"flex w-full flex-wrap gap-5"},Di=["onClick"],Fi={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},Ri=T({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t={"full-content":ci,"header-nav":sl,"mixed-nav":Ci,"sidebar-mixed-nav":$i,"sidebar-nav":Ei,"header-mixed-nav":hi,"header-sidebar-nav":yi},a=w(()=>[{name:v("preferences.vertical"),tip:v("preferences.verticalTip"),type:"sidebar-nav"},{name:v("preferences.twoColumn"),tip:v("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:v("preferences.horizontal"),tip:v("preferences.horizontalTip"),type:"header-nav"},{name:v("preferences.headerSidebarNav"),tip:v("preferences.headerSidebarNavTip"),type:"header-sidebar-nav"},{name:v("preferences.mixedMenu"),tip:v("preferences.mixedMenuTip"),type:"mixed-nav"},{name:v("preferences.headerTwoColumn"),tip:v("preferences.headerTwoColumnTip"),type:"header-mixed-nav"},{name:v("preferences.fullContent"),tip:v("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===o.value?["outline-box-active"]:[]}return(n,r)=>(u(),x("div",Ni,[(u(!0),x(Y,null,le(a.value,d=>(u(),x("div",{key:d.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:i=>o.value=d.type},[$("div",{class:D([s(d.type),"outline-box flex-center"])},[(u(),k(Ee(t[d.type])))],2),$("div",Fi,[V(M(d.name)+" ",1),d.tip?(u(),k(e(et),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[V(" "+M(d.tip),1)]),_:2},1024)):H("",!0)])],8,Di))),128))]))}}),Ki=T({name:"PreferenceNavigationConfig",__name:"navigation",props:he({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(l){const o=y(l,"navigationStyleType"),t=y(l,"navigationSplit"),a=y(l,"navigationAccordion"),s=[{label:v("preferences.rounded"),value:"rounded"},{label:v("preferences.plain"),value:"plain"}];return(n,r)=>(u(),x(Y,null,[p(Kt,{modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=d=>o.value=d),disabled:n.disabled,items:s},{default:c(()=>[V(M(e(v)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=d=>t.value=d),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[V(M(e(v)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[V(M(e(v)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=d=>a.value=d),disabled:n.disabled},{default:c(()=>[V(M(e(v)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ji={class:"flex items-center text-sm"},Gi=T({name:"PreferenceCheckboxItem",__name:"checkbox-item",props:he({disabled:{type:Boolean,default:!1},items:{default:()=>[]},multiple:{type:Boolean,default:!1},onBtnClick:{type:Function,default:()=>{}},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t=Ie();return(a,s)=>(u(),x("div",{class:D([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",ji,[P(a.$slots,"default"),e(t).tip?(u(),k(e(et),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(a.$slots,"tip")]),_:3})):H("",!0)]),p(e(ds),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n),class:"h-8 w-[165px]",options:a.items,disabled:a.disabled,multiple:a.multiple,onBtnClick:a.onBtnClick},null,8,["modelValue","options","disabled","multiple","onBtnClick"])],2))}}),qi={class:"flex items-center text-sm"},rl=T({name:"PreferenceSelectItem",__name:"number-field-item",props:he({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""},tip:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=y(l,"modelValue"),t=Ie();return(a,s)=>(u(),x("div",{class:D([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",qi,[P(a.$slots,"default"),e(t).tip||a.tip?(u(),k(e(et),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(a.$slots,"tip",{},()=>[a.tip?(u(!0),x(Y,{key:0},le(a.tip.split(`
`),(n,r)=>(u(),x("p",{key:r},M(n),1))),128)):H("",!0)])]),_:3})):H("",!0)]),p(e(Zn),me({modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},a.$attrs,{class:"w-[165px]"}),{default:c(()=>[p(e(Jn),null,{default:c(()=>[p(e(es)),p(e(as)),p(e(ts))]),_:1})]),_:1},16,["modelValue"])],2))}}),Yi=T({__name:"sidebar",props:he({currentLayout:{},disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarButtons:{default:[]},sidebarButtonsModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarCollapsed","update:sidebarExpandOnHover","update:sidebarButtons","update:sidebarCollapsedButton","update:sidebarFixedButton"],setup(l){const o=y(l,"sidebarEnable"),t=y(l,"sidebarWidth"),a=y(l,"sidebarCollapsedShowTitle"),s=y(l,"sidebarAutoActivateChild"),n=y(l,"sidebarCollapsed"),r=y(l,"sidebarExpandOnHover"),d=y(l,"sidebarButtons"),i=y(l,"sidebarCollapsedButton"),m=y(l,"sidebarFixedButton");Fe(()=>{i.value&&!d.value.includes("collapsed")&&d.value.push("collapsed"),m.value&&!d.value.includes("fixed")&&d.value.push("fixed")});const f=()=>{i.value=!!d.value.includes("collapsed"),m.value=!!d.value.includes("fixed")};return(g,b)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":b[0]||(b[0]=h=>o.value=h),disabled:g.disabled},{default:c(()=>[V(M(e(v)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":b[1]||(b[1]=h=>n.value=h),disabled:!o.value||g.disabled},{default:c(()=>[V(M(e(v)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:r.value,"onUpdate:modelValue":b[2]||(b[2]=h=>r.value=h),disabled:!o.value||g.disabled||!n.value,tip:e(v)("preferences.sidebar.expandOnHoverTip")},{default:c(()=>[V(M(e(v)("preferences.sidebar.expandOnHover")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":b[3]||(b[3]=h=>a.value=h),disabled:!o.value||g.disabled||!n.value},{default:c(()=>[V(M(e(v)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":b[4]||(b[4]=h=>s.value=h),disabled:!o.value||!["sidebar-mixed-nav","mixed-nav","header-mixed-nav"].includes(g.currentLayout)||g.disabled,tip:e(v)("preferences.sidebar.autoActivateChildTip")},{default:c(()=>[V(M(e(v)("preferences.sidebar.autoActivateChild")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Gi,{items:[{label:e(v)("preferences.sidebar.buttonCollapsed"),value:"collapsed"},{label:e(v)("preferences.sidebar.buttonFixed"),value:"fixed"}],multiple:"",modelValue:d.value,"onUpdate:modelValue":b[5]||(b[5]=h=>d.value=h),"on-btn-click":f},{default:c(()=>[V(M(e(v)("preferences.sidebar.buttons")),1)]),_:1},8,["items","modelValue"]),p(rl,{modelValue:t.value,"onUpdate:modelValue":b[6]||(b[6]=h=>t.value=h),disabled:!o.value||g.disabled,max:320,min:160,step:10},{default:c(()=>[V(M(e(v)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Xi=T({name:"PreferenceTabsConfig",__name:"tabbar",props:he({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarMaxCount","update:tabbarMiddleClickToClose"],setup(l){const o=y(l,"tabbarEnable"),t=y(l,"tabbarShowIcon"),a=y(l,"tabbarPersist"),s=y(l,"tabbarDraggable"),n=y(l,"tabbarWheelable"),r=y(l,"tabbarStyleType"),d=y(l,"tabbarShowMore"),i=y(l,"tabbarShowMaximize"),m=y(l,"tabbarMaxCount"),f=y(l,"tabbarMiddleClickToClose"),g=w(()=>[{label:v("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:v("preferences.tabbar.styleType.plain"),value:"plain"},{label:v("preferences.tabbar.styleType.card"),value:"card"},{label:v("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(b,h)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":h[0]||(h[0]=_=>o.value=_),disabled:b.disabled},{default:c(()=>[V(M(e(v)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":h[1]||(h[1]=_=>a.value=_),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),p(rl,{modelValue:m.value,"onUpdate:modelValue":h[2]||(h[2]=_=>m.value=_),disabled:!o.value,max:30,min:0,step:5,tip:e(v)("preferences.tabbar.maxCountTip")},{default:c(()=>[V(M(e(v)("preferences.tabbar.maxCount")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":h[3]||(h[3]=_=>s.value=_),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":h[4]||(h[4]=_=>n.value=_),disabled:!o.value,tip:e(v)("preferences.tabbar.wheelableTip")},{default:c(()=>[V(M(e(v)("preferences.tabbar.wheelable")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Q,{modelValue:f.value,"onUpdate:modelValue":h[5]||(h[5]=_=>f.value=_),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.middleClickClose")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":h[6]||(h[6]=_=>t.value=_),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:d.value,"onUpdate:modelValue":h[7]||(h[7]=_=>d.value=_),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:i.value,"onUpdate:modelValue":h[8]||(h[8]=_=>i.value=_),disabled:!o.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),p(Et,{modelValue:r.value,"onUpdate:modelValue":h[9]||(h[9]=_=>r.value=_),items:g.value},{default:c(()=>[V(M(e(v)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),Qi=T({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(l){const o=y(l,"widgetGlobalSearch"),t=y(l,"widgetFullscreen"),a=y(l,"widgetLanguageToggle"),s=y(l,"widgetNotification"),n=y(l,"widgetThemeToggle"),r=y(l,"widgetSidebarToggle"),d=y(l,"widgetLockScreen"),i=y(l,"appPreferencesButtonPosition"),m=y(l,"widgetRefresh"),f=w(()=>[{label:v("preferences.position.auto"),value:"auto"},{label:v("preferences.position.header"),value:"header"},{label:v("preferences.position.fixed"),value:"fixed"}]);return(g,b)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":b[0]||(b[0]=h=>o.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":b[1]||(b[1]=h=>n.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":b[2]||(b[2]=h=>a.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":b[3]||(b[3]=h=>t.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":b[4]||(b[4]=h=>s.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:d.value,"onUpdate:modelValue":b[5]||(b[5]=h=>d.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:r.value,"onUpdate:modelValue":b[6]||(b[6]=h=>r.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:m.value,"onUpdate:modelValue":b[7]||(b[7]=h=>m.value=h)},{default:c(()=>[V(M(e(v)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),p(Et,{modelValue:i.value,"onUpdate:modelValue":b[8]||(b[8]=h=>i.value=h),items:f.value},{default:c(()=>[V(M(e(v)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),Zi=T({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(l){const o=y(l,"shortcutKeysEnable"),t=y(l,"shortcutKeysGlobalSearch"),a=y(l,"shortcutKeysLogout"),s=y(l,"shortcutKeysLockScreen"),n=w(()=>pt()?"Alt":"⌥");return(r,d)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":d[0]||(d[0]=i=>o.value=i)},{default:c(()=>[V(M(e(v)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":d[1]||(d[1]=i=>t.value=i),disabled:!o.value},{shortcut:c(()=>[V(M(e(pt)()?"Ctrl":"⌘")+" ",1),d[4]||(d[4]=$("kbd",null," K ",-1))]),default:c(()=>[V(M(e(v)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":d[2]||(d[2]=i=>a.value=i),disabled:!o.value},{shortcut:c(()=>[V(M(n.value)+" Q ",1)]),default:c(()=>[V(M(e(v)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":d[3]||(d[3]=i=>s.value=i),disabled:!o.value},{shortcut:c(()=>[V(M(n.value)+" L ",1)]),default:c(()=>[V(M(e(v)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),Ji={class:"flex w-full flex-wrap justify-between"},ed=["onClick"],td={class:"flex-center relative size-5 rounded-sm"},ad=["value"],ld={class:"text-muted-foreground my-2 text-center text-xs"},od=T({name:"PreferenceBuiltinTheme",__name:"builtin",props:he({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(l){const o=l,t=X(),a=y(l,"modelValue"),s=y(l,"themeColorPrimary"),n=$t(b=>{s.value=b},300,!0,!0),r=w(()=>new qo(s.value||"").toHexString()),d=w(()=>[...Yo]);function i(b){switch(b){case"custom":return v("preferences.theme.builtin.custom");case"deep-blue":return v("preferences.theme.builtin.deepBlue");case"deep-green":return v("preferences.theme.builtin.deepGreen");case"default":return v("preferences.theme.builtin.default");case"gray":return v("preferences.theme.builtin.gray");case"green":return v("preferences.theme.builtin.green");case"neutral":return v("preferences.theme.builtin.neutral");case"orange":return v("preferences.theme.builtin.orange");case"pink":return v("preferences.theme.builtin.pink");case"rose":return v("preferences.theme.builtin.rose");case"sky-blue":return v("preferences.theme.builtin.skyBlue");case"slate":return v("preferences.theme.builtin.slate");case"violet":return v("preferences.theme.builtin.violet");case"yellow":return v("preferences.theme.builtin.yellow");case"zinc":return v("preferences.theme.builtin.zinc")}}function m(b){a.value=b.type}function f(b){const h=b.target;n(Xo(h.value))}function g(){var b,h,_;(_=(h=(b=t.value)==null?void 0:b[0])==null?void 0:h.click)==null||_.call(h)}return de(()=>[a.value,o.isDark],([b,h])=>{const _=d.value.find(O=>O.type===b);if(_){const O=h&&_.darkPrimaryColor||_.primaryColor;s.value=O||_.color}}),(b,h)=>(u(),x("div",Ji,[(u(!0),x(Y,null,le(d.value,_=>(u(),x("div",{key:_.type,class:"flex cursor-pointer flex-col",onClick:O=>m(_)},[$("div",{class:D([{"outline-box-active":_.type===a.value},"outline-box flex-center group cursor-pointer"])},[_.type!=="custom"?(u(),x("div",{key:0,style:ne({backgroundColor:_.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(u(),x("div",{key:1,class:"size-full px-10 py-2",onClick:ke(g,["stop"])},[$("div",td,[p(e(On),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),$("input",{ref_for:!0,ref_key:"colorInput",ref:t,value:r.value,class:"absolute inset-0 opacity-0",type:"color",onInput:f},null,40,ad)])]))],2),$("div",ld,M(i(_.type)),1)],8,ed))),128))]))}}),nd=T({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(l){const o=y(l,"appColorWeakMode"),t=y(l,"appColorGrayMode");return(a,s)=>(u(),x(Y,null,[p(Q,{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},{default:c(()=>[V(M(e(v)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n)},{default:c(()=>[V(M(e(v)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),sd=T({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(l){const o=y(l,"themeRadius"),t=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(a,s)=>(u(),k(e(Ga),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(),x(Y,null,le(t,n=>p(e(qa),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[V(M(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),rd={class:"flex w-full flex-wrap justify-between"},id=["onClick"],dd={class:"text-muted-foreground mt-2 text-center text-xs"},ud=T({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(l){const o=y(l,"modelValue"),t=y(l,"themeSemiDarkSidebar"),a=y(l,"themeSemiDarkHeader"),s=[{icon:un,name:"light"},{icon:cn,name:"dark"},{icon:pn,name:"auto"}];function n(d){return d===o.value?["outline-box-active"]:[]}function r(d){switch(d){case"auto":return v("preferences.followSystem");case"dark":return v("preferences.theme.dark");case"light":return v("preferences.theme.light")}}return(d,i)=>(u(),x("div",rd,[(u(),x(Y,null,le(s,m=>$("div",{key:m.name,class:"flex cursor-pointer flex-col",onClick:f=>o.value=m.name},[$("div",{class:D([n(m.name),"outline-box flex-center py-4"])},[(u(),k(Ee(m.icon),{class:"mx-9 size-5"}))],2),$("div",dd,M(r(m.name)),1)],8,id)),64)),p(Q,{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=m=>t.value=m),disabled:o.value==="dark",class:"mt-6"},{default:c(()=>[V(M(e(v)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:a.value,"onUpdate:modelValue":i[1]||(i[1]=m=>a.value=m),disabled:o.value==="dark"},{default:c(()=>[V(M(e(v)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),cd={class:"flex items-center"},pd={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},fd={class:"p-1"},md=T({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:he(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarExpandOnHover","update:sidebarCollapsedButton","update:sidebarFixedButton","update:headerEnable","update:headerMode","update:headerMenuAlign","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarMaxCount","update:tabbarMiddleClickToClose","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(l,{emit:o}){const t=o,a=Co.getMessage(),s=y(l,"appLocale"),n=y(l,"appDynamicTitle"),r=y(l,"appLayout"),d=y(l,"appColorGrayMode"),i=y(l,"appColorWeakMode"),m=y(l,"appContentCompact"),f=y(l,"appWatermark"),g=y(l,"appEnableCheckUpdates"),b=y(l,"appPreferencesButtonPosition"),h=y(l,"transitionProgress"),_=y(l,"transitionName"),O=y(l,"transitionLoading"),W=y(l,"transitionEnable"),U=y(l,"themeColorPrimary"),R=y(l,"themeBuiltinType"),K=y(l,"themeMode"),A=y(l,"themeRadius"),z=y(l,"themeSemiDarkSidebar"),j=y(l,"themeSemiDarkHeader"),E=y(l,"sidebarEnable"),B=y(l,"sidebarWidth"),N=y(l,"sidebarCollapsed"),q=y(l,"sidebarCollapsedShowTitle"),ae=y(l,"sidebarAutoActivateChild"),be=y(l,"sidebarExpandOnHover"),ye=y(l,"sidebarCollapsedButton"),G=y(l,"sidebarFixedButton"),Z=y(l,"headerEnable"),ve=y(l,"headerMode"),we=y(l,"headerMenuAlign"),Ve=y(l,"breadcrumbEnable"),Ae=y(l,"breadcrumbShowIcon"),_e=y(l,"breadcrumbShowHome"),F=y(l,"breadcrumbStyleType"),ee=y(l,"breadcrumbHideOnlyOne"),te=y(l,"tabbarEnable"),ge=y(l,"tabbarShowIcon"),$e=y(l,"tabbarShowMore"),ze=y(l,"tabbarShowMaximize"),se=y(l,"tabbarPersist"),pe=y(l,"tabbarDraggable"),ce=y(l,"tabbarWheelable"),qe=y(l,"tabbarStyleType"),Ye=y(l,"tabbarMaxCount"),Oe=y(l,"tabbarMiddleClickToClose"),I=y(l,"navigationStyleType"),J=y(l,"navigationSplit"),ie=y(l,"navigationAccordion"),Pe=y(l,"footerEnable"),He=y(l,"footerFixed"),yl=y(l,"copyrightSettingShow"),qt=y(l,"copyrightEnable"),Yt=y(l,"copyrightCompanyName"),Xt=y(l,"copyrightCompanySiteLink"),Qt=y(l,"copyrightDate"),Zt=y(l,"copyrightIcp"),Jt=y(l,"copyrightIcpLink"),ea=y(l,"shortcutKeysEnable"),ta=y(l,"shortcutKeysGlobalSearch"),aa=y(l,"shortcutKeysGlobalLogout"),la=y(l,"shortcutKeysGlobalLockScreen"),oa=y(l,"widgetGlobalSearch"),na=y(l,"widgetFullscreen"),sa=y(l,"widgetLanguageToggle"),ra=y(l,"widgetNotification"),ia=y(l,"widgetThemeToggle"),da=y(l,"widgetSidebarToggle"),ua=y(l,"widgetLockScreen"),ca=y(l,"widgetRefresh"),{diffPreference:lt,isDark:wl,isFullContent:Lt,isHeaderNav:xl,isHeaderSidebarNav:kl,isMixedNav:pa,isSideMixedNav:Cl,isSideMode:Sl,isSideNav:Ml}=Ze(),{copy:_l}=Qo({legacy:!0}),[$l]=Ya(),fa=X("appearance"),Bl=w(()=>[{label:v("preferences.appearance"),value:"appearance"},{label:v("preferences.layout"),value:"layout"},{label:v("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:v("preferences.general"),value:"general"}]),Tl=w(()=>!Lt.value&&!pa.value&&!xl.value&&L.header.enable);function Vl(){return oe(this,null,function*(){var zt;yield _l(JSON.stringify(lt.value,null,2)),(zt=a.copyPreferencesSuccess)==null||zt.call(a,v("preferences.copyPreferencesSuccessTitle"),v("preferences.copyPreferencesSuccess"))})}function El(){return oe(this,null,function*(){va(),Zo(),t("clearPreferencesAndLogout")})}function Ll(){return oe(this,null,function*(){lt.value&&(va(),yield Ua(L.app.locale))})}return(zt,C)=>(u(),x("div",null,[p(e($l),{description:e(v)("preferences.subtitle"),title:e(v)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[$("div",cd,[p(e(Xe),{disabled:!e(lt),tooltip:e(v)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(lt)?(u(),x("span",pd)):H("",!0),p(e(Ft),{class:"size-4",onClick:Ll})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[p(e(Ue),{disabled:!e(lt),class:"mx-4 w-full",size:"sm",variant:"default",onClick:Vl},{default:c(()=>[p(e(Tn),{class:"mr-2 size-3"}),V(" "+M(e(v)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),p(e(Ue),{disabled:!e(lt),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:El},{default:c(()=>[V(M(e(v)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[$("div",fd,[p(e(As),{modelValue:fa.value,"onUpdate:modelValue":C[68]||(C[68]=S=>fa.value=S),tabs:Bl.value},{general:c(()=>[p(e(xe),{title:e(v)("preferences.general")},{default:c(()=>[p(e(Zr),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":C[0]||(C[0]=S=>n.value=S),"app-enable-check-updates":g.value,"onUpdate:appEnableCheckUpdates":C[1]||(C[1]=S=>g.value=S),"app-locale":s.value,"onUpdate:appLocale":C[2]||(C[2]=S=>s.value=S),"app-watermark":f.value,"onUpdate:appWatermark":C[3]||(C[3]=S=>f.value=S)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.animation.title")},{default:c(()=>[p(e(Xr),{"transition-enable":W.value,"onUpdate:transitionEnable":C[4]||(C[4]=S=>W.value=S),"transition-loading":O.value,"onUpdate:transitionLoading":C[5]||(C[5]=S=>O.value=S),"transition-name":_.value,"onUpdate:transitionName":C[6]||(C[6]=S=>_.value=S),"transition-progress":h.value,"onUpdate:transitionProgress":C[7]||(C[7]=S=>h.value=S)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[p(e(xe),{title:e(v)("preferences.theme.title")},{default:c(()=>[p(e(ud),{modelValue:K.value,"onUpdate:modelValue":C[8]||(C[8]=S=>K.value=S),"theme-semi-dark-header":j.value,"onUpdate:themeSemiDarkHeader":C[9]||(C[9]=S=>j.value=S),"theme-semi-dark-sidebar":z.value,"onUpdate:themeSemiDarkSidebar":C[10]||(C[10]=S=>z.value=S)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.theme.builtin.title")},{default:c(()=>[p(e(od),{modelValue:R.value,"onUpdate:modelValue":C[11]||(C[11]=S=>R.value=S),"theme-color-primary":U.value,"onUpdate:themeColorPrimary":C[12]||(C[12]=S=>U.value=S),"is-dark":e(wl)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.theme.radius")},{default:c(()=>[p(e(sd),{modelValue:A.value,"onUpdate:modelValue":C[13]||(C[13]=S=>A.value=S)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.other")},{default:c(()=>[p(e(nd),{"app-color-gray-mode":d.value,"onUpdate:appColorGrayMode":C[14]||(C[14]=S=>d.value=S),"app-color-weak-mode":i.value,"onUpdate:appColorWeakMode":C[15]||(C[15]=S=>i.value=S)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[p(e(xe),{title:e(v)("preferences.layout")},{default:c(()=>[p(e(Ri),{modelValue:r.value,"onUpdate:modelValue":C[16]||(C[16]=S=>r.value=S)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.content")},{default:c(()=>[p(e(Ui),{modelValue:m.value,"onUpdate:modelValue":C[17]||(C[17]=S=>m.value=S)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.sidebar.title")},{default:c(()=>[p(e(Yi),{"sidebar-auto-activate-child":ae.value,"onUpdate:sidebarAutoActivateChild":C[18]||(C[18]=S=>ae.value=S),"sidebar-collapsed":N.value,"onUpdate:sidebarCollapsed":C[19]||(C[19]=S=>N.value=S),"sidebar-collapsed-show-title":q.value,"onUpdate:sidebarCollapsedShowTitle":C[20]||(C[20]=S=>q.value=S),"sidebar-enable":E.value,"onUpdate:sidebarEnable":C[21]||(C[21]=S=>E.value=S),"sidebar-expand-on-hover":be.value,"onUpdate:sidebarExpandOnHover":C[22]||(C[22]=S=>be.value=S),"sidebar-width":B.value,"onUpdate:sidebarWidth":C[23]||(C[23]=S=>B.value=S),"sidebar-collapsed-button":ye.value,"onUpdate:sidebarCollapsedButton":C[24]||(C[24]=S=>ye.value=S),"sidebar-fixed-button":G.value,"onUpdate:sidebarFixedButton":C[25]||(C[25]=S=>G.value=S),"current-layout":r.value,disabled:!e(Sl)},null,8,["sidebar-auto-activate-child","sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-width","sidebar-collapsed-button","sidebar-fixed-button","current-layout","disabled"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.header.title")},{default:c(()=>[p(e(Wi),{"header-enable":Z.value,"onUpdate:headerEnable":C[26]||(C[26]=S=>Z.value=S),"header-menu-align":we.value,"onUpdate:headerMenuAlign":C[27]||(C[27]=S=>we.value=S),"header-mode":ve.value,"onUpdate:headerMode":C[28]||(C[28]=S=>ve.value=S),disabled:e(Lt)},null,8,["header-enable","header-menu-align","header-mode","disabled"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.navigationMenu.title")},{default:c(()=>[p(e(Ki),{"navigation-accordion":ie.value,"onUpdate:navigationAccordion":C[29]||(C[29]=S=>ie.value=S),"navigation-split":J.value,"onUpdate:navigationSplit":C[30]||(C[30]=S=>J.value=S),"navigation-style-type":I.value,"onUpdate:navigationStyleType":C[31]||(C[31]=S=>I.value=S),disabled:e(Lt),"disabled-navigation-split":!e(pa)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.breadcrumb.title")},{default:c(()=>[p(e(ei),{"breadcrumb-enable":Ve.value,"onUpdate:breadcrumbEnable":C[32]||(C[32]=S=>Ve.value=S),"breadcrumb-hide-only-one":ee.value,"onUpdate:breadcrumbHideOnlyOne":C[33]||(C[33]=S=>ee.value=S),"breadcrumb-show-home":_e.value,"onUpdate:breadcrumbShowHome":C[34]||(C[34]=S=>_e.value=S),"breadcrumb-show-icon":Ae.value,"onUpdate:breadcrumbShowIcon":C[35]||(C[35]=S=>Ae.value=S),"breadcrumb-style-type":F.value,"onUpdate:breadcrumbStyleType":C[36]||(C[36]=S=>F.value=S),disabled:!Tl.value||!(e(Ml)||e(Cl)||e(kl))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.tabbar.title")},{default:c(()=>[p(e(Xi),{"tabbar-draggable":pe.value,"onUpdate:tabbarDraggable":C[37]||(C[37]=S=>pe.value=S),"tabbar-enable":te.value,"onUpdate:tabbarEnable":C[38]||(C[38]=S=>te.value=S),"tabbar-persist":se.value,"onUpdate:tabbarPersist":C[39]||(C[39]=S=>se.value=S),"tabbar-show-icon":ge.value,"onUpdate:tabbarShowIcon":C[40]||(C[40]=S=>ge.value=S),"tabbar-show-maximize":ze.value,"onUpdate:tabbarShowMaximize":C[41]||(C[41]=S=>ze.value=S),"tabbar-show-more":$e.value,"onUpdate:tabbarShowMore":C[42]||(C[42]=S=>$e.value=S),"tabbar-style-type":qe.value,"onUpdate:tabbarStyleType":C[43]||(C[43]=S=>qe.value=S),"tabbar-wheelable":ce.value,"onUpdate:tabbarWheelable":C[44]||(C[44]=S=>ce.value=S),"tabbar-max-count":Ye.value,"onUpdate:tabbarMaxCount":C[45]||(C[45]=S=>Ye.value=S),"tabbar-middle-click-to-close":Oe.value,"onUpdate:tabbarMiddleClickToClose":C[46]||(C[46]=S=>Oe.value=S)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type","tabbar-wheelable","tabbar-max-count","tabbar-middle-click-to-close"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.widget.title")},{default:c(()=>[p(e(Qi),{"app-preferences-button-position":b.value,"onUpdate:appPreferencesButtonPosition":C[47]||(C[47]=S=>b.value=S),"widget-fullscreen":na.value,"onUpdate:widgetFullscreen":C[48]||(C[48]=S=>na.value=S),"widget-global-search":oa.value,"onUpdate:widgetGlobalSearch":C[49]||(C[49]=S=>oa.value=S),"widget-language-toggle":sa.value,"onUpdate:widgetLanguageToggle":C[50]||(C[50]=S=>sa.value=S),"widget-lock-screen":ua.value,"onUpdate:widgetLockScreen":C[51]||(C[51]=S=>ua.value=S),"widget-notification":ra.value,"onUpdate:widgetNotification":C[52]||(C[52]=S=>ra.value=S),"widget-refresh":ca.value,"onUpdate:widgetRefresh":C[53]||(C[53]=S=>ca.value=S),"widget-sidebar-toggle":da.value,"onUpdate:widgetSidebarToggle":C[54]||(C[54]=S=>da.value=S),"widget-theme-toggle":ia.value,"onUpdate:widgetThemeToggle":C[55]||(C[55]=S=>ia.value=S)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),p(e(xe),{title:e(v)("preferences.footer.title")},{default:c(()=>[p(e(Oi),{"footer-enable":Pe.value,"onUpdate:footerEnable":C[56]||(C[56]=S=>Pe.value=S),"footer-fixed":He.value,"onUpdate:footerFixed":C[57]||(C[57]=S=>He.value=S)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),yl.value?(u(),k(e(xe),{key:0,title:e(v)("preferences.copyright.title")},{default:c(()=>[p(e(Ai),{"copyright-company-name":Yt.value,"onUpdate:copyrightCompanyName":C[58]||(C[58]=S=>Yt.value=S),"copyright-company-site-link":Xt.value,"onUpdate:copyrightCompanySiteLink":C[59]||(C[59]=S=>Xt.value=S),"copyright-date":Qt.value,"onUpdate:copyrightDate":C[60]||(C[60]=S=>Qt.value=S),"copyright-enable":qt.value,"onUpdate:copyrightEnable":C[61]||(C[61]=S=>qt.value=S),"copyright-icp":Zt.value,"onUpdate:copyrightIcp":C[62]||(C[62]=S=>Zt.value=S),"copyright-icp-link":Jt.value,"onUpdate:copyrightIcpLink":C[63]||(C[63]=S=>Jt.value=S),disabled:!Pe.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):H("",!0)]),shortcutKey:c(()=>[p(e(xe),{title:e(v)("preferences.shortcutKeys.global")},{default:c(()=>[p(e(Zi),{"shortcut-keys-enable":ea.value,"onUpdate:shortcutKeysEnable":C[64]||(C[64]=S=>ea.value=S),"shortcut-keys-global-search":ta.value,"onUpdate:shortcutKeysGlobalSearch":C[65]||(C[65]=S=>ta.value=S),"shortcut-keys-lock-screen":la.value,"onUpdate:shortcutKeysLockScreen":C[66]||(C[66]=S=>la.value=S),"shortcut-keys-logout":aa.value,"onUpdate:shortcutKeysLogout":C[67]||(C[67]=S=>aa.value=S)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),il=T({__name:"preferences",setup(l){const[o,t]=Ya({connectedComponent:md}),a=w(()=>{const n={};for(const[r,d]of Object.entries(L))for(const[i,m]of Object.entries(d))n[`${r}${ga(i)}`]=m;return n}),s=w(()=>{const n={};for(const[r,d]of Object.entries(L))if(typeof d=="object")for(const i of Object.keys(d))n[`update:${r}${ga(i)}`]=m=>{Re({[r]:{[i]:m}}),r==="app"&&i==="locale"&&Ua(m)};else n[r]=d;return n});return(n,r)=>(u(),x("div",null,[p(e(o),me(fe(fe({},n.$attrs),a.value),Na(s.value)),null,16),$("div",{onClick:r[0]||(r[0]=()=>e(t).open())},[P(n.$slots,"default",{},()=>[p(e(Ue),{title:e(v)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[p(e(tl),{class:"size-5"})]),_:1},8,["title"])])])]))}}),hd=T({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(l,{emit:o}){const t=o;function a(){t("clearPreferencesAndLogout")}return(s,n)=>(u(),k(il,{onClearPreferencesAndLogout:a},{default:c(()=>[p(e(Xe),null,{default:c(()=>[p(e(tl),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),bd={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},vd={class:"hover:text-accent-foreground flex-center"},gd={class:"ml-2 w-full"},yd={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},wd={class:"text-muted-foreground text-xs font-normal"},Wu=T({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""},trigger:{default:"click"},hoverDelay:{default:500}},emits:["logout"],setup(l,{emit:o}){const t=l,a=o,{globalLockScreenShortcutKey:s,globalLogoutShortcutKey:n}=Ze(),r=Je(),[d,i]=mt({connectedComponent:$r}),[m,f]=mt({onConfirm(){j()}}),g=ya("refTrigger"),b=ya("refContent"),[h,_]=Os([g,b],()=>t.hoverDelay);de(()=>t.trigger==="hover"||t.trigger==="both",E=>{E?_.enable():_.disable()},{immediate:!0});const O=w(()=>pt()?"Alt":"⌥"),W=w(()=>t.enableShortcutKey&&n.value),U=w(()=>t.enableShortcutKey&&s.value),R=w(()=>t.enableShortcutKey&&L.shortcutKeys.enable);function K(){i.open()}function A(E){i.close(),r.lockScreen(E)}function z(){f.open(),h.value=!1}function j(){a("logout"),f.close()}if(R.value){const E=Wa();Mt(E["Alt+KeyQ"],()=>{W.value&&z()}),Mt(E["Alt+KeyL"],()=>{U.value&&K()})}return(E,B)=>(u(),x(Y,null,[e(L).widget.lockScreen?(u(),k(e(d),{key:0,avatar:E.avatar,text:E.text,onSubmit:A},null,8,["avatar","text"])):H("",!0),p(e(m),{"cancel-text":e(v)("common.cancel"),"confirm-text":e(v)("common.confirm"),"fullscreen-button":!1,title:e(v)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[V(M(e(v)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),p(e(Wt),{open:e(h),"onUpdate:open":B[0]||(B[0]=N=>Ot(h)?h.value=N:null)},{default:c(()=>[p(e(Nt),{ref_key:"refTrigger",ref:g,disabled:t.trigger==="hover"},{default:c(()=>[$("div",bd,[$("div",vd,[p(e(ft),{alt:E.text,src:E.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1},8,["disabled"]),p(e(Dt),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var N;return[$("div",{ref_key:"refContent",ref:b},[p(e(qn),{class:"flex items-center p-3"},{default:c(()=>[p(e(ft),{alt:E.text,src:E.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),$("div",gd,[E.tagText||E.text||E.$slots.tagText?(u(),x("div",yd,[V(M(E.text)+" ",1),P(E.$slots,"tagText",{},()=>[E.tagText?(u(),k(e(Nn),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[V(M(E.tagText),1)]),_:1})):H("",!0)])])):H("",!0),$("div",wd,M(E.description),1)])]),_:3}),(N=E.menus)!=null&&N.length?(u(),k(e(kt),{key:0})):H("",!0),(u(!0),x(Y,null,le(E.menus,q=>(u(),k(e(ut),{key:q.text,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:q.handler},{default:c(()=>[p(e(Le),{icon:q.icon,class:"mr-2 size-4"},null,8,["icon"]),V(" "+M(q.text),1)]),_:2},1032,["onClick"]))),128)),p(e(kt)),e(L).widget.lockScreen?(u(),k(e(ut),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:K},{default:c(()=>[p(e(Za),{class:"mr-2 size-4"}),V(" "+M(e(v)("ui.widgets.lockScreen.title"))+" ",1),U.value?(u(),k(e(Sa),{key:0},{default:c(()=>[V(M(O.value)+" L ",1)]),_:1})):H("",!0)]),_:1})):H("",!0),e(L).widget.lockScreen?(u(),k(e(kt),{key:2})):H("",!0),p(e(ut),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:z},{default:c(()=>[p(e(zn),{class:"mr-2 size-4"}),V(" "+M(e(v)("common.logout"))+" ",1),W.value?(u(),k(e(Sa),{key:0},{default:c(()=>[V(M(O.value)+" Q ",1)]),_:1})):H("",!0)]),_:1})],512)]}),_:3})]),_:3},8,["open"])],64))}}),xd=T({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(l){const o=l,{contentElement:t,overlayStyle:a}=So(),s=w(()=>{const{contentCompact:n,padding:r,paddingBottom:d,paddingLeft:i,paddingRight:m,paddingTop:f}=o,g=n==="compact"?{margin:"0 auto",width:`${o.contentCompactWidth}px`}:{};return Be(fe({},g),{flex:1,padding:`${r}px`,paddingBottom:`${d}px`,paddingLeft:`${i}px`,paddingRight:`${m}px`,paddingTop:`${f}px`})});return(n,r)=>(u(),x("main",{ref_key:"contentElement",ref:t,style:ne(s.value),class:"bg-background-deep relative"},[p(e(Mo),{style:ne(e(a))},{default:c(()=>[P(n.$slots,"overlay")]),_:3},8,["style"]),P(n.$slots,"default")],4))}}),kd=T({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(l){const o=l,t=w(()=>{const{fixed:a,height:s,show:n,width:r,zIndex:d}=o;return{height:`${s}px`,marginBottom:n?"0":`-${s}px`,position:a?"fixed":"static",width:r,zIndex:d}});return(a,s)=>(u(),x("footer",{style:ne(t.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[P(a.$slots,"default")],4))}}),Cd=T({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(l){const o=l,t=Ie(),a=w(()=>{const{fullWidth:n,height:r,show:d}=o,i=!d||!n?void 0:0;return{height:`${r}px`,marginTop:d?0:`-${r}px`,right:i}}),s=w(()=>({minWidth:`${o.isMobile?40:o.sidebarWidth}px`}));return(n,r)=>(u(),x("header",{class:D([n.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b pl-2 transition-[margin-top] duration-200"]),style:ne(a.value)},[e(t).logo?(u(),x("div",{key:0,style:ne(s.value)},[P(n.$slots,"logo")],4)):H("",!0),P(n.$slots,"toggle-button"),P(n.$slots,"default")],6))}}),$a=T({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(l){const o=y(l,"collapsed");function t(){o.value=!o.value}return(a,s)=>(u(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:ke(t,["stop"])},[o.value?(u(),k(e(_o),{key:0,class:"size-4"})):(u(),k(e($o),{key:1,class:"size-4"}))]))}}),Ba=T({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(l){const o=y(l,"expandOnHover");function t(){o.value=!o.value}return(a,s)=>(u(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:t},[o.value?(u(),k(e(Vt),{key:1,class:"size-3.5"})):(u(),k(e(el),{key:0,class:"size-3.5"}))]))}}),Sd=T({__name:"layout-sidebar",props:he({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},showFixedButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:he(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(l,{emit:o}){const t=l,a=o,s=y(l,"collapse"),n=y(l,"extraCollapse"),r=y(l,"expandOnHovering"),d=y(l,"expandOnHover"),i=y(l,"extraVisible"),m=Jo(document.body),f=Ie(),g=St(),b=w(()=>z(!0)),h=w(()=>{const{isSidebarMixed:B,marginTop:N,paddingTop:q,zIndex:ae}=t;return fe(Be(fe({"--scroll-shadow":"var(--sidebar)"},z(!1)),{height:`calc(100% - ${N}px)`,marginTop:`${N}px`,paddingTop:`${q}px`,zIndex:ae}),B&&i.value?{transition:"none"}:{})}),_=w(()=>{const{extraWidth:B,show:N,width:q,zIndex:ae}=t;return{left:`${q}px`,width:i.value&&N?`${B}px`:0,zIndex:ae}}),O=w(()=>{const{headerHeight:B}=t;return{height:`${B-1}px`}}),W=w(()=>{const{collapseWidth:B,fixedExtra:N,isSidebarMixed:q,mixedWidth:ae}=t;return q&&N?{width:`${s.value?B:ae}px`}:{}}),U=w(()=>{const{collapseHeight:B,headerHeight:N}=t;return fe({height:`calc(100% - ${N+B}px)`,paddingTop:"8px"},W.value)}),R=w(()=>{const{headerHeight:B,isSidebarMixed:N}=t;return fe(Be(fe({},N?{display:"flex",justifyContent:"center"}:{}),{height:`${B-1}px`}),W.value)}),K=w(()=>{const{collapseHeight:B,headerHeight:N}=t;return{height:`calc(100% - ${N+B}px)`}}),A=w(()=>({height:`${t.collapseHeight}px`}));Da(()=>{i.value=t.fixedExtra?!0:i.value});function z(B){const{extraWidth:N,fixedExtra:q,isSidebarMixed:ae,show:be,width:ye}=t;let G=ye===0?"0px":`${ye+(ae&&q&&i.value?N:0)}px`;const{collapseWidth:Z}=t;return B&&r.value&&!d.value&&(G=`${Z}px`),Be(fe({},G==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${G}`,marginLeft:be?0:`-${G}`,maxWidth:G,minWidth:G,width:G})}function j(B){(B==null?void 0:B.offsetX)<10||d.value||(r.value||(s.value=!1),t.isSidebarMixed&&(m.value=!0),r.value=!0)}function E(){a("leave"),t.isSidebarMixed&&(m.value=!1),!d.value&&(r.value=!1,s.value=!0,i.value=!1)}return(B,N)=>(u(),x(Y,null,[B.domVisible?(u(),x("div",{key:0,class:D([B.theme,"h-full transition-all duration-150"]),style:ne(b.value)},null,6)):H("",!0),$("aside",{class:D([[B.theme,{"bg-sidebar-deep":B.isSidebarMixed,"bg-sidebar border-border border-r":!B.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:ne(h.value),onMouseenter:j,onMouseleave:E},[!s.value&&!B.isSidebarMixed&&B.showFixedButton?(u(),k(e(Ba),{key:0,"expand-on-hover":d.value,"onUpdate:expandOnHover":N[0]||(N[0]=q=>d.value=q)},null,8,["expand-on-hover"])):H("",!0),e(f).logo?(u(),x("div",{key:1,style:ne(R.value)},[P(B.$slots,"logo")],4)):H("",!0),p(e(ht),{style:ne(U.value),shadow:"","shadow-border":""},{default:c(()=>[P(B.$slots,"default")]),_:3},8,["style"]),$("div",{style:ne(A.value)},null,4),B.showCollapseButton&&!B.isSidebarMixed?(u(),k(e($a),{key:2,collapsed:s.value,"onUpdate:collapsed":N[1]||(N[1]=q=>s.value=q)},null,8,["collapsed"])):H("",!0),B.isSidebarMixed?(u(),x("div",{key:3,ref_key:"asideRef",ref:g,class:D([{"border-l":i.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:ne(_.value)},[B.isSidebarMixed&&d.value?(u(),k(e($a),{key:0,collapsed:n.value,"onUpdate:collapsed":N[2]||(N[2]=q=>n.value=q)},null,8,["collapsed"])):H("",!0),n.value?H("",!0):(u(),k(e(Ba),{key:1,"expand-on-hover":d.value,"onUpdate:expandOnHover":N[3]||(N[3]=q=>d.value=q)},null,8,["expand-on-hover"])),n.value?H("",!0):(u(),x("div",{key:2,style:ne(O.value),class:"pl-2"},[P(B.$slots,"extra-title")],4)),p(e(ht),{style:ne(K.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[P(B.$slots,"extra")]),_:3},8,["style"])],6)):H("",!0)],38)],64))}}),Md=T({__name:"layout-tabbar",props:{height:{}},setup(l){const o=l,t=w(()=>{const{height:a}=o;return{height:`${a}px`}});return(a,s)=>(u(),x("section",{style:ne(t.value),class:"border-border bg-background flex w-full border-b transition-all"},[P(a.$slots,"default")],4))}});function _d(l){const o=w(()=>l.isMobile?"sidebar-nav":l.layout),t=w(()=>o.value==="full-content"),a=w(()=>o.value==="sidebar-mixed-nav"),s=w(()=>o.value==="header-nav"),n=w(()=>o.value==="mixed-nav"||o.value==="header-sidebar-nav"),r=w(()=>o.value==="header-mixed-nav");return{currentLayout:o,isFullContent:t,isHeaderMixedNav:r,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:a}}const $d={class:"relative flex min-h-full w-full"},Bd=T({name:"VbenLayout",__name:"vben-layout",props:he({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapsedButton:{type:Boolean,default:!0},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarFixedButton:{type:Boolean,default:!0},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean,default:!1},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean,default:!1},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean,default:!1},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:he(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(l,{emit:o}){const t=l,a=o,s=y(l,"sidebarCollapse"),n=y(l,"sidebarExtraVisible"),r=y(l,"sidebarExtraCollapse"),d=y(l,"sidebarExpandOnHover"),i=y(l,"sidebarEnable"),m=X(!1),f=X(!1),g=X(),{arrivedState:b,directions:h,isScrolling:_,y:O}=en(document),{setLayoutHeaderHeight:W}=Bo(),{setLayoutFooterHeight:U}=To(),{y:R}=tn({target:g,type:"client"}),{currentLayout:K,isFullContent:A,isHeaderMixedNav:z,isHeaderNav:j,isMixedNav:E,isSidebarMixedNav:B}=_d(t),N=w(()=>t.headerMode==="auto"),q=w(()=>{let I=0;return t.headerVisible&&!t.headerHidden&&(I+=t.headerHeight),t.tabbarEnable&&(I+=t.tabbarHeight),I}),ae=w(()=>{const{sidebarCollapseShowTitle:I,sidebarMixedWidth:J,sideCollapseWidth:ie}=t;return I||B.value||z.value?J:ie}),be=w(()=>!j.value&&i.value),ye=w(()=>{const{headerHeight:I,isMobile:J}=t;return E.value&&!J?I:0}),G=w(()=>{const{isMobile:I,sidebarHidden:J,sidebarMixedWidth:ie,sidebarWidth:Pe}=t;let He=0;return J||!be.value||J&&!B.value&&!E.value&&!z.value||((z.value||B.value)&&!I?He=ie:s.value?He=I?0:ae.value:He=Pe),He}),Z=w(()=>{const{sidebarExtraCollapsedWidth:I,sidebarWidth:J}=t;return r.value?I:J}),ve=w(()=>K.value==="mixed-nav"||K.value==="sidebar-mixed-nav"||K.value==="sidebar-nav"||K.value==="header-mixed-nav"||K.value==="header-sidebar-nav"),we=w(()=>{const{headerMode:I}=t;return E.value||I==="fixed"||I==="auto-scroll"||I==="auto"}),Ve=w(()=>ve.value&&i.value&&!t.sidebarHidden),Ae=w(()=>!s.value&&t.isMobile),_e=w(()=>{let I="100%",J="unset";if(we.value&&K.value!=="header-nav"&&K.value!=="mixed-nav"&&K.value!=="header-sidebar-nav"&&Ve.value&&!t.isMobile)if((B.value||z.value)&&d.value&&n.value){const Pe=s.value?ae.value:t.sidebarMixedWidth,He=r.value?t.sidebarExtraCollapsedWidth:t.sidebarWidth;J=`${Pe+He}px`,I=`calc(100% - ${J})`}else J=m.value&&!d.value?`${ae.value}px`:`${G.value}px`,I=`calc(100% - ${J})`;return{sidebarAndExtraWidth:J,width:I}}),F=w(()=>{let I="",J=0;if(!E.value||t.sidebarHidden)I="100%";else if(i.value){const ie=d.value?t.sidebarWidth:ae.value;J=s.value?ae.value:ie,I=`calc(100% - ${s.value?G.value:ie}px)`}else I="100%";return{marginLeft:`${J}px`,width:I}}),ee=w(()=>{const I=we.value,{footerEnable:J,footerFixed:ie,footerHeight:Pe}=t;return{marginTop:I&&!A.value&&!f.value&&(!N.value||O.value<q.value)?`${q.value}px`:0,paddingBottom:`${J&&ie?Pe:0}px`}}),te=w(()=>{const{zIndex:I}=t,J=E.value?1:0;return I+J}),ge=w(()=>{const I=we.value;return{height:A.value?"0":`${q.value}px`,left:E.value?0:_e.value.sidebarAndExtraWidth,position:I?"fixed":"static",top:f.value||A.value?`-${q.value}px`:0,width:_e.value.width,"z-index":te.value}}),$e=w(()=>{const{isMobile:I,zIndex:J}=t;let ie=I||ve.value?1:-1;return E.value&&(ie+=1),J+ie}),ze=w(()=>t.footerFixed?_e.value.width:"100%"),se=w(()=>({zIndex:t.zIndex})),pe=w(()=>t.isMobile||t.headerToggleSidebarButton&&ve.value&&!B.value&&!E.value&&!t.isMobile),ce=w(()=>!ve.value||E.value||t.isMobile);de(()=>t.isMobile,I=>{I&&(s.value=!0)},{immediate:!0}),de([()=>q.value,()=>A.value],([I])=>{W(A.value?0:I)},{immediate:!0}),de(()=>t.footerHeight,I=>{U(I)},{immediate:!0});{const I=()=>{R.value>q.value?f.value=!0:f.value=!1};de([()=>t.headerMode,()=>R.value],()=>{if(!N.value||E.value||A.value){t.headerMode!=="auto-scroll"&&(f.value=!1);return}f.value=!0,I()},{immediate:!0})}{const I=$t((J,ie,Pe)=>{if(O.value<q.value){f.value=!1;return}if(Pe){f.value=!1;return}J?f.value=!1:ie&&(f.value=!0)},300);de(()=>O.value,()=>{t.headerMode!=="auto-scroll"||E.value||A.value||_.value&&I(h.top,h.bottom,b.top)})}function qe(){s.value=!0}function Ye(){t.isMobile?s.value=!1:a("toggleSidebar")}const Oe=Lo;return(I,J)=>(u(),x("div",$d,[be.value?(u(),k(e(Sd),{key:0,collapse:s.value,"onUpdate:collapse":J[0]||(J[0]=ie=>s.value=ie),"expand-on-hover":d.value,"onUpdate:expandOnHover":J[1]||(J[1]=ie=>d.value=ie),"expand-on-hovering":m.value,"onUpdate:expandOnHovering":J[2]||(J[2]=ie=>m.value=ie),"extra-collapse":r.value,"onUpdate:extraCollapse":J[3]||(J[3]=ie=>r.value=ie),"extra-visible":n.value,"onUpdate:extraVisible":J[4]||(J[4]=ie=>n.value=ie),"show-collapse-button":I.sidebarCollapsedButton,"show-fixed-button":I.sidebarFixedButton,"collapse-width":ae.value,"dom-visible":!I.isMobile,"extra-width":Z.value,"fixed-extra":d.value,"header-height":e(E)?0:I.headerHeight,"is-sidebar-mixed":e(B)||e(z),"margin-top":ye.value,"mixed-width":I.sidebarMixedWidth,show:Ve.value,theme:I.sidebarTheme,width:G.value,"z-index":$e.value,onLeave:J[5]||(J[5]=()=>a("sideMouseLeave"))},ot({extra:c(()=>[P(I.$slots,"side-extra")]),"extra-title":c(()=>[P(I.$slots,"side-extra-title")]),default:c(()=>[e(B)||e(z)?P(I.$slots,"mixed-menu",{key:0}):P(I.$slots,"menu",{key:1})]),_:2},[ve.value&&!e(E)?{name:"logo",fn:c(()=>[P(I.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","show-collapse-button","show-fixed-button","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):H("",!0),$("div",{ref_key:"contentRef",ref:g,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[$("div",{class:D([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(O)>20},e(Vo)],"overflow-hidden transition-all duration-200"]),style:ne(ge.value)},[I.headerVisible?(u(),k(e(Cd),{key:0,"full-width":!ve.value,height:I.headerHeight,"is-mobile":I.isMobile,show:!e(A)&&!I.headerHidden,"sidebar-width":I.sidebarWidth,theme:I.headerTheme,width:_e.value.width,"z-index":te.value},ot({"toggle-button":c(()=>[pe.value?(u(),k(e(Xe),{key:0,class:"my-0 mr-1 rounded-md",onClick:Ye},{default:c(()=>[p(e(Eo),{class:"size-4"})]),_:1})):H("",!0)]),default:c(()=>[P(I.$slots,"header")]),_:2},[ce.value?{name:"logo",fn:c(()=>[P(I.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):H("",!0),I.tabbarEnable?(u(),k(e(Md),{key:1,height:I.tabbarHeight,style:ne(F.value)},{default:c(()=>[P(I.$slots,"tabbar")]),_:3},8,["height","style"])):H("",!0)],6),p(e(xd),{id:e(Oe),"content-compact":I.contentCompact,"content-compact-width":I.contentCompactWidth,padding:I.contentPadding,"padding-bottom":I.contentPaddingBottom,"padding-left":I.contentPaddingLeft,"padding-right":I.contentPaddingRight,"padding-top":I.contentPaddingTop,style:ne(ee.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[P(I.$slots,"content-overlay")]),default:c(()=>[P(I.$slots,"content")]),_:3},8,["id","content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),I.footerEnable?(u(),k(e(kd),{key:0,fixed:I.footerFixed,height:I.footerHeight,show:!e(A),width:ze.value,"z-index":I.zIndex},{default:c(()=>[P(I.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):H("",!0)],512),P(I.$slots,"extra"),Ae.value?(u(),x("div",{key:1,style:ne(se.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:qe},null,4)):H("",!0)]))}}),Td={key:0,class:"relative size-full"},Vd=["src","onLoad"],Ed=T({name:"IFrameRouterView",__name:"iframe-router-view",setup(l){const o=X([]),t=gt(),a=Ge(),s=w(()=>L.tabbar.enable),n=w(()=>s.value?t.getTabs.filter(b=>{var h;return!!((h=b.meta)!=null&&h.iframeSrc)}):a.meta.iframeSrc?[a]:[]),r=w(()=>new Set(n.value.map(b=>b.name))),d=w(()=>n.value.length>0);function i(b){return b.name===a.name}function m(b){const{meta:h,name:_}=b;return!_||!t.renderRouteView?!1:s.value?!(h!=null&&h.keepAlive)&&r.value.has(_)&&_!==a.name?!1:t.getTabs.some(O=>O.name===_):i(b)}function f(b){o.value[b]=!1}function g(b){const h=o.value[b];return h===void 0?!0:h}return(b,h)=>d.value?(u(!0),x(Y,{key:0},le(n.value,(_,O)=>(u(),x(Y,{key:_.fullPath},[m(_)?Me((u(),x("div",Td,[p(e(Ia),{spinning:g(O)},null,8,["spinning"]),$("iframe",{src:_.meta.iframeSrc,class:"size-full",onLoad:W=>f(O)},null,40,Vd)],512)),[[Te,i(_)]]):H("",!0)],64))),128)):H("",!0)}}),Ld={class:"relative h-full"},zd=T({name:"LayoutContent",__name:"content",setup(l){const o=gt(),{keepAlive:t}=Ze(),{getCachedTabs:a,getExcludeCachedTabs:s,renderRouteView:n}=La(o),r=w(()=>{const{transition:m}=L;return m.name&&m.enable});function d(m){const{tabbar:f,transition:g}=L,b=g.name;if(!(!b||!g.enable))return!f.enable||!t,b}function i(m,f){var h;if(!m){console.error("Component view not found，please check the route configuration");return}const g=f.name;if(!g)return m;const b=(h=m==null?void 0:m.type)==null?void 0:h.name;return b||b===g||(m.type||(m.type={}),m.type.name=g),m}return(m,f)=>(u(),x("div",Ld,[p(e(Ed)),p(e(zo),null,{default:c(({Component:g,route:b})=>[r.value?(u(),k(nt,{key:0,name:d(b),appear:"",mode:"out-in"},{default:c(()=>[e(t)?(u(),k(wa,{key:0,exclude:e(s),include:e(a)},[e(n)?Me((u(),k(Ee(i(g,b)),{key:e(dt)(b)})),[[Te,!b.meta.iframeSrc]]):H("",!0)],1032,["exclude","include"])):e(n)?(u(),k(Ee(g),{key:e(dt)(b)})):H("",!0)]),_:2},1032,["name"])):(u(),x(Y,{key:1},[e(t)?(u(),k(wa,{key:0,exclude:e(s),include:e(a)},[e(n)?Me((u(),k(Ee(i(g,b)),{key:e(dt)(b)})),[[Te,!b.meta.iframeSrc]]):H("",!0)],1032,["exclude","include"])):e(n)?(u(),k(Ee(g),{key:e(dt)(b)})):H("",!0)],64))]),_:1})]))}});function Pd(){const l=X(!1),o=X(0),t=st(),a=500,s=w(()=>L.transition.loading),n=()=>{if(!s.value)return;const r=performance.now()-o.value;r<a?setTimeout(()=>{l.value=!1},a-r):l.value=!1};return t.beforeEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||(o.value=performance.now(),l.value=!0),!0)),t.afterEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||n(),!0)),{spinning:l}}const Hd=T({name:"LayoutContentSpinner",__name:"content-spinner",setup(l){const{spinning:o}=Pd();return(t,a)=>(u(),k(e(Ia),{spinning:e(o)},null,8,["spinning"]))}}),Ud={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Id=T({name:"LayoutFooter",__name:"footer",setup(l){return(o,t)=>(u(),x("div",Ud,[P(o.$slots,"default")]))}}),Ad={class:"flex-center hidden lg:block"},Od={class:"flex h-full min-w-0 flex-shrink-0 items-center"},De=50,Wd=T({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(l,{emit:o}){const t=o,a=Je(),{globalSearchShortcutKey:s,preferencesButtonPosition:n}=Ze(),r=Ie(),{refresh:d}=nl(),i=w(()=>{const g=[{index:De+100,name:"user-dropdown"}];return L.widget.globalSearch&&g.push({index:De,name:"global-search"}),n.value.header&&g.push({index:De+10,name:"preferences"}),L.widget.themeToggle&&g.push({index:De+20,name:"theme-toggle"}),L.widget.languageToggle&&g.push({index:De+30,name:"language-toggle"}),L.widget.fullscreen&&g.push({index:De+40,name:"fullscreen"}),L.widget.notification&&g.push({index:De+50,name:"notification"}),Object.keys(r).forEach(b=>{const h=b.split("-");b.startsWith("header-right")&&g.push({index:Number(h[2]),name:b})}),g.sort((b,h)=>b.index-h.index)}),m=w(()=>{const g=[];return L.widget.refresh&&g.push({index:0,name:"refresh"}),Object.keys(r).forEach(b=>{const h=b.split("-");b.startsWith("header-left")&&g.push({index:Number(h[2]),name:b})}),g.sort((b,h)=>b.index-h.index)});function f(){t("clearPreferencesAndLogout")}return(g,b)=>(u(),x(Y,null,[(u(!0),x(Y,null,le(m.value.filter(h=>h.index<De),h=>P(g.$slots,h.name,{key:h.name},()=>[h.name==="refresh"?(u(),k(e(Xe),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(d)},{default:c(()=>[p(e(Ft),{class:"size-4"})]),_:1},8,["onClick"])):H("",!0)],!0)),128)),$("div",Ad,[P(g.$slots,"breadcrumb",{},void 0,!0)]),(u(!0),x(Y,null,le(m.value.filter(h=>h.index>De),h=>P(g.$slots,h.name,{key:h.name},void 0,!0)),128)),$("div",{class:D([`menu-align-${e(L).header.menuAlign}`,"flex h-full min-w-0 flex-1 items-center"])},[P(g.$slots,"menu",{},void 0,!0)],2),$("div",Od,[(u(!0),x(Y,null,le(i.value,h=>P(g.$slots,h.name,{key:h.name},()=>[h.name==="global-search"?(u(),k(e(dr),{key:0,"enable-shortcut-key":e(s),menus:e(a).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):h.name==="preferences"?(u(),k(e(hd),{key:1,class:"mr-1",onClearPreferencesAndLogout:f})):h.name==="theme-toggle"?(u(),k(e(fn),{key:2,class:"mr-1 mt-[2px]"})):h.name==="language-toggle"?(u(),k(e(mn),{key:3,class:"mr-1"})):h.name==="fullscreen"?(u(),k(e(Vs),{key:4,class:"mr-1"})):H("",!0)],!0)),128))])],64))}}),Nd=Ce(Wd,[["__scopeId","data-v-86277249"]]),Dd={class:"relative mr-1 flex size-1.5"},Fd=T({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(l){return(o,t)=>(u(),x("span",Dd,[$("span",{class:D([o.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:ne(o.dotStyle)},null,6),$("span",{class:D([o.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:ne(o.dotStyle)},null,6)]))}}),dl=T({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(l){const o=l,t={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},a=w(()=>o.badgeType==="dot"),s=w(()=>{const{badgeVariants:r}=o;return r?t[r]||r:t.default}),n=w(()=>s.value&&an(s.value)?{backgroundColor:s.value}:{});return(r,d)=>a.value||r.badge?(u(),x("span",{key:0,class:D([r.$attrs.class,"absolute"])},[a.value?(u(),k(Fd,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(u(),x("div",{key:1,class:D([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:ne(n.value)},M(r.badge),7))],2)):H("",!0)}}),Rd=["onClick","onMouseenter"],Kd=T({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(l,{emit:o}){const t=l,a=o,{b:s,e:n,is:r}=je("normal-menu");function d(i){return t.activePath===i.path&&i.activeIcon||i.icon}return(i,m)=>(u(),x("ul",{class:D([[i.theme,e(s)(),e(r)("collapse",i.collapse),e(r)(i.theme,!0),e(r)("rounded",i.rounded)],"relative"])},[(u(!0),x(Y,null,le(i.menus,f=>(u(),x("li",{key:f.path,class:D([e(n)("item"),e(r)("active",i.activePath===f.path)]),onClick:()=>a("select",f),onMouseenter:()=>a("enter",f)},[p(e(Le),{class:D(e(n)("icon")),icon:d(f),fallback:""},null,8,["class","icon"]),$("span",{class:D([e(n)("name"),"truncate"])},M(f.name),3)],42,Rd))),128))],2))}}),jd=Ce(Kd,[["__scopeId","data-v-3ebda870"]]);function ul(l,o){var a,s;let t=l.parent;for(;t&&!o.includes((s=(a=t==null?void 0:t.type)==null?void 0:a.name)!=null?s:"");)t=t.parent;return t}const Ct=l=>{const o=Array.isArray(l)?l:[l],t=[];return o.forEach(a=>{var s;Array.isArray(a)?t.push(...Ct(a)):xa(a)&&Array.isArray(a.children)?t.push(...Ct(a.children)):(t.push(a),xa(a)&&((s=a.component)!=null&&s.subTree)&&t.push(...Ct(a.component.subTree)))}),t};function cl(){const l=Bt();if(!l)throw new Error("instance is required");const o=w(()=>{var n;let a=l.parent;const s=[l.props.path];for(;(a==null?void 0:a.type.name)!=="Menu";)a!=null&&a.props.path&&s.unshift(a.props.path),a=(n=a==null?void 0:a.parent)!=null?n:null;return s});return{parentMenu:w(()=>ul(l,["Menu","SubMenu"])),parentPaths:o}}function pl(l){return w(()=>{var t;return{"--menu-level":l?(t=l==null?void 0:l.level)!=null?t:1:0}})}const fl=Symbol("menuContext");function Gd(l){Fa(fl,l)}function ml(l){const o=Bt();Fa(`subMenu:${o==null?void 0:o.uid}`,l)}function jt(){if(!Bt())throw new Error("instance is required");return Ra(fl)}function hl(){const l=Bt();if(!l)throw new Error("instance is required");const o=ul(l,["Menu","SubMenu"]);return Ra(`subMenu:${o==null?void 0:o.uid}`)}function qd(l,o={}){const{enable:t=!0,delay:a=320}=o;function s(){if(!(typeof t=="boolean"?t:t.value))return;const d=document.querySelector("aside li[role=menuitem].is-active");d&&d.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}const n=Ut(s,a);return de(l,()=>{(typeof t=="boolean"?t:t.value)&&n()}),{scrollToActiveItem:s}}const Yd=T({name:"CollapseTransition",__name:"collapse-transition",setup(l){const o=a=>{a.style.maxHeight="",a.style.overflow=a.dataset.oldOverflow,a.style.paddingTop=a.dataset.oldPaddingTop,a.style.paddingBottom=a.dataset.oldPaddingBottom},t={afterEnter(a){a.style.maxHeight="",a.style.overflow=a.dataset.oldOverflow},afterLeave(a){o(a)},beforeEnter(a){a.dataset||(a.dataset={}),a.dataset.oldPaddingTop=a.style.paddingTop,a.dataset.oldMarginTop=a.style.marginTop,a.dataset.oldPaddingBottom=a.style.paddingBottom,a.dataset.oldMarginBottom=a.style.marginBottom,a.style.height&&(a.dataset.elExistsHeight=a.style.height),a.style.maxHeight=0,a.style.paddingTop=0,a.style.marginTop=0,a.style.paddingBottom=0,a.style.marginBottom=0},beforeLeave(a){a.dataset||(a.dataset={}),a.dataset.oldPaddingTop=a.style.paddingTop,a.dataset.oldMarginTop=a.style.marginTop,a.dataset.oldPaddingBottom=a.style.paddingBottom,a.dataset.oldMarginBottom=a.style.marginBottom,a.dataset.oldOverflow=a.style.overflow,a.style.maxHeight=`${a.scrollHeight}px`,a.style.overflow="hidden"},enter(a){requestAnimationFrame(()=>{a.dataset.oldOverflow=a.style.overflow,a.dataset.elExistsHeight?a.style.maxHeight=a.dataset.elExistsHeight:a.scrollHeight===0?a.style.maxHeight=0:a.style.maxHeight=`${a.scrollHeight}px`,a.style.paddingTop=a.dataset.oldPaddingTop,a.style.paddingBottom=a.dataset.oldPaddingBottom,a.style.marginTop=a.dataset.oldMarginTop,a.style.marginBottom=a.dataset.oldMarginBottom,a.style.overflow="hidden"})},enterCancelled(a){o(a)},leave(a){a.scrollHeight!==0&&(a.style.maxHeight=0,a.style.paddingTop=0,a.style.paddingBottom=0,a.style.marginTop=0,a.style.marginBottom=0)},leaveCancelled(a){o(a)}};return(a,s)=>(u(),k(nt,me({name:"collapse-transition"},Na(t)),{default:c(()=>[P(a.$slots,"default")]),_:3},16))}}),Ta=T({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){const o=l,t=jt(),{b:a,e:s,is:n}=je("sub-menu-content"),r=je("menu"),d=w(()=>t==null?void 0:t.openedMenus.includes(o.path)),i=w(()=>t.props.collapse),m=w(()=>o.level===1),f=w(()=>t.props.collapseShowTitle&&m.value&&i.value),g=w(()=>t==null?void 0:t.props.mode),b=w(()=>g.value==="horizontal"||!(m.value&&i.value)),h=w(()=>g.value==="vertical"&&m.value&&i.value&&!f.value),_=w(()=>g.value==="horizontal"&&!m.value||g.value==="vertical"&&i.value?It:At),O=w(()=>d.value?{transform:"rotate(180deg)"}:{});return(W,U)=>(u(),x("div",{class:D([e(a)(),e(n)("collapse-show-title",f.value),e(n)("more",W.isMenuMore)])},[P(W.$slots,"default"),W.isMenuMore?H("",!0):(u(),k(e(Le),{key:0,class:D(e(r).e("icon")),icon:W.icon,fallback:""},null,8,["class","icon"])),h.value?H("",!0):(u(),x("div",{key:1,class:D([e(s)("title")])},[P(W.$slots,"title")],2)),W.isMenuMore?H("",!0):Me((u(),k(Ee(_.value),{key:2,class:D([[e(s)("icon-arrow")],"size-4"]),style:ne(O.value)},null,8,["class","style"])),[[Te,b.value]])],2))}}),bl=T({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){var ye;const o=l,{parentMenu:t,parentPaths:a}=cl(),{b:s,is:n}=je("sub-menu"),r=je("menu"),d=jt(),i=hl(),m=pl(i),f=X(!1),g=X({}),b=X({}),h=X(null);ml({addSubMenu:j,handleMouseleave:q,level:((ye=i==null?void 0:i.level)!=null?ye:0)+1,mouseInChild:f,removeSubMenu:E});const _=w(()=>d==null?void 0:d.openedMenus.includes(o.path)),O=w(()=>{var G;return((G=t.value)==null?void 0:G.type.name)==="Menu"}),W=w(()=>{var G;return(G=d==null?void 0:d.props.mode)!=null?G:"vertical"}),U=w(()=>d==null?void 0:d.props.rounded),R=w(()=>{var G;return(G=i==null?void 0:i.level)!=null?G:0}),K=w(()=>R.value===1),A=w(()=>{const G=W.value==="horizontal",Z=G&&K.value?"bottom":"right";return{collisionPadding:{top:20},side:Z,sideOffset:G?5:10}}),z=w(()=>{let G=!1;return Object.values(g.value).forEach(Z=>{Z.active&&(G=!0)}),Object.values(b.value).forEach(Z=>{Z.active&&(G=!0)}),G});function j(G){b.value[G.path]=G}function E(G){Reflect.deleteProperty(b.value,G.path)}function B(){const G=d==null?void 0:d.props.mode;o.disabled||d!=null&&d.props.collapse&&G==="vertical"||G==="horizontal"||d==null||d.handleSubMenuClick({active:z.value,parentPaths:a.value,path:o.path})}function N(G,Z=300){var ve,we;if(G.type!=="focus"){if(!(d!=null&&d.props.collapse)&&(d==null?void 0:d.props.mode)==="vertical"||o.disabled){i&&(i.mouseInChild.value=!0);return}i&&(i.mouseInChild.value=!0),h.value&&window.clearTimeout(h.value),h.value=setTimeout(()=>{d==null||d.openMenu(o.path,a.value)},Z),(we=(ve=t.value)==null?void 0:ve.vnode.el)==null||we.dispatchEvent(new MouseEvent("mouseenter"))}}function q(G=!1){var Z;if(!(d!=null&&d.props.collapse)&&(d==null?void 0:d.props.mode)==="vertical"&&i){i.mouseInChild.value=!1;return}h.value&&window.clearTimeout(h.value),i&&(i.mouseInChild.value=!1),h.value=setTimeout(()=>{!f.value&&(d==null||d.closeMenu(o.path,a.value))},300),G&&((Z=i==null?void 0:i.handleMouseleave)==null||Z.call(i,!0))}const ae=w(()=>z.value&&o.activeIcon||o.icon),be=vt({active:z,parentPaths:a,path:o.path});return Fe(()=>{var G,Z;(G=i==null?void 0:i.addSubMenu)==null||G.call(i,be),(Z=d==null?void 0:d.addSubMenu)==null||Z.call(d,be)}),Ka(()=>{var G,Z;(G=i==null?void 0:i.removeSubMenu)==null||G.call(i,be),(Z=d==null?void 0:d.removeSubMenu)==null||Z.call(d,be)}),(G,Z)=>(u(),x("li",{class:D([e(s)(),e(n)("opened",_.value),e(n)("active",z.value),e(n)("disabled",G.disabled)]),onFocus:N,onMouseenter:N,onMouseleave:Z[3]||(Z[3]=()=>q())},[e(d).isMenuPopup?(u(),k(e(Ls),{key:0,"content-class":[e(d).theme,e(r).e("popup-container"),e(n)(e(d).theme,!0),_.value?"":"hidden","overflow-auto","max-h-[calc(var(--radix-hover-card-content-available-height)-20px)]"],"content-props":A.value,open:!0,"open-delay":0},{trigger:c(()=>[p(Ta,{class:D(e(n)("active",z.value)),icon:ae.value,"is-menu-more":G.isSubMenuMore,"is-top-level-menu-submenu":O.value,level:R.value,path:G.path,onClick:ke(B,["stop"])},{title:c(()=>[P(G.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[$("div",{class:D([e(r).is(W.value,!0),e(r).e("popup")]),onFocus:Z[0]||(Z[0]=ve=>N(ve,100)),onMouseenter:Z[1]||(Z[1]=ve=>N(ve,100)),onMouseleave:Z[2]||(Z[2]=()=>q(!0))},[$("ul",{class:D([e(r).b(),e(n)("rounded",U.value)]),style:ne(e(m))},[P(G.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(u(),x(Y,{key:1},[p(Ta,{class:D(e(n)("active",z.value)),icon:ae.value,"is-menu-more":G.isSubMenuMore,"is-top-level-menu-submenu":O.value,level:R.value,path:G.path,onClick:ke(B,["stop"])},{title:c(()=>[P(G.$slots,"title")]),default:c(()=>[P(G.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),p(Yd,null,{default:c(()=>[Me($("ul",{class:D([e(r).b(),e(n)("rounded",U.value)]),style:ne(e(m))},[P(G.$slots,"default")],6),[[Te,_.value]])]),_:3})],64))],34))}}),Xd=T({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},scrollToActive:{type:Boolean,default:!1},theme:{default:"dark"}},emits:["close","open","select"],setup(l,{emit:o}){const t=l,a=o,{b:s,is:n}=je("menu"),r=pl(),d=Ie(),i=X(),m=X(-1),f=X(t.defaultOpeneds&&!t.collapse?[...t.defaultOpeneds]:[]),g=X(t.defaultActive),b=X({}),h=X({}),_=X(!1),O=w(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),W=w(()=>{var $e,ze;const F=(ze=($e=d.default)==null?void 0:$e.call(d))!=null?ze:[],ee=Ct(F),te=m.value===-1?ee:ee.slice(0,m.value),ge=m.value===-1?[]:ee.slice(m.value);return{showSlotMore:ge.length>0,slotDefault:te,slotMore:ge}});de(()=>t.collapse,F=>{F&&(f.value=[])}),de(b.value,N),de(()=>t.defaultActive,(F="")=>{b.value[F]||(g.value=""),q(F)});let U;Da(()=>{t.mode==="horizontal"?U=on(i,j).stop:U==null||U()}),Gd(vt({activePath:g,addMenuItem:ve,addSubMenu:we,closeMenu:G,handleMenuItemClick:ae,handleSubMenuClick:be,isMenuPopup:O,openedMenus:f,openMenu:Z,props:t,removeMenuItem:Ae,removeSubMenu:Ve,subMenus:h,theme:ln(t,"theme"),items:b})),ml({addSubMenu:we,level:1,mouseInChild:_,removeSubMenu:Ve});function R(F){const ee=getComputedStyle(F),te=Number.parseInt(ee.marginLeft,10),ge=Number.parseInt(ee.marginRight,10);return F.offsetWidth+te+ge||0}function K(){var ce,qe,Ye;if(!i.value)return-1;const F=[...(qe=(ce=i.value)==null?void 0:ce.childNodes)!=null?qe:[]].filter(Oe=>Oe.nodeName!=="#comment"&&(Oe.nodeName!=="#text"||Oe.nodeValue)),ee=46,te=getComputedStyle(i==null?void 0:i.value),ge=Number.parseInt(te.paddingLeft,10),$e=Number.parseInt(te.paddingRight,10),ze=((Ye=i.value)==null?void 0:Ye.clientWidth)-ge-$e;let se=0,pe=0;return F.forEach((Oe,I)=>{se+=R(Oe),se<=ze-ee&&(pe=I+1)}),pe===F.length?-1:pe}function A(F,ee=33.34){let te;return()=>{te&&clearTimeout(te),te=setTimeout(()=>{F()},ee)}}let z=!0;function j(){if(m.value===K())return;const F=()=>{m.value=-1,Ke(()=>{m.value=K()})};F(),z?F():A(F)(),z=!1}const E=w(()=>t.scrollToActive&&t.mode==="vertical"&&!t.collapse),{scrollToActiveItem:B}=qd(g,{enable:E,delay:320});de(g,()=>{B()});function N(){_e().forEach(ee=>{const te=h.value[ee];te&&Z(ee,te.parentPaths)})}function q(F){const ee=b.value,te=ee[F]||g.value&&ee[g.value]||ee[t.defaultActive||""];g.value=te?te.path:F}function ae(F){const{collapse:ee,mode:te}=t;(te==="horizontal"||ee)&&(f.value=[]);const{parentPaths:ge,path:$e}=F;!$e||!ge||a("select",$e,ge)}function be({parentPaths:F,path:ee}){f.value.includes(ee)?G(ee,F):Z(ee,F)}function ye(F){const ee=f.value.indexOf(F);ee!==-1&&f.value.splice(ee,1)}function G(F,ee){var te,ge;t.accordion&&(f.value=(ge=(te=h.value[F])==null?void 0:te.parentPaths)!=null?ge:[]),ye(F),a("close",F,ee)}function Z(F,ee){if(!f.value.includes(F)){if(t.accordion){const te=_e();te.includes(F)&&(ee=te),f.value=f.value.filter(ge=>ee.includes(ge))}f.value.push(F),a("open",F,ee)}}function ve(F){b.value[F.path]=F}function we(F){h.value[F.path]=F}function Ve(F){Reflect.deleteProperty(h.value,F.path)}function Ae(F){Reflect.deleteProperty(b.value,F.path)}function _e(){const F=g.value&&b.value[g.value];return!F||t.mode==="horizontal"||t.collapse?[]:F.parentPaths}return(F,ee)=>(u(),x("ul",{ref_key:"menu",ref:i,class:D([F.theme,e(s)(),e(n)(F.mode,!0),e(n)(F.theme,!0),e(n)("rounded",F.rounded),e(n)("collapse",F.collapse),e(n)("menu-align",F.mode==="horizontal")]),style:ne(e(r)),role:"menu"},[F.mode==="horizontal"&&W.value.showSlotMore?(u(),x(Y,{key:0},[(u(!0),x(Y,null,le(W.value.slotDefault,te=>(u(),k(Ee(te),{key:te.key}))),128)),p(bl,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[p(e(Po),{class:"size-4"})]),default:c(()=>[(u(!0),x(Y,null,le(W.value.slotMore,te=>(u(),k(Ee(te),{key:te.key}))),128))]),_:1})],64)):P(F.$slots,"default",{key:1})],6))}}),Qd=T({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(l,{emit:o}){const t=l,a=o,s=Ie(),{b:n,e:r,is:d}=je("menu-item"),i=je("menu"),m=jt(),f=hl(),{parentMenu:g,parentPaths:b}=cl(),h=w(()=>t.path===(m==null?void 0:m.activePath)),_=w(()=>h.value&&t.activeIcon||t.icon),O=w(()=>{var A;return((A=g.value)==null?void 0:A.type.name)==="Menu"}),W=w(()=>{var A;return((A=m.props)==null?void 0:A.collapseShowTitle)&&O.value&&m.props.collapse}),U=w(()=>{var A;return m.props.mode==="vertical"&&O.value&&((A=m.props)==null?void 0:A.collapse)&&s.title}),R=vt({active:h,parentPaths:b.value,path:t.path||""});function K(){var A;t.disabled||((A=m==null?void 0:m.handleMenuItemClick)==null||A.call(m,{parentPaths:b.value,path:t.path}),a("click",R))}return Fe(()=>{var A,z;(A=f==null?void 0:f.addSubMenu)==null||A.call(f,R),(z=m==null?void 0:m.addMenuItem)==null||z.call(m,R)}),Ka(()=>{var A,z;(A=f==null?void 0:f.removeSubMenu)==null||A.call(f,R),(z=m==null?void 0:m.removeMenuItem)==null||z.call(m,R)}),(A,z)=>(u(),x("li",{class:D([e(m).theme,e(n)(),e(d)("active",h.value),e(d)("disabled",A.disabled),e(d)("collapse-show-title",W.value)]),role:"menuitem",onClick:ke(K,["stop"])},[U.value?(u(),k(e(et),{key:0,"content-class":[e(m).theme],side:"right"},{trigger:c(()=>[$("div",{class:D([e(i).be("tooltip","trigger")])},[p(e(Le),{class:D(e(i).e("icon")),icon:_.value},null,8,["class","icon"]),P(A.$slots,"default"),W.value?(u(),x("span",{key:0,class:D(e(i).e("name"))},[P(A.$slots,"title")],2)):H("",!0)],2)]),default:c(()=>[P(A.$slots,"title")]),_:3},8,["content-class"])):H("",!0),Me($("div",{class:D([e(r)("content")])},[e(m).props.mode!=="horizontal"?(u(),k(e(dl),me({key:0,class:"right-2"},t),null,16)):H("",!0),p(e(Le),{class:D(e(i).e("icon")),icon:_.value},null,8,["class","icon"]),P(A.$slots,"default"),P(A.$slots,"title")],2),[[Te,!U.value]])],2))}}),vl=T({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(l){const o=l,t=w(()=>{const{menu:a}=o;return Reflect.has(a,"children")&&!!a.children&&a.children.length>0});return(a,s)=>t.value?(u(),k(e(bl),{key:`${a.menu.path}_sub`,"active-icon":a.menu.activeIcon,icon:a.menu.icon,path:a.menu.path},{content:c(()=>[p(e(dl),{badge:a.menu.badge,"badge-type":a.menu.badgeType,"badge-variants":a.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[$("span",null,M(a.menu.name),1)]),default:c(()=>[(u(!0),x(Y,null,le(a.menu.children||[],n=>(u(),k(vl,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(u(),k(e(Qd),{key:a.menu.path,"active-icon":a.menu.activeIcon,badge:a.menu.badge,"badge-type":a.menu.badgeType,"badge-variants":a.menu.badgeVariants,icon:a.menu.icon,path:a.menu.path},{title:c(()=>[$("span",null,M(a.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),gl=T({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const t=Qe(l);return(a,s)=>(u(),k(e(Xd),We(tt(e(t))),{default:c(()=>[(u(!0),x(Y,null,le(a.menus,n=>(u(),k(vl,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function Gt(){const l=st(),o=new Map,t=()=>{l.getRoutes().forEach(d=>{o.set(d.path,d)})};t(),l.afterEach(()=>{t()});const a=r=>{var i,m;if(Ht(r))return!0;const d=o.get(r);return(m=(i=d==null?void 0:d.meta)==null?void 0:i.openInNewWindow)!=null?m:!1};return{navigation:r=>oe(null,null,function*(){var d;try{const i=o.get(r),{openInNewWindow:m=!1,query:f={}}=(d=i==null?void 0:i.meta)!=null?d:{};Ht(r)?nn(r,{target:"_blank"}):m?sn(r):yield l.push({path:r,query:f})}catch(i){throw console.error("Navigation failed:",i),i}}),willOpenedByWindow:r=>a(r)}}const Zd=T({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const o=Ge(),{navigation:t}=Gt();function a(s){return oe(this,null,function*(){yield t(s)})}return(s,n)=>{var r;return u(),k(e(gl),{accordion:s.accordion,collapse:s.collapse,"default-active":((r=e(o).meta)==null?void 0:r.activePath)||e(o).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:a},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),Va=T({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},emits:["open","select"],setup(l,{emit:o}){const t=l,a=o;function s(r){a("select",r,t.mode)}function n(r,d){a("open",r,d)}return(r,d)=>(u(),k(e(gl),{accordion:r.accordion,collapse:r.collapse,"collapse-show-title":r.collapseShowTitle,"default-active":r.defaultActive,menus:r.menus,mode:r.mode,rounded:r.rounded,"scroll-to-active":"",theme:r.theme,onOpen:n,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),Jd=T({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(l,{emit:o}){const t=l,a=o,s=Ge();return ja(()=>{const n=Rt(t.menus||[],s.path);if(n){const r=(t.menus||[]).find(d=>{var i;return d.path===((i=n.parents)==null?void 0:i[0])});a("defaultSelect",n,r)}}),(n,r)=>(u(),k(e(jd),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:r[0]||(r[0]=d=>a("enter",d)),onSelect:r[1]||(r[1]=d=>a("select",d))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function eu(l){const o=Je(),{navigation:t,willOpenedByWindow:a}=Gt(),s=w(()=>{var U;return(U=l==null?void 0:l.value)!=null?U:o.accessMenus}),n=new Map,r=X([]),d=Ge(),i=X([]),m=X(!1),f=X(""),g=w(()=>L.app.layout==="header-mixed-nav"?1:0),b=U=>oe(null,null,function*(){var A,z,j;const R=(A=U==null?void 0:U.children)!=null?A:[],K=R.length>0;a(U.path)||(i.value=R!=null?R:[],f.value=(j=(z=U.parents)==null?void 0:z[g.value])!=null?j:U.path,m.value=K),K?L.sidebar.autoActivateChild&&(yield t(n.has(U.path)?n.get(U.path):U.path)):yield t(U.path)}),h=(U,R)=>oe(null,null,function*(){var K,A,z,j;i.value=(A=(K=R==null?void 0:R.children)!=null?K:r.value)!=null?A:[],f.value=(j=(z=U.parents)==null?void 0:z[g.value])!=null?j:U.path,L.sidebar.expandOnHover&&(m.value=i.value.length>0)}),_=()=>{var A,z;if(L.sidebar.expandOnHover)return;const{findMenu:U,rootMenu:R,rootMenuPath:K}=ct(s.value,d.path);f.value=(A=K!=null?K:U==null?void 0:U.path)!=null?A:"",i.value=(z=R==null?void 0:R.children)!=null?z:[]},O=U=>{var R,K,A;if(!L.sidebar.expandOnHover){const{findMenu:z}=ct(s.value,U.path);i.value=(R=z==null?void 0:z.children)!=null?R:[],f.value=(A=(K=U.parents)==null?void 0:K[g.value])!=null?A:U.path,m.value=i.value.length>0}};function W(U){var j,E,B,N;const R=((j=d.meta)==null?void 0:j.activePath)||U,{findMenu:K,rootMenu:A,rootMenuPath:z}=ct(s.value,R,g.value);r.value=(E=A==null?void 0:A.children)!=null?E:[],z&&n.set(z,R),f.value=(B=z!=null?z:K==null?void 0:K.path)!=null?B:"",i.value=(N=A==null?void 0:A.children)!=null?N:[],L.sidebar.expandOnHover&&(m.value=i.value.length>0)}return de(()=>[d.path,L.app.layout],([U])=>{W(U||"")},{immediate:!0}),{extraActiveMenu:f,extraMenus:i,handleDefaultSelect:h,handleMenuMouseEnter:O,handleMixedMenuSelect:b,handleSideMouseLeave:_,sidebarExtraVisible:m}}function tu(){const{navigation:l,willOpenedByWindow:o}=Gt(),t=Je(),a=Ge(),s=X([]),n=X(""),r=X(""),d=X([]),i=new Map,{isMixedNav:m,isHeaderMixedNav:f}=Ze(),g=w(()=>L.navigation.split&&m.value||f.value),b=w(()=>{const j=L.sidebar.enable;return g.value?j&&s.value.length>0:j}),h=w(()=>t.accessMenus),_=w(()=>g.value?h.value.map(j=>Be(fe({},j),{children:[]})):h.value),O=w(()=>g.value?s.value:h.value),W=w(()=>f.value?O.value:_.value),U=w(()=>{var j,E;return(E=(j=a==null?void 0:a.meta)==null?void 0:j.activePath)!=null?E:a.path}),R=w(()=>{var j,E;return g.value?n.value:(E=(j=a.meta)==null?void 0:j.activePath)!=null?E:a.path}),K=(j,E)=>{var q,ae;if(!g.value||E==="vertical"){l(j);return}const B=h.value.find(be=>be.path===j),N=(q=B==null?void 0:B.children)!=null?q:[];o(j)||(n.value=(ae=B==null?void 0:B.path)!=null?ae:"",s.value=N),N.length===0?l(j):B&&L.sidebar.autoActivateChild&&l(i.has(B.path)?i.get(B.path):B.path)},A=(j,E)=>{E.length<=1&&L.sidebar.autoActivateChild&&l(i.has(j)?i.get(j):j)};function z(j=a.path){var N,q,ae,be,ye;let{rootMenu:E}=ct(h.value,j);E||(E=h.value.find(G=>G.path===j));const B=ct((E==null?void 0:E.children)||[],j,1);r.value=(N=B.rootMenuPath)!=null?N:"",d.value=(ae=(q=B.rootMenu)==null?void 0:q.children)!=null?ae:[],n.value=(be=E==null?void 0:E.path)!=null?be:"",s.value=(ye=E==null?void 0:E.children)!=null?ye:[]}return de(()=>a.path,j=>{var B,N,q,ae;const E=(ae=(q=(B=a==null?void 0:a.meta)==null?void 0:B.activePath)!=null?q:(N=a==null?void 0:a.meta)==null?void 0:N.link)!=null?ae:j;o(E)||(z(E),n.value&&i.set(n.value,E))},{immediate:!0}),ja(()=>{var j;z(((j=a.meta)==null?void 0:j.activePath)||a.path)}),{handleMenuSelect:K,handleMenuOpen:A,headerActive:R,headerMenus:_,sidebarActive:U,sidebarMenus:O,mixHeaderMenus:W,mixExtraMenus:d,sidebarVisible:b}}const au={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},lu=T({__name:"tool-more",props:{menus:{}},setup(l){return(o,t)=>(u(),k(e(Ts),{menus:o.menus,modal:!1},{default:c(()=>[$("div",au,[p(e(At),{class:"size-4"})])]),_:1},8,["menus"]))}}),ou=T({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(l){const o=y(l,"screen");function t(){o.value=!o.value}return(a,s)=>(u(),x("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:t},[o.value?(u(),k(e(Ja),{key:0,class:"size-4"})):(u(),k(e(Qa),{key:1,class:"size-4"}))]))}}),nu=["data-index","onClick","onMousedown"],su={class:"relative flex size-full items-center"},ru={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},iu={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},du={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},uu=T({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:he({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:he(["close","unpin"],["update:active"]),setup(l,{emit:o}){const t=l,a=o,s=y(l,"active"),n=w(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[t.styleType||"plain"]||{content:""}),r=w(()=>t.tabs.map(i=>{const{fullPath:m,meta:f,name:g,path:b,key:h}=i||{},{affixTab:_,icon:O,newTabTitle:W,tabClosable:U,title:R}=f||{};return{affixTab:!!_,closable:Reflect.has(f,"tabClosable")?!!U:!0,fullPath:m,icon:O,key:h,meta:f,name:g,path:b,title:W||R||g}}));function d(i,m){i.button===1&&m.closable&&!m.affixTab&&r.value.length>1&&t.middleClickToClose&&(i.preventDefault(),i.stopPropagation(),a("close",m.key))}return(i,m)=>(u(),x("div",{class:D([i.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[p(_t,{name:"slide-left"},{default:c(()=>[(u(!0),x(Y,null,le(r.value,(f,g)=>(u(),x("div",{key:f.key,class:D([[{"is-active dark:bg-accent bg-primary/15":f.key===s.value,draggable:!f.affixTab,"affix-tab":f.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":g,"data-tab-item":"true",onClick:b=>s.value=f.key,onMousedown:b=>d(b,f)},[p(e(ll),{"handler-data":f,menus:i.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[$("div",su,[$("div",ru,[Me(p(e(Tt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:ke(()=>a("close",f.key),["stop"])},null,8,["onClick"]),[[Te,!f.affixTab&&r.value.length>1&&f.closable]]),Me(p(e(Vt),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:ke(()=>a("unpin",f),["stop"])},null,8,["onClick"]),[[Te,f.affixTab&&r.value.length>1&&f.closable]])]),$("div",iu,[i.showIcon?(u(),k(e(Le),{key:0,icon:f.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):H("",!0),$("span",du,M(f.title),1)])])]),_:2},1032,["handler-data","menus"])],42,nu))),128))]),_:1})],2))}}),cu=["data-active-tab","data-index","onClick","onMousedown"],pu={class:"relative size-full px-1"},fu={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},mu={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},hu={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},bu={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},vu=T({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:he({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:he(["close","unpin"],["update:active"]),setup(l,{emit:o}){const t=l,a=o,s=y(l,"active"),n=X(),r=X(),d=w(()=>{const{gap:f}=t;return{"--gap":`${f}px`}}),i=w(()=>t.tabs.map(f=>{const{fullPath:g,meta:b,name:h,path:_,key:O}=f||{},{affixTab:W,icon:U,newTabTitle:R,tabClosable:K,title:A}=b||{};return{affixTab:!!W,closable:Reflect.has(b,"tabClosable")?!!K:!0,fullPath:g,icon:U,key:O,meta:b,name:h,path:_,title:R||A||h}}));function m(f,g){f.button===1&&g.closable&&!g.affixTab&&i.value.length>1&&t.middleClickToClose&&(f.preventDefault(),f.stopPropagation(),a("close",g.key))}return(f,g)=>(u(),x("div",{ref_key:"contentRef",ref:n,class:D([f.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:ne(d.value)},[p(_t,{name:"slide-left"},{default:c(()=>[(u(!0),x(Y,null,le(i.value,(b,h)=>(u(),x("div",{key:b.key,ref_for:!0,ref_key:"tabRef",ref:r,class:D([[{"is-active":b.key===s.value,draggable:!b.affixTab,"affix-tab":b.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":h,"data-tab-item":"true",onClick:_=>s.value=b.key,onMousedown:_=>m(_,b)},[p(e(ll),{"handler-data":b,menus:f.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[$("div",pu,[h!==0&&b.key!==s.value?(u(),x("div",fu)):H("",!0),g[0]||(g[0]=$("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[$("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),$("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[$("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),$("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[$("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),$("div",mu,[Me(p(e(Tt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:ke(()=>a("close",b.key),["stop"])},null,8,["onClick"]),[[Te,!b.affixTab&&i.value.length>1&&b.closable]]),Me(p(e(Vt),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:ke(()=>a("unpin",b),["stop"])},null,8,["onClick"]),[[Te,b.affixTab&&i.value.length>1&&b.closable]])]),$("div",hu,[f.showIcon?(u(),k(e(Le),{key:0,icon:b.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):H("",!0),$("span",bu,M(b.title),1)])])]),_:2},1032,["handler-data","menus"])],42,cu))),128))]),_:1})],6))}}),gu=Ce(vu,[["__scopeId","data-v-27f43fe8"]]);function Pt(l){const o="group";return l.classList.contains(o)?l:l.closest(`.${o}`)}function yu(l,o){const t=X(null);function a(){return oe(this,null,function*(){var i;yield Ke();const n=(i=document.querySelectorAll(`.${l.contentClass}`))==null?void 0:i[0];if(!n){console.warn("Element not found for sortable initialization");return}const r=()=>oe(null,null,function*(){var m;n.style.cursor="default",(m=n.querySelector(".draggable"))==null||m.classList.remove("dragging")}),{initializeSortable:d}=Uo(n,{filter:(m,f)=>{const g=Pt(f);return!(g==null?void 0:g.classList.contains("draggable"))||!l.draggable},onEnd(m){const{newIndex:f,oldIndex:g}=m,{srcElement:b}=m.originalEvent;if(!b){r();return}const h=Pt(b);if(!h){r();return}if(!h.classList.contains("draggable")){r();return}g!==void 0&&f!==void 0&&!Number.isNaN(g)&&!Number.isNaN(f)&&g!==f&&o("sortTabs",g,f),r()},onMove(m){const f=Pt(m.related);if(f!=null&&f.classList.contains("draggable")&&l.draggable){const g=m.dragged.classList.contains("affix-tab"),b=m.related.classList.contains("affix-tab");return g===b}else return!1},onStart:()=>{var m;n.style.cursor="grabbing",(m=n.querySelector(".draggable"))==null||m.classList.add("dragging")}});t.value=yield d()})}function s(){return oe(this,null,function*(){const{isMobile:n}=Ho();n.value||(yield Ke(),a())})}Fe(s),de(()=>l.styleType,()=>{var n;(n=t.value)==null||n.destroy(),s()}),bt(()=>{var n;(n=t.value)==null||n.destroy()})}function wu(l){let o=null,t=null,a=0;const s=X(null),n=X(null),r=X(!1),d=X(!0),i=X(!1);function m(){var K;const W=(K=s.value)==null?void 0:K.$el;if(!W||!n.value)return{};const U=W.clientWidth,R=n.value.clientWidth;return{scrollbarWidth:U,scrollViewWidth:R}}function f(W,U=150){var A;const{scrollbarWidth:R,scrollViewWidth:K}=m();!R||!K||R>K||(A=n.value)==null||A.scrollBy({behavior:"smooth",left:W==="left"?-(R-U):+(R-U)})}function g(){return oe(this,null,function*(){var R,K;yield Ke();const W=(R=s.value)==null?void 0:R.$el;if(!W)return;const U=W==null?void 0:W.querySelector("div[data-radix-scroll-area-viewport]");n.value=U,h(),yield Ke(),b(),o==null||o.disconnect(),o=new ResizeObserver(Ut(A=>{h(),b()},100)),o.observe(U),a=((K=l.tabs)==null?void 0:K.length)||0,t==null||t.disconnect(),t=new MutationObserver(()=>{const A=U.querySelectorAll('div[data-tab-item="true"]').length;A>a&&b(),A!==a&&(h(),a=A)}),t.observe(U,{attributes:!1,childList:!0,subtree:!0})})}function b(){return oe(this,null,function*(){if(!n.value)return;yield Ke();const W=n.value,{scrollbarWidth:U}=m(),{scrollWidth:R}=W;U>=R||requestAnimationFrame(()=>{const K=W==null?void 0:W.querySelector(".is-active");K==null||K.scrollIntoView({behavior:"smooth",inline:"start"})})})}function h(){return oe(this,null,function*(){if(!n.value)return;const{scrollbarWidth:W}=m();r.value=n.value.scrollWidth>W})}const _=Ut(({left:W,right:U})=>{d.value=W,i.value=U},100);function O({deltaY:W}){var U;(U=n.value)==null||U.scrollBy({left:W*3})}return de(()=>l.active,()=>oe(null,null,function*(){b()}),{flush:"post"}),de(()=>l.styleType,()=>{g()}),Fe(g),bt(()=>{o==null||o.disconnect(),t==null||t.disconnect(),o=null,t=null}),{handleScrollAt:_,handleWheel:O,initScrollbar:g,scrollbarRef:s,scrollDirection:f,scrollIsAtLeft:d,scrollIsAtRight:i,showScrollButton:r}}const xu={class:"flex h-full flex-1 overflow-hidden"},ku=T({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{},wheelable:{type:Boolean,default:!0}},emits:["close","sortTabs","unpin"],setup(l,{emit:o}){const t=l,a=o,s=Ne(t,a),{handleScrollAt:n,handleWheel:r,scrollbarRef:d,scrollDirection:i,scrollIsAtLeft:m,scrollIsAtRight:f,showScrollButton:g}=wu(t);function b(h){t.wheelable&&(r(h),h.stopPropagation(),h.preventDefault())}return yu(t,a),(h,_)=>(u(),x("div",xu,[Me($("span",{class:D([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(m),"pointer-events-none opacity-30":e(m)},"border-r px-2"]),onClick:_[0]||(_[0]=O=>e(i)("left"))},[p(e(Io),{class:"size-4 h-full"})],2),[[Te,e(g)]]),$("div",{class:D([{"pt-[3px]":h.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[p(e(ht),{ref_key:"scrollbarRef",ref:d,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n),onWheel:b},{default:c(()=>[h.styleType==="chrome"?(u(),k(e(gu),We(me({key:0},fe(fe(fe({},e(s)),h.$attrs),h.$props))),null,16)):(u(),k(e(uu),We(me({key:1},fe(fe(fe({},e(s)),h.$attrs),h.$props))),null,16))]),_:1},8,["onScrollAt"])],2),Me($("span",{class:D([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(f),"pointer-events-none opacity-30":e(f)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:_[1]||(_[1]=O=>e(i)("right"))},[p(e(It),{class:"size-4 h-full"})],2),[[Te,e(g)]])]))}});function Cu(){const l=st(),o=Ge(),t=Je(),a=gt(),{contentIsMaximize:s,toggleMaximize:n}=ol(),{closeAllTabs:r,closeCurrentTab:d,closeLeftTabs:i,closeOtherTabs:m,closeRightTabs:f,closeTabByKey:g,getTabDisableState:b,openTabInNewWindow:h,refreshTab:_,toggleTabPin:O}=Xa(),W=w(()=>dt(o)),{locale:U}=Ea(),R=X();de([()=>a.getTabs,()=>a.updateTime,()=>U.value],([B])=>{R.value=B.map(N=>j(N))});const K=()=>{const B=rn(l.getRoutes(),N=>{var q;return!!((q=N.meta)!=null&&q.affixTab)});a.setAffixTabs(B)},A=B=>{const{fullPath:N,path:q}=a.getTabByKey(B);l.push(N||q)},z=B=>oe(null,null,function*(){yield g(B)});function j(B){var N;return Be(fe({},B),{meta:Be(fe({},B==null?void 0:B.meta),{title:v((N=B==null?void 0:B.meta)==null?void 0:N.title)})})}return de(()=>t.accessMenus,()=>{K()},{immediate:!0}),de(()=>o.fullPath,()=>{var N,q;const B=(q=(N=o.matched)==null?void 0:N[o.matched.length-1])==null?void 0:q.meta;a.addTab(Be(fe({},o),{meta:B||o.meta}))},{immediate:!0}),{createContextMenus:B=>{var we,Ve;const{disabledCloseAll:N,disabledCloseCurrent:q,disabledCloseLeft:ae,disabledCloseOther:be,disabledCloseRight:ye,disabledRefresh:G}=b(B),Z=(Ve=(we=B==null?void 0:B.meta)==null?void 0:we.affixTab)!=null?Ve:!1;return[{disabled:q,handler:()=>oe(null,null,function*(){yield d(B)}),icon:Tt,key:"close",text:v("preferences.tabbar.contextMenu.close")},{handler:()=>oe(null,null,function*(){yield O(B)}),icon:Z?el:Vt,key:"affix",text:Z?v("preferences.tabbar.contextMenu.unpin"):v("preferences.tabbar.contextMenu.pin")},{handler:()=>oe(null,null,function*(){s.value||(yield l.push(B.fullPath)),n()}),icon:s.value?Ja:Qa,key:s.value?"restore-maximize":"maximize",text:s.value?v("preferences.tabbar.contextMenu.restoreMaximize"):v("preferences.tabbar.contextMenu.maximize")},{disabled:G,handler:()=>_(),icon:Ft,key:"reload",text:v("preferences.tabbar.contextMenu.reload")},{handler:()=>oe(null,null,function*(){yield h(B)}),icon:En,key:"open-in-new-window",separator:!0,text:v("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:ae,handler:()=>oe(null,null,function*(){yield i(B)}),icon:xn,key:"close-left",text:v("preferences.tabbar.contextMenu.closeLeft")},{disabled:ye,handler:()=>oe(null,null,function*(){yield f(B)}),icon:Cn,key:"close-right",separator:!0,text:v("preferences.tabbar.contextMenu.closeRight")},{disabled:be,handler:()=>oe(null,null,function*(){yield m(B)}),icon:Ln,key:"close-other",text:v("preferences.tabbar.contextMenu.closeOther")},{disabled:N,handler:r,icon:kn,key:"close-all",text:v("preferences.tabbar.contextMenu.closeAll")}].filter(Ae=>a.getMenuList.includes(Ae.key))},currentActive:W,currentTabs:R,handleClick:A,handleClose:z}}const Su={class:"flex-center h-full"},Mu=T({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(l){const o=Ge(),t=gt(),{contentIsMaximize:a,toggleMaximize:s}=ol(),{unpinTab:n}=Xa(),{createContextMenus:r,currentActive:d,currentTabs:i,handleClick:m,handleClose:f}=Cu(),g=w(()=>{const b=t.getTabByKey(d.value);return r(b).map(_=>Be(fe({},_),{label:_.text,value:_.key}))});return L.tabbar.persist||t.closeOtherTabs(o),(b,h)=>(u(),x(Y,null,[p(e(ku),{active:e(d),class:D(b.theme),"context-menus":e(r),draggable:e(L).tabbar.draggable,"show-icon":b.showIcon,"style-type":e(L).tabbar.styleType,tabs:e(i),wheelable:e(L).tabbar.wheelable,"middle-click-to-close":e(L).tabbar.middleClickToClose,onClose:e(f),onSortTabs:e(t).sortTabs,onUnpin:e(n),"onUpdate:active":e(m)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","wheelable","middle-click-to-close","onClose","onSortTabs","onUnpin","onUpdate:active"]),$("div",Su,[e(L).tabbar.showMore?(u(),k(e(lu),{key:0,menus:g.value},null,8,["menus"])):H("",!0),e(L).tabbar.showMaximize?(u(),k(e(ou),{key:1,screen:e(a),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):H("",!0)])],64))}}),Nu=T({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout","clickLogo"],setup(l,{emit:o}){const t=o,{isDark:a,isHeaderNav:s,isMixedNav:n,isMobile:r,isSideMixedNav:d,isHeaderMixedNav:i,isHeaderSidebarNav:m,layout:f,preferencesButtonPosition:g,sidebarCollapsed:b,theme:h}=Ze(),_=Je(),{refresh:O}=nl(),W=w(()=>a.value||L.theme.semiDarkSidebar?"dark":"light"),U=w(()=>a.value||L.theme.semiDarkHeader?"dark":"light"),R=w(()=>{const{collapsedShowTitle:se}=L.sidebar,pe=[];return se&&b.value&&!n.value&&pe.push("mx-auto"),d.value&&pe.push("flex-center"),pe.join(" ")}),K=w(()=>L.navigation.styleType==="rounded"),A=w(()=>r.value&&b.value?!0:s.value||n.value||m.value?!1:b.value||d.value||i.value),z=w(()=>!r.value&&(s.value||n.value||i.value)),{handleMenuSelect:j,handleMenuOpen:E,headerActive:B,headerMenus:N,sidebarActive:q,sidebarMenus:ae,mixHeaderMenus:be,sidebarVisible:ye}=tu(),{extraActiveMenu:G,extraMenus:Z,handleDefaultSelect:ve,handleMenuMouseEnter:we,handleMixedMenuSelect:Ve,handleSideMouseLeave:Ae,sidebarExtraVisible:_e}=eu(be);function F(se,pe=!0){return pe?Oa(se,ce=>Be(fe({},ka(ce)),{name:v(ce.name)})):se.map(ce=>Be(fe({},ka(ce)),{name:v(ce.name)}))}function ee(){Re({sidebar:{hidden:!L.sidebar.hidden}})}function te(){t("clearPreferencesAndLogout")}function ge(){t("clickLogo")}de(()=>L.app.layout,se=>oe(null,null,function*(){se==="sidebar-mixed-nav"&&L.sidebar.hidden&&Re({sidebar:{hidden:!1}})})),de(Ao.global.locale,O,{flush:"post"});const $e=Ie(),ze=w(()=>Object.keys($e).filter(se=>se.startsWith("header-")));return(se,pe)=>(u(),k(e(Bd),{"sidebar-extra-visible":e(_e),"onUpdate:sidebarExtraVisible":pe[0]||(pe[0]=ce=>Ot(_e)?_e.value=ce:null),"content-compact":e(L).app.contentCompact,"content-compact-width":e(L).app.contentCompactWidth,"content-padding":e(L).app.contentPadding,"content-padding-bottom":e(L).app.contentPaddingBottom,"content-padding-left":e(L).app.contentPaddingLeft,"content-padding-right":e(L).app.contentPaddingRight,"content-padding-top":e(L).app.contentPaddingTop,"footer-enable":e(L).footer.enable,"footer-fixed":e(L).footer.fixed,"footer-height":e(L).footer.height,"header-height":e(L).header.height,"header-hidden":e(L).header.hidden,"header-mode":e(L).header.mode,"header-theme":U.value,"header-toggle-sidebar-button":e(L).widget.sidebarToggle,"header-visible":e(L).header.enable,"is-mobile":e(L).app.isMobile,layout:e(f),"sidebar-collapse":e(L).sidebar.collapsed,"sidebar-collapse-show-title":e(L).sidebar.collapsedShowTitle,"sidebar-enable":e(ye),"sidebar-collapsed-button":e(L).sidebar.collapsedButton,"sidebar-fixed-button":e(L).sidebar.fixedButton,"sidebar-expand-on-hover":e(L).sidebar.expandOnHover,"sidebar-extra-collapse":e(L).sidebar.extraCollapse,"sidebar-extra-collapsed-width":e(L).sidebar.extraCollapsedWidth,"sidebar-hidden":e(L).sidebar.hidden,"sidebar-mixed-width":e(L).sidebar.mixedWidth,"sidebar-theme":W.value,"sidebar-width":e(L).sidebar.width,"side-collapse-width":e(L).sidebar.collapseWidth,"tabbar-enable":e(L).tabbar.enable,"tabbar-height":e(L).tabbar.height,"z-index":e(L).app.zIndex,onSideMouseLeave:e(Ae),onToggleSidebar:ee,"onUpdate:sidebarCollapse":pe[1]||(pe[1]=ce=>e(Re)({sidebar:{collapsed:ce}})),"onUpdate:sidebarEnable":pe[2]||(pe[2]=ce=>e(Re)({sidebar:{enable:ce}})),"onUpdate:sidebarExpandOnHover":pe[3]||(pe[3]=ce=>e(Re)({sidebar:{expandOnHover:ce}})),"onUpdate:sidebarExtraCollapse":pe[4]||(pe[4]=ce=>e(Re)({sidebar:{extraCollapse:ce}}))},ot({logo:c(()=>[e(L).logo.enable?(u(),k(e(Ma),{key:0,fit:e(L).logo.fit,class:D(R.value),collapsed:A.value,src:e(L).logo.source,text:e(L).app.name,theme:z.value?U.value:e(h),onClick:ge},ot({_:2},[se.$slots["logo-text"]?{name:"text",fn:c(()=>[P(se.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","class","collapsed","src","text","theme"])):H("",!0)]),header:c(()=>[p(e(Nd),{theme:e(h),onClearPreferencesAndLogout:te},ot({"user-dropdown":c(()=>[P(se.$slots,"user-dropdown")]),notification:c(()=>[P(se.$slots,"notification")]),_:2},[!z.value&&e(L).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[p(e(Ws),{"hide-when-only-one":e(L).breadcrumb.hideOnlyOne,"show-home":e(L).breadcrumb.showHome,"show-icon":e(L).breadcrumb.showIcon,type:e(L).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,z.value?{name:"menu",fn:c(()=>[p(e(Va),{"default-active":e(B),menus:F(e(N)),rounded:K.value,theme:U.value,class:"w-full",mode:"horizontal",onSelect:e(j)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,le(ze.value,ce=>({name:ce,fn:c(()=>[P(se.$slots,ce)])}))]),1032,["theme"])]),menu:c(()=>[p(e(Va),{accordion:e(L).navigation.accordion,collapse:e(L).sidebar.collapsed,"collapse-show-title":e(L).sidebar.collapsedShowTitle,"default-active":e(q),menus:F(e(ae)),rounded:K.value,theme:W.value,mode:"vertical",onOpen:e(E),onSelect:e(j)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onOpen","onSelect"])]),"mixed-menu":c(()=>[p(e(Jd),{"active-path":e(G),menus:F(e(be),!1),rounded:K.value,theme:W.value,onDefaultSelect:e(ve),onEnter:e(we),onSelect:e(Ve)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[p(e(Zd),{accordion:e(L).navigation.accordion,collapse:e(L).sidebar.extraCollapse,menus:F(e(Z)),rounded:K.value,theme:W.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(L).logo.enable?(u(),k(e(Ma),{key:0,fit:e(L).logo.fit,text:e(L).app.name,theme:e(h)},ot({_:2},[se.$slots["logo-text"]?{name:"text",fn:c(()=>[P(se.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","text","theme"])):H("",!0)]),tabbar:c(()=>[e(L).tabbar.enable?(u(),k(e(Mu),{key:0,"show-icon":e(L).tabbar.showIcon,theme:e(h)},null,8,["show-icon","theme"])):H("",!0)]),content:c(()=>[p(e(zd))]),extra:c(()=>[P(se.$slots,"extra"),e(L).app.enableCheckUpdates?(u(),k(e(Ns),{key:0,"check-updates-interval":e(L).app.checkUpdatesInterval},null,8,["check-updates-interval"])):H("",!0),e(L).widget.lockScreen?(u(),k(nt,{key:1,name:"slide-up"},{default:c(()=>[e(_).isLockScreen?P(se.$slots,"lock-screen",{key:0}):H("",!0)]),_:3})):H("",!0),e(g).fixed?(u(),k(e(il),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:te})):H("",!0),p(e(cs))]),_:2},[e(L).transition.loading?{name:"content-overlay",fn:c(()=>[p(e(Hd))]),key:"0"}:void 0,e(L).footer.enable?{name:"footer",fn:c(()=>[p(e(Id),null,{default:c(()=>[e(L).copyright.enable?(u(),k(e(hn),We(me({key:0},e(L).copyright)),null,16)):H("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","content-compact-width","content-padding","content-padding-bottom","content-padding-left","content-padding-right","content-padding-top","footer-enable","footer-fixed","footer-height","header-height","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-collapsed-button","sidebar-fixed-button","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-extra-collapsed-width","sidebar-hidden","sidebar-mixed-width","sidebar-theme","sidebar-width","side-collapse-width","tabbar-enable","tabbar-height","z-index","onSideMouseLeave"]))}});export{Ou as N,Nu as _,Ed as a,Ws as b,Ns as c,dr as d,Au as e,$r as f,il as g,hd as h,Wu as i};
