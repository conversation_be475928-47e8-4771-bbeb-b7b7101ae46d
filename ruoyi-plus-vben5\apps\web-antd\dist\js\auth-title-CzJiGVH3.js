import{z as o}from"./bootstrap-DCMzVRvD.js";import{c as r,o as c,j as e,r as s}from"../jse/index-index-C-MnMZEz.js";const a={},l={class:"mb-7 sm:mx-auto sm:w-full sm:max-w-md"},n={class:"text-foreground mb-3 text-3xl font-bold leading-9 tracking-tight lg:text-4xl"},d={class:"text-muted-foreground lg:text-md text-sm"};function m(t,i){return c(),r("div",l,[e("h2",n,[s(t.$slots,"default")]),e("p",d,[s(t.$slots,"desc")])])}const x=o(a,[["render",m]]);export{x as T};
