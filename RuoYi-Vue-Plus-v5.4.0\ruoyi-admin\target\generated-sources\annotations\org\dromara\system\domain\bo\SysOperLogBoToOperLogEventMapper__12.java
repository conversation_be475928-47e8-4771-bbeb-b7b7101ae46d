package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__12;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOperLogBoToSysOperLogMapper__12.class,OperLogEventToSysOperLogBoMapper__12.class},
    imports = {}
)
public interface SysOperLogBoToOperLogEventMapper__12 extends BaseMapper<SysOperLogBo, OperLogEvent> {
}
