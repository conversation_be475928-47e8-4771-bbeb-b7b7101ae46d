import{aC as It,_ as p,bj as De,h as ye,bx as he,aE as lt,T as St,g as k,aY as _e,aM as Ct,aX as it,cN as Pe,aZ as xt,b9 as Mt,c as Ie,a_ as wt,ax as Kt,dM as Le,aL as at,k as Ot,c4 as Pt,j as Tt,m as Ne,dN as Bt,b1 as kt,n as Ve,r as Et,cU as Dt,p as _t,dO as At}from"./bootstrap-DCMzVRvD.js";import{s as fe}from"./shallowequal-DdADXzCF.js";import{a as v,a4 as me,B as m,a5 as ae,d as J,$ as We,z as rt,C as R,ac as Se,q as G,p as F,T as zt,F as ke,X as Xe,v as Rt,D as Te,aC as Ht,b as be}from"../jse/index-index-C-MnMZEz.js";import{u as Be}from"./uniq-CCKK3QTo.js";import{O as pe}from"./Overflow-DmNzxpBy.js";import{i as Ge}from"./slide-B82O6h2Y.js";var Ft={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};function Ye(t){for(var n=1;n<arguments.length;n++){var e=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(e).filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),o.forEach(function(l){jt(t,l,e[l])})}return t}function jt(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}var Ae=function(n,e){var o=Ye({},n,e.attrs);return v(It,Ye({},o,{icon:Ft}),null)};Ae.displayName="EllipsisOutlined";Ae.inheritAttrs=!1;const ut=Symbol("OverrideContextKey"),st=()=>ae(ut,void 0),gn=t=>{var n,e,o;const{prefixCls:l,mode:u,selectable:s,validator:r,onClick:c,expandIcon:f}=st()||{};me(ut,{prefixCls:m(()=>{var d,i;return(i=(d=t.prefixCls)===null||d===void 0?void 0:d.value)!==null&&i!==void 0?i:l==null?void 0:l.value}),mode:m(()=>{var d,i;return(i=(d=t.mode)===null||d===void 0?void 0:d.value)!==null&&i!==void 0?i:u==null?void 0:u.value}),selectable:m(()=>{var d,i;return(i=(d=t.selectable)===null||d===void 0?void 0:d.value)!==null&&i!==void 0?i:s==null?void 0:s.value}),validator:(n=t.validator)!==null&&n!==void 0?n:r,onClick:(e=t.onClick)!==null&&e!==void 0?e:c,expandIcon:(o=t.expandIcon)!==null&&o!==void 0?o:f==null?void 0:f.value})},ct=Symbol("menuContextKey"),dt=t=>{me(ct,t)},Q=()=>ae(ct),mt=Symbol("ForceRenderKey"),Lt=t=>{me(mt,t)},vt=()=>ae(mt,!1),ft=Symbol("menuFirstLevelContextKey"),pt=t=>{me(ft,t)},Nt=()=>ae(ft,!0),Ce=J({compatConfig:{MODE:3},name:"MenuContextProvider",inheritAttrs:!1,props:{mode:{type:String,default:void 0},overflowDisabled:{type:Boolean,default:void 0}},setup(t,n){let{slots:e}=n;const o=Q(),l=p({},o);return t.mode!==void 0&&(l.mode=We(t,"mode")),t.overflowDisabled!==void 0&&(l.overflowDisabled=We(t,"overflowDisabled")),dt(l),()=>{var u;return(u=e.default)===null||u===void 0?void 0:u.call(e)}}}),Vt=Symbol("siderCollapsed"),$e="$$__vc-menu-more__key",gt=Symbol("KeyPathContext"),ze=()=>ae(gt,{parentEventKeys:m(()=>[]),parentKeys:m(()=>[]),parentInfo:{}}),Wt=(t,n,e)=>{const{parentEventKeys:o,parentKeys:l}=ze(),u=m(()=>[...o.value,t]),s=m(()=>[...l.value,n]);return me(gt,{parentEventKeys:u,parentKeys:s,parentInfo:e}),s},bt=Symbol("measure"),Ue=J({compatConfig:{MODE:3},setup(t,n){let{slots:e}=n;return me(bt,!0),()=>{var o;return(o=e.default)===null||o===void 0?void 0:o.call(e)}}}),Re=()=>ae(bt,!1);function $t(t){const{mode:n,rtl:e,inlineIndent:o}=Q();return m(()=>n.value!=="inline"?null:e.value?{paddingRight:`${t.value*o.value}px`}:{paddingLeft:`${t.value*o.value}px`})}let Xt=0;const Gt=()=>({id:String,role:String,disabled:Boolean,danger:Boolean,title:{type:[String,Boolean],default:void 0},icon:ye.any,onMouseenter:Function,onMouseleave:Function,onClick:Function,onKeydown:Function,onFocus:Function,originItemValue:De()}),ge=J({compatConfig:{MODE:3},name:"AMenuItem",inheritAttrs:!1,props:Gt(),slots:Object,setup(t,n){let{slots:e,emit:o,attrs:l}=n;const u=rt(),s=Re(),r=typeof u.vnode.key=="symbol"?String(u.vnode.key):u.vnode.key;he(typeof u.vnode.key!="symbol","MenuItem",`MenuItem \`:key="${String(r)}"\` not support Symbol type`);const c=`menu_item_${++Xt}_$$_${r}`,{parentEventKeys:f,parentKeys:d}=ze(),{prefixCls:i,activeKeys:I,disabled:$,changeActiveKeys:g,rtl:C,inlineCollapsed:x,siderCollapsed:T,onItemClick:K,selectedKeys:j,registerMenuInfo:q,unRegisterMenuInfo:h}=Q(),O=Nt(),z=R(!1),A=m(()=>[...d.value,r]);q(c,{eventKey:c,key:r,parentEventKeys:f,parentKeys:d,isLeaf:!0}),Se(()=>{h(c)}),G(I,()=>{z.value=!!I.value.find(y=>y===r)},{immediate:!0});const P=m(()=>$.value||t.disabled),V=m(()=>j.value.includes(r)),W=m(()=>{const y=`${i.value}-item`;return{[`${y}`]:!0,[`${y}-danger`]:t.danger,[`${y}-active`]:z.value,[`${y}-selected`]:V.value,[`${y}-disabled`]:P.value}}),Y=y=>({key:r,eventKey:c,keyPath:A.value,eventKeyPath:[...f.value,c],domEvent:y,item:p(p({},t),l)}),re=y=>{if(P.value)return;const E=Y(y);o("click",y),K(E)},ue=y=>{P.value||(g(A.value),o("mouseenter",y))},ee=y=>{P.value||(g([]),o("mouseleave",y))},te=y=>{if(o("keydown",y),y.which===Ct.ENTER){const E=Y(y);o("click",y),K(E)}},se=y=>{g(A.value),o("focus",y)},L=(y,E)=>{const H=v("span",{class:`${i.value}-title-content`},[E]);return(!y||it(E)&&E.type==="span")&&E&&x.value&&O&&typeof E=="string"?v("div",{class:`${i.value}-inline-collapsed-noicon`},[E.charAt(0)]):H},ve=$t(m(()=>A.value.length));return()=>{var y,E,H,Z,ne;if(s)return null;const U=(y=t.title)!==null&&y!==void 0?y:(E=e.title)===null||E===void 0?void 0:E.call(e),a=lt((H=e.default)===null||H===void 0?void 0:H.call(e)),b=a.length;let M=U;typeof U=="undefined"?M=O&&b?a:"":U===!1&&(M="");const B={title:M};!T.value&&!x.value&&(B.title=null,B.open=!1);const D={};t.role==="option"&&(D["aria-selected"]=V.value);const w=(Z=t.icon)!==null&&Z!==void 0?Z:(ne=e.icon)===null||ne===void 0?void 0:ne.call(e,t);return v(St,k(k({},B),{},{placement:C.value?"left":"right",overlayClassName:`${i.value}-inline-collapsed-tooltip`}),{default:()=>[v(pe.Item,k(k(k({component:"li"},l),{},{id:t.id,style:p(p({},l.style||{}),ve.value),class:[W.value,{[`${l.class}`]:!!l.class,[`${i.value}-item-only-child`]:(w?b+1:b)===1}],role:t.role||"menuitem",tabindex:t.disabled?null:-1,"data-menu-id":r,"aria-disabled":t.disabled},D),{},{onMouseenter:ue,onMouseleave:ee,onClick:re,onKeydown:te,onFocus:se,title:typeof U=="string"?U:void 0}),{default:()=>[_e(typeof w=="function"?w(t.originItemValue):w,{class:`${i.value}-item-icon`},!1),L(w,a)]})]})}}}),le={adjustX:1,adjustY:1},Yt={topLeft:{points:["bl","tl"],overflow:le,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:le,offset:[0,7]},leftTop:{points:["tr","tl"],overflow:le,offset:[-4,0]},rightTop:{points:["tl","tr"],overflow:le,offset:[4,0]}},Ut={topLeft:{points:["bl","tl"],overflow:le,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:le,offset:[0,7]},rightTop:{points:["tr","tl"],overflow:le,offset:[-4,0]},leftTop:{points:["tl","tr"],overflow:le,offset:[4,0]}},qt={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"},qe=J({compatConfig:{MODE:3},name:"PopupTrigger",inheritAttrs:!1,props:{prefixCls:String,mode:String,visible:Boolean,popupClassName:String,popupOffset:Array,disabled:Boolean,onVisibleChange:Function},slots:Object,emits:["visibleChange"],setup(t,n){let{slots:e,emit:o}=n;const l=R(!1),{getPopupContainer:u,rtl:s,subMenuOpenDelay:r,subMenuCloseDelay:c,builtinPlacements:f,triggerSubMenuAction:d,forceSubMenuRender:i,motion:I,defaultMotions:$,rootClassName:g}=Q(),C=vt(),x=m(()=>s.value?p(p({},Ut),f.value):p(p({},Yt),f.value)),T=m(()=>qt[t.mode]),K=R();G(()=>t.visible,h=>{Pe.cancel(K.value),K.value=Pe(()=>{l.value=h})},{immediate:!0}),Se(()=>{Pe.cancel(K.value)});const j=h=>{o("visibleChange",h)},q=m(()=>{var h,O;const z=I.value||((h=$.value)===null||h===void 0?void 0:h[t.mode])||((O=$.value)===null||O===void 0?void 0:O.other),A=typeof z=="function"?z():z;return A?xt(A.name,{css:!0}):void 0});return()=>{const{prefixCls:h,popupClassName:O,mode:z,popupOffset:A,disabled:N}=t;return v(Mt,{prefixCls:h,popupClassName:Ie(`${h}-popup`,{[`${h}-rtl`]:s.value},O,g.value),stretch:z==="horizontal"?"minWidth":null,getPopupContainer:u.value,builtinPlacements:x.value,popupPlacement:T.value,popupVisible:l.value,popupAlign:A&&{offset:A},action:N?[]:[d.value],mouseEnterDelay:r.value,mouseLeaveDelay:c.value,onPopupVisibleChange:j,forceRender:C||i.value,popupAnimation:q.value},{popup:e.popup,default:e.default})}}}),He=(t,n)=>{let{slots:e,attrs:o}=n;var l;const{prefixCls:u,mode:s}=Q();return v("ul",k(k({},o),{},{class:Ie(u.value,`${u.value}-sub`,`${u.value}-${s.value==="inline"?"inline":"vertical"}`),"data-menu-list":!0}),[(l=e.default)===null||l===void 0?void 0:l.call(e)])};He.displayName="SubMenuList";const Zt=J({compatConfig:{MODE:3},name:"InlineSubMenuList",inheritAttrs:!1,props:{id:String,open:Boolean,keyPath:Array},setup(t,n){let{slots:e}=n;const o=m(()=>"inline"),{motion:l,mode:u,defaultMotions:s}=Q(),r=m(()=>u.value===o.value),c=F(!r.value),f=m(()=>r.value?t.open:!1);G(u,()=>{r.value&&(c.value=!1)},{flush:"post"});const d=m(()=>{var i,I;const $=l.value||((i=s.value)===null||i===void 0?void 0:i[o.value])||((I=s.value)===null||I===void 0?void 0:I.other),g=typeof $=="function"?$():$;return p(p({},g),{appear:t.keyPath.length<=1})});return()=>{var i;return c.value?null:v(Ce,{mode:o.value},{default:()=>[v(wt,d.value,{default:()=>[zt(v(He,{id:t.id},{default:()=>[(i=e.default)===null||i===void 0?void 0:i.call(e)]}),[[Kt,f.value]])]})]})}}});let Ze=0;const Jt=()=>({icon:ye.any,title:ye.any,disabled:Boolean,level:Number,popupClassName:String,popupOffset:Array,internalPopupClose:Boolean,eventKey:String,expandIcon:Function,theme:String,onMouseenter:Function,onMouseleave:Function,onTitleClick:Function,originItemValue:De()}),de=J({compatConfig:{MODE:3},name:"ASubMenu",inheritAttrs:!1,props:Jt(),slots:Object,setup(t,n){let{slots:e,attrs:o,emit:l}=n;var u,s;pt(!1);const r=Re(),c=rt(),f=typeof c.vnode.key=="symbol"?String(c.vnode.key):c.vnode.key;he(typeof c.vnode.key!="symbol","SubMenu",`SubMenu \`:key="${String(f)}"\` not support Symbol type`);const d=Le(f)?f:`sub_menu_${++Ze}_$$_not_set_key`,i=(u=t.eventKey)!==null&&u!==void 0?u:Le(f)?`sub_menu_${++Ze}_$$_${f}`:d,{parentEventKeys:I,parentInfo:$,parentKeys:g}=ze(),C=m(()=>[...g.value,d]),x=R([]),T={eventKey:i,key:d,parentEventKeys:I,childrenEventKeys:x,parentKeys:g};(s=$.childrenEventKeys)===null||s===void 0||s.value.push(i),Se(()=>{var S;$.childrenEventKeys&&($.childrenEventKeys.value=(S=$.childrenEventKeys)===null||S===void 0?void 0:S.value.filter(_=>_!=i))}),Wt(i,d,T);const{prefixCls:K,activeKeys:j,disabled:q,changeActiveKeys:h,mode:O,inlineCollapsed:z,openKeys:A,overflowDisabled:N,onOpenChange:P,registerMenuInfo:V,unRegisterMenuInfo:W,selectedSubMenuKeys:Y,expandIcon:re,theme:ue}=Q(),ee=f!=null,te=!r&&(vt()||!ee);Lt(te),(r&&ee||!r&&!ee||te)&&(V(i,T),Se(()=>{W(i)}));const se=m(()=>`${K.value}-submenu`),L=m(()=>q.value||t.disabled),ve=R(),y=R(),E=m(()=>A.value.includes(d)),H=m(()=>!N.value&&E.value),Z=m(()=>Y.value.includes(d)),ne=R(!1);G(j,()=>{ne.value=!!j.value.find(S=>S===d)},{immediate:!0});const U=S=>{L.value||(l("titleClick",S,d),O.value==="inline"&&P(d,!E.value))},a=S=>{L.value||(h(C.value),l("mouseenter",S))},b=S=>{L.value||(h([]),l("mouseleave",S))},M=$t(m(()=>C.value.length)),B=S=>{O.value!=="inline"&&P(d,S)},D=()=>{h(C.value)},w=i&&`${i}-popup`,X=m(()=>Ie(K.value,`${K.value}-${t.theme||ue.value}`,t.popupClassName)),ce=(S,_)=>{if(!_)return z.value&&!g.value.length&&S&&typeof S=="string"?v("div",{class:`${K.value}-inline-collapsed-noicon`},[S.charAt(0)]):v("span",{class:`${K.value}-title-content`},[S]);const oe=it(S)&&S.type==="span";return v(ke,null,[_e(typeof _=="function"?_(t.originItemValue):_,{class:`${K.value}-item-icon`},!1),oe?S:v("span",{class:`${K.value}-title-content`},[S])])},we=m(()=>O.value!=="inline"&&C.value.length>1?"vertical":O.value),yt=m(()=>O.value==="horizontal"?"vertical":O.value),ht=m(()=>we.value==="horizontal"?"vertical":we.value),Fe=()=>{var S,_;const oe=se.value,Ke=(S=t.icon)!==null&&S!==void 0?S:(_=e.icon)===null||_===void 0?void 0:_.call(e,t),je=t.expandIcon||e.expandIcon||re.value,Oe=ce(at(e,t,"title"),Ke);return v("div",{style:M.value,class:`${oe}-title`,tabindex:L.value?null:-1,ref:ve,title:typeof Oe=="string"?Oe:null,"data-menu-id":d,"aria-expanded":H.value,"aria-haspopup":!0,"aria-controls":w,"aria-disabled":L.value,onClick:U,onFocus:D},[Oe,O.value!=="horizontal"&&je?je(p(p({},t),{isOpen:H.value})):v("i",{class:`${oe}-arrow`},null)])};return()=>{var S;if(r)return ee?(S=e.default)===null||S===void 0?void 0:S.call(e):null;const _=se.value;let oe=()=>null;if(!N.value&&O.value!=="inline"){const Ke=O.value==="horizontal"?[0,8]:[10,0];oe=()=>v(qe,{mode:we.value,prefixCls:_,visible:!t.internalPopupClose&&H.value,popupClassName:X.value,popupOffset:t.popupOffset||Ke,disabled:L.value,onVisibleChange:B},{default:()=>[Fe()],popup:()=>v(Ce,{mode:ht.value},{default:()=>[v(He,{id:w,ref:y},{default:e.default})]})})}else oe=()=>v(qe,null,{default:Fe});return v(Ce,{mode:yt.value},{default:()=>[v(pe.Item,k(k({component:"li"},o),{},{role:"none",class:Ie(_,`${_}-${O.value}`,o.class,{[`${_}-open`]:H.value,[`${_}-active`]:ne.value,[`${_}-selected`]:Z.value,[`${_}-disabled`]:L.value}),onMouseenter:a,onMouseleave:b,"data-submenu-id":d}),{default:()=>v(ke,null,[oe(),!N.value&&v(Zt,{id:w,open:H.value,keyPath:C.value},{default:e.default})])})]})}}}),Qt=()=>({title:ye.any,originItemValue:De()}),xe=J({compatConfig:{MODE:3},name:"AMenuItemGroup",inheritAttrs:!1,props:Qt(),slots:Object,setup(t,n){let{slots:e,attrs:o}=n;const{prefixCls:l}=Q(),u=m(()=>`${l.value}-item-group`),s=Re();return()=>{var r,c;return s?(r=e.default)===null||r===void 0?void 0:r.call(e):v("li",k(k({},o),{},{onClick:f=>f.stopPropagation(),class:u.value}),[v("div",{title:typeof t.title=="string"?t.title:void 0,class:`${u.value}-title`},[at(e,t,"title")]),v("ul",{class:`${u.value}-list`},[(c=e.default)===null||c===void 0?void 0:c.call(e)])])}}}),en=()=>({prefixCls:String,dashed:Boolean}),Me=J({compatConfig:{MODE:3},name:"AMenuDivider",props:en(),setup(t){const{prefixCls:n}=Q(),e=m(()=>({[`${n.value}-item-divider`]:!0,[`${n.value}-item-divider-dashed`]:!!t.dashed}));return()=>v("li",{class:e.value},null)}});var tn=function(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(t);l<o.length;l++)n.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(t,o[l])&&(e[o[l]]=t[o[l]]);return e};function Ee(t,n,e){return(t||[]).map((o,l)=>{if(o&&typeof o=="object"){const u=o,{label:s,children:r,key:c,type:f}=u,d=tn(u,["label","children","key","type"]),i=c!=null?c:`tmp-${l}`,I=e?e.parentKeys.slice():[],$=[],g={eventKey:i,key:i,parentEventKeys:F(I),parentKeys:F(I),childrenEventKeys:F($),isLeaf:!1};if(r||f==="group"){if(f==="group"){const x=Ee(r,n,e);return v(xe,k(k({key:i},d),{},{title:s,originItemValue:o}),{default:()=>[x]})}n.set(i,g),e&&e.childrenEventKeys.push(i);const C=Ee(r,n,{childrenEventKeys:$,parentKeys:[].concat(I,i)});return v(de,k(k({key:i},d),{},{title:s,originItemValue:o}),{default:()=>[C]})}return f==="divider"?v(Me,k({key:i},d),null):(g.isLeaf=!0,n.set(i,g),v(ge,k(k({key:i},d),{},{originItemValue:o}),{default:()=>[s]}))}return null}).filter(o=>o)}function nn(t){const n=R([]),e=R(!1),o=R(new Map);return G(()=>t.items,()=>{const l=new Map;e.value=!1,t.items?(e.value=!0,n.value=Ee(t.items,l)):n.value=void 0,o.value=l},{immediate:!0,deep:!0}),{itemsNodes:n,store:o,hasItmes:e}}const on=t=>{const{componentCls:n,motionDurationSlow:e,menuHorizontalHeight:o,colorSplit:l,lineWidth:u,lineType:s,menuItemPaddingInline:r}=t;return{[`${n}-horizontal`]:{lineHeight:`${o}px`,border:0,borderBottom:`${u}px ${s} ${l}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${n}-item, ${n}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:r},[`> ${n}-item:hover,
        > ${n}-item-active,
        > ${n}-submenu ${n}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${n}-item, ${n}-submenu-title`]:{transition:[`border-color ${e}`,`background ${e}`].join(",")},[`${n}-submenu-arrow`]:{display:"none"}}}},ln=t=>{let{componentCls:n,menuArrowOffset:e}=t;return{[`${n}-rtl`]:{direction:"rtl"},[`${n}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${n}-rtl${n}-vertical,
    ${n}-submenu-rtl ${n}-vertical`]:{[`${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(-${e})`},"&::after":{transform:`rotate(45deg) translateY(${e})`}}}}},Je=t=>p({},Ot(t)),Qe=(t,n)=>{const{componentCls:e,colorItemText:o,colorItemTextSelected:l,colorGroupTitle:u,colorItemBg:s,colorSubItemBg:r,colorItemBgSelected:c,colorActiveBarHeight:f,colorActiveBarWidth:d,colorActiveBarBorderSize:i,motionDurationSlow:I,motionEaseInOut:$,motionEaseOut:g,menuItemPaddingInline:C,motionDurationMid:x,colorItemTextHover:T,lineType:K,colorSplit:j,colorItemTextDisabled:q,colorDangerItemText:h,colorDangerItemTextHover:O,colorDangerItemTextSelected:z,colorDangerItemBgActive:A,colorDangerItemBgSelected:N,colorItemBgHover:P,menuSubMenuBg:V,colorItemTextSelectedHorizontal:W,colorItemBgSelectedHorizontal:Y}=t;return{[`${e}-${n}`]:{color:o,background:s,[`&${e}-root:focus-visible`]:p({},Je(t)),[`${e}-item-group-title`]:{color:u},[`${e}-submenu-selected`]:{[`> ${e}-submenu-title`]:{color:l}},[`${e}-item-disabled, ${e}-submenu-disabled`]:{color:`${q} !important`},[`${e}-item:hover, ${e}-submenu-title:hover`]:{[`&:not(${e}-item-selected):not(${e}-submenu-selected)`]:{color:T}},[`&:not(${e}-horizontal)`]:{[`${e}-item:not(${e}-item-selected)`]:{"&:hover":{backgroundColor:P},"&:active":{backgroundColor:c}},[`${e}-submenu-title`]:{"&:hover":{backgroundColor:P},"&:active":{backgroundColor:c}}},[`${e}-item-danger`]:{color:h,[`&${e}-item:hover`]:{[`&:not(${e}-item-selected):not(${e}-submenu-selected)`]:{color:O}},[`&${e}-item:active`]:{background:A}},[`${e}-item a`]:{"&, &:hover":{color:"inherit"}},[`${e}-item-selected`]:{color:l,[`&${e}-item-danger`]:{color:z},"a, a:hover":{color:"inherit"}},[`& ${e}-item-selected`]:{backgroundColor:c,[`&${e}-item-danger`]:{backgroundColor:N}},[`${e}-item, ${e}-submenu-title`]:{[`&:not(${e}-item-disabled):focus-visible`]:p({},Je(t))},[`&${e}-submenu > ${e}`]:{backgroundColor:V},[`&${e}-popup > ${e}`]:{backgroundColor:s},[`&${e}-horizontal`]:p(p({},n==="dark"?{borderBottom:0}:{}),{[`> ${e}-item, > ${e}-submenu`]:{top:i,marginTop:-i,marginBottom:0,borderRadius:0,"&::after":{position:"absolute",insetInline:C,bottom:0,borderBottom:`${f}px solid transparent`,transition:`border-color ${I} ${$}`,content:'""'},"&:hover, &-active, &-open":{"&::after":{borderBottomWidth:f,borderBottomColor:W}},"&-selected":{color:W,backgroundColor:Y,"&::after":{borderBottomWidth:f,borderBottomColor:W}}}}),[`&${e}-root`]:{[`&${e}-inline, &${e}-vertical`]:{borderInlineEnd:`${i}px ${K} ${j}`}},[`&${e}-inline`]:{[`${e}-sub${e}-inline`]:{background:r},[`${e}-item, ${e}-submenu-title`]:i&&d?{width:`calc(100% + ${i}px)`}:{},[`${e}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${d}px solid ${l}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${x} ${g}`,`opacity ${x} ${g}`].join(","),content:'""'},[`&${e}-item-danger`]:{"&::after":{borderInlineEndColor:z}}},[`${e}-selected, ${e}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${x} ${$}`,`opacity ${x} ${$}`].join(",")}}}}}},et=t=>{const{componentCls:n,menuItemHeight:e,itemMarginInline:o,padding:l,menuArrowSize:u,marginXS:s,marginXXS:r}=t,c=l+u+s;return{[`${n}-item`]:{position:"relative"},[`${n}-item, ${n}-submenu-title`]:{height:e,lineHeight:`${e}px`,paddingInline:l,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:r,width:`calc(100% - ${o*2}px)`},[`${n}-submenu`]:{paddingBottom:.02},[`> ${n}-item,
            > ${n}-submenu > ${n}-submenu-title`]:{height:e,lineHeight:`${e}px`},[`${n}-item-group-list ${n}-submenu-title,
            ${n}-submenu-title`]:{paddingInlineEnd:c}}},an=t=>{const{componentCls:n,iconCls:e,menuItemHeight:o,colorTextLightSolid:l,dropdownWidth:u,controlHeightLG:s,motionDurationMid:r,motionEaseOut:c,paddingXL:f,fontSizeSM:d,fontSizeLG:i,motionDurationSlow:I,paddingXS:$,boxShadowSecondary:g}=t,C={height:o,lineHeight:`${o}px`,listStylePosition:"inside",listStyleType:"disc"};return[{[n]:{"&-inline, &-vertical":p({[`&${n}-root`]:{boxShadow:"none"}},et(t))},[`${n}-submenu-popup`]:{[`${n}-vertical`]:p(p({},et(t)),{boxShadow:g})}},{[`${n}-submenu-popup ${n}-vertical${n}-sub`]:{minWidth:u,maxHeight:`calc(100vh - ${s*2.5}px)`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${n}-inline`]:{width:"100%",[`&${n}-root`]:{[`${n}-item, ${n}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${I}`,`background ${I}`,`padding ${r} ${c}`].join(","),[`> ${n}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${n}-sub${n}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${n}-submenu > ${n}-submenu-title`]:C,[`& ${n}-item-group-title`]:{paddingInlineStart:f}},[`${n}-item`]:C}},{[`${n}-inline-collapsed`]:{width:o*2,[`&${n}-root`]:{[`${n}-item, ${n}-submenu ${n}-submenu-title`]:{[`> ${n}-inline-collapsed-noicon`]:{fontSize:i,textAlign:"center"}}},[`> ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-submenu > ${n}-submenu-title,
          > ${n}-submenu > ${n}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${d}px)`,textOverflow:"clip",[`
            ${n}-submenu-arrow,
            ${n}-submenu-expand-icon
          `]:{opacity:0},[`${n}-item-icon, ${e}`]:{margin:0,fontSize:i,lineHeight:`${o}px`,"+ span":{display:"inline-block",opacity:0}}},[`${n}-item-icon, ${e}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${n}-item-icon, ${e}`]:{display:"none"},"a, a:hover":{color:l}},[`${n}-item-group-title`]:p(p({},Pt),{paddingInline:$})}}]},tt=t=>{const{componentCls:n,fontSize:e,motionDurationSlow:o,motionDurationMid:l,motionEaseInOut:u,motionEaseOut:s,iconCls:r,controlHeightSM:c}=t;return{[`${n}-item, ${n}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${o}`,`background ${o}`,`padding ${o} ${u}`].join(","),[`${n}-item-icon, ${r}`]:{minWidth:e,fontSize:e,transition:[`font-size ${l} ${s}`,`margin ${o} ${u}`,`color ${o}`].join(","),"+ span":{marginInlineStart:c-e,opacity:1,transition:[`opacity ${o} ${u}`,`margin ${o}`,`color ${o}`].join(",")}},[`${n}-item-icon`]:p({},Dt()),[`&${n}-item-only-child`]:{[`> ${r}, > ${n}-item-icon`]:{marginInlineEnd:0}}},[`${n}-item-disabled, ${n}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important"},[`> ${n}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},nt=t=>{const{componentCls:n,motionDurationSlow:e,motionEaseInOut:o,borderRadius:l,menuArrowSize:u,menuArrowOffset:s}=t;return{[`${n}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:t.margin,width:u,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${e} ${o}, opacity ${e}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:u*.6,height:u*.15,backgroundColor:"currentcolor",borderRadius:l,transition:[`background ${e} ${o}`,`transform ${e} ${o}`,`top ${e} ${o}`,`color ${e} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(-${s})`},"&::after":{transform:`rotate(-45deg) translateY(${s})`}}}}},rn=t=>{const{antCls:n,componentCls:e,fontSize:o,motionDurationSlow:l,motionDurationMid:u,motionEaseInOut:s,lineHeight:r,paddingXS:c,padding:f,colorSplit:d,lineWidth:i,zIndexPopup:I,borderRadiusLG:$,radiusSubMenuItem:g,menuArrowSize:C,menuArrowOffset:x,lineType:T,menuPanelMaskInset:K}=t;return[{"":{[`${e}`]:p(p({},Ve()),{"&-hidden":{display:"none"}})},[`${e}-submenu-hidden`]:{display:"none"}},{[e]:p(p(p(p(p(p(p({},Et(t)),Ve()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${l} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${e}-item`]:{flex:"none"}},[`${e}-item, ${e}-submenu, ${e}-submenu-title`]:{borderRadius:t.radiusItem},[`${e}-item-group-title`]:{padding:`${c}px ${f}px`,fontSize:o,lineHeight:r,transition:`all ${l}`},[`&-horizontal ${e}-submenu`]:{transition:[`border-color ${l} ${s}`,`background ${l} ${s}`].join(",")},[`${e}-submenu, ${e}-submenu-inline`]:{transition:[`border-color ${l} ${s}`,`background ${l} ${s}`,`padding ${u} ${s}`].join(",")},[`${e}-submenu ${e}-sub`]:{cursor:"initial",transition:[`background ${l} ${s}`,`padding ${l} ${s}`].join(",")},[`${e}-title-content`]:{transition:`color ${l}`},[`${e}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${e}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:d,borderStyle:T,borderWidth:0,borderTopWidth:i,marginBlock:i,padding:0,"&-dashed":{borderStyle:"dashed"}}}),tt(t)),{[`${e}-item-group`]:{[`${e}-item-group-list`]:{margin:0,padding:0,[`${e}-item, ${e}-submenu-title`]:{paddingInline:`${o*2}px ${f}px`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:I,background:"transparent",borderRadius:$,boxShadow:"none",transformOrigin:"0 0","&::before":{position:"absolute",inset:`${K}px 0 0`,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'}},"&-placement-rightTop::before":{top:0,insetInlineStart:K},[`> ${e}`]:p(p(p({borderRadius:$},tt(t)),nt(t)),{[`${e}-item, ${e}-submenu > ${e}-submenu-title`]:{borderRadius:g},[`${e}-submenu-title::after`]:{transition:`transform ${l} ${s}`}})}}),nt(t)),{[`&-inline-collapsed ${e}-submenu-arrow,
        &-inline ${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${x})`},"&::after":{transform:`rotate(45deg) translateX(-${x})`}},[`${e}-submenu-open${e}-submenu-inline > ${e}-submenu-title > ${e}-submenu-arrow`]:{transform:`translateY(-${C*.2}px)`,"&::after":{transform:`rotate(-45deg) translateX(-${x})`},"&::before":{transform:`rotate(45deg) translateX(${x})`}}})},{[`${n}-layout-header`]:{[e]:{lineHeight:"inherit"}}}]},un=(t,n)=>Tt("Menu",(o,l)=>{let{overrideComponentToken:u}=l;if((n==null?void 0:n.value)===!1)return[];const{colorBgElevated:s,colorPrimary:r,colorError:c,colorErrorHover:f,colorTextLightSolid:d}=o,{controlHeightLG:i,fontSize:I}=o,$=I/7*5,g=Ne(o,{menuItemHeight:i,menuItemPaddingInline:o.margin,menuArrowSize:$,menuHorizontalHeight:i*1.15,menuArrowOffset:`${$*.25}px`,menuPanelMaskInset:-7,menuSubMenuBg:s}),C=new Xe(d).setAlpha(.65).toRgbString(),x=Ne(g,{colorItemText:C,colorItemTextHover:d,colorGroupTitle:C,colorItemTextSelected:d,colorItemBg:"#001529",colorSubItemBg:"#000c17",colorItemBgActive:"transparent",colorItemBgSelected:r,colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemTextDisabled:new Xe(d).setAlpha(.25).toRgbString(),colorDangerItemText:c,colorDangerItemTextHover:f,colorDangerItemTextSelected:d,colorDangerItemBgActive:c,colorDangerItemBgSelected:c,menuSubMenuBg:"#001529",colorItemTextSelectedHorizontal:d,colorItemBgSelectedHorizontal:r},p({},u));return[rn(g),on(g),an(g),Qe(g,"light"),Qe(x,"dark"),ln(g),Bt(g),Ge(g,"slide-up"),Ge(g,"slide-down"),kt(g,"zoom-big")]},o=>{const{colorPrimary:l,colorError:u,colorTextDisabled:s,colorErrorBg:r,colorText:c,colorTextDescription:f,colorBgContainer:d,colorFillAlter:i,colorFillContent:I,lineWidth:$,lineWidthBold:g,controlItemBgActive:C,colorBgTextHover:x}=o;return{dropdownWidth:160,zIndexPopup:o.zIndexPopupBase+50,radiusItem:o.borderRadiusLG,radiusSubMenuItem:o.borderRadiusSM,colorItemText:c,colorItemTextHover:c,colorItemTextHoverHorizontal:l,colorGroupTitle:f,colorItemTextSelected:l,colorItemTextSelectedHorizontal:l,colorItemBg:d,colorItemBgHover:x,colorItemBgActive:I,colorSubItemBg:i,colorItemBgSelected:C,colorItemBgSelectedHorizontal:"transparent",colorActiveBarWidth:0,colorActiveBarHeight:g,colorActiveBarBorderSize:$,colorItemTextDisabled:s,colorDangerItemText:u,colorDangerItemTextHover:u,colorDangerItemTextSelected:u,colorDangerItemBgActive:r,colorDangerItemBgSelected:r,itemMarginInline:o.marginXXS}})(t),sn=()=>({id:String,prefixCls:String,items:Array,disabled:Boolean,inlineCollapsed:Boolean,disabledOverflow:Boolean,forceSubMenuRender:Boolean,openKeys:Array,selectedKeys:Array,activeKey:String,selectable:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},tabindex:{type:[Number,String]},motion:Object,role:String,theme:{type:String,default:"light"},mode:{type:String,default:"vertical"},inlineIndent:{type:Number,default:24},subMenuOpenDelay:{type:Number,default:0},subMenuCloseDelay:{type:Number,default:.1},builtinPlacements:{type:Object},triggerSubMenuAction:{type:String,default:"hover"},getPopupContainer:Function,expandIcon:Function,onOpenChange:Function,onSelect:Function,onDeselect:Function,onClick:[Function,Array],onFocus:Function,onBlur:Function,onMousedown:Function,"onUpdate:openKeys":Function,"onUpdate:selectedKeys":Function,"onUpdate:activeKey":Function}),ot=[],ie=J({compatConfig:{MODE:3},name:"AMenu",inheritAttrs:!1,props:sn(),slots:Object,setup(t,n){let{slots:e,emit:o,attrs:l}=n;const{direction:u,getPrefixCls:s}=_t("menu",t),r=st(),c=m(()=>{var a;return s("menu",t.prefixCls||((a=r==null?void 0:r.prefixCls)===null||a===void 0?void 0:a.value))}),[f,d]=un(c,m(()=>!r)),i=R(new Map),I=ae(Vt,F(void 0)),$=m(()=>I.value!==void 0?I.value:t.inlineCollapsed),{itemsNodes:g}=nn(t),C=R(!1);Rt(()=>{C.value=!0}),Te(()=>{he(!(t.inlineCollapsed===!0&&t.mode!=="inline"),"Menu","`inlineCollapsed` should only be used when `mode` is inline."),he(!(I.value!==void 0&&t.inlineCollapsed===!0),"Menu","`inlineCollapsed` not control Menu under Sider. Should set `collapsed` on Sider instead.")});const x=F([]),T=F([]),K=F({});G(i,()=>{const a={};for(const b of i.value.values())a[b.key]=b;K.value=a},{flush:"post"}),Te(()=>{if(t.activeKey!==void 0){let a=[];const b=t.activeKey?K.value[t.activeKey]:void 0;b&&t.activeKey!==void 0?a=Be([].concat(be(b.parentKeys),t.activeKey)):a=[],fe(x.value,a)||(x.value=a)}}),G(()=>t.selectedKeys,a=>{a&&(T.value=a.slice())},{immediate:!0,deep:!0});const j=F([]);G([K,T],()=>{let a=[];T.value.forEach(b=>{const M=K.value[b];M&&(a=a.concat(be(M.parentKeys)))}),a=Be(a),fe(j.value,a)||(j.value=a)},{immediate:!0});const q=a=>{if(t.selectable){const{key:b}=a,M=T.value.includes(b);let B;t.multiple?M?B=T.value.filter(w=>w!==b):B=[...T.value,b]:B=[b];const D=p(p({},a),{selectedKeys:B});fe(B,T.value)||(t.selectedKeys===void 0&&(T.value=B),o("update:selectedKeys",B),M&&t.multiple?o("deselect",D):o("select",D))}P.value!=="inline"&&!t.multiple&&h.value.length&&Y(ot)},h=F([]);G(()=>t.openKeys,function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:h.value;fe(h.value,a)||(h.value=a.slice())},{immediate:!0,deep:!0});let O;const z=a=>{clearTimeout(O),O=setTimeout(()=>{t.activeKey===void 0&&(x.value=a),o("update:activeKey",a[a.length-1])})},A=m(()=>!!t.disabled),N=m(()=>u.value==="rtl"),P=F("vertical"),V=R(!1);Te(()=>{var a;(t.mode==="inline"||t.mode==="vertical")&&$.value?(P.value="vertical",V.value=$.value):(P.value=t.mode,V.value=!1),!((a=r==null?void 0:r.mode)===null||a===void 0)&&a.value&&(P.value=r.mode.value)});const W=m(()=>P.value==="inline"),Y=a=>{h.value=a,o("update:openKeys",a),o("openChange",a)},re=F(h.value),ue=R(!1);G(h,()=>{W.value&&(re.value=h.value)},{immediate:!0}),G(W,()=>{if(!ue.value){ue.value=!0;return}W.value?h.value=re.value:Y(ot)},{immediate:!0});const ee=m(()=>({[`${c.value}`]:!0,[`${c.value}-root`]:!0,[`${c.value}-${P.value}`]:!0,[`${c.value}-inline-collapsed`]:V.value,[`${c.value}-rtl`]:N.value,[`${c.value}-${t.theme}`]:!0})),te=m(()=>s()),se=m(()=>({horizontal:{name:`${te.value}-slide-up`},inline:At(`${te.value}-motion-collapse`),other:{name:`${te.value}-zoom-big`}}));pt(!0);const L=function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const b=[],M=i.value;return a.forEach(B=>{const{key:D,childrenEventKeys:w}=M.get(B);b.push(D,...L(be(w)))}),b},ve=a=>{var b;o("click",a),q(a),(b=r==null?void 0:r.onClick)===null||b===void 0||b.call(r)},y=(a,b)=>{var M;const B=((M=K.value[a])===null||M===void 0?void 0:M.childrenEventKeys)||[];let D=h.value.filter(w=>w!==a);if(b)D.push(a);else if(P.value!=="inline"){const w=L(be(B));D=Be(D.filter(X=>!w.includes(X)))}fe(h,D)||Y(D)},E=(a,b)=>{i.value.set(a,b),i.value=new Map(i.value)},H=a=>{i.value.delete(a),i.value=new Map(i.value)},Z=F(0),ne=m(()=>{var a;return t.expandIcon||e.expandIcon||!((a=r==null?void 0:r.expandIcon)===null||a===void 0)&&a.value?b=>{let M=t.expandIcon||e.expandIcon;return M=typeof M=="function"?M(b):M,_e(M,{class:`${c.value}-submenu-expand-icon`},!1)}:null});dt({prefixCls:c,activeKeys:x,openKeys:h,selectedKeys:T,changeActiveKeys:z,disabled:A,rtl:N,mode:P,inlineIndent:m(()=>t.inlineIndent),subMenuCloseDelay:m(()=>t.subMenuCloseDelay),subMenuOpenDelay:m(()=>t.subMenuOpenDelay),builtinPlacements:m(()=>t.builtinPlacements),triggerSubMenuAction:m(()=>t.triggerSubMenuAction),getPopupContainer:m(()=>t.getPopupContainer),inlineCollapsed:V,theme:m(()=>t.theme),siderCollapsed:I,defaultMotions:m(()=>C.value?se.value:null),motion:m(()=>C.value?t.motion:null),overflowDisabled:R(void 0),onOpenChange:y,onItemClick:ve,registerMenuInfo:E,unRegisterMenuInfo:H,selectedSubMenuKeys:j,expandIcon:ne,forceSubMenuRender:m(()=>t.forceSubMenuRender),rootClassName:d});const U=()=>{var a;return g.value||lt((a=e.default)===null||a===void 0?void 0:a.call(e))};return()=>{var a;const b=U(),M=Z.value>=b.length-1||P.value!=="horizontal"||t.disabledOverflow,B=w=>P.value!=="horizontal"||t.disabledOverflow?w:w.map((X,ce)=>v(Ce,{key:X.key,overflowDisabled:ce>Z.value},{default:()=>X})),D=((a=e.overflowedIndicator)===null||a===void 0?void 0:a.call(e))||v(Ae,null,null);return f(v(pe,k(k({},l),{},{onMousedown:t.onMousedown,prefixCls:`${c.value}-overflow`,component:"ul",itemComponent:ge,class:[ee.value,l.class,d.value],role:"menu",id:t.id,data:B(b),renderRawItem:w=>w,renderRawRest:w=>{const X=w.length,ce=X?b.slice(-X):null;return v(ke,null,[v(de,{eventKey:$e,key:$e,title:D,disabled:M,internalPopupClose:X===0},{default:()=>ce}),v(Ue,null,{default:()=>[v(de,{eventKey:$e,key:$e,title:D,disabled:M,internalPopupClose:X===0},{default:()=>ce})]})])},maxCount:P.value!=="horizontal"||t.disabledOverflow?pe.INVALIDATE:pe.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:w=>{Z.value=w}}),{default:()=>[v(Ht,{to:"body"},{default:()=>[v("div",{style:{display:"none"},"aria-hidden":!0},[v(Ue,null,{default:()=>[B(U())]})])]})]}))}}});ie.install=function(t){return t.component(ie.name,ie),t.component(ge.name,ge),t.component(de.name,de),t.component(Me.name,Me),t.component(xe.name,xe),t};ie.Item=ge;ie.Divider=Me;ie.SubMenu=de;ie.ItemGroup=xe;export{Ae as E,ie as M,ge as a,gn as u};
