import{_ as T}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{by as d,bz as y,bA as A,bB as D}from"./bootstrap-DCMzVRvD.js";import{d as h,M as f,h as _,o as c,b as i,w as b,j as e,c as l,F as p,K as u,t as r,a as m,k as I}from"../jse/index-index-C-MnMZEz.js";var U={authorEmail:"<EMAIL>",authorName:"vben",authorUrl:"https://github.com/anncwb",buildTime:"2025-06-08 16:45:55",dependencies:{"@faker-js/faker":"^9.7.0",jsonwebtoken:"^9.0.2",nitropack:"^2.11.11","@ant-design/icons-vue":"^7.0.1","@tinymce/tinymce-vue":"^6.0.1","@vben/access":"5.5.6","@vben/common-ui":"5.5.6","@vben/constants":"5.5.6","@vben/hooks":"5.5.6","@vben/icons":"5.5.6","@vben/layouts":"5.5.6","@vben/locales":"5.5.6","@vben/plugins":"5.5.6","@vben/preferences":"5.5.6","@vben/request":"5.5.6","@vben/stores":"5.5.6","@vben/styles":"5.5.6","@vben/types":"5.5.6","@vben/utils":"5.5.6","@vueuse/core":"^13.1.0","ant-design-vue":"^4.2.6",cropperjs:"^1.6.2","crypto-js":"^4.2.0",dayjs:"^1.11.13",echarts:"^5.6.0",jsencrypt:"^3.3.2","lodash-es":"^4.17.21",pinia:"^3.0.2",tinymce:"^7.3.0","unplugin-vue-components":"^0.27.3",vue:"^3.5.13","vue-router":"^4.5.1","vue3-colorpicker":"^2.3.0","@vben-core/shadcn-ui":"5.5.6","lucide-vue-next":"^0.507.0","medium-zoom":"^1.1.0","radix-vue":"^1.9.17","vitepress-plugin-group-icons":"^1.5.2","@commitlint/cli":"^19.8.0","@commitlint/config-conventional":"^19.8.0","@vben/node-utils":"5.5.6","commitlint-plugin-function-rules":"^4.0.1","cz-git":"^1.11.1",czg:"^1.11.1","eslint-config-turbo":"^2.5.2","eslint-plugin-command":"^3.2.0","eslint-plugin-import-x":"^4.11.0",prettier:"^3.5.3","prettier-plugin-tailwindcss":"^0.6.11","@stylistic/stylelint-plugin":"^3.1.2","stylelint-config-recess-order":"^6.0.0","stylelint-scss":"^6.11.1","@changesets/git":"^3.0.4","@manypkg/get-packages":"^3.0.0",chalk:"^5.4.1",consola:"^3.4.2",execa:"^9.5.2","find-up":"^7.0.0",ora:"^8.2.0","pkg-types":"^2.1.0",rimraf:"^6.0.1","@iconify/json":"^2.2.334","@iconify/tailwind":"^1.2.0","@tailwindcss/nesting":"0.0.0-insiders.565cd3e","@tailwindcss/typography":"^0.5.16",autoprefixer:"^10.4.21",cssnano:"^7.0.6",postcss:"^8.5.3","postcss-antd-fixes":"^0.2.0","postcss-import":"^16.1.0","postcss-preset-env":"^10.1.6",tailwindcss:"^3.4.17","tailwindcss-animate":"^1.0.7",vite:"^6.3.4","@intlify/unplugin-vue-i18n":"^6.0.8","@jspm/generator":"^2.5.1",archiver:"^7.0.1",cheerio:"^1.0.0","get-port":"^7.1.0","html-minifier-terser":"^7.2.0","resolve.exports":"^2.0.3","vite-plugin-pwa":"^1.0.0","vite-plugin-vue-devtools":"^7.7.6","@iconify/vue":"^5.0.0","@ctrl/tinycolor":"^4.1.0","@tanstack/vue-store":"^0.7.0","@vue/shared":"^3.5.13",clsx:"^2.1.1",defu:"^6.1.4","lodash.clonedeep":"^4.5.0","lodash.get":"^4.4.2","lodash.isequal":"^4.5.0","lodash.set":"^4.3.2",nprogress:"^0.2.0","tailwind-merge":"^2.6.0","theme-colors":"^0.1.0","@vben-core/shared":"5.5.6",sortablejs:"^1.15.6","@vben-core/typings":"5.5.6","@vben-core/composables":"5.5.6","@vben-core/icons":"5.5.6","@vee-validate/zod":"^4.15.0","vee-validate":"^4.15.0",zod:"^3.24.3","zod-defaults":"^0.1.3","class-variance-authority":"^0.7.1","@codemirror/lang-html":"^6.4.9","@codemirror/lang-java":"^6.0.1","@codemirror/lang-javascript":"^6.2.2","@codemirror/lang-sql":"^6.7.1","@codemirror/lang-vue":"^0.1.3","@codemirror/lang-xml":"^6.1.0","@codemirror/theme-one-dark":"^6.1.2","@vben-core/form-ui":"5.5.6","@vben-core/popup-ui":"5.2.1","@vben-core/preferences":"5.5.6","@vueuse/integrations":"^13.1.0",codemirror:"6.0.1",qrcode:"^1.5.4","tippy.js":"^6.3.7",vditor:"3.10.9","vue-codemirror6":"1.3.4","vue-json-pretty":"^2.4.0","vue-json-viewer":"^3.0.4","vue-tippy":"^6.7.0","watermark-js-plus":"^1.6.0","@vben-core/layout-ui":"5.5.6","@vben-core/menu-ui":"5.5.6","@vben-core/tabs-ui":"5.5.6","@vueuse/motion":"^3.0.3","vxe-pc-ui":"^4.5.35","vxe-table":"^4.13.16",axios:"^1.9.0",qs:"^6.14.0","@intlify/core-base":"^11.1.3","vue-i18n":"^11.1.3","pinia-plugin-persistedstate":"^4.2.0","secure-ls":"^2.0.0","@vben-core/design":"5.5.6","file-type":"^19.5.0","@tanstack/vue-query":"^5.75.1","json-bigint":"^1.0.0","@clack/prompts":"^0.10.1",cac:"^6.7.14","circular-dependency-scanner":"^2.3.0",depcheck:"^1.4.7",publint:"^0.3.12"},devDependencies:{"@types/jsonwebtoken":"^9.0.9",h3:"^1.15.3","@types/crypto-js":"^4.2.2","@types/lodash-es":"^4.17.12","@nolebase/vitepress-plugin-git-changelog":"^2.17.0","@vben/vite-config":"5.5.6","@vite-pwa/vitepress":"^1.0.0",vitepress:"^1.6.3",vue:"^3.5.13","@eslint/js":"^9.26.0","@types/eslint":"^9.6.1","@typescript-eslint/eslint-plugin":"^8.31.1","@typescript-eslint/parser":"^8.31.1",eslint:"^9.26.0","eslint-plugin-eslint-comments":"^3.2.0","eslint-plugin-jsdoc":"^50.6.11","eslint-plugin-jsonc":"^2.20.0","eslint-plugin-n":"^17.17.0","eslint-plugin-no-only-tests":"^3.3.0","eslint-plugin-perfectionist":"^4.12.3","eslint-plugin-prettier":"^5.2.6","eslint-plugin-regexp":"^2.7.0","eslint-plugin-unicorn":"^59.0.0","eslint-plugin-unused-imports":"^4.1.4","eslint-plugin-vitest":"^0.5.4","eslint-plugin-vue":"^10.1.0",globals:"^16.0.0","jsonc-eslint-parser":"^2.4.0","vue-eslint-parser":"^10.1.3",postcss:"^8.5.3","postcss-html":"^1.8.0","postcss-scss":"^4.0.9",prettier:"^3.5.3",stylelint:"^16.19.1","stylelint-config-recommended":"^16.0.0","stylelint-config-recommended-scss":"^14.1.0","stylelint-config-recommended-vue":"^1.6.0","stylelint-config-standard":"^38.0.0","stylelint-order":"^7.0.0","stylelint-prettier":"^5.0.3","@types/postcss-import":"^14.0.3","@pnpm/workspace.read-manifest":"^1000.1.4","@types/archiver":"^6.0.3","@types/html-minifier-terser":"^7.0.2","@vben/node-utils":"5.5.6","@vitejs/plugin-vue":"^5.2.3","@vitejs/plugin-vue-jsx":"^4.1.2",dayjs:"^1.11.13",dotenv:"^16.5.0",rollup:"^4.40.1","rollup-plugin-visualizer":"^5.14.0",sass:"^1.87.0",vite:"^6.3.4","vite-plugin-compression":"^0.5.1","vite-plugin-dts":"^4.5.3","vite-plugin-html":"^3.2.2","vite-plugin-lazy-import":"^1.0.7","@types/lodash.clonedeep":"^4.5.9","@types/lodash.get":"^4.4.9","@types/lodash.isequal":"^4.5.8","@types/lodash.set":"^4.3.9","@types/nprogress":"^0.2.3","@types/sortablejs":"^1.15.8","@types/qrcode":"^1.5.5","@types/qs":"^6.9.18","axios-mock-adapter":"^2.1.0","@iconify/icons-akar-icons":"^1.2.19","@iconify/icons-ant-design":"^1.2.7","@iconify/icons-arcticons":"^1.2.77","@iconify/icons-bi":"^1.2.19","@iconify/icons-bx":"^1.2.6","@iconify/icons-carbon":"^1.2.20","@iconify/icons-devicon":"^1.2.17","@iconify/icons-emojione":"^1.2.6","@iconify/icons-eos-icons":"^1.2.6","@iconify/icons-fa-brands":"^1.2.4","@iconify/icons-fe":"^1.2.5","@iconify/icons-flat-color-icons":"^1.2.5","@iconify/icons-fluent":"^1.2.38","@iconify/icons-fluent-mdl2":"^1.2.1","@iconify/icons-ic":"^1.2.13","@iconify/icons-icon-park-outline":"^1.2.11","@iconify/icons-icon-park-twotone":"^1.2.8","@iconify/icons-la":"^1.2.3","@iconify/icons-logos":"^1.2.36","@iconify/icons-lucide":"^1.2.135","@iconify/icons-majesticons":"^1.2.6","@iconify/icons-material-symbols":"^1.2.58","@iconify/icons-mdi":"^1.2.48","@iconify/icons-mingcute":"^1.2.9","@iconify/icons-noto":"^1.2.10","@iconify/icons-ph":"^1.2.5","@iconify/icons-ri":"^1.2.10","@iconify/icons-simple-icons":"^1.2.74","@iconify/icons-skill-icons":"^1.2.1","@iconify/icons-solar":"^1.2.3","@iconify/icons-streamline":"^1.2.3","@iconify/icons-tabler":"^1.2.95","@iconify/icons-uiw":"^1.2.6","@iconify/icons-vscode-icons":"^1.2.29","@iconify/icons-wpf":"^1.2.3","@types/json-bigint":"^1.0.4"},homepage:"https://vben.pro",license:"MIT",version:"1.4.0"};const L={class:"text-foreground mt-3 text-sm leading-6"},$=["href"],C={class:"card-box p-5"},M={class:"mt-4"},R={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},S={class:"text-foreground text-sm font-medium leading-6"},O={class:"text-foreground mt-1 text-sm leading-6 sm:mt-2"},F={class:"card-box mt-6 p-5"},G={class:"mt-4"},H={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},K={class:"text-foreground text-sm"},P={class:"text-foreground/80 mt-1 text-sm sm:mt-2"},W={class:"card-box mt-6 p-5"},J={class:"mt-4"},Q={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},X={class:"text-foreground text-sm"},Y={class:"text-foreground/80 mt-1 text-sm sm:mt-2"},Z=h({name:"AboutUI",__name:"about",props:{description:{default:"是一个现代化开箱即用的中后台解决方案，采用最新的技术栈，包括 Vue 3.0、Vite、TailwindCSS 和 TypeScript 等前沿技术，代码规范严谨，提供丰富的配置选项，旨在为中大型项目的开发提供现成的开箱即用解决方案及丰富的示例，同时，它也是学习和深入前端技术的一个极佳示例。"},name:{default:"Vben Admin"},title:{default:"关于项目"}},setup(x){const o=(s,n)=>f("a",{href:s,target:"_blank",class:"vben-link"},{default:()=>n}),{authorEmail:a,authorName:k,authorUrl:j,buildTime:w,dependencies:v={},devDependencies:g={},homepage:V,license:B,version:E}=U||{},N=[{content:E,title:"版本号"},{content:B,title:"开源许可协议"},{content:w,title:"最后构建时间"},{content:o(V,"点击查看"),title:"主页"},{content:o(A,"点击查看"),title:"文档地址"},{content:o(D,"点击查看"),title:"预览地址"},{content:o(y,"点击查看"),title:"Github"},{content:f("div",[o(j,`${k}  `),o(`mailto:${a}`,a)]),title:"作者"}],q=Object.keys(v).map(s=>({content:v[s],title:s})),z=Object.keys(g).map(s=>({content:g[s],title:s}));return(s,n)=>(c(),_(i(T),{title:s.title},{description:b(()=>[e("p",L,[e("a",{href:i(y),class:"vben-link",target:"_blank"},r(s.name),9,$),I(" "+r(s.description),1)])]),default:b(()=>[e("div",C,[n[0]||(n[0]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"基本信息")],-1)),e("div",M,[e("dl",R,[(c(),l(p,null,u(N,t=>e("div",{key:t.title,class:"border-border border-t px-4 py-6 sm:col-span-1 sm:px-0"},[e("dt",S,r(t.title),1),e("dd",O,[m(i(d),{content:t.content},null,8,["content"])])])),64))])])]),e("div",F,[n[1]||(n[1]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"生产环境依赖")],-1)),e("div",G,[e("dl",H,[(c(!0),l(p,null,u(i(q),t=>(c(),l("div",{key:t.title,class:"border-border border-t px-4 py-3 sm:col-span-1 sm:px-0"},[e("dt",K,r(t.title),1),e("dd",P,[m(i(d),{content:t.content},null,8,["content"])])]))),128))])])]),e("div",W,[n[2]||(n[2]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"开发环境依赖")],-1)),e("div",J,[e("dl",Q,[(c(!0),l(p,null,u(i(z),t=>(c(),l("div",{key:t.title,class:"border-border border-t px-4 py-3 sm:col-span-1 sm:px-0"},[e("dt",X,r(t.title),1),e("dd",Y,[m(i(d),{content:t.content},null,8,["content"])])]))),128))])])])]),_:1},8,["title"]))}}),ne=h({name:"About",__name:"index",setup(x){return(o,a)=>(c(),_(i(Z)))}});export{ne as default};
