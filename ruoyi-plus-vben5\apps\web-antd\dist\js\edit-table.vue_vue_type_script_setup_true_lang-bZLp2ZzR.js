var u=(p,c,r)=>new Promise((o,d)=>{var f=e=>{try{s(r.next(e))}catch(t){d(t)}},m=e=>{try{s(r.throw(e))}catch(t){d(t)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(f,m);s((r=r.apply(p,c)).next())});import"./vxe-table-DzEj5Fop.js";import C from"./index-BeyziwLP.js";import"./index-BxBCzu2M.js";import{I as h}from"./Search-ClCped_G.js";import k from"./index-CW_j1L6F.js";import x from"./index-CIjgbPOA.js";import{ak as y,ap as g,an as j}from"./bootstrap-DCMzVRvD.js";import{_ as D}from"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import{d as R,v as S,I as B,l as A,S as T,h as b,o as _,b as v,w as i,a,T as V,k as l,t as I}from"../jse/index-index-C-MnMZEz.js";import{u as q}from"./use-vxe-grid-BC7vZzEr.js";import{a as N}from"./get-popup-container-P4S1sr5h.js";const L=R({__name:"edit-table",setup(p){const c={editConfig:{trigger:"click",mode:"row",showStatus:!0},border:!0,rowConfig:{drag:!0},checkboxConfig:{},editRules:{name:[{required:!0,message:"请输入姓名",trigger:"blur"}],age:[{required:!0,message:"请输入年龄",trigger:"blur"},{min:0,max:200,message:"年龄必须为1-200"}],job:[{required:!0,message:"请选择工作",trigger:"blur"}]},columns:[{type:"checkbox",width:60},{dragSort:!0,title:"排序",width:60},{field:"name",title:"姓名",align:"left",editRender:{},slots:{default:({row:e})=>e.name?a("span",null,[e.name]):a("span",{class:"text-red-500"},[l("未填写")]),edit:({row:e})=>a(h,{placeholder:"请输入",value:e.name,"onUpdate:value":t=>e.name=t},null)}},{field:"age",title:"年龄",align:"left",editRender:{},slots:{default:({row:e})=>e.age?a("span",null,[e.age]):a("span",{class:"text-red-500"},[l("未填写")]),edit:({row:e})=>a(k,{class:"w-full",placeholder:"请输入",value:e.age,"onUpdate:value":t=>e.age=t},null)}},{field:"工作",title:"job",align:"left",editRender:{},slots:{default:({row:e})=>e.job?a("span",null,[e.job]):a("span",{class:"text-red-500"},[l("未选择")]),edit:({row:e})=>{const t=["前端佬","后端佬","组长"].map(n=>({label:n,value:n}));return a(x,{class:"w-full",getPopupContainer:N,options:t,placeholder:"请选择",value:e.job,"onUpdate:value":n=>e.job=n},null)}}},{field:"action",title:"操作",width:100,slots:{default:({$table:e,row:t})=>{function n(){e.remove(t)}return a(y,{danger:!0,onClick:n,size:"small"},{default:()=>[l("删除")]})}}}],height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{enabled:!1},toolbarConfig:{enabled:!1},showOverflow:!1},[r,o]=q({gridOptions:c});S(()=>u(null,null,function*(){const e=[{name:"张三",age:18,job:"前端佬"},{name:"李四",age:19,job:"后端佬"},{name:"王五",age:20,job:"组长"}];yield B(),yield o.grid.loadData(e)}));function d(){return u(this,null,function*(){const e={name:"",age:void 0,job:void 0},{row:t}=yield o.grid.insert(e);yield o.grid.setEditCell(t,"name")})}function f(){return u(this,null,function*(){yield o.grid.removeCheckboxRow()})}function m(){return u(this,null,function*(){(yield o.grid.validate(!0))?g.error("校验失败"):g.success("校验成功")})}function s(){const e=o.grid.getTableData(),{fullData:t}=e;console.log(t),j.info({title:"提示",content:a("div",{class:"max-h-[350px] overflow-y-auto"},[a(D,{data:t},null)])})}return(e,t)=>{const n=A("a-button"),w=T("access");return _(),b(v(r),null,{"toolbar-tools":i(()=>[a(v(C),null,{default:i(()=>[a(n,{onClick:s},{default:i(()=>t[0]||(t[0]=[l("获取表格数据")])),_:1,__:[0]}),a(n,{onClick:m},{default:i(()=>t[1]||(t[1]=[l("校验")])),_:1,__:[1]}),a(n,{danger:"",onClick:f},{default:i(()=>t[2]||(t[2]=[l(" 删除勾选 ")])),_:1,__:[2]}),V((_(),b(n,{type:"primary",onClick:d},{default:i(()=>[l(I(e.$t("pages.common.add")),1)]),_:1})),[[w,["system:config:add"],"code"]])]),_:1})]),_:1})}}});export{L as _};
