{"doc": " 优先队列 演示案例\n <p>\n 轻量级队列 重量级数据量 请使用 MQ\n <p>\n 集群测试通过 同一个消息只会被消费一次 做好事务补偿\n 集群测试流程 在其中一台发送数据 两端分别调用获取接口 一次获取一条\n\n <AUTHOR> Li\n @version 3.6.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["java.lang.String"], "doc": " 添加队列数据\n\n @param queueName 队列名\n"}, {"name": "remove", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 删除队列数据\n\n @param queueName 队列名\n @param name      对象名\n @param orderNum  排序号\n"}, {"name": "get", "paramTypes": ["java.lang.String"], "doc": " 获取队列数据\n\n @param queueName 队列名\n"}], "constructors": []}