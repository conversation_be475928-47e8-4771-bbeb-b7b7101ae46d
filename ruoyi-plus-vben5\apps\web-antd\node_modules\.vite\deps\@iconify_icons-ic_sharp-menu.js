import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-ic@1.2.13/node_modules/@iconify/icons-ic/sharp-menu.js
var require_sharp_menu = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-ic@1.2.13/node_modules/@iconify/icons-ic/sharp-menu.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="currentColor" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_sharp_menu();
//# sourceMappingURL=@iconify_icons-ic_sharp-menu.js.map
