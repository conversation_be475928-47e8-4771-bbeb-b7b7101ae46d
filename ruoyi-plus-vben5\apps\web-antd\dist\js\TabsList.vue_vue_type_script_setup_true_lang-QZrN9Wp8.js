var f=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var l=(s,t)=>{var a={};for(var e in s)b.call(s,e)&&t.indexOf(e)<0&&(a[e]=s[e]);if(s!=null&&f)for(var e of f(s))t.indexOf(e)<0&&h.call(s,e)&&(a[e]=s[e]);return a};import{bG as v,bH as y,bI as B,bJ as C}from"./bootstrap-DCMzVRvD.js";import{d as c,h as i,o as u,w as d,r as p,e as P,g as S,b as n,B as g,O as m,G as _}from"../jse/index-index-C-MnMZEz.js";const O=c({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},emits:["update:modelValue"],setup(s,{emit:t}){const r=v(s,t);return(o,$)=>(u(),i(n(y),P(S(n(r))),{default:d(()=>[p(o.$slots,"default")]),_:3},16))}}),k=c({__name:"TabsContent",props:{class:{},value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const t=s,a=g(()=>{const o=t,{class:e}=o;return l(o,["class"])});return(e,r)=>(u(),i(n(B),m({class:n(_)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",t.class)},a.value),{default:d(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}}),F=c({__name:"TabsList",props:{class:{},loop:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(s){const t=s,a=g(()=>{const o=t,{class:e}=o;return l(o,["class"])});return(e,r)=>(u(),i(n(C),m(a.value,{class:n(_)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",t.class)}),{default:d(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}});export{O as _,F as a,k as b};
