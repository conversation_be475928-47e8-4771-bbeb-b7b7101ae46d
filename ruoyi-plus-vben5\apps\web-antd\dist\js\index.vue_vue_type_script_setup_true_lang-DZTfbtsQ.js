var O=Object.defineProperty;var D=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var S=(i,o,e)=>o in i?O(i,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[o]=e,q=(i,o)=>{for(var e in o||(o={}))R.call(o,e)&&S(i,e,o[e]);if(D)for(var e of D(o))T.call(o,e)&&S(i,e,o[e]);return i};var f=(i,o,e)=>new Promise((w,r)=>{var x=d=>{try{p(e.next(d))}catch(u){r(u)}},l=d=>{try{p(e.throw(d))}catch(u){r(u)}},p=d=>d.done?w(d.value):Promise.resolve(d.value).then(x,l);p((e=e.apply(i,o)).next())});import{at as M,as as N,an as z}from"./bootstrap-DCMzVRvD.js";import{v as U}from"./vxe-table-DzEj5Fop.js";import{c as j,q as E,o as F,a as G,b as L,d as V}from"./oss-config-drawer-Clh_0mly.js";import{_ as W}from"./table-switch.vue_vue_type_script_setup_true_lang-BPKnQ2Wy.js";import{_ as H}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import A from"./index-BeyziwLP.js";import{d as J,l as P,S as K,h as g,o as C,b as t,w as n,a as m,T as b,k as v,t as k}from"../jse/index-index-C-MnMZEz.js";import{u as Q}from"./use-vxe-grid-BC7vZzEr.js";import{u as X}from"./use-drawer-6qcpK-D1.js";import{P as Y}from"./index-DNdMANjv.js";import{g as Z}from"./get-popup-container-P4S1sr5h.js";const pe=J({__name:"index",setup(i){const o={schema:E(),commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={checkboxConfig:{highlight:!0,reserve:!0},columns:j,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(y,...$)=>f(null,[y,...$],function*({page:s},a={}){return yield L(q({pageNum:s.currentPage,pageSize:s.pageSize},a))})}},rowConfig:{keyField:"ossConfigId"},id:"system-oss-config-index"},[w,r]=Q({formOptions:o,gridOptions:e}),[x,l]=X({connectedComponent:F});function p(){l.setData({}),l.open()}function d(s){return f(this,null,function*(){l.setData({id:s.ossConfigId}),l.open()})}function u(s){return f(this,null,function*(){yield V([s.ossConfigId]),yield r.query()})}function B(){const a=r.grid.getCheckboxRecords().map(y=>y.ossConfigId);z.confirm({title:"提示",okType:"danger",content:`确认删除选中的${a.length}条记录吗？`,onOk:()=>f(null,null,function*(){yield V(a),yield r.query()})})}const{hasAccessByCodes:I}=M();return(s,a)=>{const y=P("a-button"),$=P("ghost-button"),h=K("access");return C(),g(t(H),{"auto-content-height":!0},{default:n(()=>[m(t(w),{"table-title":"oss配置列表"},{"toolbar-tools":n(()=>[m(t(A),null,{default:n(()=>[b((C(),g(y,{disabled:!t(U)(t(r)),danger:"",type:"primary",onClick:B},{default:n(()=>[v(k(s.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[h,["system:ossConfig:remove"],"code"]]),b((C(),g(y,{type:"primary",onClick:p},{default:n(()=>[v(k(s.$t("pages.common.add")),1)]),_:1})),[[h,["system:ossConfig:add"],"code"]])]),_:1})]),status:n(({row:c})=>[m(t(W),{value:c.status,"onUpdate:value":_=>c.status=_,api:()=>t(G)(c),disabled:!t(I)(["system:ossConfig:edit"]),"checked-text":"是","un-checked-text":"否",onReload:a[0]||(a[0]=_=>t(r).query())},null,8,["value","onUpdate:value","api","disabled"])]),action:n(({row:c})=>[m(t(A),null,{default:n(()=>[b((C(),g($,{onClick:_=>d(c)},{default:n(()=>[v(k(s.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[h,["system:ossConfig:edit"],"code"]]),m(t(Y),{"get-popup-container":t(Z),placement:"left",title:"确认删除？",onConfirm:_=>u(c)},{default:n(()=>[b((C(),g($,{danger:"",onClick:a[1]||(a[1]=N(()=>{},["stop"]))},{default:n(()=>[v(k(s.$t("pages.common.delete")),1)]),_:1})),[[h,["system:ossConfig:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),m(t(x),{onReload:a[2]||(a[2]=c=>t(r).query())})]),_:1})}}});export{pe as _};
