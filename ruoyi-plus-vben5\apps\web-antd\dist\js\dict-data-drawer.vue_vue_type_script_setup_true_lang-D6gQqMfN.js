var m=(n,c,o)=>new Promise((i,u)=>{var p=a=>{try{l(o.next(a))}catch(d){u(d)}},t=a=>{try{l(o.throw(a))}catch(d){u(d)}},l=a=>a.done?i(a.value):Promise.resolve(a.value).then(p,t);l((o=o.apply(n,c)).next())});import{$ as b,aj as k}from"./bootstrap-DCMzVRvD.js";import{c as x,e as B,f as F}from"./dict-BLkXAGS5.js";import{t as S}from"./data-1kX019oc.js";import{u as q,d as h}from"./popup-D6rC6QBG.js";import{e as P}from"./render-BxXtQdeV.js";import{_ as z}from"./tag-style-picker.vue_vue_type_script_setup_true_lang-CHIIikqF.js";import{d as A,p as y,B as L,P as G,h as U,o as $,b as g,w as D,a as I,O}from"../jse/index-index-C-MnMZEz.js";import{u as j}from"./use-drawer-6qcpK-D1.js";const Z=()=>[{component:"Input",fieldName:"dictLabel",label:"字典标签"}],ee=[{type:"checkbox",width:60},{title:"字典标签",field:"cssClass",slots:{default:({row:n})=>{const{dictValue:c}=n;return P(c,[n])}}},{title:"字典键值",field:"dictValue"},{title:"字典排序",field:"dictSort"},{title:"备注",field:"remark"},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],R=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"dictCode"},{component:"Input",componentProps:{disabled:!0},fieldName:"dictType",label:"字典类型"},{component:"Input",fieldName:"listClass",label:"标签样式"},{component:"Input",fieldName:"dictLabel",label:"数据标签",rules:"required"},{component:"Input",fieldName:"dictValue",label:"数据键值",rules:"required"},{component:"Textarea",componentProps:{placeholder:"可使用tailwind类名 如bg-blue w-full h-full等"},fieldName:"cssClass",formItemClass:"items-start",help:"标签的css样式, 可添加已经编译的css类名",label:"css类名"},{component:"InputNumber",fieldName:"dictSort",label:"显示排序",rules:"required"},{component:"Textarea",fieldName:"remark",formItemClass:"items-start",label:"备注"}],te=A({__name:"dict-data-drawer",emits:["reload"],setup(n,{emit:c}){const o=c,i=y(!1),u=L(()=>i.value?b("pages.common.edit"):b("pages.common.add")),[p,t]=k({commonConfig:{componentProps:{class:"w-full"},formItemClass:"col-span-2",labelWidth:80},schema:R(),showDefaultActions:!1,wrapperClass:"grid-cols-2"}),l=y("default");function a(s){const e=Reflect.has(S,s);l.value=e?"default":"custom"}const{onBeforeClose:d,markInitialized:v,resetInitialized:C}=q({initializedGetter:h(t),currentGetter:h(t)}),[_,r]=j({onBeforeClose:d,onClosed:T,onConfirm:N,onOpenChange(s){return m(this,null,function*(){if(!s)return null;r.drawerLoading(!0);const{dictCode:e,dictType:w}=r.getData();if(i.value=!!e,yield t.setFieldValue("dictType",w),e&&i.value){const f=yield x(e);a(f.listClass),yield t.setValues(f)}yield v(),r.drawerLoading(!1)})}});function N(){return m(this,null,function*(){try{r.lock(!0);const{valid:s}=yield t.validate();if(!s)return;const e=G(yield t.getValues());e.listClass||(e.listClass=""),yield i.value?B(e):F(e),C(),o("reload"),r.close()}catch(s){console.error(s)}finally{r.lock(!1)}})}function T(){return m(this,null,function*(){yield t.resetForm(),l.value="default",C()})}function V(){return m(this,null,function*(){yield t.setFieldValue("listClass",void 0)})}return(s,e)=>($(),U(g(_),{title:u.value,class:"w-[600px]"},{default:D(()=>[I(g(p),null,{listClass:D(w=>[I(z,O(w,{"select-type":l.value,"onUpdate:selectType":e[0]||(e[0]=f=>l.value=f),onDeselect:V}),null,16,["select-type"])]),_:1})]),_:1},8,["title"]))}});export{te as _,ee as c,Z as q};
