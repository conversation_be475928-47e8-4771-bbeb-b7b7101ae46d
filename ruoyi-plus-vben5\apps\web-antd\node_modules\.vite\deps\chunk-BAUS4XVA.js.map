{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeSelectComponent from './src/select';\nimport { dynamicApp } from '../dynamics';\nexport const VxeSelect = Object.assign(VxeSelectComponent, {\n    install: function (app) {\n        app.component(VxeSelectComponent.name, VxeSelectComponent);\n    }\n});\ndynamicApp.use(VxeSelect);\nVxeUI.component(VxeSelectComponent);\nexport const Select = VxeSelect;\nexport default VxeSelect;\n"], "mappings": ";;;;;;;;;;;AAGO,IAAM,YAAY,OAAO,OAAO,gBAAoB;AAAA,EACvD,SAAS,SAAU,KAAK;AACpB,QAAI,UAAU,eAAmB,MAAM,cAAkB;AAAA,EAC7D;AACJ,CAAC;AACD,WAAW,IAAI,SAAS;AACxB,MAAM,UAAU,cAAkB;AAC3B,IAAM,SAAS;AACtB,IAAOA,kBAAQ;", "names": ["select_default"]}