package org.dromara.resource.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.SysOss;
import org.dromara.resource.domain.SysOssToSysOssVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssToSysOssVoMapper__7.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__7 extends BaseMapper<SysOssVo, SysOss> {
}
