# 内置配置 不允许修改 如需修改请在 nacos 上写相同配置覆盖
dubbo:
  application:
    logger: slf4j
    # 元数据中心 local 本地 remote 远程 这里使用远程便于其他服务获取
    metadataType: remote
    # 可选值 interface、instance、all，默认是 all，即接口级地址、应用级地址都注册
    register-mode: instance
    service-discovery:
      # FORCE_INTERFACE，只消费接口级地址，如无地址则报错，单订阅 2.x 地址
      # APPLICATION_FIRST，智能决策接口级/应用级地址，双订阅
      # FORCE_APPLICATION，只消费应用级地址，如无地址则报错，单订阅 3.x 地址
      migration: FORCE_APPLICATION
  # 注册中心配置
  registry:
    address: nacos://${spring.cloud.nacos.server-addr}
    group: DUBBO_GROUP
    username: ${spring.cloud.nacos.username}
    password: ${spring.cloud.nacos.password}
    parameters:
      namespace: ${spring.profiles.active}
  metadata-report:
    address: redis://${spring.data.redis.host}:${spring.data.redis.port}
    group: DUBBO_GROUP
    username: dubbo
    password: ${spring.data.redis.password}
    # 集群开关
    cluster: false
    parameters:
      namespace: ${spring.profiles.active}
      database: ${spring.data.redis.database}
      timeout: ${spring.data.redis.timeout}
      # 集群地址 cluster 为 true 生效
      backup: 127.0.0.1:6379,127.0.0.1:6381
  # 消费者相关配置
  consumer:
    # 结果缓存(LRU算法)
    # 会有数据不一致问题 建议在注解局部开启
    cache: false
    # 支持校验注解
    validation: jvalidationNew
    # 调用重试 不包括第一次 0为不需要重试
    retries: 0
    # 初始化检查
    check: false
