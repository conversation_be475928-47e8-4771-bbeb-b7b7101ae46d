{"doc": " 工作流常量\n\n <AUTHOR>\n", "fields": [{"name": "INITIATOR", "doc": " 流程发起人\n"}, {"name": "BUSINESS_ID", "doc": " 业务id\n"}, {"name": "DELEGATE_TASK", "doc": " 委托\n"}, {"name": "TRANSFER_TASK", "doc": " 转办\n"}, {"name": "ADD_SIGNATURE", "doc": " 加签\n"}, {"name": "REDUCTION_SIGNATURE", "doc": " 减签\n"}, {"name": "CATEGORY_ID_TO_NAME", "doc": " 流程分类Id转名称\n"}, {"name": "FLOW_CATEGORY_NAME", "doc": " 流程分类名称\n"}, {"name": "FLOW_CATEGORY_ID", "doc": " 默认租户OA申请分类id\n"}, {"name": "SUBMIT", "doc": " 是否为申请人提交常量\n"}, {"name": "FLOW_COPY_LIST", "doc": " 抄送常量\n"}, {"name": "MESSAGE_TYPE", "doc": " 消息类型常量\n"}, {"name": "MESSAGE_NOTICE", "doc": " 消息通知常量\n"}], "enumConstants": [], "methods": [], "constructors": []}