var w=(e,m,n)=>new Promise((d,f)=>{var g=u=>{try{i(n.next(u))}catch(l){f(l)}},a=u=>{try{i(n.throw(u))}catch(l){f(l)}},i=u=>u.done?d(u.value):Promise.resolve(u.value).then(g,a);i((n=n.apply(e,m)).next())});import{bM as M,i as x,ap as C,$ as p,an as O,bN as P}from"./bootstrap-DCMzVRvD.js";import{p as y,B as S,y as E,q as L}from"../jse/index-index-C-MnMZEz.js";const z=[".jpg",".jpeg",".png",".gif",".webp"],R=[".xlsx",".csv",".docx",".pdf"];function T(e){e!=null&&e.url&&window.open(e.url)}function B(){function e(a){return new Promise((i,u)=>{const l=new FileReader;l.readAsDataURL(a),l.addEventListener("load",()=>i(l.result)),l.addEventListener("error",F=>u(F))})}const m=y(!1),n=y(""),d=y("");function f(){m.value=!1,d.value=""}function g(a){return w(this,null,function*(){var u;if(!a)return;!a.url&&!a.preview&&a.originFileObj&&(a.preview=yield e(a.originFileObj));const i=(u=a.url)!=null?u:"";n.value=i||a.preview||"",m.value=!0,d.value=a.name||i.slice(Math.max(0,i.lastIndexOf("/")+1))})}return{previewVisible:m,previewImage:n,previewTitle:d,handleCancel:f,handlePreview:g}}function D(e,m,n,d){const f=y([]),g=S(()=>{var t;return M(e.acceptFormat)?e.acceptFormat:x(e.acceptFormat)?e.acceptFormat(e.accept):(t=e.accept)==null?void 0:t.split(",").map(s=>s.startsWith(".")?s.slice(1):s).join(", ")});function a(t){return x(e.customFilename)?e.customFilename(t):t.type==="info"?t.response.originalName:t.response.fileName}function i(t){if(x(e.customThumbUrl))return e.customThumbUrl(t);if(d==="image")return t.type==="info",t.response.url}let u=!1;function l(t){function s(o,c){e.removeOnError?c.splice(c.indexOf(o),1):o.status="error"}const{file:r,fileList:v}=t;switch(r.status){case"done":{if(!r.response)return;const{ossId:o,url:c}=r.response;r.url=c,r.uid=o;const h={type:"upload",response:r.response};r.fileName=a(h),r.name=a(h),r.thumbUrl=i(h),u=!0,e.maxCount===1?n.value=o:(Array.isArray(n.value)||(n.value=[]),n.value=[...n.value,o]);break}case"error":s(r,v)}m("change",t)}function F(t){function s(){e.maxCount===1?n.value="":n.value.splice(n.value.indexOf(t.uid),1),m("remove",t)}return e.removeConfirm?new Promise(r=>{O.confirm({title:p("pages.common.tip"),content:p("component.upload.confirmDelete",[t.name]),okButtonProps:{danger:!0},centered:!0,onOk(){r(!0),s()},onCancel(){r(!1)}})}):(s(),!0)}function I(t){return t.size/1024/1024<e.maxSize?t:(C.error(p("component.upload.maxSize",[e.maxSize])),!1)}const U=new AbortController;function A(t){return w(this,null,function*(){const{api:s}=e;if(!x(s)){console.warn("upload api must exist and be a function");return}try{const r=o=>{const c=Math.trunc(o.loaded/o.total*100);t.onProgress({percent:c})},v=yield s(t.file,{onUploadProgress:r,signal:U.signal,otherData:e==null?void 0:e.data});t.onSuccess(v),e.showSuccessMsg&&C.success(p("component.upload.uploadSuccess")),m("success",t.file,v)}catch(r){console.error(r),t.onError(r)}})}return E(()=>{e.abortOnUnmounted&&U.abort()}),L(()=>n.value,t=>w(null,null,function*(){if(t.length===0){f.value=[];return}if(u){u=!1;return}const s=yield P(t);function r(o){const c={type:"info",response:o};return{uid:o.ossId,name:a(c),fileName:a(c),url:o.url,thumbUrl:i(c),status:"done"}}const v=s.map(o=>r(o));if(f.value=v,e.maxCount===1&&s.length===0&&!e.keepMissingId){n.value="";return}s.length!==t.length&&!e.keepMissingId&&e.maxCount!==1&&(Array.isArray(n.value)||(n.value=[]),n.value=n.value.filter(o=>s.map(c=>c.ossId).includes(o)))}),{immediate:!0}),{handleChange:l,handleRemove:F,beforeUpload:I,customRequest:A,innerFileList:f,acceptStr:g}}export{B as a,T as b,R as c,z as d,D as u};
