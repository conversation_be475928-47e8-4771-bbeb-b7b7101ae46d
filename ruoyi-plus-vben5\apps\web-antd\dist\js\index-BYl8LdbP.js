import{y as o}from"./bootstrap-DCMzVRvD.js";function e(i){return o.get("/workflow/definition/list",{params:i})}function f(i){return o.get("/workflow/definition/unPublishList",{params:i})}function r(i){return o.get(`/workflow/definition/${i}`)}function w(i){return o.postWithMsg("/workflow/definition",i)}function s(i){return o.putWithMsg("/workflow/definition",i)}function u(i){return o.putWithMsg(`/workflow/definition/publish/${i}`)}function l(i){return o.deleteWithMsg(`/workflow/definition/${i}`)}function a(i){return o.postWithMsg(`/workflow/definition/copy/${i}`)}function p(i){return o.postWithMsg("/workflow/definition/importDef",i,{headers:{"Content-Type":"multipart/form-data"}})}function k(i){return o.postWithMsg(`/workflow/definition/exportDef/${i}`,{},{responseType:"blob",isTransformResponse:!1})}function d(i,n){return o.putWithMsg(`/workflow/definition/active/${i}?active=${n}`)}export{e as a,l as b,d as c,u as d,a as e,k as f,r as g,s as h,w as i,f as u,p as w};
