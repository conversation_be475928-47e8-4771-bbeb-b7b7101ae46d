var k=(P,d,a)=>new Promise((r,g)=>{var y=u=>{try{v(a.next(u))}catch(h){g(h)}},m=u=>{try{v(a.throw(u))}catch(h){g(h)}},v=u=>u.done?r(u.value):Promise.resolve(u.value).then(y,m);v((a=a.apply(P,d)).next())});import{$ as o,bC as M,br as N,aB as q,bD as x,aN as F,bE as D,as as j,am as z,al as _,D as V,bp as G,ao as K}from"./bootstrap-DCMzVRvD.js";import{c as Q}from"./captcha-Bo71W0x_.js";import{u as U}from"./oauth-common-CrHfL2p7.js";import{_ as W}from"./oauth-login.vue_vue_type_script_setup_true_lang-CLt7AYa7.js";import{T as Y}from"./auth-title-CzJiGVH3.js";import{M as H,a as J,b as O}from"./index-A0HTSyFu.js";import{G as X}from"./index-C0wIoq37.js";import{d as R,c as $,o as f,j as w,t as p,b as t,a as l,w as c,s as Z,B as A,p as I,v as S,r as E,f as b,k as L,h as B,n as ee,P as te,E as ae}from"../jse/index-index-C-MnMZEz.js";const oe={class:"w-full sm:mx-auto md:max-w-md"},ne={class:"mt-4 flex items-center justify-between"},se={class:"text-muted-foreground text-center text-xs uppercase"},ie={class:"mt-4 flex flex-wrap justify-around"},re=R({name:"ThirdPartyLogin",__name:"third-party-login",emits:["oauthLogin"],setup(P){return(d,a)=>(f(),$("div",oe,[w("div",ne,[a[4]||(a[4]=w("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1)),w("span",se,p(t(o)("authentication.thirdPartyLogin")),1),a[5]||(a[5]=w("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1))]),w("div",ie,[l(t(M),{class:"mb-3",onClick:a[0]||(a[0]=r=>d.$emit("oauthLogin","wechat"))},{default:c(()=>[l(t(H),{class:"size-[24px] text-green-600"})]),_:1}),l(t(M),{class:"mb-3",onClick:a[1]||(a[1]=r=>d.$emit("oauthLogin","qq"))},{default:c(()=>[l(t(J),{class:"size-[24px]"})]),_:1}),l(t(M),{class:"mb-3",onClick:a[2]||(a[2]=r=>d.$emit("oauthLogin","github"))},{default:c(()=>[l(t(O),{class:"size-[24px]"})]),_:1}),l(t(M),{class:"mb-3",onClick:a[3]||(a[3]=r=>d.$emit("oauthLogin","gitee"))},{default:c(()=>[l(t(X),{class:"size-[24px] text-red-700"})]),_:1})])]))}}),le=["onKeydown"],ue={class:"text-muted-foreground"},de={key:0,class:"mb-6 flex justify-between"},ce={class:"flex-center"},me={key:1,class:"mb-2 mt-4 flex items-center justify-between"},pe={key:0,class:"mt-3 text-center text-sm"},fe=R({name:"AuthenticationLogin",__name:"login",props:{formSchema:{default:()=>[]},codeLoginPath:{default:"/auth/code-login"},forgetPasswordPath:{default:"/auth/forget-password"},loading:{type:Boolean,default:!1},qrCodeLoginPath:{default:"/auth/qrcode-login"},registerPath:{default:"/auth/register"},showCodeLogin:{type:Boolean,default:!0},showForgetPassword:{type:Boolean,default:!0},showQrcodeLogin:{type:Boolean,default:!0},showRegister:{type:Boolean,default:!0},showRememberMe:{type:Boolean,default:!0},showThirdPartyLogin:{type:Boolean,default:!0},subTitle:{default:""},title:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(P,{expose:d,emit:a}){const r=P,g=a,[y,m]=N(Z({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:A(()=>r.formSchema),showDefaultActions:!1})),v=q(),u=`REMEMBER_ME_USERNAME_${location.hostname}`,h=localStorage.getItem(u)||"",T=I(!!h);function n(){return k(this,null,function*(){const{valid:e}=yield m.validate();if(e){const i=te(yield m.getValues());localStorage.setItem(u,T.value?i==null?void 0:i.username:""),i.grantType="password",g("submit",i)}})}function s(e){v.push(e)}return S(()=>{h&&m.setFieldValue("username",h)}),d({getFormApi:()=>m}),(e,i)=>(f(),$("div",{onKeydown:D(j(n,["prevent"]),["enter"])},[E(e.$slots,"title",{},()=>[l(Y,null,{desc:c(()=>[w("span",ue,[E(e.$slots,"subTitle",{},()=>[L(p(e.subTitle||t(o)("authentication.loginSubtitle")),1)])])]),default:c(()=>[E(e.$slots,"title",{},()=>[L(p(e.title||`${t(o)("authentication.welcomeBack")} 👋🏻`),1)])]),_:3})]),l(t(y)),e.showRememberMe||e.showForgetPassword?(f(),$("div",de,[w("div",ce,[e.showRememberMe?(f(),B(t(x),{key:0,checked:T.value,"onUpdate:checked":i[0]||(i[0]=C=>T.value=C),name:"rememberMe"},{default:c(()=>[L(p(t(o)("authentication.rememberMe")),1)]),_:1},8,["checked"])):b("",!0)]),e.showForgetPassword?(f(),$("span",{key:0,class:"vben-link text-sm font-normal",onClick:i[1]||(i[1]=C=>s(e.forgetPasswordPath))},p(t(o)("authentication.forgetPassword")),1)):b("",!0)])):b("",!0),l(t(F),{class:ee([{"cursor-wait":e.loading},"w-full"]),loading:e.loading,"aria-label":"login",onClick:n},{default:c(()=>[L(p(e.submitButtonText||t(o)("common.login")),1)]),_:1},8,["class","loading"]),e.showCodeLogin||e.showQrcodeLogin?(f(),$("div",me,[e.showCodeLogin?(f(),B(t(F),{key:0,class:"w-1/2",variant:"outline",onClick:i[2]||(i[2]=C=>s(e.codeLoginPath))},{default:c(()=>[L(p(t(o)("authentication.mobileLogin")),1)]),_:1})):b("",!0),e.showQrcodeLogin?(f(),B(t(F),{key:1,class:"ml-4 w-1/2",variant:"outline",onClick:i[3]||(i[3]=C=>s(e.qrCodeLoginPath))},{default:c(()=>[L(p(t(o)("authentication.qrcodeLogin")),1)]),_:1})):b("",!0)])):b("",!0),e.showThirdPartyLogin?E(e.$slots,"third-party-login",{key:2},()=>[l(re)]):b("",!0),E(e.$slots,"to-register",{},()=>[e.showRegister?(f(),$("div",pe,[L(p(t(o)("authentication.accountTip"))+" ",1),w("span",{class:"vben-link text-sm font-normal",onClick:i[4]||(i[4]=C=>s(e.registerPath))},p(t(o)("authentication.createAccount")),1)])):b("",!0)])],40,le))}}),Pe=R({name:"Login",__name:"login",setup(P){const d=z(),a=ae("loginFormRef"),r=I({captchaEnabled:!1,img:"",uuid:""}),g=I(!1);function y(){return k(this,null,function*(){try{g.value=!0;const n=yield Q();n.captchaEnabled&&(n.img=`data:image/png;base64,${n.img}`),r.value=n}catch(n){console.error(n)}finally{g.value=!1}})}const m=I({tenantEnabled:!1,voList:[]});function v(){return k(this,null,function*(){var s;const n=yield G();if(m.value=n,n.tenantEnabled&&n.voList.length>0){const e=n.voList[1].tenantId;(s=a.value)==null||s.getFormApi().setFieldValue("tenantId",e)}})}S(()=>k(null,null,function*(){yield Promise.all([y(),v()])}));const{loginTenantId:u}=U(),h=A(()=>{var n;return[{component:"VbenSelect",componentProps:{class:"bg-background h-[40px] focus:border-primary",contentClass:"max-h-[256px] overflow-y-auto",options:(n=m.value.voList)==null?void 0:n.map(s=>({label:s.companyName,value:s.tenantId})),placeholder:o("authentication.selectAccount")},defaultValue:V,dependencies:{if:()=>m.value.tenantEnabled,trigger:s=>{var e;u.value=(e=s==null?void 0:s.tenantId)!=null?e:V},triggerFields:["","tenantId"]},fieldName:"tenantId",label:o("authentication.selectAccount"),rules:_().min(1,{message:o("authentication.selectAccount")})},{component:"VbenInput",componentProps:{class:"focus:border-primary",placeholder:o("authentication.usernameTip")},defaultValue:"",fieldName:"username",label:o("authentication.username"),rules:_().min(1,{message:o("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{class:"focus:border-primary",placeholder:o("authentication.password")},defaultValue:"",fieldName:"password",label:o("authentication.password"),rules:_().min(5,{message:o("authentication.passwordTip")})},{component:"VbenInputCaptcha",componentProps:{captcha:r.value.img,class:"focus:border-primary",onCaptchaClick:y,placeholder:o("authentication.code"),loading:g.value},dependencies:{if:()=>r.value.captchaEnabled,triggerFields:[""]},fieldName:"code",label:o("authentication.code"),rules:_().min(1,{message:o("authentication.verifyRequiredTip")})}]});function T(n){return k(this,null,function*(){var s;try{const e=K(n,["code"]);r.value.captchaEnabled&&(e.code=n.code,e.uuid=r.value.uuid),yield d.authLogin(e)}catch(e){console.error(e),e instanceof Error&&((s=a.value)==null||s.getFormApi().setFieldValue("code",""),yield y())}})}return(n,s)=>(f(),B(t(fe),{ref_key:"loginFormRef",ref:a,"form-schema":h.value,loading:t(d).loginLoading,"show-register":!1,"show-third-party-login":!0,onSubmit:T},{"third-party-login":c(()=>[l(W)]),_:1},8,["form-schema","loading"]))}});export{Pe as _};
