import{_ as b,bO as S,bP as B,o as m,bk as ne,h as te,aG as q,p as Q,aH as le,b4 as ue,aE as oe,c as R,bQ as se,g as P}from"./bootstrap-DCMzVRvD.js";import{V as re}from"./Checkbox-DRV8G-PI.js";import{u as X}from"./index-DabkQ3D7.js";import{d as z,B as y,D as ce,ac as ie,a5 as de,v as ve,p as I,a as O,q as H,a4 as fe}from"../jse/index-index-C-MnMZEz.js";const me=()=>({name:String,prefixCls:String,options:B([]),disabled:Boolean,id:String}),be=()=>b(b({},me()),{defaultValue:B(),value:B(),onChange:S(),"onUpdate:value":S()}),he=()=>({prefixCls:String,defaultChecked:m(),checked:m(),disabled:m(),isGroup:m(),value:te.any,name:String,id:String,indeterminate:m(),type:ne("checkbox"),autofocus:m(),onChange:S(),"onUpdate:checked":S(),onClick:S(),skipGroup:m(!1)}),ge=()=>b(b({},he()),{indeterminate:m(!1)}),J=Symbol("CheckboxGroupContext");var K=function(e,h){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&h.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)h.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o};const w=z({compatConfig:{MODE:3},name:"ACheckbox",inheritAttrs:!1,__ANT_CHECKBOX:!0,props:ge(),setup(e,h){let{emit:o,attrs:t,slots:r,expose:_}=h;const C=q(),G=se.useInject(),{prefixCls:c,direction:g,disabled:j}=Q("checkbox",e),$=le(),[i,f]=X(c),l=de(J,void 0),p=Symbol("checkboxUniId"),M=y(()=>(l==null?void 0:l.disabled.value)||j.value);ce(()=>{!e.skipGroup&&l&&l.registerValue(p,e.value)}),ie(()=>{l&&l.cancelValue(p)}),ve(()=>{ue(!!(e.checked!==void 0||l||e.value===void 0))});const A=a=>{const s=a.target.checked;o("update:checked",s),o("change",a),C.onFieldChange()},x=I();return _({focus:()=>{var a;(a=x.value)===null||a===void 0||a.focus()},blur:()=>{var a;(a=x.value)===null||a===void 0||a.blur()}}),()=>{var a;const s=oe((a=r.default)===null||a===void 0?void 0:a.call(r)),{indeterminate:u,skipGroup:d,id:D=C.id.value}=e,E=K(e,["indeterminate","skipGroup","id"]),{onMouseenter:T,onMouseleave:k,onInput:pe,class:W,style:Y}=t,Z=K(t,["onMouseenter","onMouseleave","onInput","class","style"]),v=b(b(b(b({},E),{id:D,prefixCls:c.value}),Z),{disabled:M.value});l&&!d?(v.onChange=function(){for(var U=arguments.length,N=new Array(U),V=0;V<U;V++)N[V]=arguments[V];o("change",...N),l.toggleOption({label:s,value:e.value})},v.name=l.name.value,v.checked=l.mergedValue.value.includes(e.value),v.disabled=M.value||$.value,v.indeterminate=u):v.onChange=A;const ee=R({[`${c.value}-wrapper`]:!0,[`${c.value}-rtl`]:g.value==="rtl",[`${c.value}-wrapper-checked`]:v.checked,[`${c.value}-wrapper-disabled`]:v.disabled,[`${c.value}-wrapper-in-form-item`]:G.isFormItemInput},W,f.value),ae=R({[`${c.value}-indeterminate`]:u},f.value);return i(O("label",{class:ee,style:Y,onMouseenter:T,onMouseleave:k},[O(re,P(P({"aria-checked":u?"mixed":void 0},v),{},{class:ae,ref:x}),null),s.length?O("span",null,[s]):null]))}}}),F=z({compatConfig:{MODE:3},name:"ACheckboxGroup",inheritAttrs:!1,props:be(),setup(e,h){let{slots:o,attrs:t,emit:r,expose:_}=h;const C=q(),{prefixCls:G,direction:c}=Q("checkbox",e),g=y(()=>`${G.value}-group`),[j,$]=X(g),i=I((e.value===void 0?e.defaultValue:e.value)||[]);H(()=>e.value,()=>{i.value=e.value||[]});const f=y(()=>e.options.map(n=>typeof n=="string"||typeof n=="number"?{label:n,value:n}:n)),l=I(Symbol()),p=I(new Map),M=n=>{p.value.delete(n),l.value=Symbol()},A=(n,a)=>{p.value.set(n,a),l.value=Symbol()},x=I(new Map);return H(l,()=>{const n=new Map;for(const a of p.value.values())n.set(a,!0);x.value=n}),fe(J,{cancelValue:M,registerValue:A,toggleOption:n=>{const a=i.value.indexOf(n.value),s=[...i.value];a===-1?s.push(n.value):s.splice(a,1),e.value===void 0&&(i.value=s);const u=s.filter(d=>x.value.has(d)).sort((d,D)=>{const E=f.value.findIndex(k=>k.value===d),T=f.value.findIndex(k=>k.value===D);return E-T});r("update:value",u),r("change",u),C.onFieldChange()},mergedValue:i,name:y(()=>e.name),disabled:y(()=>e.disabled)}),_({mergedValue:i}),()=>{var n;const{id:a=C.id.value}=e;let s=null;return f.value&&f.value.length>0&&(s=f.value.map(u=>{var d;return O(w,{prefixCls:G.value,key:u.value.toString(),disabled:"disabled"in u?u.disabled:e.disabled,indeterminate:u.indeterminate,value:u.value,checked:i.value.indexOf(u.value)!==-1,onChange:u.onChange,class:`${g.value}-item`},{default:()=>[o.label!==void 0?(d=o.label)===null||d===void 0?void 0:d.call(o,u):u.label]})})),j(O("div",P(P({},t),{},{class:[g.value,{[`${g.value}-rtl`]:c.value==="rtl"},t.class,$.value],id:a}),[s||((n=o.default)===null||n===void 0?void 0:n.call(o))]))}}});w.Group=F;w.install=function(e){return e.component(w.name,w),e.component(F.name,F),e};export{F as CheckboxGroup,be as checkboxGroupProps,ge as checkboxProps,w as default};
