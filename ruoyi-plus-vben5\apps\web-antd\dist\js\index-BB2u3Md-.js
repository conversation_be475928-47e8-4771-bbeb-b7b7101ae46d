import{r as i,R as o,u as d,a as p}from"./Group-oWwucTzK.js";import{p as m,g as n}from"./bootstrap-DCMzVRvD.js";import{d as l,a as f}from"../jse/index-index-C-MnMZEz.js";import"./Checkbox-DRV8G-PI.js";const c=l({compatConfig:{MODE:3},name:"ARadioButton",inheritAttrs:!1,props:i(),setup(t,r){let{slots:a,attrs:s}=r;const{prefixCls:u}=m("radio",t);return d("button"),()=>{var e;return f(o,n(n(n({},s),t),{},{prefixCls:u.value}),{default:()=>[(e=a.default)===null||e===void 0?void 0:e.call(a)]})}}});o.Group=p;o.Button=c;o.install=function(t){return t.component(o.name,o),t.component(o.Group.name,o.Group),t.component(o.Button.name,o.Button),t};export{c as Button,p as Group,c as RadioButton,p as RadioGroup,o as default};
