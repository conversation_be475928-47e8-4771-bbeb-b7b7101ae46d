{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/space/style/compact.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/space/style/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/space/Compact.js"], "sourcesContent": ["const genSpaceCompactStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'inline-flex',\n      '&-block': {\n        display: 'flex',\n        width: '100%'\n      },\n      '&-vertical': {\n        flexDirection: 'column'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSpaceCompactStyle;", "import { genComponentStyleHook } from '../../theme/internal';\nimport genSpaceCompactStyle from './compact';\nconst genSpaceStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'inline-flex',\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-vertical': {\n        flexDirection: 'column'\n      },\n      '&-align': {\n        flexDirection: 'column',\n        '&-center': {\n          alignItems: 'center'\n        },\n        '&-start': {\n          alignItems: 'flex-start'\n        },\n        '&-end': {\n          alignItems: 'flex-end'\n        },\n        '&-baseline': {\n          alignItems: 'baseline'\n        }\n      },\n      [`${componentCls}-item`]: {\n        '&:empty': {\n          display: 'none'\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook('Space', token => [genSpaceStyle(token), genSpaceCompactStyle(token)]);", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nimport classNames from '../_util/classNames';\nimport createContext from '../_util/createContext';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nimport useStyle from './style';\nimport { computed, defineComponent } from 'vue';\nimport PropTypes from '../_util/vue-types';\nimport { booleanType, tuple } from '../_util/type';\nimport { isEmpty } from 'lodash-es';\nimport { flattenChildren } from '../_util/props-util';\nexport const spaceCompactItemProps = () => ({\n  compactSize: String,\n  compactDirection: PropTypes.oneOf(tuple('horizontal', 'vertical')).def('horizontal'),\n  isFirstItem: booleanType(),\n  isLastItem: booleanType()\n});\nexport const SpaceCompactItemContext = createContext(null);\nexport const useCompactItemContext = (prefixCls, direction) => {\n  const compactItemContext = SpaceCompactItemContext.useInject();\n  const compactItemClassnames = computed(() => {\n    if (!compactItemContext || isEmpty(compactItemContext)) return '';\n    const {\n      compactDirection,\n      isFirstItem,\n      isLastItem\n    } = compactItemContext;\n    const separator = compactDirection === 'vertical' ? '-vertical-' : '-';\n    return classNames({\n      [`${prefixCls.value}-compact${separator}item`]: true,\n      [`${prefixCls.value}-compact${separator}first-item`]: isFirstItem,\n      [`${prefixCls.value}-compact${separator}last-item`]: isLastItem,\n      [`${prefixCls.value}-compact${separator}item-rtl`]: direction.value === 'rtl'\n    });\n  });\n  return {\n    compactSize: computed(() => compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactSize),\n    compactDirection: computed(() => compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactDirection),\n    compactItemClassnames\n  };\n};\nexport const NoCompactStyle = defineComponent({\n  name: 'NoCompactStyle',\n  setup(_, _ref) {\n    let {\n      slots\n    } = _ref;\n    SpaceCompactItemContext.useProvide(null);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport const spaceCompactProps = () => ({\n  prefixCls: String,\n  size: {\n    type: String\n  },\n  direction: PropTypes.oneOf(tuple('horizontal', 'vertical')).def('horizontal'),\n  align: PropTypes.oneOf(tuple('start', 'end', 'center', 'baseline')),\n  block: {\n    type: Boolean,\n    default: undefined\n  }\n});\nconst CompactItem = defineComponent({\n  name: 'CompactItem',\n  props: spaceCompactItemProps(),\n  setup(props, _ref2) {\n    let {\n      slots\n    } = _ref2;\n    SpaceCompactItemContext.useProvide(props);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nconst Compact = defineComponent({\n  name: 'ASpaceCompact',\n  inheritAttrs: false,\n  props: spaceCompactProps(),\n  setup(props, _ref3) {\n    let {\n      attrs,\n      slots\n    } = _ref3;\n    const {\n      prefixCls,\n      direction: directionConfig\n    } = useConfigInject('space-compact', props);\n    const compactItemContext = SpaceCompactItemContext.useInject();\n    const [wrapSSR, hashId] = useStyle(prefixCls);\n    const clx = computed(() => {\n      return classNames(prefixCls.value, hashId.value, {\n        [`${prefixCls.value}-rtl`]: directionConfig.value === 'rtl',\n        [`${prefixCls.value}-block`]: props.block,\n        [`${prefixCls.value}-vertical`]: props.direction === 'vertical'\n      });\n    });\n    return () => {\n      var _a;\n      const childNodes = flattenChildren(((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)) || []);\n      // =========================== Render ===========================\n      if (childNodes.length === 0) {\n        return null;\n      }\n      return wrapSSR(_createVNode(\"div\", _objectSpread(_objectSpread({}, attrs), {}, {\n        \"class\": [clx.value, attrs.class]\n      }), [childNodes.map((child, i) => {\n        var _a;\n        const key = child && child.key || `${prefixCls.value}-item-${i}`;\n        const noCompactItemContext = !compactItemContext || isEmpty(compactItemContext);\n        return _createVNode(CompactItem, {\n          \"key\": key,\n          \"compactSize\": (_a = props.size) !== null && _a !== void 0 ? _a : 'middle',\n          \"compactDirection\": props.direction,\n          \"isFirstItem\": i === 0 && (noCompactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isFirstItem)),\n          \"isLastItem\": i === childNodes.length - 1 && (noCompactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isLastItem))\n        }, {\n          default: () => [child]\n        });\n      })]));\n    };\n  }\n});\nexport default Compact;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,uBAAuB,WAAS;AACpC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,MACd,SAAS;AAAA,MACT,WAAW;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,kBAAQ;;;AChBf,IAAM,gBAAgB,WAAS;AAC7B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,cAAc;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,MACA,WAAW;AAAA,QACT,eAAe;AAAA,QACf,YAAY;AAAA,UACV,YAAY;AAAA,QACd;AAAA,QACA,WAAW;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,UACP,YAAY;AAAA,QACd;AAAA,QACA,cAAc;AAAA,UACZ,YAAY;AAAA,QACd;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,QACxB,WAAW;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,gBAAQ,sBAAsB,SAAS,WAAS,CAAC,cAAc,KAAK,GAAG,gBAAqB,KAAK,CAAC,CAAC;;;AC5BnG,IAAM,wBAAwB,OAAO;AAAA,EAC1C,aAAa;AAAA,EACb,kBAAkB,kBAAU,MAAM,MAAM,cAAc,UAAU,CAAC,EAAE,IAAI,YAAY;AAAA,EACnF,aAAa,YAAY;AAAA,EACzB,YAAY,YAAY;AAC1B;AACO,IAAM,0BAA0B,sBAAc,IAAI;AAClD,IAAM,wBAAwB,CAAC,WAAW,cAAc;AAC7D,QAAM,qBAAqB,wBAAwB,UAAU;AAC7D,QAAM,wBAAwB,SAAS,MAAM;AAC3C,QAAI,CAAC,sBAAsB,gBAAQ,kBAAkB,EAAG,QAAO;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,qBAAqB,aAAa,eAAe;AACnE,WAAO,mBAAW;AAAA,MAChB,CAAC,GAAG,UAAU,KAAK,WAAW,SAAS,MAAM,GAAG;AAAA,MAChD,CAAC,GAAG,UAAU,KAAK,WAAW,SAAS,YAAY,GAAG;AAAA,MACtD,CAAC,GAAG,UAAU,KAAK,WAAW,SAAS,WAAW,GAAG;AAAA,MACrD,CAAC,GAAG,UAAU,KAAK,WAAW,SAAS,UAAU,GAAG,UAAU,UAAU;AAAA,IAC1E,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AAAA,IACL,aAAa,SAAS,MAAM,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,WAAW;AAAA,IAClI,kBAAkB,SAAS,MAAM,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,gBAAgB;AAAA,IAC5I;AAAA,EACF;AACF;AACO,IAAM,iBAAiB,gBAAgB;AAAA,EAC5C,MAAM;AAAA,EACN,MAAM,GAAG,MAAM;AACb,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,4BAAwB,WAAW,IAAI;AACvC,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AACM,IAAM,oBAAoB,OAAO;AAAA,EACtC,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,WAAW,kBAAU,MAAM,MAAM,cAAc,UAAU,CAAC,EAAE,IAAI,YAAY;AAAA,EAC5E,OAAO,kBAAU,MAAM,MAAM,SAAS,OAAO,UAAU,UAAU,CAAC;AAAA,EAClE,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAM,cAAc,gBAAgB;AAAA,EAClC,MAAM;AAAA,EACN,OAAO,sBAAsB;AAAA,EAC7B,MAAM,OAAO,OAAO;AAClB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,4BAAwB,WAAW,KAAK;AACxC,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AACD,IAAM,UAAU,gBAAgB;AAAA,EAC9B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,kBAAkB;AAAA,EACzB,MAAM,OAAO,OAAO;AAClB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,IACb,IAAI,wBAAgB,iBAAiB,KAAK;AAC1C,UAAM,qBAAqB,wBAAwB,UAAU;AAC7D,UAAM,CAAC,SAAS,MAAM,IAAI,cAAS,SAAS;AAC5C,UAAM,MAAM,SAAS,MAAM;AACzB,aAAO,mBAAW,UAAU,OAAO,OAAO,OAAO;AAAA,QAC/C,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,gBAAgB,UAAU;AAAA,QACtD,CAAC,GAAG,UAAU,KAAK,QAAQ,GAAG,MAAM;AAAA,QACpC,CAAC,GAAG,UAAU,KAAK,WAAW,GAAG,MAAM,cAAc;AAAA,MACvD,CAAC;AAAA,IACH,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,YAAM,aAAa,kBAAkB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC;AAEnH,UAAI,WAAW,WAAW,GAAG;AAC3B,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,YAAa,OAAO,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QAC7E,SAAS,CAAC,IAAI,OAAO,MAAM,KAAK;AAAA,MAClC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,OAAO,MAAM;AAChC,YAAIA;AACJ,cAAM,MAAM,SAAS,MAAM,OAAO,GAAG,UAAU,KAAK,SAAS,CAAC;AAC9D,cAAM,uBAAuB,CAAC,sBAAsB,gBAAQ,kBAAkB;AAC9E,eAAO,YAAa,aAAa;AAAA,UAC/B,OAAO;AAAA,UACP,gBAAgBA,MAAK,MAAM,UAAU,QAAQA,QAAO,SAASA,MAAK;AAAA,UAClE,oBAAoB,MAAM;AAAA,UAC1B,eAAe,MAAM,MAAM,yBAAyB,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB;AAAA,UAC/I,cAAc,MAAM,WAAW,SAAS,MAAM,yBAAyB,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB;AAAA,QACpK,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,KAAK;AAAA,QACvB,CAAC;AAAA,MACH,CAAC,CAAC,CAAC,CAAC;AAAA,IACN;AAAA,EACF;AACF,CAAC;AACD,IAAO,kBAAQ;", "names": ["_a"]}