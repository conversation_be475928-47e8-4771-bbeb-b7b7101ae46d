{"doc": " 数据库类型\n\n <AUTHOR> Li\n", "fields": [{"name": "type", "doc": " 数据库类型\n"}], "enumConstants": [{"name": "MY_SQL", "doc": " MySQL\n"}, {"name": "ORACLE", "doc": " Oracle\n"}, {"name": "POSTGRE_SQL", "doc": " PostgreSQL\n"}, {"name": "SQL_SERVER", "doc": " SQL Server\n"}], "methods": [{"name": "find", "paramTypes": ["java.lang.String"], "doc": " 根据数据库产品名称查找对应的数据库类型\n\n @param databaseProductName 数据库产品名称\n @return 对应的数据库类型枚举值，如果未找到则返回 null\n"}], "constructors": []}