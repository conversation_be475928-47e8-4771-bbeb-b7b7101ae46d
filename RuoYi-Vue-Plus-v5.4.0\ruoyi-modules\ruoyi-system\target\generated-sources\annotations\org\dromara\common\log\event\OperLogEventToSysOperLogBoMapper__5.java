package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__5;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysOperLogBoToSysOperLogMapper__5.class,SysOperLogBoToOperLogEventMapper__5.class},
    imports = {}
)
public interface OperLogEventToSysOperLogBoMapper__5 extends BaseMapper<OperLogEvent, SysOperLogBo> {
}
