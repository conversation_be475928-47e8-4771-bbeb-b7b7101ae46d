import{bm as s}from"./bootstrap-DCMzVRvD.js";import{g as a,h as i,i as o}from"./index-C0wIoq37.js";import{a as t,a3 as n,M as c}from"../jse/index-index-C-MnMZEz.js";const u={C:{icon:n(o),value:"菜单"},F:{icon:n(i),value:"按钮"},M:{icon:n(a),value:"目录"}},m=[{label:"节点关联",value:!0},{label:"节点独立",value:!1}],p=[{type:"checkbox",title:"菜单名称",field:"label",treeNode:!0,headerAlign:"left",align:"left",width:230},{title:"图标",field:"icon",width:80,slots:{default:({row:e})=>(e==null?void 0:e.icon)==="#"?"":t("span",{class:"flex justify-center"},[t(s,{icon:e.icon},null)])}},{title:"类型",field:"menuType",width:80,slots:{default:({row:e})=>{const l=u[e.menuType];return l?t("span",{class:"flex items-center justify-center gap-1"},[c(l.icon,{class:"size-[18px]"}),t("span",null,[l.value])]):"未知"}}},{title:"权限标识",field:"permissions",headerAlign:"left",align:"left",slots:{default:"permissions"}}];export{p as c,m as n};
