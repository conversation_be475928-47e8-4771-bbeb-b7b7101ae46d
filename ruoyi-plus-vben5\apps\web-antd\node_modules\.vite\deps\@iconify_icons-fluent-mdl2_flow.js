import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-fluent-mdl2@1.2.1/node_modules/@iconify/icons-fluent-mdl2/flow.js
var require_flow = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-fluent-mdl2@1.2.1/node_modules/@iconify/icons-fluent-mdl2/flow.js"(exports) {
    var data = {
      "width": 2048,
      "height": 2048,
      "body": '<path fill="currentColor" d="M1408 256h640v640h-640V640h-120l-449 896H640v256H0v-640h640v256h120l449-896h199V256zM512 1664v-384H128v384h384zm1408-896V384h-384v384h384z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_flow();
//# sourceMappingURL=@iconify_icons-fluent-mdl2_flow.js.map
