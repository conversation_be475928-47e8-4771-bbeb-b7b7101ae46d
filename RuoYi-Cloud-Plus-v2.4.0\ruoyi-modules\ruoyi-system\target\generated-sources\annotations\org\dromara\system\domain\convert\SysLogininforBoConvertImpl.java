package org.dromara.system.domain.convert;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.bo.RemoteLogininforBo;
import org.dromara.system.domain.bo.SysLogininforBo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T10:20:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class SysLogininforBoConvertImpl implements SysLogininforBoConvert {

    @Override
    public SysLogininforBo convert(RemoteLogininforBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysLogininforBo sysLogininforBo = new SysLogininforBo();

        sysLogininforBo.setInfoId( arg0.getInfoId() );
        sysLogininforBo.setTenantId( arg0.getTenantId() );
        sysLogininforBo.setUserName( arg0.getUserName() );
        sysLogininforBo.setClientKey( arg0.getClientKey() );
        sysLogininforBo.setDeviceType( arg0.getDeviceType() );
        sysLogininforBo.setIpaddr( arg0.getIpaddr() );
        sysLogininforBo.setLoginLocation( arg0.getLoginLocation() );
        sysLogininforBo.setBrowser( arg0.getBrowser() );
        sysLogininforBo.setOs( arg0.getOs() );
        sysLogininforBo.setStatus( arg0.getStatus() );
        sysLogininforBo.setMsg( arg0.getMsg() );
        sysLogininforBo.setLoginTime( arg0.getLoginTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysLogininforBo.setParams( new LinkedHashMap<String, Object>( map ) );
        }

        return sysLogininforBo;
    }

    @Override
    public SysLogininforBo convert(RemoteLogininforBo arg0, SysLogininforBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setInfoId( arg0.getInfoId() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setClientKey( arg0.getClientKey() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setIpaddr( arg0.getIpaddr() );
        arg1.setLoginLocation( arg0.getLoginLocation() );
        arg1.setBrowser( arg0.getBrowser() );
        arg1.setOs( arg0.getOs() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setMsg( arg0.getMsg() );
        arg1.setLoginTime( arg0.getLoginTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }

        return arg1;
    }
}
