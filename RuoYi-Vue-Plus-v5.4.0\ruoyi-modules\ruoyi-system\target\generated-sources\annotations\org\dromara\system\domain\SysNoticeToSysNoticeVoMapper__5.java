package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__5;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysNoticeVoToSysNoticeMapper__5.class,SysNoticeBoToSysNoticeMapper__5.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__5 extends BaseMapper<SysNotice, SysNoticeVo> {
}
