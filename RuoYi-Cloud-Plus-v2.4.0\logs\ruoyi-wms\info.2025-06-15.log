2025-06-15 21:44:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:44:51 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 43104 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:44:51 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 21:44:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:44:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:44:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:44:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:44:58 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:45:15 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:45:15 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:45:16 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:45:16 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:45:16 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d299393
2025-06-15 21:45:17 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 21:45:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 21:45:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 21:48:03 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:48:03 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 48268 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:48:03 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 21:48:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:48:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:48:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:48:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:48:10 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:48:27 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:48:27 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:48:28 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:48:28 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:48:28 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@158e9f6e
2025-06-15 21:48:29 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 21:48:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 21:48:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 21:49:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:49:30 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 40412 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:49:30 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 21:49:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:49:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:49:37 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:49:54 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:49:54 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:49:55 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:49:55 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:49:55 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b8572df
2025-06-15 21:49:56 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 21:49:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 21:49:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:02:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:02:38 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 53300 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:02:38 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:02:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:02:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:02:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:02:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:02:45 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:03:03 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:03:03 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:03:03 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:03:03 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:03:03 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@65e22def
2025-06-15 22:03:04 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:03:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:03:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:05:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:05:51 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 35784 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:05:51 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:05:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:05:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:05:57 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:06:15 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:06:15 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:06:15 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:06:15 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:06:15 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@287ae90c
2025-06-15 22:06:16 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:06:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:06:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:09:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:09:54 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 35108 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:09:54 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:09:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:09:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:10:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:10:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:10:01 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:10:18 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:10:18 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:10:19 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:10:19 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:10:19 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@66223d94
2025-06-15 22:10:20 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:10:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:10:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:17:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:17:20 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 52084 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:17:20 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:17:20 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:17:20 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:17:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:17:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:17:26 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:17:44 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:17:44 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:17:44 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:17:44 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:17:44 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@235b4cb8
2025-06-15 22:17:45 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:17:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:17:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:28:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:28:56 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 33768 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:28:56 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:28:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:28:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:29:02 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:29:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:29:03 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:29:20 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:29:20 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:29:21 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:29:21 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:29:21 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@9b5f3c7
2025-06-15 22:29:22 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:29:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:29:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:32:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:32:06 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 43824 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:32:06 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:32:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:32:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:32:12 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:32:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:32:14 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:32:31 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:32:31 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:32:31 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:32:31 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:32:31 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1b0e9707
2025-06-15 22:32:32 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:32:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:32:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:49:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:49:54 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 55576 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:49:54 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:49:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:49:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:50:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:50:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:50:01 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:50:18 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:50:18 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:50:19 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:50:19 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:50:19 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@61ae0d43
2025-06-15 22:50:19 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:50:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:50:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:52:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:52:58 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Starting RuoYiWMSApplication using Java 17.0.14 with PID 36844 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:52:58 [main] INFO  org.dromara.wms.RuoYiWMSApplication - The following 1 profile is active: "dev"
2025-06-15 22:52:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-wms.yml, group=DEFAULT_GROUP] success
2025-06-15 22:52:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:52:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:53:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:53:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:53:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Starting...
2025-06-15 22:53:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - wms - Added connection com.mysql.cj.jdbc.ConnectionImpl@e60c516
2025-06-15 22:53:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - wms - Start completed.
2025-06-15 22:53:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [wms] success
2025-06-15 22:53:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [wms]
2025-06-15 22:53:05 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:53:22 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:53:22 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:53:23 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:53:23 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:53:23 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@74024f3
2025-06-15 22:53:25 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 22:53:25 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 22:53:25 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 22:53:25 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 22:53:26 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-wms *************:9220 register finished
2025-06-15 22:53:31 [main] INFO  org.dromara.wms.RuoYiWMSApplication - Started RuoYiWMSApplication in 36.365 seconds (process running for 37.175)
2025-06-15 22:53:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:53:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 22:53:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-wms.yml, group=DEFAULT_GROUP
2025-06-15 22:53:31 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 22:53:50 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
