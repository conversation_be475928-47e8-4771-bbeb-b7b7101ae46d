2025-06-12 16:14:29 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 22.0.2 with PID 24164 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:14:29 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 16:14:30 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 16:14:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:14:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1008 ms
2025-06-12 16:14:30 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 16:14:30 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 16:14:30 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 16:14:31 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 16:14:31 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 16:14:31 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 16:14:31 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 16:14:31 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 16:14:31 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.95 seconds (process running for 3.511)
2025-06-12 16:14:31 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 16:14:31 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 16:14:31 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-12 16:14:32 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 1836e068f522
2025-06-12 16:14:32 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【UP】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:15:02 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【UP】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:15:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 16:15:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 16:15:44 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 16:15:44 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 16:17:04 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 22.0.2 with PID 10480 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:17:04 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 16:17:05 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 16:17:05 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:17:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1043 ms
2025-06-12 16:17:05 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 16:17:05 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 16:17:06 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 16:17:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 16:17:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 16:17:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 16:17:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 16:17:06 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 16:17:06 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.764 seconds (process running for 3.23)
2025-06-12 16:17:06 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 16:17:06 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 16:17:06 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-12 16:17:07 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 1836e068f522
2025-06-12 16:17:07 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【UP】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:17:24 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【UP】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:17:46 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【UP】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:18:28 [reactor-http-nio-5] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=28719134fffc, version=2, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://162.168.2.49:8080/actuator, healthUrl=http://162.168.2.49:8080/actuator/health, serviceUrl=http://162.168.2.49:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=755766726656, free=323841671168, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=3.0.504}}}), statusTimestamp=2025-06-12T08:17:46.679628100Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.49:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.49:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.49:8080/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.49:8080/actuator/health), env=Endpoint(id=env, url=http://162.168.2.49:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.49:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.49:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.49:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://162.168.2.49:8080/actuator/startup), beans=Endpoint(id=beans, url=http://162.168.2.49:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.49:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.49:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.49:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.49:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.49:8080/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.49:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /162.168.2.49:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:600)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:546)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:262)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:603)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:596)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:572)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:505)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:649)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:642)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:131)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /162.168.2.49:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:1060)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1570)
2025-06-12 16:18:28 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【OFFLINE】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:19:36 [reactor-http-nio-9] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【UP】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:20:40 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 16:20:40 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 16:20:40 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 16:20:40 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 16:21:11 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 22.0.2 with PID 17428 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:21:11 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 16:21:12 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 16:21:12 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:21:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 947 ms
2025-06-12 16:21:12 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 16:21:12 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 16:21:12 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 16:21:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 16:21:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 16:21:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 16:21:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 16:21:13 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 16:21:13 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.618 seconds (process running for 3.09)
2025-06-12 16:21:13 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 16:21:13 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 16:21:13 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-12 16:21:14 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 1836e068f522
2025-06-12 16:21:14 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【UP】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:21:22 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【UP】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:22:01 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【UP】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:22:30 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 16:22:30 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 16:22:30 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 16:22:30 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 16:25:49 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 22.0.2 with PID 3368 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:25:49 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 16:25:50 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 16:25:50 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:25:50 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 925 ms
2025-06-12 16:25:51 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 16:25:51 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 16:25:51 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 16:25:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 16:25:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 16:25:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 16:25:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 16:25:51 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 16:25:51 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.61 seconds (process running for 3.151)
2025-06-12 16:25:52 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 16:25:52 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 16:25:52 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-12 16:25:52 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 1836e068f522
2025-06-12 16:25:52 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【UP】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:26:00 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【UP】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:26:22 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【UP】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:27:01 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【DOWN】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:27:01 [reactor-http-nio-4] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=28719134fffc, version=3, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://162.168.2.49:8080/actuator, healthUrl=http://162.168.2.49:8080/actuator/health, serviceUrl=http://162.168.2.49:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-06-12T08:27:01.637787800Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.49:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.49:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.49:8080/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.49:8080/actuator/health), env=Endpoint(id=env, url=http://162.168.2.49:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.49:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.49:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.49:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://162.168.2.49:8080/actuator/startup), beans=Endpoint(id=beans, url=http://162.168.2.49:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.49:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.49:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.49:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.49:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.49:8080/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.49:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-06-12 16:27:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 16:27:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 16:27:29 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 16:27:29 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 16:27:31 [reactor-http-nio-6] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=1836e068f522, version=2, registration=Registration(name=ruoyi-monitor-admin, managementUrl=http://162.168.2.49:9090/actuator, healthUrl=http://162.168.2.49:9090/actuator/health, serviceUrl=http://162.168.2.49:9090/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, diskSpace={status=UP, details={total=755766726656, free=323837956096, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-06-12T08:25:52.509076400Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.49:9090/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.49:9090/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.49:9090/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.49:9090/actuator/health), env=Endpoint(id=env, url=http://162.168.2.49:9090/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.49:9090/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.49:9090/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.49:9090/actuator/mappings), beans=Endpoint(id=beans, url=http://162.168.2.49:9090/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.49:9090/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.49:9090/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.49:9090/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.49:9090/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.49:9090/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.49:9090/actuator/info)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1570)
2025-06-12 16:27:31 [reactor-http-nio-5] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=9e8b1a9041f3, version=3, registration=Registration(name=ruoyi-snailjob-server, managementUrl=http://162.168.2.49:8800/snail-job/actuator, healthUrl=http://162.168.2.49:8800/snail-job/actuator/health, serviceUrl=http://162.168.2.49:8800/snail-job, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=755766726656, free=323837952000, threshold=10485760, path=D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-06-12T08:26:00.477466900Z, info=Info(values={build={artifact=snail-job-server-starter, name=snail-job-server-starter, time=2025-05-17T02:09:17.034Z, version=1.5.0, group=com.aizuda}}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://162.168.2.49:8800/snail-job/actuator/caches), loggers=Endpoint(id=loggers, url=http://162.168.2.49:8800/snail-job/actuator/loggers), logfile=Endpoint(id=logfile, url=http://162.168.2.49:8800/snail-job/actuator/logfile), health=Endpoint(id=health, url=http://162.168.2.49:8800/snail-job/actuator/health), env=Endpoint(id=env, url=http://162.168.2.49:8800/snail-job/actuator/env), heapdump=Endpoint(id=heapdump, url=http://162.168.2.49:8800/snail-job/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://162.168.2.49:8800/snail-job/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://162.168.2.49:8800/snail-job/actuator/mappings), beans=Endpoint(id=beans, url=http://162.168.2.49:8800/snail-job/actuator/beans), configprops=Endpoint(id=configprops, url=http://162.168.2.49:8800/snail-job/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://162.168.2.49:8800/snail-job/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://162.168.2.49:8800/snail-job/actuator/sbom), metrics=Endpoint(id=metrics, url=http://162.168.2.49:8800/snail-job/actuator/metrics), conditions=Endpoint(id=conditions, url=http://162.168.2.49:8800/snail-job/actuator/conditions), info=Endpoint(id=info, url=http://162.168.2.49:8800/snail-job/actuator/info)}), buildVersion=1.5.0, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1742)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:325)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:304)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:662)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:574)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1570)
2025-06-12 16:27:31 [reactor-http-nio-6] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【OFFLINE】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:27:31 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【OFFLINE】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:28:34 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 28540 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:28:34 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 16:28:35 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 16:28:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:28:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 911 ms
2025-06-12 16:28:35 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 16:28:35 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 16:28:36 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 16:28:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 16:28:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 16:28:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 16:28:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 16:28:36 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 16:28:36 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.645 seconds (process running for 3.242)
2025-06-12 16:28:36 [RMI TCP Connection(2)-162.168.2.49] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 16:28:36 [RMI TCP Connection(2)-162.168.2.49] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 16:28:36 [RMI TCP Connection(2)-162.168.2.49] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-12 16:28:37 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 1836e068f522
2025-06-12 16:28:37 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【UP】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:28:47 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【UP】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:29:10 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【UP】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 16:30:12 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 16:30:12 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 16:30:12 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 16:30:12 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 16:45:25 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 30636 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:45:25 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 16:45:26 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 16:45:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:45:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1010 ms
2025-06-12 16:45:27 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 16:45:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 16:45:27 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 16:45:27 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 16:45:27 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 16:45:27 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 16:45:27 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 16:45:28 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 16:45:28 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.817 seconds (process running for 3.334)
2025-06-12 16:45:28 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 16:45:28 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 16:45:28 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-12 16:45:28 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 1836e068f522
2025-06-12 16:45:28 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【1836e068f522】, 状态【UP】, 服务URL【http://162.168.2.49:9090/】
2025-06-12 16:45:37 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【9e8b1a9041f3】, 状态【UP】, 服务URL【http://162.168.2.49:8800/snail-job】
2025-06-12 16:46:03 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【28719134fffc】, 状态【UP】, 服务URL【http://162.168.2.49:8080/】
2025-06-12 17:25:41 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 17:25:41 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 17:25:41 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 17:25:41 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 17:57:31 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 7612 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 17:57:31 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 17:57:33 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 17:57:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 17:57:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1495 ms
2025-06-12 17:57:33 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 17:57:34 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 17:57:34 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 17:57:35 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 17:57:35 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 17:57:35 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 17:57:35 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 17:57:35 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 17:57:35 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 4.353 seconds (process running for 5.015)
2025-06-12 17:57:35 [RMI TCP Connection(1)-192.168.0.113] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 17:57:35 [RMI TCP Connection(1)-192.168.0.113] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 17:57:35 [RMI TCP Connection(1)-192.168.0.113] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-12 17:57:36 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 931170c12624
2025-06-12 17:57:36 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【931170c12624】, 状态【UP】, 服务URL【http://192.168.0.113:9090/】
2025-06-12 17:57:47 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【6cd0ef70c008】, 状态【UP】, 服务URL【http://192.168.0.113:8800/snail-job】
2025-06-12 17:58:16 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【862d8b19a5fe】, 状态【UP】, 服务URL【http://192.168.0.113:8080/】
2025-06-12 17:59:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 17:59:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 17:59:44 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 17:59:44 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-12 18:26:41 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.14 with PID 27708 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-extend\ruoyi-monitor-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 18:26:41 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-06-12 18:26:42 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-12 18:26:42 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 18:26:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 963 ms
2025-06-12 18:26:42 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-12 18:26:42 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-06-12 18:26:42 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-06-12 18:26:43 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-12 18:26:43 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-12 18:26:43 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-12 18:26:43 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-12 18:26:43 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-06-12 18:26:43 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.678 seconds (process running for 3.237)
2025-06-12 18:26:43 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 18:26:43 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-12 18:26:43 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-12 18:26:44 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 931170c12624
2025-06-12 18:26:44 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【931170c12624】, 状态【UP】, 服务URL【http://192.168.0.113:9090/】
2025-06-12 18:26:54 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【6cd0ef70c008】, 状态【UP】, 服务URL【http://192.168.0.113:8800/snail-job】
2025-06-12 18:27:16 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【862d8b19a5fe】, 状态【UP】, 服务URL【http://192.168.0.113:8080/】
2025-06-12 19:47:50 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-12 19:47:50 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-12 19:47:50 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-12 19:47:50 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
