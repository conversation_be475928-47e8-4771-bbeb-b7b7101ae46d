var c=(i,r,t)=>new Promise((o,s)=>{var f=e=>{try{a(t.next(e))}catch(n){s(n)}},p=e=>{try{a(t.throw(e))}catch(n){s(n)}},a=e=>e.done?o(e.value):Promise.resolve(e.value).then(f,p);a((t=t.apply(i,r)).next())});import{b as l}from"./api-CwjVPMpk.js";import{u as m,_}from"./use-echarts-CF-NZzbo.js";import{d,p as h,v as u,h as w,o as b,b as g}from"../jse/index-index-C-MnMZEz.js";const v=d({name:"Browser",__name:"browser",setup(i){const r=h(),{renderEcharts:t}=m(r);return u(()=>c(null,null,function*(){const o=yield l();t({legend:{left:"left",orient:"vertical"},series:[{data:o,emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)",shadowOffsetX:0}},label:{formatter:"{b}: {c} - ({d}%)",show:!0},radius:"50%",type:"pie"}],title:{left:"center",text:"使用浏览器占比"},tooltip:{trigger:"item"}})})),(o,s)=>(b(),w(g(_),{ref_key:"browserRef",ref:r,height:"720px",width:"100%"},null,512))}});export{v as _};
