{"doc": " 部门业务对象 sys_dept\n\n <AUTHOR>\n", "fields": [{"name": "deptId", "doc": " 部门id\n"}, {"name": "parentId", "doc": " 父部门ID\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "deptCategory", "doc": " 部门类别编码\n"}, {"name": "orderNum", "doc": " 显示顺序\n"}, {"name": "leader", "doc": " 负责人\n"}, {"name": "phone", "doc": " 联系电话\n"}, {"name": "email", "doc": " 邮箱\n"}, {"name": "status", "doc": " 部门状态（0正常 1停用）\n"}, {"name": "belongDeptId", "doc": " 归属部门id（部门树）\n"}], "enumConstants": [], "methods": [], "constructors": []}