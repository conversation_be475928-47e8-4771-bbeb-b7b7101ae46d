2025-06-17 08:16:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-17 08:16:56 [main] INFO  o.d.w.RuoYiWorkflowApplication - Starting RuoYiWorkflowApplication using Java 17.0.14 with PID 34800 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-workflow\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:16:56 [main] INFO  o.d.w.RuoYiWorkflowApplication - The following 1 profile is active: "dev"
2025-06-17 08:16:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-17 08:16:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-17 08:16:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-17 08:17:03 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-17 08:17:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-17 08:17:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-17 08:17:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@65b869b4
2025-06-17 08:17:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-17 08:17:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-17 08:17:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-17 08:17:05 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-17 08:17:08 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-17 08:17:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-17 08:17:09 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:17:09 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:17:09 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@16da1abc
2025-06-17 08:17:09 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-06-17 08:17:12 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-17 08:17:12 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-17 08:17:12 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-17 08:17:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-17 08:17:13 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-workflow *************:9205 register finished
2025-06-17 08:17:19 [main] INFO  o.d.w.RuoYiWorkflowApplication - Started RuoYiWorkflowApplication in 26.047 seconds (process running for 27.015)
2025-06-17 08:17:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-17 08:17:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-17 08:17:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP
2025-06-17 08:17:19 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 15:13:35 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-17 15:13:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
