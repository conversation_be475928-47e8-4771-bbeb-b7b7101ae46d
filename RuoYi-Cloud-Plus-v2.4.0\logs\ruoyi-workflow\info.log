2025-06-16 00:14:02 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 00:14:02 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 00:14:02 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:14:02 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:14:03 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:14:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 00:14:03 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 00:14:03 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 00:14:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 00:14:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 08:27:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:27:42 [main] INFO  o.d.w.RuoYiWorkflowApplication - Starting RuoYiWorkflowApplication using Java 17.0.14 with PID 42500 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-workflow\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:27:42 [main] INFO  o.d.w.RuoYiWorkflowApplication - The following 1 profile is active: "dev"
2025-06-16 08:27:43 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-16 08:27:43 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:27:43 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:27:48 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:27:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 08:27:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 08:27:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2afdab87
2025-06-16 08:27:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 08:27:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 08:27:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 08:27:50 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:27:54 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:27:54 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:27:54 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:27:54 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:27:54 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@17fede14
2025-06-16 08:27:54 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-06-16 08:27:57 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:27:57 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:27:57 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:27:57 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:27:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-workflow ************:9205 register finished
2025-06-16 08:28:04 [main] INFO  o.d.w.RuoYiWorkflowApplication - Started RuoYiWorkflowApplication in 24.846 seconds (process running for 25.658)
2025-06-16 08:28:04 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:28:04 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:28:04 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP
2025-06-16 08:28:04 [RMI TCP Connection(2)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:18:30 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:18:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:18:30 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 09:18:30 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 09:18:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 09:18:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 10:55:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:55:29 [main] INFO  o.d.w.RuoYiWorkflowApplication - Starting RuoYiWorkflowApplication using Java 17.0.14 with PID 10432 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-workflow\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:55:29 [main] INFO  o.d.w.RuoYiWorkflowApplication - The following 1 profile is active: "dev"
2025-06-16 10:55:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 10:55:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 10:55:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 10:55:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@abce494
2025-06-16 10:55:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 10:55:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 10:55:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 10:55:39 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:55:43 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:55:43 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:55:43 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:55:43 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:55:43 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2b4d4327
2025-06-16 10:55:44 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-06-16 10:55:47 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:55:47 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:55:47 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:55:47 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:55:48 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-workflow ************:9205 register finished
2025-06-16 10:55:51 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:55:55 [main] INFO  o.d.w.RuoYiWorkflowApplication - Started RuoYiWorkflowApplication in 29.13 seconds (process running for 30.225)
2025-06-16 10:55:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 10:55:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:55:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 17:39:29 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 17:39:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 17:39:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 17:39:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 17:39:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 17:39:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 17:59:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 17:59:54 [main] INFO  o.d.w.RuoYiWorkflowApplication - Starting RuoYiWorkflowApplication using Java 17.0.14 with PID 38160 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-workflow\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 17:59:54 [main] INFO  o.d.w.RuoYiWorkflowApplication - The following 1 profile is active: "dev"
2025-06-16 17:59:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-16 17:59:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 17:59:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 17:59:59 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 18:00:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 18:00:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 18:00:00 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7fc2e0b7
2025-06-16 18:00:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 18:00:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 18:00:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 18:00:01 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 18:00:02 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 18:00:02 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 18:00:03 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 18:00:03 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 18:00:03 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@68c9e023
2025-06-16 18:00:03 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-06-16 18:00:05 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 18:00:05 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 18:00:05 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 18:00:05 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 18:00:05 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-workflow *************:9205 register finished
2025-06-16 18:00:10 [main] INFO  o.d.w.RuoYiWorkflowApplication - Started RuoYiWorkflowApplication in 17.663 seconds (process running for 18.412)
2025-06-16 18:00:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 18:00:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 18:00:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP
2025-06-16 18:00:10 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
