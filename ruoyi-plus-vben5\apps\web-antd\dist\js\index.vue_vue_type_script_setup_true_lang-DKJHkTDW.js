var I=Object.defineProperty;var x=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var v=(r,o,e)=>o in r?I(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,A=(r,o)=>{for(var e in o||(o={}))V.call(o,e)&&v(r,e,o[e]);if(x)for(var e of x(o))N.call(o,e)&&v(r,e,o[e]);return r};var f=(r,o,e)=>new Promise((h,m)=>{var y=a=>{try{c(e.next(a))}catch(d){m(d)}},s=a=>{try{c(e.throw(a))}catch(d){m(d)}},c=a=>a.done?h(a.value):Promise.resolve(a.value).then(y,s);c((e=e.apply(r,o)).next())});import{av as P,as as R,an as T}from"./bootstrap-DCMzVRvD.js";import{v as B}from"./vxe-table-DzEj5Fop.js";import{r as M,a as O,b as z}from"./index-Ds1FCatV.js";import{c as j,q as F,_ as G}from"./role-assign-drawer.vue_vue_type_script_setup_true_lang-_HWwlhIh.js";import{_ as L}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import W from"./index-BeyziwLP.js";import{d as E,l as $,S as H,h as g,o as C,b as n,w as i,a as _,T as k,k as w,t as J}from"../jse/index-index-C-MnMZEz.js";import{u as K}from"./use-vxe-grid-BC7vZzEr.js";import{u as Q}from"./use-drawer-6qcpK-D1.js";import{P as U}from"./index-DNdMANjv.js";import{g as X}from"./get-popup-container-P4S1sr5h.js";const me=E({__name:"index",setup(r){const e=P().params.roleId,h={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:F(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},m={checkboxConfig:{highlight:!0,reserve:!0},columns:j,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(p,...D)=>f(null,[p,...D],function*({page:l},t={}){return yield M(A({pageNum:l.currentPage,pageSize:l.pageSize,roleId:e},t))})}},rowConfig:{keyField:"userId"},id:"system-role-assign-index"},[y,s]=K({formOptions:h,gridOptions:m}),[c,a]=Q({connectedComponent:G});function d(){a.setData({}),a.open()}function S(l){return f(this,null,function*(){yield O({userId:l.userId,roleId:e}),yield s.query()})}function q(){const t=s.grid.getCheckboxRecords().map(p=>p.userId);T.confirm({title:"提示",okType:"danger",content:`确认取消选中的${t.length}条授权记录吗？`,onOk:()=>f(null,null,function*(){yield z(e,t),yield s.query(),s.grid.clearCheckboxRow()})})}return(l,t)=>{const p=$("a-button"),D=$("ghost-button"),b=H("access");return C(),g(n(L),{"auto-content-height":!0},{default:i(()=>[_(n(y),{"table-title":"已分配的用户列表"},{"toolbar-tools":i(()=>[_(n(W),null,{default:i(()=>[k((C(),g(p,{disabled:!n(B)(n(s)),danger:"",type:"primary",onClick:q},{default:i(()=>t[2]||(t[2]=[w(" 取消授权 ")])),_:1,__:[2]},8,["disabled"])),[[b,["system:role:remove"],"code"]]),k((C(),g(p,{type:"primary",onClick:d},{default:i(()=>[w(J(l.$t("pages.common.add")),1)]),_:1})),[[b,["system:role:add"],"code"]])]),_:1})]),action:i(({row:u})=>[_(n(U),{"get-popup-container":n(X),title:`是否取消授权用户[${u.userName} - ${u.nickName}]?`,placement:"left",onConfirm:Y=>S(u)},{default:i(()=>[k((C(),g(D,{danger:"",onClick:t[0]||(t[0]=R(()=>{},["stop"]))},{default:i(()=>t[3]||(t[3]=[w(" 取消授权 ")])),_:1,__:[3]})),[[b,["system:role:remove"],"code"]])]),_:2},1032,["get-popup-container","title","onConfirm"])]),_:1}),_(n(c),{onReload:t[1]||(t[1]=u=>n(s).query())})]),_:1})}}});export{me as _};
