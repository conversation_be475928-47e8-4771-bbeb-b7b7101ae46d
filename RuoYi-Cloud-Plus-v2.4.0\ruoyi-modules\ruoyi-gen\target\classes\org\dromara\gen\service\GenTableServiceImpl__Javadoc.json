{"doc": " 业务 服务层实现\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectGenTableColumnListByTableId", "paramTypes": ["java.lang.Long"], "doc": " 查询业务字段列表\n\n @param tableId 业务字段编号\n @return 业务字段集合\n"}, {"name": "selectGenTableById", "paramTypes": ["java.lang.Long"], "doc": " 查询业务信息\n\n @param id 业务ID\n @return 业务信息\n"}, {"name": "selectPageDbTableList", "paramTypes": ["org.dromara.gen.domain.GenTable", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询数据库列表\n\n @param genTable  包含查询条件的GenTable对象\n @param pageQuery 包含分页信息的PageQuery对象\n @return 包含分页结果的TableDataInfo对象\n"}, {"name": "selectDbTableListByNames", "paramTypes": ["java.lang.String[]", "java.lang.String"], "doc": " 查询据库列表\n\n @param tableNames 表名称组\n @param dataName   数据源名称\n @return 数据库表集合\n"}, {"name": "selectGenTableAll", "paramTypes": [], "doc": " 查询所有表信息\n\n @return 表信息集合\n"}, {"name": "updateGenTable", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 修改业务\n\n @param genTable 业务信息\n"}, {"name": "deleteGenTableByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 删除业务对象\n\n @param tableIds 需要删除的数据ID\n"}, {"name": "importGenTable", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 导入表结构\n\n @param tableList 导入表列表\n @param dataName  数据源名称\n"}, {"name": "selectDbTableColumnsByName", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据表名称查询列信息\n\n @param tableName 表名称\n @param dataName  数据源名称\n @return 列信息\n"}, {"name": "previewCode", "paramTypes": ["java.lang.Long"], "doc": " 预览代码\n\n @param tableId 表编号\n @return 预览数据列表\n"}, {"name": "downloadCode", "paramTypes": ["java.lang.Long"], "doc": " 生成代码（下载方式）\n\n @param tableId 表名称\n @return 数据\n"}, {"name": "generatorCode", "paramTypes": ["java.lang.Long"], "doc": " 生成代码（自定义路径）\n\n @param tableId 表名称\n"}, {"name": "synchDb", "paramTypes": ["java.lang.Long"], "doc": " 同步数据库\n\n @param tableId 表名称\n"}, {"name": "downloadCode", "paramTypes": ["java.lang.String[]"], "doc": " 批量生成代码（下载方式）\n\n @param tableIds 表ID数组\n @return 数据\n"}, {"name": "generatorCode", "paramTypes": ["java.lang.Long", "java.util.zip.ZipOutputStream"], "doc": " 查询表信息并生成代码\n"}, {"name": "validateEdit", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 修改保存参数校验\n\n @param genTable 业务信息\n"}, {"name": "setPkColumn", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 设置主键列信息\n\n @param table 业务表信息\n"}, {"name": "setTableFromOptions", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 设置代码生成其他选项值\n\n @param genTable 设置后的生成对象\n"}, {"name": "getGenPath", "paramTypes": ["org.dromara.gen.domain.GenTable", "java.lang.String"], "doc": " 获取代码生成地址\n\n @param table    业务表信息\n @param template 模板文件路径\n @return 生成地址\n"}], "constructors": []}