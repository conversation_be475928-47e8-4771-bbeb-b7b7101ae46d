2025-06-17 08:14:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-17 08:14:35 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 11532 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:14:35 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-17 08:14:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-17 08:14:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-17 08:14:41 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-17 08:14:43 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-17 08:14:48 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-17 08:14:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-17 08:14:49 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:14:49 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:14:50 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-17 08:14:50 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-17 08:14:50 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-17 08:14:50 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-17 08:14:50 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth *************:9210 register finished
2025-06-17 08:14:54 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 23.099 seconds (process running for 24.036)
2025-06-17 08:14:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-17 08:14:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-17 08:14:55 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Received instance notification, serviceName: ruoyi-system, instances: 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] 1 unique working revisions: 7092d8208bd8d2ca5be86b5c320c6886 , dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.system.api.RemoteClientService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.q.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Successfully connect to server /*************:20880 from NettyClient ************* using dubbo version 3.3.4, channel is NettyChannel [channel=[id: 0xa70e9b33, L:/*************:63736 - R:/*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Start NettyClient /************* connect to the server /*************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xa70e9b33, L:/*************:63736 - R:/*************:20880] of *************:63736 -> *************:20880 is established., dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.system.api.RemoteClientService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.system.api.RemoteSocialService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.system.api.RemoteSocialService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.system.api.RemoteConfigService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:42 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.system.api.RemoteConfigService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.system.api.RemoteLogService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.system.api.RemoteLogService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.system.api.RemoteTenantService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.system.api.RemoteTenantService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.system.api.RemoteUserService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.system.api.RemoteUserService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20880, dubbo version: 3.3.4, current host: *************
2025-06-17 08:15:43 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:16:03 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:16:23 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Received instance notification, serviceName: ruoyi-resource, instances: 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] 1 unique working revisions: e7814e6e714c64a8a6294283f939d88f , dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service org.dromara.resource.api.RemoteMessageService:null with urls 1, dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [NettyClientWorker-5-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0x15a27568, L:/*************:63878 - R:/*************:20881] of *************:63878 -> *************:20881 is established., dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Successfully connect to server /*************:20881 from NettyClient ************* using dubbo version 3.3.4, channel is NettyChannel [channel=[id: 0x15a27568, L:/*************:63878 - R:/*************:20881]], dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Start NettyClient /************* connect to the server /*************:20881, dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-resource])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:39 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.resource.api.RemoteMessageService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.3.4, current host: *************
2025-06-17 08:16:43 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:17:03 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:17:23 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:17:43 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:03 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:23 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:43 [XNIO-1 task-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:50 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-17 08:18:53 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 08:18:53 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 08:18:53 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[406ms]
2025-06-17 08:19:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[44ms]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[11ms]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[93ms]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[10ms]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[23ms]
2025-06-17 08:19:08 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJpTVpXWGNtY3JLVFhtMmdINmZST2dzSGFBQzBYUlNPRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Jaf9kcF-ntD4hP1H7Us_-GxjWPr1caaBb3uqYMs6rk0
2025-06-17 08:19:11 [schedule-pool-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistry -  [DUBBO] Trying to subscribe from apps ruoyi-resource for service key org.dromara.resource.api.RemoteMessageService, , dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [schedule-pool-1] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify serviceKey: org.dromara.resource.api.RemoteMessageService:null, listener: ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-resource])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [schedule-pool-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: 127.0.0.1:8848, subscribed key: [ruoyi-resource])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [schedule-pool-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.resource.api.RemoteMessageService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [schedule-pool-1] INFO  o.a.d.r.c.m.MigrationRuleHandler -  [DUBBO] Succeed Migrated to FORCE_APPLICATION mode. Service Name: org.dromara.resource.api.RemoteMessageService, dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [schedule-pool-1] INFO  o.a.dubbo.config.ReferenceConfig -  [DUBBO] Referred dubbo service: [org.dromara.resource.api.RemoteMessageService]. it's not GenericService reference, dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [Dubbo-framework-mapping-refreshing-scheduler-thread-8] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener -  [DUBBO] Received mapping notification from meta server, {serviceKey: org.dromara.resource.api.RemoteMessageService, apps: [ruoyi-resource]}, dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:11 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='ruoyi-auth', serviceInterface='org.dromara.resource.api.RemoteMessageService', version='', group='', side='consumer'}; definition: {dubbo=2.0.2, release=3.3.4, application=ruoyi-auth, metadata-type=remote, interface=org.dromara.resource.api.RemoteMessageService, side=consumer, pid=11532, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=*************, methods=publishAll,publishMessage, logger=slf4j, check=false, qos.enable=false, timeout=3000, register-mode=instance, unloadClusterRelated=false, retries=0, background=false, stub=true, sticky=false, validation=jvalidationNew, timestamp=1750119551603}, dubbo version: 3.3.4, current host: *************
2025-06-17 08:19:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:19:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:20:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:20:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:20:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:21:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:21:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:21:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:22:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:22:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:22:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:23:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[8ms]
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Logout][退出成功]
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJpTVpXWGNtY3JLVFhtMmdINmZST2dzSGFBQzBYUlNPRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Jaf9kcF-ntD4hP1H7Us_-GxjWPr1caaBb3uqYMs6rk0
2025-06-17 08:23:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:23:24 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 08:23:24 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 08:23:24 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[6ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[22ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[9ms]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJJZDlTaTMydllWTWdSdm53RjQ2eEFhZUtscTBsaDVteSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ZEPjInNRj2zSqMqALz97rnlv07qrIuxD36psOkWpYjE
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 08:23:30 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[16ms]
2025-06-17 08:23:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:24:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:24:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:24:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:25:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:25:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:25:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:26:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:26:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:26:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:27:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:27:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:27:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:28:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:28:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:28:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:29:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:29:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:29:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:30:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:30:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:30:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:31:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:31:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:31:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:32:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:32:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:32:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:33:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:33:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:33:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:34:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:34:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:34:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:35:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:35:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:35:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:36:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:36:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:36:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:37:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:37:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:37:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:38:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:38:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:38:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:39:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:39:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:39:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:40:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:40:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:40:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:41:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:41:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:41:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:42:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:42:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:42:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:43:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:43:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:43:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:44:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:44:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:44:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:45:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:45:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:45:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:46:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:46:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:46:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:47:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:47:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:47:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:48:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:48:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:48:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:49:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:49:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:49:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:50:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:50:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:50:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:51:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:51:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:51:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:52:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:52:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:52:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:53:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:53:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:53:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:54:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:54:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:54:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:55:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:55:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:55:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:56:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:56:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:56:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:57:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:57:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:57:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:58:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:58:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:58:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:59:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:59:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:59:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:00:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:00:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:00:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:01:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:01:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:01:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:02:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:02:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:02:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:03:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:03:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:03:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:04:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:04:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:04:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:05:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:05:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:05:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:06:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:06:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:06:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:07:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:07:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:07:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:08:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:08:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:08:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:09:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:09:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:09:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:10:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:10:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:10:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:11:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:11:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:11:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:12:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:12:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:12:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:13:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:13:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:13:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:13:28 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 09:13:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[6ms]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[19ms]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 09:13:35 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[8ms]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJzS1JqcDh4eGhtaDcwUDlha1BwQTBwSXJVQ05GTDdZTCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.k9gpYil4dJ0pzl35cgXYmZ755jEbvWO3BssxQmygC5s
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:13:36 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-17 09:13:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:14:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:14:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:14:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:15:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:15:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:15:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:16:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:16:21 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:16:21 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-17 09:16:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:16:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:17:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:17:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 09:17:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-17 09:17:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:17:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:18:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:18:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:18:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:19:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:19:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:19:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:20:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:20:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:20:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:21:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:21:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:21:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:22:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:22:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:22:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:23:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:23:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:23:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:24:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:24:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:24:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:25:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:25:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:25:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:26:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:26:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:26:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:27:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:27:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:27:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:28:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:28:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:28:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:29:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:29:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:29:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:30:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:30:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:30:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:31:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:31:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:31:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:32:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:32:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:32:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:33:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:33:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:33:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:34:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:34:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:34:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:35:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:35:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:35:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:36:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:36:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:36:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:37:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:37:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:37:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:38:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:38:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:38:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:39:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:39:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:39:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:40:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:40:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:40:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:41:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:41:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:41:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:42:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:42:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:42:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:43:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:43:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:43:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:44:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:44:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:44:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:45:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:45:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:45:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:46:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:46:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:46:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:47:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:47:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:47:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:48:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:48:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:48:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:49:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:49:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:49:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:50:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:50:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:50:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:51:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:51:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:51:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:52:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:52:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:52:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:53:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:53:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:53:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:54:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:54:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:54:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:55:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:55:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:55:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:56:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:56:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:56:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:57:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:57:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:57:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:58:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:58:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:58:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:59:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:59:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:59:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:00:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:00:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:00:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:01:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:01:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:01:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:02:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:02:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:02:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:03:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:03:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:03:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:04:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:04:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:04:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:05:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:05:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:05:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:06:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:06:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:06:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:07:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:07:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:07:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:08:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:08:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:08:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:09:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:09:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:09:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:10:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:10:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:10:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:11:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:11:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:11:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:12:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:12:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:12:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:13:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:13:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:13:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:14:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:14:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:14:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:15:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:15:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:15:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:16:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:16:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:16:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:17:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:17:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:17:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:18:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:18:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:18:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:19:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:19:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:19:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:20:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:20:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:20:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:21:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:21:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:21:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:22:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:22:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:22:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:23:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:23:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:23:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:24:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:24:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:24:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:25:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:25:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:25:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:26:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:26:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:26:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:27:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:27:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:27:43 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:03 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:35 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 10:28:35 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 10:28:35 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-17 10:28:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[18ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMR1FiMWNaN2VvamxtWmlQSm5XWmJPOHNOelF5TkVYSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.xnRhhJB1BJQ_lAdWKQLQsBR70Vxh91mo48NSLeG0re8
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 10:28:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 10:29:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:29:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:29:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:30:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:30:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:30:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:31:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:31:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:31:36 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 10:31:36 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-17 10:31:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:32:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:32:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:32:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:33:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:33:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:33:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:34:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:34:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:34:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:35:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:35:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:35:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:36:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:36:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:36:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:37:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:37:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:37:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:38:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:38:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:38:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:39:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:39:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:39:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:40:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:40:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:40:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:41:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:41:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:41:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:42:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:42:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:42:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:43:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:43:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:43:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:44:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:44:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:44:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:45:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:45:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:45:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:46:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:46:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:46:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:47:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:47:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:47:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:48:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:48:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:48:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:49:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:49:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:49:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:50:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:50:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:50:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:51:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:51:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:51:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:52:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:52:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:52:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:53:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:53:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:53:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:54:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:54:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:54:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:55:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:55:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:55:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:56:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:56:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:56:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:57:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:57:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:57:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:58:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:58:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:58:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:59:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:59:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:59:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:00:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:00:20 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 11:00:20 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-17 11:00:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:00:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:01:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:01:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:01:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:02:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:02:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:02:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:03:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:03:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:03:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:04:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:04:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:04:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:05:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:05:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:05:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:06:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:06:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:06:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:07:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:07:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:07:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:08:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:08:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:08:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:09:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:09:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:09:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:10:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:10:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:10:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:11:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:11:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:11:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:12:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:12:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:12:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:13:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:13:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:13:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:14:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:14:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:14:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:15:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:15:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:15:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:16:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:16:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:16:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:17:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:17:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:17:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:18:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:18:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:18:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:19:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:19:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:19:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:20:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:20:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:20:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:21:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:21:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:21:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:22:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:22:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:22:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:23:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:23:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:23:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:24:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:24:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:24:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:25:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:25:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:25:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:26:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:26:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:26:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:27:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:27:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:27:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:28:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:28:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:28:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:29:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:29:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:29:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:30:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:30:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:30:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:31:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:31:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:31:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:32:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:32:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:32:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:33:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:33:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:33:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:34:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:34:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:34:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:35:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:35:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:35:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:36:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:36:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:36:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:37:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:37:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:37:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:38:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:38:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:38:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:39:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:39:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:39:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:40:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:40:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:40:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:41:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:41:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:41:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:42:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:42:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:42:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:43:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:43:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:43:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:44:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:44:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:44:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:45:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:45:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:45:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:46:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:46:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:46:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:47:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:47:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:47:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:48:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:48:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:48:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:49:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:49:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:49:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:50:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:50:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:50:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:51:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:51:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:51:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:52:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:52:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:52:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:53:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:53:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:53:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:54:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:54:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:54:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:55:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:55:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:55:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:56:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:56:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:56:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:57:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:57:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:57:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:58:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:58:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:58:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:59:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:59:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:59:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:00:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:00:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:00:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:01:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:01:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:01:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:02:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:02:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:02:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:03:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:03:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:03:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:04:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:04:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:04:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:05:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:05:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:05:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:06:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:06:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:06:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:07:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:07:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:07:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:08:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:08:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:08:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:09:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:09:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:09:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:10:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:10:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:10:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:11:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:11:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:11:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:12:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:12:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:12:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:13:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:13:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:13:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:14:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:14:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:14:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:15:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:15:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:15:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:16:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:16:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:16:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:17:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:17:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:17:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:18:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:18:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:18:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:19:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:19:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:19:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:20:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:20:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:20:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:21:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:21:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:21:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:22:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:22:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:22:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:23:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:23:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:23:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:24:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:24:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:24:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:25:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:25:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:25:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:26:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:26:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:26:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:27:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:27:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:27:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:28:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:28:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:28:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:29:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:29:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:29:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:30:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:30:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:30:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:31:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:31:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:31:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:32:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:32:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:32:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:33:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:33:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:33:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:34:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:34:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:34:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:35:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:35:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:35:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:36:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:36:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:36:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:37:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:37:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:37:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:38:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:38:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:38:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:39:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:39:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:39:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:40:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:40:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:40:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:41:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:41:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:41:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:42:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:42:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:42:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:43:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:43:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:43:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:44:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:44:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:44:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:45:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:45:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:45:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:46:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:46:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:46:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:47:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:47:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:47:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:48:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:48:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:48:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:49:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:49:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:49:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:50:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:50:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:50:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:51:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:51:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:51:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:52:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:52:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:52:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:53:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:53:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:53:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:54:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:54:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:54:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:55:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:55:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:55:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:56:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:56:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:56:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:57:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:57:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:57:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:58:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:58:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:58:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:59:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:59:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:59:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:00:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:00:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:00:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:01:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:01:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:01:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:02:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:02:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:02:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:03:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:03:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:03:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:04:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:04:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:04:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:05:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:05:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:05:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:06:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:06:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:06:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:07:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:07:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:07:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:08:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:08:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:08:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:09:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:09:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:09:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:10:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:10:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:10:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:11:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:11:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:11:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:12:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:12:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:12:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:13:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:13:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:13:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:14:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:14:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:14:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:15:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:15:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:15:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:16:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:16:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:16:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:17:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:17:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:17:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:18:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:18:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:18:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:19:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:19:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:19:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:20:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:20:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:20:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:21:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:21:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:21:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:22:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:22:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:22:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:23:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:23:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:23:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:24:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:24:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:24:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:25:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:25:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:25:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:26:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:26:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:26:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:27:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:27:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:27:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:28:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:28:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:28:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:29:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:29:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:29:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:30:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:30:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:30:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:31:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:31:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:31:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:32:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:32:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:32:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:33:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:33:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:33:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:34:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:34:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:34:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:35:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:35:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:35:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:36:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:36:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:36:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:37:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:37:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:37:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:38:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:38:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:38:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:39:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:39:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:39:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:40:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:40:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:40:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:41:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:41:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:41:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:42:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:42:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:42:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:43:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:43:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:43:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:44:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:44:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:44:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:45:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:45:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:45:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:46:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:46:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:46:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:47:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:47:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:47:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:48:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:48:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:48:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:49:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:49:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:49:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:50:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:50:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:50:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:51:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:51:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:51:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:52:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:52:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:52:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:53:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:53:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:53:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:54:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:54:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:54:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:55:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:55:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:55:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:56:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:56:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:56:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:57:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:57:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:57:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:58:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:58:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:58:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:59:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:59:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:59:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:00:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:00:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:00:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:01:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:01:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:01:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:02:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:02:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:02:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:03:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:03:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:03:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:04:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:04:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:04:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:05:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:05:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:05:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:06:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:06:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:06:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:07:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:07:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:07:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:08:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:08:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:08:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:09:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:09:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:09:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:10:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:10:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:10:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:11:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:11:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:11:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:12:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:12:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:12:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:13:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:13:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:13:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:14:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:14:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:14:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:15:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:15:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:15:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:16:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:16:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:16:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:17:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:17:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:17:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:18:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:18:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:18:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:19:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:19:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:19:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:20:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:20:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:20:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:21:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:21:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:21:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:22:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:22:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:22:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:23:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:23:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:23:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:24:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:24:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:24:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:25:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:25:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:25:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:26:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:26:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:26:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:27:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:27:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:27:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:28:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:28:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:28:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:29:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:29:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:29:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:30:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:30:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:30:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:31:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:31:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:31:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:32:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:32:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:32:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:33:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:33:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:33:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:34:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:34:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:34:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:35:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:35:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:35:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:36:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:36:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:36:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:37:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:37:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:37:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:38:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:38:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:38:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:39:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:39:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:39:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:40:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:40:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:40:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:41:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:41:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:41:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:42:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:42:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:42:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:43:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:43:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:43:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:44:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:44:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:44:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:45:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:45:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:45:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:46:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:46:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:46:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:47:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:47:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:47:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:48:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:48:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:48:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:49:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:49:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:49:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:50:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:50:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:50:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:51:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:51:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:51:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:52:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:52:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:52:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:53:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:53:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:53:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:54:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:54:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:54:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:55:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:55:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:55:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:56:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:56:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:56:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:57:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:57:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:57:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:58:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:58:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:58:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:59:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:59:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:59:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:00:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:00:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:00:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:16 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 15:01:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:01:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-17 15:01:23 [XNIO-1 task-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[18ms]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[7ms]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0QTVkemg1OTl5cHZVVzE0NGFUSGZUQzJMN1lCMEtUciIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.F2FIJpTSeU7N1lzzVu6NQNZc-ysYQWxXpHjXtuaDTBE
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:01:25 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0QTVkemg1OTl5cHZVVzE0NGFUSGZUQzJMN1lCMEtUciIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.F2FIJpTSeU7N1lzzVu6NQNZc-ysYQWxXpHjXtuaDTBE
2025-06-17 15:01:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:01:38 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 15:01:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-17 15:01:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[4ms]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[26ms]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-17 15:01:44 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw
2025-06-17 15:02:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:02:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:02:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:03:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:03:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:03:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:04:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:04:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:04:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:06:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:06:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:06:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:07:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:07:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:07:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:08:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:08:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:08:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:09:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:09:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:09:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:10:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:10:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:10:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:11:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:11:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:11:43 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:12:03 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Logout][退出成功]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw
2025-06-17 15:12:10 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:10 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-17 15:12:10 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[16ms]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[7ms]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSMzRuWmV2YmRoV09rMWhMRUhjWXNaRmljVlNRSUlOTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.q3qtNxC-J3mKgIlKMv0r5dqvpaLSpiXULGpbE6_MDVw
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:16 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 15:12:19 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:19 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-17 15:12:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-17 15:12:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[12ms]
2025-06-17 15:12:23 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:12:33 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xa70e9b33, L:/*************:63736 - R:/*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [DubboClientHandler-thread-3] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x15a27568, L:/*************:63878 - R:/*************:20881]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x15a27568, L:/*************:63878 - R:/*************:20881]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-17 15:12:33 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) has completed., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:33 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](ruoyi-auth), dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:43 [NettyClientWorker-5-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0x15a27568, L:/*************:63878 ! R:/*************:20881] of *************:63878 -> *************:20881 is disconnected., dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:53 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xa70e9b33, L:/*************:63736 - R:/*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:12:53 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xa70e9b33, L:/*************:63736 ! R:/*************:20880] of *************:63736 -> *************:20880 is disconnected., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:28 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Initial connection to HeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/*************:63878 -> /*************:20881]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:28 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x15a27568, L:/*************:63878 ! R:/*************:20881], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:33 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Initial connection to HeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/*************:63736 -> /*************:20880]], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:33 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xa70e9b33, L:/*************:63736 ! R:/*************:20880], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:44 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xa70e9b33, L:/*************:63736 ! R:/*************:20880], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x15a27568, L:/*************:63878 ! R:/*************:20881], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) has completed., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](ruoyi-auth) to null, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=11532&qos.enable=false&register-mode=instance&release=3.3.4, nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=11532&qos.enable=false&register-mode=instance&release=3.3.4], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.registry.nacos.NacosRegistry -  [DUBBO] Destroy registry:nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=11532&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-system], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-resource], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.s.c.DubboSpringInitializer -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@322204dc, dubbo version: 3.3.4, current host: *************
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-17 15:13:54 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
