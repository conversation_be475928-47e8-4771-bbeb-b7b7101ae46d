2025-06-16 00:05:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:08 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-16 00:05:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodN<PERSON>=[queryList],SpendTime=[9ms]
2025-06-16 00:05:33 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:33 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE
2025-06-16 00:05:55 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:05:55 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:05:55 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 00:09:28 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:09:28 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:09:28 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-16 00:10:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:10:12 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:10:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-16 00:10:17 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:10:17 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:10:17 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 00:10:20 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:10:20 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:10:20 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:11:11 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:11 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:11 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:11:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:16 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:11:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:19 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:19 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 00:11:21 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:21 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:21 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-16 00:11:24 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:24 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 5, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:24 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:11:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:28 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 4, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Error][验证码错误]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 00:11:40 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 3, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[19ms]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[8ms]
2025-06-16 00:11:44 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJZREFsSUJtY3BFV0ExUkhRTEZxdWlJME5GcVZVMVFNYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fRVVxbxLZN4d6_rmTO3yU4bXOXAhSXVLX1YHO68ML0s
2025-06-16 00:11:45 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 00:11:45 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-16 00:14:07 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 00:14:07 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 00:14:07 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:14:07 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:14:07 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 08:24:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:24:19 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 32740 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:24:19 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-16 08:24:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-16 08:24:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:24:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:24:25 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:24:28 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:24:28 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:24:29 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:24:29 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:24:29 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:24:29 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:24:29 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:24:29 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:24:30 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9210 register finished
2025-06-16 08:24:33 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 17.287 seconds (process running for 18.084)
2025-06-16 08:24:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:24:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-16 08:24:34 [RMI TCP Connection(5)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:00:56 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:00:56 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 09:00:57 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:00:57 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[394ms]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[50ms]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[93ms]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[12ms]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[36ms]
2025-06-16 09:01:03 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw
2025-06-16 09:01:05 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:01:05 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[21ms]
2025-06-16 09:07:23 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:07:23 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[24ms]
2025-06-16 09:12:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:12:26 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-16 09:12:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:12:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:15:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-16 09:15:27 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:15:27 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 09:15:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:15:44 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 09:15:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:10 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:16:10 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-16 09:16:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:16:40 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) has completed., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](ruoyi-auth), dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-system], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xc9b3a5b8, L:/************:58637 - R:/************:20881]], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xffae99ef, L:/************:58530 - R:/************:20880]], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xffae99ef, L:/************:58530 - R:/************:20880], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xffae99ef, L:/************:58530 ! R:/************:20880] of ************:58530 -> ************:20880 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xc9b3a5b8, L:/************:58637 - R:/************:20881], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [NettyClientWorker-5-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xc9b3a5b8, L:/************:58637 ! R:/************:20881] of ************:58637 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) has completed., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](ruoyi-auth) to null, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=32740&qos.enable=false&register-mode=instance&release=3.3.4, nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=32740&qos.enable=false&register-mode=instance&release=3.3.4], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.registry.nacos.NacosRegistry -  [DUBBO] Destroy registry:nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=32740&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-resource], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-auth) has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.a.d.c.s.c.DubboSpringInitializer -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4d93f75b, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:16:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:16:55 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:16:55 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:32:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:32:33 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 19764 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:32:33 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-16 09:32:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:32:40 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:32:44 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:32:44 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:32:44 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:44 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 09:32:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 09:32:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 09:32:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 09:32:45 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9210 register finished
2025-06-16 09:32:49 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 19.636 seconds (process running for 20.356)
2025-06-16 09:32:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:32:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-16 09:32:50 [RMI TCP Connection(10)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:33:15 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:33:15 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:33:15 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[131ms]
2025-06-16 09:33:28 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:33:28 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[18ms]
2025-06-16 09:35:27 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:35:27 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-16 09:35:57 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:35:57 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[14ms]
2025-06-16 09:37:50 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:37:50 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:37:50 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:37:50 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:37:50 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:42:37 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:42:37 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 38312 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:42:37 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-16 09:42:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-16 09:42:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:42:45 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:42:47 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:42:51 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:42:51 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:42:51 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:42:51 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:42:52 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 09:42:52 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 09:42:52 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 09:42:52 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 09:42:52 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9210 register finished
2025-06-16 09:42:57 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 23.452 seconds (process running for 24.662)
2025-06-16 09:42:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:42:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-16 09:42:57 [RMI TCP Connection(5)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:43:59 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:43:59 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:43:59 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[146ms]
2025-06-16 09:44:37 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 09:44:37 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:46:39 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:46:39 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:59:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:59:33 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 6192 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:59:33 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-16 09:59:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 09:59:40 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:59:44 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:59:44 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:59:45 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:59:45 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:59:46 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 09:59:46 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 09:59:46 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 09:59:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 09:59:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9210 register finished
2025-06-16 09:59:50 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 20.244 seconds (process running for 21.095)
2025-06-16 09:59:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:59:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-16 09:59:50 [RMI TCP Connection(5)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:00:19 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 10:00:19 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:00:19 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[130ms]
2025-06-16 10:31:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:31:18 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 43788 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:31:18 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-16 10:31:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-16 10:31:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:31:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 10:31:27 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:31:32 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:31:32 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:31:32 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:31:32 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:31:34 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:31:34 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:31:34 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:31:34 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:31:34 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9210 register finished
2025-06-16 10:31:38 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 24.914 seconds (process running for 26.289)
2025-06-16 10:31:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:31:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-16 10:31:38 [RMI TCP Connection(3)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:32:20 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 10:32:20 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:32:20 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[177ms]
2025-06-16 10:33:12 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:33:12 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[17ms]
2025-06-16 10:33:43 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:33:43 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-16 10:50:54 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:50:54 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-16 10:51:01 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:51:01 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[16ms]
2025-06-16 10:51:21 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:51:21 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[22ms]
2025-06-16 10:51:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:51:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-16 10:51:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:51:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[24ms]
2025-06-16 10:53:19 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:53:19 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 10:53:19 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 10:53:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[14ms]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[18ms]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[72ms]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[7ms]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[15ms]
2025-06-16 10:53:27 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJIMXdCMk1qY0Rad3Z0RTVpSWdmWG9UYjJkamcxa0RCbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.QEDX6P3ZmavmyfLLw505StntCuhBP5GNTosFiJ0iSl0
2025-06-16 10:56:17 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:56:17 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 10:56:17 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[29ms]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[16ms]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[7ms]
2025-06-16 10:56:23 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k
2025-06-16 10:56:24 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:56:24 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-16 10:56:31 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:56:31 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[12ms]
2025-06-16 10:56:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:56:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 10:56:34 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 10:56:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 10:56:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 10:56:34 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k
2025-06-16 10:56:42 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:56:42 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 10:56:42 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[12ms]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJLMmROdjU3SVBLMWhLTVNqRGpVZlJ1VHB0MkZDMm5jeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.FrOSIa_uqLao6oRnfa01UtXVer21vz2YzrY3W0K42vA
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:56:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-16 10:56:53 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 10:56:53 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 10:56:53 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 10:56:53 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 10:56:53 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 10:56:53 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJLMmROdjU3SVBLMWhLTVNqRGpVZlJ1VHB0MkZDMm5jeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.FrOSIa_uqLao6oRnfa01UtXVer21vz2YzrY3W0K42vA
2025-06-16 10:59:58 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 10:59:58 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 10:59:58 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[35ms]
2025-06-16 11:00:09 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:00:09 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:00:09 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[16ms]
2025-06-16 11:00:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:00:28 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:00:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-16 11:00:41 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:00:41 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:00:41 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[24ms]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[9ms]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[34ms]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[20ms]
2025-06-16 11:01:44 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJaNWlzbVFCRG1kbDN6VzlldXl3NlJoNmk0OWphVFd1ZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.KnEzZGGItABSQ6pwhmRVJkI0ZtoUwVDFm1Ekxb_QSGA
2025-06-16 11:01:45 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:01:45 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[68ms]
2025-06-16 11:01:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:01:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:01:50 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 11:01:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:01:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:01:50 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJaNWlzbVFCRG1kbDN6VzlldXl3NlJoNmk0OWphVFd1ZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.KnEzZGGItABSQ6pwhmRVJkI0ZtoUwVDFm1Ekxb_QSGA
2025-06-16 11:01:51 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:01:51 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:01:51 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[5ms]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[11ms]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJEN0k2NUpYNVN0akpyNzg2bjRSbVhrNEtuTmJvN2FYWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fY8I3zD7ZSMqkjd3cPHwIlJluCvrD2uyOqafvQFb4VU
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:01:56 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-16 11:02:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:02:03 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJEN0k2NUpYNVN0akpyNzg2bjRSbVhrNEtuTmJvN2FYWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fY8I3zD7ZSMqkjd3cPHwIlJluCvrD2uyOqafvQFb4VU
2025-06-16 11:02:06 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:02:06 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:02:06 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[11ms]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[8ms]
2025-06-16 11:02:11 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1ZXdPVVA2ZHA3S1Rka3BwcTNoVVp6bnp3YTF2dWdzNCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.WJhzOrTLjlKQjCdCxYnR1XZIutscMzxjCb1H5kw8T2w
2025-06-16 11:02:12 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:02:12 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1ZXdPVVA2ZHA3S1Rka3BwcTNoVVp6bnp3YTF2dWdzNCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.WJhzOrTLjlKQjCdCxYnR1XZIutscMzxjCb1H5kw8T2w
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:02:16 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:02:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[4ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[18ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-16 11:02:22 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJaUnJBaXZWc3FKRE10M3ZGUFBRTlZneEdiWDVnM0wwZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.4eJa8MChZxLREdFDtD2z2LjdztbylDk--zdQSyda0ZU
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Logout][退出成功]
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJaUnJBaXZWc3FKRE10M3ZGUFBRTlZneEdiWDVnM0wwZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.4eJa8MChZxLREdFDtD2z2LjdztbylDk--zdQSyda0ZU
2025-06-16 11:02:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:02:58 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:02:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[14ms]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-16 11:03:04 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwaXdrZ1daTkxkSzBZV0RTVjVydDY5M1MyeWZEUkZVTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.rRdbCzLRVV2NdJXb2LZQ6KVivFmlFKPCZB3zkxpl8Wg
2025-06-16 11:03:05 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:03:05 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 11:04:13 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:04:13 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 11:04:31 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:04:31 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 11:05:03 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:05:03 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-16 11:05:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:05:16 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwaXdrZ1daTkxkSzBZV0RTVjVydDY5M1MyeWZEUkZVTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.rRdbCzLRVV2NdJXb2LZQ6KVivFmlFKPCZB3zkxpl8Wg
2025-06-16 11:05:34 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:05:34 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:05:34 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[7ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[16ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[9ms]
2025-06-16 11:05:41 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJockdPUzdXN25mQ2dscFBBRXZJU3F4emVPYnhhRDdldCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Yfv6l93wKcb6aFUMVjbF5jmZYE6PbWRugh7glfF8M1A
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Logout][退出成功]
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJockdPUzdXN25mQ2dscFBBRXZJU3F4emVPYnhhRDdldCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Yfv6l93wKcb6aFUMVjbF5jmZYE6PbWRugh7glfF8M1A
2025-06-16 11:06:14 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 11:06:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:06:14 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[11ms]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[6ms]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJNaURnZ2p4THRvU0l0dHlLbDljUjhxVXhiVTl4ZzNuUiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.YcKABU6vJWa-6TS30BQ57RISuMxg_j0Q8-DGskyWz2M
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:06:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-16 11:07:09 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 11:07:09 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-16 13:45:34 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:45:34 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 13:45:34 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[197ms]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[7ms]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[47ms]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[12ms]
2025-06-16 13:45:40 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIyVWp0bXpXM0RXYjNudTVISE5wS0U0Z0o0dUVEZXRWRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.Ck8zNQeZYPConAToCBaDKiGa89tpUKskwYfJ7zevLy8
2025-06-16 13:45:42 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:45:42 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[20ms]
2025-06-16 13:48:37 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:48:37 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[30ms]
2025-06-16 13:49:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:49:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[70ms]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[6ms]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIyVWp0bXpXM0RXYjNudTVISE5wS0U0Z0o0dUVEZXRWRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.Ck8zNQeZYPConAToCBaDKiGa89tpUKskwYfJ7zevLy8
2025-06-16 13:49:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:49:22 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 13:49:22 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[16ms]
2025-06-16 13:49:30 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:49:30 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 13:49:30 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 13:49:30 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[4ms]
2025-06-16 13:49:30 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 13:49:30 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[289ms]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[14ms]
2025-06-16 13:49:31 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ2WjdpSzhZbUlnMFZKU1JyeTNRTUJUblRQTUpYZjJOZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Pjpg_9vvJwnUUfms2VIkBdz9JSMHrpYASEaSUva36eU
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[6ms]
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Logout][退出成功]
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ2WjdpSzhZbUlnMFZKU1JyeTNRTUJUblRQTUpYZjJOZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Pjpg_9vvJwnUUfms2VIkBdz9JSMHrpYASEaSUva36eU
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:50:05 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 13:50:05 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[17ms]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[249ms]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[155ms]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[10ms]
2025-06-16 13:50:12 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJrWkdJblVMeWtISEdmeGdJQ3pveDhDd0N4dDFXTGF6NyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.2Crlul0C3XfCoUpsWLFMCdaQiRsugegXYtrtxQYAryU
2025-06-16 13:50:13 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 13:50:13 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[25ms]
2025-06-16 16:23:57 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 16:23:57 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 16:23:57 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[43ms]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[5ms]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Error][验证码错误]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 16:24:04 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-06-16 16:24:05 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[3ms]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[41ms]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[11ms]
2025-06-16 16:24:09 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJCZ2xFbjNSdVB6MU1ZSlNTOGtMZk9ORkNEak5STnlGUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Hm_zCe5Unb5CrH-9gqkNcDr6LhSdmqAOsj1uOMDyySI
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[6ms]
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Logout][退出成功]
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJCZ2xFbjNSdVB6MU1ZSlNTOGtMZk9ORkNEak5STnlGUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Hm_zCe5Unb5CrH-9gqkNcDr6LhSdmqAOsj1uOMDyySI
2025-06-16 16:52:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-16 16:52:27 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-06-16 16:52:27 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[21ms]
2025-06-16 16:52:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:52:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-16 16:52:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId]
2025-06-16 16:52:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTenantService],MethodName=[queryByTenantId],SpendTime=[6ms]
2025-06-16 16:52:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-16 16:52:33 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[282ms]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[kwsy001][Success][登录成功]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[16ms]
2025-06-16 16:52:34 [XNIO-1 task-2] INFO  o.d.auth.listener.UserActionListener - user doLogin, useId:sys_user:1931985777415335938, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJGbktBcnV5STd5aTBXSnpERFlvZGVENFM2MndMUWZCNyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XllYOlAi6pNmM0wHXJd8533kZVfdyuCkEzXnTernKZk
2025-06-16 17:57:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 17:57:40 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Starting RuoYiAuthApplication using Java 17.0.14 with PID 14300 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-auth\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 17:57:40 [main] INFO  o.dromara.auth.RuoYiAuthApplication - The following 1 profile is active: "dev"
2025-06-16 17:57:40 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-auth.yml, group=DEFAULT_GROUP] success
2025-06-16 17:57:40 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 17:57:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 17:57:46 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 17:57:47 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 17:57:47 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 17:57:48 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:57:48 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:57:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 17:57:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 17:57:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 17:57:49 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 17:57:49 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-auth *************:9210 register finished
2025-06-16 17:57:52 [main] INFO  o.dromara.auth.RuoYiAuthApplication - Started RuoYiAuthApplication in 15.043 seconds (process running for 15.999)
2025-06-16 17:57:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 17:57:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
2025-06-16 17:57:53 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
