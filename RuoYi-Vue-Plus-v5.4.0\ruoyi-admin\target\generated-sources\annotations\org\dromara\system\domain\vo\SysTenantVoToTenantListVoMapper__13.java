package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__11;
import org.dromara.web.domain.vo.TenantListVo;
import org.dromara.web.domain.vo.TenantListVoToSysTenantVoMapper__13;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {TenantListVoToSysTenantVoMapper__13.class,SysTenantVoToSysTenantMapper__11.class,SysTenantToSysTenantVoMapper__11.class},
    imports = {}
)
public interface SysTenantVoToTenantListVoMapper__13 extends BaseMapper<SysTenantVo, TenantListVo> {
}
