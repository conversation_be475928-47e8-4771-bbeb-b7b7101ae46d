package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__5;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__5;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysRoleVoToSysRoleMapper__5.class,SysRoleToSysRoleVoMapper__5.class,SysUserVoToSysUserMapper__5.class,SysUserBoToSysUserMapper__5.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__5 extends BaseMapper<SysUser, SysUserVo> {
}
