{"groups": [{"name": "api-decrypt", "type": "org.dromara.gateway.config.properties.ApiDecryptProperties", "sourceType": "org.dromara.gateway.config.properties.ApiDecryptProperties"}, {"name": "security.ignore", "type": "org.dromara.gateway.config.properties.IgnoreWhiteProperties", "sourceType": "org.dromara.gateway.config.properties.IgnoreWhiteProperties"}, {"name": "spring.cloud.gateway", "type": "org.dromara.gateway.config.properties.CustomGatewayProperties", "sourceType": "org.dromara.gateway.config.properties.CustomGatewayProperties"}], "properties": [{"name": "api-decrypt.enabled", "type": "java.lang.Bo<PERSON>an", "description": "加密开关", "sourceType": "org.dromara.gateway.config.properties.ApiDecryptProperties"}, {"name": "api-decrypt.header-flag", "type": "java.lang.String", "description": "头部标识", "sourceType": "org.dromara.gateway.config.properties.ApiDecryptProperties"}, {"name": "security.ignore.whites", "type": "java.util.List<java.lang.String>", "description": "放行白名单配置，网关不校验此处的白名单", "sourceType": "org.dromara.gateway.config.properties.IgnoreWhiteProperties"}, {"name": "spring.cloud.gateway.request-log", "type": "java.lang.Bo<PERSON>an", "description": "请求日志", "sourceType": "org.dromara.gateway.config.properties.CustomGatewayProperties"}], "hints": []}