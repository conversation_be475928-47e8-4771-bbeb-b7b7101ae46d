import {
  config,
  setup,
  ui_default,
  version
} from "./chunk-2K5G4TR6.js";
import {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  coreVersion,
  createEvent,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  use,
  useFns,
  usePermission,
  useSize,
  validators
} from "./chunk-TETVOAVO.js";
import "./chunk-GAYNWPQE.js";
import "./chunk-7J2PGW6H.js";
import "./chunk-H3LFO6AW.js";
import "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  config,
  coreVersion,
  createEvent,
  vxe_ui_default as default,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  use,
  useFns,
  usePermission,
  useSize,
  validators,
  version
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-ui_index__js.js.map
