{"doc": " 流程分类Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询流程分类\n\n @param categoryId 主键\n @return 流程分类\n"}, {"name": "selectCategoryNameById", "paramTypes": ["java.lang.Long"], "doc": " 根据流程分类ID查询流程分类名称\n\n @param categoryId 流程分类ID\n @return 流程分类名称\n"}, {"name": "queryList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 查询符合条件的流程分类列表\n\n @param bo 查询条件\n @return 流程分类列表\n"}, {"name": "selectCategoryTreeList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 查询流程分类树结构信息\n\n @param category 流程分类信息\n @return 流程分类树信息集合\n"}, {"name": "checkCategoryDataScope", "paramTypes": ["java.lang.Long"], "doc": " 校验流程分类是否有数据权限\n\n @param categoryId 流程分类ID\n"}, {"name": "checkCategoryNameUnique", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 校验流程分类名称是否唯一\n\n @param category 流程分类信息\n @return 结果\n"}, {"name": "checkCategoryExistDefinition", "paramTypes": ["java.lang.Long"], "doc": " 查询流程分类是否存在流程定义\n\n @param categoryId 流程分类ID\n @return 结果 true 存在 false 不存在\n"}, {"name": "hasChildByCategoryId", "paramTypes": ["java.lang.Long"], "doc": " 是否存在流程分类子节点\n\n @param categoryId 流程分类ID\n @return 结果\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 新增流程分类\n\n @param bo 流程分类\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 修改流程分类\n\n @param bo 流程分类\n @return 是否修改成功\n"}, {"name": "updateCategoryChildren", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 修改子元素关系\n\n @param categoryId   被修改的流程分类ID\n @param newAncestors 新的父ID集合\n @param oldAncestors 旧的父ID集合\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": " 删除流程分类信息\n\n @param categoryId 主键\n @return 是否删除成功\n"}], "constructors": []}