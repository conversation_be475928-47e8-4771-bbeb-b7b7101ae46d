import {
  select_default
} from "./chunk-6DEZ4KBF.js";
import {
  dynamicApp
} from "./chunk-2K5G4TR6.js";
import {
  VxeUI
} from "./chunk-TETVOAVO.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js
var VxeSelect = Object.assign(select_default, {
  install: function(app) {
    app.component(select_default.name, select_default);
  }
});
dynamicApp.use(VxeSelect);
VxeUI.component(select_default);
var Select = VxeSelect;
var select_default2 = VxeSelect;

export {
  VxeSelect,
  Select,
  select_default2 as select_default
};
//# sourceMappingURL=chunk-BAUS4XVA.js.map
