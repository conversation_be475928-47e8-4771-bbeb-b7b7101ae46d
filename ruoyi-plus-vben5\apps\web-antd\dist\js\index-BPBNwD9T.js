import{b9 as ve,bP as he,h as B,_ as c,t as xe,e as ie,aJ as re,c as J,g as k,aM as I,bj as be,b8 as Se,j as ye,r as Q,c4 as Ce,p as we,aG as Pe,bQ as Ie,aE as Oe,cK as Te}from"./bootstrap-DCMzVRvD.js";import{M as Fe,a as X,u as Me}from"./index-Bg2oL4a6.js";import{S as se}from"./index-Ollxi7Rl.js";import{d as H,a5 as Ee,C as W,ac as $e,a as h,B as j,p as Y,s as Ne,D as Be,a4 as je,$ as Z,x as Ae,I as Le,q as Re}from"../jse/index-index-C-MnMZEz.js";import{B as De}from"./BaseInput-B4f3ADM3.js";import{g as ze,a as ee}from"./statusUtils-d85DZFMd.js";import{i as _e,g as He,a as Ke,b as Ve,c as We,d as Ue}from"./index-CFj2VWFk.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./index-CHpIOV4R.js";import"./slide-B82O6h2Y.js";import"./css-Dmgy8YJo.js";function ke(e){const{selectionStart:a}=e;return e.value.slice(0,a)}function Xe(e){let a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return(Array.isArray(a)?a:[a]).reduce((t,i)=>{const p=e.lastIndexOf(i);return p>t.location?{location:p,prefix:i}:t},{location:-1,prefix:""})}function te(e){return(e||"").toLowerCase()}function Ye(e,a,n){const t=e[0];if(!t||t===n)return e;let i=e;const p=a.length;for(let r=0;r<p;r+=1)if(te(i[r])!==te(a[r])){i=i.slice(r);break}else r===p-1&&(i=i.slice(p));return i}function Ge(e,a){const{measureLocation:n,prefix:t,targetText:i,selectionStart:p,split:r}=a;let l=e.slice(0,n);l[l.length-r.length]===r&&(l=l.slice(0,l.length-r.length)),l&&(l=`${l}${r}`);let d=Ye(e.slice(p),i.slice(p-n-t.length),r);d.slice(0,r.length)===r&&(d=d.slice(r.length));const u=`${l}${t}${i}${r}`;return{text:`${u}${d}`,selectionLocation:u.length}}function qe(e,a){e.setSelectionRange(a,a),e.blur(),e.focus()}function Je(e,a){const{split:n}=a;return!n||e.indexOf(n)===-1}function Qe(e,a){let{value:n=""}=a;const t=e.toLowerCase();return n.toLowerCase().indexOf(t)!==-1}const le=Symbol("MentionsContextKey");function Ze(){}const et=H({compatConfig:{MODE:3},name:"DropdownMenu",props:{prefixCls:String,options:{type:Array,default:()=>[]}},setup(e,a){let{slots:n}=a;const{activeIndex:t,setActiveIndex:i,selectOption:p,onFocus:r=Ze,loading:l}=Ee(le,{activeIndex:W(),loading:W(!1)});let d;const u=b=>{clearTimeout(d),d=setTimeout(()=>{r(b)})};return $e(()=>{clearTimeout(d)}),()=>{var b;const{prefixCls:O,options:S}=e,m=S[t.value]||{};return h(Fe,{prefixCls:`${O}-menu`,activeKey:m.value,onSelect:x=>{let{key:y}=x;const C=S.find(w=>{let{value:T}=w;return T===y});p(C)},onMousedown:u},{default:()=>[!l.value&&S.map((x,y)=>{var C,w;const{value:T,disabled:A,label:F=x.value,class:L,style:K}=x;return h(X,{key:T,disabled:A,onMouseenter:()=>{i(y)},class:L,style:K},{default:()=>[(w=(C=n.option)===null||C===void 0?void 0:C.call(n,x))!==null&&w!==void 0?w:typeof F=="function"?F(x):F]})}),!l.value&&S.length===0?h(X,{key:"notFoundContent",disabled:!0},{default:()=>[(b=n.notFoundContent)===null||b===void 0?void 0:b.call(n)]}):null,l.value&&h(X,{key:"loading",disabled:!0},{default:()=>[h(se,{size:"small"},null)]})]})}}}),tt={bottomRight:{points:["tl","br"],offset:[0,4],overflow:{adjustX:0,adjustY:1}},bottomLeft:{points:["tr","bl"],offset:[0,4],overflow:{adjustX:0,adjustY:1}},topRight:{points:["bl","tr"],offset:[0,-4],overflow:{adjustX:0,adjustY:1}},topLeft:{points:["br","tl"],offset:[0,-4],overflow:{adjustX:0,adjustY:1}}},nt=H({compatConfig:{MODE:3},name:"KeywordTrigger",props:{loading:{type:Boolean,default:void 0},options:{type:Array,default:()=>[]},prefixCls:String,placement:String,visible:{type:Boolean,default:void 0},transitionName:String,getPopupContainer:Function,direction:String,dropdownClassName:String},setup(e,a){let{slots:n}=a;const t=()=>`${e.prefixCls}-dropdown`,i=()=>{const{options:r}=e;return h(et,{prefixCls:t(),options:r},{notFoundContent:n.notFoundContent,option:n.option})},p=j(()=>{const{placement:r,direction:l}=e;let d="topRight";return l==="rtl"?d=r==="top"?"topLeft":"bottomLeft":d=r==="top"?"topRight":"bottomRight",d});return()=>{const{visible:r,transitionName:l,getPopupContainer:d}=e;return h(ve,{prefixCls:t(),popupVisible:r,popup:i(),popupClassName:e.dropdownClassName,popupPlacement:p.value,popupTransitionName:l,builtinPlacements:tt,getPopupContainer:d},{default:n.default})}}}),ot=xe("top","bottom"),ue={autofocus:{type:Boolean,default:void 0},prefix:B.oneOfType([B.string,B.arrayOf(B.string)]),prefixCls:String,value:String,disabled:{type:Boolean,default:void 0},split:String,transitionName:String,placement:B.oneOf(ot),character:B.any,characterRender:Function,filterOption:{type:[Boolean,Function]},validateSearch:Function,getPopupContainer:{type:Function},options:he(),loading:{type:Boolean,default:void 0},rows:[Number,String],direction:{type:String}},ce=c(c({},ue),{dropdownClassName:String}),de={prefix:"@",split:" ",rows:1,validateSearch:Je,filterOption:()=>Qe};ie(ce,de);var ne=function(e,a){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,t=Object.getOwnPropertySymbols(e);i<t.length;i++)a.indexOf(t[i])<0&&Object.prototype.propertyIsEnumerable.call(e,t[i])&&(n[t[i]]=e[t[i]]);return n};function oe(){}const at=H({compatConfig:{MODE:3},name:"Mentions",inheritAttrs:!1,props:ie(ce,de),emits:["change","select","search","focus","blur","pressenter"],setup(e,a){let{emit:n,attrs:t,expose:i,slots:p}=a;const r=Y(null),l=Y(null),d=Y(),u=Ne({value:e.value||"",measuring:!1,measureLocation:0,measureText:null,measurePrefix:"",activeIndex:0,isFocus:!1});Be(()=>{u.value=e.value});const b=s=>{n("change",s)},O=s=>{let{target:{value:o}}=s;b(o)},S=(s,o,f)=>{c(u,{measuring:!0,measureText:s,measurePrefix:o,measureLocation:f,activeIndex:0})},m=s=>{c(u,{measuring:!1,measureLocation:0,measureText:null}),s==null||s()},x=s=>{const{which:o}=s;if(u.measuring){if(o===I.UP||o===I.DOWN){const f=R.value.length,g=o===I.UP?-1:1,v=(u.activeIndex+g+f)%f;u.activeIndex=v,s.preventDefault()}else if(o===I.ESC)m();else if(o===I.ENTER){if(s.preventDefault(),!R.value.length){m();return}const f=R.value[u.activeIndex];L(f)}}},y=s=>{const{key:o,which:f}=s,{measureText:g,measuring:v}=u,{prefix:V,validateSearch:E}=e,D=s.target;if(D.composing)return;const $=ke(D),{location:N,prefix:M}=Xe($,V);if([I.ESC,I.UP,I.DOWN,I.ENTER].indexOf(f)===-1)if(N!==-1){const P=$.slice(N+M.length),z=E(P,e),_=!!U(P).length;z?(o===M||o==="Shift"||v||P!==g&&_)&&S(P,M,N):v&&m(),z&&n("search",P,M)}else v&&m()},C=s=>{u.measuring||n("pressenter",s)},w=s=>{A(s)},T=s=>{F(s)},A=s=>{clearTimeout(d.value);const{isFocus:o}=u;!o&&s&&n("focus",s),u.isFocus=!0},F=s=>{d.value=setTimeout(()=>{u.isFocus=!1,m(),n("blur",s)},100)},L=s=>{const{split:o}=e,{value:f=""}=s,{text:g,selectionLocation:v}=Ge(u.value,{measureLocation:u.measureLocation,targetText:f,prefix:u.measurePrefix,selectionStart:l.value.getSelectionStart(),split:o});b(g),m(()=>{qe(l.value.input,v)}),n("select",s,u.measurePrefix)},K=s=>{u.activeIndex=s},U=s=>{const o=s||u.measureText||"",{filterOption:f}=e;return e.options.filter(v=>f?f(o,v):!0)},R=j(()=>U());return i({blur:()=>{l.value.blur()},focus:()=>{l.value.focus()}}),je(le,{activeIndex:Z(u,"activeIndex"),setActiveIndex:K,selectOption:L,onFocus:A,onBlur:F,loading:Z(e,"loading")}),Ae(()=>{Le(()=>{u.measuring&&(r.value.scrollTop=l.value.getScrollTop())})}),()=>{const{measureLocation:s,measurePrefix:o,measuring:f}=u,{prefixCls:g,placement:v,transitionName:V,getPopupContainer:E,direction:D}=e,$=ne(e,["prefixCls","placement","transitionName","getPopupContainer","direction"]),{class:N,style:M}=t,P=ne(t,["class","style"]),z=re($,["value","prefix","split","validateSearch","filterOption","options","loading"]),_=c(c(c({},z),P),{onChange:oe,onSelect:oe,value:u.value,onInput:O,onBlur:T,onKeydown:x,onKeyup:y,onFocus:w,onPressenter:C});return h("div",{class:J(g,N),style:M},[h(De,k(k({},_),{},{ref:l,tag:"textarea"}),null),f&&h("div",{ref:r,class:`${g}-measure`},[u.value.slice(0,s),h(nt,{prefixCls:g,transitionName:V,dropdownClassName:e.dropdownClassName,placement:v,options:f?R.value:[],visible:!0,direction:D,getPopupContainer:E},{default:()=>[h("span",null,[o])],notFoundContent:p.notFoundContent,option:p.option}),u.value.slice(s+o.length)])])}}}),it={value:String,disabled:Boolean,payload:be()},pe=c(c({},it),{label:Se([])}),fe={name:"Option",props:pe,render(e,a){let{slots:n}=a;var t;return(t=n.default)===null||t===void 0?void 0:t.call(n)}};H(c({compatConfig:{MODE:3}},fe));const rt=e=>{const{componentCls:a,colorTextDisabled:n,controlItemBgHover:t,controlPaddingHorizontal:i,colorText:p,motionDurationSlow:r,lineHeight:l,controlHeight:d,inputPaddingHorizontal:u,inputPaddingVertical:b,fontSize:O,colorBgElevated:S,borderRadiusLG:m,boxShadowSecondary:x}=e,y=Math.round((e.controlHeight-e.fontSize*e.lineHeight)/2);return{[a]:c(c(c(c(c({},Q(e)),He(e)),{position:"relative",display:"inline-block",height:"auto",padding:0,overflow:"hidden",lineHeight:l,whiteSpace:"pre-wrap",verticalAlign:"bottom"}),Ke(e,a)),{"&-disabled":{"> textarea":c({},Ue(e))},"&-focused":c({},We(e)),[`&-affix-wrapper ${a}-suffix`]:{position:"absolute",top:0,insetInlineEnd:u,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto"},[`> textarea, ${a}-measure`]:{color:p,boxSizing:"border-box",minHeight:d-2,margin:0,padding:`${b}px ${u}px`,overflow:"inherit",overflowX:"hidden",overflowY:"auto",fontWeight:"inherit",fontSize:"inherit",fontFamily:"inherit",fontStyle:"inherit",fontVariant:"inherit",fontSizeAdjust:"inherit",fontStretch:"inherit",lineHeight:"inherit",direction:"inherit",letterSpacing:"inherit",whiteSpace:"inherit",textAlign:"inherit",verticalAlign:"top",wordWrap:"break-word",wordBreak:"inherit",tabSize:"inherit"},"> textarea":c({width:"100%",border:"none",outline:"none",resize:"none",backgroundColor:"inherit"},Ve(e.colorTextPlaceholder)),[`${a}-measure`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:-1,color:"transparent",pointerEvents:"none","> span":{display:"inline-block",minHeight:"1em"}},"&-dropdown":c(c({},Q(e)),{position:"absolute",top:-9999,insetInlineStart:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",fontSize:O,fontVariant:"initial",backgroundColor:S,borderRadius:m,outline:"none",boxShadow:x,"&-hidden":{display:"none"},[`${a}-dropdown-menu`]:{maxHeight:e.dropdownHeight,marginBottom:0,paddingInlineStart:0,overflow:"auto",listStyle:"none",outline:"none","&-item":c(c({},Ce),{position:"relative",display:"block",minWidth:e.controlItemWidth,padding:`${y}px ${i}px`,color:p,fontWeight:"normal",lineHeight:l,cursor:"pointer",transition:`background ${r} ease`,"&:hover":{backgroundColor:t},"&:first-child":{borderStartStartRadius:m,borderStartEndRadius:m,borderEndStartRadius:0,borderEndEndRadius:0},"&:last-child":{borderStartStartRadius:0,borderStartEndRadius:0,borderEndStartRadius:m,borderEndEndRadius:m},"&-disabled":{color:n,cursor:"not-allowed","&:hover":{color:n,backgroundColor:t,cursor:"not-allowed"}},"&-selected":{color:p,fontWeight:e.fontWeightStrong,backgroundColor:t},"&-active":{backgroundColor:t}})}})})}},st=ye("Mentions",e=>{const a=_e(e);return[rt(a)]},e=>({dropdownHeight:250,controlItemWidth:100,zIndexPopup:e.zIndexPopupBase+50}));var ae=function(e,a){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,t=Object.getOwnPropertySymbols(e);i<t.length;i++)a.indexOf(t[i])<0&&Object.prototype.propertyIsEnumerable.call(e,t[i])&&(n[t[i]]=e[t[i]]);return n};function lt(){return!0}const ut=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{prefix:n="@",split:t=" "}=a,i=Array.isArray(n)?n:[n];return e.split(t).map(function(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=null;return i.some(l=>p.slice(0,l.length)===l?(r=l,!0):!1),r!==null?{prefix:r,value:p.slice(r.length)}:null}).filter(p=>!!p&&!!p.value)},ct=()=>c(c({},ue),{loading:{type:Boolean,default:void 0},onFocus:{type:Function},onBlur:{type:Function},onSelect:{type:Function},onChange:{type:Function},onPressenter:{type:Function},"onUpdate:value":{type:Function},notFoundContent:B.any,defaultValue:String,id:String,status:String}),G=H({compatConfig:{MODE:3},name:"AMentions",inheritAttrs:!1,props:ct(),slots:Object,setup(e,a){let{slots:n,emit:t,attrs:i,expose:p}=a;var r,l;const{prefixCls:d,renderEmpty:u,direction:b}=we("mentions",e),[O,S]=st(d),m=W(!1),x=W(null),y=W((l=(r=e.value)!==null&&r!==void 0?r:e.defaultValue)!==null&&l!==void 0?l:""),C=Pe(),w=Ie.useInject(),T=j(()=>ze(w.status,e.status));Me({prefixCls:j(()=>`${d.value}-menu`),mode:j(()=>"vertical"),selectable:j(()=>!1),onClick:()=>{},validator:o=>{let{mode:f}=o}}),Re(()=>e.value,o=>{y.value=o});const A=o=>{m.value=!0,t("focus",o)},F=o=>{m.value=!1,t("blur",o),C.onFieldBlur()},L=function(){for(var o=arguments.length,f=new Array(o),g=0;g<o;g++)f[g]=arguments[g];t("select",...f),m.value=!0},K=o=>{e.value===void 0&&(y.value=o),t("update:value",o),t("change",o),C.onFieldChange()},U=()=>{const o=e.notFoundContent;return o!==void 0?o:n.notFoundContent?n.notFoundContent():u("Select")},R=()=>{var o;return Oe(((o=n.default)===null||o===void 0?void 0:o.call(n))||[]).map(f=>{var g,v;return c(c({},Te(f)),{label:(v=(g=f.children)===null||g===void 0?void 0:g.default)===null||v===void 0?void 0:v.call(g)})})};p({focus:()=>{x.value.focus()},blur:()=>{x.value.blur()}});const s=j(()=>e.loading?lt:e.filterOption);return()=>{const{disabled:o,getPopupContainer:f,rows:g=1,id:v=C.id.value}=e,V=ae(e,["disabled","getPopupContainer","rows","id"]),{hasFeedback:E,feedbackIcon:D}=w,{class:$}=i,N=ae(i,["class"]),M=re(V,["defaultValue","onUpdate:value","prefixCls"]),P=J({[`${d.value}-disabled`]:o,[`${d.value}-focused`]:m.value,[`${d.value}-rtl`]:b.value==="rtl"},ee(d.value,T.value),!E&&$,S.value),z=c(c(c(c({prefixCls:d.value},M),{disabled:o,direction:b.value,filterOption:s.value,getPopupContainer:f,options:e.loading?[{value:"ANTDV_SEARCHING",disabled:!0,label:h(se,{size:"small"},null)}]:e.options||R(),class:P}),N),{rows:g,onChange:K,onSelect:L,onFocus:A,onBlur:F,ref:x,value:y.value,id:v}),_=h(at,k(k({},z),{},{dropdownClassName:S.value}),{notFoundContent:U,option:n.option});return O(E?h("div",{class:J(`${d.value}-affix-wrapper`,ee(`${d.value}-affix-wrapper`,T.value,E),$,S.value)},[_,h("span",{class:`${d.value}-suffix`},[D])]):_)}}}),q=H(c(c({compatConfig:{MODE:3}},fe),{name:"AMentionsOption",props:pe})),Pt=c(G,{Option:q,getMentions:ut,install:e=>(e.component(G.name,G),e.component(q.name,q),e)});export{q as MentionsOption,Pt as default,ct as mentionsProps};
