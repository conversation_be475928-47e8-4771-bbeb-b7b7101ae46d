package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__11;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysSocialBoToSysSocialMapper__11.class,SysSocialVoToSysSocialMapper__11.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__11 extends BaseMapper<SysSocial, SysSocialVo> {
}
