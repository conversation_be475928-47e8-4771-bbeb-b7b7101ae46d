package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__11;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysRoleVoToSysRoleMapper__11.class,SysRoleBoToSysRoleMapper__11.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__11 extends BaseMapper<SysRole, SysRoleVo> {
}
