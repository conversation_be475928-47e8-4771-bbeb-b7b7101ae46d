const o="Root",t={root:"System",user:"User",role:"Role",menu:"Menu",dept:"Department",post:"Post",dict:"Dictionary",config:"Parameter Settings",notice:"Notifications",log:{root:"Log",operation:"Operation Log",login:"Login Log"},oss:"File",client:"Client"},n={root:"Tenant",package:"Package"},e={root:"System Monitoring",online:"Online Users",cache:"Cache Monitoring",admin:"Admin Monitoring",job:"Task Scheduling Center"},s={root:"System Tools",gen:"Code Generation"},i={root:"Workflow",category:"Process Category",model:"Model",define:"Process Definition",monitor:{root:"Process Monitoring",instance:"Process Instance",todo:"Pending Tasks"},form:"Form"},r={root:"My Tasks",apply:"My Initiated Tasks",todo:"My Pending Tasks",done:"My Completed Tasks",cc:"My CC"},a={root:o,system:t,tenant:n,monitor:e,tool:s,workflow:i,task:r};export{a as default,e as monitor,o as root,t as system,r as task,n as tenant,s as tool,i as workflow};
