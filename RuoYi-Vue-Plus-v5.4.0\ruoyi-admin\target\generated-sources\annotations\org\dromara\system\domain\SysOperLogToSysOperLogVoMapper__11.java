package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__12;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOperLogVoToSysOperLogMapper__11.class,SysOperLogBoToSysOperLogMapper__12.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__11 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
