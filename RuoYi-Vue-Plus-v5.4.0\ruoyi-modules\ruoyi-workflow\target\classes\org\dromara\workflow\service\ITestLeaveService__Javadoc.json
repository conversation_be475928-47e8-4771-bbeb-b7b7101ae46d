{"doc": " 请假Service接口\n\n <AUTHOR>\n @date 2023-07-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询请假\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询请假列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 查询请假列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 新增请假\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 修改请假\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.List"], "doc": " 校验并批量删除请假信息\n"}], "constructors": []}