2025-06-16 00:12:57 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 00:12:57 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 00:12:57 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:12:57 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 00:12:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 08:26:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:26:52 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 21724 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:26:52 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-16 08:26:52 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-16 08:26:52 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:26:52 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:26:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:26:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 08:26:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 08:26:58 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7fda0e30
2025-06-16 08:26:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 08:26:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 08:26:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 08:26:59 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:27:01 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:27:01 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:27:01 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:27:01 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:27:01 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@58658f63
2025-06-16 08:27:07 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:27:07 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:27:07 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:27:07 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:27:07 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen ************:9202 register finished
2025-06-16 08:27:10 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 21.986 seconds (process running for 22.653)
2025-06-16 08:27:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:27:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:27:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-16 08:27:11 [RMI TCP Connection(6)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:16:40 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) has completed., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](ruoyi-gen), dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-system], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x84a1b4fd, L:/************:58771 - R:/************:20880]], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x84a1b4fd, L:/************:58771 - R:/************:20880], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0x84a1b4fd, L:/************:58771 ! R:/************:20880] of ************:58771 -> ************:20880 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) has completed., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](ruoyi-gen) to null, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=21724&qos.enable=false&register-mode=instance&release=3.3.4, nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=21724&qos.enable=false&register-mode=instance&release=3.3.4], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.registry.nacos.NacosRegistry -  [DUBBO] Destroy registry:nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-gen&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=21724&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-gen) has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.a.d.c.s.c.DubboSpringInitializer -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@208205ed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 09:16:45 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 10:55:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:55:18 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 42928 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:55:18 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-16 10:55:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 10:55:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 10:55:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 10:55:26 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3a516e4d
2025-06-16 10:55:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 10:55:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 10:55:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 10:55:27 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:55:30 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:55:30 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:55:30 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:55:30 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:55:30 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5a545b0f
2025-06-16 10:55:38 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:55:38 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:55:38 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:55:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:55:38 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen ************:9202 register finished
2025-06-16 10:55:42 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 27.745 seconds (process running for 28.526)
2025-06-16 10:55:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 10:55:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:55:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-16 10:55:42 [RMI TCP Connection(8)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 17:39:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 17:59:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 17:59:12 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 31268 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 17:59:12 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-16 17:59:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-16 17:59:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 17:59:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 17:59:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 17:59:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 17:59:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 17:59:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@17f43f4a
2025-06-16 17:59:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 17:59:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 17:59:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 17:59:18 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 17:59:19 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 17:59:19 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 17:59:19 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:59:19 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:59:19 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@315105f
2025-06-16 17:59:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 17:59:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 17:59:24 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 17:59:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 17:59:24 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-16 17:59:26 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 16.243 seconds (process running for 16.938)
2025-06-16 17:59:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 17:59:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 17:59:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-16 17:59:27 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
