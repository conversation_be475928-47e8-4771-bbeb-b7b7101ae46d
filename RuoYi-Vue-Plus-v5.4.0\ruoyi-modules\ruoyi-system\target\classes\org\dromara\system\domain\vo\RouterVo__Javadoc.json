{"doc": " 路由配置信息\n\n <AUTHOR> Li\n", "fields": [{"name": "name", "doc": " 路由名字\n"}, {"name": "path", "doc": " 路由地址\n"}, {"name": "hidden", "doc": " 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现\n"}, {"name": "redirect", "doc": " 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\n"}, {"name": "component", "doc": " 组件地址\n"}, {"name": "query", "doc": " 路由参数：如 {\"id\": 1, \"name\": \"ry\"}\n"}, {"name": "alwaysShow", "doc": " 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\n"}, {"name": "meta", "doc": " 其他元素\n"}, {"name": "children", "doc": " 子路由\n"}], "enumConstants": [], "methods": [], "constructors": []}