package io.github.linpeilie;

import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__5;
import org.dromara.system.domain.SysClientToSysClientVoMapper__5;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__5;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__5;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__5;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__5;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__5;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__5;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__5;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__5;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__5;
import org.dromara.system.domain.SysOssToSysOssVoMapper__5;
import org.dromara.system.domain.SysPostToSysPostVoMapper__5;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__5;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__5;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper__5;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__5;
import org.dromara.system.domain.SysUserToSysUserVoMapper__5;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__5;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__5;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__5;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__5;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__5;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__5;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__5;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__5;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__5;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__5;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__5;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__5;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__5;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__5;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__5;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__5;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__5;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__5;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__5;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__5;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__5;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__5;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__5;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__5;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__5;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__5;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__5;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__5;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__5;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__5;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__5;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__5;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__5;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__5;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__5;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__697.class, SysRoleBoToSysRoleMapper__5.class, SysClientToSysClientVoMapper__5.class, SysDictTypeBoToSysDictTypeMapper__5.class, SysNoticeBoToSysNoticeMapper__5.class, SysSocialVoToSysSocialMapper__5.class, SysDictTypeToSysDictTypeVoMapper__5.class, SysOperLogBoToSysOperLogMapper__5.class, SysTenantVoToSysTenantMapper__5.class, SysSocialBoToSysSocialMapper__5.class, SysUserToSysUserVoMapper__5.class, SysLogininforToSysLogininforVoMapper__5.class, SysRoleToSysRoleVoMapper__5.class, SysOssConfigVoToSysOssConfigMapper__5.class, SysOssVoToSysOssMapper__5.class, SysOperLogToSysOperLogVoMapper__5.class, SysConfigBoToSysConfigMapper__5.class, SysConfigVoToSysConfigMapper__5.class, SysDeptBoToSysDeptMapper__5.class, SysTenantToSysTenantVoMapper__5.class, SysClientVoToSysClientMapper__5.class, SysMenuBoToSysMenuMapper__5.class, SysOssBoToSysOssMapper__5.class, SysOssConfigToSysOssConfigVoMapper__5.class, SysDeptToSysDeptVoMapper__5.class, SysDictDataVoToSysDictDataMapper__5.class, SysDictDataBoToSysDictDataMapper__5.class, SysOssConfigBoToSysOssConfigMapper__5.class, SysDictTypeVoToSysDictTypeMapper__5.class, SysTenantPackageToSysTenantPackageVoMapper__5.class, SysDictDataToSysDictDataVoMapper__5.class, SysNoticeToSysNoticeVoMapper__5.class, SysRoleVoToSysRoleMapper__5.class, SysLogininforVoToSysLogininforMapper__5.class, SysTenantPackageBoToSysTenantPackageMapper__5.class, SysMenuVoToSysMenuMapper__5.class, SysPostVoToSysPostMapper__5.class, SysOperLogBoToOperLogEventMapper__5.class, SysConfigToSysConfigVoMapper__5.class, SysSocialToSysSocialVoMapper__5.class, SysDeptVoToSysDeptMapper__5.class, OperLogEventToSysOperLogBoMapper__5.class, SysMenuToSysMenuVoMapper__5.class, SysTenantPackageVoToSysTenantPackageMapper__5.class, SysPostBoToSysPostMapper__5.class, SysLogininforBoToSysLogininforMapper__5.class, SysTenantBoToSysTenantMapper__5.class, SysUserBoToSysUserMapper__5.class, SysOssToSysOssVoMapper__5.class, SysNoticeVoToSysNoticeMapper__5.class, SysClientBoToSysClientMapper__5.class, SysOperLogVoToSysOperLogMapper__5.class, SysUserVoToSysUserMapper__5.class, SysPostToSysPostVoMapper__5.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__697 {
}
