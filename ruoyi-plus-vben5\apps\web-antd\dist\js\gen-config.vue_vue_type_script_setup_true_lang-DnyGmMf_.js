var v=(e,t,l)=>new Promise((a,c)=>{var r=i=>{try{u(l.next(i))}catch(o){c(o)}},m=i=>{try{u(l.throw(i))}catch(o){c(o)}},u=i=>i.done?a(i.value):Promise.resolve(i.value).then(r,m);u((l=l.apply(e,t)).next())});import"./vxe-table-DzEj5Fop.js";import{g as C}from"./dict-type-GiaNpHd1.js";import"./index-BxBCzu2M.js";import{I as y}from"./Search-ClCped_G.js";import p from"./index-CIjgbPOA.js";import w from"./index-D2_dYV5Z.js";import{a as n,s as x,d as E,a5 as O,c as q,o as W,j as B,b as R}from"../jse/index-index-C-MnMZEz.js";import{a as f}from"./get-popup-container-P4S1sr5h.js";import{u as _}from"./use-vxe-grid-BC7vZzEr.js";const k=["Long","String","Integer","Double","BigDecimal","Date","Boolean","LocalDate","LocalDateTime"],b=[{label:"=",value:"EQ"},{label:"!=",value:"NE"},{label:">",value:"GT"},{label:">=",value:"GE"},{label:"<",value:"LT"},{label:"<=",value:"LE"},{label:"LIKE",value:"LIKE"},{label:"BETWEEN",value:"BETWEEN"}],T=[{label:"文本框",value:"input"},{label:"文本域",value:"textarea"},{label:"下拉框",value:"select"},{label:"单选框",value:"radio"},{label:"复选框",value:"checkbox"},{label:"日期控件",value:"datetime"},{label:"图片上传",value:"imageUpload"},{label:"文件上传",value:"fileUpload"},{label:"富文本",value:"editor"}],h=x([{label:"未设置",value:""}]);(function(){return v(this,null,function*(){(yield C()).forEach(l=>{const a={label:`${l.dictName} | ${l.dictType}`,value:l.dictType};h.push(a)})})})();function s(e,t){const l=e[t]?"是":"否",a=e[t]?"text-green-500":"text-red-500";return n("span",{class:a},[l])}function d(e,t){return n(w,{checked:e[t],"onUpdate:checked":l=>e[t]=l},null)}const j={columnComment:[{required:!0,message:"请输入"}],javaField:[{required:!0,message:"请输入"}]},D=[{title:"序号",type:"seq",fixed:"left",width:"50",align:"center"},{title:"字段列名",field:"columnName",showOverflow:"tooltip",fixed:"left",minWidth:150},{title:"字段描述",field:"columnComment",minWidth:150,slots:{edit:({row:e})=>n(y,{value:e.columnComment,"onUpdate:value":t=>e.columnComment=t},null)},editRender:{}},{title:"db类型",field:"columnType",minWidth:120,showOverflow:"tooltip"},{title:"Java类型",field:"javaType",minWidth:150,slots:{edit:({row:e})=>{const t=k.map(l=>({label:l,value:l}));return n(p,{class:"w-full",getPopupContainer:f,options:t,value:e.javaType,"onUpdate:value":l=>e.javaType=l},null)}},editRender:{}},{title:"Java属性名",field:"javaField",minWidth:150,showOverflow:"tooltip",slots:{edit:({row:e})=>n(y,{value:e.javaField,"onUpdate:value":t=>e.javaField=t},null)},editRender:{}},{title:"插入",field:"insert",minWidth:80,showOverflow:"tooltip",align:"center",slots:{default:({row:e})=>s(e,"insert"),edit:({row:e})=>d(e,"insert")},editRender:{}},{title:"编辑",field:"edit",showOverflow:"tooltip",align:"center",minWidth:80,slots:{default:({row:e})=>s(e,"edit"),edit:({row:e})=>d(e,"edit")},editRender:{}},{title:"列表",field:"list",showOverflow:"tooltip",align:"center",minWidth:80,slots:{default:({row:e})=>s(e,"list"),edit:({row:e})=>d(e,"list")},editRender:{}},{title:"查询",field:"query",showOverflow:"tooltip",align:"center",minWidth:80,slots:{default:({row:e})=>s(e,"query"),edit:({row:e})=>d(e,"query")},editRender:{}},{title:"查询方式",field:"queryType",showOverflow:"tooltip",align:"center",minWidth:150,slots:{default:({row:e})=>{const t=e.queryType,l=b.find(a=>a.value===t);return l?l.label:t},edit:({row:e})=>n(p,{class:"w-full",getPopupContainer:f,options:b,value:e.queryType,"onUpdate:value":t=>e.queryType=t},null)},editRender:{}},{title:"必填",field:"required",showOverflow:"tooltip",align:"center",minWidth:80,slots:{default:({row:e})=>s(e,"required"),edit:({row:e})=>d(e,"required")},editRender:{}},{title:"显示类型",field:"htmlType",showOverflow:"tooltip",minWidth:150,align:"center",slots:{default:({row:e})=>{const t=e.htmlType,l=T.find(a=>a.value===t);return l?l.label:t},edit:({row:e})=>n(p,{class:"w-full",getPopupContainer:f,options:T,value:e.htmlType,"onUpdate:value":t=>e.htmlType=t},null)},editRender:{}},{title:"字典类型",field:"dictType",showOverflow:"tooltip",minWidth:230,align:"center",titlePrefix:{message:"仅'下拉框', '单选框', '复选框'支持字典类型"},slots:{default:({row:e})=>{const t=e.dictType,l=h.find(a=>a.value===t);return l?l.label:t},edit:({row:e})=>{const t=()=>{e.dictType=""},l=e.htmlType!=="select"&&e.htmlType!=="radio"&&e.htmlType!=="checkbox";return n(p,{allowClear:!0,class:"w-full",disabled:l,getPopupContainer:f,onDeselect:t,options:h,placeholder:"请选择字典类型",value:e.dictType,"onUpdate:value":a=>e.dictType=a},null)}},editRender:{}}],U={class:"flex flex-col gap-[16px]"},L={class:"h-[calc(100vh-200px)] overflow-y-hidden"},z=E({__name:"gen-config",setup(e,{expose:t}){const l=O("genInfoData"),a={columns:D,keepSource:!0,editConfig:{trigger:"click",mode:"cell",showStatus:!0},editRules:j,rowConfig:{keyField:"id",isCurrent:!0},columnConfig:{resizable:!0},proxyConfig:{enabled:!0},toolbarConfig:{enabled:!1},height:"auto",pagerConfig:{enabled:!1},data:l.value.columns},[c,r]=_({gridOptions:a});function m(){return v(this,null,function*(){return!(yield r.grid.validate())})}function u(){var i,o,g;return(g=(o=(i=r==null?void 0:r.grid)==null?void 0:i.getData)==null?void 0:o.call(i))!=null?g:[]}return t({validateTable:m,getTableRecords:u}),(i,o)=>(W(),q("div",U,[B("div",L,[n(R(c))])]))}});export{z as _};
