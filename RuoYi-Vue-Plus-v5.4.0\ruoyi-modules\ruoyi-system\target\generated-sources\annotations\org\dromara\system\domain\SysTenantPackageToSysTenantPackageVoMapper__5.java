package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__5;
import org.dromara.system.domain.vo.SysTenantPackageVo;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysTenantPackageBoToSysTenantPackageMapper__5.class,SysTenantPackageVoToSysTenantPackageMapper__5.class},
    imports = {}
)
public interface SysTenantPackageToSysTenantPackageVoMapper__5 extends BaseMapper<SysTenantPackage, SysTenantPackageVo> {
}
