{"doc": " 短信服务\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 同步方法：发送固定消息模板短信\n\n @param phone   目标手机号\n @param message 短信内容\n @return 封装了短信发送结果的 RemoteSms 对象\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.util.LinkedHashMap"], "doc": " 同步方法：发送固定消息模板多模板参数短信\n\n @param phone    目标手机号\n @param messages 短信模板参数，使用 LinkedHashMap 以保持参数顺序\n @return 封装了短信发送结果的 RemoteSms 对象\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.LinkedHashMap"], "doc": " 同步方法：使用自定义模板发送短信\n\n @param phone      目标手机号\n @param templateId 短信模板ID\n @param messages   短信模板参数，使用 LinkedHashMap 以保持参数顺序\n @return 封装了短信发送结果的 RemoteSms 对象\n"}, {"name": "messageTexting", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 同步方法：群发固定模板短信\n\n @param phones  目标手机号列表（1~1000）\n @param message 短信内容\n @return 封装了短信发送结果的 RemoteSms 对象\n"}, {"name": "messageTexting", "paramTypes": ["java.util.List", "java.lang.String", "java.util.LinkedHashMap"], "doc": " 同步方法：使用自定义模板群发短信\n\n @param phones     目标手机号列表（1~1000）（1~1000）\n @param templateId 短信模板ID\n @param messages   短信模板参数，使用 LinkedHashMap 以保持参数顺序\n @return 封装了短信发送结果的 RemoteSms 对象\n"}, {"name": "sendMessageAsync", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 异步方法：发送固定消息模板短信\n\n @param phone   目标手机号\n @param message 短信内容\n"}, {"name": "sendMessageAsync", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.LinkedHashMap"], "doc": " 异步方法：使用自定义模板发送短信\n\n @param phone      目标手机号\n @param templateId 短信模板ID\n @param messages   短信模板参数，使用 LinkedHashMap 以保持参数顺序\n"}, {"name": "delayMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 延迟发送：发送固定消息模板短信\n\n @param phone       目标手机号\n @param message     短信内容\n @param delayedTime 延迟发送时间（毫秒）\n"}, {"name": "delayMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.LinkedHashMap", "java.lang.Long"], "doc": " 延迟发送：使用自定义模板发送定时短信\n\n @param phone       目标手机号\n @param templateId  短信模板ID\n @param messages    短信模板参数，使用 LinkedHashMap 以保持参数顺序\n @param delayedTime 延迟发送时间（毫秒）\n"}, {"name": "delayMessageTexting", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Long"], "doc": " 延迟群发：群发延迟短信\n\n @param phones      目标手机号列表（1~1000）\n @param message     短信内容\n @param delayedTime 延迟发送时间（毫秒）\n"}, {"name": "delayMessageTexting", "paramTypes": ["java.util.List", "java.lang.String", "java.util.LinkedHashMap", "java.lang.Long"], "doc": " 延迟群发：使用自定义模板发送群体延迟短信\n\n @param phones      目标手机号列表（1~1000）\n @param templateId  短信模板ID\n @param messages    短信模板参数，使用 LinkedHashMap 以保持参数顺序\n @param delayedTime 延迟发送时间（毫秒）\n"}, {"name": "addBlacklist", "paramTypes": ["java.lang.String"], "doc": " 加入黑名单\n\n @param phone 手机号\n"}, {"name": "addBlacklist", "paramTypes": ["java.util.List"], "doc": " 加入黑名单\n\n @param phones 手机号列表\n"}, {"name": "removeBlacklist", "paramTypes": ["java.lang.String"], "doc": " 移除黑名单\n\n @param phone 手机号\n"}, {"name": "removeBlacklist", "paramTypes": ["java.util.List"], "doc": " 移除黑名单\n\n @param phones 手机号\n"}], "constructors": []}