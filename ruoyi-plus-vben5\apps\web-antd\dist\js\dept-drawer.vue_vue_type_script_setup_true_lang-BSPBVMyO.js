var l=(e,N,i)=>new Promise((d,f)=>{var g=n=>{try{m(i.next(n))}catch(u){f(u)}},o=n=>{try{m(i.throw(n))}catch(u){f(u)}},m=n=>n.done?d(n.value):Promise.resolve(n.value).then(g,o);m((i=i.apply(e,N)).next())});import{y as c,al as b,ar as h,bq as y,$ as S,aj as x}from"./bootstrap-DCMzVRvD.js";import{l as F}from"./index-ocPq22VW.js";import{u as T,d as D}from"./popup-D6rC6QBG.js";import{g as _}from"./dict-BLkXAGS5.js";import{r as V}from"./render-BxXtQdeV.js";import{a as w}from"./get-popup-container-P4S1sr5h.js";import{l as q,a as k}from"./tree-DFBawhPd.js";import{d as M,p as O,B as R,P as E,h as z,o as G,w as U,a as W,b as C}from"../jse/index-index-C-MnMZEz.js";import{u as Y}from"./use-drawer-6qcpK-D1.js";function j(e){return c.get("/system/dept/list",{params:e})}function H(e){return c.get(`/system/dept/list/exclude/${e}`)}function J(e){return c.get(`/system/dept/${e}`)}function K(e){return c.postWithMsg("/system/dept",e)}function Q(e){return c.putWithMsg("/system/dept",e)}function de(e){return c.deleteWithMsg(`/system/dept/${e}`)}const pe=()=>[{component:"Input",fieldName:"deptName",label:"部门名称"},{component:"Select",componentProps:{getPopupContainer:w,options:_(h.SYS_NORMAL_DISABLE)},fieldName:"status",label:"部门状态"}],ce=[{field:"deptName",title:"部门名称",treeNode:!0},{field:"deptCategory",title:"类别编码"},{field:"orderNum",title:"排序"},{field:"status",title:"状态",slots:{default:({row:e})=>V(e.status,h.SYS_NORMAL_DISABLE)}},{field:"createTime",title:"创建时间"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],X=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"deptId"},{component:"TreeSelect",componentProps:{getPopupContainer:w},dependencies:{show:e=>e.parentId!==0,triggerFields:["parentId"]},fieldName:"parentId",label:"上级部门",rules:"selectRequired"},{component:"Input",fieldName:"deptName",label:"部门名称",rules:"required"},{component:"InputNumber",fieldName:"orderNum",label:"显示排序",rules:"required"},{component:"Input",fieldName:"deptCategory",label:"类别编码"},{component:"Select",componentProps:{allowClear:!1,getPopupContainer:w},fieldName:"leader",label:"负责人"},{component:"Input",fieldName:"phone",label:"联系电话",rules:b().regex(/^1[3,4578]\d{9}$/,{message:"请输入正确的手机号"}).optional().or(y(""))},{component:"Input",fieldName:"email",label:"邮箱",rules:b().email({message:"请输入正确的邮箱"}).optional().or(y(""))},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:_(h.SYS_NORMAL_DISABLE),optionType:"button"},defaultValue:"0",fieldName:"status",label:"状态"}],me=M({__name:"dept-drawer",emits:["reload"],setup(e,{emit:N}){const i=N,d=O(!1),f=R(()=>d.value?S("pages.common.edit"):S("pages.common.add")),[g,o]=x({commonConfig:{componentProps:{class:"w-full"},formItemClass:"col-span-2",labelWidth:80},schema:X(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function m(a,t=!1){return l(this,null,function*(){let s=[];s=yield!a||t?j({}):H(a);const r=q(s,{id:"deptId",pid:"parentId"});return k(r,"deptName"," / "),r})}function n(a){return l(this,null,function*(){const t=yield m(a,!d.value);o.updateSchema([{componentProps:{fieldNames:{label:"deptName",value:"deptId"},showSearch:!0,treeData:t,treeDefaultExpandAll:!0,treeLine:{showLeafIcon:!1},treeNodeLabelProp:"fullName"},fieldName:"parentId"}])})}function u(a){return l(this,null,function*(){const t=yield F(a),s=t.map(r=>({label:`${r.userName} | ${r.nickName}`,value:r.userId}));o.updateSchema([{componentProps:{disabled:t.length===0,options:s,placeholder:t.length===0?"该部门暂无用户":"请选择部门负责人"},fieldName:"leader"}])})}function L(){return l(this,null,function*(){o.updateSchema([{componentProps:{disabled:!0,options:[],placeholder:"仅在更新时可选部门负责人"},fieldName:"leader"}])})}const{onBeforeClose:A,markInitialized:B,resetInitialized:I}=T({initializedGetter:D(o),currentGetter:D(o)}),[P,p]=Y({onBeforeClose:A,onClosed:v,onConfirm:$,onOpenChange(a){return l(this,null,function*(){if(!a)return null;p.drawerLoading(!0);const{id:t,update:s}=p.getData();if(d.value=s,t&&(yield o.setFieldValue("parentId",t),s)){const r=yield J(t);yield o.setValues(r)}yield s&&t?u(t):L(),yield n(t),yield B(),p.drawerLoading(!1)})}});function $(){return l(this,null,function*(){try{p.lock(!0);const{valid:a}=yield o.validate();if(!a)return;const t=E(yield o.getValues());yield d.value?Q(t):K(t),I(),i("reload"),p.close()}catch(a){console.error(a)}finally{p.lock(!1)}})}function v(){return l(this,null,function*(){yield o.resetForm(),I()})}return(a,t)=>(G(),z(C(P),{title:f.value,class:"w-[600px]"},{default:U(()=>[W(C(g))]),_:1},8,["title"]))}});export{me as _,de as a,ce as c,j as d,pe as q};
