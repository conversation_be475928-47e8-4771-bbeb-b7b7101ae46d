import{g as t}from"./dict-BLkXAGS5.js";import{T as o}from"./index-B6iusSRX.js";import{a as r}from"./get-popup-container-P4S1sr5h.js";import{ar as n}from"./bootstrap-DCMzVRvD.js";import{a}from"../jse/index-index-C-MnMZEz.js";const s=[{color:"green",label:"全部数据权限",value:"1"},{color:"default",label:"自定数据权限",value:"2"},{color:"orange",label:"本部门数据权限",value:"3"},{color:"cyan",label:"本部门及以下数据权限",value:"4"},{color:"error",label:"仅本人数据权限",value:"5"},{color:"default",label:"部门及以下或本人数据权限",value:"6"}],f=()=>[{component:"Input",fieldName:"roleName",label:"角色名称"},{component:"Input",fieldName:"roleKey",label:"权限字符"},{component:"Select",componentProps:{options:t(n.SYS_NORMAL_DISABLE)},fieldName:"status",label:"状态"},{component:"RangePicker",fieldName:"createTime",label:"创建时间"}],b=[{type:"checkbox",width:60},{title:"角色名称",field:"roleName"},{title:"权限字符",field:"roleKey",slots:{default:({row:e})=>a(o,{color:"processing"},{default:()=>[e.roleKey]})}},{title:"数据权限",field:"dataScope",slots:{default:({row:e})=>{const l=s.find(d=>d.value===e.dataScope);return l?a(o,{color:l.color},{default:()=>[l.label]}):a(o,null,{default:()=>[e.dataScope]})}}},{title:"排序",field:"roleSort"},{title:"状态",field:"status",slots:{default:"status"}},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],N=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"roleId",label:"角色ID"},{component:"Input",fieldName:"roleName",label:"角色名称",rules:"required"},{component:"Input",fieldName:"roleKey",help:"如: test simpleUser等",label:"权限标识",rules:"required"},{component:"InputNumber",fieldName:"roleSort",label:"角色排序",rules:"required"},{component:"Select",componentProps:{allowClear:!1,options:t(n.SYS_NORMAL_DISABLE),getPopupContainer:r},defaultValue:"0",fieldName:"status",help:"修改后, 拥有该角色的用户将自动下线.",label:"角色状态",rules:"required"},{component:"Radio",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"menuCheckStrictly",label:"菜单权限"},{component:"Input",defaultValue:[],fieldName:"menuIds",label:"菜单权限",formItemClass:"col-span-2"},{component:"Textarea",defaultValue:"",fieldName:"remark",formItemClass:"col-span-2",label:"备注"}],S=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"roleId",label:"角色ID"},{component:"Radio",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"deptCheckStrictly",label:"deptCheckStrictly"},{component:"Input",componentProps:{disabled:!0},fieldName:"roleName",label:"角色名称"},{component:"Input",componentProps:{disabled:!0},fieldName:"roleKey",label:"权限标识"},{component:"Select",componentProps:{allowClear:!1,getPopupContainer:r,options:s},fieldName:"dataScope",help:"更改后需要用户重新登录才能生效",label:"权限范围"},{component:"TreeSelect",defaultValue:[],dependencies:{show:e=>e.dataScope==="2",triggerFields:["dataScope"]},fieldName:"deptIds",help:"更改后立即生效",label:"部门权限"}];export{S as a,s as b,b as c,N as d,f as q};
