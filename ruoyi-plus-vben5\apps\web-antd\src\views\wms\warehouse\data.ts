import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'warehouseNumber',
    label: '仓库编码',
  },
  {
    component: 'Input',
    fieldName: 'warehouseName',
    label: '仓库名称',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.WMS_WAREHOUSE_TYPE 便于维护
      options: getDictOptions('wms_warehouse_type'),
    },
    fieldName: 'warehouseType',
    label: '仓库属性',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.WMS_WAREHOUSE_INSTOCKTYPE 便于维护
      options: getDictOptions('wms_warehouse_instocktype'),
    },
    fieldName: 'warehouseInventoryStatus',
    label: '库存状态',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.WMS_WAREHOUSE_INSTOCKTYPE 便于维护
      options: getDictOptions('wms_warehouse_instocktype'),
    },
    fieldName: 'warehouseRecevingStatus',
    label: '收料状态',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '仓库编码',
    field: 'warehouseNumber',
    sortable: true,
  },
  {
    title: '仓库名称',
    field: 'warehouseName',
    sortable: true,
  },
  {
    title: '所属部门',
    field: 'deptNames',
    width: 200,
  },
  {
    title: '仓库属性',
    field: 'warehouseType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.WMS_WAREHOUSE_TYPE 便于维护
        return renderDict(row.warehouseType, 'wms_warehouse_type');
      },
    },
  },
  {
    title: '库存状态',
    field: 'warehouseInventoryStatus',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.WMS_WAREHOUSE_INSTOCKTYPE 便于维护
        return renderDict(row.warehouseInventoryStatus, 'wms_warehouse_instocktype');
      },
    },
  },
  {
    title: '收料状态',
    field: 'warehouseRecevingStatus',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.WMS_WAREHOUSE_INSTOCKTYPE 便于维护
        return renderDict(row.warehouseRecevingStatus, 'wms_warehouse_instocktype');
      },
    },
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

