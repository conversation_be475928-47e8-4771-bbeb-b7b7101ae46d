{"doc": " 通用 工作流服务\n\n <AUTHOR>\n @Date 2024/6/3\n", "fields": [], "enumConstants": [], "methods": [{"name": "deleteInstance", "paramTypes": ["java.util.List"], "doc": " 运行中的实例 删除程实例，删除历史记录，删除业务与流程关联信息\n\n @param businessIds 业务id\n @return 结果\n"}, {"name": "getBusinessStatusByTaskId", "paramTypes": ["java.lang.Long"], "doc": " 获取当前流程状态\n\n @param taskId 任务id\n @return 状态\n"}, {"name": "getBusinessStatus", "paramTypes": ["java.lang.String"], "doc": " 获取当前流程状态\n\n @param businessId 业务id\n @return 状态\n"}, {"name": "setVariable", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 设置流程变量\n\n @param instanceId 流程实例id\n @param variable   流程变量\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": " 获取流程变量\n\n @param instanceId 流程实例id\n"}, {"name": "getInstanceIdByBusinessId", "paramTypes": ["java.lang.String"], "doc": " 按照业务id查询流程实例id\n\n @param businessId 业务id\n @return 结果\n"}, {"name": "syncDef", "paramTypes": ["java.lang.String"], "doc": " 新增租户流程定义\n\n @param tenantId 租户id\n"}, {"name": "startWorkFlow", "paramTypes": ["org.dromara.workflow.api.domain.RemoteStartProcess"], "doc": " 启动流程\n\n @param startProcess 参数\n @return 结果\n"}, {"name": "completeTask", "paramTypes": ["org.dromara.workflow.api.domain.RemoteCompleteTask"], "doc": " 办理任务\n\n @param completeTask 参数\n @return 结果\n"}], "constructors": []}