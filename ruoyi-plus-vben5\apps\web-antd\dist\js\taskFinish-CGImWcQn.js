var J=Object.defineProperty,Q=Object.defineProperties;var W=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var X=Object.prototype.hasOwnProperty,Y=Object.prototype.propertyIsEnumerable;var $=(n,i,o)=>i in n?J(n,i,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[i]=o,_=(n,i)=>{for(var o in i||(i={}))X.call(i,o)&&$(n,o,i[o]);if(U)for(var o of U(i))Y.call(i,o)&&$(n,o,i[o]);return n},S=(n,i)=>Q(n,W(i));var x=(n,i,o)=>new Promise((g,v)=>{var c=m=>{try{r(o.next(m))}catch(b){v(b)}},C=m=>{try{r(o.throw(m))}catch(b){v(b)}},r=m=>m.done?g(m.value):Promise.resolve(m.value).then(c,C);r((o=o.apply(n,i)).next())});import{c as Z}from"./index-B4NcjlQn.js";import{f as D}from"./index-CZhogUxH.js";import ee from"./approval-card-6R4jdcAw.js";import{_ as te}from"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import{au as P,aw as j,bL as oe,T as ae,aq as le,bn as w,z as re}from"./bootstrap-DCMzVRvD.js";import{_ as ie}from"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{b as se,R as pe,F as ne}from"./constant-BxyJFj0E.js";import"./index-BxBCzu2M.js";import{a as ue,I as z}from"./Search-ClCped_G.js";import{S as me}from"./index-Ollxi7Rl.js";import{_ as de}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as fe,p as d,B as ve,E as ce,v as O,l as ge,h as T,o as y,w as s,j as f,a as l,b as a,k as V,c as B,f as M,F as be,K as ye,t as ke}from"../jse/index-index-C-MnMZEz.js";import{a as _e}from"./tree-DFBawhPd.js";import{P as xe}from"./index-qvRUEWLR.js";import{a as A}from"./get-popup-container-P4S1sr5h.js";import we from"./index-B1dOBW9y.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./relativeTime-DEqmspR6.js";import"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import"./index-D59rZjD-.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-DCFckLr6.js";import"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import"./use-modal-CeMSCP2m.js";import"./x-Bfkqqjgb.js";import"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import"./index-BLwHKR_M.js";import"./index-i2_yEmR1.js";import"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import"./user-select-modal-9zMWfXzj.js";import"./index-ocPq22VW.js";import"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./use-vxe-grid-BC7vZzEr.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./LeftOutlined-DE4sX_Jv.js";import"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BeyziwLP.js";import"./index-BIMmoqOy.js";import"./move-DLDqWE9R.js";import"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import"./rotate-cw-DzZTu9nW.js";import"./index-BELOxkuV.js";import"./useMemo-BwJyMulH.js";import"./index-B-GBMyZJ.js";import"./DownOutlined-CERO2SW5.js";const Ce={class:"flex h-full gap-2"},he={class:"bg-background relative flex h-full min-w-[320px] max-w-[320px] flex-col rounded-lg"},Ne={class:"bg-background z-100 sticky left-0 top-0 w-full rounded-t-lg border-b-[1px] border-solid p-2"},Fe={class:"flex items-center gap-1"},Ie={class:"flex"},Se={key:2,class:"flex items-center justify-center text-[14px] opacity-50"},Te={key:3,class:"absolute left-0 top-0 flex h-full w-full items-center justify-center bg-[rgba(0,0,0,0.1)]"},Be={class:"bg-background sticky bottom-0 w-full rounded-b-lg border-t-[1px] py-2"},Ee={class:"flex items-center justify-center"},Le=fe({__name:"taskFinish",setup(n){const i=P.PRESENTED_IMAGE_SIMPLE,o=d([]),g=d(0),v=d(1),c=d(!1),C={flowName:"",nodeName:"",flowCode:"",createByIds:[],category:null},r=d(j(C)),m=ve(()=>o.value.length===g.value),b=ce("cardContainerRef");function k(p=!1){return x(this,null,function*(){var u;(u=b.value)==null||u.scroll({top:0,behavior:"auto"}),v.value=1,h.value=void 0,g.value=0,I.value="",p&&(r.value=j(C),F.value=[]),c.value=!0;const e=yield D(_({pageSize:10,pageNum:v.value},r.value));if(o.value=e.rows.map(t=>S(_({},t),{active:!1})),g.value=e.total,c.value=!1,o.value.length>0){const t=o.value[0];h.value=t,L(t)}})}O(k);const E=oe(p=>x(null,null,function*(){if(!p.target)return;const{scrollTop:e,clientHeight:u,scrollHeight:t}=p.target;if(e+u>=t-se&&!m.value){c.value=!0,v.value+=1;const G=yield D(_({pageSize:10,pageNum:v.value},r.value));o.value.push(...G.rows.map(K=>S(_({},K),{active:!1}))),c.value=!1}}),200),I=d(""),h=d();function L(p){return x(this,null,function*(){const{id:e}=p;I.value!==e&&(h.value=p,o.value.forEach(u=>{u.active=u.id===e}),I.value=e)})}const N=d(!1),F=d([]);function H(p){N.value=!0,F.value=p,r.value.createByIds=p.map(e=>e.userId)}const R=d([]);return O(()=>x(null,null,function*(){const p=yield Z();_e(p,"label"," / "),R.value=p})),(p,e)=>{const u=ge("a-button");return y(),T(a(de),{"auto-content-height":!0},{default:s(()=>[f("div",Ce,[f("div",he,[f("div",Ne,[f("div",Fe,[l(a(ue),{value:r.value.flowName,"onUpdate:value":e[0]||(e[0]=t=>r.value.flowName=t),placeholder:"流程名称搜索",onSearch:e[1]||(e[1]=t=>k(!1))},null,8,["value"]),l(a(ae),{placement:"top",title:"重置"},{default:s(()=>[l(u,{onClick:e[2]||(e[2]=t=>k(!0))},{default:s(()=>[l(a(pe))]),_:1})]),_:1}),l(a(xe),{open:N.value,"onUpdate:open":e[10]||(e[10]=t=>N.value=t),"get-popup-container":a(A),placement:"rightTop",trigger:"click"},{title:s(()=>e[12]||(e[12]=[f("div",{class:"w-full border-b pb-[12px] text-[16px]"},"搜索",-1)])),content:s(()=>[l(a(le),{colon:!1,"label-col":{span:6},model:r.value,autocomplete:"off",class:"w-[300px]",onFinish:e[9]||(e[9]=()=>k(!1))},{default:s(()=>[l(a(w),{label:"申请人"},{default:s(()=>[l(a(ie),{"user-list":F.value,"onUpdate:userList":e[3]||(e[3]=t=>F.value=t),onCancel:e[4]||(e[4]=()=>N.value=!0),onFinish:H},null,8,["user-list"])]),_:1}),l(a(w),{label:"流程分类"},{default:s(()=>[l(a(we),{value:r.value.category,"onUpdate:value":e[5]||(e[5]=t=>r.value.category=t),"allow-clear":!0,"field-names":{label:"label",value:"id"},"get-popup-container":a(A),"tree-data":R.value,"tree-default-expand-all":!0,"tree-line":{showLeafIcon:!1},placeholder:"请选择","tree-node-filter-prop":"label","tree-node-label-prop":"fullName"},null,8,["value","get-popup-container","tree-data"])]),_:1}),l(a(w),{label:"任务名称"},{default:s(()=>[l(a(z),{value:r.value.nodeName,"onUpdate:value":e[6]||(e[6]=t=>r.value.nodeName=t),placeholder:"请输入"},null,8,["value"])]),_:1}),l(a(w),{label:"流程编码"},{default:s(()=>[l(a(z),{value:r.value.flowCode,"onUpdate:value":e[7]||(e[7]=t=>r.value.flowCode=t),placeholder:"请输入"},null,8,["value"])]),_:1}),l(a(w),null,{default:s(()=>[f("div",Ie,[l(u,{block:"","html-type":"submit",type:"primary"},{default:s(()=>e[13]||(e[13]=[V(" 搜索 ")])),_:1,__:[13]}),l(u,{block:"",class:"ml-2",onClick:e[8]||(e[8]=t=>k(!0))},{default:s(()=>e[14]||(e[14]=[V(" 重置 ")])),_:1,__:[14]})])]),_:1})]),_:1},8,["model"])]),default:s(()=>[l(u,null,{default:s(()=>[l(a(ne))]),_:1})]),_:1},8,["open","get-popup-container"])])]),f("div",{ref_key:"cardContainerRef",ref:b,class:"thin-scrollbar flex flex-1 flex-col gap-2 overflow-y-auto py-3",onScroll:e[11]||(e[11]=(...t)=>a(E)&&a(E)(...t))},[o.value.length>0?(y(!0),B(be,{key:0},ye(o.value,t=>(y(),T(a(ee),{key:t.id,info:t,class:"mx-2",onClick:q=>L(t)},null,8,["info","onClick"]))),128)):(y(),T(a(P),{key:1,image:a(i)},null,8,["image"])),m.value&&o.value.length>0?(y(),B("div",Se," 没有更多数据了 ")):M("",!0),c.value?(y(),B("div",Te,[l(a(me),{tip:"加载中..."})])):M("",!0)],544),f("div",Be,[f("div",Ee," 共 "+ke(g.value)+" 条记录 ",1)])]),l(a(te),{task:h.value,type:"readonly"},null,8,["task"])])]),_:1})}}}),po=re(Le,[["__scopeId","data-v-584ce632"]]);export{po as default};
