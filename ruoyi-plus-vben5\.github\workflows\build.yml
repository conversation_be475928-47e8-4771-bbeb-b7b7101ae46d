# name: Dependabot post-update
name: Build detection
on:
  pull_request_target:
    types: [opened, synchronize, reopened]
    branches:
      - main

env:
  HUSKY: '0'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

permissions:
  contents: read
  pull-requests: write

jobs:
  post-update:
    if: github.repository == 'vbenjs/vue-vben-admin'
    # if: ${{ github.actor == 'dependabot[bot]' }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          # - macos-latest
          - windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Checkout out pull request
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh pr checkout ${{ github.event.pull_request.number }}

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Build
        run: |
          pnpm run build
