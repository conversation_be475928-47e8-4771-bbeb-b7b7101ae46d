var T=Object.defineProperty;var $=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var D=(i,o,e)=>o in i?T(i,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[o]=e,S=(i,o)=>{for(var e in o||(o={}))E.call(o,e)&&D(i,e,o[e]);if($)for(var e of $(o))O.call(o,e)&&D(i,e,o[e]);return i};var g=(i,o,e)=>new Promise((v,b)=>{var m=r=>{try{d(e.next(r))}catch(c){b(c)}},x=r=>{try{d(e.throw(r))}catch(c){b(c)}},d=r=>r.done?v(r.value):Promise.resolve(r.value).then(m,x);d((e=e.apply(i,o)).next())});import{as as q,an as R}from"./bootstrap-DCMzVRvD.js";import{v as j}from"./vxe-table-DzEj5Fop.js";import{c as z}from"./download-UJak946_.js";import{c as F,q as G,_ as H,d as L,a as W,b as V}from"./demo-modal.vue_vue_type_script_setup_true_lang-CVCxATRH.js";import M from"./index-BeyziwLP.js";import{_ as I}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as J,p as K,l as A,S as Q,h as p,o as l,w as s,a as y,b as n,T as C,k as h,t as _,j as U}from"../jse/index-index-C-MnMZEz.js";import{u as X}from"./use-vxe-grid-BC7vZzEr.js";import{u as Y}from"./use-modal-CeMSCP2m.js";import{P as Z}from"./index-DNdMANjv.js";import{a as ee}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const be=J({__name:"index",setup(i){const o={commonConfig:{labelWidth:80},schema:G(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={checkboxConfig:{highlight:!0,reserve:!0},columns:F,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(u,...w)=>g(null,[u,...w],function*({page:t},a={}){return yield W(S({pageNum:t.currentPage,pageSize:t.pageSize},a))})}},rowConfig:{isHover:!0,keyField:"id"}},v=K(!1),[b,m]=X({formOptions:o,gridOptions:e}),[x,d]=Y({connectedComponent:H});function r(){d.setData({}),d.open()}function c(t){return g(this,null,function*(){d.setData({id:t.id}),d.open()})}function B(t){return g(this,null,function*(){yield V(t.id),yield m.query()})}function N(){const a=m.grid.getCheckboxRecords().map(u=>u.id);R.confirm({title:"提示",okType:"danger",content:`确认删除选中的${a.length}条记录吗？`,onOk:()=>g(null,null,function*(){yield V(a),yield m.query(),v.value=!1})})}return(t,a)=>{const u=A("a-button"),w=A("ghost-button"),f=Q("access");return l(),p(n(I),{"auto-content-height":!0},{default:s(()=>[y(n(b),null,{"toolbar-actions":s(()=>a[3]||(a[3]=[U("span",{class:"pl-[7px] text-[16px]"},"测试单列表",-1)])),"toolbar-tools":s(()=>[y(n(M),null,{default:s(()=>[C((l(),p(u,{onClick:a[0]||(a[0]=k=>n(z)(n(L),"测试单数据",n(m).formApi.form.values))},{default:s(()=>[h(_(t.$t("pages.common.export")),1)]),_:1})),[[f,["system:demo:export"],"code"]]),C((l(),p(u,{disabled:!n(j)(n(m)),danger:"",type:"primary",onClick:N},{default:s(()=>[h(_(t.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[f,["system:demo:remove"],"code"]]),C((l(),p(u,{type:"primary",onClick:r},{default:s(()=>[h(_(t.$t("pages.common.add")),1)]),_:1})),[[f,["system:demo:add"],"code"]])]),_:1})]),action:s(({row:k})=>[y(n(M),null,{default:s(()=>[C((l(),p(w,{onClick:q(P=>c(k),["stop"])},{default:s(()=>[h(_(t.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[f,["system:demo:edit"],"code"]]),y(n(Z),{"get-popup-container":n(ee),placement:"left",title:"确认删除？",onConfirm:P=>B(k)},{default:s(()=>[C((l(),p(w,{danger:"",onClick:a[1]||(a[1]=q(()=>{},["stop"]))},{default:s(()=>[h(_(t.$t("pages.common.delete")),1)]),_:1})),[[f,["system:demo:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),y(n(x),{onReload:a[2]||(a[2]=k=>n(m).query())})]),_:1})}}});export{be as default};
