package org.dromara.system.mapper;

import org.dromara.system.domain.SysUserConfig;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

/**
 * 用户配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface SysUserConfigMapper extends BaseMapperPlus<SysUserConfig, SysUserConfig> {

    /**
     * 根据用户ID和配置键查询配置值
     *
     * @param userId 用户ID
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @return 配置值
     */
    @Select("SELECT config_value FROM sys_user_config WHERE user_id = #{userId} AND config_key = #{configKey} AND tenant_id = #{tenantId}")
    String selectConfigValue(@Param("userId") Long userId, @Param("configKey") String configKey, @Param("tenantId") String tenantId);

}
