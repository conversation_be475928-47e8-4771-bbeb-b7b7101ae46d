package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssBoToSysOssMapper__3;
import org.dromara.resource.domain.vo.SysOssVo;
import org.dromara.resource.domain.vo.SysOssVoToSysOssMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssBoToSysOssMapper__3.class,SysOssVoToSysOssMapper__3.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__3 extends BaseMapper<SysOss, SysOssVo> {
}
