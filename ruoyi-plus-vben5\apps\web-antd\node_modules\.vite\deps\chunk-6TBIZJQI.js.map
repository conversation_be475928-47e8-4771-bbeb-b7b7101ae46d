{"version": 3, "sources": ["../../../../../node_modules/.pnpm/defu@6.1.4/node_modules/defu/dist/defu.mjs"], "sourcesContent": ["function isPlainObject(value) {\n  if (value === null || typeof value !== \"object\") {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(value);\n  if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {\n    return false;\n  }\n  if (Symbol.iterator in value) {\n    return false;\n  }\n  if (Symbol.toStringTag in value) {\n    return Object.prototype.toString.call(value) === \"[object Module]\";\n  }\n  return true;\n}\n\nfunction _defu(baseObject, defaults, namespace = \".\", merger) {\n  if (!isPlainObject(defaults)) {\n    return _defu(baseObject, {}, namespace, merger);\n  }\n  const object = Object.assign({}, defaults);\n  for (const key in baseObject) {\n    if (key === \"__proto__\" || key === \"constructor\") {\n      continue;\n    }\n    const value = baseObject[key];\n    if (value === null || value === void 0) {\n      continue;\n    }\n    if (merger && merger(object, key, value, namespace)) {\n      continue;\n    }\n    if (Array.isArray(value) && Array.isArray(object[key])) {\n      object[key] = [...value, ...object[key]];\n    } else if (isPlainObject(value) && isPlainObject(object[key])) {\n      object[key] = _defu(\n        value,\n        object[key],\n        (namespace ? `${namespace}.` : \"\") + key.toString(),\n        merger\n      );\n    } else {\n      object[key] = value;\n    }\n  }\n  return object;\n}\nfunction createDefu(merger) {\n  return (...arguments_) => (\n    // eslint-disable-next-line unicorn/no-array-reduce\n    arguments_.reduce((p, c) => _defu(p, c, \"\", merger), {})\n  );\n}\nconst defu = createDefu();\nconst defuFn = createDefu((object, key, currentValue) => {\n  if (object[key] !== void 0 && typeof currentValue === \"function\") {\n    object[key] = currentValue(object[key]);\n    return true;\n  }\n});\nconst defuArrayFn = createDefu((object, key, currentValue) => {\n  if (Array.isArray(object[key]) && typeof currentValue === \"function\") {\n    object[key] = currentValue(object[key]);\n    return true;\n  }\n});\n\nexport { createDefu, defu as default, defu, defuArrayFn, defuFn };\n"], "mappings": ";AAAA,SAAS,cAAc,OAAO;AAC5B,MAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC/C,WAAO;AAAA,EACT;AACA,QAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,MAAI,cAAc,QAAQ,cAAc,OAAO,aAAa,OAAO,eAAe,SAAS,MAAM,MAAM;AACrG,WAAO;AAAA,EACT;AACA,MAAI,OAAO,YAAY,OAAO;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,eAAe,OAAO;AAC/B,WAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAAA,EACnD;AACA,SAAO;AACT;AAEA,SAAS,MAAM,YAAY,UAAU,YAAY,KAAK,QAAQ;AAC5D,MAAI,CAAC,cAAc,QAAQ,GAAG;AAC5B,WAAO,MAAM,YAAY,CAAC,GAAG,WAAW,MAAM;AAAA,EAChD;AACA,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ;AACzC,aAAW,OAAO,YAAY;AAC5B,QAAI,QAAQ,eAAe,QAAQ,eAAe;AAChD;AAAA,IACF;AACA,UAAM,QAAQ,WAAW,GAAG;AAC5B,QAAI,UAAU,QAAQ,UAAU,QAAQ;AACtC;AAAA,IACF;AACA,QAAI,UAAU,OAAO,QAAQ,KAAK,OAAO,SAAS,GAAG;AACnD;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AACtD,aAAO,GAAG,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC;AAAA,IACzC,WAAW,cAAc,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AAC7D,aAAO,GAAG,IAAI;AAAA,QACZ;AAAA,QACA,OAAO,GAAG;AAAA,SACT,YAAY,GAAG,SAAS,MAAM,MAAM,IAAI,SAAS;AAAA,QAClD;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,IAAI;AAAA;AAAA,IAET,WAAW,OAAO,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA;AAE3D;AACA,IAAM,OAAO,WAAW;AACxB,IAAM,SAAS,WAAW,CAAC,QAAQ,KAAK,iBAAiB;AACvD,MAAI,OAAO,GAAG,MAAM,UAAU,OAAO,iBAAiB,YAAY;AAChE,WAAO,GAAG,IAAI,aAAa,OAAO,GAAG,CAAC;AACtC,WAAO;AAAA,EACT;AACF,CAAC;AACD,IAAM,cAAc,WAAW,CAAC,QAAQ,KAAK,iBAAiB;AAC5D,MAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,KAAK,OAAO,iBAAiB,YAAY;AACpE,WAAO,GAAG,IAAI,aAAa,OAAO,GAAG,CAAC;AACtC,WAAO;AAAA,EACT;AACF,CAAC;", "names": []}