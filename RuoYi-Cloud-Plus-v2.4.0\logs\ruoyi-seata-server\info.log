2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-16 08:21:52 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-16 08:21:52 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-16 08:21:52 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-16 08:21:52 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-16 08:21:52 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 29764 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:21:52 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-16 08:21:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-16 08:21:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-16 08:21:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 08:21:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-16 08:21:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 08:21:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1860 ms
2025-06-16 08:21:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 08:21:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 08:21:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-16 08:21:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d12f065, org.springframework.security.web.context.SecurityContextPersistenceFilter@77d58f3a, org.springframework.security.web.header.HeaderWriterFilter@32ddcca, org.springframework.security.web.csrf.CsrfFilter@2634d000, org.springframework.security.web.authentication.logout.LogoutFilter@4d66cb, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@57fc6759, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@738ed8f5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5f4f6f89, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33a7331, org.springframework.security.web.access.ExceptionTranslationFilter@76b019c4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6636448b]
2025-06-16 08:21:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-16 08:21:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-16 08:21:56 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 4.641 seconds (JVM running for 5.92)
2025-06-16 08:21:56 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-16 08:21:56 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-16 08:21:57 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-16 08:21:57 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 08:22:06 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-16 08:22:07 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-16 08:22:07 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10966 millSeconds
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
