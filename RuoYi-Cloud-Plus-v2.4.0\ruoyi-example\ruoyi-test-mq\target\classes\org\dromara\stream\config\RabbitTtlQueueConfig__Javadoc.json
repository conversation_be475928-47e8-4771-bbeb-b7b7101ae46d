{"doc": " RabbitTTL队列\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "delayQueue", "paramTypes": [], "doc": " 声明延迟队列\n"}, {"name": "delayExchange", "paramTypes": [], "doc": " 声明延迟交换机\n"}, {"name": "delayBinding", "paramTypes": ["org.springframework.amqp.core.Queue", "org.springframework.amqp.core.CustomExchange"], "doc": " 将延迟队列绑定到延迟交换机\n"}, {"name": "deadLetter<PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 声明死信队列\n"}, {"name": "deadLetterExchange", "paramTypes": [], "doc": " 声明死信交换机\n"}, {"name": "deadLetter<PERSON><PERSON>ing", "paramTypes": ["org.springframework.amqp.core.Queue", "org.springframework.amqp.core.DirectExchange"], "doc": " 将死信队列绑定到死信交换机\n"}], "constructors": []}