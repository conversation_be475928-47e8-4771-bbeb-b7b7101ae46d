{"doc": " redis 工具类\n\n <AUTHOR>\n @version 3.1.0 新增\n", "fields": [], "enumConstants": [], "methods": [{"name": "rateLimiter", "paramTypes": ["java.lang.String", "org.redisson.api.RateType", "int", "int"], "doc": " 限流\n\n @param key          限流key\n @param rateType     限流类型\n @param rate         速率\n @param rateInterval 速率间隔\n @return -1 表示失败\n"}, {"name": "rateLimiter", "paramTypes": ["java.lang.String", "org.redisson.api.RateType", "int", "int", "int"], "doc": " 限流\n\n @param key          限流key\n @param rateType     限流类型\n @param rate         速率\n @param rateInterval 速率间隔\n @param timeout      超时时间\n @return -1 表示失败\n"}, {"name": "getClient", "paramTypes": [], "doc": " 获取客户端实例\n"}, {"name": "publish", "paramTypes": ["java.lang.String", "java.lang.Object", "java.util.function.Consumer"], "doc": " 发布通道消息\n\n @param channelKey 通道key\n @param msg        发送数据\n @param consumer   自定义处理\n"}, {"name": "publish", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 发布消息到指定的频道\n\n @param channelKey 通道key\n @param msg        发送数据\n"}, {"name": "subscribe", "paramTypes": ["java.lang.String", "java.lang.Class", "java.util.function.Consumer"], "doc": " 订阅通道接收消息\n\n @param channelKey 通道key\n @param clazz      消息类型\n @param consumer   自定义处理\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 缓存基本的对象，Integer、String、实体类等\n\n @param key   缓存的键值\n @param value 缓存的值\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object", "boolean"], "doc": " 缓存基本的对象，保留当前对象 TTL 有效期\n\n @param key       缓存的键值\n @param value     缓存的值\n @param isSaveTtl 是否保留TTL有效期(例如: set之前ttl剩余90 set之后还是为90)\n @since Redis 6.X 以上使用 setAndKeepTTL 兼容 5.X 方案\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object", "java.time.Duration"], "doc": " 缓存基本的对象，Integer、String、实体类等\n\n @param key      缓存的键值\n @param value    缓存的值\n @param duration 时间\n"}, {"name": "setObjectIfAbsent", "paramTypes": ["java.lang.String", "java.lang.Object", "java.time.Duration"], "doc": " 如果不存在则设置 并返回 true 如果存在则返回 false\n\n @param key   缓存的键值\n @param value 缓存的值\n @return set成功或失败\n"}, {"name": "setObjectIfExists", "paramTypes": ["java.lang.String", "java.lang.Object", "java.time.Duration"], "doc": " 如果存在则设置 并返回 true 如果存在则返回 false\n\n @param key   缓存的键值\n @param value 缓存的值\n @return set成功或失败\n"}, {"name": "addObjectListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": " 注册对象监听器\n <p>\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\n\n @param key      缓存的键值\n @param listener 监听器配置\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "long"], "doc": " 设置有效时间\n\n @param key     Redis键\n @param timeout 超时时间\n @return true=设置成功；false=设置失败\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": " 设置有效时间\n\n @param key      Redis键\n @param duration 超时时间\n @return true=设置成功；false=设置失败\n"}, {"name": "getCacheObject", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的基本对象。\n\n @param key 缓存键值\n @return 缓存键值对应的数据\n"}, {"name": "getTimeToLive", "paramTypes": ["java.lang.String"], "doc": " 获得key剩余存活时间\n\n @param key 缓存键值\n @return 剩余存活时间\n"}, {"name": "deleteObject", "paramTypes": ["java.lang.String"], "doc": " 删除单个对象\n\n @param key 缓存的键值\n"}, {"name": "deleteObject", "paramTypes": ["java.util.Collection"], "doc": " 删除集合对象\n\n @param collection 多个对象\n"}, {"name": "isExistsObject", "paramTypes": ["java.lang.String"], "doc": " 检查缓存对象是否存在\n\n @param key 缓存的键值\n"}, {"name": "setCacheList", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 缓存List数据\n\n @param key      缓存的键值\n @param dataList 待缓存的List数据\n @return 缓存的对象\n"}, {"name": "addCacheList", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 追加缓存List数据\n\n @param key  缓存的键值\n @param data 待缓存的数据\n @return 缓存的对象\n"}, {"name": "addListListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": " 注册List监听器\n <p>\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\n\n @param key      缓存的键值\n @param listener 监听器配置\n"}, {"name": "getCacheList", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的list对象\n\n @param key 缓存的键值\n @return 缓存键值对应的数据\n"}, {"name": "getCacheListRange", "paramTypes": ["java.lang.String", "int", "int"], "doc": " 获得缓存的list对象(范围)\n\n @param key  缓存的键值\n @param form 起始下标\n @param to   截止下标\n @return 缓存键值对应的数据\n"}, {"name": "setCacheSet", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": " 缓存Set\n\n @param key     缓存键值\n @param dataSet 缓存的数据\n @return 缓存数据的对象\n"}, {"name": "addCacheSet", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 追加缓存Set数据\n\n @param key  缓存的键值\n @param data 待缓存的数据\n @return 缓存的对象\n"}, {"name": "addSetListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": " 注册Set监听器\n <p>\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\n\n @param key      缓存的键值\n @param listener 监听器配置\n"}, {"name": "getCacheSet", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的set\n\n @param key 缓存的key\n @return set对象\n"}, {"name": "setCacheMap", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 缓存Map\n\n @param key     缓存的键值\n @param dataMap 缓存的数据\n"}, {"name": "addMapListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": " 注册Map监听器\n <p>\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\n\n @param key      缓存的键值\n @param listener 监听器配置\n"}, {"name": "getCacheMap", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的Map\n\n @param key 缓存的键值\n @return map对象\n"}, {"name": "getCacheMapKeySet", "paramTypes": ["java.lang.String"], "doc": " 获得缓存Map的key列表\n\n @param key 缓存的键值\n @return key列表\n"}, {"name": "setCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 往Hash中存入数据\n\n @param key   Redis键\n @param hKey  Hash键\n @param value 值\n"}, {"name": "getCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取Hash中的数据\n\n @param key  Redis键\n @param hKey Hash键\n @return Hash中的对象\n"}, {"name": "delCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除Hash中的数据\n\n @param key  Redis键\n @param hKey Hash键\n @return Hash中的对象\n"}, {"name": "delMultiCacheMapValue", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": " 删除Hash中的数据\n\n @param key   Redis键\n @param hKeys Hash键\n"}, {"name": "getMultiCacheMapValue", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": " 获取多个Hash中的数据\n\n @param key   Redis键\n @param hKeys Hash键集合\n @return Hash对象集合\n"}, {"name": "setAtomicValue", "paramTypes": ["java.lang.String", "long"], "doc": " 设置原子值\n\n @param key   Redis键\n @param value 值\n"}, {"name": "getAtomicValue", "paramTypes": ["java.lang.String"], "doc": " 获取原子值\n\n @param key Redis键\n @return 当前值\n"}, {"name": "incrAtomicValue", "paramTypes": ["java.lang.String"], "doc": " 递增原子值\n\n @param key Redis键\n @return 当前值\n"}, {"name": "decrAtomicValue", "paramTypes": ["java.lang.String"], "doc": " 递减原子值\n\n @param key Redis键\n @return 当前值\n"}, {"name": "keys", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的基本对象列表(全局匹配忽略租户 自行拼接租户id)\n <P>\n limit-设置扫描的限制数量(默认为0,查询全部)\n pattern-设置键的匹配模式(默认为null)\n chunkSize-设置每次扫描的块大小(默认为0,本方法设置为1000)\n type-设置键的类型(默认为null,查询全部类型)\n </P>\n @see KeysScanOptions\n @param pattern 字符串前缀\n @return 对象列表\n"}, {"name": "keys", "paramTypes": ["org.redisson.api.options.KeysScanOptions"], "doc": " 通过扫描参数获取缓存的基本对象列表\n @param keysScanOptions 扫描参数\n <P>\n limit-设置扫描的限制数量(默认为0,查询全部)\n pattern-设置键的匹配模式(默认为null)\n chunkSize-设置每次扫描的块大小(默认为0)\n type-设置键的类型(默认为null,查询全部类型)\n </P>\n @see KeysScanOptions\n"}, {"name": "deleteKeys", "paramTypes": ["java.lang.String"], "doc": " 删除缓存的基本对象列表(全局匹配忽略租户 自行拼接租户id)\n\n @param pattern 字符串前缀\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 检查redis中是否存在key\n\n @param key 键\n"}], "constructors": []}