{"doc": " 用户服务\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserInfo", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 通过用户名查询用户信息\n\n @param username 用户名\n @param tenantId 租户id\n @return 结果\n"}, {"name": "getUserInfo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 通过用户id查询用户信息\n\n @param userId   用户id\n @param tenantId 租户id\n @return 结果\n"}, {"name": "getUserInfoByPhonenumber", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 通过手机号查询用户信息\n\n @param phonenumber 手机号\n @param tenantId    租户id\n @return 结果\n"}, {"name": "getUserInfoByEmail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 通过邮箱查询用户信息\n\n @param email    邮箱\n @param tenantId 租户id\n @return 结果\n"}, {"name": "getUserInfoByOpenid", "paramTypes": ["java.lang.String"], "doc": " 通过openid查询用户信息\n\n @param openid openid\n @return 结果\n"}, {"name": "registerUserInfo", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteUserBo"], "doc": " 注册用户信息\n\n @param remoteUserBo 用户信息\n @return 结果\n"}, {"name": "selectUserNameById", "paramTypes": ["java.lang.Long"], "doc": " 通过userId查询用户账户\n\n @param userId 用户id\n @return 结果\n"}, {"name": "selectNicknameById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户昵称\n\n @param userId 用户id\n @return 结果\n"}, {"name": "selectNicknameByIds", "paramTypes": ["java.lang.String"], "doc": " 通过用户ID查询用户账户\n\n @param userIds 用户ID 多个用逗号隔开\n @return 用户名称\n"}, {"name": "selectPhonenumberById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户手机号\n\n @param userId 用户id\n @return 用户手机号\n"}, {"name": "selectEmailById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户邮箱\n\n @param userId 用户id\n @return 用户邮箱\n"}, {"name": "recordLoginInfo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新用户信息\n\n @param userId 用户ID\n @param ip     IP地址\n"}, {"name": "selectListByIds", "paramTypes": ["java.util.List"], "doc": " 通过用户ID查询用户列表\n\n @param userIds 用户ids\n @return 用户列表\n"}, {"name": "selectUserIdsByRoleIds", "paramTypes": ["java.util.List"], "doc": " 通过角色ID查询用户ID\n\n @param roleIds 角色ids\n @return 用户ids\n"}, {"name": "selectUsersByRoleIds", "paramTypes": ["java.util.List"], "doc": " 通过角色ID查询用户\n\n @param roleIds 角色ids\n @return 用户\n"}, {"name": "selectUsersByDeptIds", "paramTypes": ["java.util.List"], "doc": " 通过部门ID查询用户\n\n @param deptIds 部门ids\n @return 用户\n"}, {"name": "selectUsersByPostIds", "paramTypes": ["java.util.List"], "doc": " 通过岗位ID查询用户\n\n @param postIds 岗位ids\n @return 用户\n"}, {"name": "selectUserNamesByIds", "paramTypes": ["java.util.List"], "doc": " 根据用户 ID 列表查询用户名称映射关系\n\n @param userIds 用户 ID 列表\n @return Map，其中 key 为用户 ID，value 为对应的用户名称\n"}, {"name": "selectRoleNamesByIds", "paramTypes": ["java.util.List"], "doc": " 根据角色 ID 列表查询角色名称映射关系\n\n @param roleIds 角色 ID 列表\n @return Map，其中 key 为角色 ID，value 为对应的角色名称\n"}, {"name": "selectDeptNamesByIds", "paramTypes": ["java.util.List"], "doc": " 根据部门 ID 列表查询部门名称映射关系\n\n @param deptIds 部门 ID 列表\n @return Map，其中 key 为部门 ID，value 为对应的部门名称\n"}, {"name": "selectPostNamesByIds", "paramTypes": ["java.util.List"], "doc": " 根据岗位 ID 列表查询岗位名称映射关系\n\n @param postIds 岗位 ID 列表\n @return Map，其中 key 为岗位 ID，value 为对应的岗位名称\n"}], "constructors": []}