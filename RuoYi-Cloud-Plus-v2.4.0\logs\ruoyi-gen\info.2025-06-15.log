2025-06-15 21:13:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:13:21 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 50268 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:13:21 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-15 21:13:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 21:13:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:13:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:13:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:13:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:13:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 21:13:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d58136
2025-06-15 21:13:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 21:13:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 21:13:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 21:13:28 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:13:42 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:13:42 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:13:42 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:13:42 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:13:42 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6fff46bf
2025-06-15 21:13:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 21:13:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 21:13:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 21:13:49 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 21:13:49 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-15 21:13:52 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 34.102 seconds (process running for 34.852)
2025-06-15 21:13:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:13:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:13:52 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-15 21:13:52 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:16:02 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:16:02 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[30ms]
2025-06-15 21:24:23 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:23 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][执行耗时:00:00:00.006]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][封装耗时:00:00:00.010][封装行数:27]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
param2=BASE TABLE(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868348-00791773][thread:689][ds:default-master][tables][catalog:null][schema:SCHEMA:ry-cloud][pattern:wms_warehouse][type:1][result:1][执行耗时:00:00:00.097]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][action:select][执行耗时:00:00:00.006]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][action:select][封装耗时:00:00:00.002][封装行数:14]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:14][根据metadata解析:0][根据系统表查询:14][根据驱动内置接口补充:0][执行耗时:00:00:00.020]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868450-68955563][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:14][根据metadata解析:0][根据系统表查询:14][根据根据驱动内置接口补充:0][执行耗时:00:00:00.020]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868472-25703517][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.STATISTICS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY SEQ_IN_INDEX ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868472-25703517][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868472-25703517][thread:689][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE (CONSTRAINT_SCHEMA = ? AND TABLE_NAME LIKE ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868479-11362515][thread:689][ds:default-master][constraints][result:1][执行耗时:00:00:00.005]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][action:select][cmd:
show create table `ry-cloud`.wms_warehouse
]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868484-27609371][thread:689][ds:default-master][table ddl][table:wms_warehouse][result:1][执行耗时:00:00:00.003]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][cmd:
SHOW CREATE TABLE `ry-cloud`.wms_warehouse
]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.PARTITIONS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993868487-08705931][thread:689][ds:default-master][partition][table:wms_warehouse][result:true][执行耗时:00:00:00.008]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:28 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-15 21:24:33 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:33 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][执行耗时:00:00:00.004]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][封装耗时:00:00:00.002][封装行数:27]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ? AND TABLE_TYPE = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
param2=BASE TABLE(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904574-73309527][thread:689][ds:default-master][tables][catalog:null][schema:SCHEMA:ry-cloud][pattern:wms_warehouse][type:1][result:1][执行耗时:00:00:00.013]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:13]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:13][根据metadata解析:0][根据系统表查询:13][根据驱动内置接口补充:0][执行耗时:00:00:00.007]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904587-00795736][thread:689][ds:default-master][columns][catalog:null][schema:SCHEMA:ry-cloud][table:TABLE:ry-cloud.wms_warehouse][total:13][根据metadata解析:0][根据系统表查询:13][根据根据驱动内置接口补充:0][执行耗时:00:00:00.007]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904595-67913753][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.STATISTICS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY SEQ_IN_INDEX ASC
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904595-67913753][thread:689][ds:default-master][action:select][执行耗时:00:00:00.003]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904595-67913753][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE (CONSTRAINT_SCHEMA = ? AND TABLE_NAME LIKE ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904599-13927265][thread:689][ds:default-master][constraints][result:1][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][action:select][cmd:
show create table `ry-cloud`.wms_warehouse
]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904603-62685121][thread:689][ds:default-master][table ddl][table:wms_warehouse][result:1][执行耗时:00:00:00.003]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][cmd:
SHOW CREATE TABLE `ry-cloud`.wms_warehouse
]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][执行耗时:00:00:00.001]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.PARTITIONS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME = ?)
]
[param:
param0=ry-cloud(java.lang.String)
param1=wms_warehouse(java.lang.String)
];
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][执行耗时:00:00:00.002]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1749993904606-88386392][thread:689][ds:default-master][partition][table:wms_warehouse][result:true][执行耗时:00:00:00.005]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:25:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:25:30 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:25:30 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-15 21:26:01 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:26:01 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-06-15 21:26:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:26:04 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:27:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:27:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:27:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:27:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 21:28:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 21:29:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:29:28 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 50072 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:29:28 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-15 21:29:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 21:29:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:29:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:29:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:29:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:29:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 21:29:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e5c04a4
2025-06-15 21:29:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 21:29:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 21:29:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 21:29:36 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:29:50 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:29:50 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:29:51 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:29:51 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:29:51 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6fff46bf
2025-06-15 21:29:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 21:29:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 21:29:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 21:29:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 21:29:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-15 21:30:02 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:30:03 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 37.594 seconds (process running for 38.169)
2025-06-15 21:30:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:30:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:30:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-15 21:30:17 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:30:18 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[128ms]
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:07 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:33:52 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 22:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:45:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:45:50 [main] INFO  org.dromara.gen.RuoYiGenApplication - Starting RuoYiGenApplication using Java 17.0.14 with PID 35696 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:45:50 [main] INFO  org.dromara.gen.RuoYiGenApplication - The following 1 profile is active: "dev"
2025-06-15 22:45:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gen.yml, group=DEFAULT_GROUP] success
2025-06-15 22:45:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:45:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:45:55 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:45:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:45:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 22:45:56 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7266f136
2025-06-15 22:45:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 22:45:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 22:45:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 22:45:57 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:46:11 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:46:11 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:46:11 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:46:11 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:46:11 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@58658f63
2025-06-15 22:46:17 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 22:46:17 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 22:46:17 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 22:46:17 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 22:46:17 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gen *************:9202 register finished
2025-06-15 22:46:21 [main] INFO  org.dromara.gen.RuoYiGenApplication - Started RuoYiGenApplication in 34.132 seconds (process running for 34.762)
2025-06-15 22:46:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:46:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 22:46:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gen.yml, group=DEFAULT_GROUP
2025-06-15 22:46:21 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
