{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/pickAttrs.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nconst attributes = `accept acceptcharset accesskey action allowfullscreen allowtransparency\nalt async autocomplete autofocus autoplay capture cellpadding cellspacing challenge\ncharset checked classid classname colspan cols content contenteditable contextmenu\ncontrols coords crossorigin data datetime default defer dir disabled download draggable\nenctype form formaction formenctype formmethod formnovalidate formtarget frameborder\nheaders height hidden high href hreflang htmlfor for httpequiv icon id inputmode integrity\nis keyparams keytype kind label lang list loop low manifest marginheight marginwidth max maxlength media\nmediagroup method min minlength multiple muted name novalidate nonce open\noptimum pattern placeholder poster preload radiogroup readonly rel required\nreversed role rowspan rows sandbox scope scoped scrolling seamless selected\nshape size sizes span spellcheck src srcdoc srclang srcset start step style\nsummary tabindex target title type usemap value width wmode wrap`;\nconst eventsName = `onCopy onCut onPaste onCompositionend onCompositionstart onCompositionupdate onKeydown\n    onKeypress onKeyup onFocus onBlur onChange onInput onSubmit onClick onContextmenu onDoubleclick onDblclick\n    onDrag onDragend onDragenter onDragexit onDragleave onDragover onDragstart onDrop onMousedown\n    onMouseenter onMouseleave onMousemove onMouseout onMouseover onMouseup onSelect onTouchcancel\n    onTouchend onTouchmove onTouchstart onTouchstartPassive onTouchmovePassive onScroll onWheel onAbort onCanplay onCanplaythrough\n    onDurationchange onEmptied onEncrypted onEnded onError onLoadeddata onLoadedmetadata\n    onLoadstart onPause onPlay onPlaying onProgress onRatechange onSeeked onSeeking onStalled onSuspend onTimeupdate onVolumechange onWaiting onLoad onError`;\nconst propList = `${attributes} ${eventsName}`.split(/[\\s\\n]+/);\n/* eslint-enable max-len */\nconst ariaPrefix = 'aria-';\nconst dataPrefix = 'data-';\nfunction match(key, prefix) {\n  return key.indexOf(prefix) === 0;\n}\n/**\n * Picker props from exist props with filter\n * @param props Passed props\n * @param ariaOnly boolean | { aria?: boolean; data?: boolean; attr?: boolean; } filter config\n */\nexport default function pickAttrs(props) {\n  let ariaOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  let mergedConfig;\n  if (ariaOnly === false) {\n    mergedConfig = {\n      aria: true,\n      data: true,\n      attr: true\n    };\n  } else if (ariaOnly === true) {\n    mergedConfig = {\n      aria: true\n    };\n  } else {\n    mergedConfig = _extends({}, ariaOnly);\n  }\n  const attrs = {};\n  Object.keys(props).forEach(key => {\n    if (\n    // Aria\n    mergedConfig.aria && (key === 'role' || match(key, ariaPrefix)) ||\n    // Data\n    mergedConfig.data && match(key, dataPrefix) ||\n    // Attr\n    mergedConfig.attr && (propList.includes(key) || propList.includes(key.toLowerCase()))) {\n      attrs[key] = props[key];\n    }\n  });\n  return attrs;\n}"], "mappings": ";;;;;AACA,IAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYnB,IAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOnB,IAAM,WAAW,GAAG,UAAU,IAAI,UAAU,GAAG,MAAM,SAAS;AAE9D,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,SAAS,MAAM,KAAK,QAAQ;AAC1B,SAAO,IAAI,QAAQ,MAAM,MAAM;AACjC;AAMe,SAAR,UAA2B,OAAO;AACvC,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI;AACJ,MAAI,aAAa,OAAO;AACtB,mBAAe;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF,WAAW,aAAa,MAAM;AAC5B,mBAAe;AAAA,MACb,MAAM;AAAA,IACR;AAAA,EACF,OAAO;AACL,mBAAe,SAAS,CAAC,GAAG,QAAQ;AAAA,EACtC;AACA,QAAM,QAAQ,CAAC;AACf,SAAO,KAAK,KAAK,EAAE,QAAQ,SAAO;AAChC;AAAA;AAAA,MAEA,aAAa,SAAS,QAAQ,UAAU,MAAM,KAAK,UAAU;AAAA,MAE7D,aAAa,QAAQ,MAAM,KAAK,UAAU;AAAA,MAE1C,aAAa,SAAS,SAAS,SAAS,GAAG,KAAK,SAAS,SAAS,IAAI,YAAY,CAAC;AAAA,MAAI;AACrF,YAAM,GAAG,IAAI,MAAM,GAAG;AAAA,IACxB;AAAA,EACF,CAAC;AACD,SAAO;AACT;", "names": []}