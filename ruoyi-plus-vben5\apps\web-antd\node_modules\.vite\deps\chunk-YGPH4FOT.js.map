{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/vue-types/index.js"], "sourcesContent": ["import { createTypes } from 'vue-types';\nconst PropTypes = createTypes({\n  func: undefined,\n  bool: undefined,\n  string: undefined,\n  number: undefined,\n  array: undefined,\n  object: undefined,\n  integer: undefined\n});\nPropTypes.extend([{\n  name: 'looseBool',\n  getter: true,\n  type: Boolean,\n  default: undefined\n}, {\n  name: 'style',\n  getter: true,\n  type: [String, Object],\n  default: undefined\n}, {\n  name: 'VueNode',\n  getter: true,\n  type: null\n}]);\nexport function withUndefined(type) {\n  type.default = undefined;\n  return type;\n}\nexport default PropTypes;"], "mappings": ";;;;;AACA,IAAM,YAAY,EAAY;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACX,CAAC;AACD,UAAU,OAAO,CAAC;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM,CAAC,QAAQ,MAAM;AAAA,EACrB,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AACR,CAAC,CAAC;AACK,SAAS,cAAc,MAAM;AAClC,OAAK,UAAU;AACf,SAAO;AACT;AACA,IAAO,oBAAQ;", "names": []}