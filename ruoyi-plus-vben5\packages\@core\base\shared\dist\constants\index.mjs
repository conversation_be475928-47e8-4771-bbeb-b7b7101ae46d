const DictEnum = {
  SYS_COMMON_STATUS: "sys_common_status",
  SYS_DEVICE_TYPE: "sys_device_type",
  // 设备类型
  SYS_GRANT_TYPE: "sys_grant_type",
  // 授权类型
  SYS_NORMAL_DISABLE: "sys_normal_disable",
  SYS_NOTICE_STATUS: "sys_notice_status",
  // 通知状态
  SYS_NOTICE_TYPE: "sys_notice_type",
  // 通知类型
  SYS_OPER_TYPE: "sys_oper_type",
  // 操作类型
  SYS_OSS_ACCESS_POLICY: "oss_access_policy",
  // oss权限桶类型
  SYS_SHOW_HIDE: "sys_show_hide",
  // 显示状态
  SYS_USER_SEX: "sys_user_sex",
  // 性别
  SYS_YES_NO: "sys_yes_no",
  // 是否
  WF_BUSINESS_STATUS: "wf_business_status",
  // 业务状态
  WF_FORM_TYPE: "wf_form_type",
  // 表单类型
  WF_TASK_STATUS: "wf_task_status"
  // 任务状态
};

const CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT = `--vben-content-height`;
const CSS_VARIABLE_LAYOUT_CONTENT_WIDTH = `--vben-content-width`;
const CSS_VARIABLE_LAYOUT_HEADER_HEIGHT = `--vben-header-height`;
const CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT = `--vben-footer-height`;
const ELEMENT_ID_MAIN_CONTENT = `__vben_main_content`;
const DEFAULT_NAMESPACE = "vben";

const VBEN_GITHUB_URL = "https://github.com/vbenjs/vue-vben-admin";
const VBEN_DOC_URL = "https://doc.vben.pro";
const VBEN_LOGO_URL = "https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp";
const VBEN_PREVIEW_URL = "https://www.vben.pro";
const VBEN_ELE_PREVIEW_URL = "https://ele.vben.pro";
const VBEN_NAIVE_PREVIEW_URL = "https://naive.vben.pro";
const VBEN_ANT_PREVIEW_URL = "https://ant.vben.pro";

export { CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT, CSS_VARIABLE_LAYOUT_CONTENT_WIDTH, CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT, CSS_VARIABLE_LAYOUT_HEADER_HEIGHT, DEFAULT_NAMESPACE, DictEnum, ELEMENT_ID_MAIN_CONTENT, VBEN_ANT_PREVIEW_URL, VBEN_DOC_URL, VBEN_ELE_PREVIEW_URL, VBEN_GITHUB_URL, VBEN_LOGO_URL, VBEN_NAIVE_PREVIEW_URL, VBEN_PREVIEW_URL };
