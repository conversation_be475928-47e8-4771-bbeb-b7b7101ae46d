import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-mdi@1.2.48/node_modules/@iconify/icons-mdi/workflow-outline.js
var require_workflow_outline = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-mdi@1.2.48/node_modules/@iconify/icons-mdi/workflow-outline.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="currentColor" d="M21 16v-3c0-1.11-.89-2-2-2h-6V8h2V2H9v6h2v3H5c-1.11 0-2 .89-2 2v3H1v6h6v-6H5v-3h6v3H9v6h6v-6h-2v-3h6v3h-2v6h6v-6h-2M11 4h2v2h-2V4M5 20H3v-2h2v2m8 0h-2v-2h2v2m8 0h-2v-2h2v2Z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_workflow_outline();
//# sourceMappingURL=@iconify_icons-mdi_workflow-outline.js.map
