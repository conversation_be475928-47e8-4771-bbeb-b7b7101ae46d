package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysOperLogToSysOperLogVoMapper__5.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__5 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
