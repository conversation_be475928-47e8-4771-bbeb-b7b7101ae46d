import{T as n,_ as o}from"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import{d as l,h as r,f as i,o as e,b as c,w as m,c as p,F as _,K as u}from"../jse/index-index-C-MnMZEz.js";const x=l({__name:"approval-timeline",props:{list:{}},setup(s){const t=s;return(f,d)=>t.list.length>0?(e(),r(c(n),{key:0},{default:m(()=>[(e(!0),p(_,null,u(t.list,a=>(e(),r(o,{key:a.id,item:a},null,8,["item"]))),128))]),_:1})):i("",!0)}});export{x as _};
