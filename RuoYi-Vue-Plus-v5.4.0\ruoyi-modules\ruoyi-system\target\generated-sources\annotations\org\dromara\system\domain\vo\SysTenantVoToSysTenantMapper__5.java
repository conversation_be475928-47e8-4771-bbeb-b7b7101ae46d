package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenant;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysTenantToSysTenantVoMapper__5.class},
    imports = {}
)
public interface SysTenantVoToSysTenantMapper__5 extends BaseMapper<SysTenantVo, SysTenant> {
}
