# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity += params.quantity;
      inventory.availableQty += params.quantity;
      inventory.totalCost = (inventory.totalCost + params.quantity * params.unitCost);
      inventory.unitCost = inventory.totalCost / inventory.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      // 3. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.INBOUND,
        quantity: params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 4. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存出库
   */
  async outbound(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 获取库存记录
      const inventory = await this.getInventory(params.inventoryId, trx);
      if (!inventory) {
        throw new Error('库存记录不存在');
      }
      
      // 2. 检查可用库存
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      // 3. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity -= params.quantity;
      inventory.availableQty -= params.quantity;
      
      // 重新计算单位成本(移动平均法)
      if (inventory.quantity > 0) {
        inventory.totalCost = inventory.quantity * inventory.unitCost;
      } else {
        inventory.totalCost = 0;
      }
      
      inventory.lastUpdateTime = new Date();
      await this.updateInventory(inventory, trx);
      
      // 4. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.OUTBOUND,
        quantity: -params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 5. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存分配
   */
  async allocate(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      const inventory = await this.getInventory(params.inventoryId, trx);
      
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      inventory.availableQty -= params.quantity;
      inventory.allocatedQty += params.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.ALLOCATE,
        quantity: params.quantity,
        beforeQty: inventory.availableQty + params.quantity,
        afterQty: inventory.availableQty,
        sourceDocumentType: 'ALLOCATION',
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
    });
  }
  
  /**
   * 库存冻结
   */
  async freeze(params: {
    inventoryId: string;
    quantity: number;
    reason: string;
    operatorId: string;
  }): Promise<void> {
    // 类似实现...
  }
  
  private async executeTransaction<T>(
    callback: (trx: Transaction) => Promise<T>
  ): Promise<T> {
    const trx = await this.db.beginTransaction();
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
```

### 4. 入库管理模块

#### 入库流程状态机
```typescript
// 入库流程状态定义
enum InboundStatus {
  PLANNED = 'PLANNED',         // 计划中
  CONFIRMED = 'CONFIRMED',     // 已确认
  IN_TRANSIT = 'IN_TRANSIT',   // 在途
  ARRIVED = 'ARRIVED',         // 已到达
  RECEIVING = 'RECEIVING',     // 收货中
  QC_PENDING = 'QC_PENDING',   // 待质检
  QC_PASSED = 'QC_PASSED',     // 质检通过
  QC_FAILED = 'QC_FAILED',     // 质检不通过
  PUTAWAY = 'PUTAWAY',         // 上架中
  COMPLETED = 'COMPLETED',     // 已完成
  CANCELLED = 'CANCELLED'      // 已取消
}

// 状态转换规则
const InboundStatusTransitions: Record<InboundStatus, InboundStatus[]> = {
  [InboundStatus.PLANNED]: [InboundStatus.CONFIRMED, InboundStatus.CANCELLED],
  [InboundStatus.CONFIRMED]: [InboundStatus.IN_TRANSIT, InboundStatus.CANCELLED],
  [InboundStatus.IN_TRANSIT]: [InboundStatus.ARRIVED, InboundStatus.CANCELLED],
  [InboundStatus.ARRIVED]: [InboundStatus.RECEIVING],
  [InboundStatus.RECEIVING]: [InboundStatus.QC_PENDING, InboundStatus.COMPLETED],
  [InboundStatus.QC_PENDING]: [InboundStatus.QC_PASSED, InboundStatus.QC_FAILED],
  [InboundStatus.QC_PASSED]: [InboundStatus.PUTAWAY],
  [InboundStatus.QC_FAILED]: [InboundStatus.CANCELLED],
  [InboundStatus.PUTAWAY]: [InboundStatus.COMPLETED],
  [InboundStatus.COMPLETED]: [],
  [InboundStatus.CANCELLED]: []
};

// 入库流程管理器
class InboundFlowManager {
  async updateStatus(
    inboundId: string, 
    newStatus: InboundStatus,
    operatorId: string,
    remark?: string
  ): Promise<void> {
    const inbound = await this.getInbound(inboundId);
    const currentStatus = inbound.status as InboundStatus;
    
    // 验证状态转换是否合法
    if (!InboundStatusTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不能从${currentStatus}转换到${newStatus}`);
    }
    
    // 执行状态转换
    await this.executeStatusChange(inbound, newStatus, operatorId, remark);
    
    // 触发相关业务逻辑
    await this.triggerStatusActions(inbound, newStatus);
  }
  
  private async triggerStatusActions(
    inbound: InboundOrder, 
    status: InboundStatus
  ): Promise<void> {
    switch (status) {
      case InboundStatus.CONFIRMED:
        // 发送确认通知
        await this.notifySupplier(inbound);
        break;
        
      case InboundStatus.ARRIVED:
        // 生成收货任务
        await this.generateReceivingTasks(inbound);
        break;
        
      case InboundStatus.QC_PASSED:
        // 生成上架任务
        await this.generatePutawayTasks(inbound);
        break;
        
      case InboundStatus.COMPLETED:
        // 更新库存
        await this.updateInventory(inbound);
        // 发送完成通知
        await this.notifyCompletion(inbound);
        break;
    }
  }
}
```

#### 移动端收货界面
```vue
<!-- 移动端收货界面 -->
<template>
  <div class="mobile-receiving">
    <!-- 顶部导航 -->
    <div class="top-bar">
      <van-nav-bar
        title="收货作业"
        left-text="返回"
        @click-left="goBack"
      />
    </div>
    
    <!-- 入库单信息 -->
    <div class="inbound-info">
      <van-cell-group>
        <van-cell title="入库单号" :value="inbound.inboundNumber" />
        <van-cell title="供应商" :value="inbound.supplierName" />
        <van-cell title="计划日期" :value="formatDate(inbound.planDate)" />
      </van-cell-group>
    </div>
    
    <!-- 物料列表 -->
    <div class="material-list">
      <div 
        v-for="item in inbound.items" 
        :key="item.itemId"
        class="material-item"
        :class="{ 'completed': item.receivedQty >= item.planQty }"
      >
        <div class="material-info">
          <div class="material-code">{{ item.materialCode }}</div>
          <div class="material-name">{{ item.materialName }}</div>
          <div class="quantity-info">
            <span class="received">{{ item.receivedQty }}</span>
            /
            <span class="planned">{{ item.planQty }}</span>
            {{ item.unit }}
          </div>
        </div>
        
        <div class="actions">
          <van-button 
            type="primary" 
            size="small"
            @click="startReceiving(item)"
            :disabled="item.receivedQty >= item.planQty"
          >
            收货
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 扫码收货弹窗 -->
    <van-popup v-model:show="showReceivingModal" position="bottom" :style="{ height: '70%' }">
      <div class="receiving-modal">
        <div class="modal-header">
          <h3>收货 - {{ currentItem?.materialName }}</h3>
          <van-icon name="cross" @click="closeReceivingModal" />
        </div>
        
        <div class="scanner-container">
          <div class="camera-view" ref="cameraRef">
            <!-- 相机预览区域 -->
          </div>
          <div class="scan-overlay">
            <div class="scan-frame"></div>
            <div class="scan-tip">请扫描物料条码</div>
          </div>
        </div>
        
        <div class="manual-input">
          <van-field
            v-model="manualBarcode"
            label="条码"
            placeholder="扫码或手动输入"
            @blur="validateBarcode"
          />
          <van-field
            v-model="receiveQuantity"
            type="number"
            label="收货数量"
            placeholder="请输入收货数量"
          />
          <van-field
            v-model="lotNumber"
            label="批次号"
            placeholder="请输入批次号"
          />
        </div>
        
        <div class="modal-actions">
          <van-button block type="primary" @click="confirmReceiving">
            确认收货
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BarcodeScanner } from '@/utils/barcode-scanner';

const inbound = ref<InboundOrder>({} as InboundOrder);
const currentItem = ref<InboundItem | null>(null);
const showReceivingModal = ref(false);
const cameraRef = ref<HTMLElement>();
const scanner = ref<BarcodeScanner>();

// 表单数据
const manualBarcode = ref('');
const receiveQuantity = ref<number>(0);
const lotNumber = ref('');

onMounted(async () => {
  await loadInboundData();
  initializeScanner();
});

onUnmounted(() => {
  scanner.value?.destroy();
});

async function loadInboundData() {
  const inboundId = route.params.id as string;
  inbound.value = await inboundAPI.info(inboundId);
}

function initializeScanner() {
  if (cameraRef.value) {
    scanner.value = new BarcodeScanner(cameraRef.value);
    scanner.value.onScan = handleBarcodeScan;
  }
}

function startReceiving(item: InboundItem) {
  currentItem.value = item;
  receiveQuantity.value = item.planQty - item.receivedQty;
  showReceivingModal.value = true;
  
  // 启动扫码
  scanner.value?.start();
}

function handleBarcodeScan(barcode: string) {
  manualBarcode.value = barcode;
  validateBarcode();
}

async function validateBarcode() {
  if (!manualBarcode.value || !currentItem.value) return;
  
  try {
    const materialInfo = await materialAPI.getByBarcode(manualBarcode.value);
    if (materialInfo.materialId !== currentItem.value.materialId) {
      showToast('条码与物料不匹配');
      manualBarcode.value = '';
      return;
    }
    
    // 自动填充批次号（如果条码包含批次信息）
    if (materialInfo.isLotManaged && materialInfo.lotNumber) {
      lotNumber.value = materialInfo.lotNumber;
    }
  } catch (error) {
    showToast('条码验证失败');
    manualBarcode.value = '';
  }
}

async function confirmReceiving() {
  if (!currentItem.value) return;
  
  try {
    await inboundAPI.receive({
      inboundId: inbound.value.inboundId,
      itemId: currentItem.value.itemId,
      barcode: manualBarcode.value,
      quantity: receiveQuantity.value,
      lotNumber: lotNumber.value
    });
    
    // 更新本地数据
    currentItem.value.receivedQty += receiveQuantity.value;
    
    showToast('收货成功');
    closeReceivingModal();
    
    // 检查是否完成所有收货
    const allCompleted = inbound.value.items.every(
      item => item.receivedQty >= item.planQty
    );
    if (allCompleted) {
      await completeReceiving();
    }
  } catch (error) {
    showToast('收货失败');
  }
}

function closeReceivingModal() {
  showReceivingModal.value = false;
  scanner.value?.stop();
  manualBarcode.value = '';
  receiveQuantity.value = 0;
  lotNumber.value = '';
}
</script>
```

这份技术实现方案提供了：

1. **系统架构设计** - 整体技术架构和技术栈选择
2. **模块详细设计** - 每个功能模块的具体实现方案
3. **数据库设计** - 完整的表结构和索引设计
4. **API接口设计** - RESTful API规范和接口定义
5. **移动端实现** - PDA收货界面的完整实现
6. **状态管理** - 业务流程的状态机设计
7. **库存算法** - 库存更新的核心算法逻辑

现在让我调用MCP模块获取您的反馈：

<function_calls>
<invoke name="mcp_mcp-feedback-enhanced_interactive_feedback">
<parameter name="project_directory">.
</code_block_to_apply_changes_from>
</edit_instructions>


Assistant:<rewritten_file>
```
# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity += params.quantity;
      inventory.availableQty += params.quantity;
      inventory.totalCost = (inventory.totalCost + params.quantity * params.unitCost);
      inventory.unitCost = inventory.totalCost / inventory.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      // 3. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.INBOUND,
        quantity: params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 4. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存出库
   */
  async outbound(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 获取库存记录
      const inventory = await this.getInventory(params.inventoryId, trx);
      if (!inventory) {
        throw new Error('库存记录不存在');
      }
      
      // 2. 检查可用库存
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      // 3. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity -= params.quantity;
      inventory.availableQty -= params.quantity;
      
      // 重新计算单位成本(移动平均法)
      if (inventory.quantity > 0) {
        inventory.totalCost = inventory.quantity * inventory.unitCost;
      } else {
        inventory.totalCost = 0;
      }
      
      inventory.lastUpdateTime = new Date();
      await this.updateInventory(inventory, trx);
      
      // 4. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.OUTBOUND,
        quantity: -params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 5. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存分配
   */
  async allocate(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      const inventory = await this.getInventory(params.inventoryId, trx);
      
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      inventory.availableQty -= params.quantity;
      inventory.allocatedQty += params.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.ALLOCATE,
        quantity: params.quantity,
        beforeQty: inventory.availableQty + params.quantity,
        afterQty: inventory.availableQty,
        sourceDocumentType: 'ALLOCATION',
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
    });
  }
  
  /**
   * 库存冻结
   */
  async freeze(params: {
    inventoryId: string;
    quantity: number;
    reason: string;
    operatorId: string;
  }): Promise<void> {
    // 类似实现...
  }
  
  private async executeTransaction<T>(
    callback: (trx: Transaction) => Promise<T>
  ): Promise<T> {
    const trx = await this.db.beginTransaction();
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
```

### 4. 入库管理模块

#### 入库流程状态机
```typescript
// 入库流程状态定义
enum InboundStatus {
  PLANNED = 'PLANNED',         // 计划中
  CONFIRMED = 'CONFIRMED',     // 已确认
  IN_TRANSIT = 'IN_TRANSIT',   // 在途
  ARRIVED = 'ARRIVED',         // 已到达
  RECEIVING = 'RECEIVING',     // 收货中
  QC_PENDING = 'QC_PENDING',   // 待质检
  QC_PASSED = 'QC_PASSED',     // 质检通过
  QC_FAILED = 'QC_FAILED',     // 质检不通过
  PUTAWAY = 'PUTAWAY',         // 上架中
  COMPLETED = 'COMPLETED',     // 已完成
  CANCELLED = 'CANCELLED'      // 已取消
}

// 状态转换规则
const InboundStatusTransitions: Record<InboundStatus, InboundStatus[]> = {
  [InboundStatus.PLANNED]: [InboundStatus.CONFIRMED, InboundStatus.CANCELLED],
  [InboundStatus.CONFIRMED]: [InboundStatus.IN_TRANSIT, InboundStatus.CANCELLED],
  [InboundStatus.IN_TRANSIT]: [InboundStatus.ARRIVED, InboundStatus.CANCELLED],
  [InboundStatus.ARRIVED]: [InboundStatus.RECEIVING],
  [InboundStatus.RECEIVING]: [InboundStatus.QC_PENDING, InboundStatus.COMPLETED],
  [InboundStatus.QC_PENDING]: [InboundStatus.QC_PASSED, InboundStatus.QC_FAILED],
  [InboundStatus.QC_PASSED]: [InboundStatus.PUTAWAY],
  [InboundStatus.QC_FAILED]: [InboundStatus.CANCELLED],
  [InboundStatus.PUTAWAY]: [InboundStatus.COMPLETED],
  [InboundStatus.COMPLETED]: [],
  [InboundStatus.CANCELLED]: []
};

// 入库流程管理器
class InboundFlowManager {
  async updateStatus(
    inboundId: string, 
    newStatus: InboundStatus,
    operatorId: string,
    remark?: string
  ): Promise<void> {
    const inbound = await this.getInbound(inboundId);
    const currentStatus = inbound.status as InboundStatus;
    
    // 验证状态转换是否合法
    if (!InboundStatusTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不能从${currentStatus}转换到${newStatus}`);
    }
    
    // 执行状态转换
    await this.executeStatusChange(inbound, newStatus, operatorId, remark);
    
    // 触发相关业务逻辑
    await this.triggerStatusActions(inbound, newStatus);
  }
  
  private async triggerStatusActions(
    inbound: InboundOrder, 
    status: InboundStatus
  ): Promise<void> {
    switch (status) {
      case InboundStatus.CONFIRMED:
        // 发送确认通知
        await this.notifySupplier(inbound);
        break;
        
      case InboundStatus.ARRIVED:
        // 生成收货任务
        await this.generateReceivingTasks(inbound);
        break;
        
      case InboundStatus.QC_PASSED:
        // 生成上架任务
        await this.generatePutawayTasks(inbound);
        break;
        
      case InboundStatus.COMPLETED:
        // 更新库存
        await this.updateInventory(inbound);
        // 发送完成通知
        await this.notifyCompletion(inbound);
        break;
    }
  }
}
```

#### 移动端收货界面
```vue
<!-- 移动端收货界面 -->
<template>
  <div class="mobile-receiving">
    <!-- 顶部导航 -->
    <div class="top-bar">
      <van-nav-bar
        title="收货作业"
        left-text="返回"
        @click-left="goBack"
      />
    </div>
    
    <!-- 入库单信息 -->
    <div class="inbound-info">
      <van-cell-group>
        <van-cell title="入库单号" :value="inbound.inboundNumber" />
        <van-cell title="供应商" :value="inbound.supplierName" />
        <van-cell title="计划日期" :value="formatDate(inbound.planDate)" />
      </van-cell-group>
    </div>
    
    <!-- 物料列表 -->
    <div class="material-list">
      <div 
        v-for="item in inbound.items" 
        :key="item.itemId"
        class="material-item"
        :class="{ 'completed': item.receivedQty >= item.planQty }"
      >
        <div class="material-info">
          <div class="material-code">{{ item.materialCode }}</div>
          <div class="material-name">{{ item.materialName }}</div>
          <div class="quantity-info">
            <span class="received">{{ item.receivedQty }}</span>
            /
            <span class="planned">{{ item.planQty }}</span>
            {{ item.unit }}
          </div>
        </div>
        
        <div class="actions">
          <van-button 
            type="primary" 
            size="small"
            @click="startReceiving(item)"
            :disabled="item.receivedQty >= item.planQty"
          >
            收货
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 扫码收货弹窗 -->
    <van-popup v-model:show="showReceivingModal" position="bottom" :style="{ height: '70%' }">
      <div class="receiving-modal">
        <div class="modal-header">
          <h3>收货 - {{ currentItem?.materialName }}</h3>
          <van-icon name="cross" @click="closeReceivingModal" />
        </div>
        
        <div class="scanner-container">
          <div class="camera-view" ref="cameraRef">
            <!-- 相机预览区域 -->
          </div>
          <div class="scan-overlay">
            <div class="scan-frame"></div>
            <div class="scan-tip">请扫描物料条码</div>
          </div>
        </div>
        
        <div class="manual-input">
          <van-field
            v-model="manualBarcode"
            label="条码"
            placeholder="扫码或手动输入"
            @blur="validateBarcode"
          />
          <van-field
            v-model="receiveQuantity"
            type="number"
            label="收货数量"
            placeholder="请输入收货数量"
          />
          <van-field
            v-model="lotNumber"
            label="批次号"
            placeholder="请输入批次号"
          />
        </div>
        
        <div class="modal-actions">
          <van-button block type="primary" @click="confirmReceiving">
            确认收货
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BarcodeScanner } from '@/utils/barcode-scanner';

const inbound = ref<InboundOrder>({} as InboundOrder);
const currentItem = ref<InboundItem | null>(null);
const showReceivingModal = ref(false);
const cameraRef = ref<HTMLElement>();
const scanner = ref<BarcodeScanner>();

// 表单数据
const manualBarcode = ref('');
const receiveQuantity = ref<number>(0);
const lotNumber = ref('');

onMounted(async () => {
  await loadInboundData();
  initializeScanner();
});

onUnmounted(() => {
  scanner.value?.destroy();
});

async function loadInboundData() {
  const inboundId = route.params.id as string;
  inbound.value = await inboundAPI.info(inboundId);
}

function initializeScanner() {
  if (cameraRef.value) {
    scanner.value = new BarcodeScanner(cameraRef.value);
    scanner.value.onScan = handleBarcodeScan;
  }
}

function startReceiving(item: InboundItem) {
  currentItem.value = item;
  receiveQuantity.value = item.planQty - item.receivedQty;
  showReceivingModal.value = true;
  
  // 启动扫码
  scanner.value?.start();
}

function handleBarcodeScan(barcode: string) {
  manualBarcode.value = barcode;
  validateBarcode();
}

async function validateBarcode() {
  if (!manualBarcode.value || !currentItem.value) return;
  
  try {
    const materialInfo = await materialAPI.getByBarcode(manualBarcode.value);
    if (materialInfo.materialId !== currentItem.value.materialId) {
      showToast('条码与物料不匹配');
      manualBarcode.value = '';
      return;
    }
    
    // 自动填充批次号（如果条码包含批次信息）
    if (materialInfo.isLotManaged && materialInfo.lotNumber) {
      lotNumber.value = materialInfo.lotNumber;
    }
  } catch (error) {
    showToast('条码验证失败');
    manualBarcode.value = '';
  }
}

async function confirmReceiving() {
  if (!currentItem.value) return;
  
  try {
    await inboundAPI.receive({
      inboundId: inbound.value.inboundId,
      itemId: currentItem.value.itemId,
      barcode: manualBarcode.value,
      quantity: receiveQuantity.value,
      lotNumber: lotNumber.value
    });
    
    // 更新本地数据
    currentItem.value.receivedQty += receiveQuantity.value;
    
    showToast('收货成功');
    closeReceivingModal();
    
    // 检查是否完成所有收货
    const allCompleted = inbound.value.items.every(
      item => item.receivedQty >= item.planQty
    );
    if (allCompleted) {
      await completeReceiving();
    }
  } catch (error) {
    showToast('收货失败');
  }
}

function closeReceivingModal() {
  showReceivingModal.value = false;
  scanner.value?.stop();
  manualBarcode.value = '';
  receiveQuantity.value = 0;
  lotNumber.value = '';
}
</script>
```

这份技术实现方案提供了：

1. **系统架构设计** - 整体技术架构和技术栈选择
2. **模块详细设计** - 每个功能模块的具体实现方案
3. **数据库设计** - 完整的表结构和索引设计
4. **API接口设计** - RESTful API规范和接口定义
5. **移动端实现** - PDA收货界面的完整实现
6. **状态管理** - 业务流程的状态机设计
7. **库存算法** - 库存更新的核心算法逻辑

现在让我调用MCP模块获取您的反馈：

<function_calls>
<invoke name="mcp_mcp-feedback-enhanced_interactive_feedback">
<parameter name="project_directory">.
</code_block_to_apply_changes_from>
</edit_instructions>


Assistant:<rewritten_file>
```
# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity += params.quantity;
      inventory.availableQty += params.quantity;
      inventory.totalCost = (inventory.totalCost + params.quantity * params.unitCost);
      inventory.unitCost = inventory.totalCost / inventory.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      // 3. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.INBOUND,
        quantity: params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 4. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存出库
   */
  async outbound(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 获取库存记录
      const inventory = await this.getInventory(params.inventoryId, trx);
      if (!inventory) {
        throw new Error('库存记录不存在');
      }
      
      // 2. 检查可用库存
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      // 3. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity -= params.quantity;
      inventory.availableQty -= params.quantity;
      
      // 重新计算单位成本(移动平均法)
      if (inventory.quantity > 0) {
        inventory.totalCost = inventory.quantity * inventory.unitCost;
      } else {
        inventory.totalCost = 0;
      }
      
      inventory.lastUpdateTime = new Date();
      await this.updateInventory(inventory, trx);
      
      // 4. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.OUTBOUND,
        quantity: -params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 5. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存分配
   */
  async allocate(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      const inventory = await this.getInventory(params.inventoryId, trx);
      
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      inventory.availableQty -= params.quantity;
      inventory.allocatedQty += params.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.ALLOCATE,
        quantity: params.quantity,
        beforeQty: inventory.availableQty + params.quantity,
        afterQty: inventory.availableQty,
        sourceDocumentType: 'ALLOCATION',
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
    });
  }
  
  /**
   * 库存冻结
   */
  async freeze(params: {
    inventoryId: string;
    quantity: number;
    reason: string;
    operatorId: string;
  }): Promise<void> {
    // 类似实现...
  }
  
  private async executeTransaction<T>(
    callback: (trx: Transaction) => Promise<T>
  ): Promise<T> {
    const trx = await this.db.beginTransaction();
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
```

### 4. 入库管理模块

#### 入库流程状态机
```typescript
// 入库流程状态定义
enum InboundStatus {
  PLANNED = 'PLANNED',         // 计划中
  CONFIRMED = 'CONFIRMED',     // 已确认
  IN_TRANSIT = 'IN_TRANSIT',   // 在途
  ARRIVED = 'ARRIVED',         // 已到达
  RECEIVING = 'RECEIVING',     // 收货中
  QC_PENDING = 'QC_PENDING',   // 待质检
  QC_PASSED = 'QC_PASSED',     // 质检通过
  QC_FAILED = 'QC_FAILED',     // 质检不通过
  PUTAWAY = 'PUTAWAY',         // 上架中
  COMPLETED = 'COMPLETED',     // 已完成
  CANCELLED = 'CANCELLED'      // 已取消
}

// 状态转换规则
const InboundStatusTransitions: Record<InboundStatus, InboundStatus[]> = {
  [InboundStatus.PLANNED]: [InboundStatus.CONFIRMED, InboundStatus.CANCELLED],
  [InboundStatus.CONFIRMED]: [InboundStatus.IN_TRANSIT, InboundStatus.CANCELLED],
  [InboundStatus.IN_TRANSIT]: [InboundStatus.ARRIVED, InboundStatus.CANCELLED],
  [InboundStatus.ARRIVED]: [InboundStatus.RECEIVING],
  [InboundStatus.RECEIVING]: [InboundStatus.QC_PENDING, InboundStatus.COMPLETED],
  [InboundStatus.QC_PENDING]: [InboundStatus.QC_PASSED, InboundStatus.QC_FAILED],
  [InboundStatus.QC_PASSED]: [InboundStatus.PUTAWAY],
  [InboundStatus.QC_FAILED]: [InboundStatus.CANCELLED],
  [InboundStatus.PUTAWAY]: [InboundStatus.COMPLETED],
  [InboundStatus.COMPLETED]: [],
  [InboundStatus.CANCELLED]: []
};

// 入库流程管理器
class InboundFlowManager {
  async updateStatus(
    inboundId: string, 
    newStatus: InboundStatus,
    operatorId: string,
    remark?: string
  ): Promise<void> {
    const inbound = await this.getInbound(inboundId);
    const currentStatus = inbound.status as InboundStatus;
    
    // 验证状态转换是否合法
    if (!InboundStatusTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不能从${currentStatus}转换到${newStatus}`);
    }
    
    // 执行状态转换
    await this.executeStatusChange(inbound, newStatus, operatorId, remark);
    
    // 触发相关业务逻辑
    await this.triggerStatusActions(inbound, newStatus);
  }
  
  private async triggerStatusActions(
    inbound: InboundOrder, 
    status: InboundStatus
  ): Promise<void> {
    switch (status) {
      case InboundStatus.CONFIRMED:
        // 发送确认通知
        await this.notifySupplier(inbound);
        break;
        
      case InboundStatus.ARRIVED:
        // 生成收货任务
        await this.generateReceivingTasks(inbound);
        break;
        
      case InboundStatus.QC_PASSED:
        // 生成上架任务
        await this.generatePutawayTasks(inbound);
        break;
        
      case InboundStatus.COMPLETED:
        // 更新库存
        await this.updateInventory(inbound);
        // 发送完成通知
        await this.notifyCompletion(inbound);
        break;
    }
  }
}
```

#### 移动端收货界面
```vue
<!-- 移动端收货界面 -->
<template>
  <div class="mobile-receiving">
    <!-- 顶部导航 -->
    <div class="top-bar">
      <van-nav-bar
        title="收货作业"
        left-text="返回"
        @click-left="goBack"
      />
    </div>
    
    <!-- 入库单信息 -->
    <div class="inbound-info">
      <van-cell-group>
        <van-cell title="入库单号" :value="inbound.inboundNumber" />
        <van-cell title="供应商" :value="inbound.supplierName" />
        <van-cell title="计划日期" :value="formatDate(inbound.planDate)" />
      </van-cell-group>
    </div>
    
    <!-- 物料列表 -->
    <div class="material-list">
      <div 
        v-for="item in inbound.items" 
        :key="item.itemId"
        class="material-item"
        :class="{ 'completed': item.receivedQty >= item.planQty }"
      >
        <div class="material-info">
          <div class="material-code">{{ item.materialCode }}</div>
          <div class="material-name">{{ item.materialName }}</div>
          <div class="quantity-info">
            <span class="received">{{ item.receivedQty }}</span>
            /
            <span class="planned">{{ item.planQty }}</span>
            {{ item.unit }}
          </div>
        </div>
        
        <div class="actions">
          <van-button 
            type="primary" 
            size="small"
            @click="startReceiving(item)"
            :disabled="item.receivedQty >= item.planQty"
          >
            收货
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 扫码收货弹窗 -->
    <van-popup v-model:show="showReceivingModal" position="bottom" :style="{ height: '70%' }">
      <div class="receiving-modal">
        <div class="modal-header">
          <h3>收货 - {{ currentItem?.materialName }}</h3>
          <van-icon name="cross" @click="closeReceivingModal" />
        </div>
        
        <div class="scanner-container">
          <div class="camera-view" ref="cameraRef">
            <!-- 相机预览区域 -->
          </div>
          <div class="scan-overlay">
            <div class="scan-frame"></div>
            <div class="scan-tip">请扫描物料条码</div>
          </div>
        </div>
        
        <div class="manual-input">
          <van-field
            v-model="manualBarcode"
            label="条码"
            placeholder="扫码或手动输入"
            @blur="validateBarcode"
          />
          <van-field
            v-model="receiveQuantity"
            type="number"
            label="收货数量"
            placeholder="请输入收货数量"
          />
          <van-field
            v-model="lotNumber"
            label="批次号"
            placeholder="请输入批次号"
          />
        </div>
        
        <div class="modal-actions">
          <van-button block type="primary" @click="confirmReceiving">
            确认收货
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BarcodeScanner } from '@/utils/barcode-scanner';

const inbound = ref<InboundOrder>({} as InboundOrder);
const currentItem = ref<InboundItem | null>(null);
const showReceivingModal = ref(false);
const cameraRef = ref<HTMLElement>();
const scanner = ref<BarcodeScanner>();

// 表单数据
const manualBarcode = ref('');
const receiveQuantity = ref<number>(0);
const lotNumber = ref('');

onMounted(async () => {
  await loadInboundData();
  initializeScanner();
});

onUnmounted(() => {
  scanner.value?.destroy();
});

async function loadInboundData() {
  const inboundId = route.params.id as string;
  inbound.value = await inboundAPI.info(inboundId);
}

function initializeScanner() {
  if (cameraRef.value) {
    scanner.value = new BarcodeScanner(cameraRef.value);
    scanner.value.onScan = handleBarcodeScan;
  }
}

function startReceiving(item: InboundItem) {
  currentItem.value = item;
  receiveQuantity.value = item.planQty - item.receivedQty;
  showReceivingModal.value = true;
  
  // 启动扫码
  scanner.value?.start();
}

function handleBarcodeScan(barcode: string) {
  manualBarcode.value = barcode;
  validateBarcode();
}

async function validateBarcode() {
  if (!manualBarcode.value || !currentItem.value) return;
  
  try {
    const materialInfo = await materialAPI.getByBarcode(manualBarcode.value);
    if (materialInfo.materialId !== currentItem.value.materialId) {
      showToast('条码与物料不匹配');
      manualBarcode.value = '';
      return;
    }
    
    // 自动填充批次号（如果条码包含批次信息）
    if (materialInfo.isLotManaged && materialInfo.lotNumber) {
      lotNumber.value = materialInfo.lotNumber;
    }
  } catch (error) {
    showToast('条码验证失败');
    manualBarcode.value = '';
  }
}

async function confirmReceiving() {
  if (!currentItem.value) return;
  
  try {
    await inboundAPI.receive({
      inboundId: inbound.value.inboundId,
      itemId: currentItem.value.itemId,
      barcode: manualBarcode.value,
      quantity: receiveQuantity.value,
      lotNumber: lotNumber.value
    });
    
    // 更新本地数据
    currentItem.value.receivedQty += receiveQuantity.value;
    
    showToast('收货成功');
    closeReceivingModal();
    
    // 检查是否完成所有收货
    const allCompleted = inbound.value.items.every(
      item => item.receivedQty >= item.planQty
    );
    if (allCompleted) {
      await completeReceiving();
    }
  } catch (error) {
    showToast('收货失败');
  }
}

function closeReceivingModal() {
  showReceivingModal.value = false;
  scanner.value?.stop();
  manualBarcode.value = '';
  receiveQuantity.value = 0;
  lotNumber.value = '';
}
</script>
```

这份技术实现方案提供了：

1. **系统架构设计** - 整体技术架构和技术栈选择
2. **模块详细设计** - 每个功能模块的具体实现方案
3. **数据库设计** - 完整的表结构和索引设计
4. **API接口设计** - RESTful API规范和接口定义
5. **移动端实现** - PDA收货界面的完整实现
6. **状态管理** - 业务流程的状态机设计
7. **库存算法** - 库存更新的核心算法逻辑

现在让我调用MCP模块获取您的反馈：

<function_calls>
<invoke name="mcp_mcp-feedback-enhanced_interactive_feedback">
<parameter name="project_directory">.
</code_block_to_apply_changes_from>
</edit_instructions>


Assistant:<rewritten_file>
```
# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity += params.quantity;
      inventory.availableQty += params.quantity;
      inventory.totalCost = (inventory.totalCost + params.quantity * params.unitCost);
      inventory.unitCost = inventory.totalCost / inventory.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      // 3. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.INBOUND,
        quantity: params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 4. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存出库
   */
  async outbound(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 获取库存记录
      const inventory = await this.getInventory(params.inventoryId, trx);
      if (!inventory) {
        throw new Error('库存记录不存在');
      }
      
      // 2. 检查可用库存
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      // 3. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity -= params.quantity;
      inventory.availableQty -= params.quantity;
      
      // 重新计算单位成本(移动平均法)
      if (inventory.quantity > 0) {
        inventory.totalCost = inventory.quantity * inventory.unitCost;
      } else {
        inventory.totalCost = 0;
      }
      
      inventory.lastUpdateTime = new Date();
      await this.updateInventory(inventory, trx);
      
      // 4. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.OUTBOUND,
        quantity: -params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 5. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存分配
   */
  async allocate(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      const inventory = await this.getInventory(params.inventoryId, trx);
      
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      inventory.availableQty -= params.quantity;
      inventory.allocatedQty += params.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.ALLOCATE,
        quantity: params.quantity,
        beforeQty: inventory.availableQty + params.quantity,
        afterQty: inventory.availableQty,
        sourceDocumentType: 'ALLOCATION',
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
    });
  }
  
  /**
   * 库存冻结
   */
  async freeze(params: {
    inventoryId: string;
    quantity: number;
    reason: string;
    operatorId: string;
  }): Promise<void> {
    // 类似实现...
  }
  
  private async executeTransaction<T>(
    callback: (trx: Transaction) => Promise<T>
  ): Promise<T> {
    const trx = await this.db.beginTransaction();
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
```

### 4. 入库管理模块

#### 入库流程状态机
```typescript
// 入库流程状态定义
enum InboundStatus {
  PLANNED = 'PLANNED',         // 计划中
  CONFIRMED = 'CONFIRMED',     // 已确认
  IN_TRANSIT = 'IN_TRANSIT',   // 在途
  ARRIVED = 'ARRIVED',         // 已到达
  RECEIVING = 'RECEIVING',     // 收货中
  QC_PENDING = 'QC_PENDING',   // 待质检
  QC_PASSED = 'QC_PASSED',     // 质检通过
  QC_FAILED = 'QC_FAILED',     // 质检不通过
  PUTAWAY = 'PUTAWAY',         // 上架中
  COMPLETED = 'COMPLETED',     // 已完成
  CANCELLED = 'CANCELLED'      // 已取消
}

// 状态转换规则
const InboundStatusTransitions: Record<InboundStatus, InboundStatus[]> = {
  [InboundStatus.PLANNED]: [InboundStatus.CONFIRMED, InboundStatus.CANCELLED],
  [InboundStatus.CONFIRMED]: [InboundStatus.IN_TRANSIT, InboundStatus.CANCELLED],
  [InboundStatus.IN_TRANSIT]: [InboundStatus.ARRIVED, InboundStatus.CANCELLED],
  [InboundStatus.ARRIVED]: [InboundStatus.RECEIVING],
  [InboundStatus.RECEIVING]: [InboundStatus.QC_PENDING, InboundStatus.COMPLETED],
  [InboundStatus.QC_PENDING]: [InboundStatus.QC_PASSED, InboundStatus.QC_FAILED],
  [InboundStatus.QC_PASSED]: [InboundStatus.PUTAWAY],
  [InboundStatus.QC_FAILED]: [InboundStatus.CANCELLED],
  [InboundStatus.PUTAWAY]: [InboundStatus.COMPLETED],
  [InboundStatus.COMPLETED]: [],
  [InboundStatus.CANCELLED]: []
};

// 入库流程管理器
class InboundFlowManager {
  async updateStatus(
    inboundId: string, 
    newStatus: InboundStatus,
    operatorId: string,
    remark?: string
  ): Promise<void> {
    const inbound = await this.getInbound(inboundId);
    const currentStatus = inbound.status as InboundStatus;
    
    // 验证状态转换是否合法
    if (!InboundStatusTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不能从${currentStatus}转换到${newStatus}`);
    }
    
    // 执行状态转换
    await this.executeStatusChange(inbound, newStatus, operatorId, remark);
    
    // 触发相关业务逻辑
    await this.triggerStatusActions(inbound, newStatus);
  }
  
  private async triggerStatusActions(
    inbound: InboundOrder, 
    status: InboundStatus
  ): Promise<void> {
    switch (status) {
      case InboundStatus.CONFIRMED:
        // 发送确认通知
        await this.notifySupplier(inbound);
        break;
        
      case InboundStatus.ARRIVED:
        // 生成收货任务
        await this.generateReceivingTasks(inbound);
        break;
        
      case InboundStatus.QC_PASSED:
        // 生成上架任务
        await this.generatePutawayTasks(inbound);
        break;
        
      case InboundStatus.COMPLETED:
        // 更新库存
        await this.updateInventory(inbound);
        // 发送完成通知
        await this.notifyCompletion(inbound);
        break;
    }
  }
}
```

#### 移动端收货界面
```vue
<!-- 移动端收货界面 -->
<template>
  <div class="mobile-receiving">
    <!-- 顶部导航 -->
    <div class="top-bar">
      <van-nav-bar
        title="收货作业"
        left-text="返回"
        @click-left="goBack"
      />
    </div>
    
    <!-- 入库单信息 -->
    <div class="inbound-info">
      <van-cell-group>
        <van-cell title="入库单号" :value="inbound.inboundNumber" />
        <van-cell title="供应商" :value="inbound.supplierName" />
        <van-cell title="计划日期" :value="formatDate(inbound.planDate)" />
      </van-cell-group>
    </div>
    
    <!-- 物料列表 -->
    <div class="material-list">
      <div 
        v-for="item in inbound.items" 
        :key="item.itemId"
        class="material-item"
        :class="{ 'completed': item.receivedQty >= item.planQty }"
      >
        <div class="material-info">
          <div class="material-code">{{ item.materialCode }}</div>
          <div class="material-name">{{ item.materialName }}</div>
          <div class="quantity-info">
            <span class="received">{{ item.receivedQty }}</span>
            /
            <span class="planned">{{ item.planQty }}</span>
            {{ item.unit }}
          </div>
        </div>
        
        <div class="actions">
          <van-button 
            type="primary" 
            size="small"
            @click="startReceiving(item)"
            :disabled="item.receivedQty >= item.planQty"
          >
            收货
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 扫码收货弹窗 -->
    <van-popup v-model:show="showReceivingModal" position="bottom" :style="{ height: '70%' }">
      <div class="receiving-modal">
        <div class="modal-header">
          <h3>收货 - {{ currentItem?.materialName }}</h3>
          <van-icon name="cross" @click="closeReceivingModal" />
        </div>
        
        <div class="scanner-container">
          <div class="camera-view" ref="cameraRef">
            <!-- 相机预览区域 -->
          </div>
          <div class="scan-overlay">
            <div class="scan-frame"></div>
            <div class="scan-tip">请扫描物料条码</div>
          </div>
        </div>
        
        <div class="manual-input">
          <van-field
            v-model="manualBarcode"
            label="条码"
            placeholder="扫码或手动输入"
            @blur="validateBarcode"
          />
          <van-field
            v-model="receiveQuantity"
            type="number"
            label="收货数量"
            placeholder="请输入收货数量"
          />
          <van-field
            v-model="lotNumber"
            label="批次号"
            placeholder="请输入批次号"
          />
        </div>
        
        <div class="modal-actions">
          <van-button block type="primary" @click="confirmReceiving">
            确认收货
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BarcodeScanner } from '@/utils/barcode-scanner';

const inbound = ref<InboundOrder>({} as InboundOrder);
const currentItem = ref<InboundItem | null>(null);
const showReceivingModal = ref(false);
const cameraRef = ref<HTMLElement>();
const scanner = ref<BarcodeScanner>();

// 表单数据
const manualBarcode = ref('');
const receiveQuantity = ref<number>(0);
const lotNumber = ref('');

onMounted(async () => {
  await loadInboundData();
  initializeScanner();
});

onUnmounted(() => {
  scanner.value?.destroy();
});

async function loadInboundData() {
  const inboundId = route.params.id as string;
  inbound.value = await inboundAPI.info(inboundId);
}

function initializeScanner() {
  if (cameraRef.value) {
    scanner.value = new BarcodeScanner(cameraRef.value);
    scanner.value.onScan = handleBarcodeScan;
  }
}

function startReceiving(item: InboundItem) {
  currentItem.value = item;
  receiveQuantity.value = item.planQty - item.receivedQty;
  showReceivingModal.value = true;
  
  // 启动扫码
  scanner.value?.start();
}

function handleBarcodeScan(barcode: string) {
  manualBarcode.value = barcode;
  validateBarcode();
}

async function validateBarcode() {
  if (!manualBarcode.value || !currentItem.value) return;
  
  try {
    const materialInfo = await materialAPI.getByBarcode(manualBarcode.value);
    if (materialInfo.materialId !== currentItem.value.materialId) {
      showToast('条码与物料不匹配');
      manualBarcode.value = '';
      return;
    }
    
    // 自动填充批次号（如果条码包含批次信息）
    if (materialInfo.isLotManaged && materialInfo.lotNumber) {
      lotNumber.value = materialInfo.lotNumber;
    }
  } catch (error) {
    showToast('条码验证失败');
    manualBarcode.value = '';
  }
}

async function confirmReceiving() {
  if (!currentItem.value) return;
  
  try {
    await inboundAPI.receive({
      inboundId: inbound.value.inboundId,
      itemId: currentItem.value.itemId,
      barcode: manualBarcode.value,
      quantity: receiveQuantity.value,
      lotNumber: lotNumber.value
    });
    
    // 更新本地数据
    currentItem.value.receivedQty += receiveQuantity.value;
    
    showToast('收货成功');
    closeReceivingModal();
    
    // 检查是否完成所有收货
    const allCompleted = inbound.value.items.every(
      item => item.receivedQty >= item.planQty
    );
    if (allCompleted) {
      await completeReceiving();
    }
  } catch (error) {
    showToast('收货失败');
  }
}

function closeReceivingModal() {
  showReceivingModal.value = false;
  scanner.value?.stop();
  manualBarcode.value = '';
  receiveQuantity.value = 0;
  lotNumber.value = '';
}
</script>
```

这份技术实现方案提供了：

1. **系统架构设计** - 整体技术架构和技术栈选择
2. **模块详细设计** - 每个功能模块的具体实现方案
3. **数据库设计** - 完整的表结构和索引设计
4. **API接口设计** - RESTful API规范和接口定义
5. **移动端实现** - PDA收货界面的完整实现
6. **状态管理** - 业务流程的状态机设计
7. **库存算法** - 库存更新的核心算法逻辑

现在让我调用MCP模块获取您的反馈：

<function_calls>
<invoke name="mcp_mcp-feedback-enhanced_interactive_feedback">
<parameter name="project_directory">.
</code_block_to_apply_changes_from>
</edit_instructions>


Assistant:<rewritten_file>
```
# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity += params.quantity;
      inventory.availableQty += params.quantity;
      inventory.totalCost = (inventory.totalCost + params.quantity * params.unitCost);
      inventory.unitCost = inventory.totalCost / inventory.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      // 3. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.INBOUND,
        quantity: params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 4. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存出库
   */
  async outbound(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 获取库存记录
      const inventory = await this.getInventory(params.inventoryId, trx);
      if (!inventory) {
        throw new Error('库存记录不存在');
      }
      
      // 2. 检查可用库存
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      // 3. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity -= params.quantity;
      inventory.availableQty -= params.quantity;
      
      // 重新计算单位成本(移动平均法)
      if (inventory.quantity > 0) {
        inventory.totalCost = inventory.quantity * inventory.unitCost;
      } else {
        inventory.totalCost = 0;
      }
      
      inventory.lastUpdateTime = new Date();
      await this.updateInventory(inventory, trx);
      
      // 4. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.OUTBOUND,
        quantity: -params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 5. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存分配
   */
  async allocate(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      const inventory = await this.getInventory(params.inventoryId, trx);
      
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      inventory.availableQty -= params.quantity;
      inventory.allocatedQty += params.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.ALLOCATE,
        quantity: params.quantity,
        beforeQty: inventory.availableQty + params.quantity,
        afterQty: inventory.availableQty,
        sourceDocumentType: 'ALLOCATION',
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
    });
  }
  
  /**
   * 库存冻结
   */
  async freeze(params: {
    inventoryId: string;
    quantity: number;
    reason: string;
    operatorId: string;
  }): Promise<void> {
    // 类似实现...
  }
  
  private async executeTransaction<T>(
    callback: (trx: Transaction) => Promise<T>
  ): Promise<T> {
    const trx = await this.db.beginTransaction();
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
```

### 4. 入库管理模块

#### 入库流程状态机
```typescript
// 入库流程状态定义
enum InboundStatus {
  PLANNED = 'PLANNED',         // 计划中
  CONFIRMED = 'CONFIRMED',     // 已确认
  IN_TRANSIT = 'IN_TRANSIT',   // 在途
  ARRIVED = 'ARRIVED',         // 已到达
  RECEIVING = 'RECEIVING',     // 收货中
  QC_PENDING = 'QC_PENDING',   // 待质检
  QC_PASSED = 'QC_PASSED',     // 质检通过
  QC_FAILED = 'QC_FAILED',     // 质检不通过
  PUTAWAY = 'PUTAWAY',         // 上架中
  COMPLETED = 'COMPLETED',     // 已完成
  CANCELLED = 'CANCELLED'      // 已取消
}

// 状态转换规则
const InboundStatusTransitions: Record<InboundStatus, InboundStatus[]> = {
  [InboundStatus.PLANNED]: [InboundStatus.CONFIRMED, InboundStatus.CANCELLED],
  [InboundStatus.CONFIRMED]: [InboundStatus.IN_TRANSIT, InboundStatus.CANCELLED],
  [InboundStatus.IN_TRANSIT]: [InboundStatus.ARRIVED, InboundStatus.CANCELLED],
  [InboundStatus.ARRIVED]: [InboundStatus.RECEIVING],
  [InboundStatus.RECEIVING]: [InboundStatus.QC_PENDING, InboundStatus.COMPLETED],
  [InboundStatus.QC_PENDING]: [InboundStatus.QC_PASSED, InboundStatus.QC_FAILED],
  [InboundStatus.QC_PASSED]: [InboundStatus.PUTAWAY],
  [InboundStatus.QC_FAILED]: [InboundStatus.CANCELLED],
  [InboundStatus.PUTAWAY]: [InboundStatus.COMPLETED],
  [InboundStatus.COMPLETED]: [],
  [InboundStatus.CANCELLED]: []
};

// 入库流程管理器
class InboundFlowManager {
  async updateStatus(
    inboundId: string, 
    newStatus: InboundStatus,
    operatorId: string,
    remark?: string
  ): Promise<void> {
    const inbound = await this.getInbound(inboundId);
    const currentStatus = inbound.status as InboundStatus;
    
    // 验证状态转换是否合法
    if (!InboundStatusTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不能从${currentStatus}转换到${newStatus}`);
    }
    
    // 执行状态转换
    await this.executeStatusChange(inbound, newStatus, operatorId, remark);
    
    // 触发相关业务逻辑
    await this.triggerStatusActions(inbound, newStatus);
  }
  
  private async triggerStatusActions(
    inbound: InboundOrder, 
    status: InboundStatus
  ): Promise<void> {
    switch (status) {
      case InboundStatus.CONFIRMED:
        // 发送确认通知
        await this.notifySupplier(inbound);
        break;
        
      case InboundStatus.ARRIVED:
        // 生成收货任务
        await this.generateReceivingTasks(inbound);
        break;
        
      case InboundStatus.QC_PASSED:
        // 生成上架任务
        await this.generatePutawayTasks(inbound);
        break;
        
      case InboundStatus.COMPLETED:
        // 更新库存
        await this.updateInventory(inbound);
        // 发送完成通知
        await this.notifyCompletion(inbound);
        break;
    }
  }
}
```

#### 移动端收货界面
```vue
<!-- 移动端收货界面 -->
<template>
  <div class="mobile-receiving">
    <!-- 顶部导航 -->
    <div class="top-bar">
      <van-nav-bar
        title="收货作业"
        left-text="返回"
        @click-left="goBack"
      />
    </div>
    
    <!-- 入库单信息 -->
    <div class="inbound-info">
      <van-cell-group>
        <van-cell title="入库单号" :value="inbound.inboundNumber" />
        <van-cell title="供应商" :value="inbound.supplierName" />
        <van-cell title="计划日期" :value="formatDate(inbound.planDate)" />
      </van-cell-group>
    </div>
    
    <!-- 物料列表 -->
    <div class="material-list">
      <div 
        v-for="item in inbound.items" 
        :key="item.itemId"
        class="material-item"
        :class="{ 'completed': item.receivedQty >= item.planQty }"
      >
        <div class="material-info">
          <div class="material-code">{{ item.materialCode }}</div>
          <div class="material-name">{{ item.materialName }}</div>
          <div class="quantity-info">
            <span class="received">{{ item.receivedQty }}</span>
            /
            <span class="planned">{{ item.planQty }}</span>
            {{ item.unit }}
          </div>
        </div>
        
        <div class="actions">
          <van-button 
            type="primary" 
            size="small"
            @click="startReceiving(item)"
            :disabled="item.receivedQty >= item.planQty"
          >
            收货
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 扫码收货弹窗 -->
    <van-popup v-model:show="showReceivingModal" position="bottom" :style="{ height: '70%' }">
      <div class="receiving-modal">
        <div class="modal-header">
          <h3>收货 - {{ currentItem?.materialName }}</h3>
          <van-icon name="cross" @click="closeReceivingModal" />
        </div>
        
        <div class="scanner-container">
          <div class="camera-view" ref="cameraRef">
            <!-- 相机预览区域 -->
          </div>
          <div class="scan-overlay">
            <div class="scan-frame"></div>
            <div class="scan-tip">请扫描物料条码</div>
          </div>
        </div>
        
        <div class="manual-input">
          <van-field
            v-model="manualBarcode"
            label="条码"
            placeholder="扫码或手动输入"
            @blur="validateBarcode"
          />
          <van-field
            v-model="receiveQuantity"
            type="number"
            label="收货数量"
            placeholder="请输入收货数量"
          />
          <van-field
            v-model="lotNumber"
            label="批次号"
            placeholder="请输入批次号"
          />
        </div>
        
        <div class="modal-actions">
          <van-button block type="primary" @click="confirmReceiving">
            确认收货
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BarcodeScanner } from '@/utils/barcode-scanner';

const inbound = ref<InboundOrder>({} as InboundOrder);
const currentItem = ref<InboundItem | null>(null);
const showReceivingModal = ref(false);
const cameraRef = ref<HTMLElement>();
const scanner = ref<BarcodeScanner>();

// 表单数据
const manualBarcode = ref('');
const receiveQuantity = ref<number>(0);
const lotNumber = ref('');

onMounted(async () => {
  await loadInboundData();
  initializeScanner();
});

onUnmounted(() => {
  scanner.value?.destroy();
});

async function loadInboundData() {
  const inboundId = route.params.id as string;
  inbound.value = await inboundAPI.info(inboundId);
}

function initializeScanner() {
  if (cameraRef.value) {
    scanner.value = new BarcodeScanner(cameraRef.value);
    scanner.value.onScan = handleBarcodeScan;
  }
}

function startReceiving(item: InboundItem) {
  currentItem.value = item;
  receiveQuantity.value = item.planQty - item.receivedQty;
  showReceivingModal.value = true;
  
  // 启动扫码
  scanner.value?.start();
}

function handleBarcodeScan(barcode: string) {
  manualBarcode.value = barcode;
  validateBarcode();
}

async function validateBarcode() {
  if (!manualBarcode.value || !currentItem.value) return;
  
  try {
    const materialInfo = await materialAPI.getByBarcode(manualBarcode.value);
    if (materialInfo.materialId !== currentItem.value.materialId) {
      showToast('条码与物料不匹配');
      manualBarcode.value = '';
      return;
    }
    
    // 自动填充批次号（如果条码包含批次信息）
    if (materialInfo.isLotManaged && materialInfo.lotNumber) {
      lotNumber.value = materialInfo.lotNumber;
    }
  } catch (error) {
    showToast('条码验证失败');
    manualBarcode.value = '';
  }
}

async function confirmReceiving() {
  if (!currentItem.value) return;
  
  try {
    await inboundAPI.receive({
      inboundId: inbound.value.inboundId,
      itemId: currentItem.value.itemId,
      barcode: manualBarcode.value,
      quantity: receiveQuantity.value,
      lotNumber: lotNumber.value
    });
    
    // 更新本地数据
    currentItem.value.receivedQty += receiveQuantity.value;
    
    showToast('收货成功');
    closeReceivingModal();
    
    // 检查是否完成所有收货
    const allCompleted = inbound.value.items.every(
      item => item.receivedQty >= item.planQty
    );
    if (allCompleted) {
      await completeReceiving();
    }
  } catch (error) {
    showToast('收货失败');
  }
}

function closeReceivingModal() {
  showReceivingModal.value = false;
  scanner.value?.stop();
  manualBarcode.value = '';
  receiveQuantity.value = 0;
  lotNumber.value = '';
}
</script>
```

这份技术实现方案提供了：

1. **系统架构设计** - 整体技术架构和技术栈选择
2. **模块详细设计** - 每个功能模块的具体实现方案
3. **数据库设计** - 完整的表结构和索引设计
4. **API接口设计** - RESTful API规范和接口定义
5. **移动端实现** - PDA收货界面的完整实现
6. **状态管理** - 业务流程的状态机设计
7. **库存算法** - 库存更新的核心算法逻辑

现在让我调用MCP模块获取您的反馈：

<function_calls>
<invoke name="mcp_mcp-feedback-enhanced_interactive_feedback">
<parameter name="project_directory">.
</code_block_to_apply_changes_from>
</edit_instructions>


Assistant:<rewritten_file>
```
# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity += params.quantity;
      inventory.availableQty += params.quantity;
      inventory.totalCost = (inventory.totalCost + params.quantity * params.unitCost);
      inventory.unitCost = inventory.totalCost / inventory.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      // 3. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.INBOUND,
        quantity: params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 4. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存出库
   */
  async outbound(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 获取库存记录
      const inventory = await this.getInventory(params.inventoryId, trx);
      if (!inventory) {
        throw new Error('库存记录不存在');
      }
      
      // 2. 检查可用库存
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      // 3. 更新库存数量
      const beforeQty = inventory.quantity;
      inventory.quantity -= params.quantity;
      inventory.availableQty -= params.quantity;
      
      // 重新计算单位成本(移动平均法)
      if (inventory.quantity > 0) {
        inventory.totalCost = inventory.quantity * inventory.unitCost;
      } else {
        inventory.totalCost = 0;
      }
      
      inventory.lastUpdateTime = new Date();
      await this.updateInventory(inventory, trx);
      
      // 4. 记录库存流水
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.OUTBOUND,
        quantity: -params.quantity,
        beforeQty,
        afterQty: inventory.quantity,
        sourceDocumentType: params.sourceDocumentType,
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
      
      // 5. 触发预警检查
      await this.checkAlerts(inventory);
    });
  }
  
  /**
   * 库存分配
   */
  async allocate(params: {
    inventoryId: string;
    quantity: number;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      const inventory = await this.getInventory(params.inventoryId, trx);
      
      if (inventory.availableQty < params.quantity) {
        throw new Error('可用库存不足');
      }
      
      inventory.availableQty -= params.quantity;
      inventory.allocatedQty += params.quantity;
      inventory.lastUpdateTime = new Date();
      
      await this.updateInventory(inventory, trx);
      
      await this.createTransaction({
        inventoryId: inventory.inventoryId,
        transactionType: TransactionType.ALLOCATE,
        quantity: params.quantity,
        beforeQty: inventory.availableQty + params.quantity,
        afterQty: inventory.availableQty,
        sourceDocumentType: 'ALLOCATION',
        sourceDocumentNo: params.sourceDocumentNo,
        operatorId: params.operatorId
      }, trx);
    });
  }
  
  /**
   * 库存冻结
   */
  async freeze(params: {
    inventoryId: string;
    quantity: number;
    reason: string;
    operatorId: string;
  }): Promise<void> {
    // 类似实现...
  }
  
  private async executeTransaction<T>(
    callback: (trx: Transaction) => Promise<T>
  ): Promise<T> {
    const trx = await this.db.beginTransaction();
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
```

### 4. 入库管理模块

#### 入库流程状态机
```typescript
// 入库流程状态定义
enum InboundStatus {
  PLANNED = 'PLANNED',         // 计划中
  CONFIRMED = 'CONFIRMED',     // 已确认
  IN_TRANSIT = 'IN_TRANSIT',   // 在途
  ARRIVED = 'ARRIVED',         // 已到达
  RECEIVING = 'RECEIVING',     // 收货中
  QC_PENDING = 'QC_PENDING',   // 待质检
  QC_PASSED = 'QC_PASSED',     // 质检通过
  QC_FAILED = 'QC_FAILED',     // 质检不通过
  PUTAWAY = 'PUTAWAY',         // 上架中
  COMPLETED = 'COMPLETED',     // 已完成
  CANCELLED = 'CANCELLED'      // 已取消
}

// 状态转换规则
const InboundStatusTransitions: Record<InboundStatus, InboundStatus[]> = {
  [InboundStatus.PLANNED]: [InboundStatus.CONFIRMED, InboundStatus.CANCELLED],
  [InboundStatus.CONFIRMED]: [InboundStatus.IN_TRANSIT, InboundStatus.CANCELLED],
  [InboundStatus.IN_TRANSIT]: [InboundStatus.ARRIVED, InboundStatus.CANCELLED],
  [InboundStatus.ARRIVED]: [InboundStatus.RECEIVING],
  [InboundStatus.RECEIVING]: [InboundStatus.QC_PENDING, InboundStatus.COMPLETED],
  [InboundStatus.QC_PENDING]: [InboundStatus.QC_PASSED, InboundStatus.QC_FAILED],
  [InboundStatus.QC_PASSED]: [InboundStatus.PUTAWAY],
  [InboundStatus.QC_FAILED]: [InboundStatus.CANCELLED],
  [InboundStatus.PUTAWAY]: [InboundStatus.COMPLETED],
  [InboundStatus.COMPLETED]: [],
  [InboundStatus.CANCELLED]: []
};

// 入库流程管理器
class InboundFlowManager {
  async updateStatus(
    inboundId: string, 
    newStatus: InboundStatus,
    operatorId: string,
    remark?: string
  ): Promise<void> {
    const inbound = await this.getInbound(inboundId);
    const currentStatus = inbound.status as InboundStatus;
    
    // 验证状态转换是否合法
    if (!InboundStatusTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不能从${currentStatus}转换到${newStatus}`);
    }
    
    // 执行状态转换
    await this.executeStatusChange(inbound, newStatus, operatorId, remark);
    
    // 触发相关业务逻辑
    await this.triggerStatusActions(inbound, newStatus);
  }
  
  private async triggerStatusActions(
    inbound: InboundOrder, 
    status: InboundStatus
  ): Promise<void> {
    switch (status) {
      case InboundStatus.CONFIRMED:
        // 发送确认通知
        await this.notifySupplier(inbound);
        break;
        
      case InboundStatus.ARRIVED:
        // 生成收货任务
        await this.generateReceivingTasks(inbound);
        break;
        
      case InboundStatus.QC_PASSED:
        // 生成上架任务
        await this.generatePutawayTasks(inbound);
        break;
        
      case InboundStatus.COMPLETED:
        // 更新库存
        await this.updateInventory(inbound);
        // 发送完成通知
        await this.notifyCompletion(inbound);
        break;
    }
  }
}
```

#### 移动端收货界面
```vue
<!-- 移动端收货界面 -->
<template>
  <div class="mobile-receiving">
    <!-- 顶部导航 -->
    <div class="top-bar">
      <van-nav-bar
        title="收货作业"
        left-text="返回"
        @click-left="goBack"
      />
    </div>
    
    <!-- 入库单信息 -->
    <div class="inbound-info">
      <van-cell-group>
        <van-cell title="入库单号" :value="inbound.inboundNumber" />
        <van-cell title="供应商" :value="inbound.supplierName" />
        <van-cell title="计划日期" :value="formatDate(inbound.planDate)" />
      </van-cell-group>
    </div>
    
    <!-- 物料列表 -->
    <div class="material-list">
      <div 
        v-for="item in inbound.items" 
        :key="item.itemId"
        class="material-item"
        :class="{ 'completed': item.receivedQty >= item.planQty }"
      >
        <div class="material-info">
          <div class="material-code">{{ item.materialCode }}</div>
          <div class="material-name">{{ item.materialName }}</div>
          <div class="quantity-info">
            <span class="received">{{ item.receivedQty }}</span>
            /
            <span class="planned">{{ item.planQty }}</span>
            {{ item.unit }}
          </div>
        </div>
        
        <div class="actions">
          <van-button 
            type="primary" 
            size="small"
            @click="startReceiving(item)"
            :disabled="item.receivedQty >= item.planQty"
          >
            收货
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 扫码收货弹窗 -->
    <van-popup v-model:show="showReceivingModal" position="bottom" :style="{ height: '70%' }">
      <div class="receiving-modal">
        <div class="modal-header">
          <h3>收货 - {{ currentItem?.materialName }}</h3>
          <van-icon name="cross" @click="closeReceivingModal" />
        </div>
        
        <div class="scanner-container">
          <div class="camera-view" ref="cameraRef">
            <!-- 相机预览区域 -->
          </div>
          <div class="scan-overlay">
            <div class="scan-frame"></div>
            <div class="scan-tip">请扫描物料条码</div>
          </div>
        </div>
        
        <div class="manual-input">
          <van-field
            v-model="manualBarcode"
            label="条码"
            placeholder="扫码或手动输入"
            @blur="validateBarcode"
          />
          <van-field
            v-model="receiveQuantity"
            type="number"
            label="收货数量"
            placeholder="请输入收货数量"
          />
          <van-field
            v-model="lotNumber"
            label="批次号"
            placeholder="请输入批次号"
          />
        </div>
        
        <div class="modal-actions">
          <van-button block type="primary" @click="confirmReceiving">
            确认收货
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BarcodeScanner } from '@/utils/barcode-scanner';

const inbound = ref<InboundOrder>({} as InboundOrder);
const currentItem = ref<InboundItem | null>(null);
const showReceivingModal = ref(false);
const cameraRef = ref<HTMLElement>();
const scanner = ref<BarcodeScanner>();

// 表单数据
const manualBarcode = ref('');
const receiveQuantity = ref<number>(0);
const lotNumber = ref('');

onMounted(async () => {
  await loadInboundData();
  initializeScanner();
});

onUnmounted(() => {
  scanner.value?.destroy();
});

async function loadInboundData() {
  const inboundId = route.params.id as string;
  inbound.value = await inboundAPI.info(inboundId);
}

function initializeScanner() {
  if (cameraRef.value) {
    scanner.value = new BarcodeScanner(cameraRef.value);
    scanner.value.onScan = handleBarcodeScan;
  }
}

function startReceiving(item: InboundItem) {
  currentItem.value = item;
  receiveQuantity.value = item.planQty - item.receivedQty;
  showReceivingModal.value = true;
  
  // 启动扫码
  scanner.value?.start();
}

function handleBarcodeScan(barcode: string) {
  manualBarcode.value = barcode;
  validateBarcode();
}

async function validateBarcode() {
  if (!manualBarcode.value || !currentItem.value) return;
  
  try {
    const materialInfo = await materialAPI.getByBarcode(manualBarcode.value);
    if (materialInfo.materialId !== currentItem.value.materialId) {
      showToast('条码与物料不匹配');
      manualBarcode.value = '';
      return;
    }
    
    // 自动填充批次号（如果条码包含批次信息）
    if (materialInfo.isLotManaged && materialInfo.lotNumber) {
      lotNumber.value = materialInfo.lotNumber;
    }
  } catch (error) {
    showToast('条码验证失败');
    manualBarcode.value = '';
  }
}

async function confirmReceiving() {
  if (!currentItem.value) return;
  
  try {
    await inboundAPI.receive({
      inboundId: inbound.value.inboundId,
      itemId: currentItem.value.itemId,
      barcode: manualBarcode.value,
      quantity: receiveQuantity.value,
      lotNumber: lotNumber.value
    });
    
    // 更新本地数据
    currentItem.value.receivedQty += receiveQuantity.value;
    
    showToast('收货成功');
    closeReceivingModal();
    
    // 检查是否完成所有收货
    const allCompleted = inbound.value.items.every(
      item => item.receivedQty >= item.planQty
    );
    if (allCompleted) {
      await completeReceiving();
    }
  } catch (error) {
    showToast('收货失败');
  }
}

function closeReceivingModal() {
  showReceivingModal.value = false;
  scanner.value?.stop();
  manualBarcode.value = '';
  receiveQuantity.value = 0;
  lotNumber.value = '';
}
</script>
```

这份技术实现方案提供了：

1. **系统架构设计** - 整体技术架构和技术栈选择
2. **模块详细设计** - 每个功能模块的具体实现方案
3. **数据库设计** - 完整的表结构和索引设计
4. **API接口设计** - RESTful API规范和接口定义
5. **移动端实现** - PDA收货界面的完整实现
6. **状态管理** - 业务流程的状态机设计
7. **库存算法** - 库存更新的核心算法逻辑

现在让我调用MCP模块获取您的反馈：

<function_calls>
<invoke name="mcp_mcp-feedback-enhanced_interactive_feedback">
<parameter name="project_directory">.
</code_block_to_apply_changes_from>
</edit_instructions>


Assistant:<rewritten_file>
```
# WMS系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        PC[PC端管理界面]
        Mobile[移动端PDA]
        API_G[API网关]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        WMS[WMS核心服务]
        Report[报表服务]
        Alert[预警服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        ES[(ElasticSearch)]
    end
    
    subgraph "集成层"
        ERP[ERP系统]
        WCS[WCS系统]
        MQ[消息队列]
    end
    
    PC --> API_G
    Mobile --> API_G
    API_G --> Auth
    API_G --> WMS
    API_G --> Report
    API_G --> Alert
    
    WMS --> MySQL
    WMS --> Redis
    Report --> ES
    Alert --> MQ
    
    WMS <--> ERP
    WMS <--> WCS
```

### 技术栈选择

#### 前端技术栈
```typescript
// 核心框架
- Vue 3.5.13 (Composition API)
- Vben Admin 5.5.6 (企业级脚手架)
- Ant Design Vue 4.2.6 (UI组件库)
- TypeScript 5.8.3 (类型安全)

// 状态管理
- Pinia 3.0.2 (状态管理)
- @tanstack/vue-query 5.75.1 (服务端状态管理)

// 构建工具
- Vite 6.3.4 (构建工具)
- Turbo 2.5.2 (Monorepo构建系统)

// 工具库
- VueUse 13.1.0 (Composition API工具集)
- Day.js 1.11.13 (日期处理)
- Lodash-es 4.17.21 (工具函数)
- Crypto-JS 4.2.0 (加密)
```

#### 移动端技术栈
```typescript
// 移动端框架选择方案
方案一：Vue + Vant (推荐)
- Vue 3 + Vant 4 (移动端UI库)
- Vite + PWA (离线支持)
- 原生相机API (扫码功能)

方案二：Uniapp
- Vue 3 + UniApp (跨平台)
- 支持H5/微信小程序/App
- 原生插件支持

方案三：原生App
- React Native / Flutter
- 性能最优，开发成本高
```

## 📦 模块设计详解

### 1. 物料管理模块

#### 功能架构
```typescript
// 物料管理功能树
物料管理/
├── 物料主数据/
│   ├── 基本信息管理
│   ├── 规格参数管理
│   ├── 供应商关联
│   └── 图片附件管理
├── 物料分类/
│   ├── 多级分类树
│   ├── 分类属性配置
│   └── 分类权限控制
├── 条码管理/
│   ├── 条码生成规则
│   ├── 二维码生成
│   └── 标签打印
└── 导入导出/
    ├── Excel导入模板
    ├── 数据验证规则
    └── 批量操作日志
```

#### API设计
```typescript
// 物料API接口设计
export interface MaterialAPI {
  // 基础CRUD
  list(params: MaterialQuery): Promise<PageResult<MaterialVO>>;
  info(id: string): Promise<MaterialVO>;
  add(data: MaterialForm): Promise<void>;
  update(data: MaterialForm): Promise<void>;
  remove(ids: string[]): Promise<void>;
  
  // 扩展功能
  generateBarcode(id: string): Promise<string>;
  printLabel(ids: string[]): Promise<void>;
  import(file: File): Promise<ImportResult>;
  export(params: MaterialQuery): Promise<Blob>;
  
  // 关联查询
  getSuppliers(materialId: string): Promise<SupplierVO[]>;
  getCategories(): Promise<TreeNode[]>;
  checkCodeUnique(code: string): Promise<boolean>;
}

// 物料表单验证规则
export const materialRules = {
  materialCode: [
    { required: true, message: '物料编码不能为空' },
    { pattern: /^[A-Z0-9]{6,20}$/, message: '编码格式不正确' }
  ],
  materialName: [
    { required: true, message: '物料名称不能为空' },
    { max: 100, message: '名称长度不能超过100字符' }
  ],
  categoryId: [
    { required: true, message: '请选择物料分类' }
  ],
  unit: [
    { required: true, message: '请选择计量单位' }
  ]
};
```

#### 数据库设计
```sql
-- 物料主表
CREATE TABLE wms_material (
  material_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '物料ID',
  material_code VARCHAR(50) UNIQUE NOT NULL COMMENT '物料编码',
  material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
  material_name_en VARCHAR(200) COMMENT '英文名称',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  material_type VARCHAR(20) DEFAULT 'MATERIAL' COMMENT '物料类型',
  specification VARCHAR(500) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '基本单位',
  unit_weight DECIMAL(10,3) COMMENT '单位重量(kg)',
  unit_volume DECIMAL(10,3) COMMENT '单位体积(m³)',
  brand VARCHAR(100) COMMENT '品牌',
  model VARCHAR(100) COMMENT '型号',
  barcode VARCHAR(100) COMMENT '条码',
  qr_code VARCHAR(500) COMMENT '二维码内容',
  
  -- 库存控制
  is_lot_managed TINYINT(1) DEFAULT 0 COMMENT '是否批次管理',
  is_serial_managed TINYINT(1) DEFAULT 0 COMMENT '是否序列号管理',
  has_expiry_date TINYINT(1) DEFAULT 0 COMMENT '是否有保质期',
  shelf_life INT COMMENT '保质期(天)',
  
  -- 库存设置
  safety_stock DECIMAL(12,3) DEFAULT 0 COMMENT '安全库存',
  min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最小库存',
  max_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最大库存',
  reorder_point DECIMAL(12,3) DEFAULT 0 COMMENT '再订购点',
  reorder_qty DECIMAL(12,3) DEFAULT 0 COMMENT '建议订购量',
  
  -- ABC分析
  abc_class VARCHAR(10) COMMENT 'ABC分类',
  annual_usage DECIMAL(12,3) DEFAULT 0 COMMENT '年用量',
  unit_cost DECIMAL(10,4) DEFAULT 0 COMMENT '单位成本',
  
  -- 状态控制
  status CHAR(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
  
  -- 审计字段
  create_by VARCHAR(64) COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  remark VARCHAR(500) COMMENT '备注',
  
  INDEX idx_material_code(material_code),
  INDEX idx_category_id(category_id),
  INDEX idx_status(status),
  INDEX idx_create_time(create_time)
) COMMENT='物料主数据表';

-- 物料分类表
CREATE TABLE wms_material_category (
  category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID',
  category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_level INT DEFAULT 1 COMMENT '分类层级',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status CHAR(1) DEFAULT '0' COMMENT '状态',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_parent_id(parent_id),
  INDEX idx_category_code(category_code)
) COMMENT='物料分类表';

-- 物料扩展属性表
CREATE TABLE wms_material_attr (
  attr_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '属性ID',
  material_id BIGINT NOT NULL COMMENT '物料ID',
  attr_name VARCHAR(100) NOT NULL COMMENT '属性名称',
  attr_value VARCHAR(500) COMMENT '属性值',
  attr_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '属性类型',
  sort_order INT DEFAULT 0 COMMENT '排序',
  
  INDEX idx_material_id(material_id),
  FOREIGN KEY (material_id) REFERENCES wms_material(material_id)
) COMMENT='物料扩展属性表';
```

### 2. 货位管理模块

#### 货位编码规则设计
```typescript
// 货位编码规则配置
interface LocationCodeRule {
  warehouseCode: string;     // 仓库代码(2位) 如: WH
  zoneCode: string;          // 库区代码(2位) 如: A1, B2
  aisleCode: string;         // 巷道代码(2位) 如: 01, 02
  shelfCode: string;         // 货架代码(2位) 如: 01, 02
  levelCode: string;         // 层级代码(2位) 如: 01, 02, 03
  positionCode: string;      // 位置代码(2位) 如: 01, 02, 03
}

// 示例: WH-A1-01-01-02-03 
// 表示: WH仓库-A1库区-01巷道-01货架-02层-03位置

// 货位编码生成器
class LocationCodeGenerator {
  static generate(rule: LocationCodeRule): string {
    return [
      rule.warehouseCode,
      rule.zoneCode,
      rule.aisleCode,
      rule.shelfCode,
      rule.levelCode,
      rule.positionCode
    ].join('-');
  }
  
  static parse(locationCode: string): LocationCodeRule {
    const parts = locationCode.split('-');
    return {
      warehouseCode: parts[0],
      zoneCode: parts[1],
      aisleCode: parts[2],
      shelfCode: parts[3],
      levelCode: parts[4],
      positionCode: parts[5]
    };
  }
}
```

#### 货位可视化组件
```vue
<!-- 货位可视化组件 -->
<template>
  <div class="location-visualizer">
    <!-- 仓库平面图 -->
    <div class="warehouse-layout">
      <div 
        v-for="zone in zones" 
        :key="zone.id"
        class="zone"
        :style="getZoneStyle(zone)"
      >
        <div class="zone-header">{{ zone.name }}</div>
        <div class="shelves">
          <div
            v-for="shelf in zone.shelves"
            :key="shelf.id"
            class="shelf"
            :class="getShelfClass(shelf)"
            @click="selectShelf(shelf)"
          >
            <div class="shelf-code">{{ shelf.code }}</div>
            <div class="occupancy-rate">
              {{ Math.round(shelf.occupancyRate * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 货位详情面板 -->
    <LocationDetailPanel 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  shelves: Shelf[];
}

interface Shelf {
  id: string;
  code: string;
  occupancyRate: number;
  status: 'available' | 'occupied' | 'disabled';
  locations: Location[];
}

const zones = ref<Zone[]>([]);
const selectedLocation = ref<Location | null>(null);

const getZoneStyle = (zone: Zone) => ({
  left: `${zone.x}px`,
  top: `${zone.y}px`,
  width: `${zone.width}px`,
  height: `${zone.height}px`
});

const getShelfClass = (shelf: Shelf) => [
  'shelf',
  `shelf--${shelf.status}`,
  {
    'shelf--high-occupancy': shelf.occupancyRate > 0.8,
    'shelf--low-occupancy': shelf.occupancyRate < 0.2
  }
];
</script>
```

### 3. 库存管理模块

#### 库存数据模型设计
```typescript
// 库存核心模型
interface InventoryCore {
  // 基础信息
  inventoryId: string;
  warehouseId: string;
  locationId: string;
  materialId: string;
  
  // 批次信息
  lotNumber?: string;
  serialNumber?: string;
  manufactureDate?: Date;
  expiryDate?: Date;
  supplierBatch?: string;
  
  // 数量信息
  quantity: number;           // 总数量
  availableQty: number;       // 可用数量
  frozenQty: number;          // 冻结数量
  allocatedQty: number;       // 已分配数量
  inTransitQty: number;       // 在途数量
  damagedQty: number;         // 损坏数量
  
  // 质量状态
  qualityStatus: QualityStatus; // 合格/待检/不合格
  qualityLevel?: string;        // 质量等级
  
  // 成本信息
  unitCost: number;           // 单位成本
  totalCost: number;          // 总成本
  currency: string;           // 币种
  
  // 时间戳
  lastUpdateTime: Date;
  createTime: Date;
}

// 库存操作日志
interface InventoryTransaction {
  transactionId: string;
  inventoryId: string;
  transactionType: TransactionType; // 入库/出库/调整/冻结/解冻
  quantity: number;
  beforeQty: number;
  afterQty: number;
  sourceDocumentType: string;  // 源单类型
  sourceDocumentNo: string;    // 源单号
  operatorId: string;
  operationTime: Date;
  remark?: string;
}

// 库存预警规则
interface InventoryAlertRule {
  ruleId: string;
  materialId: string;
  warehouseId?: string;
  locationId?: string;
  
  // 预警类型
  alertType: AlertType; // 低库存/高库存/到期/呆滞
  
  // 预警条件
  minStockThreshold?: number;
  maxStockThreshold?: number;
  expiryDaysThreshold?: number;
  stagnantDaysThreshold?: number;
  
  // 预警级别
  alertLevel: AlertLevel; // 低/中/高/紧急
  
  // 通知设置
  notifyUsers: string[];
  notifyRoles: string[];
  notifyChannels: NotifyChannel[]; // 邮件/短信/站内信
  
  isActive: boolean;
  createTime: Date;
}
```

#### 库存更新策略
```typescript
// 库存更新服务
class InventoryUpdateService {
  
  /**
   * 库存入库
   */
  async inbound(params: {
    warehouseId: string;
    locationId: string;
    materialId: string;
    quantity: number;
    lotNumber?: string;
    unitCost: number;
    qualityStatus: QualityStatus;
    sourceDocumentType: string;
    sourceDocumentNo: string;
    operatorId: string;
  }): Promise<void> {
    return this.executeTransaction(async (trx) => {
      // 1. 查找或创建库存记录
      let inventory = await this.findInventory({
        warehouseId: params.warehouseId,
        locationId: params.locationId,
        materialId: params.materialId,
        lotNumber: params.lotNumber,
        qualityStatus: params.qualityStatus
      }, trx);
      
      if (!inventory) {
        inventory = await this.createInventory(params, trx);
      }
      
      // 2. 更新库存数量
      const beforeQty = inventory.quantity
