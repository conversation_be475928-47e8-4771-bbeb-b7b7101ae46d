var S=(L,p,r)=>new Promise((c,l)=>{var w=n=>{try{f(r.next(n))}catch(v){l(v)}},m=n=>{try{f(r.throw(n))}catch(v){l(v)}},f=n=>n.done?c(n.value):Promise.resolve(n.value).then(w,m);f((r=r.apply(L,p)).next())});import{z as $}from"./bootstrap-DCMzVRvD.js";import{J as A,X as P,l as J,T as O,V as W,g as j,m as X}from"./index-C0wIoq37.js";import{d as z,p as y,a3 as i,ab as E,l as R,h as x,o as h,w as b,c as U,f as V,j as k,a as T,b as d,L as G,t as H,k as K}from"../jse/index-index-C-MnMZEz.js";import{p as Q}from"./index-BntC6MFc.js";import{_ as Y}from"./code-mirror.vue_vue_type_script_setup_true_lang-DhrTT7Nl.js";import{S as Z}from"./index-BLwHKR_M.js";import{u as ee}from"./use-modal-CeMSCP2m.js";import{T as te}from"./index-DdFaZtFR.js";import{A as ae}from"./index-kC0HFDdy.js";import"./index-D6-099PU.js";import"./helper-Bc7QQ92Q.js";import"./x-Bfkqqjgb.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./index-CHpIOV4R.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";const oe={key:0,class:"flex gap-[8px]"},le={class:"h-[calc(100vh-80px)] w-[300px] overflow-y-scroll"},ne={class:"flex items-center gap-[16px]"},se={class:"fixed right-20 top-20"},re=z({__name:"code-preview-modal",setup(L){const p=y([]),r=y("代码预览"),c=y("点击左侧树节点查看代码"),l=y(null),[w,m]=ee({onOpenChange(o){return S(this,null,function*(){if(!o)return q(),null;m.modalLoading(!0);const{tableId:e}=m.getData(),t=yield Q(e);l.value=t;const a=f(Object.keys(t));p.value=a,m.modalLoading(!1)})}});function f(o){const e=[];for(const t of o){const a=t.split("/");let s=e,u="";for(let _=0;_<a.length;_++){const g=a[_];u+=`${g}`,_!==a.length-1&&(u+="/");const N=s.find(I=>I.title===g);if(N)s=N.children||[];else{const I=(g!=null?g:"").replace(".vm",""),D={icon:v(u),key:u,title:I,children:[]};s.push(D),s=D.children}}}return e}const n=[{key:"java",value:i(A)},{key:"xml",value:i(P)},{key:"sql",value:i(J)},{key:"ts",value:i(O)},{key:"vue",value:i(W)},{key:"folder",value:i(j)}];function v(o){const e=X,t=j;if(o.endsWith(".vm")){const a=o.slice(0,-3),s=n.find(u=>a.endsWith(u.key));return s?s.value:e}return t}const C=y("html");function B(o){const t=[{language:"ts",type:".ts"},{language:"java",type:".java"},{language:"xml",type:".xml"},{language:"sql",type:"sql"},{language:"vue",type:".vue"}].find(a=>o.includes(a.type));C.value=t?t.language:"html"}function F(o){const[e=""]=o;if(!l.value)return;const t=l.value[e];t&&(B(e),c.value=t,r.value=`代码预览: ${e.replace(".vm","")}`)}function q(){l.value=null,c.value="点击左侧树节点查看代码",r.value="代码预览",C.value="html"}const{copy:M}=E({legacy:!0});return(o,e)=>{const t=R("a-button");return h(),x(d(w),{footer:!1,fullscreen:!0,"fullscreen-button":!1,title:r.value},{default:b(()=>[l.value?(h(),U("div",oe,[k("div",le,[p.value.length>0?(h(),x(d(te),{key:0,"show-line":{showLeafIcon:!1},"tree-data":p.value,virtual:!1,"default-expand-all":"",onSelect:F},{title:b(({title:a,icon:s})=>[k("div",ne,[(h(),x(G(s))),k("span",null,H(a),1)])]),_:1},8,["tree-data"])):V("",!0),T(d(ae),{class:"mt-2","show-icon":"",message:"👆显示的名称为模板的文件名，非最终下载文件名..."})]),T(d(Y),{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=a=>c.value=a),language:C.value,class:"h-[calc(100vh-80px)] w-full overflow-y-scroll text-[16px]",readonly:""},null,8,["modelValue","language"]),k("div",se,[T(t,{onClick:e[1]||(e[1]=a=>d(M)(c.value))},{default:b(()=>e[2]||(e[2]=[K("复制")])),_:1,__:[2]})])])):V("",!0),l.value?V("",!0):(h(),x(d(Z),{key:1,active:""}))]),_:1},8,["title"])}}}),Ve=$(re,[["__scopeId","data-v-eb0f6209"]]);export{Ve as default};
