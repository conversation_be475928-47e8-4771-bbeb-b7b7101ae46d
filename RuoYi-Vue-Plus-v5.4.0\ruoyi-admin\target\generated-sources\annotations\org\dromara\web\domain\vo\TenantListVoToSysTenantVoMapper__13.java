package org.dromara.web.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.domain.vo.SysTenantVoToTenantListVoMapper__13;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysTenantVoToTenantListVoMapper__13.class},
    imports = {}
)
public interface TenantListVoToSysTenantVoMapper__13 extends BaseMapper<TenantListVo, SysTenantVo> {
}
