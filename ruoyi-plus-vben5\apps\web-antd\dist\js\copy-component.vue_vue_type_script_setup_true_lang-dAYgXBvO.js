import{T as d}from"./bootstrap-DCMzVRvD.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import w from"./user-select-modal-9zMWfXzj.js";import{G as x,A as L}from"./index-BELOxkuV.js";import{_ as M}from"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import{d as A,m as c,u as B,B as V,l as F,c as f,o as l,h as u,f as h,a as r,b as s,w as a,F as $,K as z,j as I,k as v,t as S}from"../jse/index-index-C-MnMZEz.js";import{u as T}from"./use-modal-CeMSCP2m.js";const U={class:"flex items-center gap-2"},H=A({name:"CopyComponent",inheritAttrs:!1,__name:"copy-component",props:c({allowUserIds:{default:""},ellipseNumber:{default:3}},{userList:{type:Array,default:()=>[]},userListModifiers:{}}),emits:c(["cancel","finish"],["update:userList"]),setup(m,{emit:b}){const i=m,_=b,[g,p]=T({connectedComponent:w}),e=B(m,"userList");function y(){p.setData({userList:e.value}),p.open()}function N(t){e.value.splice(0,e.value.length),e.value.push(...t),_("finish",t)}const k=V(()=>e.value.slice(0,i.ellipseNumber));return(t,o)=>{const C=F("a-button");return l(),f("div",U,[e.value.length>0?(l(),u(s(x),{key:0},{default:a(()=>[(l(!0),f($,null,z(k.value,n=>(l(),u(s(d),{key:n.userId,title:n.nickName,placement:"top"},{default:a(()=>[I("div",null,[r(s(M),{alt:n.nickName,class:"bg-primary size-[36px] cursor-pointer rounded-full border text-white",src:""},null,8,["alt"])])]),_:2},1032,["title"]))),128)),r(s(d),{title:`等${e.value.length-i.ellipseNumber}人`,placement:"top"},{default:a(()=>[e.value.length>t.ellipseNumber?(l(),u(s(L),{key:0,class:"flex size-[36px] cursor-pointer items-center justify-center rounded-full border bg-[gray] text-white"},{default:a(()=>[v(" +"+S(e.value.length-i.ellipseNumber),1)]),_:1})):h("",!0)]),_:1},8,["title"])]),_:1})):h("",!0),r(C,{size:"small",onClick:y},{default:a(()=>o[1]||(o[1]=[v("选择人员")])),_:1,__:[1]}),r(s(g),{"allow-user-ids":t.allowUserIds,onCancel:o[0]||(o[0]=n=>t.$emit("cancel")),onFinish:N},null,8,["allow-user-ids"])])}}});export{H as _};
