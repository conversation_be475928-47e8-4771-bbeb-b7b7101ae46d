import{h as r,g as f}from"./bootstrap-DCMzVRvD.js";import{d as h,C as O,a as w,p as b,q as k,B as S}from"../jse/index-index-C-MnMZEz.js";import{s as z}from"./css-Dmgy8YJo.js";var K=function(n,s){var l={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&s.indexOf(t)<0&&(l[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(n);a<t.length;a++)s.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(n,t[a])&&(l[t[a]]=n[t[a]]);return l};const M=h({compatConfig:{MODE:3},props:{disabled:r.looseBool,type:r.string,value:r.any,tag:{type:String,default:"input"},size:r.string,onChange:Function,onInput:Function,onBlur:Function,onFocus:Function,onKeydown:Function,onCompositionstart:Function,onCompositionend:Function,onKeyup:Function,onPaste:Function,onMousedown:Function},emits:["change","input","blur","keydown","focus","compositionstart","compositionend","keyup","paste","mousedown"],setup(n,s){let{expose:l}=s;const t=O(null);return l({focus:()=>{t.value&&t.value.focus()},blur:()=>{t.value&&t.value.blur()},input:t,setSelectionRange:(o,c,v)=>{var d;(d=t.value)===null||d===void 0||d.setSelectionRange(o,c,v)},select:()=>{var o;(o=t.value)===null||o===void 0||o.select()},getSelectionStart:()=>{var o;return(o=t.value)===null||o===void 0?void 0:o.selectionStart},getSelectionEnd:()=>{var o;return(o=t.value)===null||o===void 0?void 0:o.selectionEnd},getScrollTop:()=>{var o;return(o=t.value)===null||o===void 0?void 0:o.scrollTop}}),()=>{const{tag:o,value:c}=n,v=K(n,["tag","value"]);return w(o,f(f({},v),{},{ref:t,value:c}),null)}}});var D=function(n,s){var l={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&s.indexOf(t)<0&&(l[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(n);a<t.length;a++)s.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(n,t[a])&&(l[t[a]]=n[t[a]]);return l};const H=h({compatConfig:{MODE:3},inheritAttrs:!1,props:{disabled:r.looseBool,type:r.string,value:r.any,lazy:r.bool.def(!0),tag:{type:String,default:"input"},size:r.string,style:r.oneOfType([String,Object]),class:r.string},emits:["change","input","blur","keydown","focus","compositionstart","compositionend","keyup","paste","mousedown"],setup(n,s){let{emit:l,attrs:t,expose:a}=s;const u=O(null),y=b(),i=b(!1);k([()=>n.value,i],()=>{i.value||(y.value=n.value)},{immediate:!0});const o=e=>{l("change",e)},c=e=>{i.value=!0,e.target.composing=!0,l("compositionstart",e)},v=e=>{i.value=!1,e.target.composing=!1,l("compositionend",e);const p=document.createEvent("HTMLEvents");p.initEvent("input",!0,!0),e.target.dispatchEvent(p),o(e)},d=e=>{if(i.value&&n.lazy){y.value=e.target.value;return}l("input",e)},_=e=>{l("blur",e)},C=e=>{l("focus",e)},F=()=>{u.value&&u.value.focus()},E=()=>{u.value&&u.value.blur()},P=e=>{l("keydown",e)},j=e=>{l("keyup",e)},B=(e,p,g)=>{var m;(m=u.value)===null||m===void 0||m.setSelectionRange(e,p,g)},T=()=>{var e;(e=u.value)===null||e===void 0||e.select()};a({focus:F,blur:E,input:S(()=>{var e;return(e=u.value)===null||e===void 0?void 0:e.input}),setSelectionRange:B,select:T,getSelectionStart:()=>{var e;return(e=u.value)===null||e===void 0?void 0:e.getSelectionStart()},getSelectionEnd:()=>{var e;return(e=u.value)===null||e===void 0?void 0:e.getSelectionEnd()},getScrollTop:()=>{var e;return(e=u.value)===null||e===void 0?void 0:e.getScrollTop()}});const I=e=>{l("mousedown",e)},R=e=>{l("paste",e)},x=S(()=>n.style&&typeof n.style!="string"?z(n.style):n.style);return()=>{const{style:e,lazy:p}=n,g=D(n,["style","lazy"]);return w(M,f(f(f({},g),t),{},{style:x.value,onInput:d,onChange:o,onBlur:_,onFocus:C,ref:u,value:y.value,onCompositionstart:c,onCompositionend:v,onKeyup:j,onKeydown:P,onPaste:R,onMousedown:I}),null)}}});export{H as B};
