import{y as s}from"./bootstrap-DCMzVRvD.js";function e(t){return s.post("/workflow/task/startWorkFlow",t)}function r(t){return s.postWithMsg("/workflow/task/completeTask",t)}function k(t){return s.get("/workflow/task/pageByTaskWait",{params:t})}function n(t){return s.get("/workflow/task/pageByTaskFinish",{params:t})}function i(t){return s.get("/workflow/task/pageByAllTaskWait",{params:t})}function u(t){return s.get("/workflow/task/pageByAllTaskFinish",{params:t})}function g(t){return s.get("/workflow/task/pageByTaskCopy",{params:t})}function f(t){return s.get(`/workflow/task/getTask/${t}`)}function w(t){return s.postWithMsg("/workflow/task/terminationTask",t)}function p(t,a){return s.postWithMsg(`/workflow/task/taskOperation/${a}`,t)}function l(t,a){return s.putWithMsg(`/workflow/task/updateAssignee/${a}`,t)}function c(t){return s.postWithMsg("/workflow/task/backProcess",t)}function T(t,a){return s.get(`/workflow/task/getBackTaskNode/${t}/${a}`)}function y(t){return s.post("/workflow/task/getNextNodeList",t)}export{y as a,T as b,r as c,c as d,w as e,n as f,f as g,k as h,i,u as j,g as p,e as s,p as t,l as u};
