{"name": "@vben/stylelint-config", "version": "5.5.6", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/lint-configs/stylelint-config"}, "license": "MIT", "type": "module", "files": ["dist"], "main": "./index.mjs", "module": "./index.mjs", "exports": {".": {"import": "./index.mjs", "default": "./index.mjs"}}, "dependencies": {"@stylistic/stylelint-plugin": "catalog:", "stylelint-config-recess-order": "catalog:", "stylelint-scss": "catalog:"}, "devDependencies": {"postcss": "catalog:", "postcss-html": "catalog:", "postcss-scss": "catalog:", "prettier": "catalog:", "stylelint": "catalog:", "stylelint-config-recommended": "catalog:", "stylelint-config-recommended-scss": "catalog:", "stylelint-config-recommended-vue": "catalog:", "stylelint-config-standard": "catalog:", "stylelint-order": "catalog:", "stylelint-prettier": "catalog:"}}