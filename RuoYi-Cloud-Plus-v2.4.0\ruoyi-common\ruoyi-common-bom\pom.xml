<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        ruoyi-common-bom common依赖项
    </description>

    <properties>
        <revision>2.4.0</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 权限认证服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 通用service实现模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-service-impl</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- RPC服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-seata</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 自定义负载均衡 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-loadbalancer</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- oss服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流功能 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等功能 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 邮件模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 短信模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- logstash日志推送模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-logstash</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- ES搜索引擎服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-sentinel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- skywalking日志收集模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-skylog</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- prometheus监控 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-prometheus</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 通用翻译功能 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据加解密模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- websocket模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 授权认证 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 配置中心 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-nacos</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息总线模块 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-bus</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- sse -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-sse</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
