package org.dromara.wms.mapper;

import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 仓库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface WmsWarehouseMapper extends BaseMapperPlus<WmsWarehouse, WmsWarehouseVo> {

    /**
     * 查询仓库关联的部门ID列表
     *
     * @param warehouseId 仓库ID
     * @return 部门ID列表
     */
    List<Long> selectDeptIdsByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 查询仓库关联的部门名称
     *
     * @param warehouseId 仓库ID
     * @return 部门名称，多个用逗号分隔
     */
    String selectDeptNamesByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 查询用户可访问的仓库列表（基于用户部门权限）
     *
     * @param userId 用户ID
     * @return 仓库列表
     */
    List<WmsWarehouseVo> selectUserAccessibleWarehouses(@Param("userId") Long userId);

}
