var n=(f,t,o)=>new Promise((e,p)=>{var a=r=>{try{m(o.next(r))}catch(s){p(s)}},i=r=>{try{m(o.throw(r))}catch(s){p(s)}},m=r=>r.done?e(r.value):Promise.resolve(r.value).then(a,i);m((o=o.apply(f,t)).next())});import{b as _}from"./index-DLUQE3Et.js";import{am as h,bb as x}from"./bootstrap-DCMzVRvD.js";import{e as l}from"./base-setting.vue_vue_type_script_setup_true_lang-BL_DVXFu.js";import{_ as v}from"./profile-panel.vue_vue_type_script_setup_true_lang-B6zejZQO.js";import{_ as w}from"./setting-panel.vue_vue_type_script_setup_true_lang-CksTTCaa.js";import{_ as U}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as b,p as y,v as c,y as P,h as u,o as d,w as S,j as k,a as B,f as C,b as I}from"../jse/index-index-C-MnMZEz.js";import"./uuid-B0AYzFfo.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./mitt-D4bcW7KS.js";import"./pick-CyUZAAhv.js";import"./index-BeyziwLP.js";import"./index-BELOxkuV.js";import"./index-CHpIOV4R.js";import"./eagerComputed-CeBU4kWY.js";import"./index-qvRUEWLR.js";import"./use-modal-CeMSCP2m.js";import"./x-Bfkqqjgb.js";import"./index-D59rZjD-.js";import"./index-B6iusSRX.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./isMobile-8sZ0LT6r.js";import"./index-BLwHKR_M.js";import"./account-bind-DVRafLmQ.js";import"./oauth-common-CrHfL2p7.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./index-Ollxi7Rl.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-CIjgbPOA.js";import"./index-B-GBMyZJ.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./List-DFkqSBvs.js";import"./DownOutlined-CERO2SW5.js";import"./SearchOutlined-BOD_ZIye.js";import"./move-DLDqWE9R.js";import"./useMemo-BwJyMulH.js";import"./statusUtils-d85DZFMd.js";import"./index-CFj2VWFk.js";import"./use-vxe-grid-BC7vZzEr.js";import"./init-C8TKSdFQ.js";import"./index-kC0HFDdy.js";import"./online-device.vue_vue_type_script_setup_true_lang-DS8ioiKf.js";import"./vxe-table-DzEj5Fop.js";import"./index-BY49C_DM.js";import"./data-DW-5ziL3.js";import"./render-BxXtQdeV.js";import"./data-1kX019oc.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./index-DNdMANjv.js";import"./secure-setting.vue_vue_type_script_setup_true_lang-D2jcoVG-.js";const N={class:"flex flex-col gap-[16px] lg:flex-row"},Do=b({__name:"index",setup(f){const t=y();function o(){return n(this,null,function*(){const i=yield _();t.value=i})}c(o);const e=h(),p=x();function a(){return n(this,null,function*(){yield o();const i=yield e.fetchUserInfo();p.setUserInfo(i)})}return c(()=>l.on("updateProfile",o)),P(()=>l.off("updateProfile")),(i,m)=>(d(),u(I(U),null,{default:S(()=>[k("div",N,[B(v,{profile:t.value,onUploadFinish:a},null,8,["profile"]),t.value?(d(),u(w,{key:0,profile:t.value,class:"flex-1 overflow-hidden"},null,8,["profile"])):C("",!0)])]),_:1}))}});export{Do as default};
