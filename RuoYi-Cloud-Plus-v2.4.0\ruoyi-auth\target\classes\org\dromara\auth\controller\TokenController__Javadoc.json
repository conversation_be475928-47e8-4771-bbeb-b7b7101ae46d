{"doc": " token 控制\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["java.lang.String"], "doc": " 登录方法\n\n @param body 登录信息\n @return 结果\n"}, {"name": "auth<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 第三方登录请求\n\n @param source 登录来源\n @return 结果\n"}, {"name": "socialCallback", "paramTypes": ["org.dromara.auth.form.SocialLoginBody"], "doc": " 第三方登录回调业务处理 绑定授权\n\n @param loginBody 请求体\n @return 结果\n"}, {"name": "unlockSocial", "paramTypes": ["java.lang.Long"], "doc": " 取消授权\n\n @param socialId socialId\n"}, {"name": "logout", "paramTypes": [], "doc": " 登出方法\n"}, {"name": "register", "paramTypes": ["org.dromara.auth.form.RegisterBody"], "doc": " 用户注册\n"}, {"name": "tenantList", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 登录页面租户下拉框\n\n @return 租户列表\n"}], "constructors": []}