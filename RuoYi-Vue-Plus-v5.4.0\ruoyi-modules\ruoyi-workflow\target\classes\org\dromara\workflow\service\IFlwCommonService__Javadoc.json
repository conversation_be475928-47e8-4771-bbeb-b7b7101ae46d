{"doc": " 通用 工作流服务\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildUser", "paramTypes": ["java.util.List"], "doc": " 构建工作流用户\n\n @param permissionList 办理用户\n @return 用户\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.Long", "java.util.List", "java.lang.String"], "doc": " 发送消息\n\n @param flowName    流程定义名称\n @param instId      实例id\n @param messageType 消息类型\n @param message     消息内容，为空则发送默认配置的消息内容\n"}, {"name": "applyNodeCode", "paramTypes": ["java.lang.Long"], "doc": " 申请人节点编码\n\n @param definitionId 流程定义id\n @return 申请人节点编码\n"}], "constructors": []}