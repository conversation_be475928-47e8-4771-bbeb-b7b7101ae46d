import{_ as i}from"./file-upload.vue_vue_type_script_setup_true_lang-Cat_icI0.js";import"./image-upload.vue_vue_type_style_index_0_lang-DExXFAky.js";import"./bootstrap-DCMzVRvD.js";import{d as c,p as f,h as m,o as d,w as p,j as _,a as v,b as l}from"../jse/index-index-C-MnMZEz.js";import{u as x}from"./use-modal-CeMSCP2m.js";const b={class:"flex flex-col gap-4"},w=c({__name:"file-upload-modal",emits:["reload"],setup(g,{emit:t}){const s=t,e=f([]),[n,r]=x({onOpenChange:a=>{if(a)return null;if(e.value.length>0)return e.value=[],s("reload"),r.close(),null}});return(a,o)=>(d(),m(l(n),{"close-on-click-modal":!1,footer:!1,"fullscreen-button":!1,title:"文件上传"},{default:p(()=>[_("div",b,[v(l(i),{value:e.value,"onUpdate:value":o[0]||(o[0]=u=>e.value=u),"enable-drag-upload":!0,"max-count":3},null,8,["value"])])]),_:1}))}});export{w as _};
