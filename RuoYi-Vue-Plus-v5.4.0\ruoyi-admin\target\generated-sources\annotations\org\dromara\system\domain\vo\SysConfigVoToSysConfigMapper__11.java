package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysConfigToSysConfigVoMapper__11.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__11 extends BaseMapper<SysConfigVo, SysConfig> {
}
