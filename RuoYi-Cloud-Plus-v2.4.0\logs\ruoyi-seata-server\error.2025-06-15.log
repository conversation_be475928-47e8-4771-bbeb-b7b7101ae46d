2025-06-15 22:33:57 [SpringApplicationShutdownHook] ERROR o.a.s.c.r.netty.NettyServerBootstrap - shutdown execute error: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused: no further information
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:565)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:507)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:502)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:280)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at org.apache.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:122)
	at org.apache.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:194)
	at org.apache.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:132)
	at org.apache.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:129)
	at org.apache.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:681)
	at org.apache.seata.server.ServerRunner.destroy(ServerRunner.java:90)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
