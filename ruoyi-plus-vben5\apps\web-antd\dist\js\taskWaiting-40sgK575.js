var J=Object.defineProperty,Q=Object.defineProperties;var X=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var $=(n,i,a)=>i in n?J(n,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[i]=a,_=(n,i)=>{for(var a in i||(i={}))Y.call(i,a)&&$(n,a,i[a]);if(U)for(var a of U(i))Z.call(i,a)&&$(n,a,i[a]);return n},S=(n,i)=>Q(n,X(i));var x=(n,i,a)=>new Promise((g,v)=>{var c=m=>{try{r(a.next(m))}catch(b){v(b)}},C=m=>{try{r(a.throw(m))}catch(b){v(b)}},r=m=>m.done?g(m.value):Promise.resolve(m.value).then(c,C);r((a=a.apply(n,i)).next())});import{c as ee}from"./index-B4NcjlQn.js";import{h as D}from"./index-CZhogUxH.js";import te from"./approval-card-6R4jdcAw.js";import{_ as oe}from"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import{au as P,aw as j,bL as ae,T as le,aq as re,bn as w,z as ie}from"./bootstrap-DCMzVRvD.js";import{_ as se}from"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{b as pe,R as ne,F as ue}from"./constant-BxyJFj0E.js";import"./index-BxBCzu2M.js";import{a as me,I as z}from"./Search-ClCped_G.js";import{S as de}from"./index-Ollxi7Rl.js";import{_ as fe}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as ve,p as d,B as ce,E as ge,v as O,l as be,h as F,o as y,w as s,j as f,a as l,b as o,k as V,c as B,f as M,F as ye,K as ke,t as _e}from"../jse/index-index-C-MnMZEz.js";import{u as xe}from"./use-tabs-Zz_nc_n2.js";import{a as we}from"./tree-DFBawhPd.js";import{P as Ce}from"./index-qvRUEWLR.js";import{a as W}from"./get-popup-container-P4S1sr5h.js";import Ne from"./index-B1dOBW9y.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./relativeTime-DEqmspR6.js";import"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import"./index-D59rZjD-.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-DCFckLr6.js";import"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import"./use-modal-CeMSCP2m.js";import"./x-Bfkqqjgb.js";import"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import"./index-BLwHKR_M.js";import"./index-i2_yEmR1.js";import"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import"./user-select-modal-9zMWfXzj.js";import"./index-ocPq22VW.js";import"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./use-vxe-grid-BC7vZzEr.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./LeftOutlined-DE4sX_Jv.js";import"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BeyziwLP.js";import"./index-BIMmoqOy.js";import"./move-DLDqWE9R.js";import"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import"./rotate-cw-DzZTu9nW.js";import"./index-BELOxkuV.js";import"./useMemo-BwJyMulH.js";import"./index-B-GBMyZJ.js";import"./DownOutlined-CERO2SW5.js";const Te={class:"flex h-full gap-2"},he={class:"bg-background relative flex h-full min-w-[320px] max-w-[320px] flex-col rounded-lg"},Ie={class:"bg-background z-100 sticky left-0 top-0 w-full rounded-t-lg border-b-[1px] border-solid p-2"},Se={class:"flex items-center gap-1"},Fe={class:"flex"},Be={key:2,class:"flex items-center justify-center text-[14px] opacity-50"},Ee={key:3,class:"absolute left-0 top-0 flex h-full w-full items-center justify-center bg-[rgba(0,0,0,0.1)]"},Re={class:"bg-background sticky bottom-0 w-full rounded-b-lg border-t-[1px] py-2"},Le={class:"flex items-center justify-center"},Ue=ve({__name:"taskWaiting",setup(n){const i=P.PRESENTED_IMAGE_SIMPLE,a=d([]),g=d(0),v=d(1),c=d(!1),C={flowName:"",nodeName:"",flowCode:"",createByIds:[],category:null},r=d(j(C)),m=ce(()=>a.value.length===g.value),b=ge("cardContainerRef");function k(p=!1){return x(this,null,function*(){var u;(u=b.value)==null||u.scroll({top:0,behavior:"auto"}),v.value=1,N.value=void 0,g.value=0,I.value="",p&&(r.value=j(C),h.value=[]),c.value=!0;const e=yield D(_({pageSize:10,pageNum:v.value},r.value));if(a.value=e.rows.map(t=>S(_({},t),{active:!1})),g.value=e.total,c.value=!1,a.value.length>0){const t=a.value[0];N.value=t,R(t)}})}O(k);const E=ae(p=>x(null,null,function*(){if(!p.target)return;const{scrollTop:e,clientHeight:u,scrollHeight:t}=p.target;if(e+u>=t-pe&&!m.value){c.value=!0,v.value+=1;const G=yield D(_({pageSize:10,pageNum:v.value},r.value));a.value.push(...G.rows.map(K=>S(_({},K),{active:!1}))),c.value=!1}}),200),I=d(""),N=d();function R(p){return x(this,null,function*(){const{id:e}=p;I.value!==e&&(N.value=p,a.value.forEach(u=>{u.active=u.id===e}),I.value=e)})}const{refreshTab:A}=xe(),T=d(!1),h=d([]);function H(p){T.value=!0,h.value=p,r.value.createByIds=p.map(e=>e.userId)}const L=d([]);return O(()=>x(null,null,function*(){const p=yield ee();we(p,"label"," / "),L.value=p})),(p,e)=>{const u=be("a-button");return y(),F(o(fe),{"auto-content-height":!0},{default:s(()=>[f("div",Te,[f("div",he,[f("div",Ie,[f("div",Se,[l(o(me),{value:r.value.flowName,"onUpdate:value":e[0]||(e[0]=t=>r.value.flowName=t),placeholder:"流程名称搜索",onSearch:e[1]||(e[1]=t=>k(!1))},null,8,["value"]),l(o(le),{placement:"top",title:"重置"},{default:s(()=>[l(u,{onClick:e[2]||(e[2]=t=>k(!0))},{default:s(()=>[l(o(ne))]),_:1})]),_:1}),l(o(Ce),{open:T.value,"onUpdate:open":e[10]||(e[10]=t=>T.value=t),"get-popup-container":o(W),placement:"rightTop",trigger:"click"},{title:s(()=>e[12]||(e[12]=[f("div",{class:"w-full border-b pb-[12px] text-[16px]"},"搜索",-1)])),content:s(()=>[l(o(re),{colon:!1,"label-col":{span:6},model:r.value,autocomplete:"off",class:"w-[300px]",onFinish:e[9]||(e[9]=()=>k(!1))},{default:s(()=>[l(o(w),{label:"申请人"},{default:s(()=>[l(o(se),{"user-list":h.value,"onUpdate:userList":e[3]||(e[3]=t=>h.value=t),onCancel:e[4]||(e[4]=()=>T.value=!0),onFinish:H},null,8,["user-list"])]),_:1}),l(o(w),{label:"流程分类"},{default:s(()=>[l(o(Ne),{value:r.value.category,"onUpdate:value":e[5]||(e[5]=t=>r.value.category=t),"allow-clear":!0,"field-names":{label:"label",value:"id"},"get-popup-container":o(W),"tree-data":L.value,"tree-default-expand-all":!0,"tree-line":{showLeafIcon:!1},placeholder:"请选择","tree-node-filter-prop":"label","tree-node-label-prop":"fullName"},null,8,["value","get-popup-container","tree-data"])]),_:1}),l(o(w),{label:"任务名称"},{default:s(()=>[l(o(z),{value:r.value.nodeName,"onUpdate:value":e[6]||(e[6]=t=>r.value.nodeName=t),placeholder:"请输入"},null,8,["value"])]),_:1}),l(o(w),{label:"流程编码"},{default:s(()=>[l(o(z),{value:r.value.flowCode,"onUpdate:value":e[7]||(e[7]=t=>r.value.flowCode=t),placeholder:"请输入"},null,8,["value"])]),_:1}),l(o(w),null,{default:s(()=>[f("div",Fe,[l(u,{block:"","html-type":"submit",type:"primary"},{default:s(()=>e[13]||(e[13]=[V(" 搜索 ")])),_:1,__:[13]}),l(u,{block:"",class:"ml-2",onClick:e[8]||(e[8]=t=>k(!0))},{default:s(()=>e[14]||(e[14]=[V(" 重置 ")])),_:1,__:[14]})])]),_:1})]),_:1},8,["model"])]),default:s(()=>[l(u,null,{default:s(()=>[l(o(ue))]),_:1})]),_:1},8,["open","get-popup-container"])])]),f("div",{ref_key:"cardContainerRef",ref:b,class:"thin-scrollbar flex flex-1 flex-col gap-2 overflow-y-auto py-3",onScroll:e[11]||(e[11]=(...t)=>o(E)&&o(E)(...t))},[a.value.length>0?(y(!0),B(ye,{key:0},ke(a.value,t=>(y(),F(o(te),{key:t.id,info:t,class:"mx-2",onClick:q=>R(t)},null,8,["info","onClick"]))),128)):(y(),F(o(P),{key:1,image:o(i)},null,8,["image"])),m.value&&a.value.length>0?(y(),B("div",Be," 没有更多数据了 ")):M("",!0),c.value?(y(),B("div",Ee,[l(o(de),{tip:"加载中..."})])):M("",!0)],544),f("div",Re,[f("div",Le," 共 "+_e(g.value)+" 条记录 ",1)])]),l(o(oe),{task:N.value,type:"approve",onReload:o(A)},null,8,["task","onReload"])])]),_:1})}}}),mo=ie(Ue,[["__scopeId","data-v-db14c089"]]);export{mo as default};
