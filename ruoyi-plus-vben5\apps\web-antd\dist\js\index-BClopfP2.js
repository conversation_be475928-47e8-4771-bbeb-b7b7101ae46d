var E=Object.defineProperty;var D=Object.getOwnPropertySymbols;var T=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var $=(n,o,t)=>o in n?E(n,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[o]=t,S=(n,o)=>{for(var t in o||(o={}))T.call(o,t)&&$(n,t,o[t]);if(D)for(var t of D(o))G.call(o,t)&&$(n,t,o[t]);return n};var d=(n,o,t)=>new Promise((x,b)=>{var i=p=>{try{m(t.next(p))}catch(g){b(g)}},_=p=>{try{m(t.throw(p))}catch(g){b(g)}},m=p=>p.done?x(p.value):Promise.resolve(p.value).then(i,_);m((t=t.apply(n,o)).next())});import{as as M,an as N}from"./bootstrap-DCMzVRvD.js";import{v as O}from"./vxe-table-DzEj5Fop.js";import{p as z,a as I,b as F}from"./index-ErD4UjKl.js";import{c as L}from"./download-UJak946_.js";import{_ as j}from"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import{q as U,c as W,_ as H}from"./post-drawer.vue_vue_type_script_setup_true_lang-C7AbaedK.js";import P from"./index-BeyziwLP.js";import{_ as J}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as K,p as Q,l as q,S as X,h as c,o as u,w as s,a as f,b as a,T as C,k as v,t as h}from"../jse/index-index-C-MnMZEz.js";import{u as Y}from"./use-vxe-grid-BC7vZzEr.js";import{u as Z}from"./use-drawer-6qcpK-D1.js";import{P as ee}from"./index-DNdMANjv.js";import{g as oe}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./index-ocPq22VW.js";import"./index-BLwHKR_M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-BxBCzu2M.js";import"./index-CHpIOV4R.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./popup-D6rC6QBG.js";import"./dict-BLkXAGS5.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./tree-DFBawhPd.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Ke=K({__name:"index",setup(n){const o=Q([]),t={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:U(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",handleReset:()=>d(null,null,function*(){o.value=[];const{formApi:r,reload:e}=i;yield r.resetForm();const l=r.form.values;r.setLatestSubmissionValues(l),yield e(l)})},x={checkboxConfig:{highlight:!0,reserve:!0,trigger:"cell"},columns:W,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(l,...k)=>d(null,[l,...k],function*({page:r},e={}){return o.value.length===1?e.belongDeptId=o.value[0]:Reflect.deleteProperty(e,"belongDeptId"),yield z(S({pageNum:r.currentPage,pageSize:r.pageSize},e))})}},rowConfig:{keyField:"postId"},id:"system-post-index"},[b,i]=Y({formOptions:t,gridOptions:x}),[_,m]=Z({connectedComponent:H});function p(){m.setData({}),m.open()}function g(r){return d(this,null,function*(){m.setData({id:r.postId}),m.open()})}function R(r){return d(this,null,function*(){yield I([r.postId]),yield i.query()})}function V(){const e=i.grid.getCheckboxRecords().map(l=>l.postId);N.confirm({title:"提示",okType:"danger",content:`确认删除选中的${e.length}条记录吗？`,onOk:()=>d(null,null,function*(){yield I(e),yield i.query()})})}function A(){L(F,"岗位信息",i.formApi.form.values)}return(r,e)=>{const l=q("a-button"),k=q("GhostButton"),y=X("access");return u(),c(a(J),{"auto-content-height":!0,"content-class":"flex gap-[8px] w-full"},{default:s(()=>[f(j,{"select-dept-id":o.value,"onUpdate:selectDeptId":e[0]||(e[0]=w=>o.value=w),class:"w-[260px]",onReload:e[1]||(e[1]=()=>a(i).reload()),onSelect:e[2]||(e[2]=()=>a(i).reload())},null,8,["select-dept-id"]),f(a(b),{class:"flex-1 overflow-hidden","table-title":"岗位列表"},{"toolbar-tools":s(()=>[f(a(P),null,{default:s(()=>[C((u(),c(l,{onClick:A},{default:s(()=>[v(h(r.$t("pages.common.export")),1)]),_:1})),[[y,["system:post:export"],"code"]]),C((u(),c(l,{disabled:!a(O)(a(i)),danger:"",type:"primary",onClick:V},{default:s(()=>[v(h(r.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[y,["system:post:remove"],"code"]]),C((u(),c(l,{type:"primary",onClick:p},{default:s(()=>[v(h(r.$t("pages.common.add")),1)]),_:1})),[[y,["system:post:add"],"code"]])]),_:1})]),action:s(({row:w})=>[f(a(P),null,{default:s(()=>[C((u(),c(k,{onClick:B=>g(w)},{default:s(()=>[v(h(r.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[y,["system:post:edit"],"code"]]),f(a(ee),{"get-popup-container":a(oe),placement:"left",title:"确认删除？",onConfirm:B=>R(w)},{default:s(()=>[C((u(),c(k,{danger:"",onClick:e[3]||(e[3]=M(()=>{},["stop"]))},{default:s(()=>[v(h(r.$t("pages.common.delete")),1)]),_:1})),[[y,["system:post:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),f(a(_),{onReload:e[4]||(e[4]=w=>a(i).query())})]),_:1})}}});export{Ke as default};
