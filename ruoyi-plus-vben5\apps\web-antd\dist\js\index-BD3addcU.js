var Z=Object.defineProperty;var A=Object.getOwnPropertySymbols;var ee=Object.prototype.hasOwnProperty,te=Object.prototype.propertyIsEnumerable;var N=(u,o,s)=>o in u?Z(u,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):u[o]=s,V=(u,o)=>{for(var s in o||(o={}))ee.call(o,s)&&N(u,s,o[s]);if(A)for(var s of A(o))te.call(o,s)&&N(u,s,o[s]);return u};var p=(u,o,s)=>new Promise((b,w)=>{var r=m=>{try{c(s.next(m))}catch(y){w(y)}},x=m=>{try{c(s.throw(m))}catch(y){w(y)}},c=m=>m.done?b(m.value):Promise.resolve(m.value).then(r,x);c((s=s.apply(u,o)).next())});import{aB as oe,as as ie,$ as D,an as ae,ap as S}from"./bootstrap-DCMzVRvD.js";import{v as ne}from"./vxe-table-DzEj5Fop.js";import{a as le,u as se,b as B,c as re,d as de,e as pe,f as ce}from"./index-BYl8LdbP.js";import{d as ue}from"./download-UJak946_.js";import{_ as me}from"./category-tree.vue_vue_type_script_setup_true_lang-DGRUd3to.js";import{c as fe,q as ge,_ as ye}from"./process-definition-modal.vue_vue_type_script_setup_true_lang-BfedDVtt.js";import{_ as ve}from"./process-definition-deploy-modal.vue_vue_type_script_setup_true_lang-DkjSQdqb.js";import he from"./index-BeyziwLP.js";import{_ as Ce}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as ke,p as M,B as we,l as _e,S as be,h,o as C,w as l,j as k,a as d,b as n,k as g,t as $,f as xe,T as P}from"../jse/index-index-C-MnMZEz.js";import{u as De}from"./use-vxe-grid-BC7vZzEr.js";import{u as L}from"./use-modal-CeMSCP2m.js";import{P as R}from"./index-DNdMANjv.js";import{g as q}from"./get-popup-container-P4S1sr5h.js";import Se from"./index-D-hwdOI6.js";import{a as $e}from"./Group-oWwucTzK.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-B4NcjlQn.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-BLwHKR_M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./index-CHpIOV4R.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./popup-D6rC6QBG.js";import"./options-tag.vue_vue_type_script_setup_true_lang-k3ySxERw.js";import"./index-B6iusSRX.js";import"./constant-CNx795op.js";import"./tree-DFBawhPd.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";import"./Checkbox-DRV8G-PI.js";const Pe={class:"flex h-full gap-[8px]"},Re={class:"flex flex-col gap-1"},wt=ke({__name:"index",setup(u){const o=M([]),s={schema:ge(),commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",handleReset:()=>p(null,null,function*(){o.value=[];const{formApi:t,reload:e}=r;yield t.resetForm();const i=t.form.values;t.setLatestSubmissionValues(i),yield e(i)})},b={checkboxConfig:{highlight:!0,reserve:!0,trigger:"default"},columns:fe,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(i,...v)=>p(null,[i,...v],function*({page:t},e={}){return o.value.length===1?e.category=o.value[0]:Reflect.deleteProperty(e,"category"),yield m.value(V({pageNum:t.currentPage,pageSize:t.pageSize},e))})}},headerCellConfig:{height:44},cellConfig:{height:100},rowConfig:{keyField:"id"},id:"workflow-definition-index"},[w,r]=De({formOptions:s,gridOptions:b}),x=[{label:"已发布流程",value:1},{label:"未发布流程",value:0}],c=M(1),m=we(()=>c.value===1?le:se);function y(t){return p(this,null,function*(){c.value=t.target.value,yield r.reload()})}function T(t){return p(this,null,function*(){yield B(t.id),yield r.query()})}function O(){const e=r.grid.getCheckboxRecords().map(i=>i.id);ae.confirm({title:"提示",okType:"danger",content:`确认删除选中的${e.length}条记录吗？`,onOk:()=>p(null,null,function*(){yield B(e),yield r.query()})})}const U=oe();function j(t,e){U.push({path:"/workflow/design/index",query:{definitionId:t.id,disabled:String(e)}})}function E(t,e){return p(this,null,function*(){const i=e===1?0:1;try{yield re(t.id,!!e),yield r.query()}catch(v){t.activityStatus=i,console.error(v)}})}function F(t){return p(this,null,function*(){yield de(t.id),yield r.query()})}function G(t){return p(this,null,function*(){yield pe(t.id),c.value=0,yield r.reload()})}const[I,_]=L({connectedComponent:ye});function W(){_.setData({}),_.open()}function X(t){_.setData({id:t.id}),_.open()}function H(t){return p(this,null,function*(){const e=S.loading(D("pages.common.downloadLoading"),0);try{const i=yield ce(t.id);ue(i,`${t.flowName}-${Date.now()}.json`)}catch(i){console.error(i)}finally{e()}})}const[J,z]=L({connectedComponent:ve});function K(){if(o.value.length===0){S.warning("请先选择流程分类");return}const t=o.value[0];if(t===0){S.warning("不可选择根目录进行部署, 请选择子分类");return}z.setData({category:t}),z.open()}function Q(){return p(this,null,function*(){c.value=0,yield r.reload()})}function Y(t){return p(this,null,function*(){t==="add"&&(c.value=0),yield r.reload()})}return(t,e)=>{const i=_e("a-button"),v=be("access");return C(),h(n(Ce),{"auto-content-height":!0},{default:l(()=>[k("div",Pe,[d(me,{"select-code":o.value,"onUpdate:selectCode":e[0]||(e[0]=a=>o.value=a),class:"w-[260px]",onReload:e[1]||(e[1]=()=>n(r).reload()),onSelect:e[2]||(e[2]=()=>n(r).reload())},null,8,["select-code"]),d(n(w),{class:"flex-1 overflow-hidden"},{"toolbar-actions":l(()=>[d(n($e),{value:c.value,"onUpdate:value":e[3]||(e[3]=a=>c.value=a),options:x,"button-style":"solid","option-type":"button",onChange:y},null,8,["value"])]),"toolbar-tools":l(()=>[d(n(he),null,{default:l(()=>[P((C(),h(i,{disabled:!n(ne)(n(r)),danger:"",type:"primary",onClick:O},{default:l(()=>[g($(n(D)("pages.common.delete")),1)]),_:1},8,["disabled"])),[[v,["system:user:remove"],"code"]]),P((C(),h(i,{onClick:K},{default:l(()=>e[5]||(e[5]=[g(" 部署 ")])),_:1,__:[5]})),[[v,["system:user:add"],"code"]]),P((C(),h(i,{type:"primary",onClick:W},{default:l(()=>[g($(n(D)("pages.common.add")),1)]),_:1})),[[v,["system:user:add"],"code"]])]),_:1})]),activityStatus:l(({row:a})=>[d(n(Se),{checked:a.activityStatus,"onUpdate:checked":f=>a.activityStatus=f,"checked-value":1,"unchecked-value":0,"checked-children":"激活","un-checked-children":"挂起",onChange:f=>E(a,f)},null,8,["checked","onUpdate:checked","onChange"])]),action:l(({row:a})=>[k("div",Re,[k("div",null,[d(i,{size:"small",type:"link",onClick:f=>X(a)},{default:l(()=>e[6]||(e[6]=[g(" 编辑信息 ")])),_:2,__:[6]},1032,["onClick"]),d(n(R),{"get-popup-container":n(q),placement:"left",title:"确认删除？",onConfirm:f=>T(a)},{default:l(()=>[d(i,{danger:"",size:"small",type:"link",onClick:e[4]||(e[4]=ie(()=>{},["stop"]))},{default:l(()=>e[7]||(e[7]=[g(" 删除流程 ")])),_:1,__:[7]})]),_:2},1032,["get-popup-container","onConfirm"])]),k("div",null,[d(i,{size:"small",type:"link",onClick:f=>j(a,!!a.isPublish)},{default:l(()=>[g($(a.isPublish?"查看流程":"设计流程"),1)]),_:2},1032,["onClick"]),d(n(R),{"get-popup-container":n(q),title:`确认发布流程[${a.flowName}]?`,placement:"left",onConfirm:f=>F(a)},{default:l(()=>[a.isPublish?xe("",!0):(C(),h(i,{key:0,size:"small",type:"link"},{default:l(()=>e[8]||(e[8]=[g(" 发布流程 ")])),_:1,__:[8]}))]),_:2},1032,["get-popup-container","title","onConfirm"])]),k("div",null,[d(n(R),{"get-popup-container":n(q),title:`确认复制流程[${a.flowName}]?`,placement:"left",onConfirm:f=>G(a)},{default:l(()=>[d(i,{size:"small",type:"link"},{default:l(()=>e[9]||(e[9]=[g(" 复制流程 ")])),_:1,__:[9]})]),_:2},1032,["get-popup-container","title","onConfirm"]),d(i,{size:"small",type:"link",onClick:f=>H(a)},{default:l(()=>e[10]||(e[10]=[g(" 导出流程 ")])),_:2,__:[10]},1032,["onClick"])])])]),_:1})]),d(n(I),{onReload:Y}),d(n(J),{onReload:Q})]),_:1})}}});export{wt as default};
