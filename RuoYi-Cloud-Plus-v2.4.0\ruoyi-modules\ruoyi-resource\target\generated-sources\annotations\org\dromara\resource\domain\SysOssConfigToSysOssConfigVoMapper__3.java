package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssConfigBoToSysOssConfigMapper__3;
import org.dromara.resource.domain.vo.SysOssConfigVo;
import org.dromara.resource.domain.vo.SysOssConfigVoToSysOssConfigMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssConfigBoToSysOssConfigMapper__3.class,SysOssConfigVoToSysOssConfigMapper__3.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__3 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
