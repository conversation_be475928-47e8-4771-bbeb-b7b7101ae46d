---
description: 
globs: apps/web-antd/src/api/**/*.ts,packages/**/api/**/*.ts,**/request.ts,**/http.ts
alwaysApply: false
---
# API模式和约定规则

## API架构模式

### 请求客户端
- 主要请求客户端在 [apps/web-antd/src/api/request.ts](mdc:apps/web-antd/src/api/request.ts)
- 包含请求拦截器、响应拦截器、加密处理、错误处理
- 导出 `requestClient` 作为主要的HTTP客户端

### API模块组织
- 认证相关：[apps/web-antd/src/api/core/auth.ts](mdc:apps/web-antd/src/api/core/auth.ts)
- 用户管理：[apps/web-antd/src/api/system/user/index.ts](mdc:apps/web-antd/src/api/system/user/index.ts)
- 通用辅助函数：[apps/web-antd/src/api/helper.ts](mdc:apps/web-antd/src/api/helper.ts)

## API命名约定

### 标准CRUD操作
- 列表查询：`xxxList(params?: PageQuery)`
- 详情查询：`findXxxInfo(id: ID)`
- 新增：`xxxAdd(data: Partial<Xxx>)`
- 更新：`xxxUpdate(data: Partial<Xxx>)`
- 删除：`xxxRemove(ids: IDS)`

### 特殊操作
- 状态变更：`xxxStatusChange(data)`
- 导出：`xxxExport(data)`
- 导入：`xxxImportData(data)`
- 重置密码：`xxxResetPassword(data)`

## 请求配置模式

### 加密请求
```typescript
requestClient.post('/api/endpoint', data, {
  encrypt: true  // 启用RSA+AES加密
});
```

### 成功消息提示
```typescript
requestClient.postWithMsg('/api/endpoint', data);  // 自动显示成功消息
requestClient.putWithMsg('/api/endpoint', data);
requestClient.deleteWithMsg('/api/endpoint');
```

### 文件下载
```typescript
requestClient.post('/api/export', data, {
  responseType: 'blob',
  isTransformResponse: false
});
```

## 数据类型约定

### 通用类型
- ID类型：`ID = number | string`
- 批量ID：`IDS = (number | string)[]`
- 分页查询：参考 [apps/web-antd/src/api/common.d.ts](mdc:apps/web-antd/src/api/common.d.ts) 的 `PageQuery`
- 分页结果：参考 [apps/web-antd/src/api/common.d.ts](mdc:apps/web-antd/src/api/common.d.ts) 的 `PageResult<T>`

### 响应格式
- 成功：`{ code: 200, msg: string, data?: T }`
- 分页：`{ code: 200, msg: string, rows: T[], total: number }`

## 安全机制
- 所有请求自动添加Authorization头
- 敏感操作（登录、重置密码）使用加密传输
- 加密实现参考：[apps/web-antd/src/utils/encryption/crypto.ts](mdc:apps/web-antd/src/utils/encryption/crypto.ts)
