package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssConfigBoToSysOssConfigMapper__7;
import org.dromara.resource.domain.vo.SysOssConfigVo;
import org.dromara.resource.domain.vo.SysOssConfigVoToSysOssConfigMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssConfigBoToSysOssConfigMapper__7.class,SysOssConfigVoToSysOssConfigMapper__7.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__7 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
