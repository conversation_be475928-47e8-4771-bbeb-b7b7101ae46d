import{c as n}from"./helper-Bc7QQ92Q.js";import{y as e}from"./bootstrap-DCMzVRvD.js";function p(t){return e.get("/system/tenant/package/list",{params:t})}function g(){return e.get("/system/tenant/package/selectList")}function r(t){return n("/system/tenant/package/export",t)}function u(t){return e.get(`/system/tenant/package/${t}`)}function o(t){return e.postWithMsg("/system/tenant/package",t)}function i(t){return e.putWithMsg("/system/tenant/package",t)}function k(t){const a={packageId:t.packageId,status:t.status};return e.putWithMsg("/system/tenant/package/changeStatus",a)}function m(t){return e.deleteWithMsg(`/system/tenant/package/${t}`)}export{p as a,m as b,r as c,u as d,i as e,o as f,g,k as p};
