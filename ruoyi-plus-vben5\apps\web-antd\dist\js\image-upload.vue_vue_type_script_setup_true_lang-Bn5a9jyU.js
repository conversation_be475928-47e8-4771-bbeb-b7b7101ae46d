import{P as A,$ as v,U as F,a as N,I as O,u as q,i as x}from"./bootstrap-DCMzVRvD.js";import{u as E,a as L,d as $}from"./image-upload.vue_vue_type_style_index_0_lang-DExXFAky.js";import{I as j,a as D}from"./index-BY49C_DM.js";import{d as G,m as b,u as H,c as y,a as o,r as J,w as s,b as a,n as i,i as K,e as Q,g as W,o as d,f as u,h as g,j as p,t as n,k as X,l as Y}from"../jse/index-index-C-MnMZEz.js";const Z={key:0},_={class:"mt-[8px]"},se=G({__name:"image-upload",props:b({listType:{default:"picture-card"},withAnimation:{type:Boolean,default:!1},api:{default:()=>q},removeOnError:{type:Boolean,default:!0},showSuccessMsg:{type:Boolean,default:!0},removeConfirm:{type:Boolean,default:!1},accept:{default:$.join(",")},acceptFormat:{},data:{default:()=>{}},maxCount:{default:1},maxSize:{default:5},disabled:{type:Boolean,default:!1},helpMessage:{type:Boolean,default:!0},multiple:{type:Boolean},directory:{type:Boolean},enableDragUpload:{type:Boolean,default:!1},keepMissingId:{type:Boolean},preview:{},abortOnUnmounted:{type:Boolean,default:!0},customFilename:{},customThumbUrl:{}},{value:{default:()=>[]},valueModifiers:{}}),emits:b(["success","remove","change"],["update:value"]),setup(m,{emit:h}){const r=m,w=h,C=H(m,"value"),{acceptStr:B,handleChange:I,handleRemove:M,beforeUpload:k,innerFileList:t,customRequest:P}=E(r,w,C,"image"),{previewVisible:U,previewImage:S,handleCancel:T,handlePreview:z}=L();function R(e){return x(r.preview)?r.preview(e):z(e)}return(e,c)=>{const V=Y("a-button");return d(),y("div",null,[o(a(N),{"file-list":a(t),"onUpdate:fileList":c[0]||(c[0]=l=>K(t)?t.value=l:null),class:i({"upload-animation__disabled":!e.withAnimation}),"list-type":e.listType,accept:e.accept,disabled:e.disabled,directory:e.directory,"max-count":e.maxCount,progress:{showInfo:!0},multiple:e.multiple,"before-upload":a(k),"custom-request":a(P),onPreview:R,onChange:a(I),onRemove:a(M)},{default:s(()=>{var l,f;return[((l=a(t))==null?void 0:l.length)<e.maxCount&&e.listType==="picture-card"?(d(),y("div",Z,[o(a(A)),p("div",_,n(a(v)("component.upload.upload")),1)])):u("",!0),((f=a(t))==null?void 0:f.length)<e.maxCount&&e.listType!=="picture-card"?(d(),g(V,{key:1,disabled:e.disabled},{default:s(()=>[o(a(F)),X(" "+n(a(v)("component.upload.upload")),1)]),_:1},8,["disabled"])):u("",!0)]}),_:1},8,["file-list","class","list-type","accept","disabled","directory","max-count","multiple","before-upload","custom-request","onChange","onRemove"]),J(e.$slots,"helpMessage",Q(W({maxCount:e.maxCount,disabled:e.disabled,maxSize:e.maxSize,accept:e.accept})),()=>[e.helpMessage?(d(),g(a(O),{key:0,scope:"global",keypath:"component.upload.uploadHelpMessage",tag:"div",class:i({"upload-text__disabled":e.disabled,"mt-2":e.listType!=="picture-card"})},{size:s(()=>[p("span",{class:i(["text-primary mx-1 font-medium",{"upload-text__disabled":e.disabled}])},n(e.maxSize)+"MB ",3)]),ext:s(()=>[p("span",{class:i(["text-primary mx-1 font-medium",{"upload-text__disabled":e.disabled}])},n(a(B)),3)]),_:1},8,["class"])):u("",!0)]),o(a(D),{preview:{visible:a(U),onVisibleChange:a(T)}},{default:s(()=>[o(a(j),{class:"hidden",src:a(S)},null,8,["src"])]),_:1},8,["preview"])])}}});export{se as _};
