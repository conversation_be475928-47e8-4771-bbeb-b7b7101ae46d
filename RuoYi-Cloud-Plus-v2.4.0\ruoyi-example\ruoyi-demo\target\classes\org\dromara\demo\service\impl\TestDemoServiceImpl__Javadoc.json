{"doc": " 测试单表Service业务层处理\n\n <AUTHOR>\n @date 2021-07-26\n", "fields": [], "enumConstants": [], "methods": [{"name": "customPageList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 自定义分页查询\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.demo.domain.TestDemo"], "doc": " 保存前的数据校验\n\n @param entity 实体类数据\n"}], "constructors": []}