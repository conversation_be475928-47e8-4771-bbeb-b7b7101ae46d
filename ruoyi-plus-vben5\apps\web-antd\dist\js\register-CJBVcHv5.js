var v=(p,u,o)=>new Promise((r,t)=>{var l=n=>{try{c(o.next(n))}catch(m){t(m)}},i=n=>{try{c(o.throw(n))}catch(m){t(m)}},c=n=>n.done?r(n.value):Promise.resolve(n.value).then(l,i);c((o=o.apply(p,u)).next())});import{br as B,aB as $,$ as e,aN as y,al as g,bF as V}from"./bootstrap-DCMzVRvD.js";import{T as N}from"./auth-title-CzJiGVH3.js";import{d as S,s as x,B as _,c as A,o as k,a as b,j as P,w,r as T,k as f,t as d,b as s,n as F,p as R,M as C,h as I}from"../jse/index-index-C-MnMZEz.js";const L={class:"mt-4 text-center text-sm"},q=S({name:"RegisterForm",__name:"register",props:{formSchema:{default:()=>[]},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(p,{expose:u,emit:o}){const r=p,t=o,[l,i]=B(x({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:_(()=>r.formSchema),showDefaultActions:!1})),c=$();function n(){return v(this,null,function*(){const{valid:a}=yield i.validate(),h=yield i.getValues();a&&t("submit",h)})}function m(){c.push(r.loginPath)}return u({getFormApi:()=>i}),(a,h)=>(k(),A("div",null,[b(N,null,{desc:w(()=>[T(a.$slots,"subTitle",{},()=>[f(d(a.subTitle||s(e)("authentication.signUpSubtitle")),1)])]),default:w(()=>[T(a.$slots,"title",{},()=>[f(d(a.title||s(e)("authentication.createAnAccount"))+" 🚀 ",1)])]),_:3}),b(s(l)),b(s(y),{class:F([{"cursor-wait":a.loading},"mt-2 w-full"]),loading:a.loading,"aria-label":"register",onClick:n},{default:w(()=>[T(a.$slots,"submitButtonText",{},()=>[f(d(a.submitButtonText||s(e)("authentication.signUp")),1)])]),_:3},8,["class","loading"]),P("div",L,[f(d(s(e)("authentication.alreadyHaveAccount"))+" ",1),P("span",{class:"vben-link text-sm font-normal",onClick:h[0]||(h[0]=D=>m())},d(s(e)("authentication.goToLogin")),1)])]))}}),E=S({name:"Register",__name:"register",setup(p){const u=R(!1),o=_(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.usernameTip")},fieldName:"username",label:e("authentication.username"),rules:g().min(1,{message:e("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{passwordStrength:!0,placeholder:e("authentication.password")},fieldName:"password",label:e("authentication.password"),renderComponentContent(){return{strengthText:()=>e("authentication.passwordStrength")}},rules:g().min(1,{message:e("authentication.passwordTip")})},{component:"VbenInputPassword",componentProps:{placeholder:e("authentication.confirmPassword")},dependencies:{rules(t){const{password:l}=t;return g({required_error:e("authentication.passwordTip")}).min(1,{message:e("authentication.passwordTip")}).refine(i=>i===l,{message:e("authentication.confirmPasswordTip")})},triggerFields:["password"]},fieldName:"confirmPassword",label:e("authentication.confirmPassword")},{component:"VbenCheckbox",fieldName:"agreePolicy",renderComponentContent:()=>({default:()=>C("span",[e("authentication.agree"),C("a",{class:"vben-link ml-1 ",href:""},`${e("authentication.privacyPolicy")} & ${e("authentication.terms")}`)])}),rules:V().refine(t=>!!t,{message:e("authentication.agreeTip")})}]);function r(t){console.log("register submit:",t)}return(t,l)=>(k(),I(s(q),{"form-schema":o.value,loading:u.value,onSubmit:r},null,8,["form-schema","loading"]))}});export{E as default};
