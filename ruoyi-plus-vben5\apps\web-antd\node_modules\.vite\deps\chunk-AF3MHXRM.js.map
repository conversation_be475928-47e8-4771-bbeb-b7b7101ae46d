{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/dom.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nlet tpImgEl;\nexport function initTpImg() {\n    if (!tpImgEl) {\n        tpImgEl = new Image();\n        tpImgEl.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';\n    }\n    return tpImgEl;\n}\nexport function getTpImg() {\n    if (!tpImgEl) {\n        return initTpImg();\n    }\n    return tpImgEl;\n}\nconst reClsMap = {};\nfunction getClsRE(cls) {\n    if (!reClsMap[cls]) {\n        reClsMap[cls] = new RegExp(`(?:^|\\\\s)${cls}(?!\\\\S)`, 'g');\n    }\n    return reClsMap[cls];\n}\nfunction getNodeOffset(elem, container, rest) {\n    if (elem) {\n        const parentElem = elem.parentNode;\n        rest.top += elem.offsetTop;\n        rest.left += elem.offsetLeft;\n        if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {\n            rest.top -= parentElem.scrollTop;\n            rest.left -= parentElem.scrollLeft;\n        }\n        if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {\n            return getNodeOffset(elem.offsetParent, container, rest);\n        }\n    }\n    return rest;\n}\nexport function isPx(val) {\n    return val && /^\\d+(px)?$/.test(val);\n}\nexport function isScale(val) {\n    return val && /^\\d+%$/.test(val);\n}\nexport function hasClass(elem, cls) {\n    return !!(elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls)));\n}\nexport function removeClass(elem, cls) {\n    if (elem && hasClass(elem, cls)) {\n        elem.className = elem.className.replace(getClsRE(cls), '');\n    }\n}\nexport function addClass(elem, cls) {\n    if (elem && !hasClass(elem, cls)) {\n        removeClass(elem, cls);\n        elem.className = `${elem.className} ${cls}`;\n    }\n}\nexport function hasControlKey(evnt) {\n    return evnt.ctrlKey || evnt.metaKey;\n}\nexport function toCssUnit(val, unit = 'px') {\n    if (XEUtils.isNumber(val) || /^\\d+$/.test(`${val}`)) {\n        return `${val}${unit}`;\n    }\n    return `${val || ''}`;\n}\nexport function getDomNode() {\n    const documentElement = document.documentElement;\n    const bodyElem = document.body;\n    return {\n        scrollTop: documentElement.scrollTop || bodyElem.scrollTop,\n        scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,\n        visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,\n        visibleWidth: documentElement.clientWidth || bodyElem.clientWidth\n    };\n}\n/**\n * 检查触发源是否属于目标节点\n */\nexport function getEventTargetNode(evnt, container, queryCls, queryMethod) {\n    let targetElem;\n    let target = (evnt.target.shadowRoot && evnt.composed) ? (evnt.composedPath()[0] || evnt.target) : evnt.target;\n    while (target && target.nodeType && target !== document) {\n        if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {\n            targetElem = target;\n        }\n        else if (target === container) {\n            return { flag: queryCls ? !!targetElem : true, container, targetElem: targetElem };\n        }\n        target = target.parentNode;\n    }\n    return { flag: false };\n}\n/**\n * 获取元素相对于 document 的位置\n */\nexport function getOffsetPos(elem, container) {\n    return getNodeOffset(elem, container, { left: 0, top: 0 });\n}\nexport function getAbsolutePos(elem) {\n    const bounding = elem.getBoundingClientRect();\n    const boundingTop = bounding.top;\n    const boundingLeft = bounding.left;\n    const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();\n    return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };\n}\nconst scrollIntoViewIfNeeded = 'scrollIntoViewIfNeeded';\nconst scrollIntoView = 'scrollIntoView';\nexport function scrollToView(elem) {\n    if (elem) {\n        if (elem[scrollIntoViewIfNeeded]) {\n            elem[scrollIntoViewIfNeeded]();\n        }\n        else if (elem[scrollIntoView]) {\n            elem[scrollIntoView]();\n        }\n    }\n}\nexport function triggerEvent(targetElem, type) {\n    if (targetElem) {\n        targetElem.dispatchEvent(new Event(type));\n    }\n}\nexport function isNodeElement(elem) {\n    return elem && elem.nodeType === 1;\n}\nexport function updatePanelPlacement(targetElem, panelElem, options) {\n    const { placement, teleportTo, marginSize } = Object.assign({ teleportTo: false, marginSize: 5 }, options);\n    let panelPlacement = 'bottom';\n    let top = '';\n    let bottom = '';\n    let left = '';\n    const right = '';\n    let minWidth = '';\n    const stys = {};\n    if (panelElem && targetElem) {\n        const documentElement = document.documentElement;\n        const bodyElem = document.body;\n        const targetHeight = targetElem.offsetHeight;\n        const panelHeight = panelElem.offsetHeight;\n        const panelWidth = panelElem.offsetWidth;\n        const bounding = targetElem.getBoundingClientRect();\n        const boundingTop = bounding.top;\n        const boundingLeft = bounding.left;\n        const visibleHeight = documentElement.clientHeight || bodyElem.clientHeight;\n        const visibleWidth = documentElement.clientWidth || bodyElem.clientWidth;\n        minWidth = targetElem.offsetWidth;\n        if (teleportTo) {\n            left = boundingLeft;\n            top = boundingTop + targetHeight;\n            if (placement === 'top') {\n                panelPlacement = 'top';\n                top = boundingTop - panelHeight;\n            }\n            else if (!placement) {\n                // 如果下面不够放，则向上\n                if (top + panelHeight + marginSize > visibleHeight) {\n                    panelPlacement = 'top';\n                    top = boundingTop - panelHeight;\n                }\n                // 如果上面不够放，则向下（优先）\n                if (top < marginSize) {\n                    panelPlacement = 'bottom';\n                    top = boundingTop + targetHeight;\n                }\n            }\n            // 如果溢出右边\n            if (left + panelWidth + marginSize > visibleWidth) {\n                left -= left + panelWidth + marginSize - visibleWidth;\n            }\n            // 如果溢出左边\n            if (left < marginSize) {\n                left = marginSize;\n            }\n        }\n        else {\n            if (placement === 'top') {\n                panelPlacement = 'top';\n                bottom = targetHeight;\n            }\n            else if (!placement) {\n                // 如果下面不够放，则向上\n                top = targetHeight;\n                if (boundingTop + targetHeight + panelHeight > visibleHeight) {\n                    // 如果上面不够放，则向下（优先）\n                    if (boundingTop - targetHeight - panelHeight > marginSize) {\n                        panelPlacement = 'top';\n                        top = '';\n                        bottom = targetHeight;\n                    }\n                }\n            }\n        }\n        if (XEUtils.isNumber(top)) {\n            stys.top = toCssUnit(top);\n        }\n        if (XEUtils.isNumber(bottom)) {\n            stys.bottom = toCssUnit(bottom);\n        }\n        if (XEUtils.isNumber(left)) {\n            stys.left = toCssUnit(left);\n        }\n        if (XEUtils.isNumber(right)) {\n            stys.right = toCssUnit(right);\n        }\n        if (XEUtils.isNumber(minWidth)) {\n            stys.minWidth = toCssUnit(minWidth);\n        }\n    }\n    return {\n        top: top || 0,\n        bottom: bottom || 0,\n        left: left || 0,\n        right: right || 0,\n        style: stys,\n        placement: panelPlacement\n    };\n}\n"], "mappings": ";;;;;;;;AAAA,sBAAoB;AACpB,IAAI;AACG,SAAS,YAAY;AACxB,MAAI,CAAC,SAAS;AACV,cAAU,IAAI,MAAM;AACpB,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO;AACX;AACO,SAAS,WAAW;AACvB,MAAI,CAAC,SAAS;AACV,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AACX;AACA,IAAM,WAAW,CAAC;AAClB,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,aAAS,GAAG,IAAI,IAAI,OAAO,YAAY,GAAG,WAAW,GAAG;AAAA,EAC5D;AACA,SAAO,SAAS,GAAG;AACvB;AACA,SAAS,cAAc,MAAM,WAAW,MAAM;AAC1C,MAAI,MAAM;AACN,UAAM,aAAa,KAAK;AACxB,SAAK,OAAO,KAAK;AACjB,SAAK,QAAQ,KAAK;AAClB,QAAI,cAAc,eAAe,SAAS,mBAAmB,eAAe,SAAS,MAAM;AACvF,WAAK,OAAO,WAAW;AACvB,WAAK,QAAQ,WAAW;AAAA,IAC5B;AACA,QAAI,cAAc,SAAS,aAAa,KAAK,iBAAiB,aAAa,IAAI,KAAK,cAAc;AAC9F,aAAO,cAAc,KAAK,cAAc,WAAW,IAAI;AAAA,IAC3D;AAAA,EACJ;AACA,SAAO;AACX;AAIO,SAAS,QAAQ,KAAK;AACzB,SAAO,OAAO,SAAS,KAAK,GAAG;AACnC;AACO,SAAS,SAAS,MAAM,KAAK;AAChC,SAAO,CAAC,EAAE,QAAQ,KAAK,aAAa,KAAK,UAAU,SAAS,KAAK,UAAU,MAAM,SAAS,GAAG,CAAC;AAClG;AACO,SAAS,YAAY,MAAM,KAAK;AACnC,MAAI,QAAQ,SAAS,MAAM,GAAG,GAAG;AAC7B,SAAK,YAAY,KAAK,UAAU,QAAQ,SAAS,GAAG,GAAG,EAAE;AAAA,EAC7D;AACJ;AACO,SAAS,SAAS,MAAM,KAAK;AAChC,MAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,GAAG;AAC9B,gBAAY,MAAM,GAAG;AACrB,SAAK,YAAY,GAAG,KAAK,SAAS,IAAI,GAAG;AAAA,EAC7C;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,KAAK,WAAW,KAAK;AAChC;AACO,SAAS,UAAU,KAAK,OAAO,MAAM;AACxC,MAAI,gBAAAA,QAAQ,SAAS,GAAG,KAAK,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG;AACjD,WAAO,GAAG,GAAG,GAAG,IAAI;AAAA,EACxB;AACA,SAAO,GAAG,OAAO,EAAE;AACvB;AACO,SAAS,aAAa;AACzB,QAAM,kBAAkB,SAAS;AACjC,QAAM,WAAW,SAAS;AAC1B,SAAO;AAAA,IACH,WAAW,gBAAgB,aAAa,SAAS;AAAA,IACjD,YAAY,gBAAgB,cAAc,SAAS;AAAA,IACnD,eAAe,gBAAgB,gBAAgB,SAAS;AAAA,IACxD,cAAc,gBAAgB,eAAe,SAAS;AAAA,EAC1D;AACJ;AAIO,SAAS,mBAAmB,MAAM,WAAW,UAAU,aAAa;AACvE,MAAI;AACJ,MAAI,SAAU,KAAK,OAAO,cAAc,KAAK,WAAa,KAAK,aAAa,EAAE,CAAC,KAAK,KAAK,SAAU,KAAK;AACxG,SAAO,UAAU,OAAO,YAAY,WAAW,UAAU;AACrD,QAAI,YAAY,SAAS,QAAQ,QAAQ,MAAM,CAAC,eAAe,YAAY,MAAM,IAAI;AACjF,mBAAa;AAAA,IACjB,WACS,WAAW,WAAW;AAC3B,aAAO,EAAE,MAAM,WAAW,CAAC,CAAC,aAAa,MAAM,WAAW,WAAuB;AAAA,IACrF;AACA,aAAS,OAAO;AAAA,EACpB;AACA,SAAO,EAAE,MAAM,MAAM;AACzB;AAIO,SAAS,aAAa,MAAM,WAAW;AAC1C,SAAO,cAAc,MAAM,WAAW,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;AAC7D;AACO,SAAS,eAAe,MAAM;AACjC,QAAM,WAAW,KAAK,sBAAsB;AAC5C,QAAM,cAAc,SAAS;AAC7B,QAAM,eAAe,SAAS;AAC9B,QAAM,EAAE,WAAW,YAAY,eAAe,aAAa,IAAI,WAAW;AAC1E,SAAO,EAAE,aAAa,KAAK,YAAY,aAAa,cAAc,MAAM,aAAa,cAAc,eAAe,aAAa;AACnI;AACA,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AAChB,SAAS,aAAa,MAAM;AAC/B,MAAI,MAAM;AACN,QAAI,KAAK,sBAAsB,GAAG;AAC9B,WAAK,sBAAsB,EAAE;AAAA,IACjC,WACS,KAAK,cAAc,GAAG;AAC3B,WAAK,cAAc,EAAE;AAAA,IACzB;AAAA,EACJ;AACJ;AASO,SAAS,qBAAqB,YAAY,WAAW,SAAS;AACjE,QAAM,EAAE,WAAW,YAAY,WAAW,IAAI,OAAO,OAAO,EAAE,YAAY,OAAO,YAAY,EAAE,GAAG,OAAO;AACzG,MAAI,iBAAiB;AACrB,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI,OAAO;AACX,QAAM,QAAQ;AACd,MAAI,WAAW;AACf,QAAM,OAAO,CAAC;AACd,MAAI,aAAa,YAAY;AACzB,UAAM,kBAAkB,SAAS;AACjC,UAAM,WAAW,SAAS;AAC1B,UAAM,eAAe,WAAW;AAChC,UAAM,cAAc,UAAU;AAC9B,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,WAAW,sBAAsB;AAClD,UAAM,cAAc,SAAS;AAC7B,UAAM,eAAe,SAAS;AAC9B,UAAM,gBAAgB,gBAAgB,gBAAgB,SAAS;AAC/D,UAAM,eAAe,gBAAgB,eAAe,SAAS;AAC7D,eAAW,WAAW;AACtB,QAAI,YAAY;AACZ,aAAO;AACP,YAAM,cAAc;AACpB,UAAI,cAAc,OAAO;AACrB,yBAAiB;AACjB,cAAM,cAAc;AAAA,MACxB,WACS,CAAC,WAAW;AAEjB,YAAI,MAAM,cAAc,aAAa,eAAe;AAChD,2BAAiB;AACjB,gBAAM,cAAc;AAAA,QACxB;AAEA,YAAI,MAAM,YAAY;AAClB,2BAAiB;AACjB,gBAAM,cAAc;AAAA,QACxB;AAAA,MACJ;AAEA,UAAI,OAAO,aAAa,aAAa,cAAc;AAC/C,gBAAQ,OAAO,aAAa,aAAa;AAAA,MAC7C;AAEA,UAAI,OAAO,YAAY;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,OACK;AACD,UAAI,cAAc,OAAO;AACrB,yBAAiB;AACjB,iBAAS;AAAA,MACb,WACS,CAAC,WAAW;AAEjB,cAAM;AACN,YAAI,cAAc,eAAe,cAAc,eAAe;AAE1D,cAAI,cAAc,eAAe,cAAc,YAAY;AACvD,6BAAiB;AACjB,kBAAM;AACN,qBAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,gBAAAC,QAAQ,SAAS,GAAG,GAAG;AACvB,WAAK,MAAM,UAAU,GAAG;AAAA,IAC5B;AACA,QAAI,gBAAAA,QAAQ,SAAS,MAAM,GAAG;AAC1B,WAAK,SAAS,UAAU,MAAM;AAAA,IAClC;AACA,QAAI,gBAAAA,QAAQ,SAAS,IAAI,GAAG;AACxB,WAAK,OAAO,UAAU,IAAI;AAAA,IAC9B;AACA,QAAI,gBAAAA,QAAQ,SAAS,KAAK,GAAG;AACzB,WAAK,QAAQ,UAAU,KAAK;AAAA,IAChC;AACA,QAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,WAAK,WAAW,UAAU,QAAQ;AAAA,IACtC;AAAA,EACJ;AACA,SAAO;AAAA,IACH,KAAK,OAAO;AAAA,IACZ,QAAQ,UAAU;AAAA,IAClB,MAAM,QAAQ;AAAA,IACd,OAAO,SAAS;AAAA,IAChB,OAAO;AAAA,IACP,WAAW;AAAA,EACf;AACJ;", "names": ["XEUtils", "XEUtils"]}