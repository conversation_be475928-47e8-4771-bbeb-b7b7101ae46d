2025-06-12 16:16:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-12 16:16:14 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 22.0.2 with PID 21400 (D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:16:14 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:16:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:16:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:16:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:16:19 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5de6c7d7
2025-06-12 16:16:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:16:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:16:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:16:21 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:16:21 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:16:21 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:16:21 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:16:22 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:16:22 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:17:29 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:17:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:17:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:17:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:17:33 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@35b38986
2025-06-12 16:17:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:17:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:17:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:17:34 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:17:34 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:17:35 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:17:35 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:17:36 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:17:36 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:19:21 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:19:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:19:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:19:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:19:25 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1bcca516
2025-06-12 16:19:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:19:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:19:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:19:26 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:19:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:19:26 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:19:26 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:19:27 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:19:27 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:21:44 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:21:47 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:21:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:21:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:21:48 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f3b11e6
2025-06-12 16:21:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:21:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:21:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:21:50 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:21:50 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:21:50 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:21:50 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:21:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:21:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:26:06 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:26:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:26:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:26:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:26:10 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1a0e5c0c
2025-06-12 16:26:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:26:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:26:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:26:12 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:26:12 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:26:12 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:26:12 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:26:13 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:26:13 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:28:54 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:28:58 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:28:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:28:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:28:59 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@674c01ce
2025-06-12 16:28:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:28:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:28:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:29:00 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:29:00 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:29:00 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:29:00 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:29:01 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:29:01 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 16:45:46 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 16:45:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 16:45:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 16:45:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 16:45:50 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1737834
2025-06-12 16:45:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 16:45:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 16:45:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 16:45:51 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 16:45:52 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 16:45:52 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:45:52 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 16:45:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 16:45:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 17:57:53 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 17:57:58 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 17:57:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 17:57:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 17:57:59 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7c0ecdbf
2025-06-12 17:57:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 17:57:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 17:57:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 17:58:01 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 17:58:01 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 17:58:01 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 17:58:01 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 17:58:03 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 17:58:03 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\kwsywms_test\RuoYi-Vue-Plus-v5.4.0\ruoyi-admin\target\classes started by junxl in D:\kwsywms_test\RuoYi-Vue-Plus-v5.4.0)
2025-06-12 18:27:00 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-06-12 18:27:03 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-12 18:27:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-12 18:27:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-12 18:27:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1737834
2025-06-12 18:27:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-12 18:27:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-12 18:27:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-12 18:27:05 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-12 18:27:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-12 18:27:06 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-12 18:27:06 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-12 18:27:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\junxl/snailJob/worker
2025-06-12 18:27:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>