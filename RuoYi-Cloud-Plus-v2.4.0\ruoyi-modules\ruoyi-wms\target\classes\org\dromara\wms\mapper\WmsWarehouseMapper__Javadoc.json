{"doc": " 仓库Mapper接口\n\n <AUTHOR>\n @date 2025-06-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDeptIdsByWarehouseId", "paramTypes": ["java.lang.Long"], "doc": " 查询仓库关联的部门ID列表\n\n @param warehouseId 仓库ID\n @return 部门ID列表\n"}, {"name": "selectDeptNamesByWarehouseId", "paramTypes": ["java.lang.Long"], "doc": " 查询仓库关联的部门名称\n\n @param warehouseId 仓库ID\n @return 部门名称，多个用逗号分隔\n"}], "constructors": []}