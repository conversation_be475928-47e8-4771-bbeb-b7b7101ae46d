import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-material-symbols@1.2.58/node_modules/@iconify/icons-material-symbols/generating-tokens-outline.js
var require_generating_tokens_outline = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-material-symbols@1.2.58/node_modules/@iconify/icons-material-symbols/generating-tokens-outline.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "hidden": true,
      "body": '<path fill="currentColor" d="M9 20q-3.35 0-5.675-2.325T1 12q0-3.35 2.325-5.675T9 4q3.35 0 5.675 2.325T17 12q0 3.35-2.325 5.675T9 20Zm0-2q2.5 0 4.25-1.75T15 12q0-2.5-1.75-4.25T9 6Q6.5 6 4.75 7.75T3 12q0 2.5 1.75 4.25T9 18Zm-1-2.5h2v-5h2V9H6v1.5h2v5ZM19 9l-1.25-2.75L15 5l2.75-1.25L19 1l1.25 2.75L23 5l-2.75 1.25L19 9Zm0 14l-1.25-2.75L15 19l2.75-1.25L19 15l1.25 2.75L23 19l-2.75 1.25L19 23ZM9 12Z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_generating_tokens_outline();
//# sourceMappingURL=@iconify_icons-material-symbols_generating-tokens-outline.js.map
