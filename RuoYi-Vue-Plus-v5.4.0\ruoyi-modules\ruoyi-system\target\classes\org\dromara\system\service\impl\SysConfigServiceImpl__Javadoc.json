{"doc": " 参数配置 服务层实现\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectConfigById", "paramTypes": ["java.lang.Long"], "doc": " 查询参数配置信息\n\n @param configId 参数配置ID\n @return 参数配置信息\n"}, {"name": "selectConfigByKey", "paramTypes": ["java.lang.String"], "doc": " 根据键名查询参数配置信息\n\n @param configKey 参数key\n @return 参数键值\n"}, {"name": "selectRegisterEnabled", "paramTypes": ["java.lang.String"], "doc": " 获取注册开关\n @param tenantId 租户id\n @return true开启，false关闭\n"}, {"name": "selectConfigList", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 查询参数配置列表\n\n @param config 参数配置信息\n @return 参数配置集合\n"}, {"name": "insertConfig", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 新增参数配置\n\n @param bo 参数配置信息\n @return 结果\n"}, {"name": "updateConfig", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 修改参数配置\n\n @param bo 参数配置信息\n @return 结果\n"}, {"name": "deleteConfigByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除参数信息\n\n @param configIds 需要删除的参数ID\n"}, {"name": "resetConfigCache", "paramTypes": [], "doc": " 重置参数缓存数据\n"}, {"name": "checkConfigKeyUnique", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 校验参数键名是否唯一\n\n @param config 参数配置信息\n @return 结果\n"}, {"name": "getConfigValue", "paramTypes": ["java.lang.String"], "doc": " 根据参数 key 获取参数值\n\n @param configKey 参数 key\n @return 参数值\n"}], "constructors": []}