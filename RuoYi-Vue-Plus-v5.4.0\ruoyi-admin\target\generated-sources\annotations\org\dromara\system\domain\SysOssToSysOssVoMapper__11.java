package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__11;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOssBoToSysOssMapper__11.class,SysOssVoToSysOssMapper__11.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__11 extends BaseMapper<SysOss, SysOssVo> {
}
