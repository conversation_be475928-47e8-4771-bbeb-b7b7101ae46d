var $=Object.defineProperty;var M=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var Y=(e,t,o)=>t in e?$(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,v=(e,t)=>{for(var o in t||(t={}))L.call(t,o)&&Y(e,o,t[o]);if(M)for(var o of M(t))V.call(t,o)&&Y(e,o,t[o]);return e};var d=(e,t,o)=>new Promise((T,i)=>{var D=n=>{try{m(o.next(n))}catch(p){i(p)}},g=n=>{try{m(o.throw(n))}catch(p){i(p)}},m=n=>n.done?T(n.value):Promise.resolve(n.value).then(D,g);m((o=o.apply(e,t)).next())});import{y as k,ar as S,as as q,$ as b,an as A}from"./bootstrap-DCMzVRvD.js";import{v as R,a as z}from"./vxe-table-DzEj5Fop.js";import{c as B}from"./helper-Bc7QQ92Q.js";import{c as W}from"./download-UJak946_.js";import{c as F}from"./modal-CapY0ZPa.js";import{g as P}from"./dict-BLkXAGS5.js";import{r as N}from"./render-BxXtQdeV.js";import{_ as H}from"./operation-preview-drawer.vue_vue_type_script_setup_true_lang-73ebB2FY.js";import U from"./index-BeyziwLP.js";import{_ as j}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as G,l as O,S as J,h as u,o as f,w as a,a as y,b as r,T as C,k as w,t as x}from"../jse/index-index-C-MnMZEz.js";import{u as K}from"./use-vxe-grid-BC7vZzEr.js";import{u as Q}from"./use-drawer-6qcpK-D1.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-BxBCzu2M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-kC0HFDdy.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./index-D59rZjD-.js";import"./x-Bfkqqjgb.js";function X(e){return k.get("/monitor/operlog/list",{params:e})}function Z(e){return k.deleteWithMsg(`/monitor/operlog/${e}`)}function ee(){return k.deleteWithMsg("/monitor/operlog/clean")}function oe(e){return B("/monitor/operlog/export",e)}const te=()=>[{component:"Input",fieldName:"title",label:"系统模块"},{component:"Input",fieldName:"operName",label:"操作人员"},{component:"Select",componentProps:{options:P(S.SYS_OPER_TYPE)},fieldName:"businessType",label:"操作类型"},{component:"Input",fieldName:"operIp",label:"操作IP"},{component:"Select",componentProps:{options:P(S.SYS_COMMON_STATUS)},fieldName:"status",label:"状态"},{component:"RangePicker",fieldName:"createTime",label:"操作时间",componentProps:{valueFormat:"YYYY-MM-DD HH:mm:ss"}}],re=[{type:"checkbox",width:60},{field:"title",title:"系统模块"},{title:"操作类型",field:"businessType",slots:{default:({row:e})=>N(e.businessType,S.SYS_OPER_TYPE)}},{field:"operName",title:"操作人员"},{field:"operIp",title:"IP地址"},{field:"operLocation",title:"IP信息"},{field:"status",title:"操作状态",slots:{default:({row:e})=>N(e.status,S.SYS_COMMON_STATUS)}},{field:"operTime",title:"操作日期",sortable:!0},{field:"costTime",title:"操作耗时",sortable:!0,formatter({cellValue:e}){return`${e} ms`}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],Re=G({__name:"index",setup(e){const t={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:te(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",fieldMappingTime:[["createTime",["params[beginTime]","params[endTime]"],["YYYY-MM-DD 00:00:00","YYYY-MM-DD 23:59:59"]]]},o={checkboxConfig:{highlight:!0,reserve:!0,trigger:"row"},columns:re,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(I,..._)=>d(null,[I,..._],function*({page:l,sorts:c},s={}){const h=v({pageNum:l.currentPage,pageSize:l.pageSize},s);return z(h,c),yield X(h)})}},rowConfig:{keyField:"operId"},sortConfig:{remote:!0,multiple:!0},id:"monitor-operlog-index"},[T,i]=K({formOptions:t,gridOptions:o,gridEvents:{sortChange:()=>i.query()}}),[D,g]=Q({connectedComponent:H});function m(l){g.setData({record:l}),g.open()}function n(){F({onValidated:()=>d(null,null,function*(){yield ee(),yield i.reload()})})}function p(){return d(this,null,function*(){const c=i.grid.getCheckboxRecords().map(s=>s.operId);A.confirm({title:"提示",okType:"danger",content:`确认删除选中的${c.length}条操作日志吗？`,onOk:()=>d(null,null,function*(){yield Z(c),yield i.query()})})})}function E(){W(oe,"操作日志",i.formApi.form.values,{fieldMappingTime:t.fieldMappingTime})}return(l,c)=>{const s=O("a-button"),I=O("ghost-button"),_=J("access");return f(),u(r(j),{"auto-content-height":!0},{default:a(()=>[y(r(T),{"table-title":"操作日志列表"},{"toolbar-tools":a(()=>[y(r(U),null,{default:a(()=>[C((f(),u(s,{onClick:n},{default:a(()=>[w(x(r(b)("pages.common.clear")),1)]),_:1})),[[_,["monitor:operlog:remove"],"code"]]),C((f(),u(s,{onClick:E},{default:a(()=>[w(x(r(b)("pages.common.export")),1)]),_:1})),[[_,["monitor:operlog:export"],"code"]]),C((f(),u(s,{disabled:!r(R)(r(i)),danger:"",type:"primary",onClick:p},{default:a(()=>[w(x(r(b)("pages.common.delete")),1)]),_:1},8,["disabled"])),[[_,["monitor:operlog:remove"],"code"]])]),_:1})]),action:a(({row:h})=>[C((f(),u(I,{onClick:q(ie=>m(h),["stop"])},{default:a(()=>[w(x(r(b)("pages.common.preview")),1)]),_:2},1032,["onClick"])),[[_,["monitor:operlog:list"],"code"]])]),_:1}),y(r(D))]),_:1})}}});export{Re as default};
