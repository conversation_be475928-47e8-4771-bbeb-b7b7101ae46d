package org.dromara.wms.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wms.domain.bo.WmsWarehouseBo;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.WmsWarehouseDept;
import org.dromara.wms.mapper.WmsWarehouseMapper;
import org.dromara.wms.mapper.WmsWarehouseDeptMapper;
import org.dromara.wms.service.IWmsWarehouseService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 仓库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WmsWarehouseServiceImpl implements IWmsWarehouseService {

    private final WmsWarehouseMapper baseMapper;
    private final WmsWarehouseDeptMapper warehouseDeptMapper;

    /**
     * 查询仓库
     *
     * @param warehouseId 主键
     * @return 仓库
     */
    @Override
    public WmsWarehouseVo queryById(Long warehouseId){
        WmsWarehouseVo vo = baseMapper.selectVoById(warehouseId);
        if (vo != null) {
            // 加载部门信息
            vo.setDeptIds(baseMapper.selectDeptIdsByWarehouseId(warehouseId));
            vo.setDeptNames(baseMapper.selectDeptNamesByWarehouseId(warehouseId));
        }
        return vo;
    }

    /**
     * 分页查询仓库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 仓库分页列表
     */
    @Override
    public TableDataInfo<WmsWarehouseVo> queryPageList(WmsWarehouseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WmsWarehouse> lqw = buildQueryWrapper(bo);
        Page<WmsWarehouseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 为每个仓库加载部门信息
        result.getRecords().forEach(vo -> {
            vo.setDeptNames(baseMapper.selectDeptNamesByWarehouseId(vo.getWarehouseId()));
        });

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的仓库列表
     *
     * @param bo 查询条件
     * @return 仓库列表
     */
    @Override
    public List<WmsWarehouseVo> queryList(WmsWarehouseBo bo) {
        LambdaQueryWrapper<WmsWarehouse> lqw = buildQueryWrapper(bo);
        List<WmsWarehouseVo> result = baseMapper.selectVoList(lqw);

        // 为每个仓库加载部门信息
        result.forEach(vo -> {
            vo.setDeptNames(baseMapper.selectDeptNamesByWarehouseId(vo.getWarehouseId()));
        });

        return result;
    }

    private LambdaQueryWrapper<WmsWarehouse> buildQueryWrapper(WmsWarehouseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WmsWarehouse> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WmsWarehouse::getWarehouseNumber);
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseNumber()), WmsWarehouse::getWarehouseNumber, bo.getWarehouseNumber());
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseName()), WmsWarehouse::getWarehouseName, bo.getWarehouseName());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), WmsWarehouse::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseInventoryStatus()), WmsWarehouse::getWarehouseInventoryStatus, bo.getWarehouseInventoryStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseRecevingStatus()), WmsWarehouse::getWarehouseRecevingStatus, bo.getWarehouseRecevingStatus());

        // 部门筛选
        if (bo.getDeptId() != null) {
            lqw.exists("SELECT 1 FROM wms_warehouse_dept wd WHERE wd.warehouse_id = wms_warehouse.warehouse_id AND wd.dept_id = {0}", bo.getDeptId());
        }

        return lqw;
    }

    /**
     * 新增仓库
     *
     * @param bo 仓库
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WmsWarehouseBo bo) {
        WmsWarehouse add = MapstructUtils.convert(bo, WmsWarehouse.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWarehouseId(add.getWarehouseId());
            // 保存部门关联
            saveDeptRelations(add.getWarehouseId(), bo.getDeptIds());
        }
        return flag;
    }

    /**
     * 修改仓库
     *
     * @param bo 仓库
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WmsWarehouseBo bo) {
        WmsWarehouse update = MapstructUtils.convert(bo, WmsWarehouse.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            // 更新部门关联
            saveDeptRelations(bo.getWarehouseId(), bo.getDeptIds());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WmsWarehouse entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除仓库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        // 删除仓库前先删除相关的部门关联
        for (Long warehouseId : ids) {
            LambdaQueryWrapper<WmsWarehouseDept> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.eq(WmsWarehouseDept::getWarehouseId, warehouseId);
            warehouseDeptMapper.delete(deleteWrapper);
        }

        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 保存仓库部门关联
     *
     * @param warehouseId 仓库ID
     * @param deptIds     部门ID列表
     */
    private void saveDeptRelations(Long warehouseId, List<Long> deptIds) {
        if (warehouseId == null) {
            return;
        }

        // 先删除原有关联
        LambdaQueryWrapper<WmsWarehouseDept> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(WmsWarehouseDept::getWarehouseId, warehouseId);
        warehouseDeptMapper.delete(deleteWrapper);

        // 保存新的关联
        if (deptIds != null && !deptIds.isEmpty()) {
            for (Long deptId : deptIds) {
                WmsWarehouseDept relation = new WmsWarehouseDept();
                relation.setWarehouseId(warehouseId);
                relation.setDeptId(deptId);
                // TenantEntity会自动处理租户信息
                warehouseDeptMapper.insert(relation);
            }
        }
    }

    @Override
    public List<WmsWarehouseVo> getUserAccessibleWarehouses(Long userId) {
        return baseMapper.selectUserAccessibleWarehouses(userId);
    }
}
