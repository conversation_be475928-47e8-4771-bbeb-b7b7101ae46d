{"doc": " 部门表 sys_dept\n\n <AUTHOR> Li\n", "fields": [{"name": "deptId", "doc": " 部门ID\n"}, {"name": "parentId", "doc": " 父部门ID\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "deptCategory", "doc": " 部门类别编码\n"}, {"name": "orderNum", "doc": " 显示顺序\n"}, {"name": "leader", "doc": " 负责人\n"}, {"name": "phone", "doc": " 联系电话\n"}, {"name": "email", "doc": " 邮箱\n"}, {"name": "status", "doc": " 部门状态:0正常,1停用\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 2代表删除）\n"}, {"name": "ancestors", "doc": " 祖级列表\n"}, {"name": "children", "doc": " 子菜单\n"}], "enumConstants": [], "methods": [], "constructors": []}