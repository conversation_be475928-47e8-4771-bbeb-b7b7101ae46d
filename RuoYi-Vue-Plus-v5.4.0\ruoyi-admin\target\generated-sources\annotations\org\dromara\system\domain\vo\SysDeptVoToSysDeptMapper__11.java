package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__11;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysDeptBoToSysDeptMapper__11.class,SysDeptToSysDeptVoMapper__11.class,SysDeptToSysDeptVoMapper__11.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper__11 extends BaseMapper<SysDeptVo, SysDept> {
}
