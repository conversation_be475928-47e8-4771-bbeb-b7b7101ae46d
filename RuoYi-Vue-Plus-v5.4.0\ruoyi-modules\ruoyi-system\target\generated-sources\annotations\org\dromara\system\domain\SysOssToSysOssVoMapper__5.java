package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__5;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysOssBoToSysOssMapper__5.class,SysOssVoToSysOssMapper__5.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__5 extends BaseMapper<SysOss, SysOssVo> {
}
