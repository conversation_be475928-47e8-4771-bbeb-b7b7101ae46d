import {
  dayjs_default,
  generatePicker_default
} from "./chunk-7KUSKIUE.js";
import {
  _extends
} from "./chunk-YTNASAWS.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/dayjs.js
var {
  DatePicker,
  WeekPicker,
  MonthPicker,
  YearPicker,
  TimePicker,
  QuarterPicker,
  RangePicker
} = generatePicker_default(dayjs_default);
var dayjs_default2 = _extends(DatePicker, {
  WeekPicker,
  MonthPicker,
  YearPicker,
  RangePicker,
  TimePicker,
  QuarterPicker,
  install: (app) => {
    app.component(DatePicker.name, DatePicker);
    app.component(RangePicker.name, RangePicker);
    app.component(MonthPicker.name, MonthPicker);
    app.component(WeekPicker.name, WeekPicker);
    app.component(QuarterPicker.name, QuarterPicker);
    return app;
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/index.js
var date_picker_default = dayjs_default2;

export {
  WeekPicker,
  MonthPicker,
  QuarterPicker,
  RangePicker,
  date_picker_default
};
//# sourceMappingURL=chunk-TMHHTNJL.js.map
