import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-akar-icons@1.2.19/node_modules/@iconify/icons-akar-icons/schedule.js
var require_schedule = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-akar-icons@1.2.19/node_modules/@iconify/icons-akar-icons/schedule.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M9 20H6a4 4 0 0 1-4-4V7a4 4 0 0 1 4-4h11a4 4 0 0 1 4 4v3M8 2v2m7-2v2M2 8h19m-2.5 7.643l-1.5 1.5"/><circle cx="17" cy="17" r="5"/></g>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_schedule();
//# sourceMappingURL=@iconify_icons-akar-icons_schedule.js.map
