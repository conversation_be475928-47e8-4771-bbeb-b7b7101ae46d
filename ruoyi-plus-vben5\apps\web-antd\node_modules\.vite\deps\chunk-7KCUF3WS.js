import {
  require_xe_utils
} from "./chunk-TETVOAVO.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/number-input/src/util.js
var import_xe_utils = __toESM(require_xe_utils());
function handleNumber(val) {
  return import_xe_utils.default.isString(val) ? val.replace(/[^0-9e.-]/g, "") : val;
}
function toFloatValueFixed(inputValue, digitsValue) {
  if (/^-/.test("" + inputValue)) {
    return import_xe_utils.default.toFixed(import_xe_utils.default.ceil(inputValue, digitsValue), digitsValue);
  }
  return import_xe_utils.default.toFixed(import_xe_utils.default.floor(inputValue, digitsValue), digitsValue);
}

export {
  handleNumber,
  toFloatValueFixed
};
//# sourceMappingURL=chunk-7KCUF3WS.js.map
