var l=(r,n,o)=>new Promise((e,a)=>{var c=t=>{try{i(o.next(t))}catch(s){a(s)}},p=t=>{try{i(o.throw(t))}catch(s){a(s)}},i=t=>t.done?e(t.value):Promise.resolve(t.value).then(c,p);i((o=o.apply(r,n)).next())});import{l as m}from"./api-CwjVPMpk.js";import{u as y,_ as d}from"./use-echarts-CF-NZzbo.js";import{d as f,p as u,v as g,h as _,o as x,b as C}from"../jse/index-index-C-MnMZEz.js";const A=f({name:"LoginLine",__name:"loginLine",setup(r){const n=u(),{renderEcharts:o}=y(n);return g(()=>l(null,null,function*(){const e=yield m();console.log(e);const a={legend:{},series:[{data:e.success,itemStyle:{color:"#3399CC"},lineStyle:{color:"#3399CC"},name:"登录成功",type:"line"},{data:e.fail,itemStyle:{color:"#CC6633"},lineStyle:{color:"#CC6633"},name:"登录失败",type:"line"}],title:{text:"近一月登录量统计"},toolbox:{feature:{dataView:{readOnly:!0},dataZoom:{yAxisIndex:"none"},magicType:{type:["line","bar"]},saveAsImage:{}},show:!0},tooltip:{trigger:"axis"},xAxis:{boundaryGap:!1,data:e.date,type:"category"},yAxis:{type:"value"}};o(a)})),(e,a)=>(x(),_(C(d),{ref_key:"loginLineRef",ref:n,height:"720px",width:"100%"},null,512))}});export{A as _};
