var d=(w,I,i)=>new Promise((t,k)=>{var _=s=>{try{f(i.next(s))}catch(p){k(p)}},b=s=>{try{f(i.throw(s))}catch(p){k(p)}},f=s=>s.done?t(s.value):Promise.resolve(s.value).then(_,b);f((i=i.apply(w,I)).next())});import{g as V,t as C,e as R}from"./index-CZhogUxH.js";import{an as v}from"./bootstrap-DCMzVRvD.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import y from"./user-select-modal-9zMWfXzj.js";import{D as h,a as m}from"./index-D59rZjD-.js";import{d as z,p as E,B as P,l as $,h as S,o as M,b as n,w as a,f as T,a as o,k as r,t as c,c as j,F as q}from"../jse/index-index-C-MnMZEz.js";import{u as g}from"./use-modal-CeMSCP2m.js";const Y=z({__name:"flow-interfere-modal",emits:["complete"],setup(w,{emit:I}){const i=I,t=E(),k=P(()=>t.value?Number(t.value.nodeRatio)>0:!1),[_,b]=g({title:"流程干预",class:"w-[800px]",fullscreenButton:!1,onOpenChange(l){return d(this,null,function*(){if(!l)return null;const{taskId:e}=b.getData();t.value=yield V(e)})}}),[f,s]=g({connectedComponent:y});function p(l){if(l.length===0||!t.value)return;const e=l[0];v.confirm({title:"转办",content:`确定转办给${e==null?void 0:e.nickName}吗?`,centered:!0,onOk:()=>d(null,null,function*(){yield C({taskId:t.value.id,userId:e.userId},"transferTask"),i("complete")})})}function B(){t.value&&v.confirm({title:"审批终止",content:"确定终止当前审批流程吗？",centered:!0,okButtonProps:{danger:!0},onOk:()=>d(null,null,function*(){yield R({taskId:t.value.id}),i("complete")})})}const[A,D]=g({connectedComponent:y});function N(l){if(l.length===0||!t.value)return;const e=l.map(u=>u.userId);v.confirm({title:"提示",content:"确认加签吗?",centered:!0,onOk:()=>d(null,null,function*(){yield C({taskId:t.value.id,userIds:e},"addSignature"),i("complete")})})}const[O,x]=g({connectedComponent:y});function F(l){if(l.length===0||!t.value)return;const e=l.map(u=>u.userId);v.confirm({title:"提示",content:"确认减签吗?",centered:!0,onOk:()=>d(null,null,function*(){yield C({taskId:t.value.id,userIds:e},"reductionSignature"),i("complete")})})}return(l,e)=>{const u=$("a-button");return M(),S(n(_),null,{footer:a(()=>[k.value?(M(),j(q,{key:0},[o(u,{onClick:e[0]||(e[0]=()=>n(D).open())},{default:a(()=>e[3]||(e[3]=[r("加签")])),_:1,__:[3]}),o(u,{onClick:e[1]||(e[1]=()=>n(x).open())},{default:a(()=>e[4]||(e[4]=[r(" 减签 ")])),_:1,__:[4]})],64)):T("",!0),o(u,{onClick:e[2]||(e[2]=()=>n(s).open())},{default:a(()=>e[5]||(e[5]=[r("转办")])),_:1,__:[5]}),o(u,{danger:"",type:"primary",onClick:B},{default:a(()=>e[6]||(e[6]=[r("终止")])),_:1,__:[6]})]),default:a(()=>[t.value?(M(),S(n(h),{key:0,column:2,bordered:"",size:"small"},{default:a(()=>[o(n(m),{label:"任务名称"},{default:a(()=>[r(c(t.value.nodeName),1)]),_:1}),o(n(m),{label:"节点编码"},{default:a(()=>[r(c(t.value.nodeCode),1)]),_:1}),o(n(m),{label:"开始时间"},{default:a(()=>[r(c(t.value.createTime),1)]),_:1}),o(n(m),{label:"流程实例ID"},{default:a(()=>[r(c(t.value.instanceId),1)]),_:1}),o(n(m),{label:"版本号"},{default:a(()=>[r(c(t.value.version),1)]),_:1}),o(n(m),{label:"业务ID"},{default:a(()=>[r(c(t.value.businessId),1)]),_:1})]),_:1})):T("",!0),o(n(f),{mode:"single",onFinish:p}),o(n(A),{mode:"multiple",onFinish:N}),o(n(O),{mode:"multiple",onFinish:F})]),_:1})}}});export{Y as _};
