var h=(d,s,e)=>new Promise((i,a)=>{var o=t=>{try{n(e.next(t))}catch(c){a(c)}},u=t=>{try{n(e.throw(t))}catch(c){a(c)}},n=t=>t.done?i(t.value):Promise.resolve(t.value).then(o,u);n((e=e.apply(d,s)).next())});import{av as S,az as T,am as C,aB as k,ap as l,D as A,bl as B}from"./bootstrap-DCMzVRvD.js";import{a as I}from"./oauth-common-CrHfL2p7.js";import{d as L,v as b,a0 as g,c as q,o as v}from"../jse/index-index-C-MnMZEz.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";const J=L({__name:"index",setup(d){var p;const s=S(),e=s.query.code,i=s.query.state,a=JSON.parse(atob(i)),o=s.query.source,u=A,n=(p=a.tenantId)!=null?p:u,t=a.domain,c=T(),_=C(),w=k();return b(()=>h(null,null,function*(){const m=window.location.host;if(t!==m){const r=new URL(window.location.href);r.host=t,window.location.href=r.toString();return}try{if(!I.find(y=>y.source===o)){l.error({content:`未找到${o}平台`});return}const f={grantType:"social",socialCode:e,socialState:i,source:o,tenantId:n};c.accessToken?(yield B(f),l.success(`${o}授权成功`)):(yield _.authLogin(f),l.success(`${o}登录成功`))}catch(r){}finally{setTimeout(()=>{w.push(g.app.defaultHomePath)},1500)}})),(m,r)=>(v(),q("div"))}});export{J as default};
