{"doc": " 角色信息业务对象 sys_role\n\n <AUTHOR>\n", "fields": [{"name": "roleId", "doc": " 角色ID\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 角色名称\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 角色权限字符串\n"}, {"name": "roleSort", "doc": " 显示顺序\n"}, {"name": "dataScope", "doc": " 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限 6：部门及以下或本人数据权限）\n"}, {"name": "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": " 菜单树选择项是否关联显示\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": " 部门树选择项是否关联显示\n"}, {"name": "status", "doc": " 角色状态（0正常 1停用）\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "menuIds", "doc": " 菜单组\n"}, {"name": "deptIds", "doc": " 部门组（数据权限）\n"}], "enumConstants": [], "methods": [], "constructors": []}