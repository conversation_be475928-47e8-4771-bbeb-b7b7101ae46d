var B=Object.defineProperty;var q=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var D=(n,t,o)=>t in n?B(n,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[t]=o,S=(n,t)=>{for(var o in t||(t={}))E.call(t,o)&&D(n,o,t[o]);if(q)for(var o of q(t))F.call(t,o)&&D(n,o,t[o]);return n};var k=(n,t,o)=>new Promise((b,i)=>{var h=r=>{try{p(o.next(r))}catch(u){i(u)}},c=r=>{try{p(o.throw(r))}catch(u){i(u)}},p=r=>r.done?b(r.value):Promise.resolve(r.value).then(h,c);p((o=o.apply(n,t)).next())});import{as as x}from"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import{e as M,f as N}from"./index-B4NcjlQn.js";import{c as O,q as R,_ as j}from"./category-modal.vue_vue_type_script_setup_true_lang-DFF7ib2c.js";import T from"./index-BeyziwLP.js";import{_ as G}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as L,l as V,S as W,h as y,o as _,w as a,a as l,b as s,T as w,k as m,t as f,I as Y}from"../jse/index-index-C-MnMZEz.js";import{u as z}from"./use-vxe-grid-BC7vZzEr.js";import{u as H}from"./use-modal-CeMSCP2m.js";import{P as J}from"./index-DNdMANjv.js";import{g as K}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./popup-D6rC6QBG.js";import"./tree-DFBawhPd.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const ye=L({__name:"index",setup(n){const t={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:R(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},o={columns:O,height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:($,...v)=>k(null,[$,...v],function*(e,d={}){return{rows:yield M(S({},d))}}),querySuccess:()=>{Y(()=>{I()})}}},scrollY:{enabled:!1,gt:0},rowConfig:{keyField:"categoryId"},treeConfig:{parentField:"parentId",rowField:"categoryId",transform:!0},id:"workflow-category-index"},[b,i]=z({formOptions:t,gridOptions:o}),[h,c]=H({connectedComponent:j});function p(e){c.setData({parentId:e==null?void 0:e.categoryId}),c.open()}function r(e){return k(this,null,function*(){c.setData({id:e.categoryId}),c.open()})}function u(e){return k(this,null,function*(){yield N(e.categoryId),yield i.query()})}function I(){var e;(e=i.grid)==null||e.setAllTreeExpand(!0)}function P(){var e;(e=i.grid)==null||e.setAllTreeExpand(!1)}return(e,d)=>{const $=V("a-button"),v=V("ghost-button"),g=W("access");return _(),y(s(G),{"auto-content-height":!0},{default:a(()=>[l(s(b),{"table-title":"流程分类列表"},{"toolbar-tools":a(()=>[l(s(T),null,{default:a(()=>[l($,{onClick:P},{default:a(()=>[m(f(e.$t("pages.common.collapse")),1)]),_:1}),l($,{onClick:I},{default:a(()=>[m(f(e.$t("pages.common.expand")),1)]),_:1}),w((_(),y($,{type:"primary",onClick:p},{default:a(()=>[m(f(e.$t("pages.common.add")),1)]),_:1})),[[g,["workflow:category:add"],"code"]])]),_:1})]),action:a(({row:C})=>[l(s(T),null,{default:a(()=>[w((_(),y(v,{onClick:x(A=>r(C),["stop"])},{default:a(()=>[m(f(e.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[g,["workflow:category:edit"],"code"]]),w((_(),y(v,{class:"btn-success",onClick:x(A=>p(C),["stop"])},{default:a(()=>[m(f(e.$t("pages.common.add")),1)]),_:2},1032,["onClick"])),[[g,["workflow:category:edit"],"code"]]),l(s(J),{"get-popup-container":s(K),placement:"left",title:"确认删除？",onConfirm:A=>u(C)},{default:a(()=>[w((_(),y(v,{danger:"",onClick:d[0]||(d[0]=x(()=>{},["stop"]))},{default:a(()=>[m(f(e.$t("pages.common.delete")),1)]),_:1})),[[g,["workflow:category:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),l(s(h),{onReload:d[1]||(d[1]=C=>s(i).query())})]),_:1})}}});export{ye as default};
