var d=(u,m,o)=>new Promise((i,c)=>{var f=e=>{try{r(o.next(e))}catch(l){c(l)}},a=e=>{try{r(o.throw(e))}catch(l){c(l)}},r=e=>e.done?i(e.value):Promise.resolve(e.value).then(f,a);r((o=o.apply(u,m)).next())});import{al as C,$ as p,aj as I}from"./bootstrap-DCMzVRvD.js";import{c as N,e as T,f as k}from"./dict-type-GiaNpHd1.js";import{u as v,d as h}from"./popup-D6rC6QBG.js";import{d as B,p as x,B as V,P as z,h as F,o as A,w as D,a as q,b as y}from"../jse/index-index-C-MnMZEz.js";import{u as G}from"./use-modal-CeMSCP2m.js";const P=()=>[{component:"Input",fieldName:"dictName",label:"字典名称"},{component:"Input",fieldName:"dictType",label:"字典类型"}],W=[{type:"checkbox",width:60},{title:"字典名称",field:"dictName"},{title:"字典类型",field:"dictType"},{title:"备注",field:"remark"},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],$=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"dictId",label:"dictId"},{component:"Input",fieldName:"dictName",label:"字典名称",rules:"required"},{component:"Input",fieldName:"dictType",help:"使用英文/下划线命名, 如:sys_normal_disable",label:"字典类型",rules:C().regex(/^[a-z_]+$/i,{message:"字典类型只能使用英文/下划线命名"})},{component:"Textarea",fieldName:"remark",label:"备注"}],E=B({__name:"dict-type-modal",emits:["reload"],setup(u,{emit:m}){const o=m,i=x(!1),c=V(()=>i.value?p("pages.common.edit"):p("pages.common.add")),[f,a]=I({layout:"vertical",commonConfig:{labelWidth:100},schema:$(),showDefaultActions:!1}),{onBeforeClose:r,markInitialized:e,resetInitialized:l}=v({initializedGetter:h(a),currentGetter:h(a)}),[g,n]=G({fullscreenButton:!1,onBeforeClose:r,onClosed:w,onConfirm:b,onOpenChange:s=>d(null,null,function*(){if(!s)return null;n.modalLoading(!0);const{id:t}=n.getData();if(i.value=!!t,i.value&&t){const _=yield N(t);yield a.setValues(_)}yield e(),n.modalLoading(!1)})});function b(){return d(this,null,function*(){try{n.lock(!0);const{valid:s}=yield a.validate();if(!s)return;const t=z(yield a.getValues());yield i.value?T(t):k(t),l(),o("reload"),n.close()}catch(s){console.error(s)}finally{n.lock(!1)}})}function w(){return d(this,null,function*(){yield a.resetForm(),l()})}return(s,t)=>(A(),F(y(g),{title:c.value},{default:D(()=>[q(y(f))]),_:1},8,["title"]))}});export{E as _,W as c,P as q};
