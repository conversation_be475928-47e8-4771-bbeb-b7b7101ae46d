var m=(o,t,n)=>new Promise((c,a)=>{var i=e=>{try{l(n.next(e))}catch(s){a(s)}},r=e=>{try{l(n.throw(e))}catch(s){a(s)}},l=e=>e.done?c(e.value):Promise.resolve(e.value).then(i,r);l((n=n.apply(o,t)).next())});import{y as u,aj as f}from"./bootstrap-DCMzVRvD.js";import{d as p,p as g,B as h,h as _,o as v,w as y,a as w,b as d}from"../jse/index-index-C-MnMZEz.js";import{u as C}from"./use-modal-CeMSCP2m.js";function b(o){return u.postWithMsg(`/system/sse/sendAll?message=${o}`)}function B(o,t){return u.postWithMsg(`/system/sse/send/${o}?message=${t}`)}function M(){return u.get("/system/sse/list")}const V=p({__name:"send-msg-modal",setup(o){const t=g(void 0),n=h(()=>t.value?"发送指定消息":"发送全体消息"),[c,a]=C({onConfirm:l,onOpenChange:e=>{if(!e)return null;const s=a.getData();t.value=s.userId}}),[i,r]=f({layout:"vertical",commonConfig:{formItemClass:"col-span-2",componentProps:{class:"w-full"},labelWidth:80},schema:[{component:"Textarea",label:"消息内容",fieldName:"content",rules:"required"}],showDefaultActions:!1,wrapperClass:"grid-cols-2"});function l(){return m(this,null,function*(){try{a.modalLoading(!0);const{valid:e}=yield r.validate();if(!e)return;const{content:s}=yield r.getValues();yield t.value?B(t.value,s):b(s),a.close()}catch(e){console.error(e)}finally{a.modalLoading(!1)}})}return(e,s)=>(v(),_(d(c),{"close-on-click-modal":!1,"fullscreen-button":!1,title:n.value},{default:y(()=>[w(d(i))]),_:1},8,["title"]))}});export{V as _,M as s};
