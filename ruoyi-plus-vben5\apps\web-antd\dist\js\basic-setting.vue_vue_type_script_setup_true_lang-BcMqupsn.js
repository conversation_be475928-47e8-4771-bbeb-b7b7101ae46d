var s=(o,m,r)=>new Promise((i,a)=>{var u=l=>{try{p(r.next(l))}catch(e){a(e)}},d=l=>{try{p(r.throw(l))}catch(e){a(e)}},p=l=>l.done?i(l.value):Promise.resolve(l.value).then(u,d);p((r=r.apply(o,m)).next())});import{al as h,br as y,$ as f}from"./bootstrap-DCMzVRvD.js";import{d as I}from"./index-C5dPwGGG.js";import{a as b}from"./get-popup-container-P4S1sr5h.js";import{d as P,a5 as v,v as w,h as F,o as S,b as N,w as g,a as C,e as V,g as _}from"../jse/index-index-C-MnMZEz.js";import{R as q,C as T}from"./index-By0xk_Xq.js";import{l as x,a as R}from"./tree-DFBawhPd.js";const D=()=>[{component:"Divider",componentProps:{orientation:"left"},fieldName:"divider1",formItemClass:"col-span-2",label:"基本信息"},{component:"Input",fieldName:"tableName",label:"表名称",rules:"required"},{component:"Input",fieldName:"tableComment",label:"表描述",rules:"required"},{component:"Input",fieldName:"className",label:"实体类名称",rules:"required"},{component:"Input",fieldName:"functionAuthor",label:"作者",rules:"required"},{component:"Divider",componentProps:{orientation:"left"},fieldName:"divider2",formItemClass:"col-span-2",label:"生成信息"},{component:"Select",componentProps:{allowClear:!1,getPopupContainer:b,options:[{label:"单表(增删改查)",value:"crud"},{label:"树表(增删改查)",value:"tree"}]},defaultValue:"crud",fieldName:"tplCategory",label:"模板类型",rules:"selectRequired"},{component:"Select",componentProps:{getPopupContainer:b},dependencies:{show:o=>o.tplCategory==="tree",triggerFields:["tplCategory"]},fieldName:"treeCode",helpMessage:"树节点显示的编码字段名， 如: dept_id (相当于id)",label:"树编码字段",rules:"selectRequired"},{component:"Select",componentProps:{allowClear:!1},dependencies:{show:o=>o.tplCategory==="tree",triggerFields:["tplCategory"]},fieldName:"treeParentCode",help:"树节点显示的父编码字段名， 如: parent_Id (相当于parentId)",label:"树父编码字段",rules:"selectRequired"},{component:"Select",componentProps:{allowClear:!1},dependencies:{show:o=>o.tplCategory==="tree",triggerFields:["tplCategory"]},fieldName:"treeName",help:"树节点的显示名称字段名， 如: dept_name (相当于label)",label:"树名称字段",rules:"selectRequired"},{component:"Input",fieldName:"packageName",help:"生成在哪个java包下, 例如 com.ruoyi.system",label:"生成包路径",rules:"required"},{component:"Input",fieldName:"moduleName",help:"可理解为子系统名，例如 system",label:"生成模块名",rules:"required"},{component:"Input",fieldName:"businessName",help:"可理解为功能英文名，例如 user",label:"生成业务名",rules:"required"},{component:"Input",fieldName:"functionName",help:"用作类描述，例如 用户",label:"生成功能名",rules:"required"},{component:"TreeSelect",componentProps:{allowClear:!1,getPopupContainer:b},defaultValue:0,fieldName:"parentMenuId",label:"上级菜单"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:[{label:"modal弹窗",value:"modal"},{label:"drawer抽屉",value:"drawer"}],optionType:"button"},help:"自定义功能, 需要后端支持",defaultValue:"modal",fieldName:"popupComponent",label:"弹窗组件类型"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:[{label:"useVbenForm",value:"useForm"},{label:"antd原生表单",value:"native"}],optionType:"button"},help:`自定义功能, 需要后端支持
复杂(布局, 联动等)表单建议用antd原生表单`,defaultValue:"useForm",fieldName:"formComponent",label:"生成表单类型"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:[{label:"zip压缩包",value:"0"},{label:"自定义路径",value:"1"}],optionType:"button"},defaultValue:"0",fieldName:"genType",help:"默认为zip压缩包下载, 也可以自定义生成路径",label:"生成代码方式"},{component:"Input",defaultValue:"/",dependencies:{show:o=>o.genType==="1",triggerFields:["genType"]},fieldName:"genPath",help:'输入绝对路径, 不支持"./"相对路径',label:"代码生成路径",rules:h().regex(/^(?:[a-z]:)?(?:\/|(?:\\|\/)[^\\/:*?"<>|\r\n]+)*(?:\\|\/)?$/i,{message:"请输入合法的路径"})},{component:"Textarea",fieldName:"remark",formItemClass:"col-span-2 items-baseline",label:"备注"}],B=P({__name:"basic-setting",setup(o,{expose:m}){const r=v("genInfoData"),[i,a]=y({commonConfig:{componentProps:{class:"w-full",formItemClass:"col-span-1"},labelWidth:150},schema:D(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function u(e){return s(this,null,function*(){const t=e.map(n=>({label:`${n.columnName} | ${n.columnComment}`,value:n.columnName}));a.updateSchema([{componentProps:{options:t},fieldName:"treeCode"},{componentProps:{options:t},fieldName:"treeParentCode"},{componentProps:{options:t},fieldName:"treeName"}])})}function d(){return s(this,null,function*(){const e=yield I();e.forEach(c=>{c.menuName=f(c.menuName)});const t=x(e,{id:"menuId",pid:"parentId"}),n=[{fullName:f("menu.root"),menuId:0,menuName:f("menu.root"),children:t}];R(n,"menuName"," / "),a.updateSchema([{componentProps:{fieldNames:{label:"menuName",value:"menuId"},listHeight:300,treeData:n,treeDefaultExpandAll:!1,treeDefaultExpandedKeys:[0],treeLine:{showLeafIcon:!1},treeNodeLabelProp:"fullName"},fieldName:"parentMenuId"}])})}w(()=>s(null,null,function*(){const e=r.value;if(yield a.setValues(e),e.options){const{popupComponent:t,formComponent:n}=JSON.parse(e.options);t&&a.setFieldValue("popupComponent",t),n&&a.setFieldValue("formComponent",n)}yield Promise.all([u(e.columns),d()])}));function p(){return s(this,null,function*(){const{valid:e}=yield a.validate();return!!e})}function l(){return s(this,null,function*(){return yield a.getValues()})}return m({validateForm:p,getFormValues:l}),(e,t)=>(S(),F(N(q),{justify:"center"},{default:g(()=>[C(N(T),V(_({xs:24,sm:24,md:20,lg:16,xl:16})),{default:g(()=>[C(N(i))]),_:1},16)]),_:1}))}});export{B as _};
