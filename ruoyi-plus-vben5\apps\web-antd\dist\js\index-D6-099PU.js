var I=Object.defineProperty;var o=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var t=(n,e,r)=>e in n?I(n,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[e]=r,a=(n,e)=>{for(var r in e||(e={}))m.call(e,r)&&t(n,r,e[r]);if(o)for(var r of o(e))i.call(e,r)&&t(n,r,e[r]);return n};import{ah as c,ai as p}from"./bootstrap-DCMzVRvD.js";import{d as f,M as s}from"../jse/index-index-C-MnMZEz.js";function $(n){return f({name:`Icon-${n}`,setup(e,{attrs:r}){return()=>s(c,a(a({icon:n},e),r))}})}function l(n,e){return f({name:`Icon-${n}`,setup(r,{attrs:u}){return p(n,e),()=>s(c,a(a({icon:n},r),u))}})}export{$ as a,l as c};
