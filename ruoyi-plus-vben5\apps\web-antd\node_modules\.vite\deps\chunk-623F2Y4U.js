import {
  Checkbox_default
} from "./chunk-L5LHNRYZ.js";
import {
  style_default
} from "./chunk-PRUP77ZU.js";
import {
  FormItemInputContext,
  useInjectFormItemContext
} from "./chunk-SKZ6LSCT.js";
import {
  _objectSpread2,
  classNames_default,
  flattenChildren,
  useConfigInject_default,
  useInjectDisabled,
  warning_default2 as warning_default
} from "./chunk-R5AV7YOM.js";
import {
  _extends
} from "./chunk-YTNASAWS.js";
import {
  vue_types_default
} from "./chunk-YGPH4FOT.js";
import {
  arrayType,
  booleanType,
  functionType,
  stringType
} from "./chunk-KHBHQMJ3.js";
import {
  computed,
  createVNode,
  defineComponent,
  inject,
  onBeforeUnmount,
  onMounted,
  provide,
  ref,
  watch,
  watchEffect
} from "./chunk-7J2PGW6H.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/checkbox/interface.js
var abstractCheckboxGroupProps = () => {
  return {
    name: String,
    prefixCls: String,
    options: arrayType([]),
    disabled: Boolean,
    id: String
  };
};
var checkboxGroupProps = () => {
  return _extends(_extends({}, abstractCheckboxGroupProps()), {
    defaultValue: arrayType(),
    value: arrayType(),
    onChange: functionType(),
    "onUpdate:value": functionType()
  });
};
var abstractCheckboxProps = () => {
  return {
    prefixCls: String,
    defaultChecked: booleanType(),
    checked: booleanType(),
    disabled: booleanType(),
    isGroup: booleanType(),
    value: vue_types_default.any,
    name: String,
    id: String,
    indeterminate: booleanType(),
    type: stringType("checkbox"),
    autofocus: booleanType(),
    onChange: functionType(),
    "onUpdate:checked": functionType(),
    onClick: functionType(),
    skipGroup: booleanType(false)
  };
};
var checkboxProps = () => {
  return _extends(_extends({}, abstractCheckboxProps()), {
    indeterminate: booleanType(false)
  });
};
var CheckboxGroupContextKey = Symbol("CheckboxGroupContext");

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/checkbox/Checkbox.js
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var Checkbox_default2 = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ACheckbox",
  inheritAttrs: false,
  __ANT_CHECKBOX: true,
  props: checkboxProps(),
  // emits: ['change', 'update:checked'],
  setup(props, _ref) {
    let {
      emit,
      attrs,
      slots,
      expose
    } = _ref;
    const formItemContext = useInjectFormItemContext();
    const formItemInputContext = FormItemInputContext.useInject();
    const {
      prefixCls,
      direction,
      disabled
    } = useConfigInject_default("checkbox", props);
    const contextDisabled = useInjectDisabled();
    const [wrapSSR, hashId] = style_default(prefixCls);
    const checkboxGroup = inject(CheckboxGroupContextKey, void 0);
    const uniId = Symbol("checkboxUniId");
    const mergedDisabled = computed(() => {
      return (checkboxGroup === null || checkboxGroup === void 0 ? void 0 : checkboxGroup.disabled.value) || disabled.value;
    });
    watchEffect(() => {
      if (!props.skipGroup && checkboxGroup) {
        checkboxGroup.registerValue(uniId, props.value);
      }
    });
    onBeforeUnmount(() => {
      if (checkboxGroup) {
        checkboxGroup.cancelValue(uniId);
      }
    });
    onMounted(() => {
      warning_default(!!(props.checked !== void 0 || checkboxGroup || props.value === void 0), "Checkbox", "`value` is not validate prop, do you mean `checked`?");
    });
    const handleChange = (event) => {
      const targetChecked = event.target.checked;
      emit("update:checked", targetChecked);
      emit("change", event);
      formItemContext.onFieldChange();
    };
    const checkboxRef = ref();
    const focus = () => {
      var _a;
      (_a = checkboxRef.value) === null || _a === void 0 ? void 0 : _a.focus();
    };
    const blur = () => {
      var _a;
      (_a = checkboxRef.value) === null || _a === void 0 ? void 0 : _a.blur();
    };
    expose({
      focus,
      blur
    });
    return () => {
      var _a;
      const children = flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));
      const {
        indeterminate,
        skipGroup,
        id = formItemContext.id.value
      } = props, restProps = __rest(props, ["indeterminate", "skipGroup", "id"]);
      const {
        onMouseenter,
        onMouseleave,
        onInput,
        class: className,
        style
      } = attrs, restAttrs = __rest(attrs, ["onMouseenter", "onMouseleave", "onInput", "class", "style"]);
      const checkboxProps2 = _extends(_extends(_extends(_extends({}, restProps), {
        id,
        prefixCls: prefixCls.value
      }), restAttrs), {
        disabled: mergedDisabled.value
      });
      if (checkboxGroup && !skipGroup) {
        checkboxProps2.onChange = function() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          emit("change", ...args);
          checkboxGroup.toggleOption({
            label: children,
            value: props.value
          });
        };
        checkboxProps2.name = checkboxGroup.name.value;
        checkboxProps2.checked = checkboxGroup.mergedValue.value.includes(props.value);
        checkboxProps2.disabled = mergedDisabled.value || contextDisabled.value;
        checkboxProps2.indeterminate = indeterminate;
      } else {
        checkboxProps2.onChange = handleChange;
      }
      const classString = classNames_default({
        [`${prefixCls.value}-wrapper`]: true,
        [`${prefixCls.value}-rtl`]: direction.value === "rtl",
        [`${prefixCls.value}-wrapper-checked`]: checkboxProps2.checked,
        [`${prefixCls.value}-wrapper-disabled`]: checkboxProps2.disabled,
        [`${prefixCls.value}-wrapper-in-form-item`]: formItemInputContext.isFormItemInput
      }, className, hashId.value);
      const checkboxClass = classNames_default({
        [`${prefixCls.value}-indeterminate`]: indeterminate
      }, hashId.value);
      const ariaChecked = indeterminate ? "mixed" : void 0;
      return wrapSSR(createVNode("label", {
        "class": classString,
        "style": style,
        "onMouseenter": onMouseenter,
        "onMouseleave": onMouseleave
      }, [createVNode(Checkbox_default, _objectSpread2(_objectSpread2({
        "aria-checked": ariaChecked
      }, checkboxProps2), {}, {
        "class": checkboxClass,
        "ref": checkboxRef
      }), null), children.length ? createVNode("span", null, [children]) : null]));
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/checkbox/Group.js
var Group_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ACheckboxGroup",
  inheritAttrs: false,
  props: checkboxGroupProps(),
  // emits: ['change', 'update:value'],
  setup(props, _ref) {
    let {
      slots,
      attrs,
      emit,
      expose
    } = _ref;
    const formItemContext = useInjectFormItemContext();
    const {
      prefixCls,
      direction
    } = useConfigInject_default("checkbox", props);
    const groupPrefixCls = computed(() => `${prefixCls.value}-group`);
    const [wrapSSR, hashId] = style_default(groupPrefixCls);
    const mergedValue = ref((props.value === void 0 ? props.defaultValue : props.value) || []);
    watch(() => props.value, () => {
      mergedValue.value = props.value || [];
    });
    const options = computed(() => {
      return props.options.map((option) => {
        if (typeof option === "string" || typeof option === "number") {
          return {
            label: option,
            value: option
          };
        }
        return option;
      });
    });
    const triggerUpdate = ref(Symbol());
    const registeredValuesMap = ref(/* @__PURE__ */ new Map());
    const cancelValue = (id) => {
      registeredValuesMap.value.delete(id);
      triggerUpdate.value = Symbol();
    };
    const registerValue = (id, value) => {
      registeredValuesMap.value.set(id, value);
      triggerUpdate.value = Symbol();
    };
    const registeredValues = ref(/* @__PURE__ */ new Map());
    watch(triggerUpdate, () => {
      const valuseMap = /* @__PURE__ */ new Map();
      for (const value of registeredValuesMap.value.values()) {
        valuseMap.set(value, true);
      }
      registeredValues.value = valuseMap;
    });
    const toggleOption = (option) => {
      const optionIndex = mergedValue.value.indexOf(option.value);
      const value = [...mergedValue.value];
      if (optionIndex === -1) {
        value.push(option.value);
      } else {
        value.splice(optionIndex, 1);
      }
      if (props.value === void 0) {
        mergedValue.value = value;
      }
      const val = value.filter((val2) => registeredValues.value.has(val2)).sort((a, b) => {
        const indexA = options.value.findIndex((opt) => opt.value === a);
        const indexB = options.value.findIndex((opt) => opt.value === b);
        return indexA - indexB;
      });
      emit("update:value", val);
      emit("change", val);
      formItemContext.onFieldChange();
    };
    provide(CheckboxGroupContextKey, {
      cancelValue,
      registerValue,
      toggleOption,
      mergedValue,
      name: computed(() => props.name),
      disabled: computed(() => props.disabled)
    });
    expose({
      mergedValue
    });
    return () => {
      var _a;
      const {
        id = formItemContext.id.value
      } = props;
      let children = null;
      if (options.value && options.value.length > 0) {
        children = options.value.map((option) => {
          var _a2;
          return createVNode(Checkbox_default2, {
            "prefixCls": prefixCls.value,
            "key": option.value.toString(),
            "disabled": "disabled" in option ? option.disabled : props.disabled,
            "indeterminate": option.indeterminate,
            "value": option.value,
            "checked": mergedValue.value.indexOf(option.value) !== -1,
            "onChange": option.onChange,
            "class": `${groupPrefixCls.value}-item`
          }, {
            default: () => [slots.label !== void 0 ? (_a2 = slots.label) === null || _a2 === void 0 ? void 0 : _a2.call(slots, option) : option.label]
          });
        });
      }
      return wrapSSR(createVNode("div", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": [groupPrefixCls.value, {
          [`${groupPrefixCls.value}-rtl`]: direction.value === "rtl"
        }, attrs.class, hashId.value],
        "id": id
      }), [children || ((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots))]));
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/checkbox/index.js
Checkbox_default2.Group = Group_default;
Checkbox_default2.install = function(app) {
  app.component(Checkbox_default2.name, Checkbox_default2);
  app.component(Group_default.name, Group_default);
  return app;
};
var checkbox_default = Checkbox_default2;

export {
  checkboxGroupProps,
  checkboxProps,
  Group_default,
  checkbox_default
};
//# sourceMappingURL=chunk-623F2Y4U.js.map
