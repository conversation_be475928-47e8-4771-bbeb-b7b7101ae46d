var c=(f,m,l)=>new Promise((r,d)=>{var u=t=>{try{n(l.next(t))}catch(i){d(i)}},e=t=>{try{n(l.throw(t))}catch(i){d(i)}},n=t=>t.done?r(t.value):Promise.resolve(t.value).then(u,e);n((l=l.apply(f,m)).next())});import{$ as y,aj as _}from"./bootstrap-DCMzVRvD.js";import{a as v,b as B,d as V,e as D}from"./index-B4NcjlQn.js";import{u as x,d as N}from"./popup-D6rC6QBG.js";import{d as F,p as L,B as k,P as A,h as P,o as S,w as q,a as z,b as h}from"../jse/index-index-C-MnMZEz.js";import{u as T}from"./use-modal-CeMSCP2m.js";import{l as G,a as M}from"./tree-DFBawhPd.js";import{a as U}from"./get-popup-container-P4S1sr5h.js";const R=()=>[{fieldName:"categoryName",label:"分类名称",component:"Input"},{fieldName:"categoryCode",label:"分类编码",component:"Input"}],X=[{field:"categoryName",title:"分类名称",treeNode:!0},{field:"orderNum",title:"排序"},{field:"createTime",title:"创建时间"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],$=()=>[{label:"categoryId",fieldName:"categoryId",component:"Input",dependencies:{show:()=>!1,triggerFields:[""]}},{fieldName:"parentId",label:"父级分类",rules:"required",defaultValue:100,component:"TreeSelect"},{fieldName:"categoryName",label:"分类名称",component:"Input",rules:"required"},{fieldName:"orderNum",label:"排序",component:"InputNumber"}],Y=F({__name:"category-modal",emits:["reload"],setup(f,{emit:m}){const l=m,r=L(!1),d=k(()=>r.value?y("pages.common.edit"):y("pages.common.add")),[u,e]=_({commonConfig:{formItemClass:"col-span-2",labelWidth:80,componentProps:{class:"w-full"}},schema:$(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function n(){return c(this,null,function*(){const o=yield D(),a=G(o,{id:"categoryId",pid:"parentId"});M(a,"categoryName"," / "),e.updateSchema([{fieldName:"parentId",componentProps:{treeData:a,treeLine:{showLeafIcon:!1},fieldNames:{label:"categoryName",value:"categoryId"},treeDefaultExpandAll:!0,treeNodeLabelProp:"fullName",getPopupContainer:U}}])})}const{onBeforeClose:t,markInitialized:i,resetInitialized:p}=x({initializedGetter:N(e),currentGetter:N(e)}),[w,s]=T({fullscreenButton:!1,onBeforeClose:t,onClosed:b,onConfirm:I,onOpenChange:o=>c(null,null,function*(){if(!o)return null;s.modalLoading(!0);const{id:a,parentId:g}=s.getData();if(r.value=!!a,r.value&&a){const C=yield v(a);yield e.setValues(C)}g&&(yield e.setValues({parentId:g})),yield n(),yield i(),s.modalLoading(!1)})});function I(){return c(this,null,function*(){try{s.lock(!0);const{valid:o}=yield e.validate();if(!o)return;const a=A(yield e.getValues());yield r.value?B(a):V(a),p(),l("reload"),s.close()}catch(o){console.error(o)}finally{s.lock(!1)}})}function b(){return c(this,null,function*(){yield e.resetForm(),p()})}return(o,a)=>(S(),P(h(w),{title:d.value,class:"min-h-[500px]"},{default:q(()=>[z(h(u))]),_:1},8,["title"]))}});export{Y as _,X as c,R as q};
