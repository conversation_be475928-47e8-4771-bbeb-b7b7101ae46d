import{Textarea as o}from"./index-BxBCzu2M.js";import{d as r,c as l,o as n,j as s,a as p,t as i,b as u}from"../jse/index-index-C-MnMZEz.js";const c={class:"flex flex-col gap-2"},f=r({name:"ApprovalContent",inheritAttrs:!1,__name:"approval-content",props:{description:{},value:{}},emits:["update:value"],setup(d){return(e,a)=>(n(),l("div",c,[s("div",null,i(e.description),1),p(u(o),{"allow-clear":!0,"auto-size":!0,value:e.value,placeholder:"审批意见(可选)",onChange:a[0]||(a[0]=t=>e.$emit("update:value",t.target.value))},null,8,["value"])]))}});export{f as _};
