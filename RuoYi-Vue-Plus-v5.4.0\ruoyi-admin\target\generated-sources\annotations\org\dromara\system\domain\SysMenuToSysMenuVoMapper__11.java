package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__11;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysMenuVoToSysMenuMapper__11.class,SysMenuBoToSysMenuMapper__11.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__11 extends BaseMapper<SysMenu, SysMenuVo> {
}
