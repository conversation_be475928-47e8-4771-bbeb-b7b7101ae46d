{"$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "version": "0.2", "language": "en,en-US", "allowCompoundWords": true, "words": ["acmr", "antd", "antdv", "astro", "brotli", "clientid", "clsx", "defu", "demi", "echarts", "ependencies", "esno", "etag", "execa", "<PERSON><PERSON><PERSON>", "iconify", "iconoir", "intlify", "ipaddr", "jsencrypt", "lockb", "logininfor", "lucide", "minh", "minw", "mkdist", "mockjs", "<PERSON><PERSON>", "nocheck", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "nprogress", "nuxt", "oper", "operlog", "pinia", "prefixs", "publint", "Qqchat", "qrcode", "ruoyi", "shadcn", "sonner", "sortablejs", "styl", "taze", "ui-kit", "uicons", "unplugin", "unref", "vben", "vbenjs", "vite", "vite<PERSON><PERSON>", "vitepress", "vnode", "vueuse", "yxxx"], "ignorePaths": ["**/node_modules/**", "**/dist/**", "**/*-dist/**", "**/icons/**", "pnpm-lock.yaml", "**/*.log", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**"]}