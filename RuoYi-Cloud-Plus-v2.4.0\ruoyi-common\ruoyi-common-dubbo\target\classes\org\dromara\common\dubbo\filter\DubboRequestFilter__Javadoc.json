{"doc": " Dubbo 日志过滤器\n <p>\n 该过滤器通过实现 Dubbo 的 Filter 接口，在服务调用前后记录日志信息\n 可根据配置开关和日志级别输出不同详细程度的日志信息\n <p>\n 激活条件：\n - 在 Provider 和 Consumer 端都生效\n - 执行顺序设置为最大值，确保在所有其他过滤器之后执行\n <p>\n 使用 SpringUtils 获取配置信息，根据配置决定是否记录日志及日志详细程度\n <p>\n 使用 Lombok 的 @Slf4j 注解简化日志记录\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "invoke", "paramTypes": ["org.apache.dubbo.rpc.Invoker", "org.apache.dubbo.rpc.Invocation"], "doc": " Dubbo Filter 接口实现方法，处理服务调用逻辑并记录日志\n\n @param invoker    Dubbo 服务调用者实例\n @param invocation 调用的具体方法信息\n @return 调用结果\n @throws RpcException 如果调用过程中发生异常\n"}], "constructors": []}