{"doc": " 仓库Service接口\n\n <AUTHOR>\n @date 2025-06-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询仓库\n\n @param warehouseId 主键\n @return 仓库\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询仓库列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 仓库分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo"], "doc": " 查询符合条件的仓库列表\n\n @param bo 查询条件\n @return 仓库列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo"], "doc": " 新增仓库\n\n @param bo 仓库\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo"], "doc": " 修改仓库\n\n @param bo 仓库\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除仓库信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}