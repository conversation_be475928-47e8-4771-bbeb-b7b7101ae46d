<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-api-bom</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <description>
        ruoyi-api-bom api依赖项
    </description>

    <properties>
        <revision>2.4.0</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 系统接口 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-api-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 资源服务接口 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-api-resource</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- workflow接口 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-api-workflow</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
