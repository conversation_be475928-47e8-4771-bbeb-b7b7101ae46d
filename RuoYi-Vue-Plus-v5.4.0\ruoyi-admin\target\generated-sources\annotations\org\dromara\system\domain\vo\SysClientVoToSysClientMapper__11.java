package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysClient;
import org.dromara.system.domain.SysClientToSysClientVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysClientToSysClientVoMapper__11.class},
    imports = {}
)
public interface SysClientVoToSysClientMapper__11 extends BaseMapper<SysClientVo, SysClient> {
}
