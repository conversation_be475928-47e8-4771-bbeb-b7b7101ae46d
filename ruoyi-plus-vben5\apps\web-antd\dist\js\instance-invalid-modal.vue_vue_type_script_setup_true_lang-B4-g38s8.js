var m=(d,s,t)=>new Promise((r,o)=>{var i=a=>{try{n(t.next(a))}catch(e){o(e)}},l=a=>{try{n(t.throw(a))}catch(e){o(e)}},n=a=>a.done?r(a.value):Promise.resolve(a.value).then(i,l);n((t=t.apply(d,s)).next())});import{aj as u,aw as p}from"./bootstrap-DCMzVRvD.js";import{w}from"./index-DCFckLr6.js";import{d as _,h,o as C,w as b,a as g,b as f}from"../jse/index-index-C-MnMZEz.js";import{u as v}from"./use-modal-CeMSCP2m.js";const A=_({__name:"instance-invalid-modal",emits:["reload"],setup(d,{emit:s}){const t=s,[r,o]=v({onConfirm:a,onCancel:n,fullscreenButton:!1,title:"作废原因"}),[i,l]=u({commonConfig:{formItemClass:"col-span-2",componentProps:{class:"w-full"},labelWidth:80},layout:"vertical",schema:[{fieldName:"comment",label:"作废原因",component:"Textarea"}],showDefaultActions:!1,wrapperClass:"grid-cols-2"});function n(){return m(this,null,function*(){o.close(),yield l.resetForm()})}function a(){return m(this,null,function*(){try{o.modalLoading(!0);const{valid:e}=yield l.validate();if(!e)return;const c=p(yield l.getValues());c.id=o.getData().id,yield w(c),t("reload"),n()}catch(e){console.error(e)}finally{o.modalLoading(!1)}})}return(e,c)=>(C(),h(f(r),null,{default:b(()=>[g(f(i))]),_:1}))}});export{A as _};
