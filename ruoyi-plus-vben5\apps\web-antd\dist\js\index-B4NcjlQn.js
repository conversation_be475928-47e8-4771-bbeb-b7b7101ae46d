import{y as e}from"./bootstrap-DCMzVRvD.js";function r(){return e.get("/workflow/category/categoryTree")}function a(t){return e.get("/workflow/category/list",{params:t})}function c(t){return e.get(`/workflow/category/${t}`)}function n(t){return e.postWithMsg("/workflow/category",t)}function g(t){return e.putWithMsg("/workflow/category",t)}function f(t){return e.deleteWithMsg(`/workflow/category/${t}`)}export{c as a,g as b,r as c,n as d,a as e,f};
