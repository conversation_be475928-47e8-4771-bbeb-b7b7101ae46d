import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-tabler@1.2.95/node_modules/@iconify/icons-tabler/category-plus.js
var require_category_plus = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-tabler@1.2.95/node_modules/@iconify/icons-tabler/category-plus.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4h6v6H4zm10 0h6v6h-6zM4 14h6v6H4zm10 3h6m-3-3v6"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_category_plus();
//# sourceMappingURL=@iconify_icons-tabler_category-plus.js.map
