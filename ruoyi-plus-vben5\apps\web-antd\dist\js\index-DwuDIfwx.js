var E=Object.defineProperty;var I=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var M=(p,i,a)=>i in p?E(p,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):p[i]=a,P=(p,i)=>{for(var a in i||(i={}))F.call(i,a)&&M(p,a,i[a]);if(I)for(var a of I(i))U.call(i,a)&&M(p,a,i[a]);return p};var u=(p,i,a)=>new Promise((N,r)=>{var D=d=>{try{k(a.next(d))}catch(w){r(w)}},$=d=>{try{k(a.throw(d))}catch(w){r(w)}},k=d=>d.done?N(d.value):Promise.resolve(d.value).then(D,$);k((a=a.apply(p,i)).next())});import{aB as H,as as v,ap as _,an as J}from"./bootstrap-DCMzVRvD.js";import{d as K,v as Q,l as X,S as Z,h as c,o as f,w as l,a as C,b as s,T as g,k as b,t as h,j as ee,U as te}from"../jse/index-index-C-MnMZEz.js";import{v as x}from"./vxe-table-DzEj5Fop.js";import{g as oe,b as ae,s as ne,c as T,d as ie,f as S}from"./index-BntC6MFc.js";import{d as z}from"./download-UJak946_.js";import le from"./code-preview-modal-Co-7X6zj.js";import{_ as se}from"./table-import-modal.vue_vue_type_script_setup_true_lang-7XDdxioi.js";import re from"./index-BeyziwLP.js";import{_ as pe}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{u as me}from"./use-vxe-grid-BC7vZzEr.js";import{u as V}from"./use-modal-CeMSCP2m.js";import{P as Y}from"./index-DNdMANjv.js";import{g as B}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./code-mirror.vue_vue_type_script_setup_true_lang-DhrTT7Nl.js";import"./index-BLwHKR_M.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./index-CHpIOV4R.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./index-kC0HFDdy.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const de=()=>[{component:"Select",fieldName:"dataName",label:"数据源",defaultValue:"",componentProps:{allowClear:!1}},{component:"Input",fieldName:"tableName",label:"表名称"},{component:"Input",fieldName:"tableComment",label:"表描述"},{component:"RangePicker",fieldName:"createTime",label:"创建时间"}],ce=[{type:"checkbox",width:60},{field:"tableName",title:"表名称"},{field:"tableComment",title:"表描述"},{field:"className",title:"实体类"},{field:"createTime",title:"创建时间"},{field:"updateTime",title:"更新时间"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",width:300}],Ee=K({__name:"index",setup(p){const i={schema:de(),commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",fieldMappingTime:[["createTime",["params[beginTime]","params[endTime]"],["YYYY-MM-DD 00:00:00","YYYY-MM-DD 23:59:59"]]]},a={checkboxConfig:{highlight:!0,reserve:!0,trigger:"row"},columns:ce,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(o,...n)=>u(null,[o,...n],function*({page:e},t={}){return yield ae(P({pageNum:e.currentPage,pageSize:e.pageSize},t))})}},rowConfig:{keyField:"tableId"},id:"tool-gen-index"},[N,r]=me({formOptions:i,gridOptions:a});Q(()=>u(null,null,function*(){const e=yield oe(),t=[{label:"全部",value:""}],o=e.map(n=>({label:n,value:n}));t.push(...o),r.formApi.updateSchema([{fieldName:"dataName",componentProps:{options:t}}])}));const[D,$]=V({connectedComponent:le});function k(e){$.setData({tableId:e.tableId}),$.open()}const d=H();function w(e){d.push(`/tool/gen-edit/index/${e.tableId}`)}function q(e){return u(this,null,function*(){yield ne(e.tableId),yield r.query()})}function O(){return u(this,null,function*(){const t=r.grid.getCheckboxRecords().map(n=>n.tableId);if(t.length===0){_.info("请选择需要生成代码的表");return}const o=_.loading("下载中...");try{const n=t.join(","),m=yield T(n),y=Date.now();z(m,`批量代码生成_${y}.zip`)}finally{o()}})}function R(e){return u(this,null,function*(){const t=_.loading("加载中...");try{if(e.genType==="1"&&e.genPath){yield ie(e.tableId),_.success(`生成成功: ${e.genPath}`);return}const o=yield T(e.tableId),n=`代码生成_${e.tableName}_${te().valueOf()}.zip`;z(o,n)}catch(o){console.error(o)}finally{t()}})}function j(e){return u(this,null,function*(){yield S(e.tableId),yield r.query()})}function A(){const t=r.grid.getCheckboxRecords().map(o=>o.tableId);J.confirm({title:"提示",okType:"danger",content:`确认删除选中的${t.length}条记录吗？`,onOk:()=>u(null,null,function*(){yield S(t),yield r.query()})})}const[G,L]=V({connectedComponent:se});function W(){L.open()}return(e,t)=>{const o=X("a-button"),n=Z("access");return f(),c(s(pe),{"auto-content-height":!0},{default:l(()=>[C(s(N),{"table-title":"代码生成列表"},{"toolbar-tools":l(()=>[t[3]||(t[3]=ee("a",{class:"text-primary mr-2",href:"https://dapdap.top/other/template.html",target:"_blank"},"👉关于代码生成模板 ",-1)),C(s(re),null,{default:l(()=>[g((f(),c(o,{disabled:!s(x)(s(r)),danger:"",type:"primary",onClick:A},{default:l(()=>[b(h(e.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[n,["tool:gen:remove"],"code"]]),g((f(),c(o,{disabled:!s(x)(s(r)),onClick:O},{default:l(()=>[b(h(e.$t("pages.common.generate")),1)]),_:1},8,["disabled"])),[[n,["tool:gen:code"],"code"]]),g((f(),c(o,{type:"primary",onClick:W},{default:l(()=>[b(h(e.$t("pages.common.import")),1)]),_:1})),[[n,["tool:gen:import"],"code"]])]),_:1})]),action:l(({row:m})=>[g((f(),c(o,{size:"small",type:"link",onClick:v(y=>k(m),["stop"])},{default:l(()=>[b(h(e.$t("pages.common.preview")),1)]),_:2},1032,["onClick"])),[[n,["tool:gen:preview"],"code"]]),g((f(),c(o,{size:"small",type:"link",onClick:v(y=>w(m),["stop"])},{default:l(()=>[b(h(e.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[n,["tool:gen:edit"],"code"]]),C(s(Y),{"get-popup-container":s(B),title:`确认同步[${m.tableName}]?`,placement:"left",onConfirm:y=>q(m)},{default:l(()=>[g((f(),c(o,{size:"small",type:"link",onClick:t[0]||(t[0]=v(()=>{},["stop"]))},{default:l(()=>[b(h(e.$t("pages.common.sync")),1)]),_:1})),[[n,["tool:gen:edit"],"code"]])]),_:2},1032,["get-popup-container","title","onConfirm"]),g((f(),c(o,{size:"small",type:"link",onClick:v(y=>R(m),["stop"])},{default:l(()=>t[4]||(t[4]=[b(" 生成代码 ")])),_:2,__:[4]},1032,["onClick"])),[[n,["tool:gen:code"],"code"]]),C(s(Y),{"get-popup-container":s(B),title:`确认删除[${m.tableName}]?`,placement:"left",onConfirm:y=>j(m)},{default:l(()=>[g((f(),c(o,{danger:"",size:"small",type:"link",onClick:t[1]||(t[1]=v(()=>{},["stop"]))},{default:l(()=>[b(h(e.$t("pages.common.delete")),1)]),_:1})),[[n,["tool:gen:remove"],"code"]])]),_:2},1032,["get-popup-container","title","onConfirm"])]),_:1}),C(s(D)),C(s(G),{onReload:t[2]||(t[2]=m=>s(r).query())})]),_:1})}}});export{Ee as default};
