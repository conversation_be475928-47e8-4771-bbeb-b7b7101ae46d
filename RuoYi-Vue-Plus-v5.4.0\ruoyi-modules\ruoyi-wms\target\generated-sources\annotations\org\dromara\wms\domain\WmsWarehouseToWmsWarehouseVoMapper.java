package org.dromara.wms.domain;

import io.github.linpeilie.AutoMapperConfig__1039;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.bo.WmsWarehouseBoToWmsWarehouseMapper;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.vo.WmsWarehouseVoToWmsWarehouseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1039.class,
    uses = {WmsWarehouseBoToWmsWarehouseMapper.class,WmsWarehouseVoToWmsWarehouseMapper.class},
    imports = {}
)
public interface WmsWarehouseToWmsWarehouseVoMapper extends BaseMapper<WmsWarehouse, WmsWarehouseVo> {
}
