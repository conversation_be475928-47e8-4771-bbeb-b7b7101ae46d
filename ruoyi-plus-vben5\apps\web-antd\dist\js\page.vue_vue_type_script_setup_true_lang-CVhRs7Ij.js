var y=(t,d,o)=>new Promise((u,l)=>{var h=e=>{try{n(o.next(e))}catch(s){l(s)}},c=e=>{try{n(o.throw(e))}catch(s){l(s)}},n=e=>e.done?u(e.value):Promise.resolve(e.value).then(h,c);n((o=o.apply(t,d)).next())});import{x as H}from"./bootstrap-DCMzVRvD.js";import{d as k,p as g,E as C,B as R,v as T,c as a,o as r,f as i,j as $,n as m,b as p,G as v,r as f,t as b,H as B,I as O}from"../jse/index-index-C-MnMZEz.js";const S={class:"relative"},E={class:"flex-auto"},A={key:0,class:"mb-2 flex text-lg font-semibold"},N={key:0,class:"text-muted-foreground"},I={key:0},G=k({name:"Page",__name:"page",props:{title:{},description:{},contentClass:{},autoContentHeight:{type:Boolean,default:!1},headerClass:{},footerClass:{},heightOffset:{default:0}},setup(t){const d=g(0),o=g(0),u=g(!1),l=C("headerRef"),h=C("footerRef"),c=R(()=>t.autoContentHeight?{height:`calc(var(${H}) - ${d.value}px - ${typeof t.heightOffset=="number"?`${t.heightOffset}px`:t.heightOffset})`,overflowY:u.value?"auto":"unset"}:{});function n(){return y(this,null,function*(){var e,s;t.autoContentHeight&&(yield O(),d.value=((e=l.value)==null?void 0:e.offsetHeight)||0,o.value=((s=h.value)==null?void 0:s.offsetHeight)||0,setTimeout(()=>{u.value=!0},30))})}return T(()=>{n()}),(e,s)=>(r(),a("div",S,[e.description||e.$slots.description||e.title||e.$slots.title||e.$slots.extra?(r(),a("div",{key:0,ref_key:"headerRef",ref:l,class:m(p(v)("bg-card border-border relative flex items-end border-b px-6 py-4",e.headerClass))},[$("div",E,[f(e.$slots,"title",{},()=>[e.title?(r(),a("div",A,b(e.title),1)):i("",!0)]),f(e.$slots,"description",{},()=>[e.description?(r(),a("p",N,b(e.description),1)):i("",!0)])]),e.$slots.extra?(r(),a("div",I,[f(e.$slots,"extra")])):i("",!0)],2)):i("",!0),$("div",{class:m(p(v)("h-full p-4",e.contentClass)),style:B(c.value)},[f(e.$slots,"default")],6),e.$slots.footer?(r(),a("div",{key:1,ref_key:"footerRef",ref:h,class:m(p(v)("bg-card align-center absolute bottom-0 left-0 right-0 flex px-6 py-4",e.footerClass))},[f(e.$slots,"footer")],2)):i("",!0)]))}});export{G as _};
