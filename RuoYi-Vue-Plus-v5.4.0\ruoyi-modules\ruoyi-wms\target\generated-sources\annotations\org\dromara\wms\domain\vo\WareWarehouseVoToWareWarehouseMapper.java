package org.dromara.wms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__698;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.WareWarehouse;
import org.dromara.wms.domain.WareWarehouseToWareWarehouseVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__698.class,
    uses = {WareWarehouseToWareWarehouseVoMapper.class},
    imports = {}
)
public interface WareWarehouseVoToWareWarehouseMapper extends BaseMapper<WareWarehouseVo, WareWarehouse> {
}
