var S=(e,u,s)=>new Promise((a,f)=>{var y=n=>{try{d(s.next(n))}catch(m){f(m)}},o=n=>{try{d(s.throw(n))}catch(m){f(m)}},d=n=>n.done?a(n.value):Promise.resolve(n.value).then(y,o);d((s=s.apply(e,u)).next())});import{y as c,ar as r,$ as _,aj as k,z as E}from"./bootstrap-DCMzVRvD.js";import{c as A}from"./helper-Bc7QQ92Q.js";import{u as x,d as w}from"./popup-D6rC6QBG.js";import{g as p}from"./dict-BLkXAGS5.js";import{f as L,r as V}from"./render-BxXtQdeV.js";import{a as I}from"./get-popup-container-P4S1sr5h.js";import q from"./secret-input-DRN8XoW8.js";import{d as Y,p as B,B as F,P as $,h as O,o as R,w as N,a as T,b as v,O as M}from"../jse/index-index-C-MnMZEz.js";import{u as z}from"./use-drawer-6qcpK-D1.js";function se(e){return c.get("/system/client/list",{params:e})}function ie(e){return A("/system/client/export",e)}function G(e){return c.get(`/system/client/${e}`)}function K(e){return c.postWithMsg("/system/client",e)}function W(e){return c.putWithMsg("/system/client",e)}function re(e){const u={clientId:e.clientId,status:e.status};return c.putWithMsg("/system/client/changeStatus",u)}function ce(e){return c.deleteWithMsg(`/system/client/${e}`)}const ue=()=>[{component:"Input",fieldName:"clientKey",label:"客户端key"},{component:"Input",fieldName:"clientSecret",label:"客户端密钥"},{component:"Select",componentProps:{options:p(r.SYS_NORMAL_DISABLE)},fieldName:"status",label:"状态"}],de=[{type:"checkbox",width:60},{title:"客户端ID",field:"clientId",showOverflow:!0},{title:"客户端key",field:"clientKey"},{title:"客户端密钥",field:"clientSecret"},{title:"授权类型",field:"grantTypeList",slots:{default:({row:e})=>e.grantTypeList?L(e.grantTypeList,p(r.SYS_GRANT_TYPE),!0,4):"无"}},{title:"设备类型",field:"deviceType",slots:{default:({row:e})=>V(e.deviceType,r.SYS_DEVICE_TYPE)}},{title:"token活跃时间",field:"activeTimeout",formatter({row:e}){return`${e.activeTimeout}秒`}},{title:"token超时时间",field:"timeout",formatter({row:e}){return`${e.timeout}秒`}},{title:"状态",field:"status",slots:{default:"status"}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],j=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"id",label:"id"},{component:"Input",componentProps:{disabled:!0},dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"clientId",label:"客户端ID"},{component:"Input",fieldName:"clientKey",label:"客户端key",rules:"required"},{component:"Input",fieldName:"clientSecret",label:"客户端密钥",rules:"required"},{component:"Select",componentProps:{getPopupContainer:I,mode:"multiple",optionFilterProp:"label",options:p(r.SYS_GRANT_TYPE)},fieldName:"grantTypeList",label:"授权类型",rules:"selectRequired"},{component:"Select",componentProps:{allowClear:!1,getPopupContainer:I,options:p(r.SYS_DEVICE_TYPE)},fieldName:"deviceType",label:"设备类型",rules:"selectRequired"},{component:"InputNumber",componentProps:{addonAfter:"秒",placeholder:"请输入"},defaultValue:1800,fieldName:"activeTimeout",formItemClass:"col-span-2 lg:col-span-1",help:"指定时间无操作则过期(单位：秒), 默认30分钟(1800秒)",label:"Token活跃超时时间",rules:"required"},{component:"InputNumber",componentProps:{addonAfter:"秒"},defaultValue:604800,fieldName:"timeout",formItemClass:"col-span-2 lg:col-span-1 ",help:"指定时间必定过期(单位：秒)，默认七天(604800秒)",label:"Token固定超时时间",rules:"required"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:p(r.SYS_NORMAL_DISABLE),optionType:"button"},defaultValue:"0",fieldName:"status",label:"状态"}],U=Y({__name:"client-drawer",emits:["reload"],setup(e,{emit:u}){const s=u,a=B(!1),f=F(()=>a.value?_("pages.common.edit"):_("pages.common.add")),[y,o]=k({commonConfig:{formItemClass:"col-span-2",componentProps:{class:"w-full"}},layout:"vertical",schema:j(),showDefaultActions:!1,wrapperClass:"grid-cols-2 gap-x-4"});function d(t){o.updateSchema([{dependencies:{show:()=>t,triggerFields:[""]},fieldName:"clientId"},{componentProps:{disabled:t},fieldName:"clientKey"},{componentProps:{disabled:t},fieldName:"clientSecret"}])}const{onBeforeClose:n,markInitialized:m,resetInitialized:h}=x({initializedGetter:w(o),currentGetter:w(o)}),b=t=>[{componentProps:{disabled:t},fieldName:"status"}],[C,i]=z({onBeforeClose:n,onClosed:D,onConfirm:P,onOpenChange(t){return S(this,null,function*(){if(!t)return null;i.drawerLoading(!0);const{id:l}=i.getData();if(a.value=!!l,d(a.value),a.value&&l){const g=yield G(l);o.updateSchema(b(g.id===1)),yield o.setValues(g)}else o.updateSchema(b(!1));yield m(),i.drawerLoading(!1)})}});function P(){return S(this,null,function*(){try{i.lock(!0);const{valid:t}=yield o.validate();if(!t)return;const l=$(yield o.getValues());yield a.value?W(l):K(l),h(),s("reload"),i.close()}catch(t){console.error(t)}finally{i.lock(!1)}})}function D(){return S(this,null,function*(){yield o.resetForm(),h()})}return(t,l)=>(R(),O(v(C),{title:f.value,class:"w-[600px]"},{default:N(()=>[T(v(y),null,{clientSecret:N(g=>[T(q,M(g,{disabled:a.value}),null,16,["disabled"])]),_:1})]),_:1},8,["title"]))}}),H=E(U,[["__scopeId","data-v-c3f94f67"]]),me=Object.freeze(Object.defineProperty({__proto__:null,default:H},Symbol.toStringTag,{value:"Module"}));export{H as a,re as b,de as c,se as d,ce as e,ie as f,me as g,ue as q};
