var R=Object.defineProperty;var D=Object.getOwnPropertySymbols;var A=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var S=(o,e,t)=>e in o?R(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t,T=(o,e)=>{for(var t in e||(e={}))A.call(e,t)&&S(o,t,e[t]);if(D)for(var t of D(e))U.call(e,t)&&S(o,t,e[t]);return o};var d=(o,e,t)=>new Promise((C,w)=>{var a=l=>{try{m(t.next(l))}catch(b){w(b)}},y=l=>{try{m(t.throw(l))}catch(b){w(b)}},m=l=>l.done?C(l.value):Promise.resolve(l.value).then(a,y);m((t=t.apply(o,e)).next())});import{y as x,ar as Y,as as I,an as B}from"./bootstrap-DCMzVRvD.js";import{v as L}from"./vxe-table-DzEj5Fop.js";import{c as W}from"./helper-Bc7QQ92Q.js";import{c as z}from"./download-UJak946_.js";import{c as j}from"./modal-CapY0ZPa.js";import{g as F}from"./dict-BLkXAGS5.js";import{b as G,a as K,r as H}from"./render-BxXtQdeV.js";import{_ as J}from"./login-info-modal.vue_vue_type_script_setup_true_lang-nEoaNEMd.js";import $ from"./index-BeyziwLP.js";import{g as Q}from"./get-popup-container-P4S1sr5h.js";import{_ as X}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as Z,p as oo,l as N,S as eo,h as p,o as f,w as r,a as u,b as s,k as g,t as h,T as _}from"../jse/index-index-C-MnMZEz.js";import{u as to}from"./use-vxe-grid-BC7vZzEr.js";import{u as io}from"./use-modal-CeMSCP2m.js";import{P as no}from"./index-DNdMANjv.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-BxBCzu2M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-kC0HFDdy.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./index-D59rZjD-.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";function ro(o){return x.get("/monitor/logininfor/list",{params:o})}function ao(o){return W("/monitor/logininfor/export",o)}function P(o){return x.deleteWithMsg(`/monitor/logininfor/${o}`)}function lo(o){return x.get(`/monitor/logininfor/unlock/${o}`,{successMessageMode:"message"})}function so(){return x.deleteWithMsg("/monitor/logininfor/clean")}const mo=()=>[{component:"Input",fieldName:"ipaddr",label:"IP地址"},{component:"Input",fieldName:"userName",label:"用户账号"},{component:"Select",componentProps:{options:F(Y.SYS_COMMON_STATUS)},fieldName:"status",label:"登录状态"},{component:"RangePicker",fieldName:"dateTime",label:"登录日期"}],co=[{type:"checkbox",width:60},{title:"用户账号",field:"userName"},{title:"登录平台",field:"clientKey"},{title:"IP地址",field:"ipaddr"},{title:"IP地点",field:"loginLocation",width:200},{title:"浏览器",field:"browser",slots:{default:({row:o})=>G(o.browser,!0)}},{title:"系统",field:"os",slots:{default:({row:o})=>{let e=o.os;if(e){const t=e.split(" or ");t.length===2&&(e=t[0])}return K(e,!0)}}},{title:"登录结果",field:"status",slots:{default:({row:o})=>H(o.status,Y.SYS_COMMON_STATUS)}},{title:"信息",field:"msg"},{title:"日期",field:"loginTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",width:150}],Qo=Z({__name:"index",setup(o){const e={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:mo(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",fieldMappingTime:[["dateTime",["params[beginTime]","params[endTime]"],["YYYY-MM-DD 00:00:00","YYYY-MM-DD 23:59:59"]]]},t={checkboxConfig:{highlight:!0,reserve:!0,trigger:"row"},columns:co,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(c,...M)=>d(null,[c,...M],function*({page:i},n={}){return yield ro(T({pageNum:i.currentPage,pageSize:i.pageSize},n))})}},rowConfig:{keyField:"infoId"},id:"monitor-logininfo-index"},C=oo(!1),[w,a]=to({formOptions:e,gridOptions:t,gridEvents:{checkboxChange:i=>{const n=i.$grid.getCheckboxRecords();C.value=n.length===1&&n[0].status==="1"}}}),[y,m]=io({connectedComponent:J});function l(i){m.setData(i),m.open()}function b(){j({onValidated:()=>d(null,null,function*(){yield so(),yield a.reload()})})}function O(i){return d(this,null,function*(){yield P([i.infoId]),yield a.query()})}function V(){const n=a.grid.getCheckboxRecords().map(c=>c.infoId);B.confirm({title:"提示",okType:"danger",content:`确认删除选中的${n.length}条记录吗？`,onOk:()=>d(null,null,function*(){yield P(n),yield a.query()})})}function q(){return d(this,null,function*(){const i=a.grid.getCheckboxRecords();if(i.length!==1)return;const{userName:n}=i[0];yield lo(n),yield a.query(),C.value=!1,a.grid.clearCheckboxRow()})}function E(){z(ao,"登录日志",a.formApi.form.values,{fieldMappingTime:e.fieldMappingTime})}return(i,n)=>{const c=N("a-button"),M=N("ghost-button"),k=eo("access");return f(),p(s(X),{"auto-content-height":""},{default:r(()=>[u(s(w),{"table-title":"登录日志列表"},{"toolbar-tools":r(()=>[u(s($),null,{default:r(()=>[_((f(),p(c,{onClick:b},{default:r(()=>[g(h(i.$t("pages.common.clear")),1)]),_:1})),[[k,["monitor:logininfor:remove"],"code"]]),_((f(),p(c,{onClick:E},{default:r(()=>[g(h(i.$t("pages.common.export")),1)]),_:1})),[[k,["monitor:logininfor:export"],"code"]]),_((f(),p(c,{disabled:!s(L)(s(a)),danger:"",type:"primary",onClick:V},{default:r(()=>[g(h(i.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[k,["monitor:logininfor:remove"],"code"]]),_((f(),p(c,{disabled:!C.value,type:"primary",onClick:q},{default:r(()=>[g(h(i.$t("pages.common.unlock")),1)]),_:1},8,["disabled"])),[[k,["monitor:logininfor:unlock"],"code"]])]),_:1})]),action:r(({row:v})=>[u(s($),null,{default:r(()=>[u(M,{onClick:I(po=>l(v),["stop"])},{default:r(()=>[g(h(i.$t("pages.common.info")),1)]),_:2},1032,["onClick"]),u(s(no),{"get-popup-container":s(Q),placement:"left",title:"确认删除?",onConfirm:()=>O(v)},{default:r(()=>[_((f(),p(M,{danger:"",onClick:n[0]||(n[0]=I(()=>{},["stop"]))},{default:r(()=>[g(h(i.$t("pages.common.delete")),1)]),_:1})),[[k,["monitor:logininfor:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),u(s(y))]),_:1})}}});export{Qo as default};
