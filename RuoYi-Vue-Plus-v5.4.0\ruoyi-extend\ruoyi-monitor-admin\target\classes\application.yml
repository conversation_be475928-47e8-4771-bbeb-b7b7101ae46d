server:
  port: 9090
spring:
  application:
    name: ruoyi-monitor-admin
  profiles:
    active: dev

logging:
  config: classpath:logback-plus.xml

--- # 监控中心服务端配置
spring:
  security:
    user:
      name: ruoyi
      password: 123456
  boot:
    admin:
      ui:
        title: RuoYi-Vue-Plus服务监控中心
      context-path: /admin

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/ruoyi-monitor-admin.log

--- # 监控配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  # 设置 Spring Boot Admin Server 地址
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
    metadata:
      username: ${spring.boot.admin.client.username}
      userpassword: ${spring.boot.admin.client.password}
  username: ruoyi
  password: 123456
