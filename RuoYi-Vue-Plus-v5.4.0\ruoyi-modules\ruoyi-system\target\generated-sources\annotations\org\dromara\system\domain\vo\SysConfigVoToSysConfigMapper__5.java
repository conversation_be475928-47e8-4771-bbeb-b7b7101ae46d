package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysConfigToSysConfigVoMapper__5.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__5 extends BaseMapper<SysConfigVo, SysConfig> {
}
