var y=(n,e,t)=>new Promise((r,o)=>{var C=d=>{try{b(t.next(d))}catch(g){o(g)}},m=d=>{try{b(t.throw(d))}catch(g){o(g)}},b=d=>d.done?r(d.value):Promise.resolve(d.value).then(C,m);b((t=t.apply(n,e)).next())});import{aC as N,T as O,P as H,as as $,cw as G,an as F}from"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import{d as U,a as Y,r as J,b as Q}from"./dict-type-GiaNpHd1.js";import{c as W}from"./download-UJak946_.js";import{e as X}from"./mitt-Dl_8zfXM.js";import{_ as Z}from"./dict-type-modal.vue_vue_type_script_setup_true_lang-DKa6r6XD.js";import{S as A}from"./SyncOutlined-GoH9kFJY.js";import{a as i,d as K,C as ee,p as _,q as te,l as ne,S as oe,c as ae,o as le,j as p,w as f,b as a,T as h,M as D,n as E,G as P,t as T}from"../jse/index-index-C-MnMZEz.js";import ie from"./index-BeyziwLP.js";import"./index-BxBCzu2M.js";import{I as re}from"./Search-ClCped_G.js";import{u as se}from"./use-vxe-grid-BC7vZzEr.js";import{u as ce}from"./use-modal-CeMSCP2m.js";import{A as de}from"./index-kC0HFDdy.js";import{P as ue}from"./index-DNdMANjv.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./mitt-D4bcW7KS.js";import"./popup-D6rC6QBG.js";import"./index-CFj2VWFk.js";import"./statusUtils-d85DZFMd.js";import"./index-CHpIOV4R.js";import"./BaseInput-B4f3ADM3.js";import"./SearchOutlined-BOD_ZIye.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";var pe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};function M(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},r=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(o){return Object.getOwnPropertyDescriptor(t,o).enumerable}))),r.forEach(function(o){fe(n,o,t[o])})}return n}function fe(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var k=function(e,t){var r=M({},e,t.attrs);return i(N,M({},r,{icon:pe}),null)};k.displayName="EditOutlined";k.inheritAttrs=!1;var me={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};function I(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},r=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(o){return Object.getOwnPropertyDescriptor(t,o).enumerable}))),r.forEach(function(o){ve(n,o,t[o])})}return n}function ve(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var S=function(e,t){var r=I({},e,t.attrs);return i(N,I({},r,{icon:me}),null)};S.displayName="ExportOutlined";S.inheritAttrs=!1;const ye={class:"flex flex-1 flex-col overflow-y-hidden p-4"},he={class:"flex flex-col items-baseline overflow-hidden"},be={class:"font-medium"},ge={class:"max-w-full overflow-hidden text-ellipsis whitespace-nowrap"},we={class:"flex items-center gap-3 text-[17px]"},xe={class:"border-t px-4 py-3"},Ze=K({__name:"index-refactor",setup(n){const e=ee([]),t={columns:[{title:"name",field:"render",slots:{default:"render"}}],height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:()=>y(null,null,function*(){const l=yield U();return x.value=l.total,e.value=l.rows,l})}},rowConfig:{keyField:"dictId",isCurrent:!0},cellConfig:{height:60},showHeader:!1,toolbarConfig:{enabled:!1},scrollY:{enabled:!1,gt:0},rowClassName:"cursor-pointer",id:"system-dict-data-index"},[r,o]=se({gridOptions:t,gridEvents:{cellClick:({row:l})=>{z(l)}}}),[C,m]=ce({connectedComponent:Z});function b(){m.setData({}),m.open()}function d(l){return y(this,null,function*(){m.setData({id:l.dictId}),m.open()})}function g(l){return y(this,null,function*(){yield Y([l.dictId]),yield o.query()})}function V(){return y(this,null,function*(){j.value="",v.value="",yield o.query()})}function B(){W(Q,"字典类型数据")}function R(){F.confirm({title:"提示",content:"确认刷新字典类型缓存吗？",okButtonProps:{danger:!0},onOk:()=>y(null,null,function*(){yield J(),yield o.query()})})}const q=_(""),j=_(null);function z(l){q.value!==l.dictType&&(j.value=l.dictId,X.emit("rowClick",l.dictType))}const v=_(""),x=_(0);return te(v,l=>{if(o)if(l){const c=e.value.filter(s=>s.dictName.includes(v.value)),w=e.value.filter(s=>s.dictType.includes(v.value)),u=[...new Set([...c,...w])];x.value=u.length,o.grid.loadData(u)}else x.value=e.value.length,o.grid.loadData(e.value)}),(l,c)=>{const w=ne("a-button"),u=oe("access");return le(),ae("div",{class:E(a(P)("bg-background flex max-h-[100vh] w-[360px] flex-col overflow-y-hidden","rounded-lg","dict-type-card"))},[p("div",{class:E(a(P)("flex items-center justify-between","border-b px-4 py-2"))},[c[3]||(c[3]=p("span",{class:"font-semibold"},"字典项列表",-1)),i(a(ie),null,{default:f(()=>[i(a(O),{title:"刷新缓存"},{default:f(()=>[h(i(w,{icon:D(a(A)),onClick:R},null,8,["icon"]),[[u,["system:dict:edit"],"code"]])]),_:1}),i(a(O),{title:l.$t("pages.common.export")},{default:f(()=>[h(i(w,{icon:D(a(S)),onClick:B},null,8,["icon"]),[[u,["system:dict:export"],"code"]])]),_:1},8,["title"]),i(a(O),{title:l.$t("pages.common.add")},{default:f(()=>[h(i(w,{icon:D(a(H)),onClick:b},null,8,["icon"]),[[u,["system:dict:add"],"code"]])]),_:1},8,["title"])]),_:1})],2),p("div",ye,[i(a(de),{class:"mb-4","show-icon":"",message:"如果你的数据量大 自行开启虚拟滚动"}),i(a(re),{placeholder:"搜索字典项名称/类型",value:v.value,"onUpdate:value":c[0]||(c[0]=s=>v.value=s),"allow-clear":""},{addonAfter:f(()=>[i(a(O),{title:"重置/刷新"},{default:f(()=>[h(i(a(A),{onClick:V},null,512),[[u,["system:dict:edit"],"code"]])]),_:1})]),_:1},8,["value"]),i(a(r),{class:"flex-1 overflow-hidden"},{render:f(({row:s})=>[p("div",{class:E(a(P)("flex items-center justify-between px-2 py-2"))},[p("div",he,[p("span",be,T(s.dictName),1),p("div",ge,T(s.dictType),1)]),p("div",we,[h(i(a(k),{class:"text-primary",onClick:$(L=>d(s),["stop"])},null,8,["onClick"]),[[u,["system:dict:edit"],"code"]]),i(a(ue),{placement:"left",title:`确认删除 [${s.dictName}]?`,onConfirm:L=>g(s)},{default:f(()=>[h(i(a(G),{class:"text-destructive",onClick:c[1]||(c[1]=$(()=>{},["stop"]))},null,512),[[u,["system:dict:remove"],"code"]])]),_:2},1032,["title","onConfirm"])])],2)]),_:1})]),p("div",xe,"共 "+T(x.value)+" 条数据",1),i(a(C),{onReload:c[2]||(c[2]=s=>a(o).query())})],2)}}});export{Ze as default};
