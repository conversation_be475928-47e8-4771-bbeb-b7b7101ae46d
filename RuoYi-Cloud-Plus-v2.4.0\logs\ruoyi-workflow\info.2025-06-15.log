2025-06-15 21:14:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:14:29 [main] INFO  o.d.w.RuoYiWorkflowApplication - Starting RuoYiWorkflowApplication using Java 17.0.14 with PID 51684 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-workflow\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:14:29 [main] INFO  o.d.w.RuoYiWorkflowApplication - The following 1 profile is active: "dev"
2025-06-15 21:14:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-15 21:14:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:14:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:14:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:14:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:14:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 21:14:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-06-15 21:14:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 21:14:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 21:14:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 21:14:37 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:14:53 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:14:53 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:14:53 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:14:53 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:14:53 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@21c747a
2025-06-15 21:14:54 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-06-15 21:14:56 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 21:14:56 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 21:14:56 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 21:14:56 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 21:14:58 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-workflow *************:9205 register finished
2025-06-15 21:15:02 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:15:03 [main] INFO  o.d.w.RuoYiWorkflowApplication - Started RuoYiWorkflowApplication in 37.628 seconds (process running for 38.373)
2025-06-15 21:15:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:15:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:15:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:07 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:07 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 22:33:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:47:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:47:11 [main] INFO  o.d.w.RuoYiWorkflowApplication - Starting RuoYiWorkflowApplication using Java 17.0.14 with PID 45468 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-workflow\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:47:11 [main] INFO  o.d.w.RuoYiWorkflowApplication - The following 1 profile is active: "dev"
2025-06-15 22:47:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP] success
2025-06-15 22:47:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:47:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:47:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:47:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:47:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 22:47:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@40c31127
2025-06-15 22:47:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 22:47:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 22:47:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 22:47:19 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:47:35 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:47:35 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:47:35 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:47:35 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:47:35 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@86d6bf7
2025-06-15 22:47:36 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-06-15 22:47:38 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 22:47:38 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 22:47:38 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 22:47:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 22:47:40 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-workflow *************:9205 register finished
2025-06-15 22:47:45 [main] INFO  o.d.w.RuoYiWorkflowApplication - Started RuoYiWorkflowApplication in 37.261 seconds (process running for 37.988)
2025-06-15 22:47:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:47:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 22:47:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-workflow.yml, group=DEFAULT_GROUP
2025-06-15 22:47:46 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
