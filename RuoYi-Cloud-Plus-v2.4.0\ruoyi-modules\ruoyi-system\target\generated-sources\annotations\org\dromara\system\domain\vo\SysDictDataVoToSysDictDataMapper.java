package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1170;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1170.class,
    uses = {SysDictDataToSysDictDataVoMapper.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper extends BaseMapper<SysDictDataVo, SysDictData> {
}
