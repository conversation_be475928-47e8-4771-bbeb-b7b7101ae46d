var d=(e,u,i)=>new Promise((r,m)=>{var p=a=>{try{s(i.next(a))}catch(t){m(t)}},l=a=>{try{s(i.throw(a))}catch(t){m(t)}},s=a=>a.done?r(a.value):Promise.resolve(a.value).then(p,l);s((i=i.apply(e,u)).next())});import{y as c,$ as I,aj as w}from"./bootstrap-DCMzVRvD.js";import{l as b}from"./tree-DFBawhPd.js";import{d as v,p as C,B as _,P as q,h as y,o as L,w as x,a as B,b as h}from"../jse/index-index-C-MnMZEz.js";import{u as D}from"./use-modal-CeMSCP2m.js";function A(e){return c.get("/demo/tree/list",{params:e})}function M(e){return c.get(`/demo/tree/${e}`)}function S(e){return c.postWithMsg("/demo/tree",e)}function V(e){return c.putWithMsg("/demo/tree",e)}function U(e){return c.deleteWithMsg(`/demo/tree/${e}`)}const j=()=>[{component:"Input",fieldName:"parentId",label:"父id"},{component:"Input",fieldName:"deptId",label:"部门id"},{component:"Input",fieldName:"userId",label:"用户id"},{component:"Input",fieldName:"treeName",label:"值"},{component:"Input",fieldName:"version",label:"版本"}],z=[{title:"主键",field:"id",treeNode:!0},{title:"父id",field:"parentId"},{title:"部门id",field:"deptId"},{title:"用户id",field:"userId"},{title:"值",field:"treeName"},{title:"版本",field:"version"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],F=()=>[{label:"主键",fieldName:"id",component:"Input",dependencies:{show:()=>!1,triggerFields:[""]}},{label:"父id",fieldName:"parentId",component:"TreeSelect",rules:"required"},{label:"部门id",fieldName:"deptId",component:"Input",rules:"required"},{label:"用户id",fieldName:"userId",component:"Input",rules:"required"},{label:"值",fieldName:"treeName",component:"Input",rules:"required"},{label:"版本",fieldName:"version",component:"Input",rules:"required"}],E=v({__name:"tree-modal",emits:["reload"],setup(e,{emit:u}){const i=u,r=C(!1),m=_(()=>r.value?I("pages.common.edit"):I("pages.common.add")),[p,l]=w({commonConfig:{formItemClass:"col-span-2",labelWidth:80,componentProps:{class:"w-full"}},schema:F(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function s(){return d(this,null,function*(){const n=yield A(),o=b(n,{id:"id",pid:"parentId"});l.updateSchema([{fieldName:"parentId",componentProps:{treeData:o,treeLine:{showLeafIcon:!1},fieldNames:{label:"treeName",value:"id"},treeDefaultExpandAll:!0}}])})}const[a,t]=D({fullscreenButton:!1,onCancel:f,onConfirm:g,onOpenChange:n=>d(null,null,function*(){if(!n)return null;t.modalLoading(!0);const{id:o}=t.getData();if(r.value=!!o,r.value&&o){const N=yield M(o);yield l.setValues(N)}yield s(),t.modalLoading(!1)})});function g(){return d(this,null,function*(){try{t.modalLoading(!0);const{valid:n}=yield l.validate();if(!n)return;const o=q(yield l.getValues());yield r.value?V(o):S(o),i("reload"),yield f()}catch(n){console.error(n)}finally{t.modalLoading(!1)}})}function f(){return d(this,null,function*(){t.close(),yield l.resetForm()})}return(n,o)=>(L(),y(h(a),{"close-on-click-modal":!1,title:m.value,class:"w-[550px]"},{default:x(()=>[B(h(p))]),_:1},8,["title"]))}});export{E as _,U as a,z as c,j as q,A as t};
