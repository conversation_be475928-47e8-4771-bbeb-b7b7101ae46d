var w=Object.defineProperty;var g=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable;var b=(o,a,e)=>a in o?w(o,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[a]=e,h=(o,a)=>{for(var e in a||(a={}))N.call(a,e)&&b(o,e,a[e]);if(g)for(var e of g(a))x.call(a,e)&&b(o,e,a[e]);return o};var d=(o,a,e)=>new Promise((p,c)=>{var f=t=>{try{n(e.next(t))}catch(s){c(s)}},i=t=>{try{n(e.throw(t))}catch(s){c(s)}},n=t=>t.done?p(t.value):Promise.resolve(t.value).then(f,i);n((e=e.apply(o,a)).next())});import"./vxe-table-DzEj5Fop.js";import{g as y,i as _,r as k}from"./index-BntC6MFc.js";import{d as S,h as V,o as B,w as T,a as A,b as C}from"../jse/index-index-C-MnMZEz.js";import{u as O}from"./use-vxe-grid-BC7vZzEr.js";import{u as z}from"./use-modal-CeMSCP2m.js";const P=S({__name:"table-import-modal",emits:["reload"],setup(o,{emit:a}){const e=a,p={schema:[{label:"数据源",fieldName:"dataName",component:"Select",defaultValue:"master"},{label:"表名称",fieldName:"tableName",component:"Input"},{label:"表描述",fieldName:"tableComment",component:"Input"}],commonConfig:{labelWidth:60},showCollapseButton:!1,wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"},c={checkboxConfig:{highlight:!0,reserve:!0,trigger:"row"},columns:[{type:"checkbox",width:60},{title:"表名称",field:"tableName",align:"left"},{title:"表描述",field:"tableComment",align:"left"},{title:"创建时间",field:"createTime"},{title:"更新时间",field:"updateTime"}],keepSource:!0,size:"small",minHeight:400,pagerConfig:{},proxyConfig:{ajax:{query:(u,...m)=>d(null,[u,...m],function*({page:l},r={}){return yield k(h({pageNum:l.currentPage,pageSize:l.pageSize},r))})}},rowConfig:{keyField:"tableId"},toolbarConfig:{enabled:!1}},[f,i]=O({formOptions:p,gridOptions:c}),[n,t]=z({onOpenChange:l=>d(null,null,function*(){if(!l)return i.grid.clearCheckboxRow(),null;const u=(yield y()).map(m=>({label:m,value:m}));i.formApi.updateSchema([{fieldName:"dataName",componentProps:{options:u}}])}),onConfirm:s});function s(){return d(this,null,function*(){try{const r=i.grid.getCheckboxRecords().map(m=>m.tableName);if(r.length===0){t.close();return}t.modalLoading(!0);const{dataName:u}=yield i.formApi.getValues();yield _(r.join(","),u),e("reload"),t.close()}catch(l){console.warn(l)}finally{t.modalLoading(!1)}})}return(l,r)=>(B(),V(C(n),{class:"w-[800px]",title:"导入表"},{default:T(()=>[A(C(f))]),_:1}))}});export{P as _};
