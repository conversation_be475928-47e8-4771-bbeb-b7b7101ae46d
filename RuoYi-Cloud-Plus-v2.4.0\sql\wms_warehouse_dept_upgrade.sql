-- 如果表已经存在，执行以下ALTER语句添加租户相关字段
-- 注意：执行前请备份数据！

-- 添加租户字段
ALTER TABLE `wms_warehouse_dept` 
ADD COLUMN `tenant_id` varchar(20) NOT NULL DEFAULT '000000' COMMENT '租户编号' AFTER `dept_id`;

-- 添加审计字段
ALTER TABLE `wms_warehouse_dept` 
ADD COLUMN `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门' AFTER `tenant_id`,
ADD COLUMN `create_by` bigint(20) DEFAULT NULL COMMENT '创建者' AFTER `create_dept`,
ADD COLUMN `create_time` datetime DEFAULT NULL COMMENT '创建时间' AFTER `create_by`,
ADD COLUMN `update_by` bigint(20) DEFAULT NULL COMMENT '更新者' AFTER `create_time`,
ADD COLUMN `update_time` datetime DEFAULT NULL COMMENT '更新时间' AFTER `update_by`;

-- 删除原有唯一索引
ALTER TABLE `wms_warehouse_dept` DROP INDEX `uk_warehouse_dept`;

-- 添加新的唯一索引（包含租户）
ALTER TABLE `wms_warehouse_dept` 
ADD UNIQUE KEY `uk_warehouse_dept_tenant` (`warehouse_id`, `dept_id`, `tenant_id`) COMMENT '仓库部门租户唯一索引';

-- 添加租户索引
ALTER TABLE `wms_warehouse_dept` 
ADD KEY `idx_tenant_id` (`tenant_id`) COMMENT '租户ID索引';
