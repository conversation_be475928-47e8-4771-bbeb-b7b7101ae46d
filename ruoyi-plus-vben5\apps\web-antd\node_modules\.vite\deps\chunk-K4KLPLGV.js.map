{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/utils.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nimport { getConfig } from '@vxe-ui/core';\nimport DomZIndex from 'dom-zindex';\nexport function isEnableConf(conf) {\n    return conf && conf.enabled !== false;\n}\nexport function nextZIndex() {\n    return DomZIndex.getNext();\n}\nexport function getLastZIndex() {\n    return DomZIndex.getCurrent();\n}\nexport function getGlobalDefaultConfig(value, globalValue) {\n    if (XEUtils.eqNull(value)) {\n        return globalValue;\n    }\n    return value;\n}\nexport function getFuncText(content, args) {\n    if (content) {\n        const translate = getConfig().translate;\n        return XEUtils.toValueString(translate ? translate('' + content, args) : content);\n    }\n    return '';\n}\n/**\n * 判断值为：'' | null | undefined 时都属于空值\n */\nexport function eqEmptyValue(cellValue) {\n    return cellValue === null || cellValue === undefined || cellValue === '';\n}\nexport function handleBooleanDefaultValue(value) {\n    return XEUtils.isBoolean(value) ? value : null;\n}\n"], "mappings": ";;;;;;;;;;AAAA,sBAAoB;AAGb,SAAS,aAAa,MAAM;AAC/B,SAAO,QAAQ,KAAK,YAAY;AACpC;AACO,SAAS,aAAa;AACzB,SAAO,kBAAU,QAAQ;AAC7B;AACO,SAAS,gBAAgB;AAC5B,SAAO,kBAAU,WAAW;AAChC;AACO,SAAS,uBAAuB,OAAO,aAAa;AACvD,MAAI,gBAAAA,QAAQ,OAAO,KAAK,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACO,SAAS,YAAY,SAAS,MAAM;AACvC,MAAI,SAAS;AACT,UAAM,YAAY,UAAU,EAAE;AAC9B,WAAO,gBAAAA,QAAQ,cAAc,YAAY,UAAU,KAAK,SAAS,IAAI,IAAI,OAAO;AAAA,EACpF;AACA,SAAO;AACX;AAIO,SAAS,aAAa,WAAW;AACpC,SAAO,cAAc,QAAQ,cAAc,UAAa,cAAc;AAC1E;AACO,SAAS,0BAA0B,OAAO;AAC7C,SAAO,gBAAAA,QAAQ,UAAU,KAAK,IAAI,QAAQ;AAC9C;", "names": ["XEUtils"]}