var F=Object.defineProperty;var T=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var D=(n,o,e)=>o in n?F(n,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[o]=e,A=(n,o)=>{for(var e in o||(o={}))I.call(o,e)&&D(n,e,o[e]);if(T)for(var e of T(o))M.call(o,e)&&D(n,e,o[e]);return n};var C=(n,o,e)=>new Promise((h,l)=>{var k=s=>{try{c(e.next(s))}catch(m){l(m)}},d=s=>{try{c(e.throw(s))}catch(m){l(m)}},c=s=>s.done?h(s.value):Promise.resolve(s.value).then(k,d);c((e=e.apply(n,o)).next())});import{as as q}from"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import{c as N,q as P,_ as j,t as O,a as R}from"./tree-modal.vue_vue_type_script_setup_true_lang-n85UUXb9.js";import S from"./index-BeyziwLP.js";import{_ as G}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as L,l as V,S as W,h as y,o as b,w as a,a as p,b as i,T as v,k as f,t as g,j as z,I as H}from"../jse/index-index-C-MnMZEz.js";import{u as J}from"./use-vxe-grid-BC7vZzEr.js";import{u as K}from"./use-modal-CeMSCP2m.js";import{P as Q}from"./index-DNdMANjv.js";import{a as U}from"./get-popup-container-P4S1sr5h.js";import{l as X}from"./tree-DFBawhPd.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const _e=L({__name:"index",setup(n){const o={commonConfig:{labelWidth:80},schema:P(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={columns:N,height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{ajax:{query:(x,...$)=>C(null,[x,...$],function*(t,r={}){const u=yield O(A({},r));return{rows:X(u,{id:"id",pid:"parentId",children:"children"})}}),querySuccess:()=>{H(()=>{w()})}}},rowConfig:{keyField:"id"},treeConfig:{parentField:"parentId",rowField:"id",transform:!1}},[h,l]=J({formOptions:o,gridOptions:e}),[k,d]=K({connectedComponent:j});function c(){d.setData({update:!1}),d.open()}function s(t){return C(this,null,function*(){d.setData({id:t.id,update:!0}),d.open()})}function m(t){return C(this,null,function*(){yield R(t.id),yield l.query()})}function w(){var t;(t=l.grid)==null||t.setAllTreeExpand(!0)}function B(){var t;(t=l.grid)==null||t.setAllTreeExpand(!1)}return(t,r)=>{const x=V("a-button"),$=V("ghost-button"),u=W("access");return b(),y(i(G),{"auto-content-height":!0},{default:a(()=>[p(i(h),null,{"toolbar-actions":a(()=>r[2]||(r[2]=[z("span",{class:"pl-[7px] text-[16px]"},"测试树列表",-1)])),"toolbar-tools":a(()=>[p(i(S),null,{default:a(()=>[p(x,{onClick:B},{default:a(()=>[f(g(t.$t("pages.common.collapse")),1)]),_:1}),p(x,{onClick:w},{default:a(()=>[f(g(t.$t("pages.common.expand")),1)]),_:1}),v((b(),y(x,{type:"primary",onClick:c},{default:a(()=>[f(g(t.$t("pages.common.add")),1)]),_:1})),[[u,["system:tree:add"],"code"]])]),_:1})]),action:a(({row:_})=>[p(i(S),null,{default:a(()=>[v((b(),y($,{onClick:q(E=>s(_),["stop"])},{default:a(()=>[f(g(t.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[u,["system:tree:edit"],"code"]]),p(i(Q),{"get-popup-container":i(U),placement:"left",title:"确认删除？",onConfirm:E=>m(_)},{default:a(()=>[v((b(),y($,{danger:"",onClick:r[0]||(r[0]=q(()=>{},["stop"]))},{default:a(()=>[f(g(t.$t("pages.common.delete")),1)]),_:1})),[[u,["system:tree:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),p(i(k),{onReload:r[1]||(r[1]=_=>i(l).query())})]),_:1})}}});export{_e as default};
