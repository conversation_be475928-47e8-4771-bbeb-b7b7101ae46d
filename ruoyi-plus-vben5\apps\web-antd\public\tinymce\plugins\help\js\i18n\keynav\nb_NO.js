tinymce.Resource.add('tinymce.html-i18n.help-keynav.nb_NO',
'<h1>Starte tastaturnavigering</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Uthe<PERSON> menylinjen</dt>\n' +
  '  <dd>Windows eller Linux: Alt + F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Utheve verktøylinjen</dt>\n' +
  '  <dd>Windows eller Linux: Alt + F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Utheve bunnteksten</dt>\n' +
  '  <dd>Windows eller Linux: Alt + F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Fokuser på varselet</dt>\n' +
  '  <dd>Windows eller Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Utheve en kontekstuell verktøylinje</dt>\n' +
  '  <dd>Windows, Linux eller macOS: Ctrl + F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Navigeringen starter ved det første grensesnittelementet, som utheves, eller understrekes når det gjelder det første elementet i\n' +
  '  elementstien i bunnteksten.</p>\n' +
  '\n' +
  '<h1>Navigere mellom grensesnittdeler</h1>\n' +
  '\n' +
  '<p>Du kan bevege deg fra én grensesnittdel til den neste ved å trykke på <strong>tabulatortasten</strong>.</p>\n' +
  '\n' +
  '<p>Du kan bevege deg fra én grensesnittdel til den forrige ved å trykke på <strong>Shift + tabulatortasten</strong>.</p>\n' +
  '\n' +
  '<p>Rekkefølgen til <strong>tabulatortasten</strong> gjennom grensesnittdelene er:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Menylinjen</li>\n' +
  '  <li>Hver gruppe på verktøylinjen</li>\n' +
  '  <li>Sidestolpen</li>\n' +
  '  <li>Elementstien i bunnteksten</li>\n' +
  '  <li>Veksleknappen for ordantall i bunnteksten</li>\n' +
  '  <li>Merkelenken i bunnteksten</li>\n' +
  '  <li>Skaleringshåndtaket for redigeringsprogrammet i bunnteksten</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Hvis en grensesnittdel ikke er til stede, blir den hoppet over.</p>\n' +
  '\n' +
  '<p>Hvis tastaturnavigeringen har uthevet bunnteksten og det ikke finnes en synlig sidestolpe, kan du trykke på <strong>Shift + tabulatortasten</strong>\n' +
  '  for å flytte fokuset til den første gruppen på verktøylinjen i stedet for den siste.</p>\n' +
  '\n' +
  '<h1>Navigere innenfor grensesnittdeler</h1>\n' +
  '\n' +
  '<p>Du kan bevege deg fra ett grensesnittelement til det neste ved å trykke på den aktuelle <strong>piltasten</strong>.</p>\n' +
  '\n' +
  '<p>De <strong>venstre</strong> og <strong>høyre</strong> piltastene</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>beveger deg mellom menyer på menylinjen.</li>\n' +
  '  <li>åpner en undermeny i en meny.</li>\n' +
  '  <li>beveger deg mellom knapper i en gruppe på verktøylinjen.</li>\n' +
  '  <li>beveger deg mellom elementer i elementstien i bunnteksten.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Ned</strong>- og <strong>opp</strong>-piltastene</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>beveger deg mellom menyelementer i en meny.</li>\n' +
  '  <li>beveger deg mellom elementer i en hurtigmeny på verktøylinjen.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Med <strong>piltastene</strong> kan du bevege deg innenfor den uthevede grensesnittdelen.</p>\n' +
  '\n' +
  '<p>Du kan lukke en åpen meny, en åpen undermeny eller en åpen hurtigmeny ved å klikke på <strong>Esc</strong>-tasten.</p>\n' +
  '\n' +
  '<p>Hvis det øverste nivået i en grensesnittdel er uthevet, kan du ved å trykke på <strong>Esc</strong> også avslutte\n' +
  '  tastaturnavigeringen helt.</p>\n' +
  '\n' +
  '<h1>Utføre et menyelement eller en knapp på en verktøylinje</h1>\n' +
  '\n' +
  '<p>Når det ønskede menyelementet eller verktøylinjeknappen er uthevet, trykker du på <strong>Retur</strong>, <strong>Enter</strong>,\n' +
  '  eller <strong>mellomromstasten</strong> for å utføre elementet.</p>\n' +
  '\n' +
  '<h1>Navigere i dialogbokser uten faner</h1>\n' +
  '\n' +
  '<p>I dialogbokser uten faner blir den første interaktive komponenten uthevet når dialogboksen åpnes.</p>\n' +
  '\n' +
  '<p>Naviger mellom interaktive komponenter i dialogboksen ved å trykke på <strong>tabulatortasten</strong> eller <strong>Shift + tabulatortasten</strong>.</p>\n' +
  '\n' +
  '<h1>Navigere i fanebaserte dialogbokser</h1>\n' +
  '\n' +
  '<p>I fanebaserte dialogbokser blir den første knappen i fanemenyen uthevet når dialogboksen åpnes.</p>\n' +
  '\n' +
  '<p>Naviger mellom interaktive komponenter i fanen ved å trykke på <strong>tabulatortasten</strong> eller\n' +
  '  <strong>Shift + tabulatortasten</strong>.</p>\n' +
  '\n' +
  '<p>Veksle til en annen fane i dialogboksen ved å utheve fanemenyen, og trykk deretter på den aktuelle <strong>piltasten</strong>\n' +
  '  for å bevege deg mellom de tilgjengelige fanene.</p>\n');