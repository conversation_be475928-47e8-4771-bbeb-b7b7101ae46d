import{_ as t,bk as U,o as c,aG as O,bx as D,g as d,aJ as N}from"./bootstrap-DCMzVRvD.js";import{g as H,c as B,d as G,r as J,a as M}from"./index-C4kXBOTV.js";import{d as w,p as R,a as S}from"../jse/index-index-C-MnMZEz.js";import"./index-B6iusSRX.js";import"./useMemo-BwJyMulH.js";import"./shallowequal-DdADXzCF.js";import"./statusUtils-d85DZFMd.js";import"./index-CFj2VWFk.js";import"./move-DLDqWE9R.js";import"./slide-B82O6h2Y.js";const T=()=>({format:String,showNow:c(),showHour:c(),showMinute:c(),showSecond:c(),use12Hours:c(),hourStep:Number,minuteStep:Number,secondStep:Number,hideDisabledOptions:c(),popupClassName:String,status:U()});function V(u){const _=H(u,t(t({},T()),{order:{type:Boolean,default:!0}})),{TimePicker:I,RangePicker:y}=_,j=w({name:"ATimePicker",inheritAttrs:!1,props:t(t(t(t({},B()),G()),T()),{addon:{type:Function}}),slots:Object,setup(m,g){let{slots:i,expose:C,emit:n,attrs:h}=g;const r=m,a=O();D(!(i.addon||r.addon),"TimePicker","`addon` is deprecated. Please use `v-slot:renderExtraFooter` instead.");const s=R();C({focus:()=>{var o;(o=s.value)===null||o===void 0||o.focus()},blur:()=>{var o;(o=s.value)===null||o===void 0||o.blur()}});const k=(o,F)=>{n("update:value",o),n("change",o,F),a.onFieldChange()},f=o=>{n("update:open",o),n("openChange",o)},P=o=>{n("focus",o)},v=o=>{n("blur",o),a.onFieldBlur()},b=o=>{n("ok",o)};return()=>{const{id:o=a.id.value}=r;return S(I,d(d(d({},h),N(r,["onUpdate:value","onUpdate:open"])),{},{id:o,dropdownClassName:r.popupClassName,mode:void 0,ref:s,renderExtraFooter:r.addon||i.addon||r.renderExtraFooter||i.renderExtraFooter,onChange:k,onOpenChange:f,onFocus:P,onBlur:v,onOk:b}),i)}}}),A=w({name:"ATimeRangePicker",inheritAttrs:!1,props:t(t(t(t({},B()),J()),T()),{order:{type:Boolean,default:!0}}),slots:Object,setup(m,g){let{slots:i,expose:C,emit:n,attrs:h}=g;const r=m,a=R(),s=O();C({focus:()=>{var e;(e=a.value)===null||e===void 0||e.focus()},blur:()=>{var e;(e=a.value)===null||e===void 0||e.blur()}});const k=(e,p)=>{n("update:value",e),n("change",e,p),s.onFieldChange()},f=e=>{n("update:open",e),n("openChange",e)},P=e=>{n("focus",e)},v=e=>{n("blur",e),s.onFieldBlur()},b=(e,p)=>{n("panelChange",e,p)},o=e=>{n("ok",e)},F=(e,p,E)=>{n("calendarChange",e,p,E)};return()=>{const{id:e=s.id.value}=r;return S(y,d(d(d({},h),N(r,["onUpdate:open","onUpdate:value"])),{},{id:e,dropdownClassName:r.popupClassName,picker:"time",mode:void 0,ref:a,onChange:k,onOpenChange:f,onFocus:P,onBlur:v,onPanelChange:b,onOk:o,onCalendarChange:F}),i)}}});return{TimePicker:j,TimeRangePicker:A}}const{TimePicker:l,TimeRangePicker:x}=V(M),ee=t(l,{TimePicker:l,TimeRangePicker:x,install:u=>(u.component(l.name,l),u.component(x.name,x),u)});export{l as TimePicker,x as TimeRangePicker,ee as default};
