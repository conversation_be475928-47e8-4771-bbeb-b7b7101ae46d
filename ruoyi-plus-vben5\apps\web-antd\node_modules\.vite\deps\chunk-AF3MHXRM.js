import {
  require_xe_utils
} from "./chunk-TETVOAVO.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/dom.js
var import_xe_utils = __toESM(require_xe_utils());
var tpImgEl;
function initTpImg() {
  if (!tpImgEl) {
    tpImgEl = new Image();
    tpImgEl.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
  }
  return tpImgEl;
}
function getTpImg() {
  if (!tpImgEl) {
    return initTpImg();
  }
  return tpImgEl;
}
var reClsMap = {};
function getClsRE(cls) {
  if (!reClsMap[cls]) {
    reClsMap[cls] = new RegExp(`(?:^|\\s)${cls}(?!\\S)`, "g");
  }
  return reClsMap[cls];
}
function getNodeOffset(elem, container, rest) {
  if (elem) {
    const parentElem = elem.parentNode;
    rest.top += elem.offsetTop;
    rest.left += elem.offsetLeft;
    if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {
      rest.top -= parentElem.scrollTop;
      rest.left -= parentElem.scrollLeft;
    }
    if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {
      return getNodeOffset(elem.offsetParent, container, rest);
    }
  }
  return rest;
}
function isScale(val) {
  return val && /^\d+%$/.test(val);
}
function hasClass(elem, cls) {
  return !!(elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls)));
}
function removeClass(elem, cls) {
  if (elem && hasClass(elem, cls)) {
    elem.className = elem.className.replace(getClsRE(cls), "");
  }
}
function addClass(elem, cls) {
  if (elem && !hasClass(elem, cls)) {
    removeClass(elem, cls);
    elem.className = `${elem.className} ${cls}`;
  }
}
function hasControlKey(evnt) {
  return evnt.ctrlKey || evnt.metaKey;
}
function toCssUnit(val, unit = "px") {
  if (import_xe_utils.default.isNumber(val) || /^\d+$/.test(`${val}`)) {
    return `${val}${unit}`;
  }
  return `${val || ""}`;
}
function getDomNode() {
  const documentElement = document.documentElement;
  const bodyElem = document.body;
  return {
    scrollTop: documentElement.scrollTop || bodyElem.scrollTop,
    scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,
    visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,
    visibleWidth: documentElement.clientWidth || bodyElem.clientWidth
  };
}
function getEventTargetNode(evnt, container, queryCls, queryMethod) {
  let targetElem;
  let target = evnt.target.shadowRoot && evnt.composed ? evnt.composedPath()[0] || evnt.target : evnt.target;
  while (target && target.nodeType && target !== document) {
    if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {
      targetElem = target;
    } else if (target === container) {
      return { flag: queryCls ? !!targetElem : true, container, targetElem };
    }
    target = target.parentNode;
  }
  return { flag: false };
}
function getOffsetPos(elem, container) {
  return getNodeOffset(elem, container, { left: 0, top: 0 });
}
function getAbsolutePos(elem) {
  const bounding = elem.getBoundingClientRect();
  const boundingTop = bounding.top;
  const boundingLeft = bounding.left;
  const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();
  return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };
}
var scrollIntoViewIfNeeded = "scrollIntoViewIfNeeded";
var scrollIntoView = "scrollIntoView";
function scrollToView(elem) {
  if (elem) {
    if (elem[scrollIntoViewIfNeeded]) {
      elem[scrollIntoViewIfNeeded]();
    } else if (elem[scrollIntoView]) {
      elem[scrollIntoView]();
    }
  }
}
function updatePanelPlacement(targetElem, panelElem, options) {
  const { placement, teleportTo, marginSize } = Object.assign({ teleportTo: false, marginSize: 5 }, options);
  let panelPlacement = "bottom";
  let top = "";
  let bottom = "";
  let left = "";
  const right = "";
  let minWidth = "";
  const stys = {};
  if (panelElem && targetElem) {
    const documentElement = document.documentElement;
    const bodyElem = document.body;
    const targetHeight = targetElem.offsetHeight;
    const panelHeight = panelElem.offsetHeight;
    const panelWidth = panelElem.offsetWidth;
    const bounding = targetElem.getBoundingClientRect();
    const boundingTop = bounding.top;
    const boundingLeft = bounding.left;
    const visibleHeight = documentElement.clientHeight || bodyElem.clientHeight;
    const visibleWidth = documentElement.clientWidth || bodyElem.clientWidth;
    minWidth = targetElem.offsetWidth;
    if (teleportTo) {
      left = boundingLeft;
      top = boundingTop + targetHeight;
      if (placement === "top") {
        panelPlacement = "top";
        top = boundingTop - panelHeight;
      } else if (!placement) {
        if (top + panelHeight + marginSize > visibleHeight) {
          panelPlacement = "top";
          top = boundingTop - panelHeight;
        }
        if (top < marginSize) {
          panelPlacement = "bottom";
          top = boundingTop + targetHeight;
        }
      }
      if (left + panelWidth + marginSize > visibleWidth) {
        left -= left + panelWidth + marginSize - visibleWidth;
      }
      if (left < marginSize) {
        left = marginSize;
      }
    } else {
      if (placement === "top") {
        panelPlacement = "top";
        bottom = targetHeight;
      } else if (!placement) {
        top = targetHeight;
        if (boundingTop + targetHeight + panelHeight > visibleHeight) {
          if (boundingTop - targetHeight - panelHeight > marginSize) {
            panelPlacement = "top";
            top = "";
            bottom = targetHeight;
          }
        }
      }
    }
    if (import_xe_utils.default.isNumber(top)) {
      stys.top = toCssUnit(top);
    }
    if (import_xe_utils.default.isNumber(bottom)) {
      stys.bottom = toCssUnit(bottom);
    }
    if (import_xe_utils.default.isNumber(left)) {
      stys.left = toCssUnit(left);
    }
    if (import_xe_utils.default.isNumber(right)) {
      stys.right = toCssUnit(right);
    }
    if (import_xe_utils.default.isNumber(minWidth)) {
      stys.minWidth = toCssUnit(minWidth);
    }
  }
  return {
    top: top || 0,
    bottom: bottom || 0,
    left: left || 0,
    right: right || 0,
    style: stys,
    placement: panelPlacement
  };
}

export {
  initTpImg,
  getTpImg,
  isScale,
  hasClass,
  removeClass,
  addClass,
  hasControlKey,
  toCssUnit,
  getDomNode,
  getEventTargetNode,
  getOffsetPos,
  getAbsolutePos,
  scrollToView,
  updatePanelPlacement
};
//# sourceMappingURL=chunk-AF3MHXRM.js.map
