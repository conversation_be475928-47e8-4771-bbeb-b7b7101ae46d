package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1177;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssConfigBoToSysOssConfigMapper;
import org.dromara.resource.domain.vo.SysOssConfigVo;
import org.dromara.resource.domain.vo.SysOssConfigVoToSysOssConfigMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1177.class,
    uses = {SysOssConfigBoToSysOssConfigMapper.class,SysOssConfigVoToSysOssConfigMapper.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
