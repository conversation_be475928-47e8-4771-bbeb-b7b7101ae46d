package org.dromara.wms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1179;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.WmsWarehouse;
import org.dromara.wms.domain.WmsWarehouseToWmsWarehouseVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1179.class,
    uses = {WmsWarehouseToWmsWarehouseVoMapper.class},
    imports = {}
)
public interface WmsWarehouseVoToWmsWarehouseMapper extends BaseMapper<WmsWarehouseVo, WmsWarehouse> {
}
