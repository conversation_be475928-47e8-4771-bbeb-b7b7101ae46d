{"doc": " 仓库列表\n\n <AUTHOR>\n @date 2025-06-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询仓库列表\n"}, {"name": "export", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出仓库列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取仓库详细信息\n\n @param warehouseId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo"], "doc": " 新增仓库\n"}, {"name": "edit", "paramTypes": ["org.dromara.wms.domain.bo.WmsWarehouseBo"], "doc": " 修改仓库\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除仓库\n\n @param warehouseIds 主键串\n"}], "constructors": []}