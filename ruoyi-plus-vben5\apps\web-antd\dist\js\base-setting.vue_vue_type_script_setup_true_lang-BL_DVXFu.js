var h=Object.defineProperty,S=Object.defineProperties;var _=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable;var f=(s,t,e)=>t in s?h(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,u=(s,t)=>{for(var e in t||(t={}))g.call(t,e)&&f(s,e,t[e]);if(c)for(var e of c(t))x.call(t,e)&&f(s,e,t[e]);return s},p=(s,t)=>S(s,_(t));var d=(s,t,e)=>new Promise((m,r)=>{var i=o=>{try{a(e.next(o))}catch(n){r(n)}},l=o=>{try{a(e.throw(o))}catch(n){r(n)}},a=o=>o.done?m(o.value):Promise.resolve(o.value).then(i,l);a((e=e.apply(s,t)).next())});import{bb as I,am as N,aj as w,al as b,ar as B}from"./bootstrap-DCMzVRvD.js";import{a as y}from"./index-DLUQE3Et.js";import{g as k}from"./dict-BLkXAGS5.js";import{m as O}from"./mitt-D4bcW7KS.js";import{p as U}from"./pick-CyUZAAhv.js";import{d as E,v as V,c as q,o as C,a as D,b as F}from"../jse/index-index-C-MnMZEz.js";const P=O(),A={class:"mt-[16px] md:w-full lg:w-1/2 2xl:w-2/5"},M=E({__name:"base-setting",props:{profile:{}},setup(s){const t=s,e=I(),m=N(),[r,i]=w({actionWrapperClass:"text-left ml-[68px] mb-[16px]",commonConfig:{labelWidth:60},handleSubmit:a,resetButtonOptions:{show:!1},schema:[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"userId",label:"用户ID",rules:"required"},{component:"Input",fieldName:"nickName",label:"昵称",rules:"required"},{component:"Input",fieldName:"email",label:"邮箱",rules:b().email("请输入正确的邮箱")},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:k(B.SYS_USER_SEX),optionType:"button"},defaultValue:"0",fieldName:"sex",label:"性别",rules:"required"},{component:"Input",fieldName:"phonenumber",label:"电话",rules:b().regex(/^1[3-9]\d{9}$/,"请输入正确的电话")}],submitButtonOptions:{content:"更新信息"}});function l(o){i.setState(n=>p(u({},n),{submitButtonOptions:p(u({},n.submitButtonOptions),{loading:o})}))}function a(o){return d(this,null,function*(){try{l(!0),yield y(o);const n=yield m.fetchUserInfo();e.setUserInfo(n),P.emit("updateProfile")}catch(n){console.error(n)}finally{l(!1)}})}return V(()=>{const o=U(t.profile.user,["userId","nickName","email","phonenumber","sex"]);i.setValues(o)}),(o,n)=>(C(),q("div",A,[D(F(r))]))}});export{M as _,P as e};
