var c=(e,w,n)=>new Promise((r,m)=>{var f=o=>{try{p(n.next(o))}catch(s){m(s)}},t=o=>{try{p(n.throw(o))}catch(s){m(s)}},p=o=>o.done?r(o.value):Promise.resolve(o.value).then(f,t);p((n=n.apply(e,w)).next())});import{y as d,$ as y,aj as S}from"./bootstrap-DCMzVRvD.js";import{c as C}from"./helper-Bc7QQ92Q.js";import{u as D,d as b}from"./popup-D6rC6QBG.js";import{g as u}from"./dict-BLkXAGS5.js";import{r as h}from"./render-BxXtQdeV.js";import{d as R,p as T,B as x,P as G,h as k,o as B,w as P,a as V,b as g}from"../jse/index-index-C-MnMZEz.js";import{u as q}from"./use-modal-CeMSCP2m.js";function J(e){return d.get("/warehousemanage/warehouse/list",{params:e})}function K(e){return C("/warehousemanage/warehouse/export",e!=null?e:{})}function F(e){return d.get(`/warehousemanage/warehouse/${e}`)}function M(e){return d.postWithMsg("/warehousemanage/warehouse",e)}function A(e){return d.putWithMsg("/warehousemanage/warehouse",e)}function Q(e){return d.deleteWithMsg(`/warehousemanage/warehouse/${e}`)}const X=()=>[{component:"Input",fieldName:"deptId",label:"部门ID"},{component:"Input",fieldName:"warehouseNumber",label:"仓库编码"},{component:"Input",fieldName:"warehouseName",label:"仓库名称"},{component:"RadioGroup",componentProps:{options:u("ware_warehouse_type"),buttonStyle:"solid",optionType:"button"},fieldName:"warehouseType",label:"仓库属性"},{component:"RadioGroup",componentProps:{options:u("ware_warehouse_intype"),buttonStyle:"solid",optionType:"button"},fieldName:"warehouseInventoryStatus",label:"库存状态"},{component:"RadioGroup",componentProps:{options:u("ware_warehouse_intype"),buttonStyle:"solid",optionType:"button"},fieldName:"warehouseRecevingStatus",label:"库存状态"}],Y=[{type:"checkbox",width:60},{title:"仓库ID",field:"warehouseId"},{title:"部门ID",field:"deptId"},{title:"仓库编码",field:"warehouseNumber"},{title:"仓库名称",field:"warehouseName"},{title:"仓库属性",field:"warehouseType",slots:{default:({row:e})=>h(e.warehouseType,"ware_warehouse_type")}},{title:"库存状态",field:"warehouseInventoryStatus",slots:{default:({row:e})=>h(e.warehouseInventoryStatus,"ware_warehouse_intype")}},{title:"库存状态",field:"warehouseRecevingStatus",slots:{default:({row:e})=>h(e.warehouseRecevingStatus,"ware_warehouse_intype")}},{title:"备注",field:"remark"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",width:180}],W=()=>[{label:"仓库ID",fieldName:"warehouseId",component:"Input",dependencies:{show:()=>!1,triggerFields:[""]}},{label:"部门ID",fieldName:"deptId",component:"Input"},{label:"仓库编码",fieldName:"warehouseNumber",component:"Input",rules:"required"},{label:"仓库名称",fieldName:"warehouseName",component:"Input",rules:"required"},{label:"仓库属性",fieldName:"warehouseType",component:"RadioGroup",componentProps:{options:u("ware_warehouse_type"),buttonStyle:"solid",optionType:"button"}},{label:"库存状态",fieldName:"warehouseInventoryStatus",component:"RadioGroup",componentProps:{options:u("ware_warehouse_intype"),buttonStyle:"solid",optionType:"button"}},{label:"库存状态",fieldName:"warehouseRecevingStatus",component:"RadioGroup",componentProps:{options:u("ware_warehouse_intype"),buttonStyle:"solid",optionType:"button"}},{label:"备注",fieldName:"remark",component:"Textarea"}],Z=R({__name:"warehouse-modal",emits:["reload"],setup(e,{emit:w}){const n=w,r=T(!1),m=x(()=>r.value?y("pages.common.edit"):y("pages.common.add")),[f,t]=S({commonConfig:{formItemClass:"col-span-2",labelWidth:80,componentProps:{class:"w-full"}},schema:W(),showDefaultActions:!1,wrapperClass:"grid-cols-2"}),{onBeforeClose:p,markInitialized:o,resetInitialized:s}=D({initializedGetter:b(t),currentGetter:b(t)}),[_,l]=q({class:"w-[550px]",fullscreenButton:!1,onBeforeClose:p,onClosed:N,onConfirm:I,onOpenChange:i=>c(null,null,function*(){if(!i)return null;l.modalLoading(!0);const{id:a}=l.getData();if(r.value=!!a,r.value&&a){const v=yield F(a);yield t.setValues(v)}yield o(),l.modalLoading(!1)})});function I(){return c(this,null,function*(){try{l.lock(!0);const{valid:i}=yield t.validate();if(!i)return;const a=G(yield t.getValues());yield r.value?A(a):M(a),s(),n("reload"),l.close()}catch(i){console.error(i)}finally{l.lock(!1)}})}function N(){return c(this,null,function*(){yield t.resetForm(),s()})}return(i,a)=>(B(),k(g(_),{title:m.value},{default:P(()=>[V(g(f))]),_:1},8,["title"]))}});export{Z as _,Q as a,K as b,Y as c,X as q,J as w};
