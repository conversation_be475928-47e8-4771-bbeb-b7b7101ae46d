import{U as e,p as d,M as r,d as f}from"../jse/index-index-C-MnMZEz.js";import{d as u,r as c}from"./relativeTime-DEqmspR6.js";import{_ as m}from"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import{an as p}from"./bootstrap-DCMzVRvD.js";function T(t){const{onOk:n,title:a,description:i}=t,o=d("");p.confirm({title:a,content:r(f({setup(){return()=>r(m,{description:i,value:o.value,"onUpdate:value":s=>o.value=s})}})),centered:!0,okButtonProps:{danger:!0},onOk:()=>n(o.value)})}e.extend(u);e.extend(c);function h(t){const n=e().diff(e(t),"second");return e.duration(n,"seconds").humanize()}export{T as a,h as g};
