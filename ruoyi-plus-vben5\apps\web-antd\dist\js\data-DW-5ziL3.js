import{y as i}from"./bootstrap-DCMzVRvD.js";import{U as n}from"../jse/index-index-C-MnMZEz.js";import{b as l,a as r}from"./render-BxXtQdeV.js";function d(){return i.get("/monitor/online")}function u(e){return i.get("/monitor/online/list",{params:e})}function m(e){return i.deleteWithMsg(`/monitor/online/${e}`)}function c(e){return i.deleteWithMsg(`/monitor/online/myself/${e}`)}const p=()=>[{component:"Input",fieldName:"ipaddr",label:"IP地址"},{component:"Input",fieldName:"userName",label:"用户账号"}],g=[{title:"登录平台",field:"deviceType"},{title:"登录账号",field:"userName"},{title:"部门名称",field:"deptName"},{title:"IP地址",field:"ipaddr"},{title:"登录地址",field:"loginLocation"},{title:"浏览器",field:"browser",slots:{default:({row:e})=>l(e.browser,!0)}},{title:"系统",field:"os",slots:{default:({row:e})=>{let t=e.os;if(t){const o=t.split(" or ");o.length===2&&(t=o[0])}return r(t,!0)}}},{title:"登录时间",field:"loginTime",formatter:({cellValue:e})=>n(e).format("YYYY-MM-DD HH:mm:ss")},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}];export{u as a,m as b,g as c,c as f,d as o,p as q};
