package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__5;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysPostBoToSysPostMapper__5.class,SysPostVoToSysPostMapper__5.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__5 extends BaseMapper<SysPost, SysPostVo> {
}
