{"doc": " 字典工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "setDict<PERSON>ache", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 设置字典缓存\n\n @param key       参数键\n @param dictDatas 字典数据列表\n"}, {"name": "getDictCache", "paramTypes": ["java.lang.String"], "doc": " 获取字典缓存\n\n @param key 参数键\n @return dictDatas 字典数据列表\n"}, {"name": "removeDictCache", "paramTypes": ["java.lang.String"], "doc": " 删除指定字典缓存\n\n @param key 字典键\n"}, {"name": "clearDictCache", "paramTypes": [], "doc": " 清空字典缓存\n"}], "constructors": []}