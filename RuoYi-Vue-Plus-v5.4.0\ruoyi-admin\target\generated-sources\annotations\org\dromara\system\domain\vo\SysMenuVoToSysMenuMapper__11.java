package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMenu;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysMenuToSysMenuVoMapper__11.class,SysMenuToSysMenuVoMapper__11.class},
    imports = {}
)
public interface SysMenuVoToSysMenuMapper__11 extends BaseMapper<SysMenuVo, SysMenu> {
}
