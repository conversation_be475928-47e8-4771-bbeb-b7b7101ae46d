var M=Object.defineProperty;var P=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var D=(r,o,e)=>o in r?M(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,S=(r,o)=>{for(var e in o||(o={}))N.call(o,e)&&D(r,e,o[e]);if(P)for(var e of P(o))O.call(o,e)&&D(r,e,o[e]);return r};var _=(r,o,e)=>new Promise((w,i)=>{var x=p=>{try{g(e.next(p))}catch(f){i(f)}},l=p=>{try{g(e.throw(p))}catch(f){i(f)}},g=p=>p.done?w(p.value):Promise.resolve(p.value).then(x,l);g((e=e.apply(r,o)).next())});import{at as z,as as U,an as j}from"./bootstrap-DCMzVRvD.js";import{v as F}from"./vxe-table-DzEj5Fop.js";import{p as G,a as L,b as A,c as W}from"./index-C9ZGxfH6.js";import{_ as H}from"./table-switch.vue_vue_type_script_setup_true_lang-BPKnQ2Wy.js";import{c as J}from"./download-UJak946_.js";import{c as K,q as Q,_ as X}from"./tenant-package-drawer.vue_vue_type_script_setup_true_lang-B1Xm6BZc.js";import q from"./index-BeyziwLP.js";import{_ as Y}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{_ as Z}from"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import{d as ee,B as te,l as B,S as oe,h as c,o as d,w as n,a as u,b as t,T as C,k as h,t as v}from"../jse/index-index-C-MnMZEz.js";import{u as ae}from"./use-vxe-grid-BC7vZzEr.js";import{u as ne}from"./use-drawer-6qcpK-D1.js";import{P as se}from"./index-DNdMANjv.js";import{g as re}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./index-D-hwdOI6.js";import"./index-C5dPwGGG.js";import"./menu-select-table-C_Crb3c1.js";import"./menu-select-table.vue_vue_type_style_index_0_scoped_96b02cc5_lang-Be00nslj.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./tree-DFBawhPd.js";import"./uniq-CCKK3QTo.js";import"./index-D2_dYV5Z.js";import"./Checkbox-DRV8G-PI.js";import"./index-DabkQ3D7.js";import"./Group-oWwucTzK.js";import"./index-kC0HFDdy.js";import"./popup-D6rC6QBG.js";import"./rotate-cw-DzZTu9nW.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Ge=ee({__name:"index",setup(r){const o={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:Q(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},e={checkboxConfig:{highlight:!0,reserve:!0},columns:K,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(k,...$)=>_(null,[k,...$],function*({page:a},s={}){return yield L(S({pageNum:a.currentPage,pageSize:a.pageSize},s))})}},rowConfig:{keyField:"packageId"},id:"system-tenant-package-index"},[w,i]=ae({formOptions:o,gridOptions:e}),[x,l]=ne({connectedComponent:X});function g(){l.setData({}),l.open()}function p(a){return _(this,null,function*(){l.setData({id:a.packageId}),l.open()})}function f(a){return _(this,null,function*(){yield A([a.packageId]),yield i.query()})}function V(){const s=i.grid.getCheckboxRecords().map(k=>k.packageId);j.confirm({title:"提示",okType:"danger",content:`确认删除选中的${s.length}条记录吗？`,onOk:()=>_(null,null,function*(){yield A(s),yield i.query()})})}function R(){J(W,"租户套餐数据",i.formApi.form.values)}const{hasAccessByCodes:T,hasAccessByRoles:E}=z(),I=te(()=>E(["superadmin"]));return(a,s)=>{const k=B("a-button"),$=B("ghost-button"),y=oe("access");return I.value?(d(),c(t(Y),{key:0,"auto-content-height":!0},{default:n(()=>[u(t(w),{"table-title":"租户套餐列表"},{"toolbar-tools":n(()=>[u(t(q),null,{default:n(()=>[C((d(),c(k,{onClick:R},{default:n(()=>[h(v(a.$t("pages.common.export")),1)]),_:1})),[[y,["system:tenantPackage:export"],"code"]]),C((d(),c(k,{disabled:!t(F)(t(i)),danger:"",type:"primary",onClick:V},{default:n(()=>[h(v(a.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[y,["system:tenantPackage:remove"],"code"]]),C((d(),c(k,{type:"primary",onClick:g},{default:n(()=>[h(v(a.$t("pages.common.add")),1)]),_:1})),[[y,["system:tenantPackage:add"],"code"]])]),_:1})]),status:n(({row:m})=>[u(t(H),{value:m.status,"onUpdate:value":b=>m.status=b,api:()=>t(G)(m),disabled:!t(T)(["system:tenantPackage:edit"]),onReload:s[0]||(s[0]=b=>t(i).query())},null,8,["value","onUpdate:value","api","disabled"])]),action:n(({row:m})=>[u(t(q),null,{default:n(()=>[C((d(),c($,{onClick:b=>p(m)},{default:n(()=>[h(v(a.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[y,["system:tenantPackage:edit"],"code"]]),u(t(se),{"get-popup-container":t(re),placement:"left",title:"确认删除？",onConfirm:b=>f(m)},{default:n(()=>[C((d(),c($,{danger:"",onClick:s[1]||(s[1]=U(()=>{},["stop"]))},{default:n(()=>[h(v(a.$t("pages.common.delete")),1)]),_:1})),[[y,["system:tenantPackage:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),u(t(x),{onReload:s[2]||(s[2]=m=>t(i).query())})]),_:1})):(d(),c(t(Z),{key:1,description:"您没有租户的访问权限",status:"403"}))}}});export{Ge as default};
