package org.dromara.wms.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.wms.domain.WmsWarehouse;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库列表业务对象 wms_warehouse
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WmsWarehouse.class, reverseConvertGenerate = false)
public class WmsWarehouseBo extends BaseEntity {

    /**
     * 仓库id
     */
    @NotNull(message = "仓库id不能为空", groups = { EditGroup.class })
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @NotBlank(message = "仓库编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseNumber;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseName;

    /**
     * 仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）
     */
    private String warehouseType;

    /**
     * 库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
     */
    private String warehouseInventoryStatus;

    /**
     * 收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
     */
    private String warehouseRecevingStatus;

    /**
     * 备注
     */
    private String remark;


}
