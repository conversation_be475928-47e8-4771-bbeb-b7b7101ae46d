package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__11;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__11.class,SysDictTypeVoToSysDictTypeMapper__11.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__11 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
