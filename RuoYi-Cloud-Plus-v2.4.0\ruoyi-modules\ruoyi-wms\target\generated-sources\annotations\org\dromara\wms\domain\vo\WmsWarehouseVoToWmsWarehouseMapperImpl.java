package org.dromara.wms.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.wms.domain.WmsWarehouse;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:40:18+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class WmsWarehouseVoToWmsWarehouseMapperImpl implements WmsWarehouseVoToWmsWarehouseMapper {

    @Override
    public WmsWarehouse convert(WmsWarehouseVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WmsWarehouse wmsWarehouse = new WmsWarehouse();

        wmsWarehouse.setWarehouseId( arg0.getWarehouseId() );
        wmsWarehouse.setWarehouseNumber( arg0.getWarehouseNumber() );
        wmsWarehouse.setWarehouseName( arg0.getWarehouseName() );
        wmsWarehouse.setWarehouseType( arg0.getWarehouseType() );
        wmsWarehouse.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wmsWarehouse.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wmsWarehouse.setRemark( arg0.getRemark() );

        return wmsWarehouse;
    }

    @Override
    public WmsWarehouse convert(WmsWarehouseVo arg0, WmsWarehouse arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseType( arg0.getWarehouseType() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
