{"doc": " 安全相关工具类\n\n <AUTHOR>\n", "fields": [{"name": "PUBLIC_KEY", "doc": " 公钥\n"}, {"name": "PRIVATE_KEY", "doc": " 私钥\n"}], "enumConstants": [], "methods": [{"name": "encryptByBase64", "paramTypes": ["java.lang.String"], "doc": " Base64加密\n\n @param data 待加密数据\n @return 加密后字符串\n"}, {"name": "decryptByBase64", "paramTypes": ["java.lang.String"], "doc": " Base64解密\n\n @param data 待解密数据\n @return 解密后字符串\n"}, {"name": "encryptByAes", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " AES加密\n\n @param data     待加密数据\n @param password 秘钥字符串\n @return 加密后字符串, 采用Base64编码\n"}, {"name": "encryptByAesHex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " AES加密\n\n @param data     待加密数据\n @param password 秘钥字符串\n @return 加密后字符串, 采用Hex编码\n"}, {"name": "decryptByAes", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " AES解密\n\n @param data     待解密数据\n @param password 秘钥字符串\n @return 解密后字符串\n"}, {"name": "encryptBySm4", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " SM4加密（Base64编码）\n\n @param data     待加密数据\n @param password 秘钥字符串\n @return 加密后字符串, 采用Base64编码\n"}, {"name": "encryptBySm4Hex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " SM4加密（Hex编码）\n\n @param data     待加密数据\n @param password 秘钥字符串\n @return 加密后字符串, 采用Hex编码\n"}, {"name": "decryptBySm4", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " sm4解密\n\n @param data     待解密数据（可以是Base64或Hex编码）\n @param password 秘钥字符串\n @return 解密后字符串\n"}, {"name": "generateSm2Key", "paramTypes": [], "doc": " 产生sm2加解密需要的公钥和私钥\n\n @return 公私钥Map\n"}, {"name": "encryptBySm2", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " sm2公钥加密\n\n @param data      待加密数据\n @param publicKey 公钥\n @return 加密后字符串, 采用Base64编码\n"}, {"name": "encryptBySm2Hex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " sm2公钥加密\n\n @param data      待加密数据\n @param publicKey 公钥\n @return 加密后字符串, 采用Hex编码\n"}, {"name": "decryptBySm2", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " sm2私钥解密\n\n @param data       待解密数据\n @param privateKey 私钥\n @return 解密后字符串\n"}, {"name": "generateRsaKey", "paramTypes": [], "doc": " 产生RSA加解密需要的公钥和私钥\n\n @return 公私钥Map\n"}, {"name": "encryptByRsa", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " rsa公钥加密\n\n @param data      待加密数据\n @param publicKey 公钥\n @return 加密后字符串, 采用Base64编码\n"}, {"name": "encryptByRsaHex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " rsa公钥加密\n\n @param data      待加密数据\n @param publicKey 公钥\n @return 加密后字符串, 采用Hex编码\n"}, {"name": "decryptByRsa", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " rsa私钥解密\n\n @param data       待解密数据\n @param privateKey 私钥\n @return 解密后字符串\n"}, {"name": "encryptByMd5", "paramTypes": ["java.lang.String"], "doc": " md5加密\n\n @param data 待加密数据\n @return 加密后字符串, 采用Hex编码\n"}, {"name": "encryptBySha256", "paramTypes": ["java.lang.String"], "doc": " sha256加密\n\n @param data 待加密数据\n @return 加密后字符串, 采用Hex编码\n"}, {"name": "encryptBySm3", "paramTypes": ["java.lang.String"], "doc": " sm3加密\n\n @param data 待加密数据\n @return 加密后字符串, 采用Hex编码\n"}], "constructors": []}