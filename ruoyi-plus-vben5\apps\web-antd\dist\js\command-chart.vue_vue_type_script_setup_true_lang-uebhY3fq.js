import{u as c,_ as p}from"./use-echarts-CF-NZzbo.js";import{d as u,p as m,q as d,v as f,R as h,h as _,o as l,b}from"../jse/index-index-C-MnMZEz.js";const k=u({__name:"command-chart",props:{data:{default:()=>[]}},setup(n){const t=n,a=m(),{renderEcharts:o,resize:s}=c(a);d(()=>t.data,()=>{a.value&&e(t.data)},{immediate:!0}),f(()=>{e(t.data)}),h(()=>s(!1));function e(r){o({series:[{animationDuration:1e3,animationEasing:"cubicInOut",center:["50%","50%"],data:r,name:"命令",radius:[15,95],roseType:"radius",type:"pie"}],tooltip:{formatter:"{a} <br/>{b} : {c} ({d}%)",trigger:"item"}})}return(r,i)=>(l(),_(b(p),{ref_key:"chartRef",ref:a,height:"400px",width:"100%"},null,512))}});export{k as _};
