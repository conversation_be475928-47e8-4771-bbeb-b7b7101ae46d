package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssBoToSysOssMapper__10;
import org.dromara.resource.domain.vo.SysOssVo;
import org.dromara.resource.domain.vo.SysOssVoToSysOssMapper__10;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssBoToSysOssMapper__10.class,SysOssVoToSysOssMapper__10.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__10 extends BaseMapper<SysOss, SysOssVo> {
}
