---
description: RuoYi-Plus-Vben5项目的详细技术栈信息，包括框架版本、核心依赖、构建工具等技术细节。当需要深入了解项目技术架构、依赖版本、构建配置等专业技术信息时使用。
globs: 
alwaysApply: false
---
# 技术栈详细信息

## 核心技术栈

### 前端框架
- **Vue 3.5.13**: 采用Composition API，提供更好的TypeScript支持
- **Vben 5.5.6**: 基于Vue3的现代化管理后台框架
- **TypeScript 5.8.3**: 类型安全的JavaScript超集

### UI组件库
- **Ant Design Vue 4.2.6**: 企业级UI组件库
- **@ant-design/icons-vue**: Ant Design图标库
- **TailwindCSS 3.4.17**: 原子化CSS框架

### 构建工具
- **Vite 6.3.4**: 现代化构建工具，配置在 [apps/web-antd/vite.config.mts](mdc:apps/web-antd/vite.config.mts)
- **Turbo 2.5.2**: 高性能构建系统，配置在 [turbo.json](mdc:turbo.json)
- **pnpm**: 包管理器，工作空间配置在 [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)

### 状态管理
- **Pinia 3.0.2**: Vue3官方推荐的状态管理库
- **pinia-plugin-persistedstate**: 状态持久化插件
- 主要Store位于 [packages/stores/](mdc:packages/stores)

### 路由管理
- **Vue Router 4.5.1**: Vue3路由管理器
- 路由配置：[apps/web-antd/src/router/](mdc:apps/web-antd/src/router)
- 路由守卫：[apps/web-antd/src/router/guard.ts](mdc:apps/web-antd/src/router/guard.ts)

## 业务功能技术

### 表单处理
- **Vee-validate 4.15.0**: Vue3表单验证库
- **Zod 3.24.3**: TypeScript优先的模式验证库
- **@vee-validate/zod**: 两者的集成包

### HTTP客户端
- **Axios 1.9.0**: HTTP请求库
- 封装在：[apps/web-antd/src/api/request.ts](mdc:apps/web-antd/src/api/request.ts)
- 支持请求/响应拦截、加密传输、错误处理

### 加密安全
- **crypto-js 4.2.0**: JavaScript加密库，AES加密实现
- **jsencrypt 3.3.2**: RSA加密库
- 加密工具：[apps/web-antd/src/utils/encryption/](mdc:apps/web-antd/src/utils/encryption)

### 富文本编辑
- **TinyMCE 7.3.0**: 功能丰富的富文本编辑器
- **@tinymce/tinymce-vue**: Vue3集成包
- 组件封装：[apps/web-antd/src/components/tinymce/](mdc:apps/web-antd/src/components/tinymce)

### 数据可视化
- **ECharts 5.6.0**: 强大的数据可视化库
- 支持图表组件和仪表板展示

### 国际化
- **Vue I18n 11.1.3**: Vue3国际化库
- **@intlify/unplugin-vue-i18n**: 构建时优化插件
- 语言包：[apps/web-antd/src/locales/](mdc:apps/web-antd/src/locales)

### 工作流
- **warmflow**: 工作流引擎集成
- API接口：[apps/web-antd/src/api/workflow/](mdc:apps/web-antd/src/api/workflow)
- 页面组件：[apps/web-antd/src/views/workflow/](mdc:apps/web-antd/src/views/workflow)

## 开发工具链

### 代码质量
- **ESLint 9.26.0**: JavaScript/TypeScript代码检查
- **@typescript-eslint/parser**: TypeScript解析器
- **eslint-plugin-vue**: Vue专用ESLint插件

### 代码格式化
- **Prettier 3.5.3**: 代码格式化工具
- **prettier-plugin-tailwindcss**: TailwindCSS类排序

### 样式规范
- **Stylelint 16.19.1**: CSS/SCSS代码检查
- **stylelint-config-standard**: 标准配置
- **stylelint-scss**: SCSS支持

### 测试框架
- **Vitest 3.1.2**: 基于Vite的测试框架
- **@vue/test-utils**: Vue组件测试工具
- **@playwright/test**: 端到端测试框架

### Git工作流
- **Lefthook 1.11.12**: Git钩子管理器，配置在 [lefthook.yml](mdc:lefthook.yml)
- **Commitlint**: 提交信息规范检查
- **czg**: 交互式提交信息生成

## 工具函数和工具库

### 工具函数
- **lodash-es**: JavaScript工具库（ES模块版本）
- **dayjs**: 轻量级日期处理库
- **@vueuse/core**: Vue组合式API工具集

### 文件处理
- **cropperjs**: 图片裁剪库
- **qrcode**: 二维码生成库
- **watermark-js-plus**: 水印生成库

### 其他工具
- **clsx**: 条件样式类名工具
- **sortablejs**: 拖拽排序库
- **tippy.js**: 提示框库

## 版本要求

### 运行环境
- **Node.js**: >= 20.10.0
- **pnpm**: >= 9.12.0

### 浏览器支持
- Chrome 88+
- Firefox 最新2个版本
- Safari 最新2个版本
- Edge 最新2个版本
- 不支持 IE

### 包管理器
使用 `pnpm@10.10.0`，配置在 [package.json](mdc:package.json) 的 `packageManager` 字段
