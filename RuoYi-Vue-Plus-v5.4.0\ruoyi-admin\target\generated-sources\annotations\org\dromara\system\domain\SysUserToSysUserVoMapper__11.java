package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__11;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__11;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysRoleVoToSysRoleMapper__11.class,SysRoleToSysRoleVoMapper__11.class,SysUserVoToSysUserMapper__11.class,SysUserBoToSysUserMapper__11.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__11 extends BaseMapper<SysUser, SysUserVo> {
}
