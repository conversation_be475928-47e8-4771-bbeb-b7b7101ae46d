package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOssConfig;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOssConfigToSysOssConfigVoMapper__11.class},
    imports = {}
)
public interface SysOssConfigVoToSysOssConfigMapper__11 extends BaseMapper<SysOssConfigVo, SysOssConfig> {
}
