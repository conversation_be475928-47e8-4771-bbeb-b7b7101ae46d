{"doc": " 流程分类视图对象 wf_category\n\n <AUTHOR>\n @date 2023-06-27\n", "fields": [{"name": "categoryId", "doc": " 流程分类ID\n"}, {"name": "parentId", "doc": " 父级id\n"}, {"name": "parentName", "doc": " 父类别名称\n"}, {"name": "ancestors", "doc": " 祖级列表\n"}, {"name": "categoryName", "doc": " 流程分类名称\n"}, {"name": "orderNum", "doc": " 显示顺序\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "children", "doc": " 子菜单\n"}], "enumConstants": [], "methods": [], "constructors": []}