import{c as d}from"./helper-Bc7QQ92Q.js";import{y as e,bc as c}from"./bootstrap-DCMzVRvD.js";function u(t){return e.get(`/system/dict/data/type/${t}`)}function g(t){return e.get("/system/dict/data/list",{params:t})}function l(t){return d("/system/dict/data/export",t)}function h(t){return e.deleteWithMsg(`/system/dict/data/${t}`)}function y(t){return e.postWithMsg("/system/dict/data",t)}function m(t){return e.putWithMsg("/system/dict/data",t)}function $(t){return e.get(`/system/dict/data/${t}`)}function f(t,a,n=!1){const{dictRequestCache:s,setDictInfo:o}=c(),i=a();return i.length===0&&!s.has(t)&&s.set(t,u(t).then(r=>{o(t,r,n)}).catch(()=>{s.delete(t)}).finally(()=>{i.length>0&&s.delete(t)})),i}function x(t,a=!1){const{getDictOptions:n}=c();return f(t,()=>n(t),a)}export{h as a,l as b,$ as c,g as d,m as e,y as f,x as g};
