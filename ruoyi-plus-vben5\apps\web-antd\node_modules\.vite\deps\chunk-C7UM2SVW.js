import {
  _extends
} from "./chunk-YTNASAWS.js";
import {
  inject,
  provide,
  reactive,
  watchEffect
} from "./chunk-7J2PGW6H.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/createContext.js
function createContext(defaultValue) {
  const contextKey = Symbol("contextKey");
  const useProvide = (props, newProps) => {
    const mergedProps = reactive({});
    provide(contextKey, mergedProps);
    watchEffect(() => {
      _extends(mergedProps, props, newProps || {});
    });
    return mergedProps;
  };
  const useInject = () => {
    return inject(contextKey, defaultValue) || {};
  };
  return {
    useProvide,
    useInject
  };
}
var createContext_default = createContext;

export {
  createContext_default
};
//# sourceMappingURL=chunk-C7UM2SVW.js.map
