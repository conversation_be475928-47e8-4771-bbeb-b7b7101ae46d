{"doc": " 办理人权限处理器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "permissions", "paramTypes": [], "doc": " 办理人权限标识，比如用户，角色，部门等，用于校验是否有权限办理任务\n 后续在{@link FlowParams#getPermissionFlag}  中获取\n 返回当前用户权限集合\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 获取当前办理人\n\n @return 当前办理人\n"}, {"name": "convertPermissions", "paramTypes": ["java.util.List"], "doc": " 转换办理人，比如设计器中预设了能办理的人，如果其中包含角色或者部门id等，可以通过此接口进行转换成用户id\n"}], "constructors": []}