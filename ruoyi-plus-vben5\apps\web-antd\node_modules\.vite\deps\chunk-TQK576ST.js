import {
  AntdIcon_default
} from "./chunk-NMOUCHYD.js";
import {
  createVNode
} from "./chunk-7J2PGW6H.js";

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/CheckOutlined.js
var CheckOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z" } }] }, "name": "check", "theme": "outlined" };
var CheckOutlined_default = CheckOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.16_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/CheckOutlined.js
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var CheckOutlined2 = function CheckOutlined3(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread({}, p, {
    "icon": CheckOutlined_default
  }), null);
};
CheckOutlined2.displayName = "CheckOutlined";
CheckOutlined2.inheritAttrs = false;
var CheckOutlined_default2 = CheckOutlined2;

export {
  CheckOutlined_default2 as CheckOutlined_default
};
//# sourceMappingURL=chunk-TQK576ST.js.map
