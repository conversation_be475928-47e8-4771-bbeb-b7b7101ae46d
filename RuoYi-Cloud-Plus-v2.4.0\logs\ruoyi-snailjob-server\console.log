2025-06-16 00:12:28 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 00:12:28 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [Thread<PERSON>oolManager] Start destroying ThreadPool
2025-06-16 00:12:28 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-16 00:12:28 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 00:12:28 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [Thread<PERSON><PERSON>Manager] Completed destruction of ThreadPool
2025-06-16 00:12:28 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-16 00:12:28 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934260300768702464]
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-16 00:12:28 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 00:12:28 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-16 00:12:28 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:12:29 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-snailjob-server', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/snail-job/actuator, IPv6=[240e:3a1:bc85:beb1:187:bb4c:9c3e:8a7b], username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=8800, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 00:12:29 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:12:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 00:12:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-16 08:23:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:23:09 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 29080 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:23:09 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-16 08:23:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-16 08:23:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:23:16 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-16 08:23:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-16 08:23:16 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-16 08:23:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 08:23:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-16 08:23:17 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-16 08:23:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7151 ms
2025-06-16 08:23:19 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-16 08:23:19 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 08:23:20 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 08:23:20 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 08:23:20 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 08:23:21 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 08:23:21 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@35f3a22c
2025-06-16 08:23:23 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 08:23:23 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-16 08:23:23 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-16 08:23:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-16 08:23:23 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-16 08:23:23 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-16 08:23:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server ************:8800 register finished
2025-06-16 08:23:24 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 08:23:24 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-16 08:23:24 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-16 08:23:25 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-16 08:23:25 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-16 08:23:25 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-16 08:23:25 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-16 08:23:25 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-16 08:23:25 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-16 08:23:25 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 20.33 seconds (process running for 21.205)
2025-06-16 08:23:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:23:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-16 08:23:25 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 08:23:25 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 08:23:25 [RMI TCP Connection(14)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 08:23:25 [RMI TCP Connection(14)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-16 08:23:25 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@a42c5c9
2025-06-16 08:23:25 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 08:23:25 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934406394152476672]
2025-06-16 08:23:35 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-16 08:23:35 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-16 08:27:36 [snail-job-scheduled-thread-2] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1934407461296582656]
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-16 09:16:40 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934406394152476672]
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-16 09:16:40 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:16:41 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:16:41 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-snailjob-server', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/snail-job/actuator, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8800, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 09:16:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-16 09:31:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:31:47 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 43516 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:31:47 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:31:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-16 09:31:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:31:49 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-16 09:31:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-16 09:31:49 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-16 09:31:49 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:31:49 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-16 09:31:49 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:31:49 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1593 ms
2025-06-16 09:31:51 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-16 09:31:51 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:31:51 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:31:51 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:31:51 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:31:53 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:31:53 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@e76b097
2025-06-16 09:31:55 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:31:55 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-16 09:31:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-16 09:31:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-16 09:31:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-16 09:31:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-16 09:31:55 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server ************:8800 register finished
2025-06-16 09:31:56 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:31:56 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-16 09:31:56 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-16 09:31:56 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-16 09:31:56 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-16 09:31:56 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-16 09:31:56 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-16 09:31:56 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-16 09:31:56 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-16 09:31:56 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 12.251 seconds (process running for 12.904)
2025-06-16 09:31:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 09:31:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-16 09:31:57 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:31:57 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@320bcb31
2025-06-16 09:31:57 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:31:57 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:31:57 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:31:57 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-16 09:31:57 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934423641407324160]
2025-06-16 09:32:06 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-16 09:32:06 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-16 09:36:22 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:36:22 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:36:22 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:36:22 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:36:22 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:36:22 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-16 09:36:22 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934423641407324160]
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-16 09:36:22 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-16 09:36:22 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:36:23 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:36:23 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-snailjob-server', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/snail-job/actuator, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8800, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 09:36:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-16 09:41:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:41:55 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 42000 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:41:55 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:41:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-16 09:41:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:41:56 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-16 09:41:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-16 09:41:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-16 09:41:57 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:41:57 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-16 09:41:57 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:41:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1983 ms
2025-06-16 09:41:59 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-16 09:42:00 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:42:00 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:42:00 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:42:00 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:42:02 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:42:02 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6f2e1024
2025-06-16 09:42:04 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:42:04 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-16 09:42:05 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-16 09:42:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-16 09:42:05 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-16 09:42:05 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-16 09:42:05 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server ************:8800 register finished
2025-06-16 09:42:06 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:42:06 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-16 09:42:06 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-16 09:42:07 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-16 09:42:07 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-16 09:42:07 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-16 09:42:07 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-16 09:42:07 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-16 09:42:07 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-16 09:42:07 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 15.83 seconds (process running for 16.63)
2025-06-16 09:42:07 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 09:42:07 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-16 09:42:07 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:42:07 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@25af1870
2025-06-16 09:42:07 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:42:07 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:42:07 [RMI TCP Connection(5)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:42:07 [RMI TCP Connection(5)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-16 09:42:07 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934426193033097216]
2025-06-16 09:42:17 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-16 09:42:17 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-16 09:46:38 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:46:38 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:46:38 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:46:38 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:46:38 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:46:38 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-16 09:46:38 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934426193033097216]
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-16 09:46:38 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-16 09:46:38 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:46:40 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:46:41 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-snailjob-server', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/snail-job/actuator, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8800, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:46:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:46:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 09:46:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-16 09:49:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:49:17 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 45720 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:49:17 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:49:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-16 09:49:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:49:18 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-16 09:49:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-16 09:49:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-16 09:49:19 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:49:19 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-16 09:49:19 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:49:19 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2196 ms
2025-06-16 09:49:21 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:49:21 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:49:21 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:49:21 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:49:21 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:49:21 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:49:22 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-16 09:49:23 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:49:23 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:49:23 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:49:23 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:49:24 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:49:25 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6f2e1024
2025-06-16 09:49:27 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:49:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-16 09:49:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-16 09:49:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-16 09:49:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-16 09:57:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:57:49 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 36212 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:57:49 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-16 09:57:49 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-16 09:57:49 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 09:57:50 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-16 09:57:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-16 09:57:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-16 09:57:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 09:57:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-16 09:57:51 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-16 09:57:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1914 ms
2025-06-16 09:57:53 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-16 09:57:54 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:57:54 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:57:54 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:57:54 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-16 09:57:56 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:57:56 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@35f3a22c
2025-06-16 09:57:58 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:57:58 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-16 09:57:58 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-16 09:57:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-16 09:57:58 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-16 09:57:58 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-16 09:57:58 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server ************:8800 register finished
2025-06-16 09:58:00 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-16 09:58:00 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-16 09:58:00 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-16 09:58:00 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-16 09:58:00 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-16 09:58:00 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-16 09:58:00 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-16 09:58:00 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-16 09:58:00 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-16 09:58:00 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 15.177 seconds (process running for 15.929)
2025-06-16 09:58:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 09:58:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-16 09:58:00 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 09:58:01 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e333e6e
2025-06-16 09:58:01 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 09:58:01 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:58:01 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 09:58:01 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-16 09:58:01 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934430193954508800]
2025-06-16 09:58:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-16 09:58:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-16 10:04:43 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 10:04:43 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-16 10:04:43 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 10:04:43 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 10:04:43 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 10:04:43 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-16 10:04:43 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934430193954508800]
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-16 10:04:43 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-16 10:04:43 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 10:04:46 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 10:04:46 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-snailjob-server', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/snail-job/actuator, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8800, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
