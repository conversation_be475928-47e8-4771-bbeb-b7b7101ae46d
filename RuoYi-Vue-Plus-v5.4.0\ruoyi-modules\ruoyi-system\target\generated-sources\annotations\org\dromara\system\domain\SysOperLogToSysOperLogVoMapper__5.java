package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__5;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysOperLogVoToSysOperLogMapper__5.class,SysOperLogBoToSysOperLogMapper__5.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__5 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
