{"doc": " 跨域配置\n\n <AUTHOR> Li\n", "fields": [{"name": "ALLOWED_HEADERS", "doc": " 这里为支持的请求头，如果有自定义的header字段请自己添加\n"}, {"name": "ALLOWED_METHODS", "doc": " 允许的请求方法\n"}, {"name": "ALLOWED_ORIGIN", "doc": " 允许的请求来源，使用 * 表示允许任何来源\n"}, {"name": "ALLOWED_EXPOSE", "doc": " 允许前端访问的响应头，使用 * 表示允许任何响应头\n"}, {"name": "MAX_AGE", "doc": " 预检请求的缓存时间，单位为秒（此处设置为 5 小时）\n"}], "enumConstants": [], "methods": [{"name": "filter", "paramTypes": ["org.springframework.web.server.ServerWebExchange", "org.springframework.web.server.WebFilterChain"], "doc": " 实现跨域配置的 Web 过滤器\n\n @param exchange ServerWebExchange 对象，表示一次 Web 交换\n @param chain    WebFilterChain 对象，表示一组 Web 过滤器链\n @return Mono<Void> 表示异步的过滤器链处理结果\n"}], "constructors": []}