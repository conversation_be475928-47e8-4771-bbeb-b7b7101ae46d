package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__11;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysConfigVoToSysConfigMapper__11.class,SysConfigBoToSysConfigMapper__11.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__11 extends BaseMapper<SysConfig, SysConfigVo> {
}
