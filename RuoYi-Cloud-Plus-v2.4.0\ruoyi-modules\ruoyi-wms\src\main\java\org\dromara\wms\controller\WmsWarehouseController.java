package org.dromara.wms.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.dromara.wms.domain.bo.WmsWarehouseBo;
import org.dromara.wms.service.IWmsWarehouseService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.service.ISysUserConfigService;

/**
 * 仓库
 * 前端访问路由地址为:/wms/warehouse
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/warehouse")
public class WmsWarehouseController extends BaseController {

    private final IWmsWarehouseService wmsWarehouseService;
    private final ISysUserConfigService userConfigService;

    /**
     * 查询仓库列表
     */
    @SaCheckPermission("wms:warehouse:list")
    @GetMapping("/list")
    public TableDataInfo<WmsWarehouseVo> list(WmsWarehouseBo bo, PageQuery pageQuery) {
        return wmsWarehouseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出仓库列表
     */
    @SaCheckPermission("wms:warehouse:export")
    @Log(title = "仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WmsWarehouseBo bo, HttpServletResponse response) {
        List<WmsWarehouseVo> list = wmsWarehouseService.queryList(bo);
        ExcelUtil.exportExcel(list, "仓库", WmsWarehouseVo.class, response);
    }

    /**
     * 获取仓库详细信息
     *
     * @param warehouseId 主键
     */
    @SaCheckPermission("wms:warehouse:query")
    @GetMapping("/{warehouseId}")
    public R<WmsWarehouseVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("warehouseId") Long warehouseId) {
        return R.ok(wmsWarehouseService.queryById(warehouseId));
    }

    /**
     * 新增仓库
     */
    @SaCheckPermission("wms:warehouse:add")
    @Log(title = "仓库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WmsWarehouseBo bo) {
        return toAjax(wmsWarehouseService.insertByBo(bo));
    }

    /**
     * 修改仓库
     */
    @SaCheckPermission("wms:warehouse:edit")
    @Log(title = "仓库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WmsWarehouseBo bo) {
        return toAjax(wmsWarehouseService.updateByBo(bo));
    }

    /**
     * 删除仓库
     *
     * @param warehouseIds 主键串
     */
    @SaCheckPermission("wms:warehouse:remove")
    @Log(title = "仓库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("warehouseIds") Long[] warehouseIds) {
        return toAjax(wmsWarehouseService.deleteWithValidByIds(List.of(warehouseIds), true));
    }

    /**
     * 获取当前用户可访问的仓库列表
     */
    @GetMapping("/accessible")
    public R<List<WmsWarehouseVo>> getAccessibleWarehouses() {
        Long userId = LoginHelper.getUserId();
        List<WmsWarehouseVo> warehouses = wmsWarehouseService.getUserAccessibleWarehouses(userId);
        return R.ok(warehouses);
    }

    /**
     * 获取当前用户选择的仓库
     */
    @GetMapping("/current")
    public R<Long> getCurrentWarehouse() {
        Long warehouseId = userConfigService.getCurrentUserWarehouse();
        return R.ok(warehouseId);
    }

    /**
     * 设置当前用户选择的仓库
     */
    @PostMapping("/current/{warehouseId}")
    public R<Void> setCurrentWarehouse(@PathVariable Long warehouseId) {
        userConfigService.setCurrentUserWarehouse(warehouseId);
        return R.ok();
    }
}
