var r=(e,i,a)=>new Promise((s,c)=>{var u=t=>{try{o(a.next(t))}catch(n){c(n)}},d=t=>{try{o(a.throw(t))}catch(n){c(n)}},o=t=>t.done?s(t.value):Promise.resolve(t.value).then(u,d);o((a=a.apply(e,i)).next())});import{A as h,D as l}from"./bootstrap-DCMzVRvD.js";import{G as p,a as m}from"./index-C0wIoq37.js";import{S as g,a as I,b as v}from"./index-DjJOU2eu.js";import{J as T,p as f}from"../jse/index-index-C-MnMZEz.js";const G=T(()=>({loginTenantId:f(l)}));function S(e){return r(this,null,function*(){const{loginTenantId:i}=G(),a=yield h(e,i.value);window.location.href=a})}const B=[{avatar:p,description:"绑定Gitee账号",source:"gitee",title:"Gitee",style:{color:"#c71d23"}},{avatar:m,description:"绑定Github账号",source:"github",title:"Github"},{avatar:g,description:"绑定MaxKey账号",source:"maxkey",title:"MaxKey"},{avatar:I,description:"绑定topiam账号",source:"topiam",title:"Topiam"},{avatar:v,description:"绑定wechat账号",source:"wechat",title:"Wechat"}];export{B as a,S as h,G as u};
