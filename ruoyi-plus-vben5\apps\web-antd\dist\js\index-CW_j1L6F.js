import{aC as nt,bO as _,c as j,g as A,cL as rt,cN as Ae,o as F,cx as Z,bk as we,_ as v,aM as ie,j as at,l as lt,r as Ve,cU as it,h as Se,aG as ot,bQ as st,p as ut,ck as dt,aH as ct,aJ as ft,cl as Be,cm as Te,aY as pt}from"./bootstrap-DCMzVRvD.js";import{a as h,d as Ee,p as We,ac as Ge,C as D,B as H,q as ee}from"../jse/index-index-C-MnMZEz.js";import{D as mt}from"./DownOutlined-CERO2SW5.js";import{i as gt}from"./isMobile-8sZ0LT6r.js";import{g as vt,a as $e}from"./statusUtils-d85DZFMd.js";import{i as ht,g as Le,a as Ue,b as bt,e as St,d as $t,c as Nt,f as qe}from"./index-CFj2VWFk.js";var yt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"};function Fe(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(l){return Object.getOwnPropertyDescriptor(n,l).enumerable}))),a.forEach(function(l){wt(e,l,n[l])})}return e}function wt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ce=function(t,n){var a=Fe({},t,n.attrs);return h(nt,Fe({},a,{icon:yt}),null)};Ce.displayName="UpOutlined";Ce.inheritAttrs=!1;function xe(){return typeof BigInt=="function"}function te(e){let t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),t.startsWith(".")&&(t=`0${t}`);const a=t||"0",l=a.split("."),i=l[0]||"0",d=l[1]||"0";i==="0"&&d==="0"&&(n=!1);const c=n?"-":"";return{negative:n,negativeStr:c,trimStr:a,integerStr:i,decimalStr:d,fullStr:`${c}${a}`}}function Oe(e){const t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function ne(e){const t=String(e);if(Oe(e)){let n=Number(t.slice(t.indexOf("e-")+2));const a=t.match(/\.(\d+)/);return a!=null&&a[1]&&(n+=a[1].length),n}return t.includes(".")&&De(t)?t.length-t.indexOf(".")-1:0}function Pe(e){let t=String(e);if(Oe(e)){if(e>Number.MAX_SAFE_INTEGER)return String(xe()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(xe()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(ne(t))}return te(t).fullStr}function De(e){return typeof e=="number"?!Number.isNaN(e):e?/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e):!1}function ke(e){return!e&&e!==0&&!Number.isNaN(e)||!String(e).trim()}class z{constructor(t){if(this.origin="",ke(t)){this.empty=!0;return}this.origin=String(t),this.number=Number(t)}negate(){return new z(-this.toNumber())}add(t){if(this.isInvalidate())return new z(t);const n=Number(t);if(Number.isNaN(n))return this;const a=this.number+n;if(a>Number.MAX_SAFE_INTEGER)return new z(Number.MAX_SAFE_INTEGER);if(a<Number.MIN_SAFE_INTEGER)return new z(Number.MIN_SAFE_INTEGER);const l=Math.max(ne(this.number),ne(n));return new z(a.toFixed(l))}isEmpty(){return this.empty}isNaN(){return Number.isNaN(this.number)}isInvalidate(){return this.isEmpty()||this.isNaN()}equals(t){return this.toNumber()===(t==null?void 0:t.toNumber())}lessEquals(t){return this.add(t.negate().toString()).toNumber()<=0}toNumber(){return this.number}toString(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)?this.isInvalidate()?"":Pe(this.number):this.origin}}class q{constructor(t){if(this.origin="",ke(t)){this.empty=!0;return}if(this.origin=String(t),t==="-"||Number.isNaN(t)){this.nan=!0;return}let n=t;if(Oe(n)&&(n=Number(n)),n=typeof n=="string"?n:Pe(n),De(n)){const a=te(n);this.negative=a.negative;const l=a.trimStr.split(".");this.integer=BigInt(l[0]);const i=l[1]||"0";this.decimal=BigInt(i),this.decimalLen=i.length}else this.nan=!0}getMark(){return this.negative?"-":""}getIntegerStr(){return this.integer.toString()}getDecimalStr(){return this.decimal.toString().padStart(this.decimalLen,"0")}alignDecimal(t){const n=`${this.getMark()}${this.getIntegerStr()}${this.getDecimalStr().padEnd(t,"0")}`;return BigInt(n)}negate(){const t=new q(this.toString());return t.negative=!t.negative,t}add(t){if(this.isInvalidate())return new q(t);const n=new q(t);if(n.isInvalidate())return this;const a=Math.max(this.getDecimalStr().length,n.getDecimalStr().length),l=this.alignDecimal(a),i=n.alignDecimal(a),d=(l+i).toString(),{negativeStr:c,trimStr:f}=te(d),p=`${c}${f.padStart(a+1,"0")}`;return new q(`${p.slice(0,-a)}.${p.slice(-a)}`)}isEmpty(){return this.empty}isNaN(){return this.nan}isInvalidate(){return this.isEmpty()||this.isNaN()}equals(t){return this.toString()===(t==null?void 0:t.toString())}lessEquals(t){return this.add(t.negate().toString()).toNumber()<=0}toNumber(){return this.isNaN()?NaN:Number(this.toString())}toString(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)?this.isInvalidate()?"":te(`${this.getMark()}${this.getIntegerStr()}.${this.getDecimalStr()}`).fullStr:this.origin}}function P(e){return xe()?new q(e):new z(e)}function Ie(e,t,n){let a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e==="")return"";const{negativeStr:l,integerStr:i,decimalStr:d}=te(e),c=`${t}${d}`,f=`${l}${i}`;if(n>=0){const p=Number(d[n]);if(p>=5&&!a){const s=P(e).add(`${l}0.${"0".repeat(n)}${10-p}`);return Ie(s.toString(),t,n,a)}return n===0?f:`${f}${t}${d.padEnd(n,"0").slice(0,n)}`}return c===".0"?f:`${f}${c}`}const xt=200,It=600,Et=Ee({compatConfig:{MODE:3},name:"StepHandler",inheritAttrs:!1,props:{prefixCls:String,upDisabled:Boolean,downDisabled:Boolean,onStep:_()},slots:Object,setup(e,t){let{slots:n,emit:a}=t;const l=We(),i=(c,f)=>{c.preventDefault(),a("step",f);function p(){a("step",f),l.value=setTimeout(p,xt)}l.value=setTimeout(p,It)},d=()=>{clearTimeout(l.value)};return Ge(()=>{d()}),()=>{if(gt())return null;const{prefixCls:c,upDisabled:f,downDisabled:p}=e,s=`${c}-handler`,w=j(s,`${s}-up`,{[`${s}-up-disabled`]:f}),S=j(s,`${s}-down`,{[`${s}-down-disabled`]:p}),I={unselectable:"on",role:"button",onMouseup:d,onMouseleave:d},{upNode:$,downNode:E}=n;return h("div",{class:`${s}-wrap`},[h("span",A(A({},I),{},{onMousedown:M=>{i(M,!0)},"aria-label":"Increase Value","aria-disabled":f,class:w}),[($==null?void 0:$())||h("span",{unselectable:"on",class:`${c}-handler-up-inner`},null)]),h("span",A(A({},I),{},{onMousedown:M=>{i(M,!1)},"aria-label":"Decrease Value","aria-disabled":p,class:S}),[(E==null?void 0:E())||h("span",{unselectable:"on",class:`${c}-handler-down-inner`},null)])])}}});function Ct(e,t){const n=We(null);function a(){try{const{selectionStart:i,selectionEnd:d,value:c}=e.value,f=c.substring(0,i),p=c.substring(d);n.value={start:i,end:d,value:c,beforeTxt:f,afterTxt:p}}catch(i){}}function l(){if(e.value&&n.value&&t.value)try{const{value:i}=e.value,{beforeTxt:d,afterTxt:c,start:f}=n.value;let p=i.length;if(i.endsWith(c))p=i.length-n.value.afterTxt.length;else if(i.startsWith(d))p=d.length;else{const s=d[f-1],w=i.indexOf(s,f-1);w!==-1&&(p=w+1)}e.value.setSelectionRange(p,p)}catch(i){rt(!1,`Something warning of cursor restore. Please fire issue about this: ${i.message}`)}}return[a,l]}const Ot=()=>{const e=D(0),t=()=>{Ae.cancel(e.value)};return Ge(()=>{t()}),n=>{t(),e.value=Ae(()=>{n()})}};var Pt=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n};const ze=(e,t)=>e||t.isEmpty()?t.toString():t.toNumber(),je=e=>{const t=P(e);return t.isInvalidate()?null:t},Xe=()=>({stringMode:F(),defaultValue:Z([String,Number]),value:Z([String,Number]),prefixCls:we(),min:Z([String,Number]),max:Z([String,Number]),step:Z([String,Number],1),tabindex:Number,controls:F(!0),readonly:F(),disabled:F(),autofocus:F(),keyboard:F(!0),parser:_(),formatter:_(),precision:Number,decimalSeparator:String,onInput:_(),onChange:_(),onPressEnter:_(),onStep:_(),onBlur:_(),onFocus:_()}),Dt=Ee({compatConfig:{MODE:3},name:"InnerInputNumber",inheritAttrs:!1,props:v(v({},Xe()),{lazy:Boolean}),slots:Object,setup(e,t){let{attrs:n,slots:a,emit:l,expose:i}=t;const d=D(),c=D(!1),f=D(!1),p=D(!1),s=D(P(e.value));function w(r){e.value===void 0&&(s.value=r)}const S=(r,o)=>{if(!o)return e.precision>=0?e.precision:Math.max(ne(r),ne(e.step))},I=r=>{const o=String(r);if(e.parser)return e.parser(o);let u=o;return e.decimalSeparator&&(u=u.replace(e.decimalSeparator,".")),u.replace(/[^\w.-]+/g,"")},$=D(""),E=(r,o)=>{if(e.formatter)return e.formatter(r,{userTyping:o,input:String($.value)});let u=typeof r=="number"?Pe(r):r;if(!o){const m=S(u,o);if(De(u)&&(e.decimalSeparator||m>=0)){const y=e.decimalSeparator||".";u=Ie(u,y,m)}}return u},M=(()=>{const r=e.value;return s.value.isInvalidate()&&["string","number"].includes(typeof r)?Number.isNaN(r)?"":r:E(s.value.toString(),!1)})();$.value=M;function C(r,o){$.value=E(r.isInvalidate()?r.toString(!1):r.toString(!o),o)}const O=H(()=>je(e.max)),N=H(()=>je(e.min)),x=H(()=>!O.value||!s.value||s.value.isInvalidate()?!1:O.value.lessEquals(s.value)),V=H(()=>!N.value||!s.value||s.value.isInvalidate()?!1:s.value.lessEquals(N.value)),[k,X]=Ct(d,c),K=r=>O.value&&!r.lessEquals(O.value)?O.value:N.value&&!N.value.lessEquals(r)?N.value:null,oe=r=>!K(r),Y=(r,o)=>{var u;let m=r,y=oe(m)||m.isEmpty();if(!m.isEmpty()&&!o&&(m=K(m)||m,y=!0),!e.readonly&&!e.disabled&&y){const R=m.toString(),L=S(R,o);return L>=0&&(m=P(Ie(R,".",L))),m.equals(s.value)||(w(m),(u=e.onChange)===null||u===void 0||u.call(e,m.isEmpty()?null:ze(e.stringMode,m)),e.value===void 0&&C(m,o)),m}return s.value},se=Ot(),J=r=>{var o;if(k(),$.value=r,!p.value){const u=I(r),m=P(u);m.isNaN()||Y(m,!0)}(o=e.onInput)===null||o===void 0||o.call(e,r),se(()=>{let u=r;e.parser||(u=r.replace(/。/g,".")),u!==r&&J(u)})},b=()=>{p.value=!0},Q=()=>{p.value=!1,J(d.value.value)},W=r=>{J(r.target.value)},G=r=>{var o,u;if(r&&x.value||!r&&V.value)return;f.value=!1;let m=P(e.step);r||(m=m.negate());const y=(s.value||P(0)).add(m.toString()),R=Y(y,!1);(o=e.onStep)===null||o===void 0||o.call(e,ze(e.stringMode,R),{offset:e.step,type:r?"up":"down"}),(u=d.value)===null||u===void 0||u.focus()},B=r=>{const o=P(I($.value));let u=o;o.isNaN()?u=s.value:u=Y(o,r),e.value!==void 0?C(s.value,!1):u.isNaN()||C(u,!1)},ue=()=>{f.value=!0},de=r=>{var o;const{which:u}=r;f.value=!0,u===ie.ENTER&&(p.value||(f.value=!1),B(!1),(o=e.onPressEnter)===null||o===void 0||o.call(e,r)),e.keyboard!==!1&&!p.value&&[ie.UP,ie.DOWN].includes(u)&&(G(ie.UP===u),r.preventDefault())},ce=()=>{f.value=!1},re=r=>{B(!1),c.value=!1,f.value=!1,l("blur",r)};return ee(()=>e.precision,()=>{s.value.isInvalidate()||C(s.value,!1)},{flush:"post"}),ee(()=>e.value,()=>{const r=P(e.value);s.value=r;const o=P(I($.value));(!r.equals(o)||!f.value||e.formatter)&&C(r,f.value)},{flush:"post"}),ee($,()=>{e.formatter&&X()},{flush:"post"}),ee(()=>e.disabled,r=>{r&&(c.value=!1)}),i({focus:()=>{var r;(r=d.value)===null||r===void 0||r.focus()},blur:()=>{var r;(r=d.value)===null||r===void 0||r.blur()}}),()=>{const r=v(v({},n),e),{prefixCls:o="rc-input-number",min:u,max:m,step:y=1,defaultValue:R,value:L,disabled:ae,readonly:le,keyboard:g,controls:fe=!0,autofocus:T,stringMode:pe,parser:me,formatter:U,precision:ge,decimalSeparator:ve,onChange:he,onInput:Me,onPressEnter:Re,onStep:Bt,lazy:Ke,class:Ye,style:Je}=r,Qe=Pt(r,["prefixCls","min","max","step","defaultValue","value","disabled","readonly","keyboard","controls","autofocus","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","lazy","class","style"]),{upHandler:Ze,downHandler:et}=a,_e=`${o}-input`,be={};return Ke?be.onChange=W:be.onInput=W,h("div",{class:j(o,Ye,{[`${o}-focused`]:c.value,[`${o}-disabled`]:ae,[`${o}-readonly`]:le,[`${o}-not-a-number`]:s.value.isNaN(),[`${o}-out-of-range`]:!s.value.isInvalidate()&&!oe(s.value)}),style:Je,onKeydown:de,onKeyup:ce},[fe&&h(Et,{prefixCls:o,upDisabled:x.value,downDisabled:V.value,onStep:G},{upNode:Ze,downNode:et}),h("div",{class:`${_e}-wrap`},[h("input",A(A(A({autofocus:T,autocomplete:"off",role:"spinbutton","aria-valuemin":u,"aria-valuemax":m,"aria-valuenow":s.value.isInvalidate()?null:s.value.toString(),step:y},Qe),{},{ref:d,class:_e,value:$.value,disabled:ae,readonly:le,onFocus:tt=>{c.value=!0,l("focus",tt)}},be),{},{onBlur:re,onCompositionstart:b,onCompositionend:Q,onBeforeinput:ue}),null)])])}}});function Ne(e){return e!=null}const Mt=e=>{const{componentCls:t,lineWidth:n,lineType:a,colorBorder:l,borderRadius:i,fontSizeLG:d,controlHeightLG:c,controlHeightSM:f,colorError:p,inputPaddingHorizontalSM:s,colorTextDescription:w,motionDurationMid:S,colorPrimary:I,controlHeight:$,inputPaddingHorizontal:E,colorBgContainer:M,colorTextDisabled:C,borderRadiusSM:O,borderRadiusLG:N,controlWidth:x,handleVisible:V}=e;return[{[t]:v(v(v(v({},Ve(e)),Le(e)),Ue(e,t)),{display:"inline-block",width:x,margin:0,padding:0,border:`${n}px ${a} ${l}`,borderRadius:i,"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:d,borderRadius:N,[`input${t}-input`]:{height:c-2*n}},"&-sm":{padding:0,borderRadius:O,[`input${t}-input`]:{height:f-2*n,padding:`0 ${s}px`}},"&:hover":v({},qe(e)),"&-focused":v({},Nt(e)),"&-disabled":v(v({},$t(e)),{[`${t}-input`]:{cursor:"not-allowed"}}),"&-out-of-range":{input:{color:p}},"&-group":v(v(v({},Ve(e)),St(e)),{"&-wrapper":{display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:N}},"&-sm":{[`${t}-group-addon`]:{borderRadius:O}}}}),[t]:{"&-input":v(v({width:"100%",height:$-2*n,padding:`0 ${E}px`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:i,outline:0,transition:`all ${S} linear`,appearance:"textfield",color:e.colorText,fontSize:"inherit",verticalAlign:"top"},bt(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,webkitAppearance:"none",appearance:"none"}})}})},{[t]:{[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{opacity:1},[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleWidth,height:"100%",background:M,borderStartStartRadius:0,borderStartEndRadius:i,borderEndEndRadius:i,borderEndStartRadius:0,opacity:V===!0?1:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`opacity ${S} linear ${S}`,[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:w,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${n}px ${a} ${l}`,transition:`all ${S} linear`,"&:active":{background:e.colorFillAlter},"&:hover":{height:"60%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{color:I}},"&-up-inner, &-down-inner":v(v({},it()),{color:w,transition:`all ${S} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:i},[`${t}-handler-down`]:{borderBlockStart:`${n}px ${a} ${l}`,borderEndEndRadius:i},"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`
          ${t}-handler-up-disabled,
          ${t}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${t}-handler-up-disabled:hover &-handler-up-inner,
          ${t}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:C}}},{[`${t}-borderless`]:{borderColor:"transparent",boxShadow:"none",[`${t}-handler-down`]:{borderBlockStartWidth:0}}}]},Rt=e=>{const{componentCls:t,inputPaddingHorizontal:n,inputAffixPadding:a,controlWidth:l,borderRadiusLG:i,borderRadiusSM:d}=e;return{[`${t}-affix-wrapper`]:v(v(v({},Le(e)),Ue(e,`${t}-affix-wrapper`)),{position:"relative",display:"inline-flex",width:l,padding:0,paddingInlineStart:n,"&-lg":{borderRadius:i},"&-sm":{borderRadius:d},[`&:not(${t}-affix-wrapper-disabled):hover`]:v(v({},qe(e)),{zIndex:1}),"&-focused, &:focus":{zIndex:1},"&-disabled":{[`${t}[disabled]`]:{background:"transparent"}},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},[`input${t}-input`]:{padding:0},"&::before":{width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{"&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:a},"&-suffix":{position:"absolute",insetBlockStart:0,insetInlineEnd:0,zIndex:1,height:"100%",marginInlineEnd:n,marginInlineStart:a}}})}},_t=at("InputNumber",e=>{const t=ht(e);return[Mt(t),Rt(t),lt(t)]},e=>({controlWidth:90,handleWidth:e.controlHeightSM-e.lineWidth*2,handleFontSize:e.fontSize/2,handleVisible:"auto"}));var At=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n};const He=Xe(),Vt=()=>v(v({},He),{size:we(),bordered:F(!0),placeholder:String,name:String,id:String,type:String,addonBefore:Se.any,addonAfter:Se.any,prefix:Se.any,"onUpdate:value":He.onChange,valueModifiers:Object,status:we()}),ye=Ee({compatConfig:{MODE:3},name:"AInputNumber",inheritAttrs:!1,props:Vt(),slots:Object,setup(e,t){let{emit:n,expose:a,attrs:l,slots:i}=t;var d;const c=ot(),f=st.useInject(),p=H(()=>vt(f.status,e.status)),{prefixCls:s,size:w,direction:S,disabled:I}=ut("input-number",e),{compactSize:$,compactItemClassnames:E}=dt(s,S),M=ct(),C=H(()=>{var b;return(b=I.value)!==null&&b!==void 0?b:M.value}),[O,N]=_t(s),x=H(()=>$.value||w.value),V=D((d=e.value)!==null&&d!==void 0?d:e.defaultValue),k=D(!1);ee(()=>e.value,()=>{V.value=e.value});const X=D(null),K=()=>{var b;(b=X.value)===null||b===void 0||b.focus()};a({focus:K,blur:()=>{var b;(b=X.value)===null||b===void 0||b.blur()}});const Y=b=>{e.value===void 0&&(V.value=b),n("update:value",b),n("change",b),c.onFieldChange()},se=b=>{k.value=!1,n("blur",b),c.onFieldBlur()},J=b=>{k.value=!0,n("focus",b)};return()=>{var b,Q,W,G;const{hasFeedback:B,isFormItemInput:ue,feedbackIcon:de}=f,ce=(b=e.id)!==null&&b!==void 0?b:c.id.value,re=v(v(v({},l),e),{id:ce,disabled:C.value}),{class:r,bordered:o,readonly:u,style:m,addonBefore:y=(Q=i.addonBefore)===null||Q===void 0?void 0:Q.call(i),addonAfter:R=(W=i.addonAfter)===null||W===void 0?void 0:W.call(i),prefix:L=(G=i.prefix)===null||G===void 0?void 0:G.call(i),valueModifiers:ae={}}=re,le=At(re,["class","bordered","readonly","style","addonBefore","addonAfter","prefix","valueModifiers"]),g=s.value,fe=j({[`${g}-lg`]:x.value==="large",[`${g}-sm`]:x.value==="small",[`${g}-rtl`]:S.value==="rtl",[`${g}-readonly`]:u,[`${g}-borderless`]:!o,[`${g}-in-form-item`]:ue},$e(g,p.value),r,E.value,N.value);let T=h(Dt,A(A({},ft(le,["size","defaultValue"])),{},{ref:X,lazy:!!ae.lazy,value:V.value,class:fe,prefixCls:g,readonly:u,onChange:Y,onBlur:se,onFocus:J}),{upHandler:i.upIcon?()=>h("span",{class:`${g}-handler-up-inner`},[i.upIcon()]):()=>h(Ce,{class:`${g}-handler-up-inner`},null),downHandler:i.downIcon?()=>h("span",{class:`${g}-handler-down-inner`},[i.downIcon()]):()=>h(mt,{class:`${g}-handler-down-inner`},null)});const pe=Ne(y)||Ne(R),me=Ne(L);if(me||B){const U=j(`${g}-affix-wrapper`,$e(`${g}-affix-wrapper`,p.value,B),{[`${g}-affix-wrapper-focused`]:k.value,[`${g}-affix-wrapper-disabled`]:C.value,[`${g}-affix-wrapper-sm`]:x.value==="small",[`${g}-affix-wrapper-lg`]:x.value==="large",[`${g}-affix-wrapper-rtl`]:S.value==="rtl",[`${g}-affix-wrapper-readonly`]:u,[`${g}-affix-wrapper-borderless`]:!o,[`${r}`]:!pe&&r},N.value);T=h("div",{class:U,style:m,onClick:K},[me&&h("span",{class:`${g}-prefix`},[L]),T,B&&h("span",{class:`${g}-suffix`},[de])])}if(pe){const U=`${g}-group`,ge=`${U}-addon`,ve=y?h("div",{class:ge},[y]):null,he=R?h("div",{class:ge},[R]):null,Me=j(`${g}-wrapper`,U,{[`${U}-rtl`]:S.value==="rtl"},N.value),Re=j(`${g}-group-wrapper`,{[`${g}-group-wrapper-sm`]:x.value==="small",[`${g}-group-wrapper-lg`]:x.value==="large",[`${g}-group-wrapper-rtl`]:S.value==="rtl"},$e(`${s}-group-wrapper`,p.value,B),r,N.value);T=h("div",{class:Re,style:m},[h("div",{class:Me},[ve&&h(Be,null,{default:()=>[h(Te,null,{default:()=>[ve]})]}),T,he&&h(Be,null,{default:()=>[h(Te,null,{default:()=>[he]})]})])])}return O(pt(T,{style:m}))}}}),Gt=v(ye,{install:e=>(e.component(ye.name,ye),e)});export{Gt as default,Vt as inputNumberProps};
