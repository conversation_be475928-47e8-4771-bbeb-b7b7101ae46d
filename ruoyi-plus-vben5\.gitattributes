# https://docs.github.com/cn/get-started/getting-started-with-git/configuring-git-to-handle-line-endings

# Automatically normalize line endings (to LF) for all text-based files.
* text=auto eol=lf

# Declare files that will always have CRLF line endings on checkout.
*.{cmd,[cC][mM][dD]} text eol=crlf
*.{bat,[bB][aA][tT]} text eol=crlf

# Denote all files that are truly binary and should not be modified.
*.{ico,png,jpg,jpeg,gif,webp,svg,woff,woff2} binary