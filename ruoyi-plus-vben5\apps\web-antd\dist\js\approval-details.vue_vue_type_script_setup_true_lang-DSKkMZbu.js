import{ax as o}from"./bootstrap-DCMzVRvD.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{_ as t}from"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import{S as i}from"./index-BLwHKR_M.js";import l from"./index-i2_yEmR1.js";import{d as m,c as n,o as p,T as s,a,j as f,H as d,b as r}from"../jse/index-index-C-MnMZEz.js";const c=["src"],$=m({name:"ApprovalDetails",inheritAttrs:!1,__name:"approval-details",props:{currentFlowInfo:{},iframeHeight:{},iframeLoaded:{type:<PERSON>olean},task:{}},setup(u){return(e,h)=>(p(),n("div",null,[s(f("iframe",{src:`${e.task.formPath}/iframe?readonly=true&id=${e.task.businessId}`,style:d({height:`${e.iframeHeight}px`}),class:"w-full"},null,12,c),[[o,e.iframeLoaded]]),s(a(r(i),{paragraph:{rows:6},active:""},null,512),[[o,!e.iframeLoaded]]),a(r(l)),a(r(t),{list:e.currentFlowInfo.list},null,8,["list"])]))}});export{$ as _};
