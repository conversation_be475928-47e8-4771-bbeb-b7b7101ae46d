package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__11;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysRoleVoToSysRoleMapper__11.class,SysRoleToSysRoleVoMapper__11.class,SysUserToSysUserVoMapper__11.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__11 extends BaseMapper<SysUserVo, SysUser> {
}
