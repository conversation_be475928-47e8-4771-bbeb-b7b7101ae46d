import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-ant-design@1.2.7/node_modules/@iconify/icons-ant-design/inbox-outlined.js
var require_inbox_outlined = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-ant-design@1.2.7/node_modules/@iconify/icons-ant-design/inbox-outlined.js"(exports) {
    var data = {
      "width": 1024,
      "height": 1024,
      "body": '<path fill="currentColor" d="m885.2 446.3l-.2-.8l-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7l-.2.8c-1.3 4.9-1.7 9.9-1 14.8c-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0 0 60.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7c.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1c-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8c33.7 0 65-9.4 90.3-27.2c22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_inbox_outlined();
//# sourceMappingURL=@iconify_icons-ant-design_inbox-outlined.js.map
