{"groups": [{"name": "xss", "type": "org.dromara.common.web.config.properties.XssProperties", "sourceType": "org.dromara.common.web.config.properties.XssProperties"}], "properties": [{"name": "xss.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Xss开关", "sourceType": "org.dromara.common.web.config.properties.XssProperties"}, {"name": "xss.exclude-urls", "type": "java.util.List<java.lang.String>", "description": "排除路径", "sourceType": "org.dromara.common.web.config.properties.XssProperties"}], "hints": []}