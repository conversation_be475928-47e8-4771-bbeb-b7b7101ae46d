var M=Object.defineProperty;var y=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var D=(r,l,e)=>l in r?M(r,l,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[l]=e,N=(r,l)=>{for(var e in l||(l={}))j.call(l,e)&&D(r,e,l[e]);if(y)for(var e of y(l))E.call(l,e)&&D(r,e,l[e]);return r};var u=(r,l,e)=>new Promise((v,w)=>{var _=d=>{try{x(e.next(d))}catch(b){w(b)}},p=d=>{try{x(e.throw(d))}catch(b){w(b)}},x=d=>d.done?v(d.value):Promise.resolve(d.value).then(_,p);x((e=e.apply(r,l)).next())});import"./vxe-table-DzEj5Fop.js";import{c as F}from"./index-ocPq22VW.js";import{_ as $}from"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import{u as I}from"./use-vxe-grid-BC7vZzEr.js";import{_ as z}from"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import{d as G,p as q,l as Q,h as W,o as H,b as g,w as h,j as a,a as f,n as S,t as k,k as B}from"../jse/index-index-C-MnMZEz.js";import{u as J}from"./use-modal-CeMSCP2m.js";/* empty css                                                          */import{z as K}from"./bootstrap-DCMzVRvD.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./index-BLwHKR_M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-BxBCzu2M.js";import"./index-CHpIOV4R.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./x-Bfkqqjgb.js";const X={class:"flex min-h-[600px]"},Y={class:"h-[600px] w-[450px]"},Z={class:"flex items-center gap-2"},ee={class:"flex flex-col items-baseline text-[12px]"},te={class:"opacity-50"},oe={class:"flex h-[600px] flex-col"},se={class:"flex w-full px-4"},ie={class:"flex w-full items-center justify-between"},ae={class:"flex items-center gap-2 overflow-hidden"},le={class:"flex flex-col items-baseline text-[12px]"},re={class:"overflow-ellipsis whitespace-nowrap"},ne={class:"opacity-50"},de=G({name:"UserSelectModal",inheritAttrs:!1,__name:"user-select-modal",props:{allowUserIds:{default:""},mode:{default:"multiple"}},emits:["cancel","finish"],setup(r,{emit:l}){const e=r,v=l,[w,_]=J({title:"选择人员",class:"w-[1060px]",fullscreenButton:!1,onClosed:()=>v("cancel"),onConfirm:U,onOpened(){return u(this,null,function*(){const{userList:t=[]}=_.getData();e.mode==="multiple"&&(yield s.grid.setCheckboxRow(t,!0),yield n.grid.loadData(t))})}}),p=q([]),x={schema:[{component:"Input",fieldName:"userName",label:"用户账号",hideLabel:!0,componentProps:{placeholder:"请输入账号"}}],commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},wrapperClass:"grid-cols-2",handleReset:()=>u(null,null,function*(){p.value=[];const{formApi:t,reload:o}=s;yield t.resetForm();const c=t.form.values;t.setLatestSubmissionValues(c),yield o(c)})},d={checkboxConfig:{reserve:!0,trigger:"row"},radioConfig:{trigger:"row",strict:!0},columns:[{type:e.mode==="single"?"radio":"checkbox",width:60,resizable:!1},{field:"userName",title:"用户",headerAlign:"left",resizable:!1,slots:{default:"user"}}],height:"auto",keepSource:!0,pagerConfig:{layouts:["PrevPage","Number","NextPage","Sizes","Total"]},proxyConfig:{ajax:{query:(c,...i)=>u(null,[c,...i],function*({page:t},o={}){if(p.value.length===1?o.deptId=p.value[0]:Reflect.deleteProperty(o,"deptId"),e.mode==="multiple"){const C=n.grid.getData();yield s.grid.setCheckboxRow(C,!0)}if(e.mode==="single"){const C=n.grid.getData();C.length===1&&(yield s.grid.setRadioRow(C[0]))}const m=N({pageNum:t.currentPage,pageSize:t.pageSize},o);return e.allowUserIds&&(m.userIds=e.allowUserIds),yield F(m)})}},rowConfig:{keyField:"userId"},toolbarConfig:{enabled:!1},showOverflow:!1},[b,s]=I({formOptions:x,gridOptions:d,gridEvents:{checkboxChange:R,checkboxAll:R,radioChange:O}});function R(){if(e.mode!=="multiple")return;const t=s.grid.getCheckboxRecords(),o=s.grid.getCheckboxReserveRecords(),c=[...t,...o];n.grid.loadData(c)}function O(){if(e.mode!=="single")return;const t=s.grid.getRadioRecord();n.grid.loadData([t])}const P={checkboxConfig:{},columns:[{field:"nickName",title:"昵称",width:200,resizable:!1,slots:{default:"user"}},{field:"action",title:"操作",width:120,slots:{default:"action"}}],height:"auto",keepSource:!0,pagerConfig:{enabled:!1},proxyConfig:{enabled:!1},rowConfig:{keyField:"userId"},toolbarConfig:{enabled:!1},showOverflow:!1},[A,n]=I({gridOptions:P});function V(t){return u(this,null,function*(){e.mode==="multiple"&&(yield s.grid.setCheckboxRow(t,!1)),e.mode==="single"&&(yield s.grid.clearRadioRow());const o=n.grid.getData();yield n.grid.loadData(o.filter(c=>c!==t))})}function L(){e.mode==="multiple"&&(s.grid.clearCheckboxRow(),s.grid.clearCheckboxReserve()),e.mode==="single"&&s.grid.clearRadioRow(),n.grid.loadData([])}function T(){return u(this,null,function*(){yield s.reload();const t=n.grid.getData();e.mode==="multiple"&&(s==null||s.grid.setCheckboxRow(t,!0)),e.mode==="single"&&t.length===1&&s.grid.setRadioRow(t[0])})}function U(){const t=n.grid.getData();console.log(t),v("finish",t),_.close()}return(t,o)=>{const c=Q("a-button");return H(),W(g(w),null,{default:h(()=>[a("div",X,[f($,{"select-dept-id":p.value,"onUpdate:selectDeptId":o[0]||(o[0]=i=>p.value=i),"show-search":!1,class:"w-[230px]",onReload:o[1]||(o[1]=()=>g(s).reload()),onSelect:T},null,8,["select-dept-id"]),a("div",Y,[f(g(b),null,{user:h(({row:i})=>{var m;return[a("div",Z,[f(g(z),{alt:i.nickName,src:(m=i.avatar)!=null?m:"",class:S([{"bg-primary":!i.avatar},"size-[32px] rounded-full text-white"])},null,8,["alt","src","class"]),a("div",ee,[a("div",null,k(i.nickName),1),a("div",te,k(i.phonenumber||"暂无手机号"),1)])])]}),_:1})]),a("div",oe,[a("div",se,[a("div",ie,[o[3]||(o[3]=a("div",null,"已选中人员",-1)),a("div",null,[f(c,{size:"small",onClick:L},{default:h(()=>o[2]||(o[2]=[B(" 清空选中 ")])),_:1,__:[2]})])])]),f(g(A),{id:"user-select-right-table"},{user:h(({row:i})=>{var m;return[a("div",ae,[f(g(z),{alt:i.nickName,src:(m=i.avatar)!=null?m:"",class:S([{"bg-primary":!i.avatar},"size-[32px] rounded-full text-white"])},null,8,["alt","src","class"]),a("div",le,[a("div",re,k(i.nickName),1),a("div",ne,k(i.phonenumber||"暂无手机号"),1)])])]}),action:h(({row:i})=>[f(c,{size:"small",onClick:m=>V(i)},{default:h(()=>o[4]||(o[4]=[B(" 移除 ")])),_:2,__:[4]},1032,["onClick"])]),_:1})])])]),_:1})}}}),je=K(de,[["__scopeId","data-v-3c715be1"]]);export{je as default};
