package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__11;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysDeptBoToSysDeptMapper__11.class,SysDeptVoToSysDeptMapper__11.class,SysDeptBoToSysDeptMapper__11.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__11 extends BaseMapper<SysDept, SysDeptVo> {
}
