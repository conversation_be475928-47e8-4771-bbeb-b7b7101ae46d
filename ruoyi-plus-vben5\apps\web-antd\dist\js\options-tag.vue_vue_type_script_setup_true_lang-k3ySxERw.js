import{T as n}from"./index-B6iusSRX.js";import{d as c,B as r,h as l,c as p,o as t,w as i,k as u,t as m,b as _}from"../jse/index-index-C-MnMZEz.js";const d={key:1},g=c({name:"OptionsTag",__name:"options-tag",props:{options:{},value:{}},setup(s){const e=s,o=r(()=>e.options.find(a=>a.value===e.value));return(a,f)=>o.value?(t(),l(_(n),{key:0,color:o.value.color},{default:i(()=>[u(m(o.value.label),1)]),_:1},8,["color"])):(t(),p("span",d,"未知"))}});export{g as _};
