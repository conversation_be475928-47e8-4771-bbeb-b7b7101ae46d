{"flowCode": "leave1", "flowName": "请假申请-普通", "category": "100", "version": "1", "formCustom": "N", "formPath": "/workflow/leaveEdit/index", "nodeList": [{"nodeType": 0, "nodeCode": "d5ee3ddf-3968-4379-a86f-9<PERSON>bde5faac", "nodeName": "开始", "nodeRatio": 0.0, "coordinate": "200,200|200,200", "formCustom": "N", "ext": "[]", "skipList": [{"nowNodeCode": "d5ee3ddf-3968-4379-a86f-9<PERSON>bde5faac", "nextNodeCode": "dd515cdd-59f6-446f-94ca-25ca062afb42", "skipType": "PASS", "coordinate": "220,200;310,200"}]}, {"nodeType": 1, "nodeCode": "dd515cdd-59f6-446f-94ca-25ca062afb42", "nodeName": "申请人", "nodeRatio": 0.0, "coordinate": "360,200|360,200", "formCustom": "N", "ext": "[]", "skipList": [{"nowNodeCode": "dd515cdd-59f6-446f-94ca-25ca062afb42", "nextNodeCode": "78fa8e5b-e809-44ed-978a-41092409ebcf", "skipType": "PASS", "coordinate": "410,200;490,200"}]}, {"nodeType": 1, "nodeCode": "78fa8e5b-e809-44ed-978a-41092409ebcf", "nodeName": "组长", "permissionFlag": "role:1", "nodeRatio": 0.0, "coordinate": "540,200|540,200", "formCustom": "N", "ext": "[{\"code\":\"ButtonPermissionEnum\",\"value\":\"back,termination\"}]", "skipList": [{"nowNodeCode": "78fa8e5b-e809-44ed-978a-41092409ebcf", "nextNodeCode": "a8abf15f-b83e-428a-86cc-033555ea9bbe", "skipType": "PASS", "coordinate": "590,200;670,200"}]}, {"nodeType": 1, "nodeCode": "a8abf15f-b83e-428a-86cc-033555ea9bbe", "nodeName": "部门主管", "permissionFlag": "role:3@@role:4", "nodeRatio": 0.0, "coordinate": "720,200|720,200", "formCustom": "N", "ext": "[{\"code\":\"ButtonPermissionEnum\",\"value\":\"back,termination\"}]", "skipList": [{"nowNodeCode": "a8abf15f-b83e-428a-86cc-033555ea9bbe", "nextNodeCode": "8b82b7d7-8660-455e-b880-d6d22ea3eb6d", "skipType": "PASS", "coordinate": "770,200;880,200"}]}, {"nodeType": 2, "nodeCode": "8b82b7d7-8660-455e-b880-d6d22ea3eb6d", "nodeName": "结束", "nodeRatio": 0.0, "coordinate": "900,200|900,200", "formCustom": "N", "ext": "[]"}]}