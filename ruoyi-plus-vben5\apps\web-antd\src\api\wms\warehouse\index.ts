import type { WarehouseVO, WarehouseForm, WarehouseQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询仓库列表列表
* @param params
* @returns 仓库列表列表
*/
export function warehouseList(params?: WarehouseQuery) {
  return requestClient.get<PageResult<WarehouseVO>>('/wms/warehouse/list', { params });
}

/**
 * 导出仓库列表列表
 * @param params
 * @returns 仓库列表列表
 */
export function warehouseExport(params?: WarehouseQuery) {
  return commonExport('/wms/warehouse/export', params ?? {});
}

/**
 * 查询仓库列表详情
 * @param warehouseId id
 * @returns 仓库列表详情
 */
export function warehouseInfo(warehouseId: ID) {
  return requestClient.get<WarehouseVO>(`/wms/warehouse/${warehouseId}`);
}

/**
 * 新增仓库列表
 * @param data
 * @returns void
 */
export function warehouseAdd(data: WarehouseForm) {
  return requestClient.postWithMsg<void>('/wms/warehouse', data);
}

/**
 * 更新仓库列表
 * @param data
 * @returns void
 */
export function warehouseUpdate(data: WarehouseForm) {
  return requestClient.putWithMsg<void>('/wms/warehouse', data);
}

/**
 * 删除仓库列表
 * @param warehouseId id
 * @returns void
 */
export function warehouseRemove(warehouseId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/wms/warehouse/${warehouseId}`);
}

/**
 * 获取当前用户可访问的仓库列表
 */
export function getAccessibleWarehouses() {
  return requestClient.get<WarehouseVO[]>('/wms/warehouse/accessible');
}

/**
 * 获取当前用户选择的仓库
 */
export function getCurrentWarehouse() {
  return requestClient.get<number>('/wms/warehouse/current');
}

/**
 * 设置当前用户选择的仓库
 */
export function setCurrentWarehouse(warehouseId: number) {
  return requestClient.post(`/wms/warehouse/current/${warehouseId}`);
}
