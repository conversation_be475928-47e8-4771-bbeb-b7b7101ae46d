{"doc": " 无符号计算生成器\n\n <AUTHOR> Li\n", "fields": [{"name": "numberLength", "doc": " 参与计算数字最大长度\n"}], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 获取验证码长度\n\n @return 验证码长度\n"}, {"name": "getLimit", "paramTypes": [], "doc": " 根据长度获取参与计算数字最大值\n\n @return 最大值\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " 构造\n"}, {"name": "<init>", "paramTypes": ["int"], "doc": " 构造\n\n @param numberLength 参与计算最大数字位数\n"}]}