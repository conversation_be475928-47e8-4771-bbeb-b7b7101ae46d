{"doc": " 正则字段校验器\n 主要验证字段非空、是否为满足指定格式等\n\n <AUTHOR>\n", "fields": [{"name": "DICTIONARY_TYPE", "doc": " 字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）\n"}, {"name": "ID_CARD_LAST_6", "doc": " 身份证号码（后6位）\n"}, {"name": "QQ_NUMBER", "doc": " QQ号码\n"}, {"name": "POSTAL_CODE", "doc": " 邮政编码\n"}, {"name": "ACCOUNT", "doc": " 注册账号\n"}, {"name": "PASSWORD", "doc": " 密码：包含至少8个字符，包括大写字母、小写字母、数字和特殊字符\n"}, {"name": "STATUS", "doc": " 通用状态（0表示正常，1表示停用）\n"}], "enumConstants": [], "methods": [{"name": "isAccount", "paramTypes": ["java.lang.CharSequence"], "doc": " 检查输入的账号是否匹配预定义的规则\n\n @param value 要验证的账号\n @return 如果账号符合规则，返回 true；否则，返回 false。\n"}, {"name": "validateAccount", "paramTypes": ["java.lang.CharSequence", "java.lang.String"], "doc": " 验证输入的账号是否符合规则，如果不符合，则抛出 ValidateException 异常\n\n @param value    要验证的账号\n @param errorMsg 验证失败时抛出的异常消息\n @param <T>      CharSequence 的子类型\n @return 如果验证通过，返回输入的账号\n @throws ValidateException 如果验证失败\n"}, {"name": "isStatus", "paramTypes": ["java.lang.CharSequence"], "doc": " 检查输入的状态是否匹配预定义的规则\n\n @param value 要验证的状态\n @return 如果状态符合规则，返回 true；否则，返回 false。\n"}, {"name": "validateStatus", "paramTypes": ["java.lang.CharSequence", "java.lang.String"], "doc": " 验证输入的状态是否符合规则，如果不符合，则抛出 ValidateException 异常\n\n @param value    要验证的状态\n @param errorMsg 验证失败时抛出的异常消息\n @param <T>      CharSequence 的子类型\n @return 如果验证通过，返回输入的状态\n @throws ValidateException 如果验证失败\n"}], "constructors": []}