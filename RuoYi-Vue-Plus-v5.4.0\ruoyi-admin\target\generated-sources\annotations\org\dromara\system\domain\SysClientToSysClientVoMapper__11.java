package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__11;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysClientVoToSysClientMapper__11.class,SysClientBoToSysClientMapper__11.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__11 extends BaseMapper<SysClient, SysClientVo> {
}
