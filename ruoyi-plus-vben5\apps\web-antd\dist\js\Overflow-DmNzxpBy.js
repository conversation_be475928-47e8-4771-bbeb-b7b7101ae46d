import{h as P,g as u,c as q,_ as D}from"./bootstrap-DCMzVRvD.js";import{R as ee}from"./index-CHpIOV4R.js";import{d as k,a5 as ge,B as c,a4 as pe,p as be,y as he,a as v,C as x,q as Oe}from"../jse/index-index-C-MnMZEz.js";const te=Symbol("OverflowContextProviderKey"),X=k({compatConfig:{MODE:3},name:"OverflowContextProvider",inheritAttrs:!1,props:{value:{type:Object}},setup(e,o){let{slots:a}=o;return pe(te,c(()=>e.value)),()=>{var t;return(t=a.default)===null||t===void 0?void 0:t.call(a)}}}),Se=()=>ge(te,c(()=>null));var Ce=function(e,o){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(a[t[n]]=e[t[n]]);return a};const _=void 0,$=k({compatConfig:{MODE:3},name:"Item",props:{prefixCls:String,item:P.any,renderItem:Function,responsive:Boolean,itemKey:{type:[String,Number]},registerSize:Function,display:Boolean,order:Number,component:P.any,invalidate:Boolean},setup(e,o){let{slots:a,expose:t}=o;const n=c(()=>e.responsive&&!e.display),p=be();t({itemNodeRef:p});function b(i){e.registerSize(e.itemKey,i)}return he(()=>{b(null)}),()=>{var i;const{prefixCls:O,invalidate:w,item:m,renderItem:y,responsive:f,registerSize:z,itemKey:R,display:V,order:I,component:M="div"}=e,S=Ce(e,["prefixCls","invalidate","item","renderItem","responsive","registerSize","itemKey","display","order","component"]),W=(i=a.default)===null||i===void 0?void 0:i.call(a),T=y&&m!==_?y(m):W;let h;w||(h={opacity:n.value?0:1,height:n.value?0:_,overflowY:n.value?"hidden":_,order:f?I:_,pointerEvents:n.value?"none":_,position:n.value?"absolute":_});const N={};return n.value&&(N["aria-hidden"]=!0),v(ee,{disabled:!f,onResize:E=>{let{offsetWidth:B}=E;b(B)}},{default:()=>v(M,u(u(u({class:q(!w&&O),style:h},N),S),{},{ref:p}),{default:()=>[T]})})}}});var U=function(e,o){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(a[t[n]]=e[t[n]]);return a};const xe=k({compatConfig:{MODE:3},name:"RawItem",inheritAttrs:!1,props:{component:P.any,title:P.any,id:String,onMouseenter:{type:Function},onMouseleave:{type:Function},onClick:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},role:String,tabindex:Number},setup(e,o){let{slots:a,attrs:t}=o;const n=Se();return()=>{var p;if(!n.value){const{component:y="div"}=e,f=U(e,["component"]);return v(y,u(u({},f),t),{default:()=>[(p=a.default)===null||p===void 0?void 0:p.call(a)]})}const b=n.value,{className:i}=b,O=U(b,["className"]),{class:w}=t,m=U(t,["class"]);return v(X,{value:null},{default:()=>[v($,u(u(u({class:q(i,w)},O),m),e),a)]})}}});var we=function(e,o){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(a[t[n]]=e[t[n]]);return a};const ne="responsive",le="invalidate";function Re(e){return`+ ${e.length} ...`}const Ie=()=>({id:String,prefixCls:String,data:Array,itemKey:[String,Number,Function],itemWidth:{type:Number,default:10},renderItem:Function,renderRawItem:Function,maxCount:[Number,String],renderRest:Function,renderRawRest:Function,suffix:P.any,component:String,itemComponent:P.any,onVisibleChange:Function,ssr:String,onMousedown:Function,role:String}),H=k({name:"Overflow",inheritAttrs:!1,props:Ie(),emits:["visibleChange"],setup(e,o){let{attrs:a,emit:t,slots:n}=o;const p=c(()=>e.ssr==="full"),b=x(null),i=c(()=>b.value||0),O=x(new Map),w=x(0),m=x(0),y=x(0),f=x(null),z=x(null),R=c(()=>z.value===null&&p.value?Number.MAX_SAFE_INTEGER:z.value||0),V=x(!1),I=c(()=>`${e.prefixCls}-item`),M=c(()=>Math.max(w.value,m.value)),S=c(()=>!!(e.data.length&&e.maxCount===ne)),W=c(()=>e.maxCount===le),T=c(()=>S.value||typeof e.maxCount=="number"&&e.data.length>e.maxCount),h=c(()=>{let l=e.data;return S.value?b.value===null&&p.value?l=e.data:l=e.data.slice(0,Math.min(e.data.length,i.value/e.itemWidth)):typeof e.maxCount=="number"&&(l=e.data.slice(0,e.maxCount)),l}),N=c(()=>S.value?e.data.slice(R.value+1):e.data.slice(h.value.length)),E=(l,r)=>{var s;return typeof e.itemKey=="function"?e.itemKey(l):(s=e.itemKey&&(l==null?void 0:l[e.itemKey]))!==null&&s!==void 0?s:r},B=c(()=>e.renderItem||(l=>l)),A=(l,r)=>{z.value=l,r||(V.value=l<e.data.length-1,t("visibleChange",l))},ae=(l,r)=>{b.value=r.clientWidth},Y=(l,r)=>{const s=new Map(O.value);r===null?s.delete(l):s.set(l,r),O.value=s},re=(l,r)=>{w.value=m.value,m.value=r},oe=(l,r)=>{y.value=r},G=l=>O.value.get(E(h.value[l],l));return Oe([i,O,m,y,()=>e.itemKey,h],()=>{if(i.value&&M.value&&h.value){let l=y.value;const r=h.value.length,s=r-1;if(!r){A(0),f.value=null;return}for(let g=0;g<r;g+=1){const F=G(g);if(F===void 0){A(g-1,!0);break}if(l+=F,s===0&&l<=i.value||g===s-1&&l+G(s)<=i.value){A(s),f.value=null;break}else if(l+M.value>i.value){A(g-1),f.value=l-F-y.value+m.value;break}}e.suffix&&G(0)+y.value>i.value&&(f.value=null)}}),()=>{const l=V.value&&!!N.value.length,{itemComponent:r,renderRawItem:s,renderRawRest:g,renderRest:F,prefixCls:ie="rc-overflow",suffix:J,component:se="div",id:ue,onMousedown:de}=e,{class:ce,style:ve}=a,fe=we(a,["class","style"]);let Q={};f.value!==null&&S.value&&(Q={position:"absolute",left:`${f.value}px`,top:0});const j={prefixCls:I.value,responsive:S.value,component:r,invalidate:W.value},me=s?(d,C)=>{const K=E(d,C);return v(X,{key:K,value:D(D({},j),{order:C,item:d,itemKey:K,registerSize:Y,display:C<=R.value})},{default:()=>[s(d,C)]})}:(d,C)=>{const K=E(d,C);return v($,u(u({},j),{},{order:C,key:K,item:d,renderItem:B.value,itemKey:K,registerSize:Y,display:C<=R.value}),null)};let L=()=>null;const Z={order:l?R.value:Number.MAX_SAFE_INTEGER,className:`${I.value} ${I.value}-rest`,registerSize:re,display:l};if(g)g&&(L=()=>v(X,{value:D(D({},j),Z)},{default:()=>[g(N.value)]}));else{const d=F||Re;L=()=>v($,u(u({},j),Z),{default:()=>typeof d=="function"?d(N.value):d})}const ye=()=>{var d;return v(se,u({id:ue,class:q(!W.value&&ie,ce),style:ve,onMousedown:de,role:e.role},fe),{default:()=>[h.value.map(me),T.value?L():null,J&&v($,u(u({},j),{},{order:R.value,class:`${I.value}-suffix`,registerSize:oe,display:!0,style:Q}),{default:()=>J}),(d=n.default)===null||d===void 0?void 0:d.call(n)]})};return v(ee,{disabled:!S.value,onResize:ae},{default:ye})}}});H.Item=xe;H.RESPONSIVE=ne;H.INVALIDATE=le;export{H as O};
