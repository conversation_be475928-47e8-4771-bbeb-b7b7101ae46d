package org.dromara.system.domain.convert;

import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T10:28:19+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class SysUserVoConvertImpl implements SysUserVoConvert {

    @Override
    public RemoteUserVo convert(SysUserVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteUserVo remoteUserVo = new RemoteUserVo();

        remoteUserVo.setUserId( arg0.getUserId() );
        remoteUserVo.setDeptId( arg0.getDeptId() );
        remoteUserVo.setUserName( arg0.getUserName() );
        remoteUserVo.setNickName( arg0.getNickName() );
        remoteUserVo.setUserType( arg0.getUserType() );
        remoteUserVo.setEmail( arg0.getEmail() );
        remoteUserVo.setPhonenumber( arg0.getPhonenumber() );
        remoteUserVo.setSex( arg0.getSex() );
        remoteUserVo.setStatus( arg0.getStatus() );
        remoteUserVo.setCreateTime( arg0.getCreateTime() );

        return remoteUserVo;
    }

    @Override
    public RemoteUserVo convert(SysUserVo arg0, RemoteUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setUserType( arg0.getUserType() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setPhonenumber( arg0.getPhonenumber() );
        arg1.setSex( arg0.getSex() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
