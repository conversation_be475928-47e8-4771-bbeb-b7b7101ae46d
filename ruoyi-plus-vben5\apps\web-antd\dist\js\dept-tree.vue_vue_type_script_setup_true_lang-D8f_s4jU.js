var g=(f,y,o)=>new Promise((i,t)=>{var u=s=>{try{l(o.next(s))}catch(e){t(e)}},c=s=>{try{l(o.throw(s))}catch(e){t(e)}},l=s=>s.done?i(s.value):Promise.resolve(s.value).then(u,c);l((o=o.apply(f,y)).next())});import{g as D}from"./index-ocPq22VW.js";import{S as T}from"./index-BLwHKR_M.js";import{a as E}from"./Search-ClCped_G.js";import"./index-BxBCzu2M.js";import{S as M}from"./SyncOutlined-GoH9kFJY.js";import{T as B}from"./index-DdFaZtFR.js";import{au as _}from"./bootstrap-DCMzVRvD.js";import{d as C,m as S,u as w,p as x,v as $,l as b,c as p,o as n,a as m,b as d,w as v,j as k,f as N,h as O,O as A,k as I,t as h,n as z}from"../jse/index-index-C-MnMZEz.js";const P={class:"bg-background flex h-full flex-col overflow-y-auto rounded-lg"},L={key:0,class:"bg-background z-100 sticky left-0 top-0 p-[8px]"},R={class:"h-full overflow-x-hidden px-[8px]"},U={key:0},j={style:{color:"#f50"}},q={key:1},G={key:1,class:"mt-5"},ee=C({inheritAttrs:!1,__name:"dept-tree",props:S({showSearch:{type:Boolean,default:!0}},{selectDeptId:{required:!0,type:Array},selectDeptIdModifiers:{},searchValue:{type:String,default:""},searchValueModifiers:{}}),emits:S(["reload","select"],["update:selectDeptId","update:searchValue"]),setup(f,{emit:y}){const o=y,i=w(f,"selectDeptId"),t=w(f,"searchValue"),u=x([]),c=x(!0);function l(){return g(this,null,function*(){c.value=!0,t.value="",i.value=[];const e=yield D();u.value=e,c.value=!1})}function s(){return g(this,null,function*(){yield l(),o("reload")})}return $(l),(e,r)=>{const V=b("a-button");return n(),p("div",{class:z(e.$attrs.class)},[m(d(T),{loading:c.value,paragraph:{rows:8},active:"",class:"p-[8px]"},{default:v(()=>[k("div",P,[e.showSearch?(n(),p("div",L,[m(d(E),{value:t.value,"onUpdate:value":r[0]||(r[0]=a=>t.value=a),placeholder:e.$t("pages.common.search"),size:"small"},{enterButton:v(()=>[m(V,{onClick:s},{default:v(()=>[m(d(M),{class:"text-primary"})]),_:1})]),_:1},8,["value","placeholder"])])):N("",!0),k("div",R,[u.value.length>0?(n(),O(d(B),A({key:0},e.$attrs,{"selected-keys":i.value,"onUpdate:selectedKeys":r[1]||(r[1]=a=>i.value=a),class:e.$attrs.class,"field-names":{title:"label",key:"id"},"show-line":{showLeafIcon:!1},"tree-data":u.value,virtual:!1,"default-expand-all":"",onSelect:r[2]||(r[2]=a=>e.$emit("select"))}),{title:v(({label:a})=>[a.indexOf(t.value)>-1?(n(),p("span",U,[I(h(a.substring(0,a.indexOf(t.value)))+" ",1),k("span",j,h(t.value),1),I(" "+h(a.substring(a.indexOf(t.value)+t.value.length)),1)])):(n(),p("span",q,h(a),1))]),_:1},16,["selected-keys","class","tree-data"])):(n(),p("div",G,[m(d(_),{image:d(_).PRESENTED_IMAGE_SIMPLE,description:"无部门数据"},null,8,["image"])]))])])]),_:1},8,["loading"])],2)}}});export{ee as _};
