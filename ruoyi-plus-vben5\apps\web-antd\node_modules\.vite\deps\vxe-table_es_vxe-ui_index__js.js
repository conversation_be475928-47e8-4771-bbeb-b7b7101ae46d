import {
  VXETable,
  _t,
  clipboard,
  commands,
  config,
  formats,
  getConfig,
  getI18n,
  getIcon,
  getTheme,
  globalEvents,
  globalResize,
  hooks,
  interceptor,
  log,
  menus,
  modal,
  print,
  readFile,
  renderer,
  saveFile,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  t,
  ui_default,
  use,
  validators,
  version
} from "./chunk-GRQJEYPS.js";
import {
  VxeUI
} from "./chunk-TETVOAVO.js";
import "./chunk-GAYNWPQE.js";
import "./chunk-7J2PGW6H.js";
import "./chunk-H3LFO6AW.js";
import "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/vxe-table@4.13.35_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-table/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  VXETable,
  VxeUI,
  _t,
  clipboard,
  commands,
  config,
  vxe_ui_default as default,
  formats,
  getConfig,
  getI18n,
  getIcon,
  getTheme,
  globalEvents,
  globalResize,
  hooks,
  interceptor,
  log,
  menus,
  modal,
  print,
  readFile,
  renderer,
  saveFile,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  t,
  use,
  validators,
  version
};
//# sourceMappingURL=vxe-table_es_vxe-ui_index__js.js.map
