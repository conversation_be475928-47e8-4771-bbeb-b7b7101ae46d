{"doc": " 操作日志 服务层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "insertOperlog", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo"], "doc": " 新增操作日志\n\n @param bo 操作日志对象\n"}, {"name": "selectOperLogList", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo"], "doc": " 查询系统操作日志集合\n\n @param operLog 操作日志对象\n @return 操作日志集合\n"}, {"name": "deleteOperLogByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除系统操作日志\n\n @param operIds 需要删除的操作日志ID\n @return 结果\n"}, {"name": "selectOperLogById", "paramTypes": ["java.lang.Long"], "doc": " 查询操作日志详细\n\n @param operId 操作ID\n @return 操作日志对象\n"}, {"name": "cleanOperLog", "paramTypes": [], "doc": " 清空操作日志\n"}], "constructors": []}