{"doc": " SaToken异常处理器\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleNotPermissionException", "paramTypes": ["cn.dev33.satoken.exception.NotPermissionException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 权限码异常\n"}, {"name": "handleNotRoleException", "paramTypes": ["cn.dev33.satoken.exception.NotRoleException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 角色权限异常\n"}, {"name": "handleNotLoginException", "paramTypes": ["cn.dev33.satoken.exception.NotLoginException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 认证失败\n"}], "constructors": []}