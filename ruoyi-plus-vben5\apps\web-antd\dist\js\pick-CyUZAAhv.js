import{bK as o,bW as c,bX as v,bY as g,bZ as x,b_ as _,b$ as b,c0 as m}from"./bootstrap-DCMzVRvD.js";function I(n,r,f,e){if(!o(n))return n;r=c(r,n);for(var s=-1,u=r.length,a=u-1,i=n;i!=null&&++s<u;){var t=v(r[s]),l=f;if(t==="__proto__"||t==="constructor"||t==="prototype")return n;if(s!=a){var d=i[t];l=void 0,l===void 0&&(l=o(d)?d:g(r[s+1])?[]:{})}x(i,t,l),i=i[t]}return n}function P(n,r,f){for(var e=-1,s=r.length,u={};++e<s;){var a=r[e],i=_(n,a);f(i,a)&&I(u,c(a,n),i)}return u}function K(n,r){return P(n,r,function(f,e){return b(n,e)})}var z=m(function(n,r){return n==null?{}:K(n,r)});export{z as p};
