{"doc": " 带有下拉选的Excel导出\n\n <AUTHOR>\n", "fields": [{"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "userStatus", "doc": " 用户类型\n </p>\n 使用ExcelEnumFormat注解需要进行下拉选的部分\n"}, {"name": "gender", "doc": " 性别\n <p>\n 使用ExcelDictFormat注解需要进行下拉选的部分\n"}, {"name": "phoneNumber", "doc": " 手机号\n"}, {"name": "email", "doc": " Email\n"}, {"name": "province", "doc": " 省\n <p>\n 级联下拉，仅判断是否选了\n"}, {"name": "provinceId", "doc": " 数据库中的省ID\n </p>\n 处理完毕后再判断是否市正确的值\n"}, {"name": "city", "doc": " 市\n <p>\n 级联下拉\n"}, {"name": "cityId", "doc": " 数据库中的市ID\n"}, {"name": "area", "doc": " 县\n <p>\n 级联下拉\n"}, {"name": "areaId", "doc": " 数据库中的县ID\n"}], "enumConstants": [], "methods": [], "constructors": []}