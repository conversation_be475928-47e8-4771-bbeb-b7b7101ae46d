import {
  onClickOutside,
  useClipboard,
  useLocalStorage
} from "./chunk-H3KUYBZ4.js";
import {
  tryOnMounted,
  useDebounceFn,
  whenever
} from "./chunk-R4PVOD3E.js";
import {
  merge_default
} from "./chunk-U4HUMHNL.js";
import "./chunk-NOHC5SXN.js";
import "./chunk-3NKQQAHI.js";
import {
  vue_types_m_default
} from "./chunk-6SCF53YI.js";
import "./chunk-GAYNWPQE.js";
import {
  Fragment,
  Teleport,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  inject,
  mergeProps,
  nextTick,
  onMounted,
  openBlock,
  popScopeId,
  provide,
  pushScopeId,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDynamicComponent,
  useSlots,
  vModelText,
  vShow,
  watch,
  withCtx,
  withDirectives
} from "./chunk-7J2PGW6H.js";
import {
  normalizeClass,
  normalizeStyle,
  toDisplayString
} from "./chunk-H3LFO6AW.js";
import {
  __commonJS,
  __toESM
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/gradient-parser@1.1.1/node_modules/gradient-parser/build/node.js
var require_node = __commonJS({
  "../../node_modules/.pnpm/gradient-parser@1.1.1/node_modules/gradient-parser/build/node.js"(exports) {
    var GradientParser = GradientParser || {};
    GradientParser.stringify = /* @__PURE__ */ function() {
      var visitor = {
        "visit_linear-gradient": function(node) {
          return visitor.visit_gradient(node);
        },
        "visit_repeating-linear-gradient": function(node) {
          return visitor.visit_gradient(node);
        },
        "visit_radial-gradient": function(node) {
          return visitor.visit_gradient(node);
        },
        "visit_repeating-radial-gradient": function(node) {
          return visitor.visit_gradient(node);
        },
        "visit_gradient": function(node) {
          var orientation = visitor.visit(node.orientation);
          if (orientation) {
            orientation += ", ";
          }
          return node.type + "(" + orientation + visitor.visit(node.colorStops) + ")";
        },
        "visit_shape": function(node) {
          var result = node.value, at3 = visitor.visit(node.at), style = visitor.visit(node.style);
          if (style) {
            result += " " + style;
          }
          if (at3) {
            result += " at " + at3;
          }
          return result;
        },
        "visit_default-radial": function(node) {
          var result = "", at3 = visitor.visit(node.at);
          if (at3) {
            result += at3;
          }
          return result;
        },
        "visit_extent-keyword": function(node) {
          var result = node.value, at3 = visitor.visit(node.at);
          if (at3) {
            result += " at " + at3;
          }
          return result;
        },
        "visit_position-keyword": function(node) {
          return node.value;
        },
        "visit_position": function(node) {
          return visitor.visit(node.value.x) + " " + visitor.visit(node.value.y);
        },
        "visit_%": function(node) {
          return node.value + "%";
        },
        "visit_em": function(node) {
          return node.value + "em";
        },
        "visit_px": function(node) {
          return node.value + "px";
        },
        "visit_calc": function(node) {
          return "calc(" + node.value + ")";
        },
        "visit_literal": function(node) {
          return visitor.visit_color(node.value, node);
        },
        "visit_hex": function(node) {
          return visitor.visit_color("#" + node.value, node);
        },
        "visit_rgb": function(node) {
          return visitor.visit_color("rgb(" + node.value.join(", ") + ")", node);
        },
        "visit_rgba": function(node) {
          return visitor.visit_color("rgba(" + node.value.join(", ") + ")", node);
        },
        "visit_hsl": function(node) {
          return visitor.visit_color("hsl(" + node.value[0] + ", " + node.value[1] + "%, " + node.value[2] + "%)", node);
        },
        "visit_hsla": function(node) {
          return visitor.visit_color("hsla(" + node.value[0] + ", " + node.value[1] + "%, " + node.value[2] + "%, " + node.value[3] + ")", node);
        },
        "visit_var": function(node) {
          return visitor.visit_color("var(" + node.value + ")", node);
        },
        "visit_color": function(resultColor, node) {
          var result = resultColor, length = visitor.visit(node.length);
          if (length) {
            result += " " + length;
          }
          return result;
        },
        "visit_angular": function(node) {
          return node.value + "deg";
        },
        "visit_directional": function(node) {
          return "to " + node.value;
        },
        "visit_array": function(elements) {
          var result = "", size = elements.length;
          elements.forEach(function(element, i2) {
            result += visitor.visit(element);
            if (i2 < size - 1) {
              result += ", ";
            }
          });
          return result;
        },
        "visit_object": function(obj) {
          if (obj.width && obj.height) {
            return visitor.visit(obj.width) + " " + visitor.visit(obj.height);
          }
          return "";
        },
        "visit": function(element) {
          if (!element) {
            return "";
          }
          var result = "";
          if (element instanceof Array) {
            return visitor.visit_array(element);
          } else if (typeof element === "object" && !element.type) {
            return visitor.visit_object(element);
          } else if (element.type) {
            var nodeVisitor = visitor["visit_" + element.type];
            if (nodeVisitor) {
              return nodeVisitor(element);
            } else {
              throw Error("Missing visitor visit_" + element.type);
            }
          } else {
            throw Error("Invalid node.");
          }
        }
      };
      return function(root) {
        return visitor.visit(root);
      };
    }();
    var GradientParser = GradientParser || {};
    GradientParser.parse = /* @__PURE__ */ function() {
      var tokens = {
        linearGradient: /^(\-(webkit|o|ms|moz)\-)?(linear\-gradient)/i,
        repeatingLinearGradient: /^(\-(webkit|o|ms|moz)\-)?(repeating\-linear\-gradient)/i,
        radialGradient: /^(\-(webkit|o|ms|moz)\-)?(radial\-gradient)/i,
        repeatingRadialGradient: /^(\-(webkit|o|ms|moz)\-)?(repeating\-radial\-gradient)/i,
        sideOrCorner: /^to (left (top|bottom)|right (top|bottom)|top (left|right)|bottom (left|right)|left|right|top|bottom)/i,
        extentKeywords: /^(closest\-side|closest\-corner|farthest\-side|farthest\-corner|contain|cover)/,
        positionKeywords: /^(left|center|right|top|bottom)/i,
        pixelValue: /^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))px/,
        percentageValue: /^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))\%/,
        emValue: /^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))em/,
        angleValue: /^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))deg/,
        radianValue: /^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))rad/,
        startCall: /^\(/,
        endCall: /^\)/,
        comma: /^,/,
        hexColor: /^\#([0-9a-fA-F]+)/,
        literalColor: /^([a-zA-Z]+)/,
        rgbColor: /^rgb/i,
        rgbaColor: /^rgba/i,
        varColor: /^var/i,
        calcValue: /^calc/i,
        variableName: /^(--[a-zA-Z0-9-,\s\#]+)/,
        number: /^(([0-9]*\.[0-9]+)|([0-9]+\.?))/,
        hslColor: /^hsl/i,
        hslaColor: /^hsla/i
      };
      var input = "";
      function error(msg) {
        var err = new Error(input + ": " + msg);
        err.source = input;
        throw err;
      }
      function getAST() {
        var ast = matchListDefinitions();
        if (input.length > 0) {
          error("Invalid input not EOF");
        }
        return ast;
      }
      function matchListDefinitions() {
        return matchListing(matchDefinition);
      }
      function matchDefinition() {
        return matchGradient(
          "linear-gradient",
          tokens.linearGradient,
          matchLinearOrientation
        ) || matchGradient(
          "repeating-linear-gradient",
          tokens.repeatingLinearGradient,
          matchLinearOrientation
        ) || matchGradient(
          "radial-gradient",
          tokens.radialGradient,
          matchListRadialOrientations
        ) || matchGradient(
          "repeating-radial-gradient",
          tokens.repeatingRadialGradient,
          matchListRadialOrientations
        );
      }
      function matchGradient(gradientType, pattern, orientationMatcher) {
        return matchCall(pattern, function(captures) {
          var orientation = orientationMatcher();
          if (orientation) {
            if (!scan(tokens.comma)) {
              error("Missing comma before color stops");
            }
          }
          return {
            type: gradientType,
            orientation,
            colorStops: matchListing(matchColorStop)
          };
        });
      }
      function matchCall(pattern, callback) {
        var captures = scan(pattern);
        if (captures) {
          if (!scan(tokens.startCall)) {
            error("Missing (");
          }
          var result = callback(captures);
          if (!scan(tokens.endCall)) {
            error("Missing )");
          }
          return result;
        }
      }
      function matchLinearOrientation() {
        var sideOrCorner = matchSideOrCorner();
        if (sideOrCorner) {
          return sideOrCorner;
        }
        var legacyDirection = match("position-keyword", tokens.positionKeywords, 1);
        if (legacyDirection) {
          return {
            type: "directional",
            value: legacyDirection.value
          };
        }
        return matchAngle();
      }
      function matchSideOrCorner() {
        return match("directional", tokens.sideOrCorner, 1);
      }
      function matchAngle() {
        return match("angular", tokens.angleValue, 1) || match("angular", tokens.radianValue, 1);
      }
      function matchListRadialOrientations() {
        var radialOrientations, radialOrientation = matchRadialOrientation(), lookaheadCache;
        if (radialOrientation) {
          radialOrientations = [];
          radialOrientations.push(radialOrientation);
          lookaheadCache = input;
          if (scan(tokens.comma)) {
            radialOrientation = matchRadialOrientation();
            if (radialOrientation) {
              radialOrientations.push(radialOrientation);
            } else {
              input = lookaheadCache;
            }
          }
        }
        return radialOrientations;
      }
      function matchRadialOrientation() {
        var radialType = matchCircle() || matchEllipse();
        if (radialType) {
          radialType.at = matchAtPosition();
        } else {
          var extent = matchExtentKeyword();
          if (extent) {
            radialType = extent;
            var positionAt = matchAtPosition();
            if (positionAt) {
              radialType.at = positionAt;
            }
          } else {
            var atPosition = matchAtPosition();
            if (atPosition) {
              radialType = {
                type: "default-radial",
                at: atPosition
              };
            } else {
              var defaultPosition = matchPositioning();
              if (defaultPosition) {
                radialType = {
                  type: "default-radial",
                  at: defaultPosition
                };
              }
            }
          }
        }
        return radialType;
      }
      function matchCircle() {
        var circle = match("shape", /^(circle)/i, 0);
        if (circle) {
          circle.style = matchLength() || matchExtentKeyword();
        }
        return circle;
      }
      function matchEllipse() {
        var ellipse = match("shape", /^(ellipse)/i, 0);
        if (ellipse) {
          ellipse.style = matchPositioning() || matchDistance() || matchExtentKeyword();
        }
        return ellipse;
      }
      function matchExtentKeyword() {
        return match("extent-keyword", tokens.extentKeywords, 1);
      }
      function matchAtPosition() {
        if (match("position", /^at/, 0)) {
          var positioning = matchPositioning();
          if (!positioning) {
            error("Missing positioning value");
          }
          return positioning;
        }
      }
      function matchPositioning() {
        var location = matchCoordinates();
        if (location.x || location.y) {
          return {
            type: "position",
            value: location
          };
        }
      }
      function matchCoordinates() {
        return {
          x: matchDistance(),
          y: matchDistance()
        };
      }
      function matchListing(matcher) {
        var captures = matcher(), result = [];
        if (captures) {
          result.push(captures);
          while (scan(tokens.comma)) {
            captures = matcher();
            if (captures) {
              result.push(captures);
            } else {
              error("One extra comma");
            }
          }
        }
        return result;
      }
      function matchColorStop() {
        var color = matchColor();
        if (!color) {
          error("Expected color definition");
        }
        color.length = matchDistance();
        return color;
      }
      function matchColor() {
        return matchHexColor() || matchHSLAColor() || matchHSLColor() || matchRGBAColor() || matchRGBColor() || matchVarColor() || matchLiteralColor();
      }
      function matchLiteralColor() {
        return match("literal", tokens.literalColor, 0);
      }
      function matchHexColor() {
        return match("hex", tokens.hexColor, 1);
      }
      function matchRGBColor() {
        return matchCall(tokens.rgbColor, function() {
          return {
            type: "rgb",
            value: matchListing(matchNumber)
          };
        });
      }
      function matchRGBAColor() {
        return matchCall(tokens.rgbaColor, function() {
          return {
            type: "rgba",
            value: matchListing(matchNumber)
          };
        });
      }
      function matchVarColor() {
        return matchCall(tokens.varColor, function() {
          return {
            type: "var",
            value: matchVariableName()
          };
        });
      }
      function matchHSLColor() {
        return matchCall(tokens.hslColor, function() {
          var lookahead = scan(tokens.percentageValue);
          if (lookahead) {
            error("HSL hue value must be a number in degrees (0-360) or normalized (-360 to 360), not a percentage");
          }
          var hue = matchNumber();
          scan(tokens.comma);
          var captures = scan(tokens.percentageValue);
          var sat = captures ? captures[1] : null;
          scan(tokens.comma);
          captures = scan(tokens.percentageValue);
          var light = captures ? captures[1] : null;
          if (!sat || !light) {
            error("Expected percentage value for saturation and lightness in HSL");
          }
          return {
            type: "hsl",
            value: [hue, sat, light]
          };
        });
      }
      function matchHSLAColor() {
        return matchCall(tokens.hslaColor, function() {
          var hue = matchNumber();
          scan(tokens.comma);
          var captures = scan(tokens.percentageValue);
          var sat = captures ? captures[1] : null;
          scan(tokens.comma);
          captures = scan(tokens.percentageValue);
          var light = captures ? captures[1] : null;
          scan(tokens.comma);
          var alpha = matchNumber();
          if (!sat || !light) {
            error("Expected percentage value for saturation and lightness in HSLA");
          }
          return {
            type: "hsla",
            value: [hue, sat, light, alpha]
          };
        });
      }
      function matchPercentage() {
        var captures = scan(tokens.percentageValue);
        return captures ? captures[1] : null;
      }
      function matchVariableName() {
        return scan(tokens.variableName)[1];
      }
      function matchNumber() {
        return scan(tokens.number)[1];
      }
      function matchDistance() {
        return match("%", tokens.percentageValue, 1) || matchPositionKeyword() || matchCalc() || matchLength();
      }
      function matchPositionKeyword() {
        return match("position-keyword", tokens.positionKeywords, 1);
      }
      function matchCalc() {
        return matchCall(tokens.calcValue, function() {
          var openParenCount = 1;
          var i2 = 0;
          while (openParenCount > 0 && i2 < input.length) {
            var char = input.charAt(i2);
            if (char === "(") {
              openParenCount++;
            } else if (char === ")") {
              openParenCount--;
            }
            i2++;
          }
          if (openParenCount > 0) {
            error("Missing closing parenthesis in calc() expression");
          }
          var calcContent = input.substring(0, i2 - 1);
          consume(i2 - 1);
          return {
            type: "calc",
            value: calcContent
          };
        });
      }
      function matchLength() {
        return match("px", tokens.pixelValue, 1) || match("em", tokens.emValue, 1);
      }
      function match(type, pattern, captureIndex) {
        var captures = scan(pattern);
        if (captures) {
          return {
            type,
            value: captures[captureIndex]
          };
        }
      }
      function scan(regexp) {
        var captures, blankCaptures;
        blankCaptures = /^[\n\r\t\s]+/.exec(input);
        if (blankCaptures) {
          consume(blankCaptures[0].length);
        }
        captures = regexp.exec(input);
        if (captures) {
          consume(captures[0].length);
        }
        return captures;
      }
      function consume(size) {
        input = input.substr(size);
      }
      return function(code) {
        input = code.toString().trim();
        if (input.endsWith(";")) {
          input = input.slice(0, -1);
        }
        return getAST();
      };
    }();
    exports.parse = GradientParser.parse;
    exports.stringify = GradientParser.stringify;
  }
});

// ../../node_modules/.pnpm/tinycolor2@1.6.0/node_modules/tinycolor2/esm/tinycolor.js
function _typeof(obj) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj2) {
    return typeof obj2;
  } : function(obj2) {
    return obj2 && "function" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
  }, _typeof(obj);
}
var trimLeft = /^\s+/;
var trimRight = /\s+$/;
function tinycolor(color, opts) {
  color = color ? color : "";
  opts = opts || {};
  if (color instanceof tinycolor) {
    return color;
  }
  if (!(this instanceof tinycolor)) {
    return new tinycolor(color, opts);
  }
  var rgb = inputToRGB(color);
  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;
  this._gradientType = opts.gradientType;
  if (this._r < 1) this._r = Math.round(this._r);
  if (this._g < 1) this._g = Math.round(this._g);
  if (this._b < 1) this._b = Math.round(this._b);
  this._ok = rgb.ok;
}
tinycolor.prototype = {
  isDark: function isDark() {
    return this.getBrightness() < 128;
  },
  isLight: function isLight() {
    return !this.isDark();
  },
  isValid: function isValid() {
    return this._ok;
  },
  getOriginalInput: function getOriginalInput() {
    return this._originalInput;
  },
  getFormat: function getFormat() {
    return this._format;
  },
  getAlpha: function getAlpha() {
    return this._a;
  },
  getBrightness: function getBrightness() {
    var rgb = this.toRgb();
    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1e3;
  },
  getLuminance: function getLuminance() {
    var rgb = this.toRgb();
    var RsRGB, GsRGB, BsRGB, R2, G2, B2;
    RsRGB = rgb.r / 255;
    GsRGB = rgb.g / 255;
    BsRGB = rgb.b / 255;
    if (RsRGB <= 0.03928) R2 = RsRGB / 12.92;
    else R2 = Math.pow((RsRGB + 0.055) / 1.055, 2.4);
    if (GsRGB <= 0.03928) G2 = GsRGB / 12.92;
    else G2 = Math.pow((GsRGB + 0.055) / 1.055, 2.4);
    if (BsRGB <= 0.03928) B2 = BsRGB / 12.92;
    else B2 = Math.pow((BsRGB + 0.055) / 1.055, 2.4);
    return 0.2126 * R2 + 0.7152 * G2 + 0.0722 * B2;
  },
  setAlpha: function setAlpha(value) {
    this._a = boundAlpha(value);
    this._roundA = Math.round(100 * this._a) / 100;
    return this;
  },
  toHsv: function toHsv() {
    var hsv = rgbToHsv(this._r, this._g, this._b);
    return {
      h: hsv.h * 360,
      s: hsv.s,
      v: hsv.v,
      a: this._a
    };
  },
  toHsvString: function toHsvString() {
    var hsv = rgbToHsv(this._r, this._g, this._b);
    var h2 = Math.round(hsv.h * 360), s2 = Math.round(hsv.s * 100), v2 = Math.round(hsv.v * 100);
    return this._a == 1 ? "hsv(" + h2 + ", " + s2 + "%, " + v2 + "%)" : "hsva(" + h2 + ", " + s2 + "%, " + v2 + "%, " + this._roundA + ")";
  },
  toHsl: function toHsl() {
    var hsl = rgbToHsl(this._r, this._g, this._b);
    return {
      h: hsl.h * 360,
      s: hsl.s,
      l: hsl.l,
      a: this._a
    };
  },
  toHslString: function toHslString() {
    var hsl = rgbToHsl(this._r, this._g, this._b);
    var h2 = Math.round(hsl.h * 360), s2 = Math.round(hsl.s * 100), l2 = Math.round(hsl.l * 100);
    return this._a == 1 ? "hsl(" + h2 + ", " + s2 + "%, " + l2 + "%)" : "hsla(" + h2 + ", " + s2 + "%, " + l2 + "%, " + this._roundA + ")";
  },
  toHex: function toHex(allow3Char) {
    return rgbToHex(this._r, this._g, this._b, allow3Char);
  },
  toHexString: function toHexString(allow3Char) {
    return "#" + this.toHex(allow3Char);
  },
  toHex8: function toHex8(allow4Char) {
    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);
  },
  toHex8String: function toHex8String(allow4Char) {
    return "#" + this.toHex8(allow4Char);
  },
  toRgb: function toRgb() {
    return {
      r: Math.round(this._r),
      g: Math.round(this._g),
      b: Math.round(this._b),
      a: this._a
    };
  },
  toRgbString: function toRgbString() {
    return this._a == 1 ? "rgb(" + Math.round(this._r) + ", " + Math.round(this._g) + ", " + Math.round(this._b) + ")" : "rgba(" + Math.round(this._r) + ", " + Math.round(this._g) + ", " + Math.round(this._b) + ", " + this._roundA + ")";
  },
  toPercentageRgb: function toPercentageRgb() {
    return {
      r: Math.round(bound01(this._r, 255) * 100) + "%",
      g: Math.round(bound01(this._g, 255) * 100) + "%",
      b: Math.round(bound01(this._b, 255) * 100) + "%",
      a: this._a
    };
  },
  toPercentageRgbString: function toPercentageRgbString() {
    return this._a == 1 ? "rgb(" + Math.round(bound01(this._r, 255) * 100) + "%, " + Math.round(bound01(this._g, 255) * 100) + "%, " + Math.round(bound01(this._b, 255) * 100) + "%)" : "rgba(" + Math.round(bound01(this._r, 255) * 100) + "%, " + Math.round(bound01(this._g, 255) * 100) + "%, " + Math.round(bound01(this._b, 255) * 100) + "%, " + this._roundA + ")";
  },
  toName: function toName() {
    if (this._a === 0) {
      return "transparent";
    }
    if (this._a < 1) {
      return false;
    }
    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;
  },
  toFilter: function toFilter(secondColor) {
    var hex8String = "#" + rgbaToArgbHex(this._r, this._g, this._b, this._a);
    var secondHex8String = hex8String;
    var gradientType = this._gradientType ? "GradientType = 1, " : "";
    if (secondColor) {
      var s2 = tinycolor(secondColor);
      secondHex8String = "#" + rgbaToArgbHex(s2._r, s2._g, s2._b, s2._a);
    }
    return "progid:DXImageTransform.Microsoft.gradient(" + gradientType + "startColorstr=" + hex8String + ",endColorstr=" + secondHex8String + ")";
  },
  toString: function toString(format) {
    var formatSet = !!format;
    format = format || this._format;
    var formattedString = false;
    var hasAlpha = this._a < 1 && this._a >= 0;
    var needsAlphaFormat = !formatSet && hasAlpha && (format === "hex" || format === "hex6" || format === "hex3" || format === "hex4" || format === "hex8" || format === "name");
    if (needsAlphaFormat) {
      if (format === "name" && this._a === 0) {
        return this.toName();
      }
      return this.toRgbString();
    }
    if (format === "rgb") {
      formattedString = this.toRgbString();
    }
    if (format === "prgb") {
      formattedString = this.toPercentageRgbString();
    }
    if (format === "hex" || format === "hex6") {
      formattedString = this.toHexString();
    }
    if (format === "hex3") {
      formattedString = this.toHexString(true);
    }
    if (format === "hex4") {
      formattedString = this.toHex8String(true);
    }
    if (format === "hex8") {
      formattedString = this.toHex8String();
    }
    if (format === "name") {
      formattedString = this.toName();
    }
    if (format === "hsl") {
      formattedString = this.toHslString();
    }
    if (format === "hsv") {
      formattedString = this.toHsvString();
    }
    return formattedString || this.toHexString();
  },
  clone: function clone() {
    return tinycolor(this.toString());
  },
  _applyModification: function _applyModification(fn3, args) {
    var color = fn3.apply(null, [this].concat([].slice.call(args)));
    this._r = color._r;
    this._g = color._g;
    this._b = color._b;
    this.setAlpha(color._a);
    return this;
  },
  lighten: function lighten() {
    return this._applyModification(_lighten, arguments);
  },
  brighten: function brighten() {
    return this._applyModification(_brighten, arguments);
  },
  darken: function darken() {
    return this._applyModification(_darken, arguments);
  },
  desaturate: function desaturate() {
    return this._applyModification(_desaturate, arguments);
  },
  saturate: function saturate() {
    return this._applyModification(_saturate, arguments);
  },
  greyscale: function greyscale() {
    return this._applyModification(_greyscale, arguments);
  },
  spin: function spin() {
    return this._applyModification(_spin, arguments);
  },
  _applyCombination: function _applyCombination(fn3, args) {
    return fn3.apply(null, [this].concat([].slice.call(args)));
  },
  analogous: function analogous() {
    return this._applyCombination(_analogous, arguments);
  },
  complement: function complement() {
    return this._applyCombination(_complement, arguments);
  },
  monochromatic: function monochromatic() {
    return this._applyCombination(_monochromatic, arguments);
  },
  splitcomplement: function splitcomplement() {
    return this._applyCombination(_splitcomplement, arguments);
  },
  // Disabled until https://github.com/bgrins/TinyColor/issues/254
  // polyad: function (number) {
  //   return this._applyCombination(polyad, [number]);
  // },
  triad: function triad() {
    return this._applyCombination(polyad, [3]);
  },
  tetrad: function tetrad() {
    return this._applyCombination(polyad, [4]);
  }
};
tinycolor.fromRatio = function(color, opts) {
  if (_typeof(color) == "object") {
    var newColor = {};
    for (var i2 in color) {
      if (color.hasOwnProperty(i2)) {
        if (i2 === "a") {
          newColor[i2] = color[i2];
        } else {
          newColor[i2] = convertToPercentage(color[i2]);
        }
      }
    }
    color = newColor;
  }
  return tinycolor(color, opts);
};
function inputToRGB(color) {
  var rgb = {
    r: 0,
    g: 0,
    b: 0
  };
  var a2 = 1;
  var s2 = null;
  var v2 = null;
  var l2 = null;
  var ok = false;
  var format = false;
  if (typeof color == "string") {
    color = stringInputToObject(color);
  }
  if (_typeof(color) == "object") {
    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {
      rgb = rgbToRgb(color.r, color.g, color.b);
      ok = true;
      format = String(color.r).substr(-1) === "%" ? "prgb" : "rgb";
    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {
      s2 = convertToPercentage(color.s);
      v2 = convertToPercentage(color.v);
      rgb = hsvToRgb(color.h, s2, v2);
      ok = true;
      format = "hsv";
    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {
      s2 = convertToPercentage(color.s);
      l2 = convertToPercentage(color.l);
      rgb = hslToRgb(color.h, s2, l2);
      ok = true;
      format = "hsl";
    }
    if (color.hasOwnProperty("a")) {
      a2 = color.a;
    }
  }
  a2 = boundAlpha(a2);
  return {
    ok,
    format: color.format || format,
    r: Math.min(255, Math.max(rgb.r, 0)),
    g: Math.min(255, Math.max(rgb.g, 0)),
    b: Math.min(255, Math.max(rgb.b, 0)),
    a: a2
  };
}
function rgbToRgb(r2, g2, b2) {
  return {
    r: bound01(r2, 255) * 255,
    g: bound01(g2, 255) * 255,
    b: bound01(b2, 255) * 255
  };
}
function rgbToHsl(r2, g2, b2) {
  r2 = bound01(r2, 255);
  g2 = bound01(g2, 255);
  b2 = bound01(b2, 255);
  var max2 = Math.max(r2, g2, b2), min2 = Math.min(r2, g2, b2);
  var h2, s2, l2 = (max2 + min2) / 2;
  if (max2 == min2) {
    h2 = s2 = 0;
  } else {
    var d2 = max2 - min2;
    s2 = l2 > 0.5 ? d2 / (2 - max2 - min2) : d2 / (max2 + min2);
    switch (max2) {
      case r2:
        h2 = (g2 - b2) / d2 + (g2 < b2 ? 6 : 0);
        break;
      case g2:
        h2 = (b2 - r2) / d2 + 2;
        break;
      case b2:
        h2 = (r2 - g2) / d2 + 4;
        break;
    }
    h2 /= 6;
  }
  return {
    h: h2,
    s: s2,
    l: l2
  };
}
function hslToRgb(h2, s2, l2) {
  var r2, g2, b2;
  h2 = bound01(h2, 360);
  s2 = bound01(s2, 100);
  l2 = bound01(l2, 100);
  function hue2rgb(p3, q3, t2) {
    if (t2 < 0) t2 += 1;
    if (t2 > 1) t2 -= 1;
    if (t2 < 1 / 6) return p3 + (q3 - p3) * 6 * t2;
    if (t2 < 1 / 2) return q3;
    if (t2 < 2 / 3) return p3 + (q3 - p3) * (2 / 3 - t2) * 6;
    return p3;
  }
  if (s2 === 0) {
    r2 = g2 = b2 = l2;
  } else {
    var q2 = l2 < 0.5 ? l2 * (1 + s2) : l2 + s2 - l2 * s2;
    var p2 = 2 * l2 - q2;
    r2 = hue2rgb(p2, q2, h2 + 1 / 3);
    g2 = hue2rgb(p2, q2, h2);
    b2 = hue2rgb(p2, q2, h2 - 1 / 3);
  }
  return {
    r: r2 * 255,
    g: g2 * 255,
    b: b2 * 255
  };
}
function rgbToHsv(r2, g2, b2) {
  r2 = bound01(r2, 255);
  g2 = bound01(g2, 255);
  b2 = bound01(b2, 255);
  var max2 = Math.max(r2, g2, b2), min2 = Math.min(r2, g2, b2);
  var h2, s2, v2 = max2;
  var d2 = max2 - min2;
  s2 = max2 === 0 ? 0 : d2 / max2;
  if (max2 == min2) {
    h2 = 0;
  } else {
    switch (max2) {
      case r2:
        h2 = (g2 - b2) / d2 + (g2 < b2 ? 6 : 0);
        break;
      case g2:
        h2 = (b2 - r2) / d2 + 2;
        break;
      case b2:
        h2 = (r2 - g2) / d2 + 4;
        break;
    }
    h2 /= 6;
  }
  return {
    h: h2,
    s: s2,
    v: v2
  };
}
function hsvToRgb(h2, s2, v2) {
  h2 = bound01(h2, 360) * 6;
  s2 = bound01(s2, 100);
  v2 = bound01(v2, 100);
  var i2 = Math.floor(h2), f2 = h2 - i2, p2 = v2 * (1 - s2), q2 = v2 * (1 - f2 * s2), t2 = v2 * (1 - (1 - f2) * s2), mod = i2 % 6, r2 = [v2, q2, p2, p2, t2, v2][mod], g2 = [t2, v2, v2, q2, p2, p2][mod], b2 = [p2, p2, t2, v2, v2, q2][mod];
  return {
    r: r2 * 255,
    g: g2 * 255,
    b: b2 * 255
  };
}
function rgbToHex(r2, g2, b2, allow3Char) {
  var hex = [pad2(Math.round(r2).toString(16)), pad2(Math.round(g2).toString(16)), pad2(Math.round(b2).toString(16))];
  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {
    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);
  }
  return hex.join("");
}
function rgbaToHex(r2, g2, b2, a2, allow4Char) {
  var hex = [pad2(Math.round(r2).toString(16)), pad2(Math.round(g2).toString(16)), pad2(Math.round(b2).toString(16)), pad2(convertDecimalToHex(a2))];
  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {
    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);
  }
  return hex.join("");
}
function rgbaToArgbHex(r2, g2, b2, a2) {
  var hex = [pad2(convertDecimalToHex(a2)), pad2(Math.round(r2).toString(16)), pad2(Math.round(g2).toString(16)), pad2(Math.round(b2).toString(16))];
  return hex.join("");
}
tinycolor.equals = function(color1, color2) {
  if (!color1 || !color2) return false;
  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();
};
tinycolor.random = function() {
  return tinycolor.fromRatio({
    r: Math.random(),
    g: Math.random(),
    b: Math.random()
  });
};
function _desaturate(color, amount) {
  amount = amount === 0 ? 0 : amount || 10;
  var hsl = tinycolor(color).toHsl();
  hsl.s -= amount / 100;
  hsl.s = clamp01(hsl.s);
  return tinycolor(hsl);
}
function _saturate(color, amount) {
  amount = amount === 0 ? 0 : amount || 10;
  var hsl = tinycolor(color).toHsl();
  hsl.s += amount / 100;
  hsl.s = clamp01(hsl.s);
  return tinycolor(hsl);
}
function _greyscale(color) {
  return tinycolor(color).desaturate(100);
}
function _lighten(color, amount) {
  amount = amount === 0 ? 0 : amount || 10;
  var hsl = tinycolor(color).toHsl();
  hsl.l += amount / 100;
  hsl.l = clamp01(hsl.l);
  return tinycolor(hsl);
}
function _brighten(color, amount) {
  amount = amount === 0 ? 0 : amount || 10;
  var rgb = tinycolor(color).toRgb();
  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));
  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));
  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));
  return tinycolor(rgb);
}
function _darken(color, amount) {
  amount = amount === 0 ? 0 : amount || 10;
  var hsl = tinycolor(color).toHsl();
  hsl.l -= amount / 100;
  hsl.l = clamp01(hsl.l);
  return tinycolor(hsl);
}
function _spin(color, amount) {
  var hsl = tinycolor(color).toHsl();
  var hue = (hsl.h + amount) % 360;
  hsl.h = hue < 0 ? 360 + hue : hue;
  return tinycolor(hsl);
}
function _complement(color) {
  var hsl = tinycolor(color).toHsl();
  hsl.h = (hsl.h + 180) % 360;
  return tinycolor(hsl);
}
function polyad(color, number) {
  if (isNaN(number) || number <= 0) {
    throw new Error("Argument to polyad must be a positive number");
  }
  var hsl = tinycolor(color).toHsl();
  var result = [tinycolor(color)];
  var step = 360 / number;
  for (var i2 = 1; i2 < number; i2++) {
    result.push(tinycolor({
      h: (hsl.h + i2 * step) % 360,
      s: hsl.s,
      l: hsl.l
    }));
  }
  return result;
}
function _splitcomplement(color) {
  var hsl = tinycolor(color).toHsl();
  var h2 = hsl.h;
  return [tinycolor(color), tinycolor({
    h: (h2 + 72) % 360,
    s: hsl.s,
    l: hsl.l
  }), tinycolor({
    h: (h2 + 216) % 360,
    s: hsl.s,
    l: hsl.l
  })];
}
function _analogous(color, results, slices) {
  results = results || 6;
  slices = slices || 30;
  var hsl = tinycolor(color).toHsl();
  var part = 360 / slices;
  var ret = [tinycolor(color)];
  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results; ) {
    hsl.h = (hsl.h + part) % 360;
    ret.push(tinycolor(hsl));
  }
  return ret;
}
function _monochromatic(color, results) {
  results = results || 6;
  var hsv = tinycolor(color).toHsv();
  var h2 = hsv.h, s2 = hsv.s, v2 = hsv.v;
  var ret = [];
  var modification = 1 / results;
  while (results--) {
    ret.push(tinycolor({
      h: h2,
      s: s2,
      v: v2
    }));
    v2 = (v2 + modification) % 1;
  }
  return ret;
}
tinycolor.mix = function(color1, color2, amount) {
  amount = amount === 0 ? 0 : amount || 50;
  var rgb1 = tinycolor(color1).toRgb();
  var rgb2 = tinycolor(color2).toRgb();
  var p2 = amount / 100;
  var rgba = {
    r: (rgb2.r - rgb1.r) * p2 + rgb1.r,
    g: (rgb2.g - rgb1.g) * p2 + rgb1.g,
    b: (rgb2.b - rgb1.b) * p2 + rgb1.b,
    a: (rgb2.a - rgb1.a) * p2 + rgb1.a
  };
  return tinycolor(rgba);
};
tinycolor.readability = function(color1, color2) {
  var c1 = tinycolor(color1);
  var c2 = tinycolor(color2);
  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);
};
tinycolor.isReadable = function(color1, color2, wcag2) {
  var readability = tinycolor.readability(color1, color2);
  var wcag2Parms, out;
  out = false;
  wcag2Parms = validateWCAG2Parms(wcag2);
  switch (wcag2Parms.level + wcag2Parms.size) {
    case "AAsmall":
    case "AAAlarge":
      out = readability >= 4.5;
      break;
    case "AAlarge":
      out = readability >= 3;
      break;
    case "AAAsmall":
      out = readability >= 7;
      break;
  }
  return out;
};
tinycolor.mostReadable = function(baseColor, colorList, args) {
  var bestColor = null;
  var bestScore = 0;
  var readability;
  var includeFallbackColors, level, size;
  args = args || {};
  includeFallbackColors = args.includeFallbackColors;
  level = args.level;
  size = args.size;
  for (var i2 = 0; i2 < colorList.length; i2++) {
    readability = tinycolor.readability(baseColor, colorList[i2]);
    if (readability > bestScore) {
      bestScore = readability;
      bestColor = tinycolor(colorList[i2]);
    }
  }
  if (tinycolor.isReadable(baseColor, bestColor, {
    level,
    size
  }) || !includeFallbackColors) {
    return bestColor;
  } else {
    args.includeFallbackColors = false;
    return tinycolor.mostReadable(baseColor, ["#fff", "#000"], args);
  }
};
var names = tinycolor.names = {
  aliceblue: "f0f8ff",
  antiquewhite: "faebd7",
  aqua: "0ff",
  aquamarine: "7fffd4",
  azure: "f0ffff",
  beige: "f5f5dc",
  bisque: "ffe4c4",
  black: "000",
  blanchedalmond: "ffebcd",
  blue: "00f",
  blueviolet: "8a2be2",
  brown: "a52a2a",
  burlywood: "deb887",
  burntsienna: "ea7e5d",
  cadetblue: "5f9ea0",
  chartreuse: "7fff00",
  chocolate: "d2691e",
  coral: "ff7f50",
  cornflowerblue: "6495ed",
  cornsilk: "fff8dc",
  crimson: "dc143c",
  cyan: "0ff",
  darkblue: "00008b",
  darkcyan: "008b8b",
  darkgoldenrod: "b8860b",
  darkgray: "a9a9a9",
  darkgreen: "006400",
  darkgrey: "a9a9a9",
  darkkhaki: "bdb76b",
  darkmagenta: "8b008b",
  darkolivegreen: "556b2f",
  darkorange: "ff8c00",
  darkorchid: "9932cc",
  darkred: "8b0000",
  darksalmon: "e9967a",
  darkseagreen: "8fbc8f",
  darkslateblue: "483d8b",
  darkslategray: "2f4f4f",
  darkslategrey: "2f4f4f",
  darkturquoise: "00ced1",
  darkviolet: "9400d3",
  deeppink: "ff1493",
  deepskyblue: "00bfff",
  dimgray: "696969",
  dimgrey: "696969",
  dodgerblue: "1e90ff",
  firebrick: "b22222",
  floralwhite: "fffaf0",
  forestgreen: "228b22",
  fuchsia: "f0f",
  gainsboro: "dcdcdc",
  ghostwhite: "f8f8ff",
  gold: "ffd700",
  goldenrod: "daa520",
  gray: "808080",
  green: "008000",
  greenyellow: "adff2f",
  grey: "808080",
  honeydew: "f0fff0",
  hotpink: "ff69b4",
  indianred: "cd5c5c",
  indigo: "4b0082",
  ivory: "fffff0",
  khaki: "f0e68c",
  lavender: "e6e6fa",
  lavenderblush: "fff0f5",
  lawngreen: "7cfc00",
  lemonchiffon: "fffacd",
  lightblue: "add8e6",
  lightcoral: "f08080",
  lightcyan: "e0ffff",
  lightgoldenrodyellow: "fafad2",
  lightgray: "d3d3d3",
  lightgreen: "90ee90",
  lightgrey: "d3d3d3",
  lightpink: "ffb6c1",
  lightsalmon: "ffa07a",
  lightseagreen: "20b2aa",
  lightskyblue: "87cefa",
  lightslategray: "789",
  lightslategrey: "789",
  lightsteelblue: "b0c4de",
  lightyellow: "ffffe0",
  lime: "0f0",
  limegreen: "32cd32",
  linen: "faf0e6",
  magenta: "f0f",
  maroon: "800000",
  mediumaquamarine: "66cdaa",
  mediumblue: "0000cd",
  mediumorchid: "ba55d3",
  mediumpurple: "9370db",
  mediumseagreen: "3cb371",
  mediumslateblue: "7b68ee",
  mediumspringgreen: "00fa9a",
  mediumturquoise: "48d1cc",
  mediumvioletred: "c71585",
  midnightblue: "191970",
  mintcream: "f5fffa",
  mistyrose: "ffe4e1",
  moccasin: "ffe4b5",
  navajowhite: "ffdead",
  navy: "000080",
  oldlace: "fdf5e6",
  olive: "808000",
  olivedrab: "6b8e23",
  orange: "ffa500",
  orangered: "ff4500",
  orchid: "da70d6",
  palegoldenrod: "eee8aa",
  palegreen: "98fb98",
  paleturquoise: "afeeee",
  palevioletred: "db7093",
  papayawhip: "ffefd5",
  peachpuff: "ffdab9",
  peru: "cd853f",
  pink: "ffc0cb",
  plum: "dda0dd",
  powderblue: "b0e0e6",
  purple: "800080",
  rebeccapurple: "663399",
  red: "f00",
  rosybrown: "bc8f8f",
  royalblue: "4169e1",
  saddlebrown: "8b4513",
  salmon: "fa8072",
  sandybrown: "f4a460",
  seagreen: "2e8b57",
  seashell: "fff5ee",
  sienna: "a0522d",
  silver: "c0c0c0",
  skyblue: "87ceeb",
  slateblue: "6a5acd",
  slategray: "708090",
  slategrey: "708090",
  snow: "fffafa",
  springgreen: "00ff7f",
  steelblue: "4682b4",
  tan: "d2b48c",
  teal: "008080",
  thistle: "d8bfd8",
  tomato: "ff6347",
  turquoise: "40e0d0",
  violet: "ee82ee",
  wheat: "f5deb3",
  white: "fff",
  whitesmoke: "f5f5f5",
  yellow: "ff0",
  yellowgreen: "9acd32"
};
var hexNames = tinycolor.hexNames = flip(names);
function flip(o2) {
  var flipped = {};
  for (var i2 in o2) {
    if (o2.hasOwnProperty(i2)) {
      flipped[o2[i2]] = i2;
    }
  }
  return flipped;
}
function boundAlpha(a2) {
  a2 = parseFloat(a2);
  if (isNaN(a2) || a2 < 0 || a2 > 1) {
    a2 = 1;
  }
  return a2;
}
function bound01(n2, max2) {
  if (isOnePointZero(n2)) n2 = "100%";
  var processPercent = isPercentage(n2);
  n2 = Math.min(max2, Math.max(0, parseFloat(n2)));
  if (processPercent) {
    n2 = parseInt(n2 * max2, 10) / 100;
  }
  if (Math.abs(n2 - max2) < 1e-6) {
    return 1;
  }
  return n2 % max2 / parseFloat(max2);
}
function clamp01(val) {
  return Math.min(1, Math.max(0, val));
}
function parseIntFromHex(val) {
  return parseInt(val, 16);
}
function isOnePointZero(n2) {
  return typeof n2 == "string" && n2.indexOf(".") != -1 && parseFloat(n2) === 1;
}
function isPercentage(n2) {
  return typeof n2 === "string" && n2.indexOf("%") != -1;
}
function pad2(c2) {
  return c2.length == 1 ? "0" + c2 : "" + c2;
}
function convertToPercentage(n2) {
  if (n2 <= 1) {
    n2 = n2 * 100 + "%";
  }
  return n2;
}
function convertDecimalToHex(d2) {
  return Math.round(parseFloat(d2) * 255).toString(16);
}
function convertHexToDecimal(h2) {
  return parseIntFromHex(h2) / 255;
}
var matchers = function() {
  var CSS_INTEGER = "[-\\+]?\\d+%?";
  var CSS_NUMBER = "[-\\+]?\\d*\\.\\d+%?";
  var CSS_UNIT = "(?:" + CSS_NUMBER + ")|(?:" + CSS_INTEGER + ")";
  var PERMISSIVE_MATCH3 = "[\\s|\\(]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")\\s*\\)?";
  var PERMISSIVE_MATCH4 = "[\\s|\\(]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")\\s*\\)?";
  return {
    CSS_UNIT: new RegExp(CSS_UNIT),
    rgb: new RegExp("rgb" + PERMISSIVE_MATCH3),
    rgba: new RegExp("rgba" + PERMISSIVE_MATCH4),
    hsl: new RegExp("hsl" + PERMISSIVE_MATCH3),
    hsla: new RegExp("hsla" + PERMISSIVE_MATCH4),
    hsv: new RegExp("hsv" + PERMISSIVE_MATCH3),
    hsva: new RegExp("hsva" + PERMISSIVE_MATCH4),
    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
  };
}();
function isValidCSSUnit(color) {
  return !!matchers.CSS_UNIT.exec(color);
}
function stringInputToObject(color) {
  color = color.replace(trimLeft, "").replace(trimRight, "").toLowerCase();
  var named = false;
  if (names[color]) {
    color = names[color];
    named = true;
  } else if (color == "transparent") {
    return {
      r: 0,
      g: 0,
      b: 0,
      a: 0,
      format: "name"
    };
  }
  var match;
  if (match = matchers.rgb.exec(color)) {
    return {
      r: match[1],
      g: match[2],
      b: match[3]
    };
  }
  if (match = matchers.rgba.exec(color)) {
    return {
      r: match[1],
      g: match[2],
      b: match[3],
      a: match[4]
    };
  }
  if (match = matchers.hsl.exec(color)) {
    return {
      h: match[1],
      s: match[2],
      l: match[3]
    };
  }
  if (match = matchers.hsla.exec(color)) {
    return {
      h: match[1],
      s: match[2],
      l: match[3],
      a: match[4]
    };
  }
  if (match = matchers.hsv.exec(color)) {
    return {
      h: match[1],
      s: match[2],
      v: match[3]
    };
  }
  if (match = matchers.hsva.exec(color)) {
    return {
      h: match[1],
      s: match[2],
      v: match[3],
      a: match[4]
    };
  }
  if (match = matchers.hex8.exec(color)) {
    return {
      r: parseIntFromHex(match[1]),
      g: parseIntFromHex(match[2]),
      b: parseIntFromHex(match[3]),
      a: convertHexToDecimal(match[4]),
      format: named ? "name" : "hex8"
    };
  }
  if (match = matchers.hex6.exec(color)) {
    return {
      r: parseIntFromHex(match[1]),
      g: parseIntFromHex(match[2]),
      b: parseIntFromHex(match[3]),
      format: named ? "name" : "hex"
    };
  }
  if (match = matchers.hex4.exec(color)) {
    return {
      r: parseIntFromHex(match[1] + "" + match[1]),
      g: parseIntFromHex(match[2] + "" + match[2]),
      b: parseIntFromHex(match[3] + "" + match[3]),
      a: convertHexToDecimal(match[4] + "" + match[4]),
      format: named ? "name" : "hex8"
    };
  }
  if (match = matchers.hex3.exec(color)) {
    return {
      r: parseIntFromHex(match[1] + "" + match[1]),
      g: parseIntFromHex(match[2] + "" + match[2]),
      b: parseIntFromHex(match[3] + "" + match[3]),
      format: named ? "name" : "hex"
    };
  }
  return false;
}
function validateWCAG2Parms(parms) {
  var level, size;
  parms = parms || {
    level: "AA",
    size: "small"
  };
  level = (parms.level || "AA").toUpperCase();
  size = (parms.size || "small").toLowerCase();
  if (level !== "AA" && level !== "AAA") {
    level = "AA";
  }
  if (size !== "small" && size !== "large") {
    size = "small";
  }
  return {
    level,
    size
  };
}

// ../../node_modules/.pnpm/vue3-colorpicker@2.3.0_@aes_9843de7cc8b629dfff6843adf764afda/node_modules/vue3-colorpicker/index.es.js
var import_gradient_parser = __toESM(require_node());

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/enums.js
var top = "top";
var bottom = "bottom";
var right = "right";
var left = "left";
var auto = "auto";
var basePlacements = [top, bottom, right, left];
var start = "start";
var end = "end";
var clippingParents = "clippingParents";
var viewport = "viewport";
var popper = "popper";
var reference = "reference";
var variationPlacements = basePlacements.reduce(function(acc, placement) {
  return acc.concat([placement + "-" + start, placement + "-" + end]);
}, []);
var placements = [].concat(basePlacements, [auto]).reduce(function(acc, placement) {
  return acc.concat([placement, placement + "-" + start, placement + "-" + end]);
}, []);
var beforeRead = "beforeRead";
var read = "read";
var afterRead = "afterRead";
var beforeMain = "beforeMain";
var main = "main";
var afterMain = "afterMain";
var beforeWrite = "beforeWrite";
var write = "write";
var afterWrite = "afterWrite";
var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js
function getNodeName(element) {
  return element ? (element.nodeName || "").toLowerCase() : null;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindow.js
function getWindow(node) {
  if (node == null) {
    return window;
  }
  if (node.toString() !== "[object Window]") {
    var ownerDocument = node.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView || window : window;
  }
  return node;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js
function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
  if (typeof ShadowRoot === "undefined") {
    return false;
  }
  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/applyStyles.js
function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function(name) {
    var style = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name];
    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    }
    Object.assign(element.style, style);
    Object.keys(attributes).forEach(function(name2) {
      var value = attributes[name2];
      if (value === false) {
        element.removeAttribute(name2);
      } else {
        element.setAttribute(name2, value === true ? "" : value);
      }
    });
  });
}
function effect(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;
  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }
  return function() {
    Object.keys(state.elements).forEach(function(name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]);
      var style = styleProperties.reduce(function(style2, property) {
        style2[property] = "";
        return style2;
      }, {});
      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }
      Object.assign(element.style, style);
      Object.keys(attributes).forEach(function(attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
}
var applyStyles_default = {
  name: "applyStyles",
  enabled: true,
  phase: "write",
  fn: applyStyles,
  effect,
  requires: ["computeStyles"]
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getBasePlacement.js
function getBasePlacement(placement) {
  return placement.split("-")[0];
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/math.js
var max = Math.max;
var min = Math.min;
var round = Math.round;

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/userAgent.js
function getUAString() {
  var uaData = navigator.userAgentData;
  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {
    return uaData.brands.map(function(item) {
      return item.brand + "/" + item.version;
    }).join(" ");
  }
  return navigator.userAgent;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js
function isLayoutViewport() {
  return !/^((?!chrome|android).)*safari/i.test(getUAString());
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js
function getBoundingClientRect(element, includeScale, isFixedStrategy) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  var clientRect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;
  if (includeScale && isHTMLElement(element)) {
    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
  }
  var _ref = isElement(element) ? getWindow(element) : window, visualViewport = _ref.visualViewport;
  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
  var x2 = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
  var y2 = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
  var width = clientRect.width / scaleX;
  var height = clientRect.height / scaleY;
  return {
    width,
    height,
    top: y2,
    right: x2 + width,
    bottom: y2 + height,
    left: x2,
    x: x2,
    y: y2
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js
function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element);
  var width = element.offsetWidth;
  var height = element.offsetHeight;
  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }
  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }
  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width,
    height
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/contains.js
function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode();
  if (parent.contains(child)) {
    return true;
  } else if (rootNode && isShadowRoot(rootNode)) {
    var next = child;
    do {
      if (next && parent.isSameNode(next)) {
        return true;
      }
      next = next.parentNode || next.host;
    } while (next);
  }
  return false;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js
function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js
function isTableElement(element) {
  return ["table", "td", "th"].indexOf(getNodeName(element)) >= 0;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js
function getDocumentElement(element) {
  return ((isElement(element) ? element.ownerDocument : (
    // $FlowFixMe[prop-missing]
    element.document
  )) || window.document).documentElement;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js
function getParentNode(element) {
  if (getNodeName(element) === "html") {
    return element;
  }
  return (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || // DOM Element detected
    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element)
  );
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js
function getTrueOffsetParent(element) {
  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
  getComputedStyle(element).position === "fixed") {
    return null;
  }
  return element.offsetParent;
}
function getContainingBlock(element) {
  var isFirefox = /firefox/i.test(getUAString());
  var isIE = /Trident/i.test(getUAString());
  if (isIE && isHTMLElement(element)) {
    var elementCss = getComputedStyle(element);
    if (elementCss.position === "fixed") {
      return null;
    }
  }
  var currentNode = getParentNode(element);
  if (isShadowRoot(currentNode)) {
    currentNode = currentNode.host;
  }
  while (isHTMLElement(currentNode) && ["html", "body"].indexOf(getNodeName(currentNode)) < 0) {
    var css = getComputedStyle(currentNode);
    if (css.transform !== "none" || css.perspective !== "none" || css.contain === "paint" || ["transform", "perspective"].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === "filter" || isFirefox && css.filter && css.filter !== "none") {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }
  return null;
}
function getOffsetParent(element) {
  var window2 = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);
  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === "static") {
    offsetParent = getTrueOffsetParent(offsetParent);
  }
  if (offsetParent && (getNodeName(offsetParent) === "html" || getNodeName(offsetParent) === "body" && getComputedStyle(offsetParent).position === "static")) {
    return window2;
  }
  return offsetParent || getContainingBlock(element) || window2;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js
function getMainAxisFromPlacement(placement) {
  return ["top", "bottom"].indexOf(placement) >= 0 ? "x" : "y";
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/within.js
function within(min2, value, max2) {
  return max(min2, min(value, max2));
}
function withinMaxClamp(min2, value, max2) {
  var v2 = within(min2, value, max2);
  return v2 > max2 ? max2 : v2;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js
function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js
function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/expandToHashMap.js
function expandToHashMap(value, keys) {
  return keys.reduce(function(hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/arrow.js
var toPaddingObject = function toPaddingObject2(padding, state) {
  padding = typeof padding === "function" ? padding(Object.assign({}, state.rects, {
    placement: state.placement
  })) : padding;
  return mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
};
function arrow(_ref) {
  var _state$modifiersData$;
  var state = _ref.state, name = _ref.name, options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? "height" : "width";
  if (!arrowElement || !popperOffsets2) {
    return;
  }
  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === "y" ? top : left;
  var maxProp = axis === "y" ? bottom : right;
  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets2[axis] - state.rects.popper[len];
  var startDiff = popperOffsets2[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent ? axis === "y" ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
  var centerToReference = endDiff / 2 - startDiff / 2;
  var min2 = paddingObject[minProp];
  var max2 = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset2 = within(min2, center, max2);
  var axisProp = axis;
  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset2, _state$modifiersData$.centerOffset = offset2 - center, _state$modifiersData$);
}
function effect2(_ref2) {
  var state = _ref2.state, options = _ref2.options;
  var _options$element = options.element, arrowElement = _options$element === void 0 ? "[data-popper-arrow]" : _options$element;
  if (arrowElement == null) {
    return;
  }
  if (typeof arrowElement === "string") {
    arrowElement = state.elements.popper.querySelector(arrowElement);
    if (!arrowElement) {
      return;
    }
  }
  if (!contains(state.elements.popper, arrowElement)) {
    return;
  }
  state.elements.arrow = arrowElement;
}
var arrow_default = {
  name: "arrow",
  enabled: true,
  phase: "main",
  fn: arrow,
  effect: effect2,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getVariation.js
function getVariation(placement) {
  return placement.split("-")[1];
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/computeStyles.js
var unsetSides = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function roundOffsetsByDPR(_ref, win) {
  var x2 = _ref.x, y2 = _ref.y;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(x2 * dpr) / dpr || 0,
    y: round(y2 * dpr) / dpr || 0
  };
}
function mapToStyles(_ref2) {
  var _Object$assign2;
  var popper2 = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;
  var _offsets$x = offsets.x, x2 = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y2 = _offsets$y === void 0 ? 0 : _offsets$y;
  var _ref3 = typeof roundOffsets === "function" ? roundOffsets({
    x: x2,
    y: y2
  }) : {
    x: x2,
    y: y2
  };
  x2 = _ref3.x;
  y2 = _ref3.y;
  var hasX = offsets.hasOwnProperty("x");
  var hasY = offsets.hasOwnProperty("y");
  var sideX = left;
  var sideY = top;
  var win = window;
  if (adaptive) {
    var offsetParent = getOffsetParent(popper2);
    var heightProp = "clientHeight";
    var widthProp = "clientWidth";
    if (offsetParent === getWindow(popper2)) {
      offsetParent = getDocumentElement(popper2);
      if (getComputedStyle(offsetParent).position !== "static" && position === "absolute") {
        heightProp = "scrollHeight";
        widthProp = "scrollWidth";
      }
    }
    offsetParent = offsetParent;
    if (placement === top || (placement === left || placement === right) && variation === end) {
      sideY = bottom;
      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : (
        // $FlowFixMe[prop-missing]
        offsetParent[heightProp]
      );
      y2 -= offsetY - popperRect.height;
      y2 *= gpuAcceleration ? 1 : -1;
    }
    if (placement === left || (placement === top || placement === bottom) && variation === end) {
      sideX = right;
      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : (
        // $FlowFixMe[prop-missing]
        offsetParent[widthProp]
      );
      x2 -= offsetX - popperRect.width;
      x2 *= gpuAcceleration ? 1 : -1;
    }
  }
  var commonStyles = Object.assign({
    position
  }, adaptive && unsetSides);
  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
    x: x2,
    y: y2
  }, getWindow(popper2)) : {
    x: x2,
    y: y2
  };
  x2 = _ref4.x;
  y2 = _ref4.y;
  if (gpuAcceleration) {
    var _Object$assign;
    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? "0" : "", _Object$assign[sideX] = hasX ? "0" : "", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x2 + "px, " + y2 + "px)" : "translate3d(" + x2 + "px, " + y2 + "px, 0)", _Object$assign));
  }
  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y2 + "px" : "", _Object$assign2[sideX] = hasX ? x2 + "px" : "", _Object$assign2.transform = "", _Object$assign2));
}
function computeStyles(_ref5) {
  var state = _ref5.state, options = _ref5.options;
  var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;
  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration,
    isFixed: state.options.strategy === "fixed"
  };
  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.popperOffsets,
      position: state.options.strategy,
      adaptive,
      roundOffsets
    })));
  }
  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.arrow,
      position: "absolute",
      adaptive: false,
      roundOffsets
    })));
  }
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-placement": state.placement
  });
}
var computeStyles_default = {
  name: "computeStyles",
  enabled: true,
  phase: "beforeWrite",
  fn: computeStyles,
  data: {}
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/eventListeners.js
var passive = {
  passive: true
};
function effect3(_ref) {
  var state = _ref.state, instance = _ref.instance, options = _ref.options;
  var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;
  var window2 = getWindow(state.elements.popper);
  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);
  if (scroll) {
    scrollParents.forEach(function(scrollParent) {
      scrollParent.addEventListener("scroll", instance.update, passive);
    });
  }
  if (resize) {
    window2.addEventListener("resize", instance.update, passive);
  }
  return function() {
    if (scroll) {
      scrollParents.forEach(function(scrollParent) {
        scrollParent.removeEventListener("scroll", instance.update, passive);
      });
    }
    if (resize) {
      window2.removeEventListener("resize", instance.update, passive);
    }
  };
}
var eventListeners_default = {
  name: "eventListeners",
  enabled: true,
  phase: "write",
  fn: function fn() {
  },
  effect: effect3,
  data: {}
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js
var hash = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function(matched) {
    return hash[matched];
  });
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js
var hash2 = {
  start: "end",
  end: "start"
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function(matched) {
    return hash2[matched];
  });
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js
function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft,
    scrollTop
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js
function getWindowScrollBarX(element) {
  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js
function getViewportRect(element, strategy) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x2 = 0;
  var y2 = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    var layoutViewport = isLayoutViewport();
    if (layoutViewport || !layoutViewport && strategy === "fixed") {
      x2 = visualViewport.offsetLeft;
      y2 = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x2 + getWindowScrollBarX(element),
    y: y2
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js
function getDocumentRect(element) {
  var _element$ownerDocumen;
  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
  var x2 = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y2 = -winScroll.scrollTop;
  if (getComputedStyle(body || html).direction === "rtl") {
    x2 += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }
  return {
    width,
    height,
    x: x2,
    y: y2
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js
function isScrollParent(element) {
  var _getComputedStyle = getComputedStyle(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;
  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js
function getScrollParent(node) {
  if (["html", "body", "#document"].indexOf(getNodeName(node)) >= 0) {
    return node.ownerDocument.body;
  }
  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }
  return getScrollParent(getParentNode(node));
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js
function listScrollParents(element, list) {
  var _element$ownerDocumen;
  if (list === void 0) {
    list = [];
  }
  var scrollParent = getScrollParent(element);
  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
  var updatedList = list.concat(target);
  return isBody ? updatedList : (
    // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
    updatedList.concat(listScrollParents(getParentNode(target)))
  );
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/rectToClientRect.js
function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height
  });
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js
function getInnerBoundingClientRect(element, strategy) {
  var rect = getBoundingClientRect(element, false, strategy === "fixed");
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}
function getClientRectFromMixedType(element, clippingParent, strategy) {
  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
}
function getClippingParents(element) {
  var clippingParents2 = listScrollParents(getParentNode(element));
  var canEscapeClipping = ["absolute", "fixed"].indexOf(getComputedStyle(element).position) >= 0;
  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;
  if (!isElement(clipperElement)) {
    return [];
  }
  return clippingParents2.filter(function(clippingParent) {
    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== "body";
  });
}
function getClippingRect(element, boundary, rootBoundary, strategy) {
  var mainClippingParents = boundary === "clippingParents" ? getClippingParents(element) : [].concat(boundary);
  var clippingParents2 = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents2[0];
  var clippingRect = clippingParents2.reduce(function(accRect, clippingParent) {
    var rect = getClientRectFromMixedType(element, clippingParent, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromMixedType(element, firstClippingParent, strategy));
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeOffsets.js
function computeOffsets(_ref) {
  var reference2 = _ref.reference, element = _ref.element, placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference2.x + reference2.width / 2 - element.width / 2;
  var commonY = reference2.y + reference2.height / 2 - element.height / 2;
  var offsets;
  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference2.y - element.height
      };
      break;
    case bottom:
      offsets = {
        x: commonX,
        y: reference2.y + reference2.height
      };
      break;
    case right:
      offsets = {
        x: reference2.x + reference2.width,
        y: commonY
      };
      break;
    case left:
      offsets = {
        x: reference2.x - element.width,
        y: commonY
      };
      break;
    default:
      offsets = {
        x: reference2.x,
        y: reference2.y
      };
  }
  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
  if (mainAxis != null) {
    var len = mainAxis === "y" ? "height" : "width";
    switch (variation) {
      case start:
        offsets[mainAxis] = offsets[mainAxis] - (reference2[len] / 2 - element[len] / 2);
        break;
      case end:
        offsets[mainAxis] = offsets[mainAxis] + (reference2[len] / 2 - element[len] / 2);
        break;
      default:
    }
  }
  return offsets;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/detectOverflow.js
function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets2 = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: "absolute",
    placement
  });
  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets2));
  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect;
  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right: elementClientRect.right - clippingClientRect.right + paddingObject.right
  };
  var offsetData = state.modifiersData.offset;
  if (elementContext === popper && offsetData) {
    var offset2 = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function(key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? "y" : "x";
      overflowOffsets[key] += offset2[axis] * multiply;
    });
  }
  return overflowOffsets;
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js
function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements2 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function(placement2) {
    return getVariation(placement2) === variation;
  }) : basePlacements;
  var allowedPlacements = placements2.filter(function(placement2) {
    return allowedAutoPlacements.indexOf(placement2) >= 0;
  });
  if (allowedPlacements.length === 0) {
    allowedPlacements = placements2;
  }
  var overflows = allowedPlacements.reduce(function(acc, placement2) {
    acc[placement2] = detectOverflow(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding
    })[getBasePlacement(placement2)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function(a2, b2) {
    return overflows[a2] - overflows[b2];
  });
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/flip.js
function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }
  var oppositePlacement = getOppositePlacement(placement);
  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];
}
function flip2(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  if (state.modifiersData[name]._skip) {
    return;
  }
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));
  var placements2 = [preferredPlacement].concat(fallbackPlacements).reduce(function(acc, placement2) {
    return acc.concat(getBasePlacement(placement2) === auto ? computeAutoPlacement(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding,
      flipVariations,
      allowedAutoPlacements
    }) : placement2);
  }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = /* @__PURE__ */ new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements2[0];
  for (var i2 = 0; i2 < placements2.length; i2++) {
    var placement = placements2[i2];
    var _basePlacement = getBasePlacement(placement);
    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? "width" : "height";
    var overflow = detectOverflow(state, {
      placement,
      boundary,
      rootBoundary,
      altBoundary,
      padding
    });
    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;
    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }
    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];
    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }
    if (checkAltAxis) {
      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
    }
    if (checks.every(function(check) {
      return check;
    })) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }
    checksMap.set(placement, checks);
  }
  if (makeFallbackChecks) {
    var numberOfChecks = flipVariations ? 3 : 1;
    var _loop = function _loop2(_i2) {
      var fittingPlacement = placements2.find(function(placement2) {
        var checks2 = checksMap.get(placement2);
        if (checks2) {
          return checks2.slice(0, _i2).every(function(check) {
            return check;
          });
        }
      });
      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return "break";
      }
    };
    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);
      if (_ret === "break") break;
    }
  }
  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
}
var flip_default = {
  name: "flip",
  enabled: true,
  phase: "main",
  fn: flip2,
  requiresIfExists: ["offset"],
  data: {
    _skip: false
  }
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/hide.js
function getSideOffsets(overflow, rect, preventedOffsets) {
  if (preventedOffsets === void 0) {
    preventedOffsets = {
      x: 0,
      y: 0
    };
  }
  return {
    top: overflow.top - rect.height - preventedOffsets.y,
    right: overflow.right - rect.width + preventedOffsets.x,
    bottom: overflow.bottom - rect.height + preventedOffsets.y,
    left: overflow.left - rect.width - preventedOffsets.x
  };
}
function isAnySideFullyClipped(overflow) {
  return [top, right, bottom, left].some(function(side) {
    return overflow[side] >= 0;
  });
}
function hide(_ref) {
  var state = _ref.state, name = _ref.name;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var preventedOffsets = state.modifiersData.preventOverflow;
  var referenceOverflow = detectOverflow(state, {
    elementContext: "reference"
  });
  var popperAltOverflow = detectOverflow(state, {
    altBoundary: true
  });
  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);
  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);
  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
  state.modifiersData[name] = {
    referenceClippingOffsets,
    popperEscapeOffsets,
    isReferenceHidden,
    hasPopperEscaped
  };
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-reference-hidden": isReferenceHidden,
    "data-popper-escaped": hasPopperEscaped
  });
}
var hide_default = {
  name: "hide",
  enabled: true,
  phase: "main",
  requiresIfExists: ["preventOverflow"],
  fn: hide
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/offset.js
function distanceAndSkiddingToXY(placement, rects, offset2) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;
  var _ref = typeof offset2 === "function" ? offset2(Object.assign({}, rects, {
    placement
  })) : offset2, skidding = _ref[0], distance = _ref[1];
  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0 ? {
    x: distance,
    y: skidding
  } : {
    x: skidding,
    y: distance
  };
}
function offset(_ref2) {
  var state = _ref2.state, options = _ref2.options, name = _ref2.name;
  var _options$offset = options.offset, offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function(acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement], x2 = _data$state$placement.x, y2 = _data$state$placement.y;
  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x2;
    state.modifiersData.popperOffsets.y += y2;
  }
  state.modifiersData[name] = data;
}
var offset_default = {
  name: "offset",
  enabled: true,
  phase: "main",
  requires: ["popperOffsets"],
  fn: offset
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js
function popperOffsets(_ref) {
  var state = _ref.state, name = _ref.name;
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: "absolute",
    placement: state.placement
  });
}
var popperOffsets_default = {
  name: "popperOffsets",
  enabled: true,
  phase: "read",
  fn: popperOffsets,
  data: {}
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getAltAxis.js
function getAltAxis(axis) {
  return axis === "x" ? "y" : "x";
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js
function preventOverflow(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary,
    rootBoundary,
    padding,
    altBoundary
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue = typeof tetherOffset === "function" ? tetherOffset(Object.assign({}, state.rects, {
    placement: state.placement
  })) : tetherOffset;
  var normalizedTetherOffsetValue = typeof tetherOffsetValue === "number" ? {
    mainAxis: tetherOffsetValue,
    altAxis: tetherOffsetValue
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, tetherOffsetValue);
  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
  var data = {
    x: 0,
    y: 0
  };
  if (!popperOffsets2) {
    return;
  }
  if (checkMainAxis) {
    var _offsetModifierState$;
    var mainSide = mainAxis === "y" ? top : left;
    var altSide = mainAxis === "y" ? bottom : right;
    var len = mainAxis === "y" ? "height" : "width";
    var offset2 = popperOffsets2[mainAxis];
    var min2 = offset2 + overflow[mainSide];
    var max2 = offset2 - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len];
    var arrowElement = state.elements.arrow;
    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
      width: 0,
      height: 0
    };
    var arrowPaddingObject = state.modifiersData["arrow#persistent"] ? state.modifiersData["arrow#persistent"].padding : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide];
    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent ? mainAxis === "y" ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
    var tetherMin = offset2 + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = offset2 + maxOffset - offsetModifierValue;
    var preventedOffset = within(tether ? min(min2, tetherMin) : min2, offset2, tether ? max(max2, tetherMax) : max2);
    popperOffsets2[mainAxis] = preventedOffset;
    data[mainAxis] = preventedOffset - offset2;
  }
  if (checkAltAxis) {
    var _offsetModifierState$2;
    var _mainSide = mainAxis === "x" ? top : left;
    var _altSide = mainAxis === "x" ? bottom : right;
    var _offset = popperOffsets2[altAxis];
    var _len = altAxis === "y" ? "height" : "width";
    var _min = _offset + overflow[_mainSide];
    var _max = _offset - overflow[_altSide];
    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;
    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;
    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;
    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;
    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);
    popperOffsets2[altAxis] = _preventedOffset;
    data[altAxis] = _preventedOffset - _offset;
  }
  state.modifiersData[name] = data;
}
var preventOverflow_default = {
  name: "preventOverflow",
  enabled: true,
  phase: "main",
  fn: preventOverflow,
  requiresIfExists: ["offset"]
};

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js
function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js
function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js
function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = round(rect.width) / element.offsetWidth || 1;
  var scaleY = round(rect.height) / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
}
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  var offsets = {
    x: 0,
    y: 0
  };
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || // https://github.com/popperjs/popper-core/issues/1078
    isScrollParent(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/orderModifiers.js
function order(modifiers) {
  var map = /* @__PURE__ */ new Map();
  var visited = /* @__PURE__ */ new Set();
  var result = [];
  modifiers.forEach(function(modifier) {
    map.set(modifier.name, modifier);
  });
  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
    requires.forEach(function(dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);
        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }
  modifiers.forEach(function(modifier) {
    if (!visited.has(modifier.name)) {
      sort(modifier);
    }
  });
  return result;
}
function orderModifiers(modifiers) {
  var orderedModifiers = order(modifiers);
  return modifierPhases.reduce(function(acc, phase) {
    return acc.concat(orderedModifiers.filter(function(modifier) {
      return modifier.phase === phase;
    }));
  }, []);
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/debounce.js
function debounce(fn3) {
  var pending;
  return function() {
    if (!pending) {
      pending = new Promise(function(resolve) {
        Promise.resolve().then(function() {
          pending = void 0;
          resolve(fn3());
        });
      });
    }
    return pending;
  };
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergeByName.js
function mergeByName(modifiers) {
  var merged = modifiers.reduce(function(merged2, current) {
    var existing = merged2[current.name];
    merged2[current.name] = existing ? Object.assign({}, existing, current, {
      options: Object.assign({}, existing.options, current.options),
      data: Object.assign({}, existing.data, current.data)
    }) : current;
    return merged2;
  }, {});
  return Object.keys(merged).map(function(key) {
    return merged[key];
  });
}

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/createPopper.js
var DEFAULT_OPTIONS = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function areValidElements() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return !args.some(function(element) {
    return !(element && typeof element.getBoundingClientRect === "function");
  });
}
function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }
  var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers3 = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
  return function createPopper4(reference2, popper2, options) {
    if (options === void 0) {
      options = defaultOptions;
    }
    var state = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference2,
        popper: popper2
      },
      attributes: {},
      styles: {}
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state,
      setOptions: function setOptions(setOptionsAction) {
        var options2 = typeof setOptionsAction === "function" ? setOptionsAction(state.options) : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign({}, defaultOptions, state.options, options2);
        state.scrollParents = {
          reference: isElement(reference2) ? listScrollParents(reference2) : reference2.contextElement ? listScrollParents(reference2.contextElement) : [],
          popper: listScrollParents(popper2)
        };
        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers3, state.options.modifiers)));
        state.orderedModifiers = orderedModifiers.filter(function(m2) {
          return m2.enabled;
        });
        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }
        var _state$elements = state.elements, reference3 = _state$elements.reference, popper3 = _state$elements.popper;
        if (!areValidElements(reference3, popper3)) {
          return;
        }
        state.rects = {
          reference: getCompositeRect(reference3, getOffsetParent(popper3), state.options.strategy === "fixed"),
          popper: getLayoutRect(popper3)
        };
        state.reset = false;
        state.placement = state.options.placement;
        state.orderedModifiers.forEach(function(modifier) {
          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
        });
        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }
          var _state$orderedModifie = state.orderedModifiers[index], fn3 = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;
          if (typeof fn3 === "function") {
            state = fn3({
              state,
              options: _options,
              name,
              instance
            }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function() {
        return new Promise(function(resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      }
    };
    if (!areValidElements(reference2, popper2)) {
      return instance;
    }
    instance.setOptions(options).then(function(state2) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state2);
      }
    });
    function runModifierEffects() {
      state.orderedModifiers.forEach(function(_ref) {
        var name = _ref.name, _ref$options = _ref.options, options2 = _ref$options === void 0 ? {} : _ref$options, effect4 = _ref.effect;
        if (typeof effect4 === "function") {
          var cleanupFn = effect4({
            state,
            name,
            instance,
            options: options2
          });
          var noopFn = function noopFn2() {
          };
          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }
    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function(fn3) {
        return fn3();
      });
      effectCleanupFns = [];
    }
    return instance;
  };
}
var createPopper = popperGenerator();

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper-lite.js
var defaultModifiers = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default];
var createPopper2 = popperGenerator({
  defaultModifiers
});

// ../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js
var defaultModifiers2 = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default, offset_default, flip_default, preventOverflow_default, arrow_default, hide_default];
var createPopper3 = popperGenerator({
  defaultModifiers: defaultModifiers2
});

// ../../node_modules/.pnpm/@aesoper+normal-utils@0.1.5/node_modules/@aesoper/normal-utils/NormalUtils.es.js
var t = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {};
function e(t2) {
  var e2 = { exports: {} };
  return t2(e2, e2.exports), e2.exports;
}
var n = function(t2) {
  return t2 && t2.Math == Math && t2;
};
var r = n("object" == typeof globalThis && globalThis) || n("object" == typeof window && window) || n("object" == typeof self && self) || n("object" == typeof t && t) || /* @__PURE__ */ function() {
  return this;
}() || Function("return this")();
var o = function(t2) {
  try {
    return !!t2();
  } catch (t3) {
    return true;
  }
};
var i = !o(function() {
  return 7 != Object.defineProperty({}, 1, { get: function() {
    return 7;
  } })[1];
});
var u = {}.propertyIsEnumerable;
var a = Object.getOwnPropertyDescriptor;
var c = { f: a && !u.call({ 1: 2 }, 1) ? function(t2) {
  var e2 = a(this, t2);
  return !!e2 && e2.enumerable;
} : u };
var l = function(t2, e2) {
  return { enumerable: !(1 & t2), configurable: !(2 & t2), writable: !(4 & t2), value: e2 };
};
var f = {}.toString;
var s = function(t2) {
  return f.call(t2).slice(8, -1);
};
var d = "".split;
var v = o(function() {
  return !Object("z").propertyIsEnumerable(0);
}) ? function(t2) {
  return "String" == s(t2) ? d.call(t2, "") : Object(t2);
} : Object;
var p = function(t2) {
  if (null == t2) throw TypeError("Can't call method on " + t2);
  return t2;
};
var g = function(t2) {
  return v(p(t2));
};
var h = function(t2) {
  return "object" == typeof t2 ? null !== t2 : "function" == typeof t2;
};
var y = function(t2, e2) {
  if (!h(t2)) return t2;
  var n2, r2;
  if (e2 && "function" == typeof (n2 = t2.toString) && !h(r2 = n2.call(t2))) return r2;
  if ("function" == typeof (n2 = t2.valueOf) && !h(r2 = n2.call(t2))) return r2;
  if (!e2 && "function" == typeof (n2 = t2.toString) && !h(r2 = n2.call(t2))) return r2;
  throw TypeError("Can't convert object to primitive value");
};
var m = {}.hasOwnProperty;
var S = function(t2, e2) {
  return m.call(t2, e2);
};
var x = r.document;
var b = h(x) && h(x.createElement);
var E = function(t2) {
  return b ? x.createElement(t2) : {};
};
var w = !i && !o(function() {
  return 7 != Object.defineProperty(E("div"), "a", { get: function() {
    return 7;
  } }).a;
});
var O = Object.getOwnPropertyDescriptor;
var T = { f: i ? O : function(t2, e2) {
  if (t2 = g(t2), e2 = y(e2, true), w) try {
    return O(t2, e2);
  } catch (t3) {
  }
  if (S(t2, e2)) return l(!c.f.call(t2, e2), t2[e2]);
} };
var A = function(t2) {
  if (!h(t2)) throw TypeError(String(t2) + " is not an object");
  return t2;
};
var k = Object.defineProperty;
var R = { f: i ? k : function(t2, e2, n2) {
  if (A(t2), e2 = y(e2, true), A(n2), w) try {
    return k(t2, e2, n2);
  } catch (t3) {
  }
  if ("get" in n2 || "set" in n2) throw TypeError("Accessors not supported");
  return "value" in n2 && (t2[e2] = n2.value), t2;
} };
var I = i ? function(t2, e2, n2) {
  return R.f(t2, e2, l(1, n2));
} : function(t2, e2, n2) {
  return t2[e2] = n2, t2;
};
var j = function(t2, e2) {
  try {
    I(r, t2, e2);
  } catch (n2) {
    r[t2] = e2;
  }
  return e2;
};
var C = r["__core-js_shared__"] || j("__core-js_shared__", {});
var L = Function.toString;
"function" != typeof C.inspectSource && (C.inspectSource = function(t2) {
  return L.call(t2);
});
var P;
var M;
var _;
var D = C.inspectSource;
var U = r.WeakMap;
var N = "function" == typeof U && /native code/.test(D(U));
var F = e(function(t2) {
  (t2.exports = function(t3, e2) {
    return C[t3] || (C[t3] = void 0 !== e2 ? e2 : {});
  })("versions", []).push({ version: "3.8.3", mode: "global", copyright: "© 2021 Denis Pushkarev (zloirock.ru)" });
});
var W = 0;
var z = Math.random();
var $ = function(t2) {
  return "Symbol(" + String(void 0 === t2 ? "" : t2) + ")_" + (++W + z).toString(36);
};
var B = F("keys");
var Y = function(t2) {
  return B[t2] || (B[t2] = $(t2));
};
var G = {};
var H = r.WeakMap;
if (N) {
  X = C.state || (C.state = new H()), V = X.get, K = X.has, q2 = X.set;
  P = function(t2, e2) {
    return e2.facade = t2, q2.call(X, t2, e2), e2;
  }, M = function(t2) {
    return V.call(X, t2) || {};
  }, _ = function(t2) {
    return K.call(X, t2);
  };
} else {
  Q = Y("state");
  G[Q] = true, P = function(t2, e2) {
    return e2.facade = t2, I(t2, Q, e2), e2;
  }, M = function(t2) {
    return S(t2, Q) ? t2[Q] : {};
  }, _ = function(t2) {
    return S(t2, Q);
  };
}
var X;
var V;
var K;
var q2;
var Q;
var J = { set: P, get: M, has: _, enforce: function(t2) {
  return _(t2) ? M(t2) : P(t2, {});
}, getterFor: function(t2) {
  return function(e2) {
    var n2;
    if (!h(e2) || (n2 = M(e2)).type !== t2) throw TypeError("Incompatible receiver, " + t2 + " required");
    return n2;
  };
} };
var Z = e(function(t2) {
  var e2 = J.get, n2 = J.enforce, o2 = String(String).split("String");
  (t2.exports = function(t3, e3, i2, u2) {
    var a2, c2 = !!u2 && !!u2.unsafe, l2 = !!u2 && !!u2.enumerable, f2 = !!u2 && !!u2.noTargetGet;
    "function" == typeof i2 && ("string" != typeof e3 || S(i2, "name") || I(i2, "name", e3), (a2 = n2(i2)).source || (a2.source = o2.join("string" == typeof e3 ? e3 : ""))), t3 !== r ? (c2 ? !f2 && t3[e3] && (l2 = true) : delete t3[e3], l2 ? t3[e3] = i2 : I(t3, e3, i2)) : l2 ? t3[e3] = i2 : j(e3, i2);
  })(Function.prototype, "toString", function() {
    return "function" == typeof this && e2(this).source || D(this);
  });
});
var tt = r;
var et = function(t2) {
  return "function" == typeof t2 ? t2 : void 0;
};
var nt = function(t2, e2) {
  return arguments.length < 2 ? et(tt[t2]) || et(r[t2]) : tt[t2] && tt[t2][e2] || r[t2] && r[t2][e2];
};
var rt = Math.ceil;
var ot = Math.floor;
var it = function(t2) {
  return isNaN(t2 = +t2) ? 0 : (t2 > 0 ? ot : rt)(t2);
};
var ut = Math.min;
var at = function(t2) {
  return t2 > 0 ? ut(it(t2), 9007199254740991) : 0;
};
var ct = Math.max;
var lt = Math.min;
var ft = function(t2, e2) {
  var n2 = it(t2);
  return n2 < 0 ? ct(n2 + e2, 0) : lt(n2, e2);
};
var st = function(t2) {
  return function(e2, n2, r2) {
    var o2, i2 = g(e2), u2 = at(i2.length), a2 = ft(r2, u2);
    if (t2 && n2 != n2) {
      for (; u2 > a2; ) if ((o2 = i2[a2++]) != o2) return true;
    } else for (; u2 > a2; a2++) if ((t2 || a2 in i2) && i2[a2] === n2) return t2 || a2 || 0;
    return !t2 && -1;
  };
};
var dt = { includes: st(true), indexOf: st(false) };
var vt = dt.indexOf;
var pt = function(t2, e2) {
  var n2, r2 = g(t2), o2 = 0, i2 = [];
  for (n2 in r2) !S(G, n2) && S(r2, n2) && i2.push(n2);
  for (; e2.length > o2; ) S(r2, n2 = e2[o2++]) && (~vt(i2, n2) || i2.push(n2));
  return i2;
};
var gt = ["constructor", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "toLocaleString", "toString", "valueOf"];
var ht = gt.concat("length", "prototype");
var yt = { f: Object.getOwnPropertyNames || function(t2) {
  return pt(t2, ht);
} };
var mt = { f: Object.getOwnPropertySymbols };
var St = nt("Reflect", "ownKeys") || function(t2) {
  var e2 = yt.f(A(t2)), n2 = mt.f;
  return n2 ? e2.concat(n2(t2)) : e2;
};
var xt = function(t2, e2) {
  for (var n2 = St(e2), r2 = R.f, o2 = T.f, i2 = 0; i2 < n2.length; i2++) {
    var u2 = n2[i2];
    S(t2, u2) || r2(t2, u2, o2(e2, u2));
  }
};
var bt = /#|\.prototype\./;
var Et = function(t2, e2) {
  var n2 = Ot[wt(t2)];
  return n2 == At || n2 != Tt && ("function" == typeof e2 ? o(e2) : !!e2);
};
var wt = Et.normalize = function(t2) {
  return String(t2).replace(bt, ".").toLowerCase();
};
var Ot = Et.data = {};
var Tt = Et.NATIVE = "N";
var At = Et.POLYFILL = "P";
var kt = Et;
var Rt = T.f;
var It = function(t2, e2) {
  var n2, o2, i2, u2, a2, c2 = t2.target, l2 = t2.global, f2 = t2.stat;
  if (n2 = l2 ? r : f2 ? r[c2] || j(c2, {}) : (r[c2] || {}).prototype) for (o2 in e2) {
    if (u2 = e2[o2], i2 = t2.noTargetGet ? (a2 = Rt(n2, o2)) && a2.value : n2[o2], !kt(l2 ? o2 : c2 + (f2 ? "." : "#") + o2, t2.forced) && void 0 !== i2) {
      if (typeof u2 == typeof i2) continue;
      xt(u2, i2);
    }
    (t2.sham || i2 && i2.sham) && I(u2, "sham", true), Z(n2, o2, u2, t2);
  }
};
var jt = function(t2, e2) {
  var n2 = [][t2];
  return !!n2 && o(function() {
    n2.call(null, e2 || function() {
      throw 1;
    }, 1);
  });
};
var Ct = Object.defineProperty;
var Lt = {};
var Pt = function(t2) {
  throw t2;
};
var Mt = function(t2, e2) {
  if (S(Lt, t2)) return Lt[t2];
  e2 || (e2 = {});
  var n2 = [][t2], r2 = !!S(e2, "ACCESSORS") && e2.ACCESSORS, u2 = S(e2, 0) ? e2[0] : Pt, a2 = S(e2, 1) ? e2[1] : void 0;
  return Lt[t2] = !!n2 && !o(function() {
    if (r2 && !i) return true;
    var t3 = { length: -1 };
    r2 ? Ct(t3, 1, { enumerable: true, get: Pt }) : t3[1] = 1, n2.call(t3, u2, a2);
  });
};
var _t = dt.indexOf;
var Dt = [].indexOf;
var Ut = !!Dt && 1 / [1].indexOf(1, -0) < 0;
var Nt = jt("indexOf");
var Ft = Mt("indexOf", { ACCESSORS: true, 1: 0 });
function Wt(t2, e2) {
  if (!(t2 instanceof e2)) throw new TypeError("Cannot call a class as a function");
}
function zt(t2, e2) {
  for (var n2 = 0; n2 < e2.length; n2++) {
    var r2 = e2[n2];
    r2.enumerable = r2.enumerable || false, r2.configurable = true, "value" in r2 && (r2.writable = true), Object.defineProperty(t2, r2.key, r2);
  }
}
function $t(t2, e2, n2) {
  return e2 && zt(t2.prototype, e2), n2 && zt(t2, n2), t2;
}
It({ target: "Array", proto: true, forced: Ut || !Nt || !Ft }, { indexOf: function(t2) {
  return Ut ? Dt.apply(this, arguments) || 0 : _t(this, t2, arguments.length > 1 ? arguments[1] : void 0);
} });
var Bt = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "isInBrowser", value: function() {
    return "undefined" != typeof window;
  } }, { key: "isServer", value: function() {
    return "undefined" == typeof window;
  } }, { key: "getUA", value: function() {
    return t2.isInBrowser() ? window.navigator.userAgent.toLowerCase() : "";
  } }, { key: "isMobile", value: function() {
    return /Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(navigator.appVersion);
  } }, { key: "isOpera", value: function() {
    return -1 !== navigator.userAgent.indexOf("Opera");
  } }, { key: "isIE", value: function() {
    var e2 = t2.getUA();
    return "" !== e2 && e2.indexOf("msie") > 0;
  } }, { key: "isIE9", value: function() {
    var e2 = t2.getUA();
    return "" !== e2 && e2.indexOf("msie 9.0") > 0;
  } }, { key: "isEdge", value: function() {
    var e2 = t2.getUA();
    return "" !== e2 && e2.indexOf("edge/") > 0;
  } }, { key: "isChrome", value: function() {
    var e2 = t2.getUA();
    return "" !== e2 && /chrome\/\d+/.test(e2) && !t2.isEdge();
  } }, { key: "isPhantomJS", value: function() {
    var e2 = t2.getUA();
    return "" !== e2 && /phantomjs/.test(e2);
  } }, { key: "isFirefox", value: function() {
    var e2 = t2.getUA();
    return "" !== e2 && /firefox/.test(e2);
  } }]), t2;
}();
var Yt = [].join;
var Gt = v != Object;
var Ht = jt("join", ",");
It({ target: "Array", proto: true, forced: Gt || !Ht }, { join: function(t2) {
  return Yt.call(g(this), void 0 === t2 ? "," : t2);
} });
var Xt;
var Vt;
var Kt = function(t2) {
  return Object(p(t2));
};
var qt = Array.isArray || function(t2) {
  return "Array" == s(t2);
};
var Qt = !!Object.getOwnPropertySymbols && !o(function() {
  return !String(Symbol());
});
var Jt = Qt && !Symbol.sham && "symbol" == typeof Symbol.iterator;
var Zt = F("wks");
var te = r.Symbol;
var ee = Jt ? te : te && te.withoutSetter || $;
var ne = function(t2) {
  return S(Zt, t2) || (Qt && S(te, t2) ? Zt[t2] = te[t2] : Zt[t2] = ee("Symbol." + t2)), Zt[t2];
};
var re = ne("species");
var oe = function(t2, e2) {
  var n2;
  return qt(t2) && ("function" != typeof (n2 = t2.constructor) || n2 !== Array && !qt(n2.prototype) ? h(n2) && null === (n2 = n2[re]) && (n2 = void 0) : n2 = void 0), new (void 0 === n2 ? Array : n2)(0 === e2 ? 0 : e2);
};
var ie = function(t2, e2, n2) {
  var r2 = y(e2);
  r2 in t2 ? R.f(t2, r2, l(0, n2)) : t2[r2] = n2;
};
var ue = nt("navigator", "userAgent") || "";
var ae = r.process;
var ce = ae && ae.versions;
var le = ce && ce.v8;
le ? Vt = (Xt = le.split("."))[0] + Xt[1] : ue && (!(Xt = ue.match(/Edge\/(\d+)/)) || Xt[1] >= 74) && (Xt = ue.match(/Chrome\/(\d+)/)) && (Vt = Xt[1]);
var fe = Vt && +Vt;
var se = ne("species");
var de = function(t2) {
  return fe >= 51 || !o(function() {
    var e2 = [];
    return (e2.constructor = {})[se] = function() {
      return { foo: 1 };
    }, 1 !== e2[t2](Boolean).foo;
  });
};
var ve = de("splice");
var pe = Mt("splice", { ACCESSORS: true, 0: 0, 1: 2 });
var ge = Math.max;
var he = Math.min;
It({ target: "Array", proto: true, forced: !ve || !pe }, { splice: function(t2, e2) {
  var n2, r2, o2, i2, u2, a2, c2 = Kt(this), l2 = at(c2.length), f2 = ft(t2, l2), s2 = arguments.length;
  if (0 === s2 ? n2 = r2 = 0 : 1 === s2 ? (n2 = 0, r2 = l2 - f2) : (n2 = s2 - 2, r2 = he(ge(it(e2), 0), l2 - f2)), l2 + n2 - r2 > 9007199254740991) throw TypeError("Maximum allowed length exceeded");
  for (o2 = oe(c2, r2), i2 = 0; i2 < r2; i2++) (u2 = f2 + i2) in c2 && ie(o2, i2, c2[u2]);
  if (o2.length = r2, n2 < r2) {
    for (i2 = f2; i2 < l2 - r2; i2++) a2 = i2 + n2, (u2 = i2 + r2) in c2 ? c2[a2] = c2[u2] : delete c2[a2];
    for (i2 = l2; i2 > l2 - r2 + n2; i2--) delete c2[i2 - 1];
  } else if (n2 > r2) for (i2 = l2 - r2; i2 > f2; i2--) a2 = i2 + n2 - 1, (u2 = i2 + r2 - 1) in c2 ? c2[a2] = c2[u2] : delete c2[a2];
  for (i2 = 0; i2 < n2; i2++) c2[i2 + f2] = arguments[i2 + 2];
  return c2.length = l2 - r2 + n2, o2;
} });
var ye = {};
ye[ne("toStringTag")] = "z";
var me = "[object z]" === String(ye);
var Se = ne("toStringTag");
var xe = "Arguments" == s(/* @__PURE__ */ function() {
  return arguments;
}());
var be = me ? s : function(t2) {
  var e2, n2, r2;
  return void 0 === t2 ? "Undefined" : null === t2 ? "Null" : "string" == typeof (n2 = function(t3, e3) {
    try {
      return t3[e3];
    } catch (t4) {
    }
  }(e2 = Object(t2), Se)) ? n2 : xe ? s(e2) : "Object" == (r2 = s(e2)) && "function" == typeof e2.callee ? "Arguments" : r2;
};
var Ee = me ? {}.toString : function() {
  return "[object " + be(this) + "]";
};
me || Z(Object.prototype, "toString", Ee, { unsafe: true });
var we = function() {
  var t2 = A(this), e2 = "";
  return t2.global && (e2 += "g"), t2.ignoreCase && (e2 += "i"), t2.multiline && (e2 += "m"), t2.dotAll && (e2 += "s"), t2.unicode && (e2 += "u"), t2.sticky && (e2 += "y"), e2;
};
function Oe(t2, e2) {
  return RegExp(t2, e2);
}
var Te;
var Ae;
var ke = { UNSUPPORTED_Y: o(function() {
  var t2 = Oe("a", "y");
  return t2.lastIndex = 2, null != t2.exec("abcd");
}), BROKEN_CARET: o(function() {
  var t2 = Oe("^r", "gy");
  return t2.lastIndex = 2, null != t2.exec("str");
}) };
var Re = RegExp.prototype.exec;
var Ie = String.prototype.replace;
var je = Re;
var Ce = (Te = /a/, Ae = /b*/g, Re.call(Te, "a"), Re.call(Ae, "a"), 0 !== Te.lastIndex || 0 !== Ae.lastIndex);
var Le = ke.UNSUPPORTED_Y || ke.BROKEN_CARET;
var Pe = void 0 !== /()??/.exec("")[1];
(Ce || Pe || Le) && (je = function(t2) {
  var e2, n2, r2, o2, i2 = this, u2 = Le && i2.sticky, a2 = we.call(i2), c2 = i2.source, l2 = 0, f2 = t2;
  return u2 && (-1 === (a2 = a2.replace("y", "")).indexOf("g") && (a2 += "g"), f2 = String(t2).slice(i2.lastIndex), i2.lastIndex > 0 && (!i2.multiline || i2.multiline && "\n" !== t2[i2.lastIndex - 1]) && (c2 = "(?: " + c2 + ")", f2 = " " + f2, l2++), n2 = new RegExp("^(?:" + c2 + ")", a2)), Pe && (n2 = new RegExp("^" + c2 + "$(?!\\s)", a2)), Ce && (e2 = i2.lastIndex), r2 = Re.call(u2 ? n2 : i2, f2), u2 ? r2 ? (r2.input = r2.input.slice(l2), r2[0] = r2[0].slice(l2), r2.index = i2.lastIndex, i2.lastIndex += r2[0].length) : i2.lastIndex = 0 : Ce && r2 && (i2.lastIndex = i2.global ? r2.index + r2[0].length : e2), Pe && r2 && r2.length > 1 && Ie.call(r2[0], n2, function() {
    for (o2 = 1; o2 < arguments.length - 2; o2++) void 0 === arguments[o2] && (r2[o2] = void 0);
  }), r2;
});
var Me = je;
It({ target: "RegExp", proto: true, forced: /./.exec !== Me }, { exec: Me });
var _e = RegExp.prototype;
var De = _e.toString;
var Ue = o(function() {
  return "/a/b" != De.call({ source: "a", flags: "b" });
});
var Ne = "toString" != De.name;
(Ue || Ne) && Z(RegExp.prototype, "toString", function() {
  var t2 = A(this), e2 = String(t2.source), n2 = t2.flags;
  return "/" + e2 + "/" + String(void 0 === n2 && t2 instanceof RegExp && !("flags" in _e) ? we.call(t2) : n2);
}, { unsafe: true });
var Fe = ne("species");
var We = !o(function() {
  var t2 = /./;
  return t2.exec = function() {
    var t3 = [];
    return t3.groups = { a: "7" }, t3;
  }, "7" !== "".replace(t2, "$<a>");
});
var ze = "$0" === "a".replace(/./, "$0");
var $e = ne("replace");
var Be = !!/./[$e] && "" === /./[$e]("a", "$0");
var Ye = !o(function() {
  var t2 = /(?:)/, e2 = t2.exec;
  t2.exec = function() {
    return e2.apply(this, arguments);
  };
  var n2 = "ab".split(t2);
  return 2 !== n2.length || "a" !== n2[0] || "b" !== n2[1];
});
var Ge = function(t2, e2, n2, r2) {
  var i2 = ne(t2), u2 = !o(function() {
    var e3 = {};
    return e3[i2] = function() {
      return 7;
    }, 7 != ""[t2](e3);
  }), a2 = u2 && !o(function() {
    var e3 = false, n3 = /a/;
    return "split" === t2 && ((n3 = {}).constructor = {}, n3.constructor[Fe] = function() {
      return n3;
    }, n3.flags = "", n3[i2] = /./[i2]), n3.exec = function() {
      return e3 = true, null;
    }, n3[i2](""), !e3;
  });
  if (!u2 || !a2 || "replace" === t2 && (!We || !ze || Be) || "split" === t2 && !Ye) {
    var c2 = /./[i2], l2 = n2(i2, ""[t2], function(t3, e3, n3, r3, o2) {
      return e3.exec === Me ? u2 && !o2 ? { done: true, value: c2.call(e3, n3, r3) } : { done: true, value: t3.call(n3, e3, r3) } : { done: false };
    }, { REPLACE_KEEPS_$0: ze, REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE: Be }), f2 = l2[0], s2 = l2[1];
    Z(String.prototype, t2, f2), Z(RegExp.prototype, i2, 2 == e2 ? function(t3, e3) {
      return s2.call(t3, this, e3);
    } : function(t3) {
      return s2.call(t3, this);
    });
  }
  r2 && I(RegExp.prototype[i2], "sham", true);
};
var He = ne("match");
var Xe = function(t2) {
  var e2;
  return h(t2) && (void 0 !== (e2 = t2[He]) ? !!e2 : "RegExp" == s(t2));
};
var Ve = function(t2) {
  if ("function" != typeof t2) throw TypeError(String(t2) + " is not a function");
  return t2;
};
var Ke = ne("species");
var qe = function(t2) {
  return function(e2, n2) {
    var r2, o2, i2 = String(p(e2)), u2 = it(n2), a2 = i2.length;
    return u2 < 0 || u2 >= a2 ? t2 ? "" : void 0 : (r2 = i2.charCodeAt(u2)) < 55296 || r2 > 56319 || u2 + 1 === a2 || (o2 = i2.charCodeAt(u2 + 1)) < 56320 || o2 > 57343 ? t2 ? i2.charAt(u2) : r2 : t2 ? i2.slice(u2, u2 + 2) : o2 - 56320 + (r2 - 55296 << 10) + 65536;
  };
};
var Qe = { codeAt: qe(false), charAt: qe(true) };
var Je = Qe.charAt;
var Ze = function(t2, e2, n2) {
  return e2 + (n2 ? Je(t2, e2).length : 1);
};
var tn = function(t2, e2) {
  var n2 = t2.exec;
  if ("function" == typeof n2) {
    var r2 = n2.call(t2, e2);
    if ("object" != typeof r2) throw TypeError("RegExp exec method returned something other than an Object or null");
    return r2;
  }
  if ("RegExp" !== s(t2)) throw TypeError("RegExp#exec called on incompatible receiver");
  return Me.call(t2, e2);
};
var en = [].push;
var nn = Math.min;
var rn = !o(function() {
  return !RegExp(4294967295, "y");
});
Ge("split", 2, function(t2, e2, n2) {
  var r2;
  return r2 = "c" == "abbc".split(/(b)*/)[1] || 4 != "test".split(/(?:)/, -1).length || 2 != "ab".split(/(?:ab)*/).length || 4 != ".".split(/(.?)(.?)/).length || ".".split(/()()/).length > 1 || "".split(/.?/).length ? function(t3, n3) {
    var r3 = String(p(this)), o2 = void 0 === n3 ? 4294967295 : n3 >>> 0;
    if (0 === o2) return [];
    if (void 0 === t3) return [r3];
    if (!Xe(t3)) return e2.call(r3, t3, o2);
    for (var i2, u2, a2, c2 = [], l2 = (t3.ignoreCase ? "i" : "") + (t3.multiline ? "m" : "") + (t3.unicode ? "u" : "") + (t3.sticky ? "y" : ""), f2 = 0, s2 = new RegExp(t3.source, l2 + "g"); (i2 = Me.call(s2, r3)) && !((u2 = s2.lastIndex) > f2 && (c2.push(r3.slice(f2, i2.index)), i2.length > 1 && i2.index < r3.length && en.apply(c2, i2.slice(1)), a2 = i2[0].length, f2 = u2, c2.length >= o2)); ) s2.lastIndex === i2.index && s2.lastIndex++;
    return f2 === r3.length ? !a2 && s2.test("") || c2.push("") : c2.push(r3.slice(f2)), c2.length > o2 ? c2.slice(0, o2) : c2;
  } : "0".split(void 0, 0).length ? function(t3, n3) {
    return void 0 === t3 && 0 === n3 ? [] : e2.call(this, t3, n3);
  } : e2, [function(e3, n3) {
    var o2 = p(this), i2 = null == e3 ? void 0 : e3[t2];
    return void 0 !== i2 ? i2.call(e3, o2, n3) : r2.call(String(o2), e3, n3);
  }, function(t3, o2) {
    var i2 = n2(r2, t3, this, o2, r2 !== e2);
    if (i2.done) return i2.value;
    var u2 = A(t3), a2 = String(this), c2 = function(t4, e3) {
      var n3, r3 = A(t4).constructor;
      return void 0 === r3 || null == (n3 = A(r3)[Ke]) ? e3 : Ve(n3);
    }(u2, RegExp), l2 = u2.unicode, f2 = (u2.ignoreCase ? "i" : "") + (u2.multiline ? "m" : "") + (u2.unicode ? "u" : "") + (rn ? "y" : "g"), s2 = new c2(rn ? u2 : "^(?:" + u2.source + ")", f2), d2 = void 0 === o2 ? 4294967295 : o2 >>> 0;
    if (0 === d2) return [];
    if (0 === a2.length) return null === tn(s2, a2) ? [a2] : [];
    for (var v2 = 0, p2 = 0, g2 = []; p2 < a2.length; ) {
      s2.lastIndex = rn ? p2 : 0;
      var h2, y2 = tn(s2, rn ? a2 : a2.slice(p2));
      if (null === y2 || (h2 = nn(at(s2.lastIndex + (rn ? 0 : p2)), a2.length)) === v2) p2 = Ze(a2, p2, l2);
      else {
        if (g2.push(a2.slice(v2, p2)), g2.length === d2) return g2;
        for (var m2 = 1; m2 <= y2.length - 1; m2++) if (g2.push(y2[m2]), g2.length === d2) return g2;
        p2 = v2 = h2;
      }
    }
    return g2.push(a2.slice(v2)), g2;
  }];
}, !rn);
var on = "	\n\v\f\r                　\u2028\u2029\uFEFF";
var un = "[" + on + "]";
var an = RegExp("^" + un + un + "*");
var cn = RegExp(un + un + "*$");
var ln = function(t2) {
  return function(e2) {
    var n2 = String(p(e2));
    return 1 & t2 && (n2 = n2.replace(an, "")), 2 & t2 && (n2 = n2.replace(cn, "")), n2;
  };
};
var fn2 = { start: ln(1), end: ln(2), trim: ln(3) };
var sn = fn2.trim;
It({ target: "String", proto: true, forced: function(t2) {
  return o(function() {
    return !!on[t2]() || "​᠎" != "​᠎"[t2]() || on[t2].name !== t2;
  });
}("trim") }, { trim: function() {
  return sn(this);
} });
var dn = de("slice");
var vn = Mt("slice", { ACCESSORS: true, 0: 0, 1: 2 });
var pn = ne("species");
var gn = [].slice;
var hn = Math.max;
It({ target: "Array", proto: true, forced: !dn || !vn }, { slice: function(t2, e2) {
  var n2, r2, o2, i2 = g(this), u2 = at(i2.length), a2 = ft(t2, u2), c2 = ft(void 0 === e2 ? u2 : e2, u2);
  if (qt(i2) && ("function" != typeof (n2 = i2.constructor) || n2 !== Array && !qt(n2.prototype) ? h(n2) && null === (n2 = n2[pn]) && (n2 = void 0) : n2 = void 0, n2 === Array || void 0 === n2)) return gn.call(i2, a2, c2);
  for (r2 = new (void 0 === n2 ? Array : n2)(hn(c2 - a2, 0)), o2 = 0; a2 < c2; a2++, o2++) a2 in i2 && ie(r2, o2, i2[a2]);
  return r2.length = o2, r2;
} });
var yn = Object.keys || function(t2) {
  return pt(t2, gt);
};
var mn = o(function() {
  yn(1);
});
It({ target: "Object", stat: true, forced: mn }, { keys: function(t2) {
  return yn(Kt(t2));
} });
var Sn;
var xn = function(t2) {
  if (Xe(t2)) throw TypeError("The method doesn't accept regular expressions");
  return t2;
};
var bn = ne("match");
var En = T.f;
var wn = "".startsWith;
var On = Math.min;
var Tn = function(t2) {
  var e2 = /./;
  try {
    "/./"[t2](e2);
  } catch (n2) {
    try {
      return e2[bn] = false, "/./"[t2](e2);
    } catch (t3) {
    }
  }
  return false;
}("startsWith");
var An = !(Tn || (Sn = En(String.prototype, "startsWith"), !Sn || Sn.writable));
function kn(t2) {
  return (kn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
    return typeof t3;
  } : function(t3) {
    return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
  })(t2);
}
It({ target: "String", proto: true, forced: !An && !Tn }, { startsWith: function(t2) {
  var e2 = String(p(this));
  xn(t2);
  var n2 = at(On(arguments.length > 1 ? arguments[1] : void 0, e2.length)), r2 = String(t2);
  return wn ? wn.call(e2, r2, n2) : e2.slice(n2, n2 + r2.length) === r2;
} });
var jn = function(t2) {
  return "string" == typeof t2;
};
var Mn = function(t2) {
  return null !== t2 && "object" === kn(t2);
};
var Fn = Array.isArray;
var Vn = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "isWindow", value: function(t3) {
    return t3 === window;
  } }, { key: "addEventListener", value: function(t3, e2, n2) {
    var r2 = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
    t3 && e2 && n2 && t3.addEventListener(e2, n2, r2);
  } }, { key: "removeEventListener", value: function(t3, e2, n2) {
    var r2 = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
    t3 && e2 && n2 && t3.removeEventListener(e2, n2, r2);
  } }, { key: "triggerDragEvent", value: function(e2, n2) {
    var r2 = false, o2 = function(t3) {
      var e3;
      null === (e3 = n2.drag) || void 0 === e3 || e3.call(n2, t3);
    }, i2 = function e3(i3) {
      var u2;
      t2.removeEventListener(document, "mousemove", o2), t2.removeEventListener(document, "mouseup", e3), document.onselectstart = null, document.ondragstart = null, r2 = false, null === (u2 = n2.end) || void 0 === u2 || u2.call(n2, i3);
    };
    t2.addEventListener(e2, "mousedown", function(e3) {
      var u2;
      r2 || (document.onselectstart = function() {
        return false;
      }, document.ondragstart = function() {
        return false;
      }, t2.addEventListener(document, "mousemove", o2), t2.addEventListener(document, "mouseup", i2), r2 = true, null === (u2 = n2.start) || void 0 === u2 || u2.call(n2, e3));
    });
  } }, { key: "getBoundingClientRect", value: function(t3) {
    return t3 && Mn(t3) && 1 === t3.nodeType ? t3.getBoundingClientRect() : null;
  } }, { key: "hasClass", value: function(t3, e2) {
    return !!(t3 && Mn(t3) && jn(e2) && 1 === t3.nodeType) && t3.classList.contains(e2.trim());
  } }, { key: "addClass", value: function(e2, n2) {
    if (e2 && Mn(e2) && jn(n2) && 1 === e2.nodeType && (n2 = n2.trim(), !t2.hasClass(e2, n2))) {
      var r2 = e2.className;
      e2.className = r2 ? r2 + " " + n2 : n2;
    }
  } }, { key: "removeClass", value: function(t3, e2) {
    if (t3 && Mn(t3) && jn(e2) && 1 === t3.nodeType && "string" == typeof t3.className) {
      e2 = e2.trim();
      for (var n2 = t3.className.trim().split(" "), r2 = n2.length - 1; r2 >= 0; r2--) n2[r2] = n2[r2].trim(), n2[r2] && n2[r2] !== e2 || n2.splice(r2, 1);
      t3.className = n2.join(" ");
    }
  } }, { key: "toggleClass", value: function(t3, e2, n2) {
    t3 && Mn(t3) && jn(e2) && 1 === t3.nodeType && t3.classList.toggle(e2, n2);
  } }, { key: "replaceClass", value: function(e2, n2, r2) {
    e2 && Mn(e2) && jn(n2) && jn(r2) && 1 === e2.nodeType && (n2 = n2.trim(), r2 = r2.trim(), t2.removeClass(e2, n2), t2.addClass(e2, r2));
  } }, { key: "getScrollTop", value: function(t3) {
    var e2 = "scrollTop" in t3 ? t3.scrollTop : t3.pageYOffset;
    return Math.max(e2, 0);
  } }, { key: "setScrollTop", value: function(t3, e2) {
    "scrollTop" in t3 ? t3.scrollTop = e2 : t3.scrollTo(t3.scrollX, e2);
  } }, { key: "getRootScrollTop", value: function() {
    return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
  } }, { key: "setRootScrollTop", value: function(e2) {
    t2.setScrollTop(window, e2), t2.setScrollTop(document.body, e2);
  } }, { key: "getElementTop", value: function(e2, n2) {
    if (t2.isWindow(e2)) return 0;
    var r2 = n2 ? t2.getScrollTop(n2) : t2.getRootScrollTop();
    return e2.getBoundingClientRect().top + r2;
  } }, { key: "getVisibleHeight", value: function(e2) {
    return t2.isWindow(e2) ? e2.innerHeight : e2.getBoundingClientRect().height;
  } }, { key: "isHidden", value: function(t3) {
    if (!t3) return false;
    var e2 = window.getComputedStyle(t3), n2 = "none" === e2.display, r2 = null === t3.offsetParent && "fixed" !== e2.position;
    return n2 || r2;
  } }, { key: "triggerEvent", value: function(t3, e2) {
    if ("createEvent" in document) {
      var n2 = document.createEvent("HTMLEvents");
      n2.initEvent(e2, false, true), t3.dispatchEvent(n2);
    }
  } }, { key: "calcAngle", value: function(t3, e2) {
    var n2 = t3.getBoundingClientRect(), r2 = n2.left + n2.width / 2, o2 = n2.top + n2.height / 2, i2 = Math.abs(r2 - e2.clientX), u2 = Math.abs(o2 - e2.clientY), a2 = u2 / Math.sqrt(Math.pow(i2, 2) + Math.pow(u2, 2)), c2 = Math.acos(a2), l2 = Math.floor(180 / (Math.PI / c2));
    return e2.clientX > r2 && e2.clientY > o2 && (l2 = 180 - l2), e2.clientX == r2 && e2.clientY > o2 && (l2 = 180), e2.clientX > r2 && e2.clientY == o2 && (l2 = 90), e2.clientX < r2 && e2.clientY > o2 && (l2 = 180 + l2), e2.clientX < r2 && e2.clientY == o2 && (l2 = 270), e2.clientX < r2 && e2.clientY < o2 && (l2 = 360 - l2), l2;
  } }, { key: "querySelector", value: function(t3, e2) {
    return e2 ? e2.querySelector(t3) : document.querySelector(t3);
  } }, { key: "createElement", value: function(t3) {
    for (var e2 = document.createElement(t3), n2 = arguments.length, r2 = new Array(n2 > 1 ? n2 - 1 : 0), o2 = 1; o2 < n2; o2++) r2[o2 - 1] = arguments[o2];
    for (var i2 = 0; i2 < r2.length; i2++) r2[i2] && e2.classList.add(r2[i2]);
    return e2;
  } }, { key: "appendChild", value: function(t3) {
    for (var e2 = 0; e2 < (arguments.length <= 1 ? 0 : arguments.length - 1); e2++) t3.appendChild(e2 + 1 < 1 || arguments.length <= e2 + 1 ? void 0 : arguments[e2 + 1]);
  } }, { key: "getWindow", value: function(t3) {
    if ("[object Window]" !== t3.toString()) {
      var e2 = t3.ownerDocument;
      return e2 && e2.defaultView || window;
    }
    return t3;
  } }, { key: "isElement", value: function(t3) {
    return t3 instanceof this.getWindow(t3).Element || t3 instanceof Element;
  } }, { key: "isHTMLElement", value: function(t3) {
    return t3 instanceof this.getWindow(t3).HTMLElement || t3 instanceof HTMLElement;
  } }, { key: "isShadowRoot", value: function(t3) {
    return "undefined" != typeof ShadowRoot && (t3 instanceof this.getWindow(t3).ShadowRoot || t3 instanceof ShadowRoot);
  } }, { key: "getWindowScroll", value: function(t3) {
    var e2 = this.getWindow(t3);
    return { scrollLeft: e2.pageXOffset || 0, scrollTop: e2.pageYOffset || 0 };
  } }]), t2;
}();
var Kn = Math.floor;
var qn = "".replace;
var Qn = /\$([$&'`]|\d\d?|<[^>]*>)/g;
var Jn = /\$([$&'`]|\d\d?)/g;
var Zn = function(t2, e2, n2, r2, o2, i2) {
  var u2 = n2 + t2.length, a2 = r2.length, c2 = Jn;
  return void 0 !== o2 && (o2 = Kt(o2), c2 = Qn), qn.call(i2, c2, function(i3, c3) {
    var l2;
    switch (c3.charAt(0)) {
      case "$":
        return "$";
      case "&":
        return t2;
      case "`":
        return e2.slice(0, n2);
      case "'":
        return e2.slice(u2);
      case "<":
        l2 = o2[c3.slice(1, -1)];
        break;
      default:
        var f2 = +c3;
        if (0 === f2) return i3;
        if (f2 > a2) {
          var s2 = Kn(f2 / 10);
          return 0 === s2 ? i3 : s2 <= a2 ? void 0 === r2[s2 - 1] ? c3.charAt(1) : r2[s2 - 1] + c3.charAt(1) : i3;
        }
        l2 = r2[f2 - 1];
    }
    return void 0 === l2 ? "" : l2;
  });
};
var tr = Math.max;
var er = Math.min;
Ge("replace", 2, function(t2, e2, n2, r2) {
  var o2 = r2.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE, i2 = r2.REPLACE_KEEPS_$0, u2 = o2 ? "$" : "$0";
  return [function(n3, r3) {
    var o3 = p(this), i3 = null == n3 ? void 0 : n3[t2];
    return void 0 !== i3 ? i3.call(n3, o3, r3) : e2.call(String(o3), n3, r3);
  }, function(t3, r3) {
    if (!o2 && i2 || "string" == typeof r3 && -1 === r3.indexOf(u2)) {
      var a2 = n2(e2, t3, this, r3);
      if (a2.done) return a2.value;
    }
    var c2 = A(t3), l2 = String(this), f2 = "function" == typeof r3;
    f2 || (r3 = String(r3));
    var s2 = c2.global;
    if (s2) {
      var d2 = c2.unicode;
      c2.lastIndex = 0;
    }
    for (var v2 = []; ; ) {
      var p2 = tn(c2, l2);
      if (null === p2) break;
      if (v2.push(p2), !s2) break;
      "" === String(p2[0]) && (c2.lastIndex = Ze(l2, at(c2.lastIndex), d2));
    }
    for (var g2, h2 = "", y2 = 0, m2 = 0; m2 < v2.length; m2++) {
      p2 = v2[m2];
      for (var S2 = String(p2[0]), x2 = tr(er(it(p2.index), l2.length), 0), b2 = [], E2 = 1; E2 < p2.length; E2++) b2.push(void 0 === (g2 = p2[E2]) ? g2 : String(g2));
      var w2 = p2.groups;
      if (f2) {
        var O2 = [S2].concat(b2, x2, l2);
        void 0 !== w2 && O2.push(w2);
        var T2 = String(r3.apply(void 0, O2));
      } else T2 = Zn(S2, l2, x2, b2, w2, r3);
      x2 >= y2 && (h2 += l2.slice(y2, x2) + T2, y2 = x2 + S2.length);
    }
    return h2 + l2.slice(y2);
  }];
});
var nr = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "camelize", value: function(t3) {
    return t3.replace(/-(\w)/g, function(t4, e2) {
      return e2 ? e2.toUpperCase() : "";
    });
  } }, { key: "capitalize", value: function(t3) {
    return t3.charAt(0).toUpperCase() + t3.slice(1);
  } }]), t2;
}();
var rr = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "_clone", value: function() {
  } }]), t2;
}();
var or = ne("isConcatSpreadable");
var ir = fe >= 51 || !o(function() {
  var t2 = [];
  return t2[or] = false, t2.concat()[0] !== t2;
});
var ur = de("concat");
var ar = function(t2) {
  if (!h(t2)) return false;
  var e2 = t2[or];
  return void 0 !== e2 ? !!e2 : qt(t2);
};
It({ target: "Array", proto: true, forced: !ir || !ur }, { concat: function(t2) {
  var e2, n2, r2, o2, i2, u2 = Kt(this), a2 = oe(u2, 0), c2 = 0;
  for (e2 = -1, r2 = arguments.length; e2 < r2; e2++) if (ar(i2 = -1 === e2 ? u2 : arguments[e2])) {
    if (c2 + (o2 = at(i2.length)) > 9007199254740991) throw TypeError("Maximum allowed index exceeded");
    for (n2 = 0; n2 < o2; n2++, c2++) n2 in i2 && ie(a2, c2, i2[n2]);
  } else {
    if (c2 >= 9007199254740991) throw TypeError("Maximum allowed index exceeded");
    ie(a2, c2++, i2);
  }
  return a2.length = c2, a2;
} });
var cr;
var lr = function(t2, e2, n2) {
  if (Ve(t2), void 0 === e2) return t2;
  switch (n2) {
    case 0:
      return function() {
        return t2.call(e2);
      };
    case 1:
      return function(n3) {
        return t2.call(e2, n3);
      };
    case 2:
      return function(n3, r2) {
        return t2.call(e2, n3, r2);
      };
    case 3:
      return function(n3, r2, o2) {
        return t2.call(e2, n3, r2, o2);
      };
  }
  return function() {
    return t2.apply(e2, arguments);
  };
};
var fr = [].push;
var sr = function(t2) {
  var e2 = 1 == t2, n2 = 2 == t2, r2 = 3 == t2, o2 = 4 == t2, i2 = 6 == t2, u2 = 7 == t2, a2 = 5 == t2 || i2;
  return function(c2, l2, f2, s2) {
    for (var d2, p2, g2 = Kt(c2), h2 = v(g2), y2 = lr(l2, f2, 3), m2 = at(h2.length), S2 = 0, x2 = s2 || oe, b2 = e2 ? x2(c2, m2) : n2 || u2 ? x2(c2, 0) : void 0; m2 > S2; S2++) if ((a2 || S2 in h2) && (p2 = y2(d2 = h2[S2], S2, g2), t2)) if (e2) b2[S2] = p2;
    else if (p2) switch (t2) {
      case 3:
        return true;
      case 5:
        return d2;
      case 6:
        return S2;
      case 2:
        fr.call(b2, d2);
    }
    else switch (t2) {
      case 4:
        return false;
      case 7:
        fr.call(b2, d2);
    }
    return i2 ? -1 : r2 || o2 ? o2 : b2;
  };
};
var dr = { forEach: sr(0), map: sr(1), filter: sr(2), some: sr(3), every: sr(4), find: sr(5), findIndex: sr(6), filterOut: sr(7) };
var vr = i ? Object.defineProperties : function(t2, e2) {
  A(t2);
  for (var n2, r2 = yn(e2), o2 = r2.length, i2 = 0; o2 > i2; ) R.f(t2, n2 = r2[i2++], e2[n2]);
  return t2;
};
var pr = nt("document", "documentElement");
var gr = Y("IE_PROTO");
var hr = function() {
};
var yr = function(t2) {
  return "<script>" + t2 + "<\/script>";
};
var mr = function() {
  try {
    cr = document.domain && new ActiveXObject("htmlfile");
  } catch (t3) {
  }
  var t2, e2;
  mr = cr ? function(t3) {
    t3.write(yr("")), t3.close();
    var e3 = t3.parentWindow.Object;
    return t3 = null, e3;
  }(cr) : ((e2 = E("iframe")).style.display = "none", pr.appendChild(e2), e2.src = String("javascript:"), (t2 = e2.contentWindow.document).open(), t2.write(yr("document.F=Object")), t2.close(), t2.F);
  for (var n2 = gt.length; n2--; ) delete mr.prototype[gt[n2]];
  return mr();
};
G[gr] = true;
var Sr = Object.create || function(t2, e2) {
  var n2;
  return null !== t2 ? (hr.prototype = A(t2), n2 = new hr(), hr.prototype = null, n2[gr] = t2) : n2 = mr(), void 0 === e2 ? n2 : vr(n2, e2);
};
var xr = ne("unscopables");
var br = Array.prototype;
null == br[xr] && R.f(br, xr, { configurable: true, value: Sr(null) });
var Er = function(t2) {
  br[xr][t2] = true;
};
var wr = dr.find;
var Or = true;
var Tr = Mt("find");
"find" in [] && Array(1).find(function() {
  Or = false;
}), It({ target: "Array", proto: true, forced: Or || !Tr }, { find: function(t2) {
  return wr(this, t2, arguments.length > 1 ? arguments[1] : void 0);
} }), Er("find");
var Ar = dr.findIndex;
var kr = true;
var Rr = Mt("findIndex");
"findIndex" in [] && Array(1).findIndex(function() {
  kr = false;
}), It({ target: "Array", proto: true, forced: kr || !Rr }, { findIndex: function(t2) {
  return Ar(this, t2, arguments.length > 1 ? arguments[1] : void 0);
} }), Er("findIndex");
var Ir = function(t2, e2, n2, r2, o2, i2, u2, a2) {
  for (var c2, l2 = o2, f2 = 0, s2 = !!u2 && lr(u2, a2, 3); f2 < r2; ) {
    if (f2 in n2) {
      if (c2 = s2 ? s2(n2[f2], f2, e2) : n2[f2], i2 > 0 && qt(c2)) l2 = Ir(t2, e2, c2, at(c2.length), l2, i2 - 1) - 1;
      else {
        if (l2 >= 9007199254740991) throw TypeError("Exceed the acceptable array length");
        t2[l2] = c2;
      }
      l2++;
    }
    f2++;
  }
  return l2;
};
var jr = Ir;
It({ target: "Array", proto: true }, { flat: function() {
  var t2 = arguments.length ? arguments[0] : void 0, e2 = Kt(this), n2 = at(e2.length), r2 = oe(e2, 0);
  return r2.length = jr(r2, e2, e2, n2, 0, void 0 === t2 ? 1 : it(t2)), r2;
} });
var Cr = function(t2) {
  var e2 = t2.return;
  if (void 0 !== e2) return A(e2.call(t2)).value;
};
var Lr = function(t2, e2, n2, r2) {
  try {
    return r2 ? e2(A(n2)[0], n2[1]) : e2(n2);
  } catch (e3) {
    throw Cr(t2), e3;
  }
};
var Pr = {};
var Mr = ne("iterator");
var _r = Array.prototype;
var Dr = function(t2) {
  return void 0 !== t2 && (Pr.Array === t2 || _r[Mr] === t2);
};
var Ur = ne("iterator");
var Nr = function(t2) {
  if (null != t2) return t2[Ur] || t2["@@iterator"] || Pr[be(t2)];
};
var Fr = ne("iterator");
var Wr = false;
try {
  zr = 0, $r = { next: function() {
    return { done: !!zr++ };
  }, return: function() {
    Wr = true;
  } };
  $r[Fr] = function() {
    return this;
  }, Array.from($r, function() {
    throw 2;
  });
} catch (t2) {
}
var zr;
var $r;
var Br = function(t2, e2) {
  if (!e2 && !Wr) return false;
  var n2 = false;
  try {
    var r2 = {};
    r2[Fr] = function() {
      return { next: function() {
        return { done: n2 = true };
      } };
    }, t2(r2);
  } catch (t3) {
  }
  return n2;
};
var Yr = !Br(function(t2) {
  Array.from(t2);
});
It({ target: "Array", stat: true, forced: Yr }, { from: function(t2) {
  var e2, n2, r2, o2, i2, u2, a2 = Kt(t2), c2 = "function" == typeof this ? this : Array, l2 = arguments.length, f2 = l2 > 1 ? arguments[1] : void 0, s2 = void 0 !== f2, d2 = Nr(a2), v2 = 0;
  if (s2 && (f2 = lr(f2, l2 > 2 ? arguments[2] : void 0, 2)), null == d2 || c2 == Array && Dr(d2)) for (n2 = new c2(e2 = at(a2.length)); e2 > v2; v2++) u2 = s2 ? f2(a2[v2], v2) : a2[v2], ie(n2, v2, u2);
  else for (i2 = (o2 = d2.call(a2)).next, n2 = new c2(); !(r2 = i2.call(o2)).done; v2++) u2 = s2 ? Lr(o2, f2, [r2.value, v2], true) : r2.value, ie(n2, v2, u2);
  return n2.length = v2, n2;
} });
var Gr = function(t2) {
  return function(e2, n2, r2, o2) {
    Ve(n2);
    var i2 = Kt(e2), u2 = v(i2), a2 = at(i2.length), c2 = t2 ? a2 - 1 : 0, l2 = t2 ? -1 : 1;
    if (r2 < 2) for (; ; ) {
      if (c2 in u2) {
        o2 = u2[c2], c2 += l2;
        break;
      }
      if (c2 += l2, t2 ? c2 < 0 : a2 <= c2) throw TypeError("Reduce of empty array with no initial value");
    }
    for (; t2 ? c2 >= 0 : a2 > c2; c2 += l2) c2 in u2 && (o2 = n2(o2, u2[c2], c2, i2));
    return o2;
  };
};
var Hr = { left: Gr(false), right: Gr(true) };
var Xr = "process" == s(r.process);
var Vr = Hr.left;
var Kr = jt("reduce");
var qr = Mt("reduce", { 1: 0 });
It({ target: "Array", proto: true, forced: !Kr || !qr || !Xr && fe > 79 && fe < 83 }, { reduce: function(t2) {
  return Vr(this, t2, arguments.length, arguments.length > 1 ? arguments[1] : void 0);
} }), Er("flat");
var Qr;
var Jr;
var Zr;
var to = !o(function() {
  return Object.isExtensible(Object.preventExtensions({}));
});
var eo = e(function(t2) {
  var e2 = R.f, n2 = $("meta"), r2 = 0, o2 = Object.isExtensible || function() {
    return true;
  }, i2 = function(t3) {
    e2(t3, n2, { value: { objectID: "O" + ++r2, weakData: {} } });
  }, u2 = t2.exports = { REQUIRED: false, fastKey: function(t3, e3) {
    if (!h(t3)) return "symbol" == typeof t3 ? t3 : ("string" == typeof t3 ? "S" : "P") + t3;
    if (!S(t3, n2)) {
      if (!o2(t3)) return "F";
      if (!e3) return "E";
      i2(t3);
    }
    return t3[n2].objectID;
  }, getWeakData: function(t3, e3) {
    if (!S(t3, n2)) {
      if (!o2(t3)) return true;
      if (!e3) return false;
      i2(t3);
    }
    return t3[n2].weakData;
  }, onFreeze: function(t3) {
    return to && u2.REQUIRED && o2(t3) && !S(t3, n2) && i2(t3), t3;
  } };
  G[n2] = true;
});
var no = function(t2, e2) {
  this.stopped = t2, this.result = e2;
};
var ro = function(t2, e2, n2) {
  var r2, o2, i2, u2, a2, c2, l2, f2 = n2 && n2.that, s2 = !(!n2 || !n2.AS_ENTRIES), d2 = !(!n2 || !n2.IS_ITERATOR), v2 = !(!n2 || !n2.INTERRUPTED), p2 = lr(e2, f2, 1 + s2 + v2), g2 = function(t3) {
    return r2 && Cr(r2), new no(true, t3);
  }, h2 = function(t3) {
    return s2 ? (A(t3), v2 ? p2(t3[0], t3[1], g2) : p2(t3[0], t3[1])) : v2 ? p2(t3, g2) : p2(t3);
  };
  if (d2) r2 = t2;
  else {
    if ("function" != typeof (o2 = Nr(t2))) throw TypeError("Target is not iterable");
    if (Dr(o2)) {
      for (i2 = 0, u2 = at(t2.length); u2 > i2; i2++) if ((a2 = h2(t2[i2])) && a2 instanceof no) return a2;
      return new no(false);
    }
    r2 = o2.call(t2);
  }
  for (c2 = r2.next; !(l2 = c2.call(r2)).done; ) {
    try {
      a2 = h2(l2.value);
    } catch (t3) {
      throw Cr(r2), t3;
    }
    if ("object" == typeof a2 && a2 && a2 instanceof no) return a2;
  }
  return new no(false);
};
var oo = function(t2, e2, n2) {
  if (!(t2 instanceof e2)) throw TypeError("Incorrect " + (n2 ? n2 + " " : "") + "invocation");
  return t2;
};
var io = R.f;
var uo = ne("toStringTag");
var ao = function(t2, e2, n2) {
  t2 && !S(t2 = n2 ? t2 : t2.prototype, uo) && io(t2, uo, { configurable: true, value: e2 });
};
var co = Object.setPrototypeOf || ("__proto__" in {} ? function() {
  var t2, e2 = false, n2 = {};
  try {
    (t2 = Object.getOwnPropertyDescriptor(Object.prototype, "__proto__").set).call(n2, []), e2 = n2 instanceof Array;
  } catch (t3) {
  }
  return function(n3, r2) {
    return A(n3), function(t3) {
      if (!h(t3) && null !== t3) throw TypeError("Can't set " + String(t3) + " as a prototype");
    }(r2), e2 ? t2.call(n3, r2) : n3.__proto__ = r2, n3;
  };
}() : void 0);
var lo = function(t2, e2, n2) {
  for (var r2 in e2) Z(t2, r2, e2[r2], n2);
  return t2;
};
var fo = !o(function() {
  function t2() {
  }
  return t2.prototype.constructor = null, Object.getPrototypeOf(new t2()) !== t2.prototype;
});
var so = Y("IE_PROTO");
var vo = Object.prototype;
var po = fo ? Object.getPrototypeOf : function(t2) {
  return t2 = Kt(t2), S(t2, so) ? t2[so] : "function" == typeof t2.constructor && t2 instanceof t2.constructor ? t2.constructor.prototype : t2 instanceof Object ? vo : null;
};
var go = ne("iterator");
var ho = false;
[].keys && ("next" in (Zr = [].keys()) ? (Jr = po(po(Zr))) !== Object.prototype && (Qr = Jr) : ho = true), (null == Qr || o(function() {
  var t2 = {};
  return Qr[go].call(t2) !== t2;
})) && (Qr = {}), S(Qr, go) || I(Qr, go, function() {
  return this;
});
var yo = { IteratorPrototype: Qr, BUGGY_SAFARI_ITERATORS: ho };
var mo = yo.IteratorPrototype;
var So = function() {
  return this;
};
var xo = yo.IteratorPrototype;
var bo = yo.BUGGY_SAFARI_ITERATORS;
var Eo = ne("iterator");
var wo = function() {
  return this;
};
var Oo = function(t2, e2, n2, r2, o2, i2, u2) {
  !function(t3, e3, n3) {
    var r3 = e3 + " Iterator";
    t3.prototype = Sr(mo, { next: l(1, n3) }), ao(t3, r3, false), Pr[r3] = So;
  }(n2, e2, r2);
  var a2, c2, f2, s2 = function(t3) {
    if (t3 === o2 && h2) return h2;
    if (!bo && t3 in p2) return p2[t3];
    switch (t3) {
      case "keys":
      case "values":
      case "entries":
        return function() {
          return new n2(this, t3);
        };
    }
    return function() {
      return new n2(this);
    };
  }, d2 = e2 + " Iterator", v2 = false, p2 = t2.prototype, g2 = p2[Eo] || p2["@@iterator"] || o2 && p2[o2], h2 = !bo && g2 || s2(o2), y2 = "Array" == e2 && p2.entries || g2;
  if (y2 && (a2 = po(y2.call(new t2())), xo !== Object.prototype && a2.next && (po(a2) !== xo && (co ? co(a2, xo) : "function" != typeof a2[Eo] && I(a2, Eo, wo)), ao(a2, d2, true))), "values" == o2 && g2 && "values" !== g2.name && (v2 = true, h2 = function() {
    return g2.call(this);
  }), p2[Eo] !== h2 && I(p2, Eo, h2), Pr[e2] = h2, o2) if (c2 = { values: s2("values"), keys: i2 ? h2 : s2("keys"), entries: s2("entries") }, u2) for (f2 in c2) (bo || v2 || !(f2 in p2)) && Z(p2, f2, c2[f2]);
  else It({ target: e2, proto: true, forced: bo || v2 }, c2);
  return c2;
};
var To = ne("species");
var Ao = R.f;
var ko = eo.fastKey;
var Ro = J.set;
var Io = J.getterFor;
!function(t2, e2, n2) {
  var i2 = -1 !== t2.indexOf("Map"), u2 = -1 !== t2.indexOf("Weak"), a2 = i2 ? "set" : "add", c2 = r[t2], l2 = c2 && c2.prototype, f2 = c2, s2 = {}, d2 = function(t3) {
    var e3 = l2[t3];
    Z(l2, t3, "add" == t3 ? function(t4) {
      return e3.call(this, 0 === t4 ? 0 : t4), this;
    } : "delete" == t3 ? function(t4) {
      return !(u2 && !h(t4)) && e3.call(this, 0 === t4 ? 0 : t4);
    } : "get" == t3 ? function(t4) {
      return u2 && !h(t4) ? void 0 : e3.call(this, 0 === t4 ? 0 : t4);
    } : "has" == t3 ? function(t4) {
      return !(u2 && !h(t4)) && e3.call(this, 0 === t4 ? 0 : t4);
    } : function(t4, n3) {
      return e3.call(this, 0 === t4 ? 0 : t4, n3), this;
    });
  };
  if (kt(t2, "function" != typeof c2 || !(u2 || l2.forEach && !o(function() {
    new c2().entries().next();
  })))) f2 = n2.getConstructor(e2, t2, i2, a2), eo.REQUIRED = true;
  else if (kt(t2, true)) {
    var v2 = new f2(), p2 = v2[a2](u2 ? {} : -0, 1) != v2, g2 = o(function() {
      v2.has(1);
    }), y2 = Br(function(t3) {
      new c2(t3);
    }), m2 = !u2 && o(function() {
      for (var t3 = new c2(), e3 = 5; e3--; ) t3[a2](e3, e3);
      return !t3.has(-0);
    });
    y2 || ((f2 = e2(function(e3, n3) {
      oo(e3, f2, t2);
      var r2 = function(t3, e4, n4) {
        var r3, o2;
        return co && "function" == typeof (r3 = e4.constructor) && r3 !== n4 && h(o2 = r3.prototype) && o2 !== n4.prototype && co(t3, o2), t3;
      }(new c2(), e3, f2);
      return null != n3 && ro(n3, r2[a2], { that: r2, AS_ENTRIES: i2 }), r2;
    })).prototype = l2, l2.constructor = f2), (g2 || m2) && (d2("delete"), d2("has"), i2 && d2("get")), (m2 || p2) && d2(a2), u2 && l2.clear && delete l2.clear;
  }
  s2[t2] = f2, It({ global: true, forced: f2 != c2 }, s2), ao(f2, t2), u2 || n2.setStrong(f2, t2, i2);
}("Set", function(t2) {
  return function() {
    return t2(this, arguments.length ? arguments[0] : void 0);
  };
}, { getConstructor: function(t2, e2, n2, r2) {
  var o2 = t2(function(t3, u3) {
    oo(t3, o2, e2), Ro(t3, { type: e2, index: Sr(null), first: void 0, last: void 0, size: 0 }), i || (t3.size = 0), null != u3 && ro(u3, t3[r2], { that: t3, AS_ENTRIES: n2 });
  }), u2 = Io(e2), a2 = function(t3, e3, n3) {
    var r3, o3, a3 = u2(t3), l2 = c2(t3, e3);
    return l2 ? l2.value = n3 : (a3.last = l2 = { index: o3 = ko(e3, true), key: e3, value: n3, previous: r3 = a3.last, next: void 0, removed: false }, a3.first || (a3.first = l2), r3 && (r3.next = l2), i ? a3.size++ : t3.size++, "F" !== o3 && (a3.index[o3] = l2)), t3;
  }, c2 = function(t3, e3) {
    var n3, r3 = u2(t3), o3 = ko(e3);
    if ("F" !== o3) return r3.index[o3];
    for (n3 = r3.first; n3; n3 = n3.next) if (n3.key == e3) return n3;
  };
  return lo(o2.prototype, { clear: function() {
    for (var t3 = u2(this), e3 = t3.index, n3 = t3.first; n3; ) n3.removed = true, n3.previous && (n3.previous = n3.previous.next = void 0), delete e3[n3.index], n3 = n3.next;
    t3.first = t3.last = void 0, i ? t3.size = 0 : this.size = 0;
  }, delete: function(t3) {
    var e3 = this, n3 = u2(e3), r3 = c2(e3, t3);
    if (r3) {
      var o3 = r3.next, a3 = r3.previous;
      delete n3.index[r3.index], r3.removed = true, a3 && (a3.next = o3), o3 && (o3.previous = a3), n3.first == r3 && (n3.first = o3), n3.last == r3 && (n3.last = a3), i ? n3.size-- : e3.size--;
    }
    return !!r3;
  }, forEach: function(t3) {
    for (var e3, n3 = u2(this), r3 = lr(t3, arguments.length > 1 ? arguments[1] : void 0, 3); e3 = e3 ? e3.next : n3.first; ) for (r3(e3.value, e3.key, this); e3 && e3.removed; ) e3 = e3.previous;
  }, has: function(t3) {
    return !!c2(this, t3);
  } }), lo(o2.prototype, n2 ? { get: function(t3) {
    var e3 = c2(this, t3);
    return e3 && e3.value;
  }, set: function(t3, e3) {
    return a2(this, 0 === t3 ? 0 : t3, e3);
  } } : { add: function(t3) {
    return a2(this, t3 = 0 === t3 ? 0 : t3, t3);
  } }), i && Ao(o2.prototype, "size", { get: function() {
    return u2(this).size;
  } }), o2;
}, setStrong: function(t2, e2, n2) {
  var r2 = e2 + " Iterator", o2 = Io(e2), u2 = Io(r2);
  Oo(t2, e2, function(t3, e3) {
    Ro(this, { type: r2, target: t3, state: o2(t3), kind: e3, last: void 0 });
  }, function() {
    for (var t3 = u2(this), e3 = t3.kind, n3 = t3.last; n3 && n3.removed; ) n3 = n3.previous;
    return t3.target && (t3.last = n3 = n3 ? n3.next : t3.state.first) ? "keys" == e3 ? { value: n3.key, done: false } : "values" == e3 ? { value: n3.value, done: false } : { value: [n3.key, n3.value], done: false } : (t3.target = void 0, { value: void 0, done: true });
  }, n2 ? "entries" : "values", !n2, true), function(t3) {
    var e3 = nt(t3), n3 = R.f;
    i && e3 && !e3[To] && n3(e3, To, { configurable: true, get: function() {
      return this;
    } });
  }(e2);
} });
var jo = Qe.charAt;
var Co = J.set;
var Lo = J.getterFor("String Iterator");
Oo(String, "String", function(t2) {
  Co(this, { type: "String Iterator", string: String(t2), index: 0 });
}, function() {
  var t2, e2 = Lo(this), n2 = e2.string, r2 = e2.index;
  return r2 >= n2.length ? { value: void 0, done: true } : (t2 = jo(n2, r2), e2.index += t2.length, { value: t2, done: false });
});
var Po = { CSSRuleList: 0, CSSStyleDeclaration: 0, CSSValueList: 0, ClientRectList: 0, DOMRectList: 0, DOMStringList: 0, DOMTokenList: 1, DataTransferItemList: 0, FileList: 0, HTMLAllCollection: 0, HTMLCollection: 0, HTMLFormElement: 0, HTMLSelectElement: 0, MediaList: 0, MimeTypeArray: 0, NamedNodeMap: 0, NodeList: 1, PaintRequestList: 0, Plugin: 0, PluginArray: 0, SVGLengthList: 0, SVGNumberList: 0, SVGPathSegList: 0, SVGPointList: 0, SVGStringList: 0, SVGTransformList: 0, SourceBufferList: 0, StyleSheetList: 0, TextTrackCueList: 0, TextTrackList: 0, TouchList: 0 };
var Mo = J.set;
var _o = J.getterFor("Array Iterator");
var Do = Oo(Array, "Array", function(t2, e2) {
  Mo(this, { type: "Array Iterator", target: g(t2), index: 0, kind: e2 });
}, function() {
  var t2 = _o(this), e2 = t2.target, n2 = t2.kind, r2 = t2.index++;
  return !e2 || r2 >= e2.length ? (t2.target = void 0, { value: void 0, done: true }) : "keys" == n2 ? { value: r2, done: false } : "values" == n2 ? { value: e2[r2], done: false } : { value: [r2, e2[r2]], done: false };
}, "values");
Pr.Arguments = Pr.Array, Er("keys"), Er("values"), Er("entries");
var Uo = ne("iterator");
var No = ne("toStringTag");
var Fo = Do.values;
for (Wo2 in Po) {
  zo2 = r[Wo2], $o2 = zo2 && zo2.prototype;
  if ($o2) {
    if ($o2[Uo] !== Fo) try {
      I($o2, Uo, Fo);
    } catch (t2) {
      $o2[Uo] = Fo;
    }
    if ($o2[No] || I($o2, No, Wo2), Po[Wo2]) {
      for (Bo2 in Do) if ($o2[Bo2] !== Do[Bo2]) try {
        I($o2, Bo2, Do[Bo2]);
      } catch (t2) {
        $o2[Bo2] = Do[Bo2];
      }
    }
  }
}
var zo2;
var $o2;
var Bo2;
var Wo2;
var Yo = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "deduplicate", value: function(t3) {
    return Array.from(new Set(t3));
  } }, { key: "flat", value: function(e2) {
    return e2.reduce(function(e3, n2) {
      var r2 = Array.isArray(n2) ? t2.flat(n2) : n2;
      return e3.concat(r2);
    }, []);
  } }, { key: "find", value: function(t3, e2) {
    return t3.find(e2);
  } }, { key: "findIndex", value: function(t3, e2) {
    return t3.findIndex(e2);
  } }]), t2;
}();
var Go = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "today", value: function() {
    return /* @__PURE__ */ new Date();
  } }]), t2;
}();
var Vo = function() {
  function t2() {
    Wt(this, t2);
  }
  return $t(t2, null, [{ key: "range", value: function(t3, e2, n2) {
    return Math.min(Math.max(t3, e2), n2);
  } }, { key: "clamp", value: function(t3, e2, n2) {
    return e2 < n2 ? t3 < e2 ? e2 : t3 > n2 ? n2 : t3 : t3 < n2 ? n2 : t3 > e2 ? e2 : t3;
  } }]), t2;
}();

// ../../node_modules/.pnpm/vue3-colorpicker@2.3.0_@aes_9843de7cc8b629dfff6843adf764afda/node_modules/vue3-colorpicker/index.es.js
var qe2 = Object.defineProperty;
var Ye2 = (e2, t2, o2) => t2 in e2 ? qe2(e2, t2, { enumerable: true, configurable: true, writable: true, value: o2 }) : e2[t2] = o2;
var W2 = (e2, t2, o2) => (Ye2(e2, typeof t2 != "symbol" ? t2 + "" : t2, o2), o2);
var P2 = (e2) => Math.round(e2 * 100) / 100;
var A2 = class {
  constructor(t2) {
    W2(this, "instance");
    W2(this, "alphaValue", 0);
    W2(this, "redValue", 0);
    W2(this, "greenValue", 0);
    W2(this, "blueValue", 0);
    W2(this, "hueValue", 0);
    W2(this, "saturationValue", 0);
    W2(this, "brightnessValue", 0);
    W2(this, "hslSaturationValue", 0);
    W2(this, "lightnessValue", 0);
    W2(this, "initAlpha", () => {
      const t3 = this.instance.getAlpha();
      this.alphaValue = Math.min(1, t3) * 100;
    });
    W2(this, "initLightness", () => {
      const { s: t3, l: o2 } = this.instance.toHsl();
      this.hslSaturationValue = P2(t3), this.lightnessValue = P2(o2);
    });
    W2(this, "initRgb", () => {
      const { r: t3, g: o2, b: n2 } = this.instance.toRgb();
      this.redValue = P2(t3), this.greenValue = P2(o2), this.blueValue = P2(n2);
    });
    W2(this, "initHsb", () => {
      const { h: t3, s: o2, v: n2 } = this.instance.toHsv();
      this.hueValue = Math.min(360, Math.ceil(t3)), this.saturationValue = P2(o2), this.brightnessValue = P2(n2);
    });
    W2(this, "toHexString", () => this.instance.toHexString());
    W2(this, "toRgbString", () => this.instance.toRgbString());
    this.instance = tinycolor(t2), this.initRgb(), this.initHsb(), this.initLightness(), this.initAlpha();
  }
  toString(t2) {
    return this.instance.toString(t2);
  }
  get hex() {
    return this.instance.toHex();
  }
  set hex(t2) {
    this.instance = tinycolor(t2), this.initHsb(), this.initRgb(), this.initAlpha(), this.initLightness();
  }
  // 色调
  set hue(t2) {
    this.saturation === 0 && this.brightness === 0 && (this.saturationValue = 1, this.brightnessValue = 1), this.instance = tinycolor({
      h: P2(t2),
      s: this.saturation,
      v: this.brightness,
      a: this.alphaValue / 100
    }), this.initRgb(), this.initLightness(), this.hueValue = P2(t2);
  }
  get hue() {
    return this.hueValue;
  }
  // 饱和度
  set saturation(t2) {
    this.instance = tinycolor({
      h: this.hue,
      s: P2(t2),
      v: this.brightness,
      a: this.alphaValue / 100
    }), this.initRgb(), this.initLightness(), this.saturationValue = P2(t2);
  }
  get saturation() {
    return this.saturationValue;
  }
  // 明度
  set brightness(t2) {
    this.instance = tinycolor({
      h: this.hue,
      s: this.saturation,
      v: P2(t2),
      a: this.alphaValue / 100
    }), this.initRgb(), this.initLightness(), this.brightnessValue = P2(t2);
  }
  get brightness() {
    return this.brightnessValue;
  }
  // 亮度
  set lightness(t2) {
    this.instance = tinycolor({
      h: this.hue,
      s: this.hslSaturationValue,
      l: P2(t2),
      a: this.alphaValue / 100
    }), this.initRgb(), this.initHsb(), this.lightnessValue = P2(t2);
  }
  get lightness() {
    return this.lightnessValue;
  }
  // red
  set red(t2) {
    const o2 = this.instance.toRgb();
    this.instance = tinycolor({
      ...o2,
      r: P2(t2),
      a: this.alphaValue / 100
    }), this.initHsb(), this.initLightness(), this.redValue = P2(t2);
  }
  get red() {
    return this.redValue;
  }
  // green
  set green(t2) {
    const o2 = this.instance.toRgb();
    this.instance = tinycolor({
      ...o2,
      g: P2(t2),
      a: this.alphaValue / 100
    }), this.initHsb(), this.initLightness(), this.greenValue = P2(t2);
  }
  get green() {
    return this.greenValue;
  }
  // blue
  set blue(t2) {
    const o2 = this.instance.toRgb();
    this.instance = tinycolor({
      ...o2,
      b: P2(t2),
      a: this.alphaValue / 100
    }), this.initHsb(), this.initLightness(), this.blueValue = P2(t2);
  }
  get blue() {
    return this.blueValue;
  }
  // alpha
  set alpha(t2) {
    this.instance.setAlpha(t2 / 100), this.alphaValue = t2;
  }
  get alpha() {
    return this.alphaValue;
  }
  get RGB() {
    return [this.red, this.green, this.blue, parseFloat((this.alpha / 100).toFixed(2))];
  }
  get HSB() {
    return [this.hue, this.saturation, this.brightness, parseFloat((this.alpha / 100).toFixed(2))];
  }
  get HSL() {
    return [
      this.hue,
      this.hslSaturationValue,
      this.lightness,
      parseFloat((this.alpha / 100).toFixed(2))
    ];
  }
};
function Ae2(e2, t2, o2, n2) {
  return `rgba(${[e2, t2, o2, n2 / 100].join(",")})`;
}
var ue2 = (e2, t2, o2) => t2 < o2 ? e2 < t2 ? t2 : e2 > o2 ? o2 : e2 : e2 < o2 ? o2 : e2 > t2 ? t2 : e2;
var fe2 = "color-history";
var Ce2 = 8;
var q = (e2, t2) => {
  const o2 = e2.__vccOpts || e2;
  for (const [n2, i2] of t2)
    o2[n2] = i2;
  return o2;
};
var lt2 = defineComponent({
  name: "Alpha",
  props: {
    color: vue_types_m_default.instanceOf(A2),
    size: vue_types_m_default.oneOf(["small", "default"]).def("default")
  },
  emits: ["change"],
  setup(e2, { emit: t2 }) {
    const o2 = ref(null), n2 = ref(null);
    let i2 = e2.color || new A2();
    const l2 = reactive({
      red: i2.red,
      green: i2.green,
      blue: i2.blue,
      alpha: i2.alpha
    });
    watch(
      () => e2.color,
      (g2) => {
        g2 && (i2 = g2, merge_default(l2, {
          red: g2.red,
          green: g2.green,
          blue: g2.blue,
          alpha: g2.alpha
        }));
      },
      { deep: true }
    );
    const a2 = computed(() => {
      const g2 = Ae2(l2.red, l2.green, l2.blue, 0), d2 = Ae2(l2.red, l2.green, l2.blue, 100);
      return {
        background: `linear-gradient(to right, ${g2} , ${d2})`
      };
    }), r2 = () => {
      if (o2.value && n2.value) {
        const g2 = l2.alpha / 100, d2 = o2.value.getBoundingClientRect(), m2 = n2.value.offsetWidth;
        return Math.round(g2 * (d2.width - m2) + m2 / 2);
      }
      return 0;
    }, c2 = computed(() => ({
      left: r2() + "px",
      top: 0
    })), k2 = (g2) => {
      g2.target !== o2.value && p2(g2);
    }, p2 = (g2) => {
      if (g2.stopPropagation(), o2.value && n2.value) {
        const d2 = o2.value.getBoundingClientRect(), m2 = n2.value.offsetWidth;
        let b2 = g2.clientX - d2.left;
        b2 = Math.max(m2 / 2, b2), b2 = Math.min(b2, d2.width - m2 / 2);
        const h2 = Math.round((b2 - m2 / 2) / (d2.width - m2) * 100);
        i2.alpha = h2, l2.alpha = h2, t2("change", h2);
      }
    };
    return tryOnMounted(() => {
      const g2 = {
        drag: (d2) => {
          p2(d2);
        },
        end: (d2) => {
          p2(d2);
        }
      };
      o2.value && n2.value && Vn.triggerDragEvent(o2.value, g2);
    }), { barElement: o2, cursorElement: n2, getCursorStyle: c2, getBackgroundStyle: a2, onClickSider: k2 };
  }
});
var st2 = (e2) => (pushScopeId("data-v-18925ba6"), e2 = e2(), popScopeId(), e2);
var it2 = st2(() => createBaseVNode("div", { class: "vc-alpha-slider__bar-handle" }, null, -1));
var ct2 = [
  it2
];
function ut2(e2, t2, o2, n2, i2, l2) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["vc-alpha-slider", "transparent", { "small-slider": e2.size === "small" }])
  }, [
    createBaseVNode("div", {
      ref: "barElement",
      class: "vc-alpha-slider__bar",
      style: normalizeStyle(e2.getBackgroundStyle),
      onClick: t2[0] || (t2[0] = (...a2) => e2.onClickSider && e2.onClickSider(...a2))
    }, [
      createBaseVNode("div", {
        class: normalizeClass(["vc-alpha-slider__bar-pointer", { "small-bar": e2.size === "small" }]),
        ref: "cursorElement",
        style: normalizeStyle(e2.getCursorStyle)
      }, ct2, 6)
    ], 4)
  ], 2);
}
var ve2 = q(lt2, [["render", ut2], ["__scopeId", "data-v-18925ba6"]]);
var dt2 = [
  // 第一行
  [
    "#fcc02e",
    "#f67c01",
    "#e64a19",
    "#d81b43",
    "#8e24aa",
    "#512da7",
    "#1f87e8",
    "#008781",
    "#05a045"
  ],
  // 第二行
  [
    "#fed835",
    "#fb8c00",
    "#f5511e",
    "#eb1d4e",
    "#9c28b1",
    "#5d35b0",
    "#2097f3",
    "#029688",
    "#4cb050"
  ],
  // 第三行
  [
    "#ffeb3c",
    "#ffa727",
    "#fe5722",
    "#eb4165",
    "#aa47bc",
    "#673bb7",
    "#42a5f6",
    "#26a59a",
    "#83c683"
  ],
  // 第四行
  [
    "#fff176",
    "#ffb74e",
    "#ff8a66",
    "#f1627e",
    "#b968c7",
    "#7986cc",
    "#64b5f6",
    "#80cbc4",
    "#a5d6a7"
  ],
  // 第五行
  [
    "#fff59c",
    "#ffcc80",
    "#ffab91",
    "#fb879e",
    "#cf93d9",
    "#9ea8db",
    "#90caf8",
    "#b2dfdc",
    "#c8e6ca"
  ],
  // 最后一行
  [
    "transparent",
    "#ffffff",
    "#dedede",
    "#a9a9a9",
    "#4b4b4b",
    "#353535",
    "#212121",
    "#000000",
    "advance"
  ]
];
var gt2 = defineComponent({
  name: "Palette",
  emits: ["change"],
  setup(e2, { emit: t2 }) {
    return { palettes: dt2, computedBgStyle: (i2) => i2 === "transparent" ? i2 : i2 === "advance" ? {} : { background: tinycolor(i2).toRgbString() }, onColorChange: (i2) => {
      t2("change", i2);
    } };
  }
});
var ht2 = { class: "vc-compact" };
var pt2 = ["onClick"];
function ft2(e2, t2, o2, n2, i2, l2) {
  return openBlock(), createElementBlock("div", ht2, [
    (openBlock(true), createElementBlock(Fragment, null, renderList(e2.palettes, (a2, r2) => (openBlock(), createElementBlock("div", {
      key: r2,
      class: "vc-compact__row"
    }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(a2, (c2, k2) => (openBlock(), createElementBlock("div", {
        key: k2,
        class: "vc-compact__color-cube--wrap",
        onClick: (p2) => e2.onColorChange(c2)
      }, [
        createBaseVNode("div", {
          class: normalizeClass([
            "vc-compact__color_cube",
            {
              advance: c2 === "advance",
              transparent: c2 === "transparent"
            }
          ]),
          style: normalizeStyle(e2.computedBgStyle(c2))
        }, null, 6)
      ], 8, pt2))), 128))
    ]))), 128))
  ]);
}
var Ke2 = q(gt2, [["render", ft2], ["__scopeId", "data-v-b969fd48"]]);
var Ct2 = defineComponent({
  name: "Board",
  props: {
    color: vue_types_m_default.instanceOf(A2),
    round: vue_types_m_default.bool.def(false),
    hide: vue_types_m_default.bool.def(true)
  },
  emits: ["change"],
  setup(e2, { emit: t2 }) {
    var y2, f2, w2;
    const o2 = getCurrentInstance(), n2 = {
      h: ((y2 = e2.color) == null ? void 0 : y2.hue) || 0,
      s: 1,
      v: 1
    }, i2 = new A2(n2).toHexString(), l2 = reactive({
      hueColor: i2,
      saturation: ((f2 = e2.color) == null ? void 0 : f2.saturation) || 0,
      brightness: ((w2 = e2.color) == null ? void 0 : w2.brightness) || 0
    }), a2 = ref(0), r2 = ref(0), c2 = ref(), k2 = computed(() => ({
      top: a2.value + "px",
      left: r2.value + "px"
    })), p2 = () => {
      if (o2) {
        const S2 = o2.vnode.el;
        r2.value = l2.saturation * (S2 == null ? void 0 : S2.clientWidth), a2.value = (1 - l2.brightness) * (S2 == null ? void 0 : S2.clientHeight);
      }
    };
    let g2 = false;
    const d2 = (S2) => {
      g2 = true, h2(S2);
    }, m2 = (S2) => {
      g2 && h2(S2);
    }, b2 = () => {
      g2 = false;
    }, h2 = (S2) => {
      if (o2) {
        const F2 = o2.vnode.el, E2 = F2 == null ? void 0 : F2.getBoundingClientRect();
        let L2 = S2.clientX - E2.left, U2 = S2.clientY - E2.top;
        L2 = ue2(L2, 0, E2.width), U2 = ue2(U2, 0, E2.height);
        const J2 = L2 / E2.width, X = ue2(-(U2 / E2.height) + 1, 0, 1);
        r2.value = L2, a2.value = U2, l2.saturation = J2, l2.brightness = X, t2("change", J2, X);
      }
    };
    return tryOnMounted(() => {
      o2 && o2.vnode.el && c2.value && nextTick(() => {
        p2();
      });
    }), whenever(
      () => e2.color,
      (S2) => {
        merge_default(l2, {
          hueColor: new A2({ h: S2.hue, s: 1, v: 1 }).toHexString(),
          saturation: S2.saturation,
          brightness: S2.brightness
        }), p2();
      },
      { deep: true }
    ), { state: l2, cursorElement: c2, getCursorStyle: k2, onClickBoard: d2, onDrag: m2, onDragEnd: b2 };
  }
});
var be2 = (e2) => (pushScopeId("data-v-7f0cdcdf"), e2 = e2(), popScopeId(), e2);
var vt2 = be2(() => createBaseVNode("div", { class: "vc-saturation__white" }, null, -1));
var bt2 = be2(() => createBaseVNode("div", { class: "vc-saturation__black" }, null, -1));
var yt2 = be2(() => createBaseVNode("div", null, null, -1));
var _t2 = [
  yt2
];
function mt2(e2, t2, o2, n2, i2, l2) {
  return openBlock(), createElementBlock("div", {
    ref: "boardElement",
    class: normalizeClass(["vc-saturation", { "vc-saturation__chrome": e2.round, "vc-saturation__hidden": e2.hide }]),
    style: normalizeStyle({ backgroundColor: e2.state.hueColor }),
    onMousedown: t2[0] || (t2[0] = (...a2) => e2.onClickBoard && e2.onClickBoard(...a2)),
    onMousemove: t2[1] || (t2[1] = (...a2) => e2.onDrag && e2.onDrag(...a2)),
    onMouseup: t2[2] || (t2[2] = (...a2) => e2.onDragEnd && e2.onDragEnd(...a2))
  }, [
    vt2,
    bt2,
    createBaseVNode("div", {
      class: "vc-saturation__cursor",
      ref: "cursorElement",
      style: normalizeStyle(e2.getCursorStyle)
    }, _t2, 4)
  ], 38);
}
var ye2 = q(Ct2, [["render", mt2], ["__scopeId", "data-v-7f0cdcdf"]]);
var St2 = defineComponent({
  name: "Hue",
  props: {
    color: vue_types_m_default.instanceOf(A2),
    size: vue_types_m_default.oneOf(["small", "default"]).def("default")
  },
  emits: ["change"],
  setup(e2, { emit: t2 }) {
    const o2 = ref(null), n2 = ref(null);
    let i2 = e2.color || new A2();
    const l2 = reactive({
      hue: i2.hue || 0
    });
    watch(
      () => e2.color,
      (p2) => {
        p2 && (i2 = p2, merge_default(l2, { hue: i2.hue }));
      },
      { deep: true }
    );
    const a2 = () => {
      if (o2.value && n2.value) {
        const p2 = o2.value.getBoundingClientRect(), g2 = n2.value.offsetWidth;
        return l2.hue === 360 ? p2.width - g2 / 2 : l2.hue % 360 * (p2.width - g2) / 360 + g2 / 2;
      }
      return 0;
    }, r2 = computed(() => ({
      left: a2() + "px",
      top: 0
    })), c2 = (p2) => {
      p2.target !== o2.value && k2(p2);
    }, k2 = (p2) => {
      if (p2.stopPropagation(), o2.value && n2.value) {
        const g2 = o2.value.getBoundingClientRect(), d2 = n2.value.offsetWidth;
        let m2 = p2.clientX - g2.left;
        m2 = Math.min(m2, g2.width - d2 / 2), m2 = Math.max(d2 / 2, m2);
        const b2 = Math.round((m2 - d2 / 2) / (g2.width - d2) * 360);
        i2.hue = b2, l2.hue = b2, t2("change", b2);
      }
    };
    return tryOnMounted(() => {
      const p2 = {
        drag: (g2) => {
          k2(g2);
        },
        end: (g2) => {
          k2(g2);
        }
      };
      o2.value && n2.value && Vn.triggerDragEvent(o2.value, p2);
    }), { barElement: o2, cursorElement: n2, getCursorStyle: r2, onClickSider: c2 };
  }
});
var kt2 = (e2) => (pushScopeId("data-v-e1a08576"), e2 = e2(), popScopeId(), e2);
var $t2 = kt2(() => createBaseVNode("div", { class: "vc-hue-slider__bar-handle" }, null, -1));
var wt2 = [
  $t2
];
function Bt2(e2, t2, o2, n2, i2, l2) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["vc-hue-slider", { "small-slider": e2.size === "small" }])
  }, [
    createBaseVNode("div", {
      ref: "barElement",
      class: "vc-hue-slider__bar",
      onClick: t2[0] || (t2[0] = (...a2) => e2.onClickSider && e2.onClickSider(...a2))
    }, [
      createBaseVNode("div", {
        class: normalizeClass(["vc-hue-slider__bar-pointer", { "small-bar": e2.size === "small" }]),
        ref: "cursorElement",
        style: normalizeStyle(e2.getCursorStyle)
      }, wt2, 6)
    ], 512)
  ], 2);
}
var _e2 = q(St2, [["render", Bt2], ["__scopeId", "data-v-e1a08576"]]);
var Ht2 = defineComponent({
  name: "Lightness",
  props: {
    color: vue_types_m_default.instanceOf(A2),
    size: vue_types_m_default.oneOf(["small", "default"]).def("default")
  },
  emits: ["change"],
  setup(e2, { emit: t2 }) {
    const o2 = ref(null), n2 = ref(null);
    let i2 = e2.color || new A2();
    const [l2, a2, r2] = i2.HSL, c2 = reactive({
      hue: l2,
      saturation: a2,
      lightness: r2
    });
    watch(
      () => e2.color,
      (b2) => {
        if (b2) {
          i2 = b2;
          const [h2, y2, f2] = i2.HSL;
          merge_default(c2, {
            hue: h2,
            saturation: y2,
            lightness: f2
          });
        }
      },
      { deep: true }
    );
    const k2 = computed(() => {
      const b2 = tinycolor({
        h: c2.hue,
        s: c2.saturation,
        l: 0.8
      }).toPercentageRgbString(), h2 = tinycolor({
        h: c2.hue,
        s: c2.saturation,
        l: 0.6
      }).toPercentageRgbString(), y2 = tinycolor({
        h: c2.hue,
        s: c2.saturation,
        l: 0.4
      }).toPercentageRgbString(), f2 = tinycolor({
        h: c2.hue,
        s: c2.saturation,
        l: 0.2
      }).toPercentageRgbString();
      return {
        background: [
          `linear-gradient(to right, rgb(255, 255, 255), ${b2}, ${h2}, ${y2}, ${f2}, rgb(0, 0, 0))`,
          `-webkit-linear-gradient(left, rgb(255, 255, 255), ${b2}, ${h2}, ${y2}, ${f2}, rgb(0, 0, 0))`,
          `-moz-linear-gradient(left, rgb(255, 255, 255), ${b2}, ${h2}, ${y2}, ${f2}, rgb(0, 0, 0))`,
          `-ms-linear-gradient(left, rgb(255, 255, 255), ${b2}, ${h2}, ${y2}, ${f2}, rgb(0, 0, 0))`
        ]
      };
    }), p2 = () => {
      if (o2.value && n2.value) {
        const b2 = c2.lightness, h2 = o2.value.getBoundingClientRect(), y2 = n2.value.offsetWidth;
        return (1 - b2) * (h2.width - y2) + y2 / 2;
      }
      return 0;
    }, g2 = computed(() => ({
      left: p2() + "px",
      top: 0
    })), d2 = (b2) => {
      b2.target !== o2.value && m2(b2);
    }, m2 = (b2) => {
      if (b2.stopPropagation(), o2.value && n2.value) {
        const h2 = o2.value.getBoundingClientRect(), y2 = n2.value.offsetWidth;
        let f2 = b2.clientX - h2.left;
        f2 = Math.max(y2 / 2, f2), f2 = Math.min(f2, h2.width - y2 / 2);
        const w2 = 1 - (f2 - y2 / 2) / (h2.width - y2);
        i2.lightness = w2, t2("change", w2);
      }
    };
    return tryOnMounted(() => {
      const b2 = {
        drag: (h2) => {
          m2(h2);
        },
        end: (h2) => {
          m2(h2);
        }
      };
      o2.value && n2.value && Vn.triggerDragEvent(o2.value, b2);
    }), { barElement: o2, cursorElement: n2, getCursorStyle: g2, getBackgroundStyle: k2, onClickSider: d2 };
  }
});
var Rt2 = (e2) => (pushScopeId("data-v-94a50a9e"), e2 = e2(), popScopeId(), e2);
var At2 = Rt2(() => createBaseVNode("div", { class: "vc-lightness-slider__bar-handle" }, null, -1));
var Pt2 = [
  At2
];
function Vt2(e2, t2, o2, n2, i2, l2) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["vc-lightness-slider", { "small-slider": e2.size === "small" }])
  }, [
    createBaseVNode("div", {
      ref: "barElement",
      class: "vc-lightness-slider__bar",
      style: normalizeStyle(e2.getBackgroundStyle),
      onClick: t2[0] || (t2[0] = (...a2) => e2.onClickSider && e2.onClickSider(...a2))
    }, [
      createBaseVNode("div", {
        class: normalizeClass(["vc-lightness-slider__bar-pointer", { "small-bar": e2.size === "small" }]),
        ref: "cursorElement",
        style: normalizeStyle(e2.getCursorStyle)
      }, Pt2, 6)
    ], 4)
  ], 2);
}
var Le2 = q(Ht2, [["render", Vt2], ["__scopeId", "data-v-94a50a9e"]]);
var Mt2 = defineComponent({
  name: "History",
  props: {
    colors: vue_types_m_default.arrayOf(String).def(() => []),
    round: vue_types_m_default.bool.def(false)
  },
  emits: ["change"],
  setup(e2, { emit: t2 }) {
    return { onColorSelect: (n2) => {
      t2("change", n2);
    } };
  }
});
var Et2 = {
  key: 0,
  class: "vc-colorPicker__record"
};
var It2 = { class: "color-list" };
var Kt2 = ["onClick"];
function Lt2(e2, t2, o2, n2, i2, l2) {
  return e2.colors && e2.colors.length > 0 ? (openBlock(), createElementBlock("div", Et2, [
    createBaseVNode("div", It2, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(e2.colors, (a2, r2) => (openBlock(), createElementBlock("div", {
        key: r2,
        class: normalizeClass(["color-item", "transparent", { "color-item__round": e2.round }]),
        onClick: (c2) => e2.onColorSelect(a2)
      }, [
        createBaseVNode("div", {
          class: "color-item__display",
          style: normalizeStyle({ backgroundColor: a2 })
        }, null, 4)
      ], 10, Kt2))), 128))
    ])
  ])) : createCommentVNode("", true);
}
var me2 = q(Mt2, [["render", Lt2], ["__scopeId", "data-v-0f657238"]]);
var Nt2 = defineComponent({
  name: "Display",
  props: {
    color: vue_types_m_default.instanceOf(A2),
    disableAlpha: vue_types_m_default.bool.def(false)
  },
  emits: ["update:color", "change"],
  setup(e2, { emit: t2 }) {
    var m2, b2, h2, y2;
    const { copy: o2, copied: n2, isSupported: i2 } = useClipboard(), l2 = ref("hex"), a2 = reactive({
      color: e2.color,
      hex: (m2 = e2.color) == null ? void 0 : m2.hex,
      alpha: Math.round(((b2 = e2.color) == null ? void 0 : b2.alpha) || 100),
      rgba: (h2 = e2.color) == null ? void 0 : h2.RGB,
      previewBgColor: (y2 = e2.color) == null ? void 0 : y2.toRgbString()
    }), r2 = computed(() => ({
      background: a2.previewBgColor
    })), c2 = () => {
      l2.value = l2.value === "rgba" ? "hex" : "rgba";
    }, k2 = useDebounceFn((f2) => {
      if (!f2.target.value)
        return;
      let w2 = parseInt(f2.target.value.replace("%", ""));
      w2 > 100 && (f2.target.value = "100", w2 = 100), w2 < 0 && (f2.target.value = "0", w2 = 0), isNaN(w2) && (f2.target.value = "100", w2 = 100), !isNaN(w2) && a2.color && (a2.color.alpha = w2), t2("change", a2.color);
    }, 300), p2 = useDebounceFn((f2, w2) => {
      if (a2.color) {
        if (l2.value === "hex") {
          const S2 = f2.target.value.replace("#", "");
          tinycolor(S2).isValid() ? [3, 4].includes(S2.length) && (a2.color.hex = S2) : a2.color.hex = "000000", t2("change", a2.color);
        } else if (l2.value === "rgba" && w2 === 3 && f2.target.value.toString() === "0." && a2.rgba) {
          a2.rgba[w2] = f2.target.value;
          const [S2, F2, E2, L2] = a2.rgba;
          a2.color.hex = tinycolor({ r: S2, g: F2, b: E2 }).toHex(), a2.color.alpha = Math.round(L2 * 100), t2("change", a2.color);
        }
      }
    }, 100), g2 = useDebounceFn((f2, w2) => {
      if (f2.target.value) {
        if (l2.value === "hex") {
          const S2 = f2.target.value.replace("#", "");
          tinycolor(S2).isValid() && a2.color && [6, 8].includes(S2.length) && (a2.color.hex = S2);
        } else if (w2 !== void 0 && a2.rgba && a2.color) {
          if (f2.target.value < 0 && (f2.target.value = 0), w2 === 3 && ((f2.target.value > 1 || isNaN(f2.target.value)) && (f2.target.value = 1), f2.target.value.toString() === "0."))
            return;
          w2 < 3 && f2.target.value > 255 && (f2.target.value = 255), a2.rgba[w2] = f2.target.value;
          const [S2, F2, E2, L2] = a2.rgba;
          a2.color.hex = tinycolor({ r: S2, g: F2, b: E2 }).toHex(), a2.color.alpha = Math.round(L2 * 100);
        }
        t2("change", a2.color);
      }
    }, 300), d2 = () => {
      if (i2 && a2.color) {
        const f2 = l2.value === "hex" ? a2.color.toString(a2.color.alpha === 100 ? "hex6" : "hex8") : a2.color.toRgbString();
        o2(f2 || "");
      }
    };
    return whenever(
      () => e2.color,
      (f2) => {
        f2 && (a2.color = f2, a2.alpha = Math.round(a2.color.alpha), a2.hex = a2.color.hex, a2.rgba = a2.color.RGB);
      },
      { deep: true }
    ), whenever(
      () => a2.color,
      () => {
        a2.color && (a2.previewBgColor = a2.color.toRgbString());
      },
      { deep: true }
    ), {
      state: a2,
      getBgColorStyle: r2,
      inputType: l2,
      copied: n2,
      onInputTypeChange: c2,
      onAlphaBlur: k2,
      onInputChange: g2,
      onBlurChange: p2,
      onCopyColorStr: d2
    };
  }
});
var Wt2 = { class: "vc-display" };
var Dt2 = { class: "vc-current-color vc-transparent" };
var Tt2 = {
  key: 0,
  class: "copy-text"
};
var Ot2 = {
  key: 0,
  style: { display: "flex", flex: "1", gap: "4px", height: "100%" }
};
var zt2 = { class: "vc-color-input" };
var Gt2 = {
  key: 0,
  class: "vc-alpha-input"
};
var Ft2 = ["value"];
var Xt2 = {
  key: 1,
  style: { display: "flex", flex: "1", gap: "4px", height: "100%" }
};
var qt2 = ["value", "onInput", "onBlur"];
function Yt2(e2, t2, o2, n2, i2, l2) {
  return openBlock(), createElementBlock("div", Wt2, [
    createBaseVNode("div", Dt2, [
      createBaseVNode("div", {
        class: "color-cube",
        style: normalizeStyle(e2.getBgColorStyle),
        onClick: t2[0] || (t2[0] = (...a2) => e2.onCopyColorStr && e2.onCopyColorStr(...a2))
      }, [
        e2.copied ? (openBlock(), createElementBlock("span", Tt2, "Copied!")) : createCommentVNode("", true)
      ], 4)
    ]),
    e2.inputType === "hex" ? (openBlock(), createElementBlock("div", Ot2, [
      createBaseVNode("div", zt2, [
        withDirectives(createBaseVNode("input", {
          "onUpdate:modelValue": t2[1] || (t2[1] = (a2) => e2.state.hex = a2),
          maxlength: "8",
          onInput: t2[2] || (t2[2] = (...a2) => e2.onInputChange && e2.onInputChange(...a2)),
          onBlur: t2[3] || (t2[3] = (...a2) => e2.onBlurChange && e2.onBlurChange(...a2))
        }, null, 544), [
          [vModelText, e2.state.hex]
        ])
      ]),
      e2.disableAlpha ? createCommentVNode("", true) : (openBlock(), createElementBlock("div", Gt2, [
        createBaseVNode("input", {
          class: "vc-alpha-input__inner",
          value: e2.state.alpha,
          onInput: t2[4] || (t2[4] = (...a2) => e2.onAlphaBlur && e2.onAlphaBlur(...a2))
        }, null, 40, Ft2),
        createTextVNode("% ")
      ]))
    ])) : e2.state.rgba ? (openBlock(), createElementBlock("div", Xt2, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(e2.state.rgba, (a2, r2) => (openBlock(), createElementBlock("div", {
        class: "vc-color-input",
        key: r2
      }, [
        createBaseVNode("input", {
          value: a2,
          onInput: (c2) => e2.onInputChange(c2, r2),
          onBlur: (c2) => e2.onBlurChange(c2, r2)
        }, null, 40, qt2)
      ]))), 128))
    ])) : createCommentVNode("", true),
    createBaseVNode("div", {
      class: "vc-input-toggle",
      onClick: t2[5] || (t2[5] = (...a2) => e2.onInputTypeChange && e2.onInputTypeChange(...a2))
    }, toDisplayString(e2.inputType), 1)
  ]);
}
var Se2 = q(Nt2, [["render", Yt2], ["__scopeId", "data-v-7334ac20"]]);
var Ut2 = defineComponent({
  name: "FkColorPicker",
  components: { Display: Se2, Alpha: ve2, Palette: Ke2, Board: ye2, Hue: _e2, Lightness: Le2, History: me2 },
  props: {
    color: vue_types_m_default.instanceOf(A2),
    disableHistory: vue_types_m_default.bool.def(false),
    roundHistory: vue_types_m_default.bool.def(false),
    disableAlpha: vue_types_m_default.bool.def(false)
  },
  emits: ["update:color", "change", "advanceChange"],
  setup(e2, { emit: t2 }) {
    const o2 = e2.color || new A2(), n2 = reactive({
      color: o2,
      hex: o2.toHexString(),
      rgb: o2.toRgbString()
    }), i2 = ref(false), l2 = computed(() => ({ background: n2.rgb })), a2 = () => {
      i2.value = false, t2("advanceChange", false);
    }, r2 = useLocalStorage(fe2, [], {}), c2 = useDebounceFn(() => {
      if (e2.disableHistory)
        return;
      const h2 = n2.color.toRgbString();
      if (r2.value = r2.value.filter((y2) => !tinycolor.equals(y2, h2)), !r2.value.includes(h2)) {
        for (; r2.value.length > Ce2; )
          r2.value.pop();
        r2.value.unshift(h2);
      }
    }, 500), k2 = (h2) => {
      h2 === "advance" ? (i2.value = true, t2("advanceChange", true)) : (n2.color.hex = h2, t2("advanceChange", false));
    }, p2 = (h2) => {
      n2.color.alpha = h2;
    }, g2 = (h2) => {
      n2.color.hue = h2;
    }, d2 = (h2, y2) => {
      n2.color.saturation = h2, n2.color.brightness = y2;
    }, m2 = (h2) => {
      n2.color.lightness = h2;
    }, b2 = (h2) => {
      const f2 = h2.target.value.replace("#", "");
      tinycolor(f2).isValid() && (n2.color.hex = f2);
    };
    return whenever(
      () => e2.color,
      (h2) => {
        h2 && (n2.color = h2);
      },
      { deep: true }
    ), whenever(
      () => n2.color,
      () => {
        n2.hex = n2.color.hex, n2.rgb = n2.color.toRgbString(), c2(), t2("update:color", n2.color), t2("change", n2.color);
      },
      { deep: true }
    ), {
      state: n2,
      advancePanelShow: i2,
      onBack: a2,
      onCompactChange: k2,
      onAlphaChange: p2,
      onHueChange: g2,
      onBoardChange: d2,
      onLightChange: m2,
      onInputChange: b2,
      previewStyle: l2,
      historyColors: r2
    };
  }
});
var jt2 = (e2) => (pushScopeId("data-v-48e3c224"), e2 = e2(), popScopeId(), e2);
var Zt2 = { class: "vc-fk-colorPicker" };
var Jt2 = { class: "vc-fk-colorPicker__inner" };
var Qt2 = { class: "vc-fk-colorPicker__header" };
var xt2 = jt2(() => createBaseVNode("div", { class: "back" }, null, -1));
var eo2 = [
  xt2
];
function to2(e2, t2, o2, n2, i2, l2) {
  const a2 = resolveComponent("Palette"), r2 = resolveComponent("Board"), c2 = resolveComponent("Hue"), k2 = resolveComponent("Lightness"), p2 = resolveComponent("Alpha"), g2 = resolveComponent("Display"), d2 = resolveComponent("History");
  return openBlock(), createElementBlock("div", Zt2, [
    createBaseVNode("div", Jt2, [
      createBaseVNode("div", Qt2, [
        e2.advancePanelShow ? (openBlock(), createElementBlock("span", {
          key: 0,
          style: { cursor: "pointer" },
          onClick: t2[0] || (t2[0] = (...m2) => e2.onBack && e2.onBack(...m2))
        }, eo2)) : createCommentVNode("", true)
      ]),
      e2.advancePanelShow ? createCommentVNode("", true) : (openBlock(), createBlock(a2, {
        key: 0,
        onChange: e2.onCompactChange
      }, null, 8, ["onChange"])),
      e2.advancePanelShow ? (openBlock(), createBlock(r2, {
        key: 1,
        color: e2.state.color,
        onChange: e2.onBoardChange
      }, null, 8, ["color", "onChange"])) : createCommentVNode("", true),
      e2.advancePanelShow ? (openBlock(), createBlock(c2, {
        key: 2,
        color: e2.state.color,
        onChange: e2.onHueChange
      }, null, 8, ["color", "onChange"])) : createCommentVNode("", true),
      e2.advancePanelShow ? createCommentVNode("", true) : (openBlock(), createBlock(k2, {
        key: 3,
        color: e2.state.color,
        onChange: e2.onLightChange
      }, null, 8, ["color", "onChange"])),
      e2.disableAlpha ? createCommentVNode("", true) : (openBlock(), createBlock(p2, {
        key: 4,
        color: e2.state.color,
        onChange: e2.onAlphaChange
      }, null, 8, ["color", "onChange"])),
      createVNode(g2, {
        color: e2.state.color,
        "disable-alpha": e2.disableAlpha
      }, null, 8, ["color", "disable-alpha"]),
      e2.disableHistory ? createCommentVNode("", true) : (openBlock(), createBlock(d2, {
        key: 5,
        round: e2.roundHistory,
        colors: e2.historyColors,
        onChange: e2.onCompactChange
      }, null, 8, ["round", "colors", "onChange"]))
    ])
  ]);
}
var Pe2 = q(Ut2, [["render", to2], ["__scopeId", "data-v-48e3c224"]]);
var oo2 = defineComponent({
  name: "ChromeColorPicker",
  components: { Display: Se2, Alpha: ve2, Board: ye2, Hue: _e2, History: me2 },
  props: {
    color: vue_types_m_default.instanceOf(A2),
    disableHistory: vue_types_m_default.bool.def(false),
    roundHistory: vue_types_m_default.bool.def(false),
    disableAlpha: vue_types_m_default.bool.def(false)
  },
  emits: ["update:color", "change"],
  setup(e2, { emit: t2 }) {
    const o2 = e2.color || new A2(), n2 = reactive({
      color: o2,
      hex: o2.toHexString(),
      rgb: o2.toRgbString()
    }), i2 = computed(() => ({ background: n2.rgb })), l2 = useLocalStorage(fe2, [], {}), a2 = useDebounceFn(() => {
      if (e2.disableHistory)
        return;
      const d2 = n2.color.toRgbString();
      if (l2.value = l2.value.filter((m2) => !tinycolor.equals(m2, d2)), !l2.value.includes(d2)) {
        for (; l2.value.length > Ce2; )
          l2.value.pop();
        l2.value.unshift(d2);
      }
    }, 500), r2 = (d2) => {
      n2.color.alpha = d2;
    }, c2 = (d2) => {
      n2.color.hue = d2;
    }, k2 = (d2) => {
      d2.hex !== void 0 && (n2.color.hex = d2.hex), d2.alpha !== void 0 && (n2.color.alpha = d2.alpha);
    }, p2 = (d2, m2) => {
      n2.color.saturation = d2, n2.color.brightness = m2;
    }, g2 = (d2) => {
      d2 !== "advance" && (n2.color.hex = d2);
    };
    return whenever(
      () => e2.color,
      (d2) => {
        d2 && (n2.color = d2);
      },
      { deep: true }
    ), whenever(
      () => n2.color,
      () => {
        n2.hex = n2.color.hex, n2.rgb = n2.color.toRgbString(), a2(), t2("update:color", n2.color), t2("change", n2.color);
      },
      { deep: true }
    ), {
      state: n2,
      previewStyle: i2,
      historyColors: l2,
      onAlphaChange: r2,
      onHueChange: c2,
      onBoardChange: p2,
      onInputChange: k2,
      onCompactChange: g2
    };
  }
});
var no2 = { class: "vc-chrome-colorPicker" };
var ao2 = { class: "vc-chrome-colorPicker-body" };
var ro2 = { class: "chrome-controls" };
var lo2 = { class: "chrome-sliders" };
function so2(e2, t2, o2, n2, i2, l2) {
  const a2 = resolveComponent("Board"), r2 = resolveComponent("Hue"), c2 = resolveComponent("Alpha"), k2 = resolveComponent("Display"), p2 = resolveComponent("History");
  return openBlock(), createElementBlock("div", no2, [
    createVNode(a2, {
      round: true,
      hide: false,
      color: e2.state.color,
      onChange: e2.onBoardChange
    }, null, 8, ["color", "onChange"]),
    createBaseVNode("div", ao2, [
      createBaseVNode("div", ro2, [
        createBaseVNode("div", lo2, [
          createVNode(r2, {
            size: "small",
            color: e2.state.color,
            onChange: e2.onHueChange
          }, null, 8, ["color", "onChange"]),
          e2.disableAlpha ? createCommentVNode("", true) : (openBlock(), createBlock(c2, {
            key: 0,
            size: "small",
            color: e2.state.color,
            onChange: e2.onAlphaChange
          }, null, 8, ["color", "onChange"]))
        ])
      ]),
      createVNode(k2, {
        color: e2.state.color,
        "disable-alpha": e2.disableAlpha
      }, null, 8, ["color", "disable-alpha"]),
      e2.disableHistory ? createCommentVNode("", true) : (openBlock(), createBlock(p2, {
        key: 0,
        round: e2.roundHistory,
        colors: e2.historyColors,
        onChange: e2.onCompactChange
      }, null, 8, ["round", "colors", "onChange"]))
    ])
  ]);
}
var Ve2 = q(oo2, [["render", so2], ["__scopeId", "data-v-2611d66c"]]);
var ke2 = "Vue3ColorPickerProvider";
var io2 = (e2, t2) => {
  const o2 = e2.getBoundingClientRect(), n2 = o2.left + o2.width / 2, i2 = o2.top + o2.height / 2, l2 = Math.abs(n2 - t2.clientX), a2 = Math.abs(i2 - t2.clientY), r2 = Math.sqrt(Math.pow(l2, 2) + Math.pow(a2, 2)), c2 = a2 / r2, k2 = Math.acos(c2);
  let p2 = Math.floor(180 / (Math.PI / k2));
  return t2.clientX > n2 && t2.clientY > i2 && (p2 = 180 - p2), t2.clientX == n2 && t2.clientY > i2 && (p2 = 180), t2.clientX > n2 && t2.clientY == i2 && (p2 = 90), t2.clientX < n2 && t2.clientY > i2 && (p2 = 180 + p2), t2.clientX < n2 && t2.clientY == i2 && (p2 = 270), t2.clientX < n2 && t2.clientY < i2 && (p2 = 360 - p2), p2;
};
var de2 = false;
var co2 = (e2, t2) => {
  const o2 = function(i2) {
    var l2;
    (l2 = t2.drag) == null || l2.call(t2, i2);
  }, n2 = function(i2) {
    var l2;
    document.removeEventListener("mousemove", o2, false), document.removeEventListener("mouseup", n2, false), document.onselectstart = null, document.ondragstart = null, de2 = false, (l2 = t2.end) == null || l2.call(t2, i2);
  };
  e2 && e2.addEventListener("mousedown", (i2) => {
    var l2;
    de2 || (document.onselectstart = () => false, document.ondragstart = () => false, document.addEventListener("mousemove", o2, false), document.addEventListener("mouseup", n2, false), de2 = true, (l2 = t2.start) == null || l2.call(t2, i2));
  });
};
var uo2 = {
  angle: {
    type: Number,
    default: 0
  },
  size: {
    type: Number,
    default: 16,
    validator: (e2) => e2 >= 16
  },
  borderWidth: {
    type: Number,
    default: 1,
    validator: (e2) => e2 >= 1
  },
  borderColor: {
    type: String,
    default: "#666"
  }
};
var go2 = defineComponent({
  name: "Angle",
  props: uo2,
  emits: ["update:angle", "change"],
  setup(e2, {
    emit: t2
  }) {
    const o2 = ref(null), n2 = ref(0);
    watch(() => e2.angle, (r2) => {
      n2.value = r2;
    });
    const i2 = () => {
      let r2 = Number(n2.value);
      isNaN(r2) || (r2 = r2 > 360 || r2 < 0 ? e2.angle : r2, n2.value = r2 === 360 ? 0 : r2, t2("update:angle", n2.value), t2("change", n2.value));
    }, l2 = computed(() => ({
      width: e2.size + "px",
      height: e2.size + "px",
      borderWidth: e2.borderWidth + "px",
      borderColor: e2.borderColor,
      transform: `rotate(${n2.value}deg)`
    })), a2 = (r2) => {
      o2.value && (n2.value = io2(o2.value, r2) % 360, i2());
    };
    return onMounted(() => {
      const r2 = {
        drag: (c2) => {
          a2(c2);
        },
        end: (c2) => {
          a2(c2);
        }
      };
      o2.value && co2(o2.value, r2);
    }), () => createVNode("div", {
      class: "bee-angle"
    }, [createVNode("div", {
      class: "bee-angle__round",
      ref: o2,
      style: l2.value
    }, null)]);
  }
});
var ho2 = defineComponent({
  name: "GradientColorPicker",
  components: { Angle: go2, Display: Se2, Alpha: ve2, Palette: Ke2, Board: ye2, Hue: _e2, Lightness: Le2, History: me2 },
  props: {
    startColor: vue_types_m_default.instanceOf(A2).isRequired,
    endColor: vue_types_m_default.instanceOf(A2).isRequired,
    startColorStop: vue_types_m_default.number.def(0),
    endColorStop: vue_types_m_default.number.def(100),
    angle: vue_types_m_default.number.def(0),
    type: vue_types_m_default.oneOf(["linear", "radial"]).def("linear"),
    disableHistory: vue_types_m_default.bool.def(false),
    roundHistory: vue_types_m_default.bool.def(false),
    disableAlpha: vue_types_m_default.bool.def(false),
    pickerType: vue_types_m_default.oneOf(["fk", "chrome"]).def("fk")
  },
  emits: [
    "update:startColor",
    "update:endColor",
    "update:angle",
    "update:startColorStop",
    "update:endColorStop",
    "startColorChange",
    "endColorChange",
    "advanceChange",
    "angleChange",
    "startColorStopChange",
    "endColorStopChange",
    "typeChange"
  ],
  setup(e2, { emit: t2 }) {
    const o2 = reactive({
      startActive: true,
      startColor: e2.startColor,
      endColor: e2.endColor,
      startColorStop: e2.startColorStop,
      endColorStop: e2.endColorStop,
      angle: e2.angle,
      type: e2.type,
      // rgba
      startColorRgba: e2.startColor.toRgbString(),
      endColorRgba: e2.endColor.toRgbString()
    }), n2 = inject(ke2), i2 = ref(e2.pickerType === "chrome"), l2 = ref(), a2 = ref(), r2 = ref();
    watch(
      () => [e2.startColor, e2.endColor, e2.angle],
      (s2) => {
        o2.startColor = s2[0], o2.endColor = s2[1], o2.angle = s2[2];
      }
    ), watch(
      () => e2.type,
      (s2) => {
        o2.type = s2;
      }
    );
    const c2 = computed({
      get: () => o2.startActive ? o2.startColor : o2.endColor,
      set: (s2) => {
        if (o2.startActive) {
          o2.startColor = s2;
          return;
        }
        o2.endColor = s2;
      }
    }), k2 = computed(() => {
      if (r2.value && l2.value) {
        const s2 = o2.startColorStop / 100, _2 = r2.value.getBoundingClientRect(), H2 = l2.value.offsetWidth;
        return Math.round(s2 * (_2.width - H2) + H2 / 2);
      }
      return 0;
    }), p2 = computed(() => {
      if (r2.value && a2.value) {
        const s2 = o2.endColorStop / 100, _2 = r2.value.getBoundingClientRect(), H2 = a2.value.offsetWidth;
        return Math.round(s2 * (_2.width - H2) + H2 / 2);
      }
      return 0;
    }), g2 = computed(() => {
      let s2 = `background: linear-gradient(${o2.angle}deg, ${o2.startColorRgba} ${o2.startColorStop}%, ${o2.endColorRgba} ${o2.endColorStop}%)`;
      return o2.type === "radial" && (s2 = `background: radial-gradient(circle, ${o2.startColorRgba} ${o2.startColorStop}%, ${o2.endColorRgba} ${o2.endColorStop}%)`), s2;
    }), d2 = (s2) => {
      var _2;
      if (o2.startActive = true, r2.value && l2.value) {
        const H2 = (_2 = r2.value) == null ? void 0 : _2.getBoundingClientRect();
        let N2 = s2.clientX - H2.left;
        N2 = Math.max(l2.value.offsetWidth / 2, N2), N2 = Math.min(N2, H2.width - l2.value.offsetWidth / 2), o2.startColorStop = Math.round(
          (N2 - l2.value.offsetWidth / 2) / (H2.width - l2.value.offsetWidth) * 100
        ), t2("update:startColorStop", o2.startColorStop), t2("startColorStopChange", o2.startColorStop);
      }
    }, m2 = (s2) => {
      var _2;
      if (o2.startActive = false, r2.value && a2.value) {
        const H2 = (_2 = r2.value) == null ? void 0 : _2.getBoundingClientRect();
        let N2 = s2.clientX - H2.left;
        N2 = Math.max(a2.value.offsetWidth / 2, N2), N2 = Math.min(N2, H2.width - a2.value.offsetWidth / 2), o2.endColorStop = Math.round(
          (N2 - a2.value.offsetWidth / 2) / (H2.width - a2.value.offsetWidth) * 100
        ), t2("update:endColorStop", o2.endColorStop), t2("endColorStopChange", o2.endColorStop);
      }
    }, b2 = (s2) => {
      const _2 = s2.target, H2 = parseInt(_2.value.replace("°", ""));
      isNaN(H2) || (o2.angle = H2 % 360), t2("update:angle", o2.angle), t2("angleChange", o2.angle);
    }, h2 = (s2) => {
      o2.angle = s2, t2("update:angle", o2.angle), t2("angleChange", o2.angle);
    }, y2 = (s2) => {
      s2 === "advance" ? (i2.value = true, t2("advanceChange", true)) : (c2.value.hex = s2, t2("advanceChange", false)), L2();
    }, f2 = (s2) => {
      c2.value.alpha = s2, L2();
    }, w2 = (s2) => {
      c2.value.hue = s2, L2();
    }, S2 = (s2, _2) => {
      c2.value.saturation = s2, c2.value.brightness = _2, L2();
    }, F2 = (s2) => {
      c2.value.lightness = s2, L2();
    }, E2 = () => {
      L2();
    }, L2 = () => {
      o2.startActive ? (t2("update:startColor", o2.startColor), t2("startColorChange", o2.startColor)) : (t2("update:endColor", o2.endColor), t2("endColorChange", o2.endColor));
    }, U2 = () => {
      i2.value = false, t2("advanceChange", false);
    }, J2 = () => {
      o2.type = o2.type === "linear" ? "radial" : "linear", t2("typeChange", o2.type);
    }, X = useLocalStorage(fe2, [], {}), ce2 = useDebounceFn(() => {
      if (e2.disableHistory)
        return;
      const s2 = c2.value.toRgbString();
      if (X.value = X.value.filter((_2) => !tinycolor.equals(_2, s2)), !X.value.includes(s2)) {
        for (; X.value.length > Ce2; )
          X.value.pop();
        X.value.unshift(s2);
      }
    }, 500);
    return tryOnMounted(() => {
      a2.value && l2.value && (Vn.triggerDragEvent(a2.value, {
        drag: (s2) => {
          m2(s2);
        },
        end: (s2) => {
          m2(s2);
        }
      }), Vn.triggerDragEvent(l2.value, {
        drag: (s2) => {
          d2(s2);
        },
        end: (s2) => {
          d2(s2);
        }
      }));
    }), whenever(
      () => o2.startColor,
      (s2) => {
        o2.startColorRgba = s2.toRgbString();
      },
      { deep: true }
    ), whenever(
      () => o2.endColor,
      (s2) => {
        o2.endColorRgba = s2.toRgbString();
      },
      { deep: true }
    ), whenever(
      () => c2.value,
      () => {
        ce2();
      },
      { deep: true }
    ), {
      startGradientRef: l2,
      stopGradientRef: a2,
      colorRangeRef: r2,
      state: o2,
      currentColor: c2,
      getStartColorLeft: k2,
      getEndColorLeft: p2,
      gradientBg: g2,
      advancePanelShow: i2,
      onDegreeBlur: b2,
      onCompactChange: y2,
      onAlphaChange: f2,
      onHueChange: w2,
      onBoardChange: S2,
      onLightChange: F2,
      historyColors: X,
      onBack: U2,
      onDegreeChange: h2,
      onDisplayChange: E2,
      onTypeChange: J2,
      lang: n2 == null ? void 0 : n2.lang
    };
  }
});
var Ne2 = (e2) => (pushScopeId("data-v-c4d6d6ea"), e2 = e2(), popScopeId(), e2);
var po2 = { class: "vc-gradient-picker" };
var fo2 = { class: "vc-gradient-picker__header" };
var Co2 = { class: "vc-gradient__types" };
var vo2 = { class: "vc-gradient-wrap__types" };
var bo2 = { class: "vc-picker-degree-input vc-degree-input" };
var yo2 = { class: "vc-degree-input__control" };
var _o2 = ["value"];
var mo2 = { class: "vc-degree-input__panel" };
var So2 = { class: "vc-degree-input__disk" };
var ko2 = { class: "vc-gradient-picker__body" };
var $o = {
  class: "vc-color-range",
  ref: "colorRangeRef"
};
var wo2 = { class: "vc-color-range__container" };
var Bo = { class: "vc-gradient__stop__container" };
var Ho = ["title"];
var Ro2 = Ne2(() => createBaseVNode("span", { class: "vc-gradient__stop--inner" }, null, -1));
var Ao2 = [
  Ro2
];
var Po2 = ["title"];
var Vo2 = Ne2(() => createBaseVNode("span", { class: "vc-gradient__stop--inner" }, null, -1));
var Mo2 = [
  Vo2
];
function Eo2(e2, t2, o2, n2, i2, l2) {
  var b2, h2;
  const a2 = resolveComponent("Angle"), r2 = resolveComponent("Board"), c2 = resolveComponent("Hue"), k2 = resolveComponent("Palette"), p2 = resolveComponent("Lightness"), g2 = resolveComponent("Alpha"), d2 = resolveComponent("Display"), m2 = resolveComponent("History");
  return openBlock(), createElementBlock("div", po2, [
    createBaseVNode("div", fo2, [
      createBaseVNode("div", null, [
        withDirectives(createBaseVNode("div", {
          class: "back",
          style: { cursor: "pointer" },
          onClick: t2[0] || (t2[0] = (...y2) => e2.onBack && e2.onBack(...y2))
        }, null, 512), [
          [vShow, e2.pickerType === "fk" && e2.advancePanelShow]
        ])
      ]),
      createBaseVNode("div", Co2, [
        createBaseVNode("div", vo2, [
          (openBlock(), createElementBlock(Fragment, null, renderList(["linear", "radial"], (y2) => createBaseVNode("div", {
            class: normalizeClass(["vc-gradient__type", { active: e2.state.type === y2 }]),
            key: y2,
            onClick: t2[1] || (t2[1] = (...f2) => e2.onTypeChange && e2.onTypeChange(...f2))
          }, toDisplayString(e2.lang ? e2.lang[y2] : y2), 3)), 64))
        ]),
        withDirectives(createBaseVNode("div", bo2, [
          createBaseVNode("div", yo2, [
            createBaseVNode("input", {
              value: e2.state.angle,
              onBlur: t2[2] || (t2[2] = (...y2) => e2.onDegreeBlur && e2.onDegreeBlur(...y2))
            }, null, 40, _o2),
            createTextVNode("deg ")
          ]),
          createBaseVNode("div", mo2, [
            createBaseVNode("div", So2, [
              createVNode(a2, {
                angle: e2.state.angle,
                "onUpdate:angle": t2[3] || (t2[3] = (y2) => e2.state.angle = y2),
                size: 40,
                onChange: e2.onDegreeChange
              }, null, 8, ["angle", "onChange"])
            ])
          ])
        ], 512), [
          [vShow, e2.state.type === "linear"]
        ])
      ])
    ]),
    createBaseVNode("div", ko2, [
      createBaseVNode("div", $o, [
        createBaseVNode("div", wo2, [
          createBaseVNode("div", {
            class: "vc-background",
            style: normalizeStyle(e2.gradientBg)
          }, null, 4),
          createBaseVNode("div", Bo, [
            createBaseVNode("div", {
              class: normalizeClass(["vc-gradient__stop", {
                "vc-gradient__stop--current": e2.state.startActive
              }]),
              ref: "startGradientRef",
              title: (b2 = e2.lang) == null ? void 0 : b2.start,
              style: normalizeStyle({ left: e2.getStartColorLeft + "px", backgroundColor: e2.state.startColorRgba })
            }, Ao2, 14, Ho),
            createBaseVNode("div", {
              class: normalizeClass(["vc-gradient__stop", {
                "vc-gradient__stop--current": !e2.state.startActive
              }]),
              ref: "stopGradientRef",
              title: (h2 = e2.lang) == null ? void 0 : h2.end,
              style: normalizeStyle({ left: e2.getEndColorLeft + "px", backgroundColor: e2.state.endColorRgba })
            }, Mo2, 14, Po2)
          ])
        ])
      ], 512)
    ]),
    e2.advancePanelShow ? (openBlock(), createBlock(r2, {
      key: 0,
      color: e2.currentColor,
      onChange: e2.onBoardChange
    }, null, 8, ["color", "onChange"])) : createCommentVNode("", true),
    e2.advancePanelShow ? (openBlock(), createBlock(c2, {
      key: 1,
      color: e2.currentColor,
      onChange: e2.onHueChange
    }, null, 8, ["color", "onChange"])) : createCommentVNode("", true),
    e2.advancePanelShow ? createCommentVNode("", true) : (openBlock(), createBlock(k2, {
      key: 2,
      onChange: e2.onCompactChange
    }, null, 8, ["onChange"])),
    e2.advancePanelShow ? createCommentVNode("", true) : (openBlock(), createBlock(p2, {
      key: 3,
      color: e2.currentColor,
      onChange: e2.onLightChange
    }, null, 8, ["color", "onChange"])),
    e2.disableAlpha ? createCommentVNode("", true) : (openBlock(), createBlock(g2, {
      key: 4,
      color: e2.currentColor,
      onChange: e2.onAlphaChange
    }, null, 8, ["color", "onChange"])),
    createVNode(d2, {
      color: e2.currentColor,
      "disable-alpha": e2.disableAlpha,
      onChange: e2.onDisplayChange
    }, null, 8, ["color", "disable-alpha", "onChange"]),
    e2.disableHistory ? createCommentVNode("", true) : (openBlock(), createBlock(m2, {
      key: 5,
      round: e2.roundHistory,
      colors: e2.historyColors,
      onChange: e2.onCompactChange
    }, null, 8, ["round", "colors", "onChange"]))
  ]);
}
var Me2 = q(ho2, [["render", Eo2], ["__scopeId", "data-v-c4d6d6ea"]]);
var Io2 = defineComponent({
  name: "WrapContainer",
  props: {
    theme: vue_types_m_default.oneOf(["white", "black"]).def("white"),
    showTab: vue_types_m_default.bool.def(false),
    activeKey: vue_types_m_default.oneOf(["pure", "gradient"]).def("pure")
  },
  emits: ["update:activeKey", "change"],
  setup(e2, { emit: t2 }) {
    const o2 = reactive({
      activeKey: e2.activeKey
    }), n2 = inject(ke2), i2 = (l2) => {
      o2.activeKey = l2, t2("update:activeKey", l2), t2("change", l2);
    };
    return whenever(
      () => e2.activeKey,
      (l2) => {
        o2.activeKey = l2;
      }
    ), { state: o2, onActiveKeyChange: i2, lang: n2 == null ? void 0 : n2.lang };
  }
});
var Ko = { class: "vc-colorpicker--container" };
var Lo2 = {
  key: 0,
  class: "vc-colorpicker--tabs"
};
var No2 = { class: "vc-colorpicker--tabs__inner" };
var Wo = { class: "vc-btn__content" };
var Do2 = { class: "vc-btn__content" };
function To2(e2, t2, o2, n2, i2, l2) {
  var a2, r2;
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["vc-colorpicker", e2.theme])
  }, [
    createBaseVNode("div", Ko, [
      e2.showTab ? (openBlock(), createElementBlock("div", Lo2, [
        createBaseVNode("div", No2, [
          createBaseVNode("div", {
            class: normalizeClass([
              "vc-colorpicker--tabs__btn",
              {
                "vc-btn-active": e2.state.activeKey === "pure"
              }
            ]),
            onClick: t2[0] || (t2[0] = (c2) => e2.onActiveKeyChange("pure"))
          }, [
            createBaseVNode("button", null, [
              createBaseVNode("div", Wo, toDisplayString((a2 = e2.lang) == null ? void 0 : a2.pure), 1)
            ])
          ], 2),
          createBaseVNode("div", {
            class: normalizeClass([
              "vc-colorpicker--tabs__btn",
              {
                "vc-btn-active": e2.state.activeKey === "gradient"
              }
            ]),
            onClick: t2[1] || (t2[1] = (c2) => e2.onActiveKeyChange("gradient"))
          }, [
            createBaseVNode("button", null, [
              createBaseVNode("div", Do2, toDisplayString((r2 = e2.lang) == null ? void 0 : r2.gradient), 1)
            ])
          ], 2),
          createBaseVNode("div", {
            class: "vc-colorpicker--tabs__bg",
            style: normalizeStyle({
              width: "50%",
              left: `calc(${e2.state.activeKey === "gradient" ? 50 : 0}%)`
            })
          }, null, 4)
        ])
      ])) : createCommentVNode("", true),
      renderSlot(e2.$slots, "default", {}, void 0, true)
    ])
  ], 2);
}
var Oo2 = q(Io2, [["render", To2], ["__scopeId", "data-v-0492277d"]]);
var zo = {
  start: "Start",
  end: "End",
  pure: "Pure",
  gradient: "Gradient",
  linear: "linear",
  radial: "radial"
};
var Go2 = {
  start: "开始",
  end: "结束",
  pure: "纯色",
  gradient: "渐变",
  linear: "线性",
  radial: "径向"
};
var Fo2 = {
  En: zo,
  "ZH-cn": Go2
};
var Xo = {
  isWidget: vue_types_m_default.bool.def(false),
  pickerType: vue_types_m_default.oneOf(["fk", "chrome"]).def("fk"),
  shape: vue_types_m_default.oneOf(["circle", "square"]).def("square"),
  pureColor: {
    type: [String, Object],
    default: "#000000"
  },
  gradientColor: vue_types_m_default.string.def(
    "linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)"
  ),
  format: {
    type: String,
    default: "rgb"
  },
  disableAlpha: vue_types_m_default.bool.def(false),
  disableHistory: vue_types_m_default.bool.def(false),
  roundHistory: vue_types_m_default.bool.def(false),
  useType: vue_types_m_default.oneOf(["pure", "gradient", "both"]).def("pure"),
  activeKey: vue_types_m_default.oneOf(["pure", "gradient"]).def("pure"),
  lang: {
    type: String,
    default: "ZH-cn"
  },
  zIndex: vue_types_m_default.number.def(9999),
  pickerContainer: {
    type: [String, HTMLElement],
    default: "body"
  },
  debounce: vue_types_m_default.number.def(100),
  theme: vue_types_m_default.oneOf(["white", "black"]).def("white"),
  blurClose: vue_types_m_default.bool.def(false),
  defaultPopup: vue_types_m_default.bool.def(false)
};
var qo = defineComponent({
  name: "ColorPicker",
  components: { FkColorPicker: Pe2, ChromeColorPicker: Ve2, GradientColorPicker: Me2, WrapContainer: Oo2 },
  inheritAttrs: false,
  props: Xo,
  emits: [
    "update:pureColor",
    "pureColorChange",
    "update:gradientColor",
    "gradientColorChange",
    "update:activeKey",
    "activeKeyChange"
  ],
  setup(e2, { emit: t2 }) {
    provide(ke2, {
      lang: computed(() => Fo2[e2.lang || "ZH-cn"])
    });
    const o2 = !!useSlots().extra, n2 = reactive({
      pureColor: e2.pureColor || "",
      activeKey: e2.useType === "gradient" ? "gradient" : e2.activeKey,
      //  "pure" | "gradient"
      isAdvanceMode: false
    }), i2 = new A2("#000"), l2 = new A2("#000"), a2 = new A2(n2.pureColor), r2 = reactive({
      startColor: i2,
      endColor: l2,
      startColorStop: 0,
      endColorStop: 100,
      angle: 0,
      type: "linear",
      gradientColor: e2.gradientColor
    }), c2 = ref(a2), k2 = ref(e2.defaultPopup), p2 = ref(null), g2 = ref(null);
    let d2 = null;
    const m2 = computed(() => ({
      background: n2.activeKey !== "gradient" ? tinycolor(n2.pureColor).toRgbString() : r2.gradientColor
    })), b2 = computed(() => n2.activeKey === "gradient" ? Me2.name : e2.pickerType === "fk" ? Pe2.name : Ve2.name), h2 = (s2) => {
      n2.isAdvanceMode = s2;
    }, y2 = computed(() => {
      const s2 = {
        disableAlpha: e2.disableAlpha,
        disableHistory: e2.disableHistory,
        roundHistory: e2.roundHistory,
        pickerType: e2.pickerType
      };
      return n2.activeKey === "gradient" ? {
        ...s2,
        startColor: r2.startColor,
        endColor: r2.endColor,
        angle: r2.angle,
        type: r2.type,
        startColorStop: r2.startColorStop,
        endColorStop: r2.endColorStop,
        onStartColorChange: (_2) => {
          r2.startColor = _2, E2();
        },
        onEndColorChange: (_2) => {
          r2.endColor = _2, E2();
        },
        onStartColorStopChange: (_2) => {
          r2.startColorStop = _2, E2();
        },
        onEndColorStopChange: (_2) => {
          r2.endColorStop = _2, E2();
        },
        onAngleChange: (_2) => {
          r2.angle = _2, E2();
        },
        onTypeChange: (_2) => {
          r2.type = _2, E2();
        },
        onAdvanceChange: h2
      } : {
        ...s2,
        disableAlpha: e2.disableAlpha,
        disableHistory: e2.disableHistory,
        roundHistory: e2.roundHistory,
        color: c2.value,
        onChange: J2,
        onAdvanceChange: h2
      };
    }), f2 = () => {
      k2.value = true, d2 ? d2.update() : U2();
    }, w2 = () => {
      k2.value = false;
    }, S2 = useDebounceFn(() => {
      !e2.isWidget && e2.blurClose && w2();
    }, 100);
    onClickOutside(g2, () => {
      w2();
    });
    const F2 = () => {
      var s2, _2, H2, N2;
      try {
        const [z2] = (0, import_gradient_parser.parse)(r2.gradientColor);
        if (z2 && z2.type.includes("gradient") && z2.colorStops.length >= 2) {
          const $e2 = z2.colorStops[0], we2 = z2.colorStops[1];
          r2.startColorStop = Number((s2 = $e2.length) == null ? void 0 : s2.value) || 0, r2.endColorStop = Number((_2 = we2.length) == null ? void 0 : _2.value) || 0, z2.type === "linear-gradient" && ((H2 = z2.orientation) == null ? void 0 : H2.type) === "angular" && (r2.angle = Number((N2 = z2.orientation) == null ? void 0 : N2.value) || 0), r2.type = z2.type.split("-")[0];
          const [We2, De2, Te2, Oe2] = $e2.value, [ze2, Ge2, Fe2, Xe2] = we2.value;
          r2.startColor = new A2({
            r: Number(We2),
            g: Number(De2),
            b: Number(Te2),
            a: Number(Oe2)
          }), r2.endColor = new A2({
            r: Number(ze2),
            g: Number(Ge2),
            b: Number(Fe2),
            a: Number(Xe2)
          });
        }
      } catch (z2) {
        console.log(`[Parse Color]: ${z2}`);
      }
    }, E2 = useDebounceFn(() => {
      const s2 = L2();
      try {
        r2.gradientColor = (0, import_gradient_parser.stringify)(s2), t2("update:gradientColor", r2.gradientColor), t2("gradientColorChange", r2.gradientColor);
      } catch (_2) {
        console.log(_2);
      }
    }, e2.debounce), L2 = () => {
      const s2 = [], _2 = r2.startColor.RGB.map((z2) => z2.toString()), H2 = r2.endColor.RGB.map((z2) => z2.toString()), N2 = [
        {
          type: "rgba",
          value: [_2[0], _2[1], _2[2], _2[3]],
          length: { value: r2.startColorStop + "", type: "%" }
        },
        {
          type: "rgba",
          value: [H2[0], H2[1], H2[2], H2[3]],
          length: { value: r2.endColorStop + "", type: "%" }
        }
      ];
      return r2.type === "linear" ? s2.push({
        type: "linear-gradient",
        orientation: { type: "angular", value: r2.angle + "" },
        colorStops: N2
      }) : r2.type === "radial" && s2.push({
        type: "radial-gradient",
        orientation: [{ type: "shape", value: "circle" }],
        colorStops: N2
      }), s2;
    }, U2 = () => {
      p2.value && g2.value && (d2 = createPopper3(p2.value, g2.value, {
        placement: "auto",
        modifiers: [
          {
            name: "offset",
            options: {
              offset: [0, 8]
            }
          },
          {
            name: "flip",
            options: {
              allowedAutoPlacements: ["top", "bottom", "left", "right"],
              rootBoundary: "viewport"
            }
          }
        ]
      }));
    }, J2 = (s2) => {
      c2.value = s2, n2.pureColor = s2.toString(e2.format), X();
    }, X = useDebounceFn(() => {
      t2("update:pureColor", n2.pureColor), t2("pureColorChange", n2.pureColor);
    }, e2.debounce), ce2 = (s2) => {
      n2.activeKey = s2, t2("update:activeKey", s2), t2("activeKeyChange", s2);
    };
    return tryOnMounted(() => {
      F2(), d2 || U2();
    }), whenever(
      () => e2.gradientColor,
      (s2) => {
        s2 != r2.gradientColor && (r2.gradientColor = s2);
      }
    ), whenever(
      () => r2.gradientColor,
      () => {
        F2();
      }
    ), whenever(
      () => e2.activeKey,
      (s2) => {
        n2.activeKey = s2;
      }
    ), whenever(
      () => e2.useType,
      (s2) => {
        n2.activeKey !== "gradient" && s2 === "gradient" ? n2.activeKey = "gradient" : n2.activeKey = "pure";
      }
    ), whenever(
      () => e2.pureColor,
      (s2) => {
        tinycolor.equals(s2, n2.pureColor) || (n2.pureColor = s2, c2.value = new A2(s2));
      },
      { deep: true }
    ), {
      colorCubeRef: p2,
      pickerRef: g2,
      showPicker: k2,
      colorInstance: c2,
      getBgColorStyle: m2,
      getComponentName: b2,
      getBindArgs: y2,
      state: n2,
      hasExtra: o2,
      onColorChange: J2,
      onShowPicker: f2,
      onActiveKeyChange: ce2,
      onAutoClose: S2
    };
  }
});
var Yo2 = {
  key: 0,
  class: "vc-color-extra"
};
var Uo2 = {
  key: 0,
  class: "vc-color-extra"
};
function jo2(e2, t2, o2, n2, i2, l2) {
  const a2 = resolveComponent("WrapContainer");
  return openBlock(), createElementBlock(Fragment, null, [
    e2.isWidget ? (openBlock(), createBlock(a2, {
      key: 0,
      "active-key": e2.state.activeKey,
      "onUpdate:activeKey": t2[0] || (t2[0] = (r2) => e2.state.activeKey = r2),
      "show-tab": e2.useType === "both",
      style: normalizeStyle({ zIndex: e2.zIndex }),
      theme: e2.theme,
      onChange: e2.onActiveKeyChange
    }, {
      default: withCtx(() => [
        (openBlock(), createBlock(resolveDynamicComponent(e2.getComponentName), mergeProps({ key: e2.getComponentName }, e2.getBindArgs), null, 16)),
        e2.hasExtra ? (openBlock(), createElementBlock("div", Yo2, [
          renderSlot(e2.$slots, "extra", {}, void 0, true)
        ])) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["active-key", "show-tab", "style", "theme", "onChange"])) : createCommentVNode("", true),
    e2.isWidget ? createCommentVNode("", true) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
      createBaseVNode("div", {
        class: normalizeClass(["vc-color-wrap transparent", { round: e2.shape === "circle" }]),
        ref: "colorCubeRef"
      }, [
        createBaseVNode("div", {
          class: "current-color",
          style: normalizeStyle(e2.getBgColorStyle),
          onClick: t2[1] || (t2[1] = (...r2) => e2.onShowPicker && e2.onShowPicker(...r2))
        }, null, 4)
      ], 2),
      (openBlock(), createBlock(Teleport, { to: e2.pickerContainer }, [
        withDirectives(createBaseVNode("div", {
          ref: "pickerRef",
          style: normalizeStyle({ zIndex: e2.zIndex }),
          onMouseleave: t2[3] || (t2[3] = (...r2) => e2.onAutoClose && e2.onAutoClose(...r2))
        }, [
          e2.showPicker ? (openBlock(), createBlock(a2, {
            key: 0,
            "show-tab": e2.useType === "both" && !e2.state.isAdvanceMode,
            theme: e2.theme,
            "active-key": e2.state.activeKey,
            "onUpdate:activeKey": t2[2] || (t2[2] = (r2) => e2.state.activeKey = r2),
            onChange: e2.onActiveKeyChange
          }, {
            default: withCtx(() => [
              (openBlock(), createBlock(resolveDynamicComponent(e2.getComponentName), mergeProps({ key: e2.getComponentName }, e2.getBindArgs), null, 16)),
              e2.hasExtra ? (openBlock(), createElementBlock("div", Uo2, [
                renderSlot(e2.$slots, "extra", {}, void 0, true)
              ])) : createCommentVNode("", true)
            ]),
            _: 3
          }, 8, ["show-tab", "theme", "active-key", "onChange"])) : createCommentVNode("", true)
        ], 36), [
          [vShow, e2.showPicker]
        ])
      ], 8, ["to"]))
    ], 64))
  ], 64);
}
var re2 = q(qo, [["render", jo2], ["__scopeId", "data-v-354ca836"]]);
var rn2 = {
  install: (e2) => {
    e2.component(re2.name, re2), e2.component("Vue3" + re2.name, re2);
  }
};
export {
  re2 as ColorPicker,
  rn2 as default
};
//# sourceMappingURL=vue3-colorpicker.js.map
