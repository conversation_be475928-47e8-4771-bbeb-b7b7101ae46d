<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';
import { computed, ref } from 'vue';

import { Input, Textarea, Select, TreeSelect, RadioGroup, CheckboxGroup, DatePicker, Form, FormItem } from 'ant-design-vue';
import { ImageUpload, FileUpload } from '#/components/upload';
import { Tinymce } from '#/components/tinymce';
import { getPopupContainer } from '@vben/utils';
import { pick } from 'lodash-es';

import { getDictOptions } from '#/utils/dict';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { warehouseAdd, warehouseInfo, warehouseUpdate } from '#/api/wms/warehouse';
import type { WarehouseForm } from '#/api/wms/warehouse/model';
import { getDeptTree } from '#/api/system/user';
import { useBeforeCloseDiff } from '#/utils/popup';
import { addFullName } from '@vben/utils';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// 部门树数据
const deptTreeData = ref<any[]>([]);

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<WarehouseForm> = {
  warehouseId: undefined,
  warehouseNumber: undefined,
  warehouseName: undefined,
  warehouseType: undefined,
  warehouseInventoryStatus: undefined,
  warehouseRecevingStatus: undefined,
  deptIds: [],
  remark: undefined
}

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<WarehouseForm>>({
    warehouseNumber: [
      { required: true, message: "仓库编码不能为空" }
    ],
    warehouseName: [
      { required: true, message: "仓库名称不能为空" }
    ],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

/**
 * 初始化部门选择
 */
async function setupDeptSelect() {
  const deptTree = await getDeptTree();
  // 选中后显示在输入框的值 即父节点 / 子节点
  addFullName(deptTree, 'label', ' / ');
  deptTreeData.value = deptTree;
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    // 初始化部门选择
    await setupDeptSelect();

    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await warehouseInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value ? warehouseUpdate(data) : warehouseAdd(data));
    resetInitialized();
    emit('reload');
    drawerApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicDrawer :title="title">
    <Form :label-col="{ span: 4 }">
      <FormItem label="仓库编码" v-bind="validateInfos.warehouseNumber">
        <Input v-model:value="formData.warehouseNumber" :placeholder="$t('ui.formRules.required')" />
      </FormItem>
      <FormItem label="仓库名称" v-bind="validateInfos.warehouseName">
        <Input v-model:value="formData.warehouseName" :placeholder="$t('ui.formRules.required')" />
      </FormItem>
      <FormItem label="仓库属性" v-bind="validateInfos.warehouseType">
        <Select
          v-model:value="formData.warehouseType"
          :options="getDictOptions('wms_warehouse_type')"
          :getPopupContainer="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="库存状态" v-bind="validateInfos.warehouseInventoryStatus">
        <Select
          v-model:value="formData.warehouseInventoryStatus"
          :options="getDictOptions('wms_warehouse_instocktype')"
          :getPopupContainer="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="收料状态" v-bind="validateInfos.warehouseRecevingStatus">
        <Select
          v-model:value="formData.warehouseRecevingStatus"
          :options="getDictOptions('wms_warehouse_instocktype')"
          :getPopupContainer="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="所属部门" v-bind="validateInfos.deptIds">
        <TreeSelect
          v-model:value="formData.deptIds"
          :tree-data="deptTreeData"
          :field-names="{ key: 'id', value: 'id', children: 'children' }"
          :tree-node-label-prop="'fullName'"
          :tree-node-filter-prop="'label'"
          :tree-default-expand-all="true"
          :tree-line="{ showLeafIcon: false }"
          :get-popup-container="getPopupContainer"
          :placeholder="'请选择所属部门'"
          :show-search="true"
          :multiple="true"
        />
      </FormItem>
      <FormItem label="备注" v-bind="validateInfos.remark">
        <Textarea 
          v-model:value="formData.remark" 
          :placeholder="$t('ui.formRules.required')" 
          :rows="4" 
        />
      </FormItem>
    </Form>
  </BasicDrawer>
</template>

