var k=Object.defineProperty;var g=Object.getOwnPropertySymbols;var y=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var h=(o,t,e)=>t in o?k(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,b=(o,t)=>{for(var e in t||(t={}))y.call(t,e)&&h(o,e,t[e]);if(g)for(var e of g(t))I.call(t,e)&&h(o,e,t[e]);return o};var m=(o,t,e)=>new Promise((d,l)=>{var p=r=>{try{i(e.next(r))}catch(s){l(s)}},c=r=>{try{i(e.throw(r))}catch(s){l(s)}},i=r=>r.done?d(r.value):Promise.resolve(r.value).then(p,c);i((e=e.apply(o,t)).next())});import{av as N}from"./bootstrap-DCMzVRvD.js";import"./vxe-table-DzEj5Fop.js";import{f as S,g as B}from"./index-Ds1FCatV.js";import{d as V,h as q,o as z,b as w,w as A,a as O}from"../jse/index-index-C-MnMZEz.js";import{u as R}from"./use-drawer-6qcpK-D1.js";import{u as v}from"./use-vxe-grid-BC7vZzEr.js";const D=()=>[{component:"Input",fieldName:"userName",label:"用户账号"},{component:"Input",fieldName:"phonenumber",label:"手机号码"}],u=[{type:"checkbox",width:60},{title:"用户账号",field:"userName"},{title:"用户昵称",field:"nickName"},{title:"邮箱",field:"email"},{title:"手机号",field:"phonenumber"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],W=V({__name:"role-assign-drawer",emits:["reload"],setup(o,{emit:t}){const e=t,[d,l]=R({onConfirm:x,onCancel:f,destroyOnClose:!0}),c=N().params.roleId,i={commonConfig:{labelWidth:80},schema:D(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"},r={checkboxConfig:{highlight:!0,reserve:!0,trigger:"row"},columns:u==null?void 0:u.filter(a=>a.field!=="action"),height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(_,...J)=>m(null,[_,...J],function*({page:a},n={}){return yield B(b({pageNum:a.currentPage,pageSize:a.pageSize,roleId:c},n))})}},rowConfig:{keyField:"userId"}},[s,C]=v({formOptions:i,gridOptions:r});function x(){return m(this,null,function*(){const n=C.grid.getCheckboxRecords().map(_=>_.userId);n.length>0&&(yield S(c,n)),f(),e("reload")})}function f(){l.close()}return(a,n)=>(z(),q(w(d),{class:"w-[800px]",title:"选择用户"},{default:A(()=>[O(w(s))]),_:1}))}});export{W as _,u as c,D as q};
