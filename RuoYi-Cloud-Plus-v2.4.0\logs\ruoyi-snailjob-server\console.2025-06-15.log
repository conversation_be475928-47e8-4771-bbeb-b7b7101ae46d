2025-06-15 21:07:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:07:49 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 47936 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:07:49 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-15 21:07:49 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 21:07:49 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:07:50 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-15 21:07:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-15 21:07:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-15 21:07:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 21:07:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-15 21:07:51 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-15 21:07:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1285 ms
2025-06-15 21:07:52 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-15 21:07:53 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:53 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:53 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-7] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:53 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 21:07:54 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:08:07 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7343922c
2025-06-15 21:08:08 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:08:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-15 21:08:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-15 21:08:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-15 21:08:08 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-15 21:08:08 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-15 21:08:08 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server *************:8800 register finished
2025-06-15 21:08:10 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:08:10 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-15 21:08:10 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-15 21:08:10 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-15 21:08:10 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-15 21:08:10 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-15 21:08:10 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 23.665 seconds (process running for 24.163)
2025-06-15 21:08:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:08:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-15 21:08:10 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 21:08:10 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:08:10 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 21:08:10 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-15 21:08:10 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62cc079a
2025-06-15 21:08:10 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 21:08:10 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934236414295494656]
2025-06-15 21:08:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-15 21:08:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-15 21:14:36 [snail-job-scheduled-thread-1] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1934238099071909888]
2025-06-15 21:47:00 [http-nio-8800-exec-9] ERROR c.a.c.n.d.NacosDiscoveryClient - get service name from nacos server failed.
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.getServiceList(NamingGrpcClientProxy.java:370)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.getServiceList(NamingClientProxyDelegate.java:160)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:548)
	at com.alibaba.nacos.client.naming.NacosNamingService.getServicesOfServer(NacosNamingService.java:536)
	at com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery.getServices(NacosServiceDiscovery.java:70)
	at com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient.getServices(NacosDiscoveryClient.java:80)
	at org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClient.getServices(CompositeDiscoveryClient.java:68)
	at org.springframework.cloud.client.discovery.health.DiscoveryClientHealthIndicator.health(DiscoveryClientHealthIndicator.java:73)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.getHealth(HealthEndpointWebExtension.java:94)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.getHealth(HealthEndpointWebExtension.java:47)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.health(HealthEndpointWebExtension.java:80)
	at org.springframework.boot.actuate.health.HealthEndpointWebExtension.health(HealthEndpointWebExtension.java:69)
	at jdk.internal.reflect.GeneratedMethodAccessor150.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$ServletWebOperationAdapter.handle(AbstractWebMvcEndpointHandlerMapping.java:327)
	at org.springframework.boot.actuate.endpoint.web.servlet.AbstractWebMvcEndpointHandlerMapping$OperationHandler.handle(AbstractWebMvcEndpointHandlerMapping.java:434)
	at jdk.internal.reflect.GeneratedMethodAccessor133.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.aizuda.snailjob.server.starter.config.ActuatorAuthFilter.doFilter(ActuatorAuthFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:23 [nacos.client.config.listener.task-0] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 21:49:24 [nacos.client.config.listener.task-0] INFO  o.s.c.e.event.RefreshEventListener - Refresh keys changed: [datasource.wms.password, datasource.wms.url, datasource.wms.username]
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:04:00 [nacos.client.config.listener.task-0] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:04:00 [nacos.client.config.listener.task-0] INFO  o.s.c.e.event.RefreshEventListener - Refresh keys changed: [datasource.wms.password, datasource.wms.url, datasource.wms.username]
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:08 [nacos.client.config.listener.task-0] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:05:08 [nacos.client.config.listener.task-0] INFO  o.s.c.e.event.RefreshEventListener - Refresh keys changed: [datasource.wms.password, datasource.wms.url, datasource.wms.username]
2025-06-15 22:33:47 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-15 22:33:47 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-15 22:33:47 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-15 22:33:47 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-15 22:33:47 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-15 22:33:47 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.5.0
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-06-15 22:33:47 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1934236414295494656]
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.5.0
2025-06-15 22:33:47 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-06-15 22:33:47 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-15 22:33:48 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:33:48 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-snailjob-server', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/snail-job/actuator, IPv6=[240e:3a1:bc85:beb1:187:bb4c:9c3e:8a7b], username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=8800, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:179)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-15 22:42:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:42:44 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.14 with PID 51832 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-snailjob-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:42:44 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-06-15 22:42:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP] success
2025-06-15 22:42:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:42:45 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=237668f5-81d7-37c9-98a9-7639bf465a75
2025-06-15 22:42:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-06-15 22:42:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-06-15 22:42:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 22:42:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-15 22:42:46 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-06-15 22:42:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1370 ms
2025-06-15 22:42:47 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-06-15 22:42:48 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:48 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:48 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:48 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-06-15 22:42:49 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:42:57 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@46383a78
2025-06-15 22:42:59 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:42:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 20 endpoints beneath base path '/actuator'
2025-06-15 22:42:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-06-15 22:42:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-06-15 22:42:59 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-15 22:42:59 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-15 22:42:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-snailjob-server *************:8800 register finished
2025-06-15 22:43:00 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-06-15 22:43:00 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.5.0
2025-06-15 22:43:00 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-06-15 22:43:00 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-06-15 22:43:00 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-06-15 22:43:00 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.5.0
2025-06-15 22:43:00 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 19.49 seconds (process running for 20.047)
2025-06-15 22:43:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:43:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-snailjob-server.yml, group=DEFAULT_GROUP
2025-06-15 22:43:00 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 22:43:01 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3907ae02
2025-06-15 22:43:01 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 22:43:01 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1934260300768702464]
2025-06-15 22:43:01 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 22:43:01 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 22:43:01 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 22:43:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-06-15 22:43:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-06-15 22:47:07 [snail-job-scheduled-thread-4] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1934261381363994624]
