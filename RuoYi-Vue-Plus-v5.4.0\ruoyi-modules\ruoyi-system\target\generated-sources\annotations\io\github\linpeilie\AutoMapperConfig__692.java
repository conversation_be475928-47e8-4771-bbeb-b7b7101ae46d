package io.github.linpeilie;

import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__2;
import org.dromara.system.domain.SysClientToSysClientVoMapper__2;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__2;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__2;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__2;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__2;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__2;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__2;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__2;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__2;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__2;
import org.dromara.system.domain.SysOssToSysOssVoMapper__2;
import org.dromara.system.domain.SysPostToSysPostVoMapper__2;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__2;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__2;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper__2;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__2;
import org.dromara.system.domain.SysUserToSysUserVoMapper__2;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__2;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__2;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__2;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__2;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__2;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__2;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__2;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__2;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__2;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__2;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__2;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__2;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__2;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__2;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__2;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__2;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__2;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__2;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__2;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__2;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__2;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__2;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__2;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__2;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__2;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__2;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__2;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__2;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__2;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__2;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__2;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__2;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__2;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__2;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__2;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__692.class, SysConfigVoToSysConfigMapper__2.class, SysDictTypeToSysDictTypeVoMapper__2.class, SysOssConfigBoToSysOssConfigMapper__2.class, SysTenantVoToSysTenantMapper__2.class, SysDeptBoToSysDeptMapper__2.class, SysLogininforToSysLogininforVoMapper__2.class, SysConfigBoToSysConfigMapper__2.class, SysOssConfigToSysOssConfigVoMapper__2.class, SysDeptToSysDeptVoMapper__2.class, SysDictDataVoToSysDictDataMapper__2.class, SysDictDataBoToSysDictDataMapper__2.class, SysClientVoToSysClientMapper__2.class, SysDeptVoToSysDeptMapper__2.class, SysLogininforVoToSysLogininforMapper__2.class, SysNoticeToSysNoticeVoMapper__2.class, SysMenuVoToSysMenuMapper__2.class, SysPostVoToSysPostMapper__2.class, SysOperLogBoToOperLogEventMapper__2.class, SysSocialToSysSocialVoMapper__2.class, SysTenantPackageVoToSysTenantPackageMapper__2.class, SysLogininforBoToSysLogininforMapper__2.class, SysPostBoToSysPostMapper__2.class, SysOssToSysOssVoMapper__2.class, SysOperLogBoToSysOperLogMapper__2.class, SysClientToSysClientVoMapper__2.class, SysDictTypeBoToSysDictTypeMapper__2.class, SysNoticeBoToSysNoticeMapper__2.class, SysRoleBoToSysRoleMapper__2.class, SysRoleToSysRoleVoMapper__2.class, SysUserToSysUserVoMapper__2.class, SysOssVoToSysOssMapper__2.class, SysSocialVoToSysSocialMapper__2.class, SysOperLogToSysOperLogVoMapper__2.class, SysSocialBoToSysSocialMapper__2.class, SysTenantToSysTenantVoMapper__2.class, SysMenuBoToSysMenuMapper__2.class, SysOssConfigVoToSysOssConfigMapper__2.class, SysOssBoToSysOssMapper__2.class, OperLogEventToSysOperLogBoMapper__2.class, SysDictDataToSysDictDataVoMapper__2.class, SysTenantPackageToSysTenantPackageVoMapper__2.class, SysRoleVoToSysRoleMapper__2.class, SysTenantPackageBoToSysTenantPackageMapper__2.class, SysConfigToSysConfigVoMapper__2.class, SysDictTypeVoToSysDictTypeMapper__2.class, SysMenuToSysMenuVoMapper__2.class, SysUserVoToSysUserMapper__2.class, SysTenantBoToSysTenantMapper__2.class, SysNoticeVoToSysNoticeMapper__2.class, SysOperLogVoToSysOperLogMapper__2.class, SysClientBoToSysClientMapper__2.class, SysPostToSysPostVoMapper__2.class, SysUserBoToSysUserMapper__2.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__692 {
}
