import{d as r,c as o,o as n,n as d,b as t,G as p,r as l,h as i,w as c,a as u,k as f,t as m}from"../jse/index-index-C-MnMZEz.js";const h=r({__name:"Card",props:{class:{}},setup(a){const s=a;return(e,_)=>(n(),o("div",{class:d(t(p)("bg-card text-card-foreground border-border rounded-xl border",s.class))},[l(e.$slots,"default")],2))}}),C=r({__name:"CardContent",props:{class:{}},setup(a){const s=a;return(e,_)=>(n(),o("div",{class:d(t(p)("p-6 pt-0",s.class))},[l(e.$slots,"default")],2))}}),$=r({__name:"CardHeader",props:{class:{}},setup(a){const s=a;return(e,_)=>(n(),o("div",{class:d(t(p)("flex flex-col gap-y-1.5 p-5",s.class))},[l(e.$slots,"default")],2))}}),x=r({__name:"CardTitle",props:{class:{}},setup(a){const s=a;return(e,_)=>(n(),o("h3",{class:d(t(p)("font-semibold leading-none tracking-tight",s.class))},[l(e.$slots,"default")],2))}}),g=r({name:"AnalysisChartCard",__name:"analysis-chart-card",props:{title:{}},setup(a){return(s,e)=>(n(),i(t(h),null,{default:c(()=>[u(t($),null,{default:c(()=>[u(t(x),{class:"text-xl"},{default:c(()=>[f(m(s.title),1)]),_:1})]),_:1}),u(t(C),null,{default:c(()=>[l(s.$slots,"default")]),_:3})]),_:3}))}});export{$ as _,x as a,C as b,h as c,g as d};
