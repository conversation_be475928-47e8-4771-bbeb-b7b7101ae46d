var h=Object.defineProperty,R=Object.defineProperties;var S=Object.getOwnPropertyDescriptors;var v=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var z=(o,a,t)=>a in o?h(o,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[a]=t,k=(o,a)=>{for(var t in a||(a={}))j.call(a,t)&&z(o,t,a[t]);if(v)for(var t of v(a))B.call(a,t)&&z(o,t,a[t]);return o},I=(o,a)=>R(o,S(a));var x=(o,a,t)=>new Promise((d,p)=>{var n=l=>{try{u(t.next(l))}catch(_){p(_)}},r=l=>{try{u(t.throw(l))}catch(_){p(_)}},u=l=>l.done?d(l.value):Promise.resolve(l.value).then(n,r);u((t=t.apply(o,a)).next())});import{y as N,ak as $}from"./bootstrap-DCMzVRvD.js";import{R as M,j as V,k as P}from"./index-C0wIoq37.js";import{_ as q}from"./command-chart.vue_vue_type_script_setup_true_lang-uebhY3fq.js";import{_ as D}from"./memory-chart.vue_vue_type_script_setup_true_lang-C4RPMYcx.js";import{_ as F}from"./redis-description.vue_vue_type_script_setup_true_lang-f6M72wVD.js";import{C as y}from"./index-C1KbofmV.js";import{_ as L}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as A,s as E,p as G,v as H,h as c,o as f,w as s,a as m,b as e,f as g,j as i,e as w,g as b}from"../jse/index-index-C-MnMZEz.js";import{R as J,C}from"./index-By0xk_Xq.js";import"./index-D6-099PU.js";import"./use-echarts-CF-NZzbo.js";import"./index-D59rZjD-.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./index-CHpIOV4R.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BLwHKR_M.js";function K(){return N.get("/monitor/cache")}const O={class:"flex items-center justify-start gap-[6px]"},Q={class:"flex items-center gap-[6px]"},T={class:"flex items-center justify-start gap-[6px]"},Ct=A({__name:"index",setup(o){const a={lg:12,md:24,sm:24,xl:12,xs:24},t=E({command:[],memory:"0"}),d=G();H(()=>x(null,null,function*(){yield p()}));function p(){return x(this,null,function*(){try{const n=yield K(),r=(Number.parseInt(n.info.used_memory)/1024/1024).toFixed(2);t.memory=r,t.command=n.commandStats,console.log(t.command),d.value=I(k({},n.info),{dbSize:String(n.dbSize)})}catch(n){console.warn(n)}})}return(n,r)=>(f(),c(e(L),null,{default:s(()=>[m(e(J),{gutter:[15,15]},{default:s(()=>[m(e(C),{span:24},{default:s(()=>[m(e(y),{size:"small"},{title:s(()=>[i("div",O,[m(e(M),{class:"size-[16px]"}),r[0]||(r[0]=i("span",null,"redis信息",-1))])]),extra:s(()=>[m(e($),{size:"small",onClick:p},{default:s(()=>r[1]||(r[1]=[i("div",{class:"flex"},[i("span",{class:"icon-[charm--refresh]"})],-1)])),_:1,__:[1]})]),default:s(()=>[d.value?(f(),c(e(F),{key:0,data:d.value},null,8,["data"])):g("",!0)]),_:1})]),_:1}),m(e(C),w(b(a)),{default:s(()=>[m(e(y),{size:"small"},{title:s(()=>[i("div",Q,[m(e(V),{class:"size-[16px]"}),r[2]||(r[2]=i("span",null,"命令统计",-1))])]),default:s(()=>[t.command.length>0?(f(),c(e(q),{key:0,data:t.command},null,8,["data"])):g("",!0)]),_:1})]),_:1},16),m(e(C),w(b(a)),{default:s(()=>[m(e(y),{size:"small"},{title:s(()=>[i("div",T,[m(e(P),{class:"size-[16px]"}),r[3]||(r[3]=i("span",null,"内存占用",-1))])]),default:s(()=>[t.memory!=="0"?(f(),c(e(D),{key:0,data:t.memory},null,8,["data"])):g("",!0)]),_:1})]),_:1},16)]),_:1})]),_:1}))}});export{Ct as default};
