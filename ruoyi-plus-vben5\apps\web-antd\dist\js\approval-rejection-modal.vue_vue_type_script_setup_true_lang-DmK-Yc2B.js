var f=(u,r,l)=>new Promise((c,a)=>{var d=t=>{try{s(l.next(t))}catch(e){a(e)}},o=t=>{try{s(l.throw(t))}catch(e){a(e)}},s=t=>t.done?c(t.value):Promise.resolve(t.value).then(d,o);s((l=l.apply(u,r)).next())});import{aj as C}from"./bootstrap-DCMzVRvD.js";import{b as w,d as x}from"./index-CZhogUxH.js";import{d as I,P as _,h as k,o as v,b as h,w as N,a as P}from"../jse/index-index-C-MnMZEz.js";import{a as V}from"./get-popup-container-P4S1sr5h.js";import{u as F}from"./use-modal-CeMSCP2m.js";const T=I({__name:"approval-rejection-modal",emits:["complete"],setup(u,{emit:r}){const l=r,[c,a]=C({commonConfig:{formItemClass:"col-span-2",labelWidth:100,componentProps:{class:"w-full"}},schema:[{fieldName:"taskId",component:"Input",label:"任务ID",dependencies:{show:!1,triggerFields:[""]}},{fieldName:"messageType",component:"CheckboxGroup",componentProps:{options:[{label:"站内信",value:"1",disabled:!0},{label:"邮件",value:"2"},{label:"短信",value:"3"}]},label:"通知方式",defaultValue:["1"]},{fieldName:"nodeCode",component:"Select",componentProps:{getPopupContainer:V},label:"驳回节点"},{fieldName:"attachment",component:"FileUpload",componentProps:{maxCount:10,maxSize:20,accept:"png, jpg, jpeg, doc, docx, xlsx, xls, ppt, pdf"},defaultValue:[],label:"附件上传",formItemClass:"items-start"},{fieldName:"message",component:"Textarea",label:"审批意见",formItemClass:"items-start"}],showDefaultActions:!1,wrapperClass:"grid-cols-2"}),[d,o]=F({title:"审批驳回",fullscreenButton:!1,class:"min-h-[365px]",onConfirm:s,onOpenChange(e){return f(this,null,function*(){var g;if(!e)return yield a.resetForm(),null;o.modalLoading(!0);const{taskId:m,definitionId:i,nodeCode:n}=o.getData();yield a.setFieldValue("taskId",m);const p=(yield w(i,n)).map(b=>({label:b.nodeName,value:b.nodeCode}));a.updateSchema([{fieldName:"nodeCode",componentProps:{options:p}}]),p.length>0&&a.setFieldValue("nodeCode",(g=p[0])==null?void 0:g.value),o.modalLoading(!1)})}});function s(){return f(this,null,function*(){var e,m;try{o.modalLoading(!0);const{valid:i}=yield a.validate();if(!i)return;const n=_(yield a.getValues());n.fileId=(m=(e=n.attachment)==null?void 0:e.join)==null?void 0:m.call(e,","),n.attachment=void 0,yield x(n),o.close(),l("complete")}catch(i){console.error(i)}finally{o.modalLoading(!1)}})}return(e,m)=>(v(),k(h(d),null,{default:N(()=>[P(h(c))]),_:1}))}});export{T as _};
