package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysRoleToSysRoleVoMapper__11.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__11 extends BaseMapper<SysRoleVo, SysRole> {
}
