import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-bx@1.2.6/node_modules/@iconify/icons-bx/package.js
var require_package = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-bx@1.2.6/node_modules/@iconify/icons-bx/package.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="currentColor" d="M22 8a.76.76 0 0 0 0-.21v-.08a.77.77 0 0 0-.07-.16a.35.35 0 0 0-.05-.08l-.1-.13l-.08-.06l-.12-.09l-9-5a1 1 0 0 0-1 0l-9 5l-.09.07l-.11.08a.41.41 0 0 0-.07.11a.39.39 0 0 0-.08.1a.59.59 0 0 0-.06.14a.3.3 0 0 0 0 .1A.76.76 0 0 0 2 8v8a1 1 0 0 0 .52.87l9 5a.75.75 0 0 0 .13.06h.1a1.06 1.06 0 0 0 .5 0h.1l.14-.06l9-5A1 1 0 0 0 22 16V8zm-10 3.87L5.06 8l2.76-1.52l6.83 3.9zm0-7.72L18.94 8L16.7 9.25L9.87 5.34zM4 9.7l7 3.92v5.68l-7-3.89zm9 9.6v-5.68l3-1.68V15l2-1v-3.18l2-1.11v5.7z"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_package();
//# sourceMappingURL=@iconify_icons-bx_package.js.map
