import{y as r}from"./bootstrap-DCMzVRvD.js";import{b as u}from"./uuid-B0AYzFfo.js";function n(){return r.get("/system/user/profile")}function o(e){return r.putWithMsg("/system/user/profile",e)}function p(e){return r.putWithMsg("/system/user/profile/updatePwd",e,{encrypt:!0})}function f(e){let{file:t}=e;const{filename:s}=e;return t=s?new File([t],s):new File([t],`${u()}.png`),r.post("/system/user/profile/avatar",{avatarfile:t},{headers:{"Content-Type":"multipart/form-data"}})}export{o as a,n as b,f as c,p as u};
