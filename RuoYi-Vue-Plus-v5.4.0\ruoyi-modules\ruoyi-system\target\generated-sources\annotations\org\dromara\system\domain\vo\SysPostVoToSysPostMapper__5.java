package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysPostToSysPostVoMapper__5.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__5 extends BaseMapper<SysPostVo, SysPost> {
}
