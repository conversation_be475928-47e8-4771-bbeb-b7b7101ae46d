var m=(p,t,s)=>new Promise((n,o)=>{var e=a=>{try{i(s.next(a))}catch(l){o(l)}},r=a=>{try{i(s.throw(a))}catch(l){o(l)}},i=a=>a.done?n(a.value):Promise.resolve(a.value).then(e,r);i((s=s.apply(p,t)).next())});import{g as f}from"./index-DCFckLr6.js";import"./bootstrap-DCMzVRvD.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import{_ as u}from"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{d as _,p as d,h as k,o as w,w as B,a as g,b as c}from"../jse/index-index-C-MnMZEz.js";import{u as h}from"./use-modal-CeMSCP2m.js";const D=_({__name:"flow-info-modal",setup(p){const t=d(),[s,n]=h({title:"流程信息",class:"w-[1000px]",footer:!1,onOpenChange:o=>m(null,null,function*(){if(!o)return null;const{businessId:e}=n.getData(),r=yield f(e);t.value=r})});return(o,e)=>(w(),k(c(s),null,{default:B(()=>[g(c(u),{task:t.value,type:"readonly"},null,8,["task"])]),_:1}))}});export{D as _};
