import{u as f,_ as p}from"./use-echarts-CF-NZzbo.js";import{d,p as l,q as h,v as _,R as w,h as b,o as g,b as y}from"../jse/index-index-C-MnMZEz.js";const k=d({__name:"memory-chart",props:{data:{default:"0"}},setup(n){const a=n,r=l(),{renderEcharts:s,resize:m}=f(r);h(()=>a.data,()=>{r.value&&o(a.data)},{immediate:!0}),_(()=>{o(a.data)}),w(m);function i(t){let e=10;for(;e<=t;)e*=10;return e}function o(t){const e=Math.floor(Number.parseFloat(t)),c=i(e),u={series:[{animation:!0,animationDuration:1e3,data:[{name:"内存消耗",value:Number.parseFloat(t)}],detail:{formatter:`${t}M`,valueAnimation:!0},max:c,min:0,name:"峰值",progress:{show:!0},type:"gauge"}],tooltip:{formatter:`{b} <br/>{a} : ${t}M`}};s(u)}return(t,e)=>(g(),b(y(p),{ref_key:"memoryHtmlRef",ref:r,height:"400px",width:"100%"},null,512))}});export{k as _};
