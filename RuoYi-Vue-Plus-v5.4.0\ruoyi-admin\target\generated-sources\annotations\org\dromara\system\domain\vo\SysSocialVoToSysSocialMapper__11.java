package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysSocialToSysSocialVoMapper__11.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__11 extends BaseMapper<SysSocialVo, SysSocial> {
}
