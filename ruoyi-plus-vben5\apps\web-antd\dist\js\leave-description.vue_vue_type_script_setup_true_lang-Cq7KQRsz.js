import{d as u,B as c,h as D,o as v,b as a,w as t,a as r,k as o,t as s,U as _}from"../jse/index-index-C-MnMZEz.js";import{l as b}from"./data-DSNSln3q.js";import{D as y,a as l}from"./index-D59rZjD-.js";const Y=u({name:"LeaveDescription",inheritAttrs:!1,__name:"leave-description",props:{data:{}},setup(d){const p=d,m=c(()=>{var e,n;return(n=(e=b.find(f=>f.value===p.data.leaveType))==null?void 0:e.label)!=null?n:"未知"});function i(e){return _(e).format("YYYY-MM-DD")}return(e,n)=>(v(),D(a(y),{column:1,size:"middle"},{default:t(()=>[r(a(l),{label:"请假类型"},{default:t(()=>[o(s(m.value),1)]),_:1}),r(a(l),{label:"请假时间"},{default:t(()=>[o(s(i(e.data.startDate))+" - "+s(i(e.data.endDate)),1)]),_:1}),r(a(l),{label:"请假时长"},{default:t(()=>[o(s(e.data.leaveDays)+"天 ",1)]),_:1}),r(a(l),{label:"请假原因"},{default:t(()=>[o(s(e.data.remark||"无"),1)]),_:1})]),_:1}))}});export{Y as _};
