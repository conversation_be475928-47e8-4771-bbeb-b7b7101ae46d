{"doc": " 模板处理工具类\n\n <AUTHOR>\n", "fields": [{"name": "PROJECT_PATH", "doc": " 项目空间路径\n"}, {"name": "MYBATIS_PATH", "doc": " mybatis空间路径\n"}, {"name": "DEFAULT_PARENT_MENU_ID", "doc": " 默认上级菜单，系统工具\n"}], "enumConstants": [], "methods": [{"name": "prepareContext", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 设置模板变量信息\n\n @return 模板列表\n"}, {"name": "getTemplateList", "paramTypes": ["java.lang.String"], "doc": " 获取模板信息\n\n @return 模板列表\n"}, {"name": "getFileName", "paramTypes": ["java.lang.String", "org.dromara.gen.domain.GenTable"], "doc": " 获取文件名\n"}, {"name": "getPackagePrefix", "paramTypes": ["java.lang.String"], "doc": " 获取包前缀\n\n @param packageName 包名称\n @return 包前缀名称\n"}, {"name": "getImportList", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 根据列类型获取导入包\n\n @param genTable 业务表对象\n @return 返回需要导入的包列表\n"}, {"name": "getDicts", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 根据列类型获取字典组\n\n @param genTable 业务表对象\n @return 返回字典组\n"}, {"name": "addDicts", "paramTypes": ["java.util.Set", "java.util.List"], "doc": " 添加字典列表\n\n @param dicts 字典列表\n @param columns 列集合\n"}, {"name": "getPermissionPrefix", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取权限前缀\n\n @param moduleName   模块名称\n @param businessName 业务名称\n @return 返回权限前缀\n"}, {"name": "getParentMenuId", "paramTypes": ["cn.hutool.core.lang.Dict"], "doc": " 获取上级菜单ID字段\n\n @param paramsObj 生成其他选项\n @return 上级菜单ID字段\n"}, {"name": "getTreecode", "paramTypes": ["java.util.Map"], "doc": " 获取树编码\n\n @param paramsObj 生成其他选项\n @return 树编码\n"}, {"name": "getTreeParentCode", "paramTypes": ["cn.hutool.core.lang.Dict"], "doc": " 获取树父编码\n\n @param paramsObj 生成其他选项\n @return 树父编码\n"}, {"name": "getTreeName", "paramTypes": ["cn.hutool.core.lang.Dict"], "doc": " 获取树名称\n\n @param paramsObj 生成其他选项\n @return 树名称\n"}, {"name": "getExpandColumn", "paramTypes": ["org.dromara.gen.domain.GenTable"], "doc": " 获取需要在哪一列上面显示展开按钮\n\n @param genTable 业务表对象\n @return 展开按钮列序号\n"}], "constructors": []}