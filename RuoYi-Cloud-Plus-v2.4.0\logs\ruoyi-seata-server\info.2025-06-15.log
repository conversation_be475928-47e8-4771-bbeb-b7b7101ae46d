2025-06-15 21:07:21 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-15 21:07:21 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-15 21:07:21 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-15 21:07:21 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-15 21:07:21 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 49592 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:07:21 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-15 21:07:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-15 21:07:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-15 21:07:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 21:07:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-15 21:07:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 21:07:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 924 ms
2025-06-15 21:07:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 21:07:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 21:07:23 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-15 21:07:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@411933, org.springframework.security.web.context.SecurityContextPersistenceFilter@25d23478, org.springframework.security.web.header.HeaderWriterFilter@51e1e058, org.springframework.security.web.csrf.CsrfFilter@18d1d137, org.springframework.security.web.authentication.logout.LogoutFilter@7fdff56b, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@393e7546, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@39c1e7b7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@221cdd87, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6b43b101, org.springframework.security.web.access.ExceptionTranslationFilter@34065642, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@acb1c9c]
2025-06-15 21:07:23 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-15 21:07:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-15 21:07:23 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 2.326 seconds (JVM running for 2.982)
2025-06-15 21:07:23 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-15 21:07:23 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-15 21:07:24 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-15 21:07:24 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-15 21:07:33 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-15 21:07:33 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-15 21:07:33 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 9659 millSeconds
2025-06-15 21:40:23 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-15 21:44:25 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-15 21:44:25 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-15 21:44:25 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-15 21:44:25 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-15 21:44:25 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 34412 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:44:25 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-15 21:44:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-15 21:44:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-15 21:44:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 21:44:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-15 21:44:26 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 21:44:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 894 ms
2025-06-15 21:44:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 21:44:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 21:44:27 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-15 21:44:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@411933, org.springframework.security.web.context.SecurityContextPersistenceFilter@25d23478, org.springframework.security.web.header.HeaderWriterFilter@51e1e058, org.springframework.security.web.csrf.CsrfFilter@18d1d137, org.springframework.security.web.authentication.logout.LogoutFilter@7fdff56b, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@393e7546, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@39c1e7b7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@221cdd87, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6b43b101, org.springframework.security.web.access.ExceptionTranslationFilter@34065642, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@acb1c9c]
2025-06-15 21:44:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-15 21:44:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-15 21:44:27 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 2.259 seconds (JVM running for 2.927)
2025-06-15 21:44:27 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-15 21:44:27 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-15 21:44:27 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-15 21:44:27 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-15 21:44:37 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-15 21:44:37 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-15 21:44:37 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10026 millSeconds
2025-06-15 22:33:48 [SpringApplicationShutdownHook] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-06-15 22:42:33 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Ignore load compatible class io.seata.config.ExtConfigurationProvider, because is not assignable from origin type org.apache.seata.config.ExtConfigurationProvider
2025-06-15 22:42:33 [main] INFO  o.a.s.config.ConfigurationFactory - load Configuration from :FileConfiguration$$EnhancerByCGLIB$$6e15d955
2025-06-15 22:42:33 [main] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.config.ConfigurationProvider
2025-06-15 22:42:33 [main] INFO  o.a.s.c.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-06-15 22:42:34 [main] INFO  o.a.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.14 on 奚翔 with PID 40012 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-visual\ruoyi-seata-server\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:42:34 [main] INFO  o.a.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-06-15 22:42:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-06-15 22:42:35 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-06-15 22:42:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 22:42:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
2025-06-15 22:42:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 22:42:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1148 ms
2025-06-15 22:42:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 22:42:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 22:42:35 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/version.json']
2025-06-15 22:42:35 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/health']
2025-06-15 22:42:36 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/error']
2025-06-15 22:42:36 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/vgroup/v1/**']
2025-06-15 22:42:36 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fe7fa16, org.springframework.security.web.context.SecurityContextPersistenceFilter@4cf15477, org.springframework.security.web.header.HeaderWriterFilter@63551c66, org.springframework.security.web.csrf.CsrfFilter@5008559a, org.springframework.security.web.authentication.logout.LogoutFilter@33a7331, org.apache.seata.console.filter.JwtAuthenticationTokenFilter@61503d00, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3450bd13, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49f41c2e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1fcfcf37, org.springframework.security.web.access.ExceptionTranslationFilter@4d66cb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@169d1f92]
2025-06-15 22:42:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-06-15 22:42:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-06-15 22:42:36 [main] INFO  o.a.s.server.SeataServerApplication - Started SeataServerApplication in 2.82 seconds (JVM running for 3.553)
2025-06-15 22:42:36 [main] INFO  o.a.s.s.lock.LockerManagerFactory - use lock store mode: db
2025-06-15 22:42:36 [main] INFO  o.a.s.server.session.SessionHolder - use session store mode: db
2025-06-15 22:42:36 [main] INFO  o.a.s.c.r.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-06-15 22:42:36 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-15 22:42:46 [main] INFO  o.a.s.d.r.n.NacosRegistryServiceImpl - Nacos check auth with userName/password.
2025-06-15 22:42:46 [main] INFO  org.apache.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-06-15 22:42:46 [main] INFO  org.apache.seata.server.ServerRunner - seata server started in 10130 millSeconds
