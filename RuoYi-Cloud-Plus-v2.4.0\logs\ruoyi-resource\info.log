2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 00:14:17 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 08:26:16 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:26:16 [main] INFO  o.d.r.RuoYiResourceApplication - Starting RuoYiResourceApplication using Java 17.0.14 with PID 36096 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-resource\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:26:16 [main] INFO  o.d.r.RuoYiResourceApplication - The following 1 profile is active: "dev"
2025-06-16 08:26:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-resource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:26:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 08:26:17 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:26:22 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 08:26:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 08:26:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 08:26:23 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b760eb7
2025-06-16 08:26:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 08:26:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 08:26:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 08:26:23 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:26:27 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:26:27 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:26:27 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:26:27 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:26:27 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d74c81b
2025-06-16 08:26:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 08:26:29 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 08:26:29 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 08:26:29 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 08:26:29 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-resource ************:9204 register finished
2025-06-16 08:26:33 [main] INFO  o.d.r.RuoYiResourceApplication - Started RuoYiResourceApplication in 19.884 seconds (process running for 20.568)
2025-06-16 08:26:33 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-06-16 08:26:33 [main] INFO  o.d.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-06-16 08:26:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 08:26:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 08:26:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-resource.yml, group=DEFAULT_GROUP
2025-06-16 08:26:34 [RMI TCP Connection(5)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 09:00:56 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 09:12:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:12:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:48 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:08 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:28 [XNIO-1 task-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-resource) has completed., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:16:40 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](ruoyi-resource), dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xb5b0c2e3, L:/************:58638 - R:/************:20881]], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:40 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xc3b118c2, L:/************:58629 - R:/************:20880]], dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:41 [Dubbo-framework-shared-scheduler-thread-8] INFO  o.a.d.r.c.AbstractServiceDiscovery -  [DUBBO] Metadata of instance changed, updating instance with revision 0., dubbo version: 3.3.4, current host: ************
2025-06-16 09:16:54 [NettyServerWorker-6-1] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xc5e2e2c9, L:/************:20881 ! R:/************:58637] of ************:58637 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:39 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xb5b0c2e3, L:/************:58638 - R:/************:20881], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:39 [NettyClientWorker-7-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xb5b0c2e3, L:/************:58638 ! R:/************:20881] of ************:58638 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:39 [NettyServerWorker-6-2] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x18315aee, L:/************:20881 ! R:/************:58638] of ************:58638 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:39 [NettyServerWorker-6-5] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x225fd70a, L:/************:20881 ! R:/************:59070] of ************:59070 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:49 [NettyServerWorker-6-3] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x61e3c2ec, L:/************:20881 ! R:/************:58639] of ************:58639 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [NettyServerWorker-6-4] INFO  o.a.d.r.transport.netty4.NettyServer -  [DUBBO] All clients has disconnected from /************:20881. You can graceful shutdown now., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [NettyServerWorker-6-4] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x9957016a, L:/************:20881 ! R:/************:58967] of ************:58967 -> ************:20881 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xc3b118c2, L:/************:58629 - R:/************:20880], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [NettyClientWorker-7-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection [id: 0xc3b118c2, L:/************:58629 ! R:/************:20880] of ************:58629 -> ************:20880 is disconnected., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-resource) has completed., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](ruoyi-resource) to null, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-resource) is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Closing dubbo server: /************:20881, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.transport.netty4.NettyServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /************:20881, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-resource&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=36096&qos.enable=false&register=false&register-mode=instance&release=3.3.4, nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-resource&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=36096&qos.enable=false&register-mode=instance&release=3.3.4, nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-resource&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=36096&qos.enable=false&register-mode=instance&release=3.3.4], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.registry.nacos.NacosRegistry -  [DUBBO] Destroy registry:nacos://127.0.0.1:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=ruoyi-resource&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=36096&qos.enable=false&register-mode=instance&release=3.3.4, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-system], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [ruoyi-resource], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](ruoyi-resource) has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.t.m.FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.a.d.c.s.c.DubboSpringInitializer -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@718fd7c1, dubbo version: 3.3.4, current host: ************
2025-06-16 09:17:59 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  o.s.c.s.DefaultLifecycleProcessor - Shutdown phase 2147482623 ends with 1 bean still running after timeout of 30000ms: [webServerGracefulShutdown]
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown aborted with one or more requests still active
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-16 09:18:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-16 10:55:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:55:12 [main] INFO  o.d.r.RuoYiResourceApplication - Starting RuoYiResourceApplication using Java 17.0.14 with PID 41004 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-resource\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:55:12 [main] INFO  o.d.r.RuoYiResourceApplication - The following 1 profile is active: "dev"
2025-06-16 10:55:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-resource.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:55:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 10:55:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 10:55:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 10:55:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@51a6af38
2025-06-16 10:55:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 10:55:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 10:55:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 10:55:21 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:55:26 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:55:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:55:27 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:55:27 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:55:27 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d74c81b
2025-06-16 10:55:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 10:55:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 10:55:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 10:55:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 10:55:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-resource ************:9204 register finished
2025-06-16 10:55:34 [main] INFO  o.d.r.RuoYiResourceApplication - Started RuoYiResourceApplication in 25.538 seconds (process running for 26.417)
2025-06-16 10:55:34 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-06-16 10:55:34 [main] INFO  o.d.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-06-16 10:55:34 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 10:55:34 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:55:34 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-resource.yml, group=DEFAULT_GROUP
2025-06-16 10:55:34 [RMI TCP Connection(10)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 10:56:24 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-16 17:58:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 17:58:04 [main] INFO  o.d.r.RuoYiResourceApplication - Starting RuoYiResourceApplication using Java 17.0.14 with PID 29584 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-resource\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 17:58:04 [main] INFO  o.d.r.RuoYiResourceApplication - The following 1 profile is active: "dev"
2025-06-16 17:58:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-resource.yml, group=DEFAULT_GROUP] success
2025-06-16 17:58:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-16 17:58:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 17:58:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-16 17:58:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-16 17:58:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-16 17:58:10 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@60f6c148
2025-06-16 17:58:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-16 17:58:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-16 17:58:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-16 17:58:11 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 17:58:12 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 17:58:12 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 17:58:12 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:58:12 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:58:12 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3596b249
2025-06-16 17:58:14 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-16 17:58:14 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-16 17:58:14 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-16 17:58:14 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-16 17:58:14 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-resource *************:9204 register finished
2025-06-16 17:58:19 [main] INFO  o.d.r.RuoYiResourceApplication - Started RuoYiResourceApplication in 17.45 seconds (process running for 18.661)
2025-06-16 17:58:19 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-06-16 17:58:19 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 17:58:19 [main] INFO  o.d.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-06-16 17:58:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-16 17:58:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 17:58:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-resource.yml, group=DEFAULT_GROUP
2025-06-16 18:00:41 [XNIO-1 task-3] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
