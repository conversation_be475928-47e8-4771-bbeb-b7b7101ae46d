var Di=Object.defineProperty;var In=Object.getOwnPropertySymbols;var Oi=Object.prototype.hasOwnProperty,Hi=Object.prototype.propertyIsEnumerable;var jn=(<PERSON><PERSON>,<PERSON>,we)=>Me in Oe?Di(<PERSON><PERSON>,<PERSON>,{enumerable:!0,configurable:!0,writable:!0,value:we}):Oe[Me]=we,Pn=(<PERSON>e,Me)=>{for(var we in Me||(Me={}))Oi.call(Me,we)&&jn(Oe,we,Me[we]);if(In)for(var we of In(Me))Hi.call(Me,we)&&jn(Oe,we,Me[we]);return Oe};import{S as Ri}from"./index-Ollxi7Rl.js";import{a9 as Ni,d as Wn,m as Bn,E as Ii,C as ji,q as Un,u as Pi,v as Bi,ac as Ui,c as Vi,o as qn,p as Vn,h as Ki,w as Kn,a as Yn,b as zt}from"../jse/index-index-C-MnMZEz.js";import{cp as Yi}from"./bootstrap-DCMzVRvD.js";import{_ as Fi}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";var Mt={exports:{}};var Wi=Mt.exports,Fn;function qi(){return Fn||(Fn=1,function(Oe,Me){(function(Je,X){Oe.exports=X()})(Wi,()=>(()=>{var we={192:G=>{var E=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32},_=-1,x=1,L=0;E.Diff=function(c,l){return[c,l]},E.prototype.diff_main=function(c,l,g,p){typeof p=="undefined"&&(this.Diff_Timeout<=0?p=Number.MAX_VALUE:p=new Date().getTime()+this.Diff_Timeout*1e3);var m=p;if(c==null||l==null)throw new Error("Null input. (diff_main)");if(c==l)return c?[new E.Diff(L,c)]:[];typeof g=="undefined"&&(g=!0);var d=g,v=this.diff_commonPrefix(c,l),w=c.substring(0,v);c=c.substring(v),l=l.substring(v),v=this.diff_commonSuffix(c,l);var b=c.substring(c.length-v);c=c.substring(0,c.length-v),l=l.substring(0,l.length-v);var y=this.diff_compute_(c,l,d,m);return w&&y.unshift(new E.Diff(L,w)),b&&y.push(new E.Diff(L,b)),this.diff_cleanupMerge(y),y},E.prototype.diff_compute_=function(c,l,g,p){var m;if(!c)return[new E.Diff(x,l)];if(!l)return[new E.Diff(_,c)];var d=c.length>l.length?c:l,v=c.length>l.length?l:c,w=d.indexOf(v);if(w!=-1)return m=[new E.Diff(x,d.substring(0,w)),new E.Diff(L,v),new E.Diff(x,d.substring(w+v.length))],c.length>l.length&&(m[0][0]=m[2][0]=_),m;if(v.length==1)return[new E.Diff(_,c),new E.Diff(x,l)];var b=this.diff_halfMatch_(c,l);if(b){var y=b[0],s=b[1],T=b[2],A=b[3],D=b[4],C=this.diff_main(y,T,g,p),j=this.diff_main(s,A,g,p);return C.concat([new E.Diff(L,D)],j)}return g&&c.length>100&&l.length>100?this.diff_lineMode_(c,l,p):this.diff_bisect_(c,l,p)},E.prototype.diff_lineMode_=function(c,l,g){var p=this.diff_linesToChars_(c,l);c=p.chars1,l=p.chars2;var m=p.lineArray,d=this.diff_main(c,l,!1,g);this.diff_charsToLines_(d,m),this.diff_cleanupSemantic(d),d.push(new E.Diff(L,""));for(var v=0,w=0,b=0,y="",s="";v<d.length;){switch(d[v][0]){case x:b++,s+=d[v][1];break;case _:w++,y+=d[v][1];break;case L:if(w>=1&&b>=1){d.splice(v-w-b,w+b),v=v-w-b;for(var T=this.diff_main(y,s,!1,g),A=T.length-1;A>=0;A--)d.splice(v,0,T[A]);v=v+T.length}b=0,w=0,y="",s="";break}v++}return d.pop(),d},E.prototype.diff_bisect_=function(c,l,g){for(var p=c.length,m=l.length,d=Math.ceil((p+m)/2),v=d,w=2*d,b=new Array(w),y=new Array(w),s=0;s<w;s++)b[s]=-1,y[s]=-1;b[v+1]=0,y[v+1]=0;for(var T=p-m,A=T%2!=0,D=0,C=0,j=0,V=0,q=0;q<d&&!(new Date().getTime()>g);q++){for(var Q=-q+D;Q<=q-C;Q+=2){var ne=v+Q,oe;Q==-q||Q!=q&&b[ne-1]<b[ne+1]?oe=b[ne+1]:oe=b[ne-1]+1;for(var me=oe-Q;oe<p&&me<m&&c.charAt(oe)==l.charAt(me);)oe++,me++;if(b[ne]=oe,oe>p)C+=2;else if(me>m)D+=2;else if(A){var fe=v+T-Q;if(fe>=0&&fe<w&&y[fe]!=-1){var le=p-y[fe];if(oe>=le)return this.diff_bisectSplit_(c,l,oe,me,g)}}}for(var ve=-q+j;ve<=q-V;ve+=2){var fe=v+ve,le;ve==-q||ve!=q&&y[fe-1]<y[fe+1]?le=y[fe+1]:le=y[fe-1]+1;for(var Le=le-ve;le<p&&Le<m&&c.charAt(p-le-1)==l.charAt(m-Le-1);)le++,Le++;if(y[fe]=le,le>p)V+=2;else if(Le>m)j+=2;else if(!A){var ne=v+T-ve;if(ne>=0&&ne<w&&b[ne]!=-1){var oe=b[ne],me=v+oe-ne;if(le=p-le,oe>=le)return this.diff_bisectSplit_(c,l,oe,me,g)}}}}return[new E.Diff(_,c),new E.Diff(x,l)]},E.prototype.diff_bisectSplit_=function(c,l,g,p,m){var d=c.substring(0,g),v=l.substring(0,p),w=c.substring(g),b=l.substring(p),y=this.diff_main(d,v,!1,m),s=this.diff_main(w,b,!1,m);return y.concat(s)},E.prototype.diff_linesToChars_=function(c,l){var g=[],p={};g[0]="";function m(b){for(var y="",s=0,T=-1,A=g.length;T<b.length-1;){T=b.indexOf(`
`,s),T==-1&&(T=b.length-1);var D=b.substring(s,T+1);(p.hasOwnProperty?p.hasOwnProperty(D):p[D]!==void 0)?y+=String.fromCharCode(p[D]):(A==d&&(D=b.substring(s),T=b.length),y+=String.fromCharCode(A),p[D]=A,g[A++]=D),s=T+1}return y}var d=4e4,v=m(c);d=65535;var w=m(l);return{chars1:v,chars2:w,lineArray:g}},E.prototype.diff_charsToLines_=function(c,l){for(var g=0;g<c.length;g++){for(var p=c[g][1],m=[],d=0;d<p.length;d++)m[d]=l[p.charCodeAt(d)];c[g][1]=m.join("")}},E.prototype.diff_commonPrefix=function(c,l){if(!c||!l||c.charAt(0)!=l.charAt(0))return 0;for(var g=0,p=Math.min(c.length,l.length),m=p,d=0;g<m;)c.substring(d,m)==l.substring(d,m)?(g=m,d=g):p=m,m=Math.floor((p-g)/2+g);return m},E.prototype.diff_commonSuffix=function(c,l){if(!c||!l||c.charAt(c.length-1)!=l.charAt(l.length-1))return 0;for(var g=0,p=Math.min(c.length,l.length),m=p,d=0;g<m;)c.substring(c.length-m,c.length-d)==l.substring(l.length-m,l.length-d)?(g=m,d=g):p=m,m=Math.floor((p-g)/2+g);return m},E.prototype.diff_commonOverlap_=function(c,l){var g=c.length,p=l.length;if(g==0||p==0)return 0;g>p?c=c.substring(g-p):g<p&&(l=l.substring(0,g));var m=Math.min(g,p);if(c==l)return m;for(var d=0,v=1;;){var w=c.substring(m-v),b=l.indexOf(w);if(b==-1)return d;v+=b,(b==0||c.substring(m-v)==l.substring(0,v))&&(d=v,v++)}},E.prototype.diff_halfMatch_=function(c,l){if(this.Diff_Timeout<=0)return null;var g=c.length>l.length?c:l,p=c.length>l.length?l:c;if(g.length<4||p.length*2<g.length)return null;var m=this;function d(C,j,V){for(var q=C.substring(V,V+Math.floor(C.length/4)),Q=-1,ne="",oe,me,fe,le;(Q=j.indexOf(q,Q+1))!=-1;){var ve=m.diff_commonPrefix(C.substring(V),j.substring(Q)),Le=m.diff_commonSuffix(C.substring(0,V),j.substring(0,Q));ne.length<Le+ve&&(ne=j.substring(Q-Le,Q)+j.substring(Q,Q+ve),oe=C.substring(0,V-Le),me=C.substring(V+ve),fe=j.substring(0,Q-Le),le=j.substring(Q+ve))}return ne.length*2>=C.length?[oe,me,fe,le,ne]:null}var v=d(g,p,Math.ceil(g.length/4)),w=d(g,p,Math.ceil(g.length/2)),b;if(!v&&!w)return null;w?v?b=v[4].length>w[4].length?v:w:b=w:b=v;var y,s,T,A;c.length>l.length?(y=b[0],s=b[1],T=b[2],A=b[3]):(T=b[0],A=b[1],y=b[2],s=b[3]);var D=b[4];return[y,s,T,A,D]},E.prototype.diff_cleanupSemantic=function(c){for(var l=!1,g=[],p=0,m=null,d=0,v=0,w=0,b=0,y=0;d<c.length;)c[d][0]==L?(g[p++]=d,v=b,w=y,b=0,y=0,m=c[d][1]):(c[d][0]==x?b+=c[d][1].length:y+=c[d][1].length,m&&m.length<=Math.max(v,w)&&m.length<=Math.max(b,y)&&(c.splice(g[p-1],0,new E.Diff(_,m)),c[g[p-1]+1][0]=x,p--,p--,d=p>0?g[p-1]:-1,v=0,w=0,b=0,y=0,m=null,l=!0)),d++;for(l&&this.diff_cleanupMerge(c),this.diff_cleanupSemanticLossless(c),d=1;d<c.length;){if(c[d-1][0]==_&&c[d][0]==x){var s=c[d-1][1],T=c[d][1],A=this.diff_commonOverlap_(s,T),D=this.diff_commonOverlap_(T,s);A>=D?(A>=s.length/2||A>=T.length/2)&&(c.splice(d,0,new E.Diff(L,T.substring(0,A))),c[d-1][1]=s.substring(0,s.length-A),c[d+1][1]=T.substring(A),d++):(D>=s.length/2||D>=T.length/2)&&(c.splice(d,0,new E.Diff(L,s.substring(0,D))),c[d-1][0]=x,c[d-1][1]=T.substring(0,T.length-D),c[d+1][0]=_,c[d+1][1]=s.substring(D),d++),d++}d++}},E.prototype.diff_cleanupSemanticLossless=function(c){function l(D,C){if(!D||!C)return 6;var j=D.charAt(D.length-1),V=C.charAt(0),q=j.match(E.nonAlphaNumericRegex_),Q=V.match(E.nonAlphaNumericRegex_),ne=q&&j.match(E.whitespaceRegex_),oe=Q&&V.match(E.whitespaceRegex_),me=ne&&j.match(E.linebreakRegex_),fe=oe&&V.match(E.linebreakRegex_),le=me&&D.match(E.blanklineEndRegex_),ve=fe&&C.match(E.blanklineStartRegex_);return le||ve?5:me||fe?4:q&&!ne&&oe?3:ne||oe?2:q||Q?1:0}for(var g=1;g<c.length-1;){if(c[g-1][0]==L&&c[g+1][0]==L){var p=c[g-1][1],m=c[g][1],d=c[g+1][1],v=this.diff_commonSuffix(p,m);if(v){var w=m.substring(m.length-v);p=p.substring(0,p.length-v),m=w+m.substring(0,m.length-v),d=w+d}for(var b=p,y=m,s=d,T=l(p,m)+l(m,d);m.charAt(0)===d.charAt(0);){p+=m.charAt(0),m=m.substring(1)+d.charAt(0),d=d.substring(1);var A=l(p,m)+l(m,d);A>=T&&(T=A,b=p,y=m,s=d)}c[g-1][1]!=b&&(b?c[g-1][1]=b:(c.splice(g-1,1),g--),c[g][1]=y,s?c[g+1][1]=s:(c.splice(g+1,1),g--))}g++}},E.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,E.whitespaceRegex_=/\s/,E.linebreakRegex_=/[\r\n]/,E.blanklineEndRegex_=/\n\r?\n$/,E.blanklineStartRegex_=/^\r?\n\r?\n/,E.prototype.diff_cleanupEfficiency=function(c){for(var l=!1,g=[],p=0,m=null,d=0,v=!1,w=!1,b=!1,y=!1;d<c.length;)c[d][0]==L?(c[d][1].length<this.Diff_EditCost&&(b||y)?(g[p++]=d,v=b,w=y,m=c[d][1]):(p=0,m=null),b=y=!1):(c[d][0]==_?y=!0:b=!0,m&&(v&&w&&b&&y||m.length<this.Diff_EditCost/2&&v+w+b+y==3)&&(c.splice(g[p-1],0,new E.Diff(_,m)),c[g[p-1]+1][0]=x,p--,m=null,v&&w?(b=y=!0,p=0):(p--,d=p>0?g[p-1]:-1,b=y=!1),l=!0)),d++;l&&this.diff_cleanupMerge(c)},E.prototype.diff_cleanupMerge=function(c){c.push(new E.Diff(L,""));for(var l=0,g=0,p=0,m="",d="",v;l<c.length;)switch(c[l][0]){case x:p++,d+=c[l][1],l++;break;case _:g++,m+=c[l][1],l++;break;case L:g+p>1?(g!==0&&p!==0&&(v=this.diff_commonPrefix(d,m),v!==0&&(l-g-p>0&&c[l-g-p-1][0]==L?c[l-g-p-1][1]+=d.substring(0,v):(c.splice(0,0,new E.Diff(L,d.substring(0,v))),l++),d=d.substring(v),m=m.substring(v)),v=this.diff_commonSuffix(d,m),v!==0&&(c[l][1]=d.substring(d.length-v)+c[l][1],d=d.substring(0,d.length-v),m=m.substring(0,m.length-v))),l-=g+p,c.splice(l,g+p),m.length&&(c.splice(l,0,new E.Diff(_,m)),l++),d.length&&(c.splice(l,0,new E.Diff(x,d)),l++),l++):l!==0&&c[l-1][0]==L?(c[l-1][1]+=c[l][1],c.splice(l,1)):l++,p=0,g=0,m="",d="";break}c[c.length-1][1]===""&&c.pop();var w=!1;for(l=1;l<c.length-1;)c[l-1][0]==L&&c[l+1][0]==L&&(c[l][1].substring(c[l][1].length-c[l-1][1].length)==c[l-1][1]?(c[l][1]=c[l-1][1]+c[l][1].substring(0,c[l][1].length-c[l-1][1].length),c[l+1][1]=c[l-1][1]+c[l+1][1],c.splice(l-1,1),w=!0):c[l][1].substring(0,c[l+1][1].length)==c[l+1][1]&&(c[l-1][1]+=c[l+1][1],c[l][1]=c[l][1].substring(c[l+1][1].length)+c[l+1][1],c.splice(l+1,1),w=!0)),l++;w&&this.diff_cleanupMerge(c)},E.prototype.diff_xIndex=function(c,l){var g=0,p=0,m=0,d=0,v;for(v=0;v<c.length&&(c[v][0]!==x&&(g+=c[v][1].length),c[v][0]!==_&&(p+=c[v][1].length),!(g>l));v++)m=g,d=p;return c.length!=v&&c[v][0]===_?d:d+(l-m)},E.prototype.diff_prettyHtml=function(c){for(var l=[],g=/&/g,p=/</g,m=/>/g,d=/\n/g,v=0;v<c.length;v++){var w=c[v][0],b=c[v][1],y=b.replace(g,"&amp;").replace(p,"&lt;").replace(m,"&gt;").replace(d,"&para;<br>");switch(w){case x:l[v]='<ins style="background:#e6ffe6;">'+y+"</ins>";break;case _:l[v]='<del style="background:#ffe6e6;">'+y+"</del>";break;case L:l[v]="<span>"+y+"</span>";break}}return l.join("")},E.prototype.diff_text1=function(c){for(var l=[],g=0;g<c.length;g++)c[g][0]!==x&&(l[g]=c[g][1]);return l.join("")},E.prototype.diff_text2=function(c){for(var l=[],g=0;g<c.length;g++)c[g][0]!==_&&(l[g]=c[g][1]);return l.join("")},E.prototype.diff_levenshtein=function(c){for(var l=0,g=0,p=0,m=0;m<c.length;m++){var d=c[m][0],v=c[m][1];switch(d){case x:g+=v.length;break;case _:p+=v.length;break;case L:l+=Math.max(g,p),g=0,p=0;break}}return l+=Math.max(g,p),l},E.prototype.diff_toDelta=function(c){for(var l=[],g=0;g<c.length;g++)switch(c[g][0]){case x:l[g]="+"+encodeURI(c[g][1]);break;case _:l[g]="-"+c[g][1].length;break;case L:l[g]="="+c[g][1].length;break}return l.join("	").replace(/%20/g," ")},E.prototype.diff_fromDelta=function(c,l){for(var g=[],p=0,m=0,d=l.split(/\t/g),v=0;v<d.length;v++){var w=d[v].substring(1);switch(d[v].charAt(0)){case"+":try{g[p++]=new E.Diff(x,decodeURI(w))}catch(s){throw new Error("Illegal escape in diff_fromDelta: "+w)}break;case"-":case"=":var b=parseInt(w,10);if(isNaN(b)||b<0)throw new Error("Invalid number in diff_fromDelta: "+w);var y=c.substring(m,m+=b);d[v].charAt(0)=="="?g[p++]=new E.Diff(L,y):g[p++]=new E.Diff(_,y);break;default:if(d[v])throw new Error("Invalid diff operation in diff_fromDelta: "+d[v])}}if(m!=c.length)throw new Error("Delta length ("+m+") does not equal source text length ("+c.length+").");return g},E.prototype.match_main=function(c,l,g){if(c==null||l==null||g==null)throw new Error("Null input. (match_main)");return g=Math.max(0,Math.min(g,c.length)),c==l?0:c.length?c.substring(g,g+l.length)==l?g:this.match_bitap_(c,l,g):-1},E.prototype.match_bitap_=function(c,l,g){if(l.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var p=this.match_alphabet_(l),m=this;function d(oe,me){var fe=oe/l.length,le=Math.abs(g-me);return m.Match_Distance?fe+le/m.Match_Distance:le?1:fe}var v=this.Match_Threshold,w=c.indexOf(l,g);w!=-1&&(v=Math.min(d(0,w),v),w=c.lastIndexOf(l,g+l.length),w!=-1&&(v=Math.min(d(0,w),v)));var b=1<<l.length-1;w=-1;for(var y,s,T=l.length+c.length,A,D=0;D<l.length;D++){for(y=0,s=T;y<s;)d(D,g+s)<=v?y=s:T=s,s=Math.floor((T-y)/2+y);T=s;var C=Math.max(1,g-s+1),j=Math.min(g+s,c.length)+l.length,V=Array(j+2);V[j+1]=(1<<D)-1;for(var q=j;q>=C;q--){var Q=p[c.charAt(q-1)];if(D===0?V[q]=(V[q+1]<<1|1)&Q:V[q]=(V[q+1]<<1|1)&Q|((A[q+1]|A[q])<<1|1)|A[q+1],V[q]&b){var ne=d(D,q-1);if(ne<=v)if(v=ne,w=q-1,w>g)C=Math.max(1,2*g-w);else break}}if(d(D+1,g)>v)break;A=V}return w},E.prototype.match_alphabet_=function(c){for(var l={},g=0;g<c.length;g++)l[c.charAt(g)]=0;for(var g=0;g<c.length;g++)l[c.charAt(g)]|=1<<c.length-g-1;return l},E.prototype.patch_addContext_=function(c,l){if(l.length!=0){if(c.start2===null)throw Error("patch not initialized");for(var g=l.substring(c.start2,c.start2+c.length1),p=0;l.indexOf(g)!=l.lastIndexOf(g)&&g.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)p+=this.Patch_Margin,g=l.substring(c.start2-p,c.start2+c.length1+p);p+=this.Patch_Margin;var m=l.substring(c.start2-p,c.start2);m&&c.diffs.unshift(new E.Diff(L,m));var d=l.substring(c.start2+c.length1,c.start2+c.length1+p);d&&c.diffs.push(new E.Diff(L,d)),c.start1-=m.length,c.start2-=m.length,c.length1+=m.length+d.length,c.length2+=m.length+d.length}},E.prototype.patch_make=function(c,l,g){var p,m;if(typeof c=="string"&&typeof l=="string"&&typeof g=="undefined")p=c,m=this.diff_main(p,l,!0),m.length>2&&(this.diff_cleanupSemantic(m),this.diff_cleanupEfficiency(m));else if(c&&typeof c=="object"&&typeof l=="undefined"&&typeof g=="undefined")m=c,p=this.diff_text1(m);else if(typeof c=="string"&&l&&typeof l=="object"&&typeof g=="undefined")p=c,m=l;else if(typeof c=="string"&&typeof l=="string"&&g&&typeof g=="object")p=c,m=g;else throw new Error("Unknown call format to patch_make.");if(m.length===0)return[];for(var d=[],v=new E.patch_obj,w=0,b=0,y=0,s=p,T=p,A=0;A<m.length;A++){var D=m[A][0],C=m[A][1];switch(!w&&D!==L&&(v.start1=b,v.start2=y),D){case x:v.diffs[w++]=m[A],v.length2+=C.length,T=T.substring(0,y)+C+T.substring(y);break;case _:v.length1+=C.length,v.diffs[w++]=m[A],T=T.substring(0,y)+T.substring(y+C.length);break;case L:C.length<=2*this.Patch_Margin&&w&&m.length!=A+1?(v.diffs[w++]=m[A],v.length1+=C.length,v.length2+=C.length):C.length>=2*this.Patch_Margin&&w&&(this.patch_addContext_(v,s),d.push(v),v=new E.patch_obj,w=0,s=T,b=y);break}D!==x&&(b+=C.length),D!==_&&(y+=C.length)}return w&&(this.patch_addContext_(v,s),d.push(v)),d},E.prototype.patch_deepCopy=function(c){for(var l=[],g=0;g<c.length;g++){var p=c[g],m=new E.patch_obj;m.diffs=[];for(var d=0;d<p.diffs.length;d++)m.diffs[d]=new E.Diff(p.diffs[d][0],p.diffs[d][1]);m.start1=p.start1,m.start2=p.start2,m.length1=p.length1,m.length2=p.length2,l[g]=m}return l},E.prototype.patch_apply=function(c,l){if(c.length==0)return[l,[]];c=this.patch_deepCopy(c);var g=this.patch_addPadding(c);l=g+l+g,this.patch_splitMax(c);for(var p=0,m=[],d=0;d<c.length;d++){var v=c[d].start2+p,w=this.diff_text1(c[d].diffs),b,y=-1;if(w.length>this.Match_MaxBits?(b=this.match_main(l,w.substring(0,this.Match_MaxBits),v),b!=-1&&(y=this.match_main(l,w.substring(w.length-this.Match_MaxBits),v+w.length-this.Match_MaxBits),(y==-1||b>=y)&&(b=-1))):b=this.match_main(l,w,v),b==-1)m[d]=!1,p-=c[d].length2-c[d].length1;else{m[d]=!0,p=b-v;var s;if(y==-1?s=l.substring(b,b+w.length):s=l.substring(b,y+this.Match_MaxBits),w==s)l=l.substring(0,b)+this.diff_text2(c[d].diffs)+l.substring(b+w.length);else{var T=this.diff_main(w,s,!1);if(w.length>this.Match_MaxBits&&this.diff_levenshtein(T)/w.length>this.Patch_DeleteThreshold)m[d]=!1;else{this.diff_cleanupSemanticLossless(T);for(var A=0,D,C=0;C<c[d].diffs.length;C++){var j=c[d].diffs[C];j[0]!==L&&(D=this.diff_xIndex(T,A)),j[0]===x?l=l.substring(0,b+D)+j[1]+l.substring(b+D):j[0]===_&&(l=l.substring(0,b+D)+l.substring(b+this.diff_xIndex(T,A+j[1].length))),j[0]!==_&&(A+=j[1].length)}}}}}return l=l.substring(g.length,l.length-g.length),[l,m]},E.prototype.patch_addPadding=function(c){for(var l=this.Patch_Margin,g="",p=1;p<=l;p++)g+=String.fromCharCode(p);for(var p=0;p<c.length;p++)c[p].start1+=l,c[p].start2+=l;var m=c[0],d=m.diffs;if(d.length==0||d[0][0]!=L)d.unshift(new E.Diff(L,g)),m.start1-=l,m.start2-=l,m.length1+=l,m.length2+=l;else if(l>d[0][1].length){var v=l-d[0][1].length;d[0][1]=g.substring(d[0][1].length)+d[0][1],m.start1-=v,m.start2-=v,m.length1+=v,m.length2+=v}if(m=c[c.length-1],d=m.diffs,d.length==0||d[d.length-1][0]!=L)d.push(new E.Diff(L,g)),m.length1+=l,m.length2+=l;else if(l>d[d.length-1][1].length){var v=l-d[d.length-1][1].length;d[d.length-1][1]+=g.substring(0,v),m.length1+=v,m.length2+=v}return g},E.prototype.patch_splitMax=function(c){for(var l=this.Match_MaxBits,g=0;g<c.length;g++)if(!(c[g].length1<=l)){var p=c[g];c.splice(g--,1);for(var m=p.start1,d=p.start2,v="";p.diffs.length!==0;){var w=new E.patch_obj,b=!0;for(w.start1=m-v.length,w.start2=d-v.length,v!==""&&(w.length1=w.length2=v.length,w.diffs.push(new E.Diff(L,v)));p.diffs.length!==0&&w.length1<l-this.Patch_Margin;){var y=p.diffs[0][0],s=p.diffs[0][1];y===x?(w.length2+=s.length,d+=s.length,w.diffs.push(p.diffs.shift()),b=!1):y===_&&w.diffs.length==1&&w.diffs[0][0]==L&&s.length>2*l?(w.length1+=s.length,m+=s.length,b=!1,w.diffs.push(new E.Diff(y,s)),p.diffs.shift()):(s=s.substring(0,l-w.length1-this.Patch_Margin),w.length1+=s.length,m+=s.length,y===L?(w.length2+=s.length,d+=s.length):b=!1,w.diffs.push(new E.Diff(y,s)),s==p.diffs[0][1]?p.diffs.shift():p.diffs[0][1]=p.diffs[0][1].substring(s.length))}v=this.diff_text2(w.diffs),v=v.substring(v.length-this.Patch_Margin);var T=this.diff_text1(p.diffs).substring(0,this.Patch_Margin);T!==""&&(w.length1+=T.length,w.length2+=T.length,w.diffs.length!==0&&w.diffs[w.diffs.length-1][0]===L?w.diffs[w.diffs.length-1][1]+=T:w.diffs.push(new E.Diff(L,T))),b||c.splice(++g,0,w)}}},E.prototype.patch_toText=function(c){for(var l=[],g=0;g<c.length;g++)l[g]=c[g];return l.join("")},E.prototype.patch_fromText=function(c){var l=[];if(!c)return l;for(var g=c.split(`
`),p=0,m=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;p<g.length;){var d=g[p].match(m);if(!d)throw new Error("Invalid patch string: "+g[p]);var v=new E.patch_obj;for(l.push(v),v.start1=parseInt(d[1],10),d[2]===""?(v.start1--,v.length1=1):d[2]=="0"?v.length1=0:(v.start1--,v.length1=parseInt(d[2],10)),v.start2=parseInt(d[3],10),d[4]===""?(v.start2--,v.length2=1):d[4]=="0"?v.length2=0:(v.start2--,v.length2=parseInt(d[4],10)),p++;p<g.length;){var w=g[p].charAt(0);try{var b=decodeURI(g[p].substring(1))}catch(y){throw new Error("Illegal escape in patch_fromText: "+b)}if(w=="-")v.diffs.push(new E.Diff(_,b));else if(w=="+")v.diffs.push(new E.Diff(x,b));else if(w==" ")v.diffs.push(new E.Diff(L,b));else{if(w=="@")break;if(w!=="")throw new Error('Invalid patch mode "'+w+'" in: '+b)}p++}}return l},E.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},E.patch_obj.prototype.toString=function(){var c,l;this.length1===0?c=this.start1+",0":this.length1==1?c=this.start1+1:c=this.start1+1+","+this.length1,this.length2===0?l=this.start2+",0":this.length2==1?l=this.start2+1:l=this.start2+1+","+this.length2;for(var g=["@@ -"+c+" +"+l+` @@
`],p,m=0;m<this.diffs.length;m++){switch(this.diffs[m][0]){case x:p="+";break;case _:p="-";break;case L:p=" ";break}g[m+1]=p+encodeURI(this.diffs[m][1])+`
`}return g.join("").replace(/%20/g," ")},G.exports=E,G.exports.diff_match_patch=E,G.exports.DIFF_DELETE=_,G.exports.DIFF_INSERT=x,G.exports.DIFF_EQUAL=L},923:(G,E,_)=>{_.d(E,{default:()=>Fe});var x=_(288),L=_(59),c=_(784),l=_(51),g=_(500),p=_(339),m=_(108),d=function(R){R===void 0&&(R=document);var re=function(U){var P=document.createElement("img");P.src=U.getAttribute("data-src"),P.addEventListener("load",function(){!U.getAttribute("style")&&!U.getAttribute("class")&&!U.getAttribute("width")&&!U.getAttribute("height")&&P.naturalHeight>P.naturalWidth&&P.naturalWidth/P.naturalHeight<document.querySelector(".vditor-reset").clientWidth/(window.innerHeight-40)&&P.naturalHeight>window.innerHeight-40&&(U.style.height=window.innerHeight-40+"px"),U.src=P.src}),U.removeAttribute("data-src")};if(!("IntersectionObserver"in window))return R.querySelectorAll("img").forEach(function(U){U.getAttribute("data-src")&&re(U)}),!1;window.vditorImageIntersectionObserver?(window.vditorImageIntersectionObserver.disconnect(),R.querySelectorAll("img").forEach(function(U){window.vditorImageIntersectionObserver.observe(U)})):(window.vditorImageIntersectionObserver=new IntersectionObserver(function(U){U.forEach(function(P){(typeof P.isIntersecting=="undefined"?P.intersectionRatio!==0:P.isIntersecting)&&P.target.getAttribute("data-src")&&re(P.target)})}),R.querySelectorAll("img").forEach(function(U){window.vditorImageIntersectionObserver.observe(U)}))},v=_(960),w=_(0),b=_(975),y=_(931),s=_(597),T=_(162),A=_(70),D=_(591),C=_(913),j=_(873),V=_(161),q=_(598),Q=_(905),ne=function(R){document.querySelectorAll(".vditor-anchor").forEach(function(re){R===1&&re.classList.add("vditor-anchor--left"),re.onclick=function(){var U=re.getAttribute("href").substr(1),P=document.getElementById("vditorAnchor-"+U).offsetTop;document.querySelector("html").scrollTop=P}}),window.onhashchange=function(){var re=document.getElementById("vditorAnchor-"+decodeURIComponent(window.location.hash.substr(1)));re&&(document.querySelector("html").scrollTop=re.offsetTop)}},oe=_(796),me=_(827),fe=function(R,re){if(re===void 0&&(re="zh_CN"),!(typeof speechSynthesis=="undefined"||typeof SpeechSynthesisUtterance=="undefined")){var U=function(){var _e=speechSynthesis.getVoices(),J,de;return _e.forEach(function(He){He.lang===re.replace("_","-")&&(J=He),He.default&&(de=He)}),J||(J=de),J},P='<svg><use xlink:href="#vditor-icon-play"></use></svg>',pe='<svg><use xlink:href="#vditor-icon-pause"></use></svg>';document.getElementById("vditorIconScript")||(P='<svg viewBox="0 0 32 32"><path d="M3.436 0l25.128 16-25.128 16v-32z"></path></svg>',pe='<svg viewBox="0 0 32 32"><path d="M20.617 0h9.128v32h-9.128v-32zM2.255 32v-32h9.128v32h-9.128z"></path></svg>');var W=document.querySelector(".vditor-speech");W||(W=document.createElement("button"),W.className="vditor-speech",R.insertAdjacentElement("beforeend",W),speechSynthesis.onvoiceschanged!==void 0&&(speechSynthesis.onvoiceschanged=U));var ge=U(),ie=new SpeechSynthesisUtterance;ie.voice=ge,ie.onend=ie.onerror=function(){W.style.display="none",speechSynthesis.cancel(),W.classList.remove("vditor-speech--current"),W.innerHTML=P},R.addEventListener(window.ontouchstart!==void 0?"touchend":"click",function(_e){var J=_e.target;if(J.classList.contains("vditor-speech")||J.parentElement.classList.contains("vditor-speech")){W.classList.contains("vditor-speech--current")?speechSynthesis.speaking&&(speechSynthesis.paused?(speechSynthesis.resume(),W.innerHTML=pe):(speechSynthesis.pause(),W.innerHTML=P)):(ie.text=W.getAttribute("data-text"),speechSynthesis.speak(ie),W.classList.add("vditor-speech--current"),W.innerHTML=pe),(0,me.jl)(window.vditorSpeechRange),R.focus();return}if(W.style.display="none",speechSynthesis.cancel(),W.classList.remove("vditor-speech--current"),W.innerHTML=P,getSelection().rangeCount!==0){var de=getSelection().getRangeAt(0),He=de.toString().trim();if(He){window.vditorSpeechRange=de.cloneRange();var Ne=de.getBoundingClientRect();W.innerHTML=P,W.style.display="block",W.style.top=Ne.top+Ne.height+document.querySelector("html").scrollTop-20+"px",window.ontouchstart!==void 0?W.style.left=_e.changedTouches[_e.changedTouches.length-1].pageX+2+"px":W.style.left=_e.clientX+2+"px",W.setAttribute("data-text",He)}}})}},le=function(R,re,U,P){function pe(W){return W instanceof U?W:new U(function(ge){ge(W)})}return new(U||(U=Promise))(function(W,ge){function ie(de){try{J(P.next(de))}catch(He){ge(He)}}function _e(de){try{J(P.throw(de))}catch(He){ge(He)}}function J(de){de.done?W(de.value):pe(de.value).then(ie,_e)}J((P=P.apply(R,re||[])).next())})},ve=function(R,re){var U={label:0,sent:function(){if(W[0]&1)throw W[1];return W[1]},trys:[],ops:[]},P,pe,W,ge;return ge={next:ie(0),throw:ie(1),return:ie(2)},typeof Symbol=="function"&&(ge[Symbol.iterator]=function(){return this}),ge;function ie(J){return function(de){return _e([J,de])}}function _e(J){if(P)throw new TypeError("Generator is already executing.");for(;ge&&(ge=0,J[0]&&(U=0)),U;)try{if(P=1,pe&&(W=J[0]&2?pe.return:J[0]?pe.throw||((W=pe.return)&&W.call(pe),0):pe.next)&&!(W=W.call(pe,J[1])).done)return W;switch(pe=0,W&&(J=[J[0]&2,W.value]),J[0]){case 0:case 1:W=J;break;case 4:return U.label++,{value:J[1],done:!1};case 5:U.label++,pe=J[1],J=[0];continue;case 7:J=U.ops.pop(),U.trys.pop();continue;default:if(W=U.trys,!(W=W.length>0&&W[W.length-1])&&(J[0]===6||J[0]===2)){U=0;continue}if(J[0]===3&&(!W||J[1]>W[0]&&J[1]<W[3])){U.label=J[1];break}if(J[0]===6&&U.label<W[1]){U.label=W[1],W=J;break}if(W&&U.label<W[2]){U.label=W[2],U.ops.push(J);break}W[2]&&U.ops.pop(),U.trys.pop();continue}J=re.call(R,U)}catch(de){J=[6,de],pe=0}finally{P=W=0}if(J[0]&5)throw J[1];return{value:J[0]?J[1]:void 0,done:!0}}},Le=function(R){var re,U={anchor:0,cdn:C.Y.CDN,customEmoji:{},emojiPath:"".concat(C.Y.CDN,"/dist/images/emoji"),hljs:C.Y.HLJS_OPTIONS,icon:"ant",lang:"zh_CN",markdown:C.Y.MARKDOWN_OPTIONS,math:C.Y.MATH_OPTIONS,mode:"light",speech:{enable:!1},render:{media:{enable:!0}},theme:C.Y.THEME_OPTIONS};return R.cdn&&(!((re=R.theme)===null||re===void 0)&&re.path||(U.theme.path="".concat(R.cdn,"/dist/css/content-theme")),R.emojiPath||(U.emojiPath="".concat(R.cdn,"/dist/images/emoji"))),(0,Q.h)(U,R)},Ee=function(R,re){var U=Le(re);return(0,V.Z)("".concat(U.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then(function(){var P=(0,oe.X)({autoSpace:U.markdown.autoSpace,gfmAutoLink:U.markdown.gfmAutoLink,codeBlockPreview:U.markdown.codeBlockPreview,emojiSite:U.emojiPath,emojis:U.customEmoji,fixTermTypo:U.markdown.fixTermTypo,footnotes:U.markdown.footnotes,headingAnchor:U.anchor!==0,inlineMathDigit:U.math.inlineDigit,lazyLoadImage:U.lazyLoadImage,linkBase:U.markdown.linkBase,linkPrefix:U.markdown.linkPrefix,listStyle:U.markdown.listStyle,mark:U.markdown.mark,mathBlockPreview:U.markdown.mathBlockPreview,paragraphBeginningSpace:U.markdown.paragraphBeginningSpace,sanitize:U.markdown.sanitize,toc:U.markdown.toc});return re!=null&&re.renderers&&P.SetJSRenderers({renderers:{Md2HTML:re.renderers}}),P.SetHeadingID(!0),P.Md2HTML(R)})},M=function(R,re,U){return le(void 0,void 0,void 0,function(){var P,pe,W,ge;return ve(this,function(ie){switch(ie.label){case 0:return P=Le(U),[4,Ee(re,P)];case 1:if(pe=ie.sent(),P.transform&&(pe=P.transform(pe)),R.innerHTML=pe,R.classList.add("vditor-reset"),P.i18n)return[3,5];if(["en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(P.lang))return[3,2];throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");case 2:return W="vditorI18nScript",ge=W+P.lang,document.querySelectorAll('head script[id^="'.concat(W,'"]')).forEach(function(_e){_e.id!==ge&&document.head.removeChild(_e)}),[4,(0,V.Z)("".concat(P.cdn,"/dist/js/i18n/").concat(P.lang,".js"),ge)];case 3:ie.sent(),ie.label=4;case 4:return[3,6];case 5:window.VditorI18n=P.i18n,ie.label=6;case 6:return P.icon?[4,(0,V.Z)("".concat(P.cdn,"/dist/js/icons/").concat(P.icon,".js"),"vditorIconScript")]:[3,8];case 7:ie.sent(),ie.label=8;case 8:return(0,j.H)(P.theme.current,P.theme.path),P.anchor===1&&R.classList.add("vditor-reset--anchor"),(0,l.o)(R,P.hljs),(0,m.$)(P.hljs,R,P.cdn),(0,v.T)(R,{cdn:P.cdn,math:P.math}),(0,b.e)(R,P.cdn,P.mode),(0,y.Y)(R,P.cdn,P.mode),(0,s.K)(R,P.cdn),(0,g.D)(R,P.cdn),(0,p.m)(R,P.cdn),(0,c.v)(R,P.cdn,P.mode),(0,T.l)(R,P.cdn,P.mode),(0,D.M)(R,P.cdn),(0,x.$)(R,P.cdn),P.render.media.enable&&(0,w.l)(R),P.speech.enable&&fe(R),P.anchor!==0&&ne(P.anchor),P.after&&P.after(),P.lazyLoadImage&&d(R),R.addEventListener("click",function(_e){var J=(0,q._Y)(_e.target,"SPAN");if(J&&(0,q.KJ)(J,"vditor-toc")){var de=R.querySelector("#"+J.getAttribute("data-target-id"));de&&window.scrollTo(window.scrollX,de.offsetTop);return}}),[2]}})})},Be=_(726),pt=_(13),ht=function(){function R(){}return R.adapterRender=L,R.previewImage=Be.o,R.codeRender=l.o,R.graphvizRender=p.m,R.highlightRender=m.$,R.mathRender=v.T,R.mermaidRender=b.e,R.SMILESRender=y.Y,R.markmapRender=s.K,R.flowchartRender=g.D,R.chartRender=c.v,R.abcRender=x.$,R.mindmapRender=T.l,R.plantumlRender=D.M,R.outlineRender=A.N,R.mediaRender=w.l,R.speechRender=fe,R.lazyLoadImageRender=d,R.md2html=Ee,R.preview=M,R.setCodeTheme=pt.h,R.setContentTheme=j.H,R}();const Fe=ht},913:(G,E,_)=>{_.d(E,{Y:()=>L,g:()=>x});var x="3.10.9",L=function(){function c(){}return c.ZWSP="​",c.DROP_EDITOR="application/editor",c.MOBILE_WIDTH=520,c.CLASS_MENU_DISABLED="vditor-menu--disabled",c.EDIT_TOOLBARS=["emoji","headings","bold","italic","strike","link","list","ordered-list","outdent","indent","check","line","quote","code","inline-code","insert-after","insert-before","upload","record","table"],c.CODE_THEME=["a11y-dark","agate","an-old-hope","androidstudio","arta","atom-one-dark","atom-one-dark-reasonable","base16/3024","base16/apathy","base16/apprentice","base16/ashes","base16/atelier-cave","base16/atelier-dune","base16/atelier-estuary","base16/atelier-forest","base16/atelier-heath","base16/atelier-lakeside","base16/atelier-plateau","base16/atelier-savanna","base16/atelier-seaside","base16/atelier-sulphurpool","base16/atlas","base16/bespin","base16/black-metal","base16/black-metal-bathory","base16/black-metal-burzum","base16/black-metal-dark-funeral","base16/black-metal-gorgoroth","base16/black-metal-immortal","base16/black-metal-khold","base16/black-metal-marduk","base16/black-metal-mayhem","base16/black-metal-nile","base16/black-metal-venom","base16/brewer","base16/bright","base16/brogrammer","base16/brush-trees-dark","base16/chalk","base16/circus","base16/classic-dark","base16/codeschool","base16/colors","base16/danqing","base16/darcula","base16/dark-violet","base16/darkmoss","base16/darktooth","base16/decaf","base16/default-dark","base16/dracula","base16/edge-dark","base16/eighties","base16/embers","base16/equilibrium-dark","base16/equilibrium-gray-dark","base16/espresso","base16/eva","base16/eva-dim","base16/flat","base16/framer","base16/gigavolt","base16/google-dark","base16/grayscale-dark","base16/green-screen","base16/gruvbox-dark-hard","base16/gruvbox-dark-medium","base16/gruvbox-dark-pale","base16/gruvbox-dark-soft","base16/hardcore","base16/harmonic16-dark","base16/heetch-dark","base16/helios","base16/hopscotch","base16/horizon-dark","base16/humanoid-dark","base16/ia-dark","base16/icy-dark","base16/ir-black","base16/isotope","base16/kimber","base16/london-tube","base16/macintosh","base16/marrakesh","base16/materia","base16/material","base16/material-darker","base16/material-palenight","base16/material-vivid","base16/mellow-purple","base16/mocha","base16/monokai","base16/nebula","base16/nord","base16/nova","base16/ocean","base16/oceanicnext","base16/onedark","base16/outrun-dark","base16/papercolor-dark","base16/paraiso","base16/pasque","base16/phd","base16/pico","base16/pop","base16/porple","base16/qualia","base16/railscasts","base16/rebecca","base16/ros-pine","base16/ros-pine-moon","base16/sandcastle","base16/seti-ui","base16/silk-dark","base16/snazzy","base16/solar-flare","base16/solarized-dark","base16/spacemacs","base16/summercamp","base16/summerfruit-dark","base16/synth-midnight-terminal-dark","base16/tango","base16/tender","base16/tomorrow-night","base16/twilight","base16/unikitty-dark","base16/vulcan","base16/windows-10","base16/windows-95","base16/windows-high-contrast","base16/windows-nt","base16/woodland","base16/xcode-dusk","base16/zenburn","codepen-embed","dark","devibeans","far","felipec","github-dark","github-dark-dimmed","gml","gradient-dark","hybrid","ir-black","isbl-editor-dark","kimbie-dark","lioshi","monokai","monokai-sublime","night-owl","nnfx-dark","nord","obsidian","panda-syntax-dark","paraiso-dark","pojoaque","qtcreator-dark","rainbow","shades-of-purple","srcery","stackoverflow-dark","sunburst","tomorrow-night-blue","tomorrow-night-bright","tokyo-night-dark","vs2015","xt256","ant-design","a11y-light","arduino-light","ascetic","atom-one-light","base16/atelier-cave-light","base16/atelier-dune-light","base16/atelier-estuary-light","base16/atelier-forest-light","base16/atelier-heath-light","base16/atelier-lakeside-light","base16/atelier-plateau-light","base16/atelier-savanna-light","base16/atelier-seaside-light","base16/atelier-sulphurpool-light","base16/brush-trees","base16/classic-light","base16/cupcake","base16/cupertino","base16/default-light","base16/dirtysea","base16/edge-light","base16/equilibrium-gray-light","base16/equilibrium-light","base16/fruit-soda","base16/github","base16/google-light","base16/grayscale-light","base16/gruvbox-light-hard","base16/gruvbox-light-medium","base16/gruvbox-light-soft","base16/harmonic16-light","base16/heetch-light","base16/humanoid-light","base16/horizon-light","base16/ia-light","base16/material-lighter","base16/mexico-light","base16/one-light","base16/papercolor-light","base16/ros-pine-dawn","base16/sagelight","base16/shapeshifter","base16/silk-light","base16/solar-flare-light","base16/solarized-light","base16/summerfruit-light","base16/synth-midnight-terminal-light","base16/tomorrow","base16/unikitty-light","base16/windows-10-light","base16/windows-95-light","base16/windows-high-contrast-light","brown-paper","base16/windows-nt-light","color-brewer","docco","foundation","github","googlecode","gradient-light","grayscale","idea","intellij-light","isbl-editor-light","kimbie-light","lightfair","magula","mono-blue","nnfx-light","panda-syntax-light","paraiso-light","purebasic","qtcreator-light","routeros","school-book","stackoverflow-light","tokyo-night-light","vs","xcode","default"],c.ALIAS_CODE_LANGUAGES=["abc","plantuml","mermaid","flowchart","echarts","mindmap","graphviz","math","markmap","smiles","js","ts","html","toml","c#","bat"],c.CDN="https://unpkg.com/vditor@".concat("3.10.9"),c.MARKDOWN_OPTIONS={autoSpace:!1,gfmAutoLink:!0,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",linkPrefix:"",listStyle:!1,mark:!1,mathBlockPreview:!0,paragraphBeginningSpace:!1,sanitize:!0,toc:!1},c.HLJS_OPTIONS={enable:!0,lineNumber:!1,defaultLang:"",style:"github"},c.MATH_OPTIONS={engine:"KaTeX",inlineDigit:!1,macros:{}},c.THEME_OPTIONS={current:"light",list:{"ant-design":"Ant Design",dark:"Dark",light:"Light",wechat:"WeChat"},path:"".concat(c.CDN,"/dist/css/content-theme")},c}()},931:(G,E,_)=>{_.d(E,{Y:()=>g});var x=_(913),L=_(161),c=_(59),l=_(933),g=function(p,m,d){p===void 0&&(p=document),m===void 0&&(m=x.Y.CDN);var v=c.SMILESRenderAdapter.getElements(p);v.length>0&&(0,L.Z)("".concat(m,"/dist/js/smiles-drawer/smiles-drawer.min.js?v=2.1.7"),"vditorAbcjsScript").then(function(){var w=new SmiDrawer({},{});v.forEach(function(b){var y=c.SMILESRenderAdapter.getCode(b).trim();if(!(b.getAttribute("data-processed")==="true"||y.trim()==="")){var s="smiles"+(0,l.Ee)();b.innerHTML='<svg id="'.concat(s,'"></svg>'),w.draw(y,"#"+s,d==="dark"?"dark":void 0),b.setAttribute("data-processed","true")}})})}},288:(G,E,_)=>{_.d(E,{$:()=>l});var x=_(913),L=_(161),c=_(59),l=function(g,p){g===void 0&&(g=document),p===void 0&&(p=x.Y.CDN);var m=c.abcRenderAdapter.getElements(g);m.length>0&&(0,L.Z)("".concat(p,"/dist/js/abcjs/abcjs_basic.min.js"),"vditorAbcjsScript").then(function(){m.forEach(function(d){d.parentElement.classList.contains("vditor-wysiwyg__pre")||d.parentElement.classList.contains("vditor-ir__marker--pre")||d.getAttribute("data-processed")!=="true"&&(ABCJS.renderAbc(d,c.abcRenderAdapter.getCode(d).trim()),d.style.overflowX="auto",d.setAttribute("data-processed","true"))})})}},59:(G,E,_)=>{_.r(E),_.d(E,{SMILESRenderAdapter:()=>L,abcRenderAdapter:()=>m,chartRenderAdapter:()=>p,flowchartRenderAdapter:()=>v,graphvizRenderAdapter:()=>d,markmapRenderAdapter:()=>l,mathRenderAdapter:()=>x,mermaidRenderAdapter:()=>c,mindmapRenderAdapter:()=>g,plantumlRenderAdapter:()=>w});var x={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-math")}},L={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-smiles")}},c={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-mermaid")}},l={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-markmap")}},g={getCode:function(b){return b.getAttribute("data-code")},getElements:function(b){return b.querySelectorAll(".language-mindmap")}},p={getCode:function(b){return b.innerText},getElements:function(b){return b.querySelectorAll(".language-echarts")}},m={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-abc")}},d={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-graphviz")}},v={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-flowchart")}},w={getCode:function(b){return b.textContent},getElements:function(b){return b.querySelectorAll(".language-plantuml")}}},784:(G,E,_)=>{_.d(E,{v:()=>m});var x=_(913),L=_(161),c=_(59),l=_(933),g=function(d,v,w,b){function y(s){return s instanceof w?s:new w(function(T){T(s)})}return new(w||(w=Promise))(function(s,T){function A(j){try{C(b.next(j))}catch(V){T(V)}}function D(j){try{C(b.throw(j))}catch(V){T(V)}}function C(j){j.done?s(j.value):y(j.value).then(A,D)}C((b=b.apply(d,v||[])).next())})},p=function(d,v){var w={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},b,y,s,T;return T={next:A(0),throw:A(1),return:A(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function A(C){return function(j){return D([C,j])}}function D(C){if(b)throw new TypeError("Generator is already executing.");for(;T&&(T=0,C[0]&&(w=0)),w;)try{if(b=1,y&&(s=C[0]&2?y.return:C[0]?y.throw||((s=y.return)&&s.call(y),0):y.next)&&!(s=s.call(y,C[1])).done)return s;switch(y=0,s&&(C=[C[0]&2,s.value]),C[0]){case 0:case 1:s=C;break;case 4:return w.label++,{value:C[1],done:!1};case 5:w.label++,y=C[1],C=[0];continue;case 7:C=w.ops.pop(),w.trys.pop();continue;default:if(s=w.trys,!(s=s.length>0&&s[s.length-1])&&(C[0]===6||C[0]===2)){w=0;continue}if(C[0]===3&&(!s||C[1]>s[0]&&C[1]<s[3])){w.label=C[1];break}if(C[0]===6&&w.label<s[1]){w.label=s[1],s=C;break}if(s&&w.label<s[2]){w.label=s[2],w.ops.push(C);break}s[2]&&w.ops.pop(),w.trys.pop();continue}C=v.call(d,w)}catch(j){C=[6,j],y=0}finally{b=s=0}if(C[0]&5)throw C[1];return{value:C[0]?C[1]:void 0,done:!0}}},m=function(d,v,w){d===void 0&&(d=document),v===void 0&&(v=x.Y.CDN);var b=c.chartRenderAdapter.getElements(d);b.length>0&&(0,L.Z)("".concat(v,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){b.forEach(function(y){return g(void 0,void 0,void 0,function(){var s,T,A;return p(this,function(D){switch(D.label){case 0:if(y.parentElement.classList.contains("vditor-wysiwyg__pre")||y.parentElement.classList.contains("vditor-ir__marker--pre"))return[2];if(s=c.chartRenderAdapter.getCode(y).trim(),!s)return[2];D.label=1;case 1:return D.trys.push([1,3,,4]),y.getAttribute("data-processed")==="true"?[2]:[4,(0,l.kY)(s)];case 2:return T=D.sent(),echarts.init(y,w==="dark"?"dark":void 0).setOption(T),y.setAttribute("data-processed","true"),[3,4];case 3:return A=D.sent(),y.className="vditor-reset--error",y.innerHTML="echarts render error: <br>".concat(A),[3,4];case 4:return[2]}})})})})}},51:(G,E,_)=>{_.d(E,{o:()=>c});var x=_(695),L=_(913),c=function(l,g){Array.from(l.querySelectorAll("pre > code")).filter(function(p,m){return!(p.parentElement.classList.contains("vditor-wysiwyg__pre")||p.parentElement.classList.contains("vditor-ir__marker--pre")||p.classList.contains("language-mermaid")||p.classList.contains("language-flowchart")||p.classList.contains("language-echarts")||p.classList.contains("language-mindmap")||p.classList.contains("language-plantuml")||p.classList.contains("language-markmap")||p.classList.contains("language-abc")||p.classList.contains("language-graphviz")||p.classList.contains("language-math")||p.classList.contains("language-smiles")||p.style.maxHeight.indexOf("px")>-1||l.classList.contains("vditor-preview")&&m>5)}).forEach(function(p){var m,d,v,w=p.innerText;if(p.classList.contains("highlight-chroma")){var b=p.cloneNode(!0);b.querySelectorAll(".highlight-ln").forEach(function(A){A.remove()}),w=b.innerText}else w.endsWith(`
`)&&(w=w.substr(0,w.length-1));var y='<svg><use xlink:href="#vditor-icon-copy"></use></svg>';document.getElementById("vditorIconScript")||(y='<svg viewBox="0 0 32 32"><path d="M22.545-0h-17.455c-1.6 0-2.909 1.309-2.909 2.909v20.364h2.909v-20.364h17.455v-2.909zM26.909 5.818h-16c-1.6 0-2.909 1.309-2.909 2.909v20.364c0 1.6 1.309 2.909 2.909 2.909h16c1.6 0 2.909-1.309 2.909-2.909v-20.364c0-1.6-1.309-2.909-2.909-2.909zM26.909 29.091h-16v-20.364h16v20.364z"></path></svg>');var s=document.createElement("div");s.className="vditor-copy",s.innerHTML='<span aria-label="'.concat(((m=window.VditorI18n)===null||m===void 0?void 0:m.copy)||"复制",`"
onmouseover="this.setAttribute('aria-label', '`).concat(((d=window.VditorI18n)===null||d===void 0?void 0:d.copy)||"复制",`')"
class="vditor-tooltipped vditor-tooltipped__w"
onclick="this.previousElementSibling.select();document.execCommand('copy');this.setAttribute('aria-label', '`).concat(((v=window.VditorI18n)===null||v===void 0?void 0:v.copied)||"已复制",`');this.previousElementSibling.blur()">`).concat(y,"</span>");var T=document.createElement("textarea");T.value=(0,x.p)(w),s.insertAdjacentElement("afterbegin",T),g&&g.renderMenu&&g.renderMenu(p,s),p.before(s),p.style.maxHeight=window.outerHeight-40+"px",p.insertAdjacentHTML("afterend",'<span style="position: absolute">'.concat(L.Y.ZWSP,"</span>"))})}},500:(G,E,_)=>{_.d(E,{D:()=>l});var x=_(913),L=_(161),c=_(59),l=function(g,p){p===void 0&&(p=x.Y.CDN);var m=c.flowchartRenderAdapter.getElements(g);m.length!==0&&(0,L.Z)("".concat(p,"/dist/js/flowchart.js/flowchart.min.js"),"vditorFlowchartScript").then(function(){m.forEach(function(d){if(d.getAttribute("data-processed")!=="true"){var v=flowchart.parse(c.flowchartRenderAdapter.getCode(d));d.innerHTML="",v.drawSVG(d),d.setAttribute("data-processed","true")}})})}},339:(G,E,_)=>{_.d(E,{m:()=>l});var x=_(913),L=_(161),c=_(59),l=function(g,p){p===void 0&&(p=x.Y.CDN);var m=c.graphvizRenderAdapter.getElements(g);m.length!==0&&(0,L.Z)("".concat(p,"/dist/js/graphviz/viz.js"),"vditorGraphVizScript").then(function(){m.forEach(function(d){var v=c.graphvizRenderAdapter.getCode(d);if(!(d.parentElement.classList.contains("vditor-wysiwyg__pre")||d.parentElement.classList.contains("vditor-ir__marker--pre"))&&!(d.getAttribute("data-processed")==="true"||v.trim()==="")){try{var w=new Blob(["importScripts('".concat(document.getElementById("vditorGraphVizScript").src.replace("viz.js","full.render.js"),"');")],{type:"application/javascript"}),b=window.URL||window.webkitURL,y=b.createObjectURL(w),s=new Worker(y);new Viz({worker:s}).renderSVGElement(v).then(function(T){d.innerHTML=T.outerHTML}).catch(function(T){d.innerHTML="graphviz render error: <br>".concat(T),d.className="vditor-reset--error"})}catch(T){console.error("graphviz error",T)}d.setAttribute("data-processed","true")}})})}},108:(G,E,_)=>{_.d(E,{$:()=>l});var x=_(913),L=_(161),c=_(505),l=function(g,p,m){p===void 0&&(p=document),m===void 0&&(m=x.Y.CDN);var d=g.style;x.Y.CODE_THEME.includes(d)||(d="github");var v=document.getElementById("vditorHljsStyle"),w="".concat(m,"/dist/js/highlight.js/styles/").concat(d,".min.css");if(v&&v.getAttribute("href")!==w&&v.remove(),(0,c.T)("".concat(m,"/dist/js/highlight.js/styles/").concat(d,".min.css"),"vditorHljsStyle"),g.enable!==!1){var b=p.querySelectorAll("pre > code");b.length!==0&&(0,L.Z)("".concat(m,"/dist/js/highlight.js/highlight.min.js?v=11.7.0"),"vditorHljsScript").then(function(){(0,L.Z)("".concat(m,"/dist/js/highlight.js/third-languages.js?v=1.0.1"),"vditorHljsThirdScript").then(function(){p.querySelectorAll("pre > code").forEach(function(y){if(!(y.parentElement.classList.contains("vditor-ir__marker--pre")||y.parentElement.classList.contains("vditor-wysiwyg__pre"))&&!(y.classList.contains("language-mermaid")||y.classList.contains("language-flowchart")||y.classList.contains("language-echarts")||y.classList.contains("language-mindmap")||y.classList.contains("language-plantuml")||y.classList.contains("language-smiles")||y.classList.contains("language-abc")||y.classList.contains("language-graphviz")||y.classList.contains("language-math"))){g.defaultLang!==""&&y.className.indexOf("language-")===-1&&y.classList.add("language-"+g.defaultLang);var s=y.className.replace("language-","");if(window.hljs.getLanguage(s)||(s="plaintext"),y.innerHTML=window.hljs.highlight(y.textContent,{language:s,ignoreIllegals:!0}).value,y.classList.add("hljs"),!!g.lineNumber){y.classList.add("vditor-linenumber");var T=y.querySelector(".vditor-linenumber__temp");T||(T=document.createElement("div"),T.className="vditor-linenumber__temp",y.insertAdjacentElement("beforeend",T));var A=getComputedStyle(y).whiteSpace,D=!1;(A==="pre-wrap"||A==="pre-line")&&(D=!0);var C="",j=y.textContent.split(/\r\n|\r|\n/g);j.pop(),j.map(function(V){var q="";D&&(T.textContent=V||`
`,q=' style="height:'.concat(T.getBoundingClientRect().height,'px"')),C+="<span".concat(q,"></span>")}),T.style.display="none",C='<span class="vditor-linenumber__rows">'.concat(C,"</span>"),y.insertAdjacentHTML("beforeend",C)}}})})})}}},597:(G,E,_)=>{_.d(E,{K:()=>m});var x=_(913),L=_(161),c=_(59),l={},g=function(d,v){var w=d.transform(v),b=Object.keys(w.features).filter(function(D){return!l[D]});b.forEach(function(D){l[D]=!0});var y=d.getAssets(b),s=y.styles,T=y.scripts,A=window.markmap;return s&&A.loadCSS(s),T&&A.loadJS(T),w},p=function(d,v){var w=window.markmap,b=w.Transformer,y=w.Markmap,s=w.deriveOptions;w.globalCSS;var T=new b;d.innerHTML='<svg style="width:100%"></svg>';var A=d.firstChild,D=y.create(A,null),C=g(T,v),j=C.root,V=C.frontmatter,q=V==null?void 0:V.markmap,Q=s(q);D.setData(j,Q),D.fit()},m=function(d,v){d===void 0&&(d=document),v===void 0&&(v=x.Y.CDN);var w=c.markmapRenderAdapter.getElements(d);w.length!==0&&(0,L.Z)("".concat(v,"/dist/js/markmap/markmap.min.js"),"vditorMarkerScript").then(function(){w.forEach(function(b){var y=c.markmapRenderAdapter.getCode(b);if(!(b.getAttribute("data-processed")==="true"||y.trim()==="")){var s=document.createElement("div");s.className="language-markmap",b.parentNode.appendChild(s),p(s,y),b.parentNode.childNodes[0].nodeName=="CODE"&&b.parentNode.removeChild(b.parentNode.childNodes[0])}})})}},960:(G,E,_)=>{_.d(E,{T:()=>p});var x=_(913),L=_(161),c=_(505),l=_(695),g=_(59),p=function(m,d){m===void 0&&(m=document);var v=g.mathRenderAdapter.getElements(m);if(v.length!==0){var w={cdn:x.Y.CDN,math:{engine:"KaTeX",inlineDigit:!1,macros:{}}};if(d&&d.math&&(d.math=Object.assign({},w.math,d.math)),d=Object.assign({},w,d),d.math.engine==="KaTeX")(0,c.T)("".concat(d.cdn,"/dist/js/katex/katex.min.css?v=0.16.9"),"vditorKatexStyle"),(0,L.Z)("".concat(d.cdn,"/dist/js/katex/katex.min.js?v=0.16.9"),"vditorKatexScript").then(function(){(0,L.Z)("".concat(d.cdn,"/dist/js/katex/mhchem.min.js?v=0.16.9"),"vditorKatexChemScript").then(function(){v.forEach(function(s){if(!(s.parentElement.classList.contains("vditor-wysiwyg__pre")||s.parentElement.classList.contains("vditor-ir__marker--pre"))&&!s.getAttribute("data-math")){var T=(0,l.p)(g.mathRenderAdapter.getCode(s));s.setAttribute("data-math",T);try{s.innerHTML=katex.renderToString(T,{displayMode:s.tagName==="DIV",output:"html",macros:d.math.macros})}catch(A){s.innerHTML=A.message,s.className="language-math vditor-reset--error"}s.addEventListener("copy",function(A){A.stopPropagation(),A.preventDefault();var D=A.currentTarget.closest(".language-math");A.clipboardData.setData("text/html",D.innerHTML),A.clipboardData.setData("text/plain",D.getAttribute("data-math"))})}})})});else if(d.math.engine==="MathJax"){var b=function(s){if(s.length!==0){var T=0,A=s[s.length-1],D=function(){var C=s[T++];C===A?C():C(D)};D()}};window.MathJax||(window.MathJax={loader:{paths:{mathjax:"".concat(d.cdn,"/dist/js/mathjax")}},startup:{typeset:!1},tex:{macros:d.math.macros}},Object.assign(window.MathJax,d.math.mathJaxOptions)),(0,L.U)("".concat(d.cdn,"/dist/js/mathjax/tex-svg-full.js"),"protyleMathJaxScript");var y=function(s,T){var A=(0,l.p)(s.textContent).trim(),D=window.MathJax.getMetricsFor(s);D.display=s.tagName==="DIV",window.MathJax.tex2svgPromise(A,D).then(function(C){s.innerHTML="",s.setAttribute("data-math",A),s.append(C),window.MathJax.startup.document.clear(),window.MathJax.startup.document.updateDocument();var j=C.querySelector('[data-mml-node="merror"]');j&&j.textContent.trim()!==""&&(s.innerHTML=j.textContent.trim(),s.className="vditor-reset--error"),T&&T()})};window.MathJax.startup.promise.then(function(){for(var s=[],T=function(D){var C=v[D];!C.parentElement.classList.contains("vditor-wysiwyg__pre")&&!C.parentElement.classList.contains("vditor-ir__marker--pre")&&!C.getAttribute("data-math")&&(0,l.p)(C.textContent).trim()&&s.push(function(j){D===v.length-1?y(C):y(C,j)})},A=0;A<v.length;A++)T(A);b(s)})}}}},0:(G,E,_)=>{_.d(E,{l:()=>g});var x=_(933),L=function(p,m){p.insertAdjacentHTML("afterend",'<video controls="controls" src="'.concat(m,'"></video>')),p.remove()},c=function(p,m){p.insertAdjacentHTML("afterend",'<audio controls="controls" src="'.concat(m,'"></audio>')),p.remove()},l=function(p,m){var d=m.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?/),v=m.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),w=m.match(/\/\/v\.qq\.com\/x\/cover\/.*\/([^\/]+)\.html\??.*/),b=m.match(/(?:www\.|\/\/)coub\.com\/view\/(\w+)/),y=m.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/),s=m.match(/.+dailymotion.com\/(video|hub)\/(\w+)\?/),T=m.match(/(?:www\.|\/\/)bilibili\.com\/video\/(\w+)/),A=m.match(/(?:www\.|\/\/)ted\.com\/talks\/(\w+)/);if(d&&d[1].length===11)p.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//www.youtube.com/embed/'.concat(d[1]+(d[2]?"?start="+d[2]:""),'"></iframe>')),p.remove();else if(v&&v[1])p.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//player.youku.com/embed/'.concat(v[1],'"></iframe>')),p.remove();else if(w&&w[1])p.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="https://v.qq.com/txp/iframe/player.html?vid='.concat(w[1],'"></iframe>')),p.remove();else if(b&&b[1])p.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="//coub.com/embed/`.concat(b[1],'?muted=false&autostart=false&originalSize=true&startWithHD=true"></iframe>')),p.remove();else if(y&&y[0])p.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="https://www.facebook.com/plugins/video.php?href=`.concat(encodeURIComponent(y[0]),'"></iframe>')),p.remove();else if(s&&s[2])p.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="https://www.dailymotion.com/embed/video/`.concat(s[2],'"></iframe>')),p.remove();else if(m.indexOf("bilibili.com")>-1&&(m.indexOf("bvid=")>-1||T&&T[1])){var D={bvid:(0,x.TK)("bvid",m)||T&&T[1],page:"1",high_quality:"1",as_wide:"1",allowfullscreen:"true",autoplay:"0"};new URL(m.startsWith("http")?m:"https:"+m).search.split("&").forEach(function(V,q){if(V){q===0&&(V=V.substr(1));var Q=V.split("=");D[Q[0]]=Q[1]}});var C="https://player.bilibili.com/player.html?",j=Object.keys(D);j.forEach(function(V,q){C+="".concat(V,"=").concat(D[V]),q<j.length-1&&(C+="&")}),p.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="'.concat(C,'"></iframe>')),p.remove()}else A&&A[1]&&(p.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//embed.ted.com/talks/'.concat(A[1],'"></iframe>')),p.remove())},g=function(p){p&&p.querySelectorAll("a").forEach(function(m){var d=m.getAttribute("href");d&&(d.match(/^.+.(mp4|m4v|ogg|ogv|webm)$/)?L(m,d):d.match(/^.+.(mp3|wav|flac)$/)?c(m,d):l(m,d))})}},975:(G,E,_)=>{_.d(E,{e:()=>m});var x=_(913),L=_(161),c=_(59),l=_(933),g=function(d,v,w,b){function y(s){return s instanceof w?s:new w(function(T){T(s)})}return new(w||(w=Promise))(function(s,T){function A(j){try{C(b.next(j))}catch(V){T(V)}}function D(j){try{C(b.throw(j))}catch(V){T(V)}}function C(j){j.done?s(j.value):y(j.value).then(A,D)}C((b=b.apply(d,v||[])).next())})},p=function(d,v){var w={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},b,y,s,T;return T={next:A(0),throw:A(1),return:A(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function A(C){return function(j){return D([C,j])}}function D(C){if(b)throw new TypeError("Generator is already executing.");for(;T&&(T=0,C[0]&&(w=0)),w;)try{if(b=1,y&&(s=C[0]&2?y.return:C[0]?y.throw||((s=y.return)&&s.call(y),0):y.next)&&!(s=s.call(y,C[1])).done)return s;switch(y=0,s&&(C=[C[0]&2,s.value]),C[0]){case 0:case 1:s=C;break;case 4:return w.label++,{value:C[1],done:!1};case 5:w.label++,y=C[1],C=[0];continue;case 7:C=w.ops.pop(),w.trys.pop();continue;default:if(s=w.trys,!(s=s.length>0&&s[s.length-1])&&(C[0]===6||C[0]===2)){w=0;continue}if(C[0]===3&&(!s||C[1]>s[0]&&C[1]<s[3])){w.label=C[1];break}if(C[0]===6&&w.label<s[1]){w.label=s[1],s=C;break}if(s&&w.label<s[2]){w.label=s[2],w.ops.push(C);break}s[2]&&w.ops.pop(),w.trys.pop();continue}C=v.call(d,w)}catch(j){C=[6,j],y=0}finally{b=s=0}if(C[0]&5)throw C[1];return{value:C[0]?C[1]:void 0,done:!0}}},m=function(d,v,w){d===void 0&&(d=document),v===void 0&&(v=x.Y.CDN);var b=c.mermaidRenderAdapter.getElements(d);b.length!==0&&(0,L.Z)("".concat(v,"/dist/js/mermaid/mermaid.min.js"),"vditorMermaidScript").then(function(){var y={securityLevel:"loose",altFontFamily:"sans-serif",fontFamily:"sans-serif",startOnLoad:!1,flowchart:{htmlLabels:!0,useMaxWidth:!0},sequence:{useMaxWidth:!0,diagramMarginX:8,diagramMarginY:8,boxMargin:8,showSequenceNumbers:!0},gantt:{leftPadding:75,rightPadding:20}};w==="dark"&&(y.theme="dark"),mermaid.initialize(y),b.forEach(function(s){return g(void 0,void 0,void 0,function(){var T,A,D,C,j;return p(this,function(V){switch(V.label){case 0:if(T=c.mermaidRenderAdapter.getCode(s),s.getAttribute("data-processed")==="true"||T.trim()==="")return[2];A="mermaid"+(0,l.Ee)(),V.label=1;case 1:return V.trys.push([1,3,,4]),[4,mermaid.render(A,s.textContent)];case 2:return D=V.sent(),s.innerHTML=D.svg,[3,4];case 3:return C=V.sent(),j=document.querySelector("#"+A),s.innerHTML="".concat(j.outerHTML,`<br>
<div style="text-align: left"><small>`).concat(C.message.replace(/\n/,"<br>"),"</small></div>"),j.parentElement.remove(),[3,4];case 4:return s.setAttribute("data-processed","true"),[2]}})})})})}},162:(G,E,_)=>{_.d(E,{l:()=>l});var x=_(913),L=_(161),c=_(59),l=function(g,p,m){g===void 0&&(g=document),p===void 0&&(p=x.Y.CDN);var d=c.mindmapRenderAdapter.getElements(g);d.length>0&&(0,L.Z)("".concat(p,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){d.forEach(function(v){if(!(v.parentElement.classList.contains("vditor-wysiwyg__pre")||v.parentElement.classList.contains("vditor-ir__marker--pre"))){var w=c.mindmapRenderAdapter.getCode(v);if(w)try{if(v.getAttribute("data-processed")==="true")return;echarts.init(v,m==="dark"?"dark":void 0).setOption({series:[{data:[JSON.parse(decodeURIComponent(w))],initialTreeDepth:-1,itemStyle:{borderWidth:0,color:"#4285f4"},label:{backgroundColor:"#f6f8fa",borderColor:"#d1d5da",borderRadius:5,borderWidth:.5,color:"#586069",lineHeight:20,offset:[-5,0],padding:[0,5],position:"insideRight"},lineStyle:{color:"#d1d5da",width:1},roam:!0,symbol:function(b,y){var s;return!((s=y==null?void 0:y.data)===null||s===void 0)&&s.children?"circle":"path://"},type:"tree"}],tooltip:{trigger:"item",triggerOn:"mousemove"}}),v.setAttribute("data-processed","true")}catch(b){v.className="vditor-reset--error",v.innerHTML="mindmap render error: <br>".concat(b)}}})})}},70:(G,E,_)=>{_.d(E,{N:()=>c});var x=_(164),L=_(960),c=function(l,g,p){var m="",d=[];if(Array.from(l.children).forEach(function(y,s){if((0,x.c)(y)){if(p){var T=y.id.lastIndexOf("_");y.id=y.id.substring(0,T===-1?void 0:T)+"_"+s}d.push(y.id),m+=y.outerHTML.replace("<wbr>","")}}),m==="")return g.innerHTML="","";var v=document.createElement("div");if(p)p.lute.SetToC(!0),p.currentMode==="wysiwyg"&&!p.preview.element.contains(l)?v.innerHTML=p.lute.SpinVditorDOM("<p>[ToC]</p>"+m):p.currentMode==="ir"&&!p.preview.element.contains(l)?v.innerHTML=p.lute.SpinVditorIRDOM("<p>[ToC]</p>"+m):v.innerHTML=p.lute.HTML2VditorDOM("<p>[ToC]</p>"+m),p.lute.SetToC(p.options.preview.markdown.toc);else{g.classList.add("vditor-outline");var w=Lute.New();w.SetToC(!0),v.innerHTML=w.HTML2VditorDOM("<p>[ToC]</p>"+m)}var b=v.firstElementChild.querySelectorAll("li > span[data-target-id]");return b.forEach(function(y,s){if(y.nextElementSibling&&y.nextElementSibling.tagName==="UL"){var T="<svg class='vditor-outline__action'><use xlink:href='#vditor-icon-down'></use></svg>";document.getElementById("vditorIconScript")||(T='<svg class="vditor-outline__action" viewBox="0 0 32 32"><path d="M3.76 6.12l12.24 12.213 12.24-12.213 3.76 3.76-16 16-16-16 3.76-3.76z"></path></svg>'),y.innerHTML="".concat(T,"<span>").concat(y.innerHTML,"</span>")}else y.innerHTML="<svg></svg><span>".concat(y.innerHTML,"</span>");y.setAttribute("data-target-id",d[s])}),m=v.firstElementChild.innerHTML,b.length===0?(g.innerHTML="",m):(g.innerHTML=m,p&&(0,L.T)(g,{cdn:p.options.cdn,math:p.options.preview.math}),g.firstElementChild.addEventListener("click",function(y){for(var s=y.target;s&&!s.isEqualNode(g);){if(s.classList.contains("vditor-outline__action")){s.classList.contains("vditor-outline__action--close")?(s.classList.remove("vditor-outline__action--close"),s.parentElement.nextElementSibling.setAttribute("style","display:block")):(s.classList.add("vditor-outline__action--close"),s.parentElement.nextElementSibling.setAttribute("style","display:none")),y.preventDefault(),y.stopPropagation();break}else if(s.getAttribute("data-target-id")){y.preventDefault(),y.stopPropagation();var T=document.getElementById(s.getAttribute("data-target-id"));if(!T)return;if(p)if(p.options.height==="auto"){var A=T.offsetTop+p.element.offsetTop;p.options.toolbarConfig.pin||(A+=p.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,A)}else p.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,p.element.offsetTop),p.preview.element.contains(l)?l.parentElement.scrollTop=T.offsetTop:l.scrollTop=T.offsetTop;else window.scrollTo(window.scrollX,T.offsetTop);break}s=s.parentElement}}),m)}},591:(G,E,_)=>{_.d(E,{M:()=>l});var x=_(913),L=_(161),c=_(59),l=function(g,p){g===void 0&&(g=document),p===void 0&&(p=x.Y.CDN);var m=c.plantumlRenderAdapter.getElements(g);m.length!==0&&(0,L.Z)("".concat(p,"/dist/js/plantuml/plantuml-encoder.min.js"),"vditorPlantumlScript").then(function(){m.forEach(function(d){if(!(d.parentElement.classList.contains("vditor-wysiwyg__pre")||d.parentElement.classList.contains("vditor-ir__marker--pre"))){var v=c.plantumlRenderAdapter.getCode(d).trim();if(v)try{d.innerHTML='<object type="image/svg+xml" data="https://www.plantuml.com/plantuml/svg/~1'.concat(plantumlEncoder.encode(v),'"/>')}catch(w){d.className="vditor-reset--error",d.innerHTML="plantuml render error: <br>".concat(w)}}})})}},796:(G,E,_)=>{_.d(E,{X:()=>x});var x=function(L){var c=Lute.New();return c.PutEmojis(L.emojis),c.SetEmojiSite(L.emojiSite),c.SetHeadingAnchor(L.headingAnchor),c.SetInlineMathAllowDigitAfterOpenMarker(L.inlineMathDigit),c.SetAutoSpace(L.autoSpace),c.SetToC(L.toc),c.SetFootnotes(L.footnotes),c.SetFixTermTypo(L.fixTermTypo),c.SetVditorCodeBlockPreview(L.codeBlockPreview),c.SetVditorMathBlockPreview(L.mathBlockPreview),c.SetSanitize(L.sanitize),c.SetChineseParagraphBeginningSpace(L.paragraphBeginningSpace),c.SetRenderListStyle(L.listStyle),c.SetLinkBase(L.linkBase),c.SetLinkPrefix(L.linkPrefix),c.SetMark(L.mark),c.SetGFMAutoLink(L.gfmAutoLink),L.lazyLoadImage&&c.SetImageLazyLoading(L.lazyLoadImage),c}},726:(G,E,_)=>{_.d(E,{o:()=>x});var x=function(L,c,l){l===void 0&&(l="classic");var g=L.getBoundingClientRect(),p=36;document.body.insertAdjacentHTML("beforeend",'<div class="vditor vditor-img'.concat(l==="dark"?" vditor--dark":"",`">
    <div class="vditor-img__bar">
      <span class="vditor-img__btn" data-deg="0">
        <svg><use xlink:href="#vditor-icon-redo"></use></svg>
        `).concat(window.VditorI18n.spin,`
      </span>
      <span class="vditor-img__btn"  onclick="this.parentElement.parentElement.outerHTML = '';document.body.style.overflow = ''">
        X &nbsp;`).concat(window.VditorI18n.close,`
      </span>
    </div>
    <div class="vditor-img__img" onclick="this.parentElement.outerHTML = '';document.body.style.overflow = ''">
      <img style="width: `).concat(L.width,"px;height:").concat(L.height,"px;transform: translate3d(").concat(g.left,"px, ").concat(g.top-p,'px, 0)" src="').concat(L.getAttribute("src"),`">
    </div>
</div>`)),document.body.style.overflow="hidden";var m=document.querySelector(".vditor-img img"),d="translate3d(".concat(Math.max(0,window.innerWidth-L.naturalWidth)/2,"px, ").concat(Math.max(0,window.innerHeight-p-L.naturalHeight)/2,"px, 0)");setTimeout(function(){m.setAttribute("style","transition: transform .3s ease-in-out;transform: ".concat(d)),setTimeout(function(){m.parentElement.scrollTo((m.parentElement.scrollWidth-m.parentElement.clientWidth)/2,(m.parentElement.scrollHeight-m.parentElement.clientHeight)/2)},400)});var v=document.querySelector(".vditor-img__btn");v.addEventListener("click",function(){var w=parseInt(v.getAttribute("data-deg"),10)+90;w/90%2===1&&L.naturalWidth>m.parentElement.clientHeight?m.style.transform="translate3d(".concat(Math.max(0,window.innerWidth-L.naturalWidth)/2,"px, ").concat(L.naturalWidth/2-L.naturalHeight/2,"px, 0) rotateZ(").concat(w,"deg)"):m.style.transform="".concat(d," rotateZ(").concat(w,"deg)"),v.setAttribute("data-deg",w.toString()),setTimeout(function(){m.parentElement.scrollTo((m.parentElement.scrollWidth-m.parentElement.clientWidth)/2,(m.parentElement.scrollHeight-m.parentElement.clientHeight)/2)},400)})}},13:(G,E,_)=>{_.d(E,{h:()=>c});var x=_(913),L=_(505),c=function(l,g){g===void 0&&(g=x.Y.CDN),x.Y.CODE_THEME.includes(l)||(l="github");var p=document.getElementById("vditorHljsStyle"),m="".concat(g,"/dist/js/highlight.js/styles/").concat(l,".min.css");p?p.getAttribute("href")!==m&&(p.remove(),(0,L.T)(m,"vditorHljsStyle")):(0,L.T)(m,"vditorHljsStyle")}},873:(G,E,_)=>{_.d(E,{H:()=>L});var x=_(505),L=function(c,l){if(!(!c||!l)){var g=document.getElementById("vditorContentTheme"),p="".concat(l,"/").concat(c,".css");g?g.getAttribute("href")!==p&&(g.remove(),(0,x.T)(p,"vditorContentTheme")):(0,x.T)(p,"vditorContentTheme")}}},161:(G,E,_)=>{_.d(E,{U:()=>x,Z:()=>L});var x=function(c,l){if(document.getElementById(l))return!1;var g=new XMLHttpRequest;g.open("GET",c,!1),g.setRequestHeader("Accept","text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01"),g.send("");var p=document.createElement("script");p.type="text/javascript",p.text=g.responseText,p.id=l,document.head.appendChild(p)},L=function(c,l){return new Promise(function(g,p){if(document.getElementById(l))return g(!0),!1;var m=document.createElement("script");m.src=c,m.async=!0,document.head.appendChild(m),m.onerror=function(d){p(d)},m.onload=function(){if(document.getElementById(l))return m.remove(),g(!0),!1;m.id=l,g(!0)}})}},505:(G,E,_)=>{_.d(E,{T:()=>x});var x=function(L,c){if(!document.getElementById(c)){var l=document.createElement("link");l.id=c,l.rel="stylesheet",l.type="text/css",l.href=L,document.getElementsByTagName("head")[0].appendChild(l)}}},695:(G,E,_)=>{_.d(E,{p:()=>x});var x=function(L){return L.replace(/\u00a0/g," ")}},629:(G,E,_)=>{_.d(E,{D:()=>c,H8:()=>m,_0:()=>g,fG:()=>p,gm:()=>L,nr:()=>x,y3:()=>l});var x=function(){return navigator.userAgent.indexOf("Safari")>-1&&navigator.userAgent.indexOf("Chrome")===-1},L=function(){return navigator.userAgent.toLowerCase().indexOf("firefox")>-1},c=function(){try{return typeof localStorage!="undefined"}catch(d){return!1}},l=function(){return navigator.userAgent.indexOf("iPhone")>-1?"touchstart":"click"},g=function(d){return navigator.platform.toUpperCase().indexOf("MAC")>=0?!!(d.metaKey&&!d.ctrlKey):!!(!d.metaKey&&d.ctrlKey)},p=function(d){return/Mac/.test(navigator.platform)||navigator.platform==="iPhone"?d.indexOf("⇧")>-1&&L()&&(d=d.replace(";",":").replace("=","+").replace("-","_")):(d.startsWith("⌘")?d=d.replace("⌘","⌘+"):d.startsWith("⌥")&&d.substr(1,1)!=="⌘"?d=d.replace("⌥","⌥+"):d=d.replace("⇧⌘","⌘+⇧+").replace("⌥⌘","⌥+⌘+"),d=d.replace("⌘","Ctrl").replace("⇧","Shift").replace("⌥","Alt"),d.indexOf("Shift")>-1&&(d=d.replace(";",":").replace("=","+").replace("-","_"))),d},m=function(){return/Chrome/.test(navigator.userAgent)&&/Google Inc/.test(navigator.vendor)}},933:(G,E,_)=>{_.d(E,{Ee:()=>x,TK:()=>L,kY:()=>c});var x=function(){return([1e7].toString()+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,function(l){return(parseInt(l,10)^window.crypto.getRandomValues(new Uint32Array(1))[0]&15>>parseInt(l,10)/4).toString(16)})},L=function(l,g){g===void 0&&(g=window.location.search);var p=g.substring(g.indexOf("?")),m=p.indexOf("#"),d=new URLSearchParams(p.substring(0,m>=0?m:void 0));return d.get(l)},c=function(l){return Function('"use strict";return ('.concat(l,")"))()}},598:(G,E,_)=>{_.d(E,{Ab:()=>c,KJ:()=>d,KR:()=>l,Rp:()=>v,Th:()=>g,XW:()=>L,_Y:()=>m,pt:()=>p});var x=_(164),L=function(w,b){for(var y=d(w,b),s=!1,T=!1;y&&!y.classList.contains("vditor-reset")&&!T;)s=d(y.parentElement,b),s?y=s:T=!0;return y||!1},c=function(w,b){for(var y=(0,x.f)(w,b),s=!1,T=!1;y&&!y.classList.contains("vditor-reset")&&!T;)s=(0,x.f)(y.parentElement,b),s?y=s:T=!0;return y||!1},l=function(w){var b=c(w,"UL"),y=c(w,"OL"),s=b;return y&&(!b||b&&y.contains(b))&&(s=y),s},g=function(w,b,y){if(!w)return!1;w.nodeType===3&&(w=w.parentElement);for(var s=w,T=!1;s&&!T&&!s.classList.contains("vditor-reset");)s.getAttribute(b)===y?T=!0:s=s.parentElement;return T&&s},p=function(w){if(!w)return!1;w.nodeType===3&&(w=w.parentElement);var b=w,y=!1,s=g(w,"data-block","0");if(s)return s;for(;b&&!y&&!b.classList.contains("vditor-reset");)b.tagName==="H1"||b.tagName==="H2"||b.tagName==="H3"||b.tagName==="H4"||b.tagName==="H5"||b.tagName==="H6"||b.tagName==="P"||b.tagName==="BLOCKQUOTE"||b.tagName==="OL"||b.tagName==="UL"?y=!0:b=b.parentElement;return y&&b},m=function(w,b){if(!w)return!1;w.nodeType===3&&(w=w.parentElement);for(var y=w,s=!1;y&&!s&&!y.classList.contains("vditor-reset");)y.nodeName===b?s=!0:y=y.parentElement;return s&&y},d=function(w,b){if(!w)return!1;w.nodeType===3&&(w=w.parentElement);for(var y=w,s=!1;y&&!s&&!y.classList.contains("vditor-reset");)y.classList.contains(b)?s=!0:y=y.parentElement;return s&&y},v=function(w){for(;w&&w.lastChild;)w=w.lastChild;return w}},164:(G,E,_)=>{_.d(E,{c:()=>L,f:()=>x});var x=function(c,l){if(!c)return!1;c.nodeType===3&&(c=c.parentElement);for(var g=c,p=!1;g&&!p&&!g.classList.contains("vditor-reset");)g.nodeName.indexOf(l)===0?p=!0:g=g.parentElement;return p&&g},L=function(c){var l=x(c,"H");return l&&l.tagName.length===2&&l.tagName!=="HR"?l:!1}},905:(G,E,_)=>{_.d(E,{h:()=>x});var x=function(){for(var L=[],c=0;c<arguments.length;c++)L[c]=arguments[c];for(var l={},g=function(m){for(var d in m)m.hasOwnProperty(d)&&(Object.prototype.toString.call(m[d])==="[object Object]"?l[d]=x(l[d],m[d]):l[d]=m[d])},p=0;p<L.length;p++)g(L[p]);return l}},827:(G,E,_)=>{_.d(E,{Co:()=>p,ED:()=>d,Ey:()=>g,Fm:()=>v,RN:()=>l,Z2:()=>b,ir:()=>w,jl:()=>m});var x=_(913),L=_(629),c=_(598),l=function(y){var s,T=y[y.currentMode].element;return getSelection().rangeCount>0&&(s=getSelection().getRangeAt(0),T.isEqualNode(s.startContainer)||T.contains(s.startContainer))?s:y[y.currentMode].range?y[y.currentMode].range:(T.focus(),s=T.ownerDocument.createRange(),s.setStart(T,0),s.collapse(!0),s)},g=function(y){var s=window.getSelection().getRangeAt(0);if(!y.contains(s.startContainer)&&!(0,c.KJ)(s.startContainer,"vditor-panel--none"))return{left:0,top:0};var T=y.parentElement.getBoundingClientRect(),A;if(s.getClientRects().length===0)if(s.startContainer.nodeType===3){var D=s.startContainer.parentElement;if(D&&D.getClientRects().length>0)A=D.getClientRects()[0];else return{left:0,top:0}}else{var C=s.startContainer.children;if(C[s.startOffset]&&C[s.startOffset].getClientRects().length>0)A=C[s.startOffset].getClientRects()[0];else if(s.startContainer.childNodes.length>0){var j=s.cloneRange();s.selectNode(s.startContainer.childNodes[Math.max(0,s.startOffset-1)]),A=s.getClientRects()[0],s.setEnd(j.endContainer,j.endOffset),s.setStart(j.startContainer,j.startOffset)}else A=s.startContainer.getClientRects()[0];if(!A){for(var V=s.startContainer.childNodes[s.startOffset];!V.getClientRects||V.getClientRects&&V.getClientRects().length===0;)V=V.parentElement;A=V.getClientRects()[0]}}else A=s.getClientRects()[0];return{left:A.left-T.left,top:A.top-T.top}},p=function(y,s){if(!s){if(getSelection().rangeCount===0)return!1;s=getSelection().getRangeAt(0)}var T=s.commonAncestorContainer;return y.isEqualNode(T)||y.contains(T)},m=function(y){var s=window.getSelection();s.removeAllRanges(),s.addRange(y)},d=function(y,s,T){var A={end:0,start:0};if(!T){if(getSelection().rangeCount===0)return A;T=window.getSelection().getRangeAt(0)}if(p(s,T)){var D=T.cloneRange();y.childNodes[0]&&y.childNodes[0].childNodes[0]?D.setStart(y.childNodes[0].childNodes[0],0):D.selectNodeContents(y),D.setEnd(T.startContainer,T.startOffset),A.start=D.toString().length,A.end=A.start+T.toString().length}return A},v=function(y,s,T){var A=0,D=0,C=T.childNodes[D],j=!1,V=!1;y=Math.max(0,y),s=Math.max(0,s);var q=T.ownerDocument.createRange();for(q.setStart(C||T,0),q.collapse(!0);!V&&C;){var Q=A+C.textContent.length;if(!j&&y>=A&&y<=Q&&(y===0?q.setStart(C,0):C.childNodes[0].nodeType===3?q.setStart(C.childNodes[0],y-A):C.nextSibling?q.setStartBefore(C.nextSibling):q.setStartAfter(C),j=!0,y===s)){V=!0;break}j&&s>=A&&s<=Q&&(s===0?q.setEnd(C,0):C.childNodes[0].nodeType===3?q.setEnd(C.childNodes[0],s-A):C.nextSibling?q.setEndBefore(C.nextSibling):q.setEndAfter(C),V=!0),A=Q,C=T.childNodes[++D]}return!V&&T.childNodes[D-1]&&q.setStartBefore(T.childNodes[D-1]),m(q),q},w=function(y,s){var T=y.querySelector("wbr");if(T){if(!T.previousElementSibling)T.previousSibling?s.setStart(T.previousSibling,T.previousSibling.textContent.length):T.nextSibling?T.nextSibling.nodeType===3?s.setStart(T.nextSibling,0):s.setStartBefore(T.nextSibling):s.setStart(T.parentElement,0);else if(T.previousElementSibling.isSameNode(T.previousSibling))if(T.previousElementSibling.lastChild){s.setStartBefore(T),s.collapse(!0),m(s),(0,L.H8)()&&(T.previousElementSibling.tagName==="EM"||T.previousElementSibling.tagName==="STRONG"||T.previousElementSibling.tagName==="S")&&(s.insertNode(document.createTextNode(x.Y.ZWSP)),s.collapse(!1)),T.remove();return}else s.setStartAfter(T.previousElementSibling);else s.setStart(T.previousSibling,T.previousSibling.textContent.length);s.collapse(!0),T.remove(),m(s)}},b=function(y,s){var T=document.createElement("div");T.innerHTML=y;var A=T.querySelectorAll("p");A.length===1&&!A[0].previousSibling&&!A[0].nextSibling&&s[s.currentMode].element.children.length>0&&T.firstElementChild.tagName==="P"&&(y=A[0].innerHTML.trim());var D=document.createElement("div");D.innerHTML=y;var C=l(s);if(C.toString()!==""&&(s[s.currentMode].preventInput=!0,document.execCommand("delete",!1,"")),D.firstElementChild&&D.firstElementChild.getAttribute("data-block")==="0"){D.lastElementChild.insertAdjacentHTML("beforeend","<wbr>");var j=(0,c.pt)(C.startContainer);j?j.insertAdjacentHTML("afterend",D.innerHTML):s[s.currentMode].element.insertAdjacentHTML("beforeend",D.innerHTML),w(s[s.currentMode].element,C)}else{var V=document.createElement("template");V.innerHTML=y,C.insertNode(V.content.cloneNode(!0)),C.collapse(!1),m(C)}}}},Je={};function X(G){var E=Je[G];if(E!==void 0)return E.exports;var _=Je[G]={exports:{}};return we[G](_,_.exports,X),_.exports}X.d=(G,E)=>{for(var _ in E)X.o(E,_)&&!X.o(G,_)&&Object.defineProperty(G,_,{enumerable:!0,get:E[_]})},X.o=(G,E)=>Object.prototype.hasOwnProperty.call(G,E),X.r=G=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(G,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(G,"__esModule",{value:!0})};var ke={};return(()=>{X.d(ke,{default:()=>xi});var G=X(923),E=X(913),_=X(695),x=function(e){return e.currentMode==="sv"?(0,_.p)("".concat(e.sv.element.textContent,`
`).replace(/\n\n$/,`
`)):e.currentMode==="wysiwyg"?e.lute.VditorDOM2Md(e.wysiwyg.element.innerHTML):e.currentMode==="ir"?e.lute.VditorIRDOM2Md(e.ir.element.innerHTML):""},L=X(161),c=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-devtools",this.element.innerHTML='<div class="vditor-reset--error"></div><div style="height: 100%;"></div>'}return e.prototype.renderEchart=function(t){var n=this;t.devtools.element.style.display==="block"&&(0,L.Z)("".concat(t.options.cdn,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){n.ASTChart||(n.ASTChart=echarts.init(t.devtools.element.lastElementChild));try{n.element.lastElementChild.style.display="block",n.element.firstElementChild.innerHTML="",n.ASTChart.setOption({series:[{data:JSON.parse(t.lute.RenderEChartsJSON(x(t))),initialTreeDepth:-1,label:{align:"left",backgroundColor:"rgba(68, 77, 86, .68)",borderRadius:3,color:"#d1d5da",fontSize:12,lineHeight:12,offset:[9,12],padding:[2,4,2,4],position:"top",verticalAlign:"middle"},lineStyle:{color:"#4285f4",type:"curve",width:1},orient:"vertical",roam:!0,type:"tree"}],toolbox:{bottom:25,emphasis:{iconStyle:{color:"#4285f4"}},feature:{restore:{show:!0},saveAsImage:{show:!0}},right:15,show:!0}}),n.ASTChart.resize()}catch(r){n.element.lastElementChild.style.display="none",n.element.firstElementChild.innerHTML=r}})},e}(),l=X(629),g=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&r.classList.contains("vditor-menu--current")&&r.classList.remove("vditor-menu--current")}})},p=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&!r.classList.contains("vditor-menu--current")&&r.classList.add("vditor-menu--current")}})},m=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&r.classList.contains(E.Y.CLASS_MENU_DISABLED)&&r.classList.remove(E.Y.CLASS_MENU_DISABLED)}})},d=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&!r.classList.contains(E.Y.CLASS_MENU_DISABLED)&&r.classList.add(E.Y.CLASS_MENU_DISABLED)}})},v=function(e,t){t.forEach(function(n){e[n]&&e[n]&&(e[n].style.display="none")})},w=function(e,t){t.forEach(function(n){e[n]&&e[n]&&(e[n].style.display="block")})},b=function(e,t,n){t.includes("subToolbar")&&(e.toolbar.element.querySelectorAll(".vditor-hint").forEach(function(r){n&&r.isEqualNode(n)||(r.style.display="none")}),e.toolbar.elements.emoji&&(e.toolbar.elements.emoji.lastElementChild.style.display="none")),t.includes("hint")&&(e.hint.element.style.display="none"),e.wysiwyg.popover&&t.includes("popover")&&(e.wysiwyg.popover.style.display="none")},y=function(e,t,n,r){n.addEventListener((0,l.y3)(),function(i){i.preventDefault(),i.stopPropagation(),!n.classList.contains(E.Y.CLASS_MENU_DISABLED)&&(e.toolbar.element.querySelectorAll(".vditor-hint--current").forEach(function(a){a.classList.remove("vditor-hint--current")}),t.style.display==="block"?t.style.display="none":(b(e,["subToolbar","hint","popover"],n.parentElement.parentElement),n.classList.contains("vditor-tooltipped")||n.classList.add("vditor-hint--current"),t.style.display="block",e.toolbar.element.getBoundingClientRect().right-n.getBoundingClientRect().right<250?t.classList.add("vditor-panel--left"):t.classList.remove("vditor-panel--left")))})},s=X(598),T=X(164),A=function(e,t,n,r){r&&console.log("".concat(e," - ").concat(n,": ").concat(t))},D=X(288),C=X(784),j=X(51),V=X(500),q=X(339),Q=X(108),ne=X(960),oe=X(975),me=X(597),fe=X(162),le=X(591),ve=X(931),Le=function(e,t,n){n===void 0&&(n="sv");var r=document.createElement("div");r.innerHTML=e;var i=!1;r.childElementCount===1&&r.lastElementChild.style.fontFamily.indexOf("monospace")>-1&&(i=!0);var a=r.querySelectorAll("pre");if(r.childElementCount===1&&a.length===1&&a[0].className!=="vditor-wysiwyg"&&a[0].className!=="vditor-sv"&&(i=!0),e.indexOf(`
<p class="p1">`)===0&&(i=!0),r.childElementCount===1&&r.firstElementChild.tagName==="TABLE"&&r.querySelector(".line-number")&&r.querySelector(".line-content")&&(i=!0),i){var o=t||e;return/\n/.test(o)||a.length===1?n==="wysiwyg"?'<div class="vditor-wysiwyg__block" data-block="0" data-type="code-block"><pre><code>'.concat(o.replace(/&/g,"&amp;").replace(/</g,"&lt;"),"<wbr></code></pre></div>"):"\n```\n"+o.replace(/&/g,"&amp;").replace(/</g,"&lt;")+"\n```":n==="wysiwyg"?"<code>".concat(o.replace(/&/g,"&amp;").replace(/</g,"&lt;"),"</code><wbr>"):"`".concat(o,"`")}return!1},Ee=function(e,t){if(e){if(e.parentElement.getAttribute("data-type")==="html-block"){e.setAttribute("data-render","1");return}var n=e.firstElementChild.className.replace("language-","");if(n==="abc")(0,D.$)(e,t.options.cdn);else if(n==="mermaid")(0,oe.e)(e,t.options.cdn,t.options.theme);else if(n==="smiles")(0,ve.Y)(e,t.options.cdn,t.options.theme);else if(n==="markmap")(0,me.K)(e,t.options.cdn);else if(n==="flowchart")(0,V.D)(e,t.options.cdn);else if(n==="echarts")(0,C.v)(e,t.options.cdn,t.options.theme);else if(n==="mindmap")(0,fe.l)(e,t.options.cdn,t.options.theme);else if(n==="plantuml")(0,le.M)(e,t.options.cdn);else if(n==="graphviz")(0,q.m)(e,t.options.cdn);else if(n==="math")(0,ne.T)(e,{cdn:t.options.cdn,math:t.options.preview.math});else{var r=t.options.customRenders.find(function(i){if(i.language===n)return i.render(e,t),!0});r||((0,Q.$)(Object.assign({},t.options.preview.hljs),e,t.options.cdn),(0,j.o)(e,t.options.preview.hljs))}e.setAttribute("data-render","1")}},M=X(827),Be=function(e){if(e.currentMode!=="sv"){var t=e[e.currentMode].element,n=e.outline.render(e);n===""&&(n="[ToC]"),t.querySelectorAll('[data-type="toc-block"]').forEach(function(r){r.innerHTML=n,(0,ne.T)(r,{cdn:e.options.cdn,math:e.options.preview.math})})}},pt=function(e,t){var n=(0,s._Y)(e.target,"SPAN");if(n&&(0,s.KJ)(n,"vditor-toc")){var r=t[t.currentMode].element.querySelector("#"+n.getAttribute("data-target-id"));if(r)if(t.options.height==="auto"){var i=r.offsetTop+t.element.offsetTop;t.options.toolbarConfig.pin||(i+=t.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,i)}else t.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,t.element.offsetTop),t[t.currentMode].element.scrollTop=r.offsetTop;return}},ht=function(e,t,n,r){if(e.previousElementSibling&&e.previousElementSibling.classList.contains("vditor-toc")){if(n.key==="Backspace"&&(0,M.ED)(e,t[t.currentMode].element,r).start===0)return e.previousElementSibling.remove(),ee(t),!0;if(et(t,n,r,e,e.previousElementSibling))return!0}if(e.nextElementSibling&&e.nextElementSibling.classList.contains("vditor-toc")){if(n.key==="Delete"&&(0,M.ED)(e,t[t.currentMode].element,r).start>=e.textContent.trimRight().length)return e.nextElementSibling.remove(),ee(t),!0;if(ct(t,n,r,e,e.nextElementSibling))return!0}if(n.key==="Backspace"||n.key==="Delete"){var i=(0,s.KJ)(r.startContainer,"vditor-toc");if(i)return i.remove(),ee(t),!0}},Fe=function(e,t,n,r){n===void 0&&(n=!1);var i=(0,s.pt)(t.startContainer);if(i&&!n&&i.getAttribute("data-type")!=="code-block"){if(Ut(i.innerHTML)&&i.previousElementSibling||Vt(i.innerHTML))return;for(var a=(0,M.ED)(i,e.ir.element,t).start,o=!0,u=a-1;u>i.textContent.substr(0,a).lastIndexOf(`
`);u--)if(i.textContent.charAt(u)!==" "&&i.textContent.charAt(u)!=="	"){o=!1;break}a===0&&(o=!1);for(var h=!0,u=a-1;u<i.textContent.length;u++)if(i.textContent.charAt(u)!==" "&&i.textContent.charAt(u)!==`
`){h=!1;break}if(o){typeof e.options.input=="function"&&e.options.input(x(e));return}if(h&&/^#{1,6} $/.test(i.textContent)&&(h=!1),h){var f=(0,s.KJ)(t.startContainer,"vditor-ir__marker");if(!f){var S=t.startContainer.previousSibling;S&&S.nodeType!==3&&S.classList.contains("vditor-ir__node--expand")&&S.classList.remove("vditor-ir__node--expand"),typeof e.options.input=="function"&&e.options.input(x(e));return}}}if(e.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach(function($){$.classList.remove("vditor-ir__node--expand")}),i||(i=e.ir.element),!i.querySelector("wbr")){var k=(0,s.KJ)(t.startContainer,"vditor-ir__preview");k?k.previousElementSibling.insertAdjacentHTML("beforeend","<wbr>"):t.insertNode(document.createElement("wbr"))}i.querySelectorAll("[style]").forEach(function($){$.removeAttribute("style")}),i.getAttribute("data-type")==="link-ref-defs-block"&&(i=e.ir.element);var I=i.isEqualNode(e.ir.element),N=(0,s.Th)(i,"data-type","footnotes-block"),H="";if(I)H=i.innerHTML;else{var F=(0,T.f)(t.startContainer,"BLOCKQUOTE"),B=(0,s.KR)(t.startContainer);if(B&&(i=B),F&&(!B||B&&!F.contains(B))&&(i=F),N&&(i=N),H=i.outerHTML,i.tagName==="UL"||i.tagName==="OL"){var O=i.previousElementSibling,z=i.nextElementSibling;O&&(O.tagName==="UL"||O.tagName==="OL")&&(H=O.outerHTML+H,O.remove()),z&&(z.tagName==="UL"||z.tagName==="OL")&&(H=H+z.outerHTML,z.remove()),H=H.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}else i.previousElementSibling&&i.previousElementSibling.textContent.replace(E.Y.ZWSP,"")!==""&&r&&r.inputType==="insertParagraph"&&(H=i.previousElementSibling.outerHTML+H,i.previousElementSibling.remove());i.innerText.startsWith("```")||(e.ir.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function($){$&&!i.isEqualNode($)&&(H+=$.outerHTML,$.remove())}),e.ir.element.querySelectorAll("[data-type='footnotes-block']").forEach(function($){$&&!i.isEqualNode($)&&(H+=$.outerHTML,$.remove())}))}if(A("SpinVditorIRDOM",H,"argument",e.options.debugger),H=e.lute.SpinVditorIRDOM(H),A("SpinVditorIRDOM",H,"result",e.options.debugger),I)i.innerHTML=H;else if(i.outerHTML=H,N){var K=(0,s.Th)(e.ir.element.querySelector("wbr"),"data-type","footnotes-def");if(K){var Z=K.textContent,se=Z.substring(1,Z.indexOf("]:")),ae=e.ir.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'.concat(se,'"]'));ae&&ae.setAttribute("aria-label",Z.substr(se.length+3).trim().substr(0,24))}}var ue,xe=e.ir.element.querySelectorAll("[data-type='link-ref-defs-block']");xe.forEach(function($,be){be===0?ue=$:(ue.insertAdjacentHTML("beforeend",$.innerHTML),$.remove())}),xe.length>0&&e.ir.element.insertAdjacentElement("beforeend",xe[0]);var Ce,De=e.ir.element.querySelectorAll("[data-type='footnotes-block']");De.forEach(function($,be){be===0?Ce=$:(Ce.insertAdjacentHTML("beforeend",$.innerHTML),$.remove())}),De.length>0&&e.ir.element.insertAdjacentElement("beforeend",De[0]),(0,M.ir)(e.ir.element,t),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function($){Ee($,e)}),Be(e),Ke(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},R=function(e,t){if(e==="")return!1;if(e.indexOf("⇧")===-1&&e.indexOf("⌘")===-1&&e.indexOf("⌥")===-1)return!(0,l._0)(t)&&!t.altKey&&!t.shiftKey&&t.code===e;if(e==="⇧Tab")return!!(!(0,l._0)(t)&&!t.altKey&&t.shiftKey&&t.code==="Tab");var n=e.split("");if(e.startsWith("⌥")){var r=n.length===3?n[2]:n[1];return!!((n.length===3?(0,l._0)(t):!(0,l._0)(t))&&t.altKey&&!t.shiftKey&&t.code===(/^[0-9]$/.test(r)?"Digit":"Key")+r)}e==="⌘Enter"&&(n=["⌘","Enter"]);var i=n.length>2&&n[0]==="⇧",a=i?n[2]:n[1];return i&&((0,l.gm)()||!/Mac/.test(navigator.platform))&&(a==="-"?a="_":a==="="&&(a="+")),!!((0,l._0)(t)&&t.key.toLowerCase()===a.toLowerCase()&&!t.altKey&&(!i&&!t.shiftKey||i&&t.shiftKey))},re=function(e){var t=e.startContainer;if(t.nodeType===3&&t.nodeValue.length!==e.startOffset)return!1;for(var n=t.nextSibling;n&&n.textContent==="";)n=n.nextSibling;if(n){if(n&&n.nodeType!==3&&n.classList.contains("vditor-ir__node")&&!n.getAttribute("data-block"))return n}else{var r=(0,s.KJ)(t,"vditor-ir__marker");if(r&&!r.nextSibling){var i=t.parentElement.parentElement.nextSibling;if(i&&i.nodeType!==3&&i.classList.contains("vditor-ir__node"))return i}return!1}return!1},U=function(e){var t=e.startContainer,n=t.previousSibling;return t.nodeType===3&&e.startOffset===0&&n&&n.nodeType!==3&&n.classList.contains("vditor-ir__node")&&!n.getAttribute("data-block")?n:!1},P=function(e,t){t.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach(function(o){o.classList.remove("vditor-ir__node--expand")});var n=(0,s.XW)(e.startContainer,"vditor-ir__node"),r=!e.collapsed&&(0,s.XW)(e.endContainer,"vditor-ir__node");if(!(!e.collapsed&&(!n||n!==r))){n&&(n.classList.add("vditor-ir__node--expand"),n.classList.remove("vditor-ir__node--hidden"),(0,M.jl)(e));var i=re(e);if(i){i.classList.add("vditor-ir__node--expand"),i.classList.remove("vditor-ir__node--hidden");return}var a=U(e);if(a){a.classList.add("vditor-ir__node--expand"),a.classList.remove("vditor-ir__node--hidden");return}}},pe=function(e,t){if(e.ir.composingLock=t.isComposing,t.isComposing)return!1;t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t);var n=(0,M.RN)(e),r=n.startContainer;if(!on(t,e,r)||(sn(n,e,t),Sn(n),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,l._0)(t)&&t.key!=="Escape"&&t.key!=="Delete"))return!1;var i=(0,s.Th)(r,"data-newline","1");if(!(0,l._0)(t)&&!t.altKey&&!t.shiftKey&&t.key==="Enter"&&i&&n.startOffset<i.textContent.length){var a=i.previousElementSibling;a&&(n.insertNode(document.createTextNode(a.textContent)),n.collapse(!1));var o=i.nextSibling;o&&(n.insertNode(document.createTextNode(o.textContent)),n.collapse(!0))}var u=(0,s._Y)(r,"P");if(dn(t,e,u,n)||fn(n,e,u,t)||wn(e,n,t,u))return!0;var h=(0,s.KJ)(r,"vditor-ir__marker--pre");if(h&&h.tagName==="PRE"){var f=h.firstChild;if(bn(e,t,h,n)||(f.getAttribute("data-type")==="math-block"||f.getAttribute("data-type")==="html-block")&&et(e,t,n,f,h.parentElement)||ct(e,t,n,f,h.parentElement))return!0}var S=(0,s.Th)(r,"data-type","code-block-info");if(S){if(t.key==="Enter"||t.key==="Tab")return n.selectNodeContents(S.nextElementSibling.firstChild),n.collapse(!0),t.preventDefault(),b(e,["hint"]),!0;if(t.key==="Backspace"){var k=(0,M.ED)(S,e.ir.element).start;k===1&&n.setStart(r,0),k===2&&(e.hint.recentLanguage="")}if(et(e,t,n,S,S.parentElement))return b(e,["hint"]),!0}var I=(0,s._Y)(r,"TD")||(0,s._Y)(r,"TH");if(t.key.indexOf("Arrow")>-1&&I){var N=hr(I);if(N&&et(e,t,n,I,N))return!0;var H=mr(I);if(H&&ct(e,t,n,I,H))return!0}if(yn(e,t,n)||vn(e,n,t)||Kt(e,n,t))return!0;var F=(0,T.c)(r);if(F){if(R("⌘=",t)){var B=F.querySelector(".vditor-ir__marker--heading");return B&&B.textContent.trim().length>1&&ut(e,B.textContent.substr(1)),t.preventDefault(),!0}if(R("⌘-",t)){var B=F.querySelector(".vditor-ir__marker--heading");return B&&B.textContent.trim().length<6&&ut(e,B.textContent.trim()+"# "),t.preventDefault(),!0}}var O=(0,s.pt)(r);if(t.key==="Backspace"&&!(0,l._0)(t)&&!t.shiftKey&&!t.altKey&&n.toString()===""){if(En(e,n,t,u))return!0;if(O&&O.previousElementSibling&&O.tagName!=="UL"&&O.tagName!=="OL"&&(O.previousElementSibling.getAttribute("data-type")==="code-block"||O.previousElementSibling.getAttribute("data-type")==="math-block")){var z=(0,M.ED)(O,e.ir.element,n).start;if(z===0||z===1&&O.innerText.startsWith(E.Y.ZWSP))return n.selectNodeContents(O.previousElementSibling.querySelector(".vditor-ir__marker--pre code")),n.collapse(!1),P(n,e),O.textContent.trim().replace(E.Y.ZWSP,"")===""&&(O.remove(),Ke(e)),t.preventDefault(),!0}if(F){var K=F.firstElementChild.textContent.length;(0,M.ED)(F,e.ir.element).start===K&&K!==0&&(n.setStart(F.firstElementChild.firstChild,K-1),n.collapse(!0),(0,M.jl)(n))}}return(t.key==="ArrowUp"||t.key==="ArrowDown")&&O&&(O.querySelectorAll(".vditor-ir__node").forEach(function(Z){Z.contains(r)||Z.classList.add("vditor-ir__node--hidden")}),Tn(t,O,n))?!0:(ln(n,t.key),O&&ht(O,e,t,n)?(t.preventDefault(),!0):!1)},W=X(726),ge=function(e,t){e.querySelectorAll("[data-type=footnotes-link]").forEach(function(n){for(var r=n.parentElement,i=r.nextSibling;i&&i.textContent.startsWith("    ");){var a=i;a.childNodes.forEach(function(o){r.append(o.cloneNode(!0))}),i=i.nextSibling,a.remove()}t&&t(r)})},ie=function(e,t){var n,r=getSelection().getRangeAt(0).cloneRange(),i=r.startContainer;r.startContainer.nodeType!==3&&r.startContainer.tagName==="DIV"&&(i=r.startContainer.childNodes[r.startOffset-1]);var a=(0,s.Th)(i,"data-block","0");if(a&&t&&(t.inputType==="deleteContentBackward"||t.data===" ")){for(var o=(0,M.ED)(a,e.sv.element,r).start,u=!0,h=o-1;h>a.textContent.substr(0,o).lastIndexOf(`
`);h--)if(a.textContent.charAt(h)!==" "&&a.textContent.charAt(h)!=="	"){u=!1;break}if(o===0&&(u=!1),u){Se(e);return}if(t.inputType==="deleteContentBackward"){var f=(0,s.Th)(i,"data-type","code-block-open-marker")||(0,s.Th)(i,"data-type","code-block-close-marker");if(f){if(f.getAttribute("data-type")==="code-block-close-marker"){var S=yt(i,"code-block-open-marker");if(S){S.textContent=f.textContent,Se(e);return}}if(f.getAttribute("data-type")==="code-block-open-marker"){var S=yt(i,"code-block-close-marker",!1);if(S){S.textContent=f.textContent,Se(e);return}}}var k=(0,s.Th)(i,"data-type","math-block-open-marker");if(k){var I=k.nextElementSibling.nextElementSibling;I&&I.getAttribute("data-type")==="math-block-close-marker"&&(I.remove(),Se(e));return}a.querySelectorAll('[data-type="code-block-open-marker"]').forEach(function(O){O.textContent.length===1&&O.remove()}),a.querySelectorAll('[data-type="code-block-close-marker"]').forEach(function(O){O.textContent.length===1&&O.remove()});var N=(0,s.Th)(i,"data-type","heading-marker");if(N&&N.textContent.indexOf("#")===-1){Se(e);return}}if((t.data===" "||t.inputType==="deleteContentBackward")&&((0,s.Th)(i,"data-type","padding")||(0,s.Th)(i,"data-type","li-marker")||(0,s.Th)(i,"data-type","task-marker")||(0,s.Th)(i,"data-type","blockquote-marker"))){Se(e);return}}if(a&&a.textContent.trimRight()==="$$"){Se(e);return}a||(a=e.sv.element),((n=a.firstElementChild)===null||n===void 0?void 0:n.getAttribute("data-type"))==="link-ref-defs-block"&&(a=e.sv.element),(0,s.Th)(i,"data-type","footnotes-link")&&(a=e.sv.element),a.textContent.indexOf(Lute.Caret)===-1&&r.insertNode(document.createTextNode(Lute.Caret)),a.querySelectorAll("[style]").forEach(function(O){O.removeAttribute("style")}),a.querySelectorAll("font").forEach(function(O){O.outerHTML=O.innerHTML});var H=a.textContent,F=a.isEqualNode(e.sv.element);if(F)H=a.textContent;else{a.previousElementSibling&&(H=a.previousElementSibling.textContent+H,a.previousElementSibling.remove()),a.previousElementSibling&&H.indexOf(`---
`)===0&&(H=a.previousElementSibling.textContent+H,a.previousElementSibling.remove());var B="";e.sv.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(O,z){O&&!a.isEqualNode(O.parentElement)&&(B+=O.parentElement.textContent+`
`,O.parentElement.remove())}),e.sv.element.querySelectorAll("[data-type='footnotes-link']").forEach(function(O,z){O&&!a.isEqualNode(O.parentElement)&&(B+=O.parentElement.textContent+`
`,O.parentElement.remove())}),H=B+H}H=tn(H,e),F?a.innerHTML=H:a.outerHTML=H,e.sv.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(O){e.sv.element.insertAdjacentElement("beforeend",O.parentElement)}),ge(e.sv.element,function(O){e.sv.element.insertAdjacentElement("beforeend",O)}),(0,M.ir)(e.sv.element,r),ye(e),Se(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},_e=function(e,t){var n,r,i,a,o;if(e.sv.composingLock=t.isComposing,t.isComposing||(t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,l._0)(t)&&t.key!=="Escape"))return!1;var u=(0,M.RN)(e),h=u.startContainer;u.startContainer.nodeType!==3&&u.startContainer.tagName==="DIV"&&(h=u.startContainer.childNodes[u.startOffset-1]);var f=(0,s.Th)(h,"data-type","text"),S=(0,s.Th)(h,"data-type","blockquote-marker");if(!S&&u.startOffset===0&&f&&f.previousElementSibling&&f.previousElementSibling.getAttribute("data-type")==="blockquote-marker"&&(S=f.previousElementSibling),S&&t.key==="Enter"&&!(0,l._0)(t)&&!t.altKey&&S.nextElementSibling.textContent.trim()===""&&(0,M.ED)(S,e.sv.element,u).start===S.textContent.length)return((n=S.previousElementSibling)===null||n===void 0?void 0:n.getAttribute("data-type"))==="padding"&&S.previousElementSibling.setAttribute("data-action","enter-remove"),S.remove(),Se(e),t.preventDefault(),!0;var k=(0,s.Th)(h,"data-type","li-marker"),I=(0,s.Th)(h,"data-type","task-marker"),N=k;if(N||I&&I.nextElementSibling.getAttribute("data-type")!=="task-marker"&&(N=I),!N&&u.startOffset===0&&f&&f.previousElementSibling&&(f.previousElementSibling.getAttribute("data-type")==="li-marker"||f.previousElementSibling.getAttribute("data-type")==="task-marker")&&(N=f.previousElementSibling),N){var H=(0,M.ED)(N,e.sv.element,u).start,F=N.getAttribute("data-type")==="task-marker",B=N;if(F&&(B=N.previousElementSibling.previousElementSibling.previousElementSibling),H===N.textContent.length){if(t.key==="Enter"&&!(0,l._0)(t)&&!t.altKey&&!t.shiftKey&&N.nextElementSibling.textContent.trim()==="")return((r=B.previousElementSibling)===null||r===void 0?void 0:r.getAttribute("data-type"))==="padding"?(B.previousElementSibling.remove(),ie(e)):(F&&(B.remove(),N.previousElementSibling.previousElementSibling.remove(),N.previousElementSibling.remove()),N.nextElementSibling.remove(),N.remove(),Se(e)),t.preventDefault(),!0;if(t.key==="Tab")return t.shiftKey?B.previousElementSibling.getAttribute("data-type")==="padding"&&B.previousElementSibling.remove():B.insertAdjacentHTML("beforebegin",'<span data-type="padding">'.concat(B.textContent.replace(/\S/g," "),"</span>")),/^\d/.test(B.textContent)&&(B.textContent=B.textContent.replace(/^\d{1,}/,"1"),u.selectNodeContents(N.firstChild),u.collapse(!1)),ie(e),t.preventDefault(),!0}}if(Kt(e,u,t))return!0;var O=(0,s.Th)(h,"data-block","0"),z=(0,T.f)(h,"SPAN");if(t.key==="Enter"&&!(0,l._0)(t)&&!t.altKey&&!t.shiftKey&&O){var K=!1,Z=O.textContent.match(/^\n+/);(0,M.ED)(O,e.sv.element).start<=(Z?Z[0].length:0)&&(K=!0);var se=`
`;if(z){if(((i=z.previousElementSibling)===null||i===void 0?void 0:i.getAttribute("data-action"))==="enter-remove")return z.previousElementSibling.remove(),Se(e),t.preventDefault(),!0;se+=ir(z)}return u.insertNode(document.createTextNode(se)),u.collapse(!1),O&&O.textContent.trim()!==""&&!K?ie(e):Se(e),t.preventDefault(),!0}if(t.key==="Backspace"&&!(0,l._0)(t)&&!t.altKey&&!t.shiftKey){if(z&&((a=z.previousElementSibling)===null||a===void 0?void 0:a.getAttribute("data-type"))==="newline"&&(0,M.ED)(z,e.sv.element,u).start===1&&z.getAttribute("data-type").indexOf("code-block-")===-1)return u.setStart(z,0),u.extractContents(),z.textContent.trim()!==""?ie(e):Se(e),t.preventDefault(),!0;if(O&&(0,M.ED)(O,e.sv.element,u).start===0&&O.previousElementSibling){u.extractContents();var ae=O.previousElementSibling.lastElementChild;return ae.getAttribute("data-type")==="newline"&&(ae.remove(),ae=O.previousElementSibling.lastElementChild),ae.getAttribute("data-type")!=="newline"&&(ae.insertAdjacentHTML("afterend",O.innerHTML),O.remove()),O.textContent.trim()!==""&&!(!((o=O.previousElementSibling)===null||o===void 0)&&o.querySelector('[data-type="code-block-open-marker"]'))?ie(e):(ae.getAttribute("data-type")!=="newline"&&(u.selectNodeContents(ae.lastChild),u.collapse(!1)),Se(e)),t.preventDefault(),!0}}return!1},J=X(873),de=function(e){e.options.theme==="dark"?e.element.classList.add("vditor--dark"):e.element.classList.remove("vditor--dark")},He=function(e){e.element.innerHTML="",e.element.classList.add("vditor"),e.options.rtl&&e.element.setAttribute("dir","rtl"),de(e),(0,J.H)(e.options.preview.theme.current,e.options.preview.theme.path),typeof e.options.height=="number"?e.element.style.height=e.options.height+"px":e.element.style.height=e.options.height,typeof e.options.minHeight=="number"&&(e.element.style.minHeight=e.options.minHeight+"px"),typeof e.options.width=="number"?e.element.style.width=e.options.width+"px":e.element.style.width=e.options.width,e.element.appendChild(e.toolbar.element);var t=document.createElement("div");if(t.className="vditor-content",e.options.outline.position==="left"&&t.appendChild(e.outline.element),t.appendChild(e.wysiwyg.element.parentElement),t.appendChild(e.sv.element),t.appendChild(e.ir.element.parentElement),t.appendChild(e.preview.element),e.toolbar.elements.devtools&&t.appendChild(e.devtools.element),e.options.outline.position==="right"&&(e.outline.element.classList.add("vditor-outline--right"),t.appendChild(e.outline.element)),e.upload&&t.appendChild(e.upload.element),e.options.resize.enable&&t.appendChild(e.resize.element),t.appendChild(e.hint.element),t.appendChild(e.tip.element),e.element.appendChild(t),t.addEventListener("click",function(){b(e,["subToolbar"])}),e.toolbar.elements.export&&e.element.insertAdjacentHTML("beforeend",'<iframe id="vditorExportIframe" style="width: 100%;height: 0;border: 0"></iframe>'),Ze(e,e.options.mode,zn(e)),document.execCommand("DefaultParagraphSeparator",!1,"p"),navigator.userAgent.indexOf("iPhone")>-1&&typeof window.visualViewport!="undefined"){var n=!1,r=function(i){n||(n=!0,requestAnimationFrame(function(){n=!1;var a=e.toolbar.element;a.style.transform="none",a.getBoundingClientRect().top<0&&(a.style.transform="translate(0, ".concat(-a.getBoundingClientRect().top,"px)"))}))};window.visualViewport.addEventListener("scroll",r),window.visualViewport.addEventListener("resize",r)}},Ne=function(e){var t=window.innerWidth<=E.Y.MOBILE_WIDTH?10:35;if(e.wysiwyg.element.parentElement.style.display!=="none"){var n=(e.wysiwyg.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.wysiwyg.element.style.padding="10px ".concat(Math.max(t,n),"px")}if(e.ir.element.parentElement.style.display!=="none"){var n=(e.ir.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.ir.element.style.padding="10px ".concat(Math.max(t,n),"px")}e.preview.element.style.display!=="block"?e.toolbar.element.style.paddingLeft=Math.max(5,parseInt(e[e.currentMode].element.style.paddingLeft||"0",10)+(e.options.outline.position==="left"?e.outline.element.offsetWidth:0))+"px":e.toolbar.element.style.paddingLeft=5+(e.options.outline.position==="left"?e.outline.element.offsetWidth:0)+"px"},mt=function(e){if(e.options.typewriterMode){var t=window.innerHeight;typeof e.options.height=="number"?(t=e.options.height,typeof e.options.minHeight=="number"&&(t=Math.max(t,e.options.minHeight)),t=Math.min(window.innerHeight,t)):t=e.element.clientHeight,e.element.classList.contains("vditor--fullscreen")&&(t=window.innerHeight),e[e.currentMode].element.style.setProperty("--editor-bottom",(t-e.toolbar.element.offsetHeight)/2+"px")}},Gt;function Zt(){window.removeEventListener("resize",Gt)}var zn=function(e){mt(e),Zt(),window.addEventListener("resize",Gt=function(){Ne(e),mt(e)});var t=(0,l.D)()&&localStorage.getItem(e.options.cache.id);return(!e.options.cache.enable||!t)&&(e.options.value?t=e.options.value:e.originalInnerHTML?t=e.lute.HTML2Md(e.originalInnerHTML):e.options.cache.enable||(t="")),t||""},it=function(e){clearTimeout(e[e.currentMode].hlToolbarTimeoutId),e[e.currentMode].hlToolbarTimeoutId=window.setTimeout(function(){if(e[e.currentMode].element.getAttribute("contenteditable")!=="false"&&(0,M.Co)(e[e.currentMode].element)){g(e.toolbar.elements,E.Y.EDIT_TOOLBARS),m(e.toolbar.elements,E.Y.EDIT_TOOLBARS);var t=(0,M.RN)(e),n=t.startContainer;t.startContainer.nodeType===3&&(n=t.startContainer.parentElement),n.classList.contains("vditor-reset")&&(n=n.childNodes[t.startOffset]);var r=e.currentMode==="sv"?(0,s.Th)(n,"data-type","heading"):(0,T.c)(n);r&&p(e.toolbar.elements,["headings"]);var i=e.currentMode==="sv"?(0,s.Th)(n,"data-type","blockquote"):(0,s._Y)(n,"BLOCKQUOTE");i&&p(e.toolbar.elements,["quote"]);var a=(0,s.Th)(n,"data-type","strong");a&&p(e.toolbar.elements,["bold"]);var o=(0,s.Th)(n,"data-type","em");o&&p(e.toolbar.elements,["italic"]);var u=(0,s.Th)(n,"data-type","s");u&&p(e.toolbar.elements,["strike"]);var h=(0,s.Th)(n,"data-type","a");h&&p(e.toolbar.elements,["link"]);var f=(0,s._Y)(n,"LI");f?(f.classList.contains("vditor-task")?p(e.toolbar.elements,["check"]):f.parentElement.tagName==="OL"?p(e.toolbar.elements,["ordered-list"]):f.parentElement.tagName==="UL"&&p(e.toolbar.elements,["list"]),m(e.toolbar.elements,["outdent","indent"])):d(e.toolbar.elements,["outdent","indent"]);var S=(0,s.Th)(n,"data-type","code-block");S&&(d(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),p(e.toolbar.elements,["code"]));var k=(0,s.Th)(n,"data-type","code");k&&(d(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),p(e.toolbar.elements,["inline-code"]));var I=(0,s.Th)(n,"data-type","table");I&&d(e.toolbar.elements,["headings","list","ordered-list","check","line","quote","code","table"])}},200)},ce=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),clearTimeout(e.wysiwyg.afterRenderTimeoutId),e.wysiwyg.afterRenderTimeoutId=window.setTimeout(function(){if(!e.wysiwyg.composingLock){var n=x(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,l.D)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e)}},e.options.undoDelay)},Gn=function(e){for(var t=e.previousSibling;t;){if(t.nodeType!==3&&t.tagName==="A"&&!t.previousSibling&&t.innerHTML.replace(E.Y.ZWSP,"")===""&&t.nextSibling)return t;t=t.previousSibling}return!1},Zn=function(e){for(var t=e.startContainer.nextSibling;t&&t.textContent==="";)t=t.nextSibling;return!!(t&&t.nodeType!==3&&(t.tagName==="CODE"||t.getAttribute("data-type")==="math-inline"||t.getAttribute("data-type")==="html-entity"||t.getAttribute("data-type")==="html-inline"))},Jt=function(e){for(var t="",n=e.nextSibling;n;)n.nodeType===3?t+=n.textContent:t+=n.outerHTML,n=n.nextSibling;return t},Xt=function(e){for(var t="",n=e.previousSibling;n;)n.nodeType===3?t=n.textContent+t:t=n.outerHTML+t,n=n.previousSibling;return t},Jn=function(e){for(var t=e;t&&!t.nextSibling;)t=t.parentElement;return t.nextSibling},Xn=function(e){var t=Xt(e.startContainer),n=Jt(e.startContainer),r=e.startContainer.textContent,i=e.startOffset,a="",o="";return(r.substr(0,i)!==""&&r.substr(0,i)!==E.Y.ZWSP||t)&&(a="".concat(t).concat(r.substr(0,i))),(r.substr(i)!==""&&r.substr(i)!==E.Y.ZWSP||n)&&(o="".concat(r.substr(i)).concat(n)),{afterHTML:o,beforeHTML:a}},kt=function(e,t){Array.from(e.wysiwyg.element.childNodes).find(function(n){if(n.nodeType===3){var r=document.createElement("p");r.setAttribute("data-block","0"),r.textContent=n.textContent;var i=t.startContainer.nodeType===3?t.startOffset:n.textContent.length;return n.parentNode.insertBefore(r,n),n.remove(),t.setStart(r.firstChild,Math.min(r.firstChild.textContent.length,i)),t.collapse(!0),(0,M.jl)(t),!0}else if(!n.getAttribute("data-block"))return n.tagName==="P"?n.remove():(n.tagName==="DIV"?(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.innerHTML,"</p>")):n.tagName==="BR"?n.outerHTML='<p data-block="0">'.concat(n.outerHTML,"<wbr></p>"):(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.outerHTML,"</p>")),(0,M.ir)(e.wysiwyg.element,t),t=getSelection().getRangeAt(0)),!0})},gt=function(e,t){var n=(0,M.RN)(e),r=(0,s.pt)(n.startContainer);r||(r=n.startContainer.childNodes[n.startOffset]),!r&&e.wysiwyg.element.children.length===0&&(r=e.wysiwyg.element),r&&!r.classList.contains("vditor-wysiwyg__block")&&(n.insertNode(document.createElement("wbr")),r.innerHTML.trim()==="<wbr>"&&(r.innerHTML="<wbr><br>"),r.tagName==="BLOCKQUOTE"||r.classList.contains("vditor-reset")?r.innerHTML="<".concat(t,' data-block="0">').concat(r.innerHTML.trim(),"</").concat(t,">"):r.outerHTML="<".concat(t,' data-block="0">').concat(r.innerHTML.trim(),"</").concat(t,">"),(0,M.ir)(e.wysiwyg.element,n),Be(e))},Lt=function(e){var t=getSelection().getRangeAt(0),n=(0,s.pt)(t.startContainer);n||(n=t.startContainer.childNodes[t.startOffset]),n&&(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.innerHTML,"</p>"),(0,M.ir)(e.wysiwyg.element,t)),e.wysiwyg.popover.style.display="none"},at=function(e,t,n){n===void 0&&(n=!0);var r=e.previousElementSibling,i=r.ownerDocument.createRange();r.tagName==="CODE"?(r.style.display="inline-block",n?i.setStart(r.firstChild,1):i.selectNodeContents(r)):(r.style.display="block",r.firstChild.firstChild||r.firstChild.appendChild(document.createTextNode("")),i.selectNodeContents(r.firstChild)),n?i.collapse(!0):i.collapse(!1),(0,M.jl)(i),!e.firstElementChild.classList.contains("language-mindmap")&&ye(t)},$n=function(e,t){if(e.wysiwyg.composingLock=t.isComposing,t.isComposing)return!1;t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t);var n=(0,M.RN)(e),r=n.startContainer;if(!on(t,e,r)||(sn(n,e,t),Sn(n),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,l._0)(t)&&t.key!=="Escape"&&t.key!=="Delete"))return!1;var i=(0,s.pt)(r),a=(0,s._Y)(r,"P");if(dn(t,e,a,n)||fn(n,e,a,t)||yn(e,t,n))return!0;var o=(0,s.KJ)(r,"vditor-wysiwyg__block");if(o){if(t.key==="Escape"&&o.children.length===2)return e.wysiwyg.popover.style.display="none",o.firstElementChild.style.display="none",e.wysiwyg.element.blur(),t.preventDefault(),!0;if(!(0,l._0)(t)&&!t.shiftKey&&t.altKey&&t.key==="Enter"&&o.getAttribute("data-type")==="code-block"){var u=e.wysiwyg.popover.querySelector(".vditor-input");return u.focus(),u.select(),t.preventDefault(),!0}if(o.getAttribute("data-block")==="0"&&(bn(e,t,o.firstElementChild,n)||ct(e,t,n,o.firstElementChild,o)||o.getAttribute("data-type")!=="yaml-front-matter"&&et(e,t,n,o.firstElementChild,o)))return!0}if(wn(e,n,t,a))return!0;var h=(0,s.Ab)(r,"BLOCKQUOTE");if(h&&!t.shiftKey&&t.altKey&&t.key==="Enter"){(0,l._0)(t)?n.setStartBefore(h):n.setStartAfter(h),(0,M.jl)(n);var f=document.createElement("p");return f.setAttribute("data-block","0"),f.innerHTML=`
`,n.insertNode(f),n.collapse(!0),(0,M.jl)(n),ce(e),ye(e),t.preventDefault(),!0}var S=(0,T.c)(r);if(S){if(S.tagName==="H6"&&r.textContent.length===n.startOffset&&!(0,l._0)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Enter"){var k=document.createElement("p");return k.textContent=`
`,k.setAttribute("data-block","0"),r.parentElement.insertAdjacentElement("afterend",k),n.setStart(k,0),(0,M.jl)(n),ce(e),ye(e),t.preventDefault(),!0}if(R("⌘=",t)){var I=parseInt(S.tagName.substr(1),10)-1;return I>0&&(gt(e,"h".concat(I)),ce(e)),t.preventDefault(),!0}if(R("⌘-",t)){var I=parseInt(S.tagName.substr(1),10)+1;return I<7&&(gt(e,"h".concat(I)),ce(e)),t.preventDefault(),!0}t.key==="Backspace"&&!(0,l._0)(t)&&!t.shiftKey&&!t.altKey&&S.textContent.length===1&&Lt(e)}if(vn(e,n,t))return!0;if(t.altKey&&t.key==="Enter"&&!(0,l._0)(t)&&!t.shiftKey){var N=(0,s._Y)(r,"A"),H=(0,s.Th)(r,"data-type","link-ref"),F=(0,s.Th)(r,"data-type","footnotes-ref");if(N||H||F||S&&S.tagName.length===2){var B=e.wysiwyg.popover.querySelector("input");B.focus(),B.select()}}if(Ae(e,t))return!0;if(R("⇧⌘U",t)){var O=e.wysiwyg.popover.querySelector('[data-type="up"]');if(O)return O.click(),t.preventDefault(),!0}if(R("⇧⌘D",t)){var O=e.wysiwyg.popover.querySelector('[data-type="down"]');if(O)return O.click(),t.preventDefault(),!0}if(Kt(e,n,t))return!0;if(!(0,l._0)(t)&&t.shiftKey&&!t.altKey&&t.key==="Enter"&&r.parentElement.tagName!=="LI"&&r.parentElement.tagName!=="P")return["STRONG","STRIKE","S","I","EM","B"].includes(r.parentElement.tagName)?n.insertNode(document.createTextNode(`
`+E.Y.ZWSP)):n.insertNode(document.createTextNode(`
`)),n.collapse(!1),(0,M.jl)(n),ce(e),ye(e),t.preventDefault(),!0;if(t.key==="Backspace"&&!(0,l._0)(t)&&!t.shiftKey&&!t.altKey&&n.toString()===""){if(En(e,n,t,a))return!0;if(i){if(i.previousElementSibling&&i.previousElementSibling.classList.contains("vditor-wysiwyg__block")&&i.previousElementSibling.getAttribute("data-block")==="0"&&i.tagName!=="UL"&&i.tagName!=="OL"){var z=(0,M.ED)(i,e.wysiwyg.element,n).start;if(z===0&&n.startOffset===0||z===1&&i.innerText.startsWith(E.Y.ZWSP))return at(i.previousElementSibling.lastElementChild,e,!1),i.innerHTML.trim().replace(E.Y.ZWSP,"")===""&&(i.remove(),ce(e)),t.preventDefault(),!0}var K=n.startOffset;if(n.toString()===""&&r.nodeType===3&&r.textContent.charAt(K-2)===`
`&&r.textContent.charAt(K-1)!==E.Y.ZWSP&&["STRONG","STRIKE","S","I","EM","B"].includes(r.parentElement.tagName))return r.textContent=r.textContent.substring(0,K-1)+E.Y.ZWSP,n.setStart(r,K),n.collapse(!0),ce(e),t.preventDefault(),!0;r.textContent===E.Y.ZWSP&&n.startOffset===1&&!r.previousSibling&&Zn(n)&&(r.textContent=""),i.querySelectorAll("span.vditor-wysiwyg__block[data-type='math-inline']").forEach(function(se){se.firstElementChild.style.display="inline",se.lastElementChild.style.display="none"}),i.querySelectorAll("span.vditor-wysiwyg__block[data-type='html-entity']").forEach(function(se){se.firstElementChild.style.display="inline",se.lastElementChild.style.display="none"})}}if((0,l.gm)()&&n.startOffset===1&&r.textContent.indexOf(E.Y.ZWSP)>-1&&r.previousSibling&&r.previousSibling.nodeType!==3&&r.previousSibling.tagName==="CODE"&&(t.key==="Backspace"||t.key==="ArrowLeft"))return n.selectNodeContents(r.previousSibling),n.collapse(!1),t.preventDefault(),!0;if(Tn(t,i,n))return t.preventDefault(),!0;if(ln(n,t.key),t.key==="ArrowDown"){var Z=r.nextSibling;Z&&Z.nodeType!==3&&Z.getAttribute("data-type")==="math-inline"&&n.setStartAfter(Z)}return i&&ht(i,e,t,n)?(t.preventDefault(),!0):!1},Ae=function(e,t){if(R("⇧⌘X",t)){var n=e.wysiwyg.popover.querySelector('[data-type="remove"]');return n&&n.click(),t.preventDefault(),!0}},Ge=function(e){clearTimeout(e.wysiwyg.hlToolbarTimeoutId),e.wysiwyg.hlToolbarTimeoutId=window.setTimeout(function(){if(e.wysiwyg.element.getAttribute("contenteditable")!=="false"&&(0,M.Co)(e.wysiwyg.element)){g(e.toolbar.elements,E.Y.EDIT_TOOLBARS),m(e.toolbar.elements,E.Y.EDIT_TOOLBARS);var t=getSelection().getRangeAt(0),n=t.startContainer;t.startContainer.nodeType===3?n=t.startContainer.parentElement:n=n.childNodes[t.startOffset>=n.childNodes.length?n.childNodes.length-1:t.startOffset];var r=(0,s.Th)(n,"data-type","footnotes-block");if(r){e.wysiwyg.popover.innerHTML="",je(r,e),Ie(e,r);return}var i=(0,s._Y)(n,"LI");i?(i.classList.contains("vditor-task")?p(e.toolbar.elements,["check"]):i.parentElement.tagName==="OL"?p(e.toolbar.elements,["ordered-list"]):i.parentElement.tagName==="UL"&&p(e.toolbar.elements,["list"]),m(e.toolbar.elements,["outdent","indent"])):d(e.toolbar.elements,["outdent","indent"]),(0,s._Y)(n,"BLOCKQUOTE")&&p(e.toolbar.elements,["quote"]),((0,s._Y)(n,"B")||(0,s._Y)(n,"STRONG"))&&p(e.toolbar.elements,["bold"]),((0,s._Y)(n,"I")||(0,s._Y)(n,"EM"))&&p(e.toolbar.elements,["italic"]),((0,s._Y)(n,"STRIKE")||(0,s._Y)(n,"S"))&&p(e.toolbar.elements,["strike"]),e.wysiwyg.element.querySelectorAll(".vditor-comment--focus").forEach(function(Y){Y.classList.remove("vditor-comment--focus")});var a=(0,s.KJ)(n,"vditor-comment");if(a){var o=a.getAttribute("data-cmtids").split(" ");if(o.length>1&&a.nextSibling.isSameNode(a.nextElementSibling)){var u=a.nextElementSibling.getAttribute("data-cmtids").split(" ");o.find(function(Y){if(u.includes(Y))return o=[Y],!0})}e.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(Y){Y.getAttribute("data-cmtids").indexOf(o[0])>-1&&Y.classList.add("vditor-comment--focus")})}var h=(0,s._Y)(n,"A");h&&p(e.toolbar.elements,["link"]);var f=(0,s._Y)(n,"TABLE"),S=(0,T.c)(n);(0,s._Y)(n,"CODE")?(0,s._Y)(n,"PRE")?(d(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),p(e.toolbar.elements,["code"])):(d(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),p(e.toolbar.elements,["inline-code"])):S?(d(e.toolbar.elements,["bold"]),p(e.toolbar.elements,["headings"])):f&&d(e.toolbar.elements,["table"]);var k=(0,s.KJ)(n,"vditor-toc");if(k){e.wysiwyg.popover.innerHTML="",je(k,e),Ie(e,k);return}var I=(0,T.f)(n,"BLOCKQUOTE");if(I&&(e.wysiwyg.popover.innerHTML="",Xe(t,I,e),$e(t,I,e),je(I,e),Ie(e,I)),i&&(e.wysiwyg.popover.innerHTML="",Xe(t,i,e),$e(t,i,e),je(i,e),Ie(e,i)),f){e.options.lang,e.options,e.wysiwyg.popover.innerHTML="";var N=function(){var Y=f.rows.length,te=f.rows[0].cells.length,qe=parseInt(be.value,10)||Y,ze=parseInt(Re.value,10)||te;if(!(qe===Y&&te===ze)){if(te!==ze)for(var dt=ze-te,Ve=0;Ve<f.rows.length;Ve++)if(dt>0)for(var Hn=0;Hn<dt;Hn++)Ve===0?f.rows[Ve].lastElementChild.insertAdjacentHTML("afterend","<th> </th>"):f.rows[Ve].lastElementChild.insertAdjacentHTML("afterend","<td> </td>");else for(var Wt=te-1;Wt>=ze;Wt--)f.rows[Ve].cells[Wt].remove();if(Y!==qe){var Rn=qe-Y;if(Rn>0){for(var qt="<tr>",rt=0;rt<ze;rt++)qt+="<td> </td>";for(var Nn=0;Nn<Rn;Nn++)f.querySelector("tbody")?f.querySelector("tbody").insertAdjacentHTML("beforeend",qt):f.querySelector("thead").insertAdjacentHTML("afterend",qt+"</tr>")}else for(var rt=Y-1;rt>=qe;rt--)f.rows[rt].remove(),f.rows.length===1&&f.querySelector("tbody").remove()}typeof e.options.input=="function"&&e.options.input(x(e))}},H=function(Y){vt(f,Y),Y==="right"?(z.classList.remove("vditor-icon--current"),K.classList.remove("vditor-icon--current"),Z.classList.add("vditor-icon--current")):Y==="center"?(z.classList.remove("vditor-icon--current"),Z.classList.remove("vditor-icon--current"),K.classList.add("vditor-icon--current")):(K.classList.remove("vditor-icon--current"),Z.classList.remove("vditor-icon--current"),z.classList.add("vditor-icon--current")),(0,M.jl)(t),ce(e)},F=(0,s._Y)(n,"TD"),B=(0,s._Y)(n,"TH"),O="left";F?O=F.getAttribute("align")||"left":B&&(O=B.getAttribute("align")||"center");var z=document.createElement("button");z.setAttribute("type","button"),z.setAttribute("aria-label",window.VditorI18n.alignLeft+"<"+(0,l.fG)("⇧⌘L")+">"),z.setAttribute("data-type","left"),z.innerHTML='<svg><use xlink:href="#vditor-icon-align-left"></use></svg>',z.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(O==="left"?" vditor-icon--current":""),z.onclick=function(){H("left")};var K=document.createElement("button");K.setAttribute("type","button"),K.setAttribute("aria-label",window.VditorI18n.alignCenter+"<"+(0,l.fG)("⇧⌘C")+">"),K.setAttribute("data-type","center"),K.innerHTML='<svg><use xlink:href="#vditor-icon-align-center"></use></svg>',K.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(O==="center"?" vditor-icon--current":""),K.onclick=function(){H("center")};var Z=document.createElement("button");Z.setAttribute("type","button"),Z.setAttribute("aria-label",window.VditorI18n.alignRight+"<"+(0,l.fG)("⇧⌘R")+">"),Z.setAttribute("data-type","right"),Z.innerHTML='<svg><use xlink:href="#vditor-icon-align-right"></use></svg>',Z.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(O==="right"?" vditor-icon--current":""),Z.onclick=function(){H("right")};var se=document.createElement("button");se.setAttribute("type","button"),se.setAttribute("aria-label",window.VditorI18n.insertRowBelow+"<"+(0,l.fG)("⌘=")+">"),se.setAttribute("data-type","insertRow"),se.innerHTML='<svg><use xlink:href="#vditor-icon-insert-row"></use></svg>',se.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",se.onclick=function(){var Y=getSelection().getRangeAt(0).startContainer,te=(0,s._Y)(Y,"TD")||(0,s._Y)(Y,"TH");te&&pn(e,t,te)};var ae=document.createElement("button");ae.setAttribute("type","button"),ae.setAttribute("aria-label",window.VditorI18n.insertRowAbove+"<"+(0,l.fG)("⇧⌘F")+">"),ae.setAttribute("data-type","insertRow"),ae.innerHTML='<svg><use xlink:href="#vditor-icon-insert-rowb"></use></svg>',ae.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",ae.onclick=function(){var Y=getSelection().getRangeAt(0).startContainer,te=(0,s._Y)(Y,"TD")||(0,s._Y)(Y,"TH");te&&hn(e,t,te)};var ue=document.createElement("button");ue.setAttribute("type","button"),ue.setAttribute("aria-label",window.VditorI18n.insertColumnRight+"<"+(0,l.fG)("⇧⌘=")+">"),ue.setAttribute("data-type","insertColumn"),ue.innerHTML='<svg><use xlink:href="#vditor-icon-insert-column"></use></svg>',ue.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",ue.onclick=function(){var Y=getSelection().getRangeAt(0).startContainer,te=(0,s._Y)(Y,"TD")||(0,s._Y)(Y,"TH");te&&Et(e,f,te)};var xe=document.createElement("button");xe.setAttribute("type","button"),xe.setAttribute("aria-label",window.VditorI18n.insertColumnLeft+"<"+(0,l.fG)("⇧⌘G")+">"),xe.setAttribute("data-type","insertColumn"),xe.innerHTML='<svg><use xlink:href="#vditor-icon-insert-columnb"></use></svg>',xe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",xe.onclick=function(){var Y=getSelection().getRangeAt(0).startContainer,te=(0,s._Y)(Y,"TD")||(0,s._Y)(Y,"TH");te&&Et(e,f,te,"beforebegin")};var Ce=document.createElement("button");Ce.setAttribute("type","button"),Ce.setAttribute("aria-label",window.VditorI18n["delete-row"]+"<"+(0,l.fG)("⌘-")+">"),Ce.setAttribute("data-type","deleteRow"),Ce.innerHTML='<svg><use xlink:href="#vditor-icon-delete-row"></use></svg>',Ce.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Ce.onclick=function(){var Y=getSelection().getRangeAt(0).startContainer,te=(0,s._Y)(Y,"TD")||(0,s._Y)(Y,"TH");te&&mn(e,t,te)};var De=document.createElement("button");De.setAttribute("type","button"),De.setAttribute("aria-label",window.VditorI18n["delete-column"]+"<"+(0,l.fG)("⇧⌘-")+">"),De.setAttribute("data-type","deleteColumn"),De.innerHTML='<svg><use xlink:href="#vditor-icon-delete-column"></use></svg>',De.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",De.onclick=function(){var Y=getSelection().getRangeAt(0).startContainer,te=(0,s._Y)(Y,"TD")||(0,s._Y)(Y,"TH");te&&gn(e,t,f,te)};var $=document.createElement("span");$.setAttribute("aria-label",window.VditorI18n.row),$.className="vditor-tooltipped vditor-tooltipped__n";var be=document.createElement("input");$.appendChild(be),be.type="number",be.min="1",be.className="vditor-input",be.style.width="42px",be.style.textAlign="center",be.setAttribute("placeholder",window.VditorI18n.row),be.value=f.rows.length.toString(),be.oninput=function(){N()},be.onkeydown=function(Y){if(!Y.isComposing){if(Y.key==="Tab"){Re.focus(),Re.select(),Y.preventDefault();return}Ae(e,Y)||Ue(Y,t)}};var _t=document.createElement("span");_t.setAttribute("aria-label",window.VditorI18n.column),_t.className="vditor-tooltipped vditor-tooltipped__n";var Re=document.createElement("input");_t.appendChild(Re),Re.type="number",Re.min="1",Re.className="vditor-input",Re.style.width="42px",Re.style.textAlign="center",Re.setAttribute("placeholder",window.VditorI18n.column),Re.value=f.rows[0].cells.length.toString(),Re.oninput=function(){N()},Re.onkeydown=function(Y){if(!Y.isComposing){if(Y.key==="Tab"){be.focus(),be.select(),Y.preventDefault();return}Ae(e,Y)||Ue(Y,t)}},Xe(t,f,e),$e(t,f,e),je(f,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",z),e.wysiwyg.popover.insertAdjacentElement("beforeend",K),e.wysiwyg.popover.insertAdjacentElement("beforeend",Z),e.wysiwyg.popover.insertAdjacentElement("beforeend",ae),e.wysiwyg.popover.insertAdjacentElement("beforeend",se),e.wysiwyg.popover.insertAdjacentElement("beforeend",xe),e.wysiwyg.popover.insertAdjacentElement("beforeend",ue),e.wysiwyg.popover.insertAdjacentElement("beforeend",Ce),e.wysiwyg.popover.insertAdjacentElement("beforeend",De),e.wysiwyg.popover.insertAdjacentElement("beforeend",$),e.wysiwyg.popover.insertAdjacentHTML("beforeend"," x "),e.wysiwyg.popover.insertAdjacentElement("beforeend",_t),Ie(e,f)}var Ft=(0,s.Th)(n,"data-type","link-ref");Ft&&$t(e,Ft,t);var tt=(0,s.Th)(n,"data-type","footnotes-ref");if(tt){e.options.lang,e.options,e.wysiwyg.popover.innerHTML="";var $=document.createElement("span");$.setAttribute("aria-label",window.VditorI18n.footnoteRef+"<"+(0,l.fG)("⌥Enter")+">"),$.className="vditor-tooltipped vditor-tooltipped__n";var Ye=document.createElement("input");$.appendChild(Ye),Ye.className="vditor-input",Ye.setAttribute("placeholder",window.VditorI18n.footnoteRef+"<"+(0,l.fG)("⌥Enter")+">"),Ye.style.width="120px",Ye.value=tt.getAttribute("data-footnotes-label"),Ye.oninput=function(){Ye.value.trim()!==""&&tt.setAttribute("data-footnotes-label",Ye.value),typeof e.options.input=="function"&&e.options.input(x(e))},Ye.onkeydown=function(te){te.isComposing||Ae(e,te)||Ue(te,t)},je(tt,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",$),Ie(e,tt)}var Te=(0,s.KJ)(n,"vditor-wysiwyg__block"),Dn=Te?Te.getAttribute("data-type").indexOf("block")>-1:!1;if(e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview").forEach(function(Y){if(!Te||Te&&Dn&&!Te.contains(Y)){var te=Y.previousElementSibling;te.style.display="none"}}),Te&&Dn){if(e.wysiwyg.popover.innerHTML="",Xe(t,Te,e),$e(t,Te,e),je(Te,e),Te.getAttribute("data-type")==="code-block"){var Ct=document.createElement("span");Ct.setAttribute("aria-label",window.VditorI18n.language+"<"+(0,l.fG)("⌥Enter")+">"),Ct.className="vditor-tooltipped vditor-tooltipped__n";var Pe=document.createElement("input");Ct.appendChild(Pe);var ft=Te.firstElementChild.firstElementChild;Pe.className="vditor-input",Pe.setAttribute("placeholder",window.VditorI18n.language+"<"+(0,l.fG)("⌥Enter")+">"),Pe.value=ft.className.indexOf("language-")>-1?ft.className.split("-")[1].split(" ")[0]:"",Pe.oninput=function(Y){Pe.value.trim()!==""?ft.className="language-".concat(Pe.value):(ft.className="",e.hint.recentLanguage=""),Te.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(Te.lastElementChild.innerHTML=Te.firstElementChild.innerHTML,Ee(Te.lastElementChild,e)),ce(e),Y.detail===1&&(t.setStart(ft.firstChild,0),t.collapse(!0),(0,M.jl)(t))},Pe.onkeydown=function(Y){if(!Y.isComposing&&!Ae(e,Y)){if(Y.key==="Escape"&&e.hint.element.style.display==="block"){e.hint.element.style.display="none",Y.preventDefault();return}e.hint.select(Y,e),Ue(Y,t)}},Pe.onkeyup=function(Y){var te,qe;if(!(Y.isComposing||Y.key==="Enter"||Y.key==="ArrowUp"||Y.key==="Escape"||Y.key==="ArrowDown")){var ze=[],dt=Pe.value.substring(0,Pe.selectionStart);(e.options.preview.hljs.langs||E.Y.ALIAS_CODE_LANGUAGES.concat(((qe=(te=window.hljs)===null||te===void 0?void 0:te.listLanguages())!==null&&qe!==void 0?qe:[]).sort())).forEach(function(Ve){Ve.indexOf(dt.toLowerCase())>-1&&ze.push({html:Ve,value:Ve})}),e.hint.genHTML(ze,dt,e),Y.preventDefault()}},e.wysiwyg.popover.insertAdjacentElement("beforeend",Ct)}Ie(e,Te)}else Te=void 0;if(S){e.wysiwyg.popover.innerHTML="";var $=document.createElement("span");$.setAttribute("aria-label","ID<"+(0,l.fG)("⌥Enter")+">"),$.className="vditor-tooltipped vditor-tooltipped__n";var We=document.createElement("input");$.appendChild(We),We.className="vditor-input",We.setAttribute("placeholder","ID<"+(0,l.fG)("⌥Enter")+">"),We.style.width="120px",We.value=S.getAttribute("data-id")||"",We.oninput=function(){S.setAttribute("data-id",We.value),typeof e.options.input=="function"&&e.options.input(x(e))},We.onkeydown=function(te){te.isComposing||Ae(e,te)||Ue(te,t)},Xe(t,S,e),$e(t,S,e),je(S,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",$),Ie(e,S)}if(h&&At(e,h,t),!I&&!i&&!f&&!Te&&!h&&!Ft&&!tt&&!S&&!k){var nt=(0,s.Th)(n,"data-block","0");nt&&nt.parentElement.isEqualNode(e.wysiwyg.element)?(e.wysiwyg.popover.innerHTML="",Xe(t,nt,e),$e(t,nt,e),je(nt,e),Ie(e,nt)):e.wysiwyg.popover.style.display="none"}e.wysiwyg.element.querySelectorAll('span[data-type="backslash"] > span').forEach(function(Y){Y.style.display="none"});var On=(0,s.Th)(t.startContainer,"data-type","backslash");On&&(On.querySelector("span").style.display="inline")}},200)},Ie=function(e,t){var n=t,r=(0,s._Y)(t,"TABLE");r&&(n=r),e.wysiwyg.popover.style.left="0",e.wysiwyg.popover.style.display="block",e.wysiwyg.popover.style.top=Math.max(-8,n.offsetTop-21-e.wysiwyg.element.scrollTop)+"px",e.wysiwyg.popover.style.left=Math.min(n.offsetLeft,e.wysiwyg.element.clientWidth-e.wysiwyg.popover.clientWidth)+"px",e.wysiwyg.popover.setAttribute("data-top",(n.offsetTop-21).toString())},$t=function(e,t,n){n===void 0&&(n=getSelection().getRangeAt(0)),e.wysiwyg.popover.innerHTML="";var r=function(){a.value.trim()!==""&&(t.tagName==="IMG"?t.setAttribute("alt",a.value):t.textContent=a.value),u.value.trim()!==""&&t.setAttribute("data-link-label",u.value),typeof e.options.input=="function"&&e.options.input(x(e))},i=document.createElement("span");i.setAttribute("aria-label",window.VditorI18n.textIsNotEmpty),i.className="vditor-tooltipped vditor-tooltipped__n";var a=document.createElement("input");i.appendChild(a),a.className="vditor-input",a.setAttribute("placeholder",window.VditorI18n.textIsNotEmpty),a.style.width="120px",a.value=t.getAttribute("alt")||t.textContent,a.oninput=function(){r()},a.onkeydown=function(h){Ae(e,h)||Ue(h,n)||ot(e,t,h,u)};var o=document.createElement("span");o.setAttribute("aria-label",window.VditorI18n.linkRef),o.className="vditor-tooltipped vditor-tooltipped__n";var u=document.createElement("input");o.appendChild(u),u.className="vditor-input",u.setAttribute("placeholder",window.VditorI18n.linkRef),u.value=t.getAttribute("data-link-label"),u.oninput=function(){r()},u.onkeydown=function(h){Ae(e,h)||Ue(h,n)||ot(e,t,h,a)},je(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",i),e.wysiwyg.popover.insertAdjacentElement("beforeend",o),Ie(e,t)},Xe=function(e,t,n){var r=t.previousElementSibling;if(!(!r||!t.parentElement.isEqualNode(n.wysiwyg.element)&&t.tagName!=="LI")){var i=document.createElement("button");i.setAttribute("type","button"),i.setAttribute("data-type","up"),i.setAttribute("aria-label",window.VditorI18n.up+"<"+(0,l.fG)("⇧⌘U")+">"),i.innerHTML='<svg><use xlink:href="#vditor-icon-up"></use></svg>',i.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",i.onclick=function(){e.insertNode(document.createElement("wbr")),r.insertAdjacentElement("beforebegin",t),(0,M.ir)(n.wysiwyg.element,e),ce(n),Ge(n),ye(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",i)}},$e=function(e,t,n){var r=t.nextElementSibling;if(!(!r||!t.parentElement.isEqualNode(n.wysiwyg.element)&&t.tagName!=="LI")){var i=document.createElement("button");i.setAttribute("type","button"),i.setAttribute("data-type","down"),i.setAttribute("aria-label",window.VditorI18n.down+"<"+(0,l.fG)("⇧⌘D")+">"),i.innerHTML='<svg><use xlink:href="#vditor-icon-down"></use></svg>',i.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",i.onclick=function(){e.insertNode(document.createElement("wbr")),r.insertAdjacentElement("afterend",t),(0,M.ir)(n.wysiwyg.element,e),ce(n),Ge(n),ye(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",i)}},je=function(e,t){var n=document.createElement("button");n.setAttribute("type","button"),n.setAttribute("data-type","remove"),n.setAttribute("aria-label",window.VditorI18n.remove+"<"+(0,l.fG)("⇧⌘X")+">"),n.innerHTML='<svg><use xlink:href="#vditor-icon-trashcan"></use></svg>',n.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",n.onclick=function(){var r=(0,M.RN)(t);r.setStartAfter(e),(0,M.jl)(r),e.remove(),ce(t),Ge(t),["H1","H2","H3","H4","H5","H6"].includes(e.tagName)&&Be(t)},t.wysiwyg.popover.insertAdjacentElement("beforeend",n)},ot=function(e,t,n,r){if(!n.isComposing){if(n.key==="Tab"){r.focus(),r.select(),n.preventDefault();return}if(!(0,l._0)(n)&&!n.shiftKey&&n.altKey&&n.key==="Enter"){var i=(0,M.RN)(e);t.insertAdjacentHTML("afterend",E.Y.ZWSP),i.setStartAfter(t.nextSibling),i.collapse(!0),(0,M.jl)(i),n.preventDefault()}}},At=function(e,t,n){e.wysiwyg.popover.innerHTML="";var r=function(){a.value.trim()!==""&&(t.innerHTML=a.value),t.setAttribute("href",u.value),t.setAttribute("title",f.value),ce(e)};t.querySelectorAll("[data-marker]").forEach(function(S){S.removeAttribute("data-marker")});var i=document.createElement("span");i.setAttribute("aria-label",window.VditorI18n.textIsNotEmpty),i.className="vditor-tooltipped vditor-tooltipped__n";var a=document.createElement("input");i.appendChild(a),a.className="vditor-input",a.setAttribute("placeholder",window.VditorI18n.textIsNotEmpty),a.style.width="120px",a.value=t.innerHTML||"",a.oninput=function(){r()},a.onkeydown=function(S){Ae(e,S)||Ue(S,n)||ot(e,t,S,u)};var o=document.createElement("span");o.setAttribute("aria-label",window.VditorI18n.link),o.className="vditor-tooltipped vditor-tooltipped__n";var u=document.createElement("input");o.appendChild(u),u.className="vditor-input",u.setAttribute("placeholder",window.VditorI18n.link),u.value=t.getAttribute("href")||"",u.oninput=function(){r()},u.onkeydown=function(S){Ae(e,S)||Ue(S,n)||ot(e,t,S,f)};var h=document.createElement("span");h.setAttribute("aria-label",window.VditorI18n.tooltipText),h.className="vditor-tooltipped vditor-tooltipped__n";var f=document.createElement("input");h.appendChild(f),f.className="vditor-input",f.setAttribute("placeholder",window.VditorI18n.tooltipText),f.style.width="60px",f.value=t.getAttribute("title")||"",f.oninput=function(){r()},f.onkeydown=function(S){Ae(e,S)||Ue(S,n)||ot(e,t,S,a)},je(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",i),e.wysiwyg.popover.insertAdjacentElement("beforeend",o),e.wysiwyg.popover.insertAdjacentElement("beforeend",h),Ie(e,t)},Qn=function(e,t){var n=e.target;t.wysiwyg.popover.innerHTML="";var r=function(){n.setAttribute("src",a.value),n.setAttribute("alt",u.value),n.setAttribute("title",f.value),typeof t.options.input=="function"&&t.options.input(x(t))},i=document.createElement("span");i.setAttribute("aria-label",window.VditorI18n.imageURL),i.className="vditor-tooltipped vditor-tooltipped__n";var a=document.createElement("input");i.appendChild(a),a.className="vditor-input",a.setAttribute("placeholder",window.VditorI18n.imageURL),a.value=n.getAttribute("src")||"",a.oninput=function(){r()},a.onkeydown=function(S){Ae(t,S)};var o=document.createElement("span");o.setAttribute("aria-label",window.VditorI18n.alternateText),o.className="vditor-tooltipped vditor-tooltipped__n";var u=document.createElement("input");o.appendChild(u),u.className="vditor-input",u.setAttribute("placeholder",window.VditorI18n.alternateText),u.style.width="52px",u.value=n.getAttribute("alt")||"",u.oninput=function(){r()},u.onkeydown=function(S){Ae(t,S)};var h=document.createElement("span");h.setAttribute("aria-label",window.VditorI18n.title),h.className="vditor-tooltipped vditor-tooltipped__n";var f=document.createElement("input");h.appendChild(f),f.className="vditor-input",f.setAttribute("placeholder",window.VditorI18n.title),f.value=n.getAttribute("title")||"",f.oninput=function(){r()},f.onkeydown=function(S){Ae(t,S)},je(n,t),t.wysiwyg.popover.insertAdjacentElement("beforeend",i),t.wysiwyg.popover.insertAdjacentElement("beforeend",o),t.wysiwyg.popover.insertAdjacentElement("beforeend",h),Ie(t,n)},Ue=function(e,t){if(!(0,l._0)(e)&&!e.shiftKey&&e.key==="Enter"||e.key==="Escape")return t&&(0,M.jl)(t),e.preventDefault(),e.stopPropagation(),!0},Qe=function(e){e.currentMode==="wysiwyg"?Ge(e):e.currentMode==="ir"&&it(e)},Qt=function(e,t,n){n===void 0&&(n={enableAddUndoStack:!0,enableHint:!1,enableInput:!0});var r=e.wysiwyg.element;r.innerHTML=e.lute.Md2VditorDOM(t),r.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(i){Ee(i,e),i.previousElementSibling.setAttribute("style","display:none")}),ce(e,n)},er=function(e,t,n){for(var r=e.startContainer.parentElement,i=!1,a="",o="",u=Xn(e),h=u.beforeHTML,f=u.afterHTML;r&&!i;){var S=r.tagName;if(S==="STRIKE"&&(S="S"),S==="I"&&(S="EM"),S==="B"&&(S="STRONG"),S==="S"||S==="STRONG"||S==="EM"){var k="",I="",N="";r.parentElement.getAttribute("data-block")!=="0"&&(I=Xt(r),N=Jt(r)),(h||I)&&(k="".concat(I,"<").concat(S,">").concat(h,"</").concat(S,">"),h=k),(n==="bold"&&S==="STRONG"||n==="italic"&&S==="EM"||n==="strikeThrough"&&S==="S")&&(k+="".concat(a).concat(E.Y.ZWSP,"<wbr>").concat(o),i=!0),(f||N)&&(f="<".concat(S,">").concat(f,"</").concat(S,">").concat(N),k+=f),r.parentElement.getAttribute("data-block")!=="0"?(r=r.parentElement,r.innerHTML=k):(r.outerHTML=k,r=r.parentElement),a="<".concat(S,">")+a,o="</".concat(S,">")+o}else i=!0}(0,M.ir)(t.wysiwyg.element,e)},tr=function(e,t,n){if(!(e.wysiwyg.composingLock&&n instanceof CustomEvent)){var r=!0,i=!0;e.wysiwyg.element.querySelector("wbr")&&e.wysiwyg.element.querySelector("wbr").remove();var a=(0,M.RN)(e),o=t.getAttribute("data-type");if(t.classList.contains("vditor-menu--current"))if(o==="strike"&&(o="strikeThrough"),o==="quote"){var u=(0,s._Y)(a.startContainer,"BLOCKQUOTE");u||(u=a.startContainer.childNodes[a.startOffset]),u&&(r=!1,t.classList.remove("vditor-menu--current"),a.insertNode(document.createElement("wbr")),u.outerHTML=u.innerHTML.trim()===""?'<p data-block="0">'.concat(u.innerHTML,"</p>"):u.innerHTML,(0,M.ir)(e.wysiwyg.element,a))}else if(o==="inline-code"){var h=(0,s._Y)(a.startContainer,"CODE");h||(h=a.startContainer.childNodes[a.startOffset]),h&&(h.outerHTML=h.innerHTML.replace(E.Y.ZWSP,"")+"<wbr>",(0,M.ir)(e.wysiwyg.element,a))}else o==="link"?(a.collapsed&&a.selectNode(a.startContainer.parentElement),document.execCommand("unlink",!1,"")):o==="check"||o==="list"||o==="ordered-list"?(wt(e,a,o),(0,M.ir)(e.wysiwyg.element,a),r=!1,t.classList.remove("vditor-menu--current")):(r=!1,t.classList.remove("vditor-menu--current"),a.toString()===""?er(a,e,o):document.execCommand(o,!1,""));else{e.wysiwyg.element.childNodes.length===0&&(e.wysiwyg.element.innerHTML='<p data-block="0"><wbr></p>',(0,M.ir)(e.wysiwyg.element,a));var f=(0,s.pt)(a.startContainer);if(o==="quote"){if(f||(f=a.startContainer.childNodes[a.startOffset]),f){r=!1,t.classList.add("vditor-menu--current"),a.insertNode(document.createElement("wbr"));var S=(0,s._Y)(a.startContainer,"LI");S&&f.contains(S)?S.innerHTML='<blockquote data-block="0">'.concat(S.innerHTML,"</blockquote>"):f.outerHTML='<blockquote data-block="0">'.concat(f.outerHTML,"</blockquote>"),(0,M.ir)(e.wysiwyg.element,a)}}else if(o==="check"||o==="list"||o==="ordered-list")wt(e,a,o,!1),(0,M.ir)(e.wysiwyg.element,a),r=!1,g(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current");else if(o==="inline-code"){if(a.toString()===""){var k=document.createElement("code");k.textContent=E.Y.ZWSP,a.insertNode(k),a.setStart(k.firstChild,1),a.collapse(!0),(0,M.jl)(a)}else if(a.startContainer.nodeType===3){var k=document.createElement("code");a.surroundContents(k),a.insertNode(k),(0,M.jl)(a)}t.classList.add("vditor-menu--current")}else if(o==="code"){var k=document.createElement("div");k.className="vditor-wysiwyg__block",k.setAttribute("data-type","code-block"),k.setAttribute("data-block","0"),k.setAttribute("data-marker","```"),a.toString()===""?k.innerHTML=`<pre><code><wbr>
</code></pre>`:(k.innerHTML="<pre><code>".concat(a.toString(),"<wbr></code></pre>"),a.deleteContents()),a.insertNode(k),f&&(f.outerHTML=e.lute.SpinVditorDOM(f.outerHTML)),(0,M.ir)(e.wysiwyg.element,a),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(se){Ee(se,e)}),t.classList.add("vditor-menu--disabled")}else if(o==="link"){if(a.toString()===""){var I=document.createElement("a");I.innerText=E.Y.ZWSP,a.insertNode(I),a.setStart(I.firstChild,1),a.collapse(!0),At(e,I,a);var N=e.wysiwyg.popover.querySelector("input");N.value="",N.focus(),i=!1}else{var k=document.createElement("a");k.setAttribute("href",""),k.innerHTML=a.toString(),a.surroundContents(k),a.insertNode(k),(0,M.jl)(a),At(e,k,a);var H=e.wysiwyg.popover.querySelectorAll("input");H[0].value=k.innerText,H[1].focus()}r=!1,t.classList.add("vditor-menu--current")}else if(o==="table"){var F='<table data-block="0"><thead><tr><th>col1<wbr></th><th>col2</th><th>col3</th></tr></thead><tbody><tr><td> </td><td> </td><td> </td></tr><tr><td> </td><td> </td><td> </td></tr></tbody></table>';if(a.toString().trim()==="")f&&f.innerHTML.trim().replace(E.Y.ZWSP,"")===""?f.outerHTML=F:document.execCommand("insertHTML",!1,F),a.selectNode(e.wysiwyg.element.querySelector("wbr").previousSibling),e.wysiwyg.element.querySelector("wbr").remove(),(0,M.jl)(a);else{F='<table data-block="0"><thead><tr>';var B=a.toString().split(`
`),O=B[0].split(",").length>B[0].split("	").length?",":"	";B.forEach(function(Z,se){se===0?(Z.split(O).forEach(function(ae,ue){ue===0?F+="<th>".concat(ae,"<wbr></th>"):F+="<th>".concat(ae,"</th>")}),F+="</tr></thead>"):(se===1?F+="<tbody><tr>":F+="<tr>",Z.split(O).forEach(function(ae){F+="<td>".concat(ae,"</td>")}),F+="</tr>")}),F+="</tbody></table>",document.execCommand("insertHTML",!1,F),(0,M.ir)(e.wysiwyg.element,a)}r=!1,t.classList.add("vditor-menu--disabled")}else if(o==="line"){if(f){var z=`<hr data-block="0"><p data-block="0"><wbr>
</p>`;f.innerHTML.trim()===""?f.outerHTML=z:f.insertAdjacentHTML("afterend",z),(0,M.ir)(e.wysiwyg.element,a)}}else if(r=!1,t.classList.add("vditor-menu--current"),o==="strike"&&(o="strikeThrough"),a.toString()===""&&(o==="bold"||o==="italic"||o==="strikeThrough")){var K="strong";o==="italic"?K="em":o==="strikeThrough"&&(K="s");var k=document.createElement(K);k.textContent=E.Y.ZWSP,a.insertNode(k),k.previousSibling&&k.previousSibling.textContent===E.Y.ZWSP&&(k.previousSibling.textContent=""),a.setStart(k.firstChild,1),a.collapse(!0),(0,M.jl)(a)}else document.execCommand(o,!1,"")}r&&Ge(e),i&&ce(e)}},he=function(){function e(t,n){var r,i=this;this.element=document.createElement("div"),n.className&&(r=this.element.classList).add.apply(r,n.className.split(" "));var a=n.hotkey?" <".concat((0,l.fG)(n.hotkey),">"):"";n.level===2&&(a=n.hotkey?" &lt;".concat((0,l.fG)(n.hotkey),"&gt;"):"");var o=n.tip?n.tip+a:"".concat(window.VditorI18n[n.name]).concat(a),u=n.name==="upload"?"div":"button";if(n.level===2)this.element.innerHTML="<".concat(u,' data-type="').concat(n.name,'">').concat(o,"</").concat(u,">");else{this.element.classList.add("vditor-toolbar__item");var h=document.createElement(u);h.setAttribute("data-type",n.name),h.className="vditor-tooltipped vditor-tooltipped__".concat(n.tipPosition),h.setAttribute("aria-label",o),h.innerHTML=n.icon,this.element.appendChild(h)}n.prefix&&this.element.children[0].addEventListener((0,l.y3)(),function(f){f.preventDefault(),!i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)&&(t.currentMode==="wysiwyg"?tr(t,i.element.children[0],f):t.currentMode==="ir"?gr(t,i.element.children[0],n.prefix||"",n.suffix||""):ar(t,i.element.children[0],n.prefix||"",n.suffix||""))})}return e}(),nr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ze=function(e,t,n){var r;if(typeof n!="string"?(b(e,["subToolbar","hint"]),n.preventDefault(),r=x(e)):r=n,!(e.currentMode===t&&typeof n!="string")){if(e.devtools&&e.devtools.renderEchart(e),e.options.preview.mode==="both"&&t==="sv"?e.preview.element.style.display="block":e.preview.element.style.display="none",m(e.toolbar.elements,E.Y.EDIT_TOOLBARS),g(e.toolbar.elements,E.Y.EDIT_TOOLBARS),d(e.toolbar.elements,["outdent","indent"]),t==="ir")v(e.toolbar.elements,["both"]),w(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="block",e.lute.SetVditorIR(!0),e.lute.SetVditorWYSIWYG(!1),e.lute.SetVditorSV(!1),e.currentMode="ir",e.ir.element.innerHTML=e.lute.Md2VditorIRDOM(r),Ke(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),Ne(e),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(a){Ee(a,e)}),e.ir.element.querySelectorAll(".vditor-toc").forEach(function(a){(0,ne.T)(a,{cdn:e.options.cdn,math:e.options.preview.math})});else if(t==="wysiwyg")v(e.toolbar.elements,["both"]),w(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="block",e.ir.element.parentElement.style.display="none",e.lute.SetVditorIR(!1),e.lute.SetVditorWYSIWYG(!0),e.lute.SetVditorSV(!1),e.currentMode="wysiwyg",Ne(e),Qt(e,r,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),e.wysiwyg.element.querySelectorAll(".vditor-toc").forEach(function(a){(0,ne.T)(a,{cdn:e.options.cdn,math:e.options.preview.math})}),e.wysiwyg.popover.style.display="none";else if(t==="sv"){w(e.toolbar.elements,["both"]),v(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="none",(e.options.preview.mode==="both"||e.options.preview.mode==="editor")&&(e.sv.element.style.display="block"),e.lute.SetVditorIR(!1),e.lute.SetVditorWYSIWYG(!1),e.lute.SetVditorSV(!0),e.currentMode="sv";var i=tn(r,e);i==="<div data-block='0'></div>"&&(i=""),e.sv.element.innerHTML=i,ge(e.sv.element),Se(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),Ne(e)}e.undo.resetIcon(e),typeof n!="string"&&(e[e.currentMode].element.focus(),Qe(e)),Be(e),mt(e),e.toolbar.elements["edit-mode"]&&(e.toolbar.elements["edit-mode"].querySelectorAll("button").forEach(function(a){a.classList.remove("vditor-menu--current")}),e.toolbar.elements["edit-mode"].querySelector('button[data-mode="'.concat(e.currentMode,'"]')).classList.add("vditor-menu--current")),e.outline.toggle(e,e.currentMode!=="sv"&&e.options.outline.enable,typeof n!="string")}},rr=function(e){nr(t,e);function t(n,r){var i=e.call(this,n,r)||this,a=document.createElement("div");return a.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow"),a.innerHTML='<button data-mode="wysiwyg">'.concat(window.VditorI18n.wysiwyg," &lt;").concat((0,l.fG)("⌥⌘7"),`></button>
<button data-mode="ir">`).concat(window.VditorI18n.instantRendering," &lt;").concat((0,l.fG)("⌥⌘8"),`></button>
<button data-mode="sv">`).concat(window.VditorI18n.splitView," &lt;").concat((0,l.fG)("⌥⌘9"),"></button>"),i.element.appendChild(a),i._bindEvent(n,a,r),i}return t.prototype._bindEvent=function(n,r,i){var a=this.element.children[0];y(n,r,a,i.level),r.children.item(0).addEventListener((0,l.y3)(),function(o){Ze(n,"wysiwyg",o),o.preventDefault(),o.stopPropagation()}),r.children.item(1).addEventListener((0,l.y3)(),function(o){Ze(n,"ir",o),o.preventDefault(),o.stopPropagation()}),r.children.item(2).addEventListener((0,l.y3)(),function(o){Ze(n,"sv",o),o.preventDefault(),o.stopPropagation()})},t}(he),st=function(e,t){return(0,M.Co)(e,t)?getSelection().toString():""},xt=function(e,t){t.addEventListener("focus",function(){e.options.focus&&e.options.focus(x(e)),b(e,["subToolbar","hint"])})},en=function(e,t){t.addEventListener("dblclick",function(n){n.target.tagName==="IMG"&&(e.options.image.preview?e.options.image.preview(n.target):e.options.image.isPreview&&(0,W.o)(n.target,e.options.lang,e.options.theme))})},Dt=function(e,t){t.addEventListener("blur",function(n){if(e.currentMode==="ir"){var r=e.ir.element.querySelector(".vditor-ir__node--expand");r&&r.classList.remove("vditor-ir__node--expand")}else e.currentMode==="wysiwyg"&&!e.wysiwyg.selectPopover.contains(n.relatedTarget)&&e.wysiwyg.hideComment();e[e.currentMode].range=(0,M.RN)(e),e.options.blur&&e.options.blur(x(e))})},Ot=function(e,t){t.addEventListener("dragstart",function(n){n.dataTransfer.setData(E.Y.DROP_EDITOR,E.Y.DROP_EDITOR)}),t.addEventListener("drop",function(n){n.dataTransfer.getData(E.Y.DROP_EDITOR)?ee(e):(n.dataTransfer.types.includes("Files")||n.dataTransfer.types.includes("text/html"))&&St(e,n,{pasteCode:function(r){document.execCommand("insertHTML",!1,r)}})})},Ht=function(e,t,n){t.addEventListener("copy",function(r){return n(r,e)})},Rt=function(e,t,n){t.addEventListener("cut",function(r){n(r,e),e.options.comment.enable&&e.currentMode==="wysiwyg"&&e.wysiwyg.getComments(e),document.execCommand("delete")})},ye=function(e){if(e.currentMode==="wysiwyg"&&e.options.comment.enable&&e.options.comment.adjustTop(e.wysiwyg.getComments(e,!0)),!!e.options.typewriterMode){var t=e[e.currentMode].element,n=(0,M.Ey)(t).top;e.options.height==="auto"&&!e.element.classList.contains("vditor--fullscreen")&&window.scrollTo(window.scrollX,n+e.element.offsetTop+e.toolbar.element.offsetHeight-window.innerHeight/2+10),(e.options.height!=="auto"||e.element.classList.contains("vditor--fullscreen"))&&(t.scrollTop=n+t.scrollTop-t.clientHeight/2+10)}},Nt=function(e,t){t.addEventListener("keydown",function(n){if(!n.isComposing&&e.options.keydown&&e.options.keydown(n),!((e.options.hint.extend.length>1||e.toolbar.elements.emoji)&&e.hint.select(n,e))){if(e.options.comment.enable&&e.currentMode==="wysiwyg"&&(n.key==="Backspace"||R("⌘X",n))&&e.wysiwyg.getComments(e),e.currentMode==="sv"){if(_e(e,n))return}else if(e.currentMode==="wysiwyg"){if($n(e,n))return}else if(e.currentMode==="ir"&&pe(e,n))return;if(e.options.ctrlEnter&&R("⌘Enter",n)){e.options.ctrlEnter(x(e)),n.preventDefault();return}if(R("⌘Z",n)&&!e.toolbar.elements.undo){e.undo.undo(e),n.preventDefault();return}if(R("⌘Y",n)&&!e.toolbar.elements.redo){e.undo.redo(e),n.preventDefault();return}if(n.key==="Escape"){e.hint.element.style.display==="block"?e.hint.element.style.display="none":e.options.esc&&!n.isComposing&&e.options.esc(x(e)),n.preventDefault();return}if((0,l._0)(n)&&n.altKey&&!n.shiftKey&&/^Digit[1-6]$/.test(n.code)){if(e.currentMode==="wysiwyg"){var r=n.code.replace("Digit","H");(0,s._Y)(getSelection().getRangeAt(0).startContainer,r)?Lt(e):gt(e,r),ce(e)}else e.currentMode==="sv"?nn(e,"#".repeat(parseInt(n.code.replace("Digit",""),10))+" "):e.currentMode==="ir"&&ut(e,"#".repeat(parseInt(n.code.replace("Digit",""),10))+" ");return n.preventDefault(),!0}if((0,l._0)(n)&&n.altKey&&!n.shiftKey&&/^Digit[7-9]$/.test(n.code))return n.code==="Digit7"?Ze(e,"wysiwyg",n):n.code==="Digit8"?Ze(e,"ir",n):n.code==="Digit9"&&Ze(e,"sv",n),!0;e.options.toolbar.find(function(i){if(!i.hotkey||i.toolbar){if(i.toolbar){var a=i.toolbar.find(function(o){if(!o.hotkey)return!1;if(R(o.hotkey,n))return e.toolbar.elements[o.name].children[0].dispatchEvent(new CustomEvent((0,l.y3)())),n.preventDefault(),!0});return!!a}return!1}if(R(i.hotkey,n))return e.toolbar.elements[i.name].children[0].dispatchEvent(new CustomEvent((0,l.y3)())),n.preventDefault(),!0})}})},It=function(e,t){t.addEventListener("selectstart",function(n){t.onmouseup=function(){setTimeout(function(){var r=st(e[e.currentMode].element);r.trim()?(e.currentMode==="wysiwyg"&&e.options.comment.enable&&(!(0,s.Th)(n.target,"data-type","footnotes-block")&&!(0,s.Th)(n.target,"data-type","link-ref-defs-block")?e.wysiwyg.showComment():e.wysiwyg.hideComment()),e.options.select&&e.options.select(r)):e.currentMode==="wysiwyg"&&e.options.comment.enable&&e.wysiwyg.hideComment()})}})},jt=function(e,t){var n=(0,M.RN)(e);n.extractContents(),n.insertNode(document.createTextNode(Lute.Caret)),n.insertNode(document.createTextNode(t));var r=(0,s.Th)(n.startContainer,"data-block","0");r||(r=e.sv.element);var i=e.lute.SpinVditorSVDOM(r.textContent);i="<div data-block='0'>"+i.replace(/<span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span><span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span></g,`<span data-type="newline"><br /><span style="display: none">
</span></span><span data-type="newline"><br /><span style="display: none">
</span></span></div><div data-block="0"><`)+"</div>",r.isEqualNode(e.sv.element)?r.innerHTML=i:r.outerHTML=i,ge(e.sv.element),(0,M.ir)(e.sv.element,n),ye(e)},yt=function(e,t,n){n===void 0&&(n=!0);var r=e;for(r.nodeType===3&&(r=r.parentElement);r;){if(r.getAttribute("data-type")===t)return r;n?r=r.previousElementSibling:r=r.nextElementSibling}return!1},tn=function(e,t){A("SpinVditorSVDOM",e,"argument",t.options.debugger);var n=t.lute.SpinVditorSVDOM(e);return e="<div data-block='0'>"+n.replace(/<span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span><span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span></g,`<span data-type="newline"><br /><span style="display: none">
</span></span><span data-type="newline"><br /><span style="display: none">
</span></span></div><div data-block="0"><`)+"</div>",A("SpinVditorSVDOM",e,"result",t.options.debugger),e},ir=function(e){var t=e.getAttribute("data-type"),n=e.previousElementSibling,r=t&&t!=="text"&&t!=="table"&&t!=="heading-marker"&&t!=="newline"&&t!=="yaml-front-matter-open-marker"&&t!=="yaml-front-matter-close-marker"&&t!=="code-block-info"&&t!=="code-block-close-marker"&&t!=="code-block-open-marker"?e.textContent:"",i=!1;for(t==="newline"&&(i=!0);n&&!i;){var a=n.getAttribute("data-type");if(a==="li-marker"||a==="blockquote-marker"||a==="task-marker"||a==="padding"){var o=n.textContent;if(a==="li-marker"&&(t==="code-block-open-marker"||t==="code-block-info"))r=o.replace(/\S/g," ")+r;else if(t==="code-block-close-marker"&&n.nextElementSibling.isSameNode(e)){var u=yt(e,"code-block-open-marker");u&&u.previousElementSibling&&(n=u.previousElementSibling,r=o+r)}else r=o+r}else a==="newline"&&(i=!0);n=n.previousElementSibling}return r},Se=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),e.preview.render(e);var n=x(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,l.D)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),clearTimeout(e.sv.processTimeoutId),e.sv.processTimeoutId=window.setTimeout(function(){t.enableAddUndoStack&&!e.sv.composingLock&&e.undo.addToUndoStack(e)},e.options.undoDelay)},nn=function(e,t){var n=(0,M.RN)(e),r=(0,T.f)(n.startContainer,"SPAN");r&&r.textContent.trim()!==""&&(t=`
`+t),n.collapse(!0),document.execCommand("insertHTML",!1,t)},ar=function(e,t,n,r){var i=(0,M.RN)(e),a=t.getAttribute("data-type");e.sv.element.childNodes.length===0&&(e.sv.element.innerHTML=`<span data-type="p" data-block="0"><span data-type="text"><wbr></span></span><span data-type="newline"><br><span style="display: none">
</span></span>`,(0,M.ir)(e.sv.element,i));var o=(0,s.pt)(i.startContainer),u=(0,T.f)(i.startContainer,"SPAN");if(o){if(a==="link"){var h=void 0;i.toString()===""?h="".concat(n).concat(Lute.Caret).concat(r):h="".concat(n).concat(i.toString()).concat(r.replace(")",Lute.Caret+")")),document.execCommand("insertHTML",!1,h);return}else if(a==="italic"||a==="bold"||a==="strike"||a==="inline-code"||a==="code"||a==="table"||a==="line"){var h=void 0;i.toString()===""?h="".concat(n).concat(Lute.Caret).concat(a==="code"?"":r):h="".concat(n).concat(i.toString()).concat(Lute.Caret).concat(a==="code"?"":r),a==="table"||a==="code"&&u&&u.textContent!==""?h=`

`+h:a==="line"&&(h=`

`.concat(n,`
`).concat(Lute.Caret)),document.execCommand("insertHTML",!1,h);return}else if((a==="check"||a==="list"||a==="ordered-list"||a==="quote")&&u){var f="* ";a==="check"?f="* [ ] ":a==="ordered-list"?f="1. ":a==="quote"&&(f="> ");var S=yt(u,"newline");S?S.insertAdjacentText("afterend",f):o.insertAdjacentText("afterbegin",f),ie(e);return}(0,M.ir)(e.sv.element,i),Se(e)}},rn=function(e){switch(e.currentMode){case"ir":return e.ir.element;case"wysiwyg":return e.wysiwyg.element;case"sv":return e.sv.element}},an=function(e,t){e.options.upload.setHeaders&&(e.options.upload.headers=e.options.upload.setHeaders()),e.options.upload.headers&&Object.keys(e.options.upload.headers).forEach(function(n){t.setRequestHeader(n,e.options.upload.headers[n])})},or=function(e,t,n,r){function i(a){return a instanceof n?a:new n(function(o){o(a)})}return new(n||(n=Promise))(function(a,o){function u(S){try{f(r.next(S))}catch(k){o(k)}}function h(S){try{f(r.throw(S))}catch(k){o(k)}}function f(S){S.done?a(S.value):i(S.value).then(u,h)}f((r=r.apply(e,t||[])).next())})},sr=function(e,t){var n={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function u(f){return function(S){return h([f,S])}}function h(f){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,f[0]&&(n=0)),n;)try{if(r=1,i&&(a=f[0]&2?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[f[0]&2,a.value]),f[0]){case 0:case 1:a=f;break;case 4:return n.label++,{value:f[1],done:!1};case 5:n.label++,i=f[1],f=[0];continue;case 7:f=n.ops.pop(),n.trys.pop();continue;default:if(a=n.trys,!(a=a.length>0&&a[a.length-1])&&(f[0]===6||f[0]===2)){n=0;continue}if(f[0]===3&&(!a||f[1]>a[0]&&f[1]<a[3])){n.label=f[1];break}if(f[0]===6&&n.label<a[1]){n.label=a[1],a=f;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(f);break}a[2]&&n.ops.pop(),n.trys.pop();continue}f=t.call(e,n)}catch(S){f=[6,S],i=0}finally{r=a=0}if(f[0]&5)throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}},lr=function(){function e(){this.isUploading=!1,this.element=document.createElement("div"),this.element.className="vditor-upload"}return e}(),cr=function(e,t){e.tip.hide();var n=[],r="",i="";e.options.lang,e.options;for(var a=function(h,f){var S=t[f],k=!0;S.name||(r+="<li>".concat(window.VditorI18n.nameEmpty,"</li>"),k=!1),S.size>e.options.upload.max&&(r+="<li>".concat(S.name," ").concat(window.VditorI18n.over," ").concat(e.options.upload.max/1024/1024,"M</li>"),k=!1);var I=S.name.lastIndexOf("."),N=S.name.substr(I),H=e.options.upload.filename(S.name.substr(0,I))+N;if(e.options.upload.accept){var F=e.options.upload.accept.split(",").some(function(B){var O=B.trim();if(O.indexOf(".")===0){if(N.toLowerCase()===O.toLowerCase())return!0}else if(S.type.split("/")[0]===O.split("/")[0])return!0;return!1});F||(r+="<li>".concat(S.name," ").concat(window.VditorI18n.fileTypeError,"</li>"),k=!1)}k&&(n.push(S),i+="<li>".concat(H," ").concat(window.VditorI18n.uploading,"</li>"))},o=t.length,u=0;u<o;u++)a(o,u);return e.tip.show("<ul>".concat(r).concat(i,"</ul>")),n},ur=function(e,t){var n=rn(t);n.focus();var r=JSON.parse(e),i="";r.code===1&&(i="".concat(r.msg)),r.data.errFiles&&r.data.errFiles.length>0&&(i="<ul><li>".concat(i,"</li>"),r.data.errFiles.forEach(function(o){var u=o.lastIndexOf("."),h=t.options.upload.filename(o.substr(0,u))+o.substr(u);i+="<li>".concat(h," ").concat(window.VditorI18n.uploadError,"</li>")}),i+="</ul>"),i?t.tip.show(i):t.tip.hide();var a="";Object.keys(r.data.succMap).forEach(function(o){var u=r.data.succMap[o],h=o.lastIndexOf("."),f=o.substr(h),S=t.options.upload.filename(o.substr(0,h))+f;f=f.toLowerCase(),f.indexOf(".wav")===0||f.indexOf(".mp3")===0||f.indexOf(".ogg")===0?t.currentMode==="wysiwyg"?a+=`<div class="vditor-wysiwyg__block" data-type="html-block"
 data-block="0"><pre><code>&lt;audio controls="controls" src="`.concat(u,'"&gt;&lt;/audio&gt;</code></pre><pre class="vditor-wysiwyg__preview" data-render="1"><audio controls="controls" src="').concat(u,`"></audio></pre></div>
`):t.currentMode==="ir"?a+='<audio controls="controls" src="'.concat(u,`"></audio>
`):a+="[".concat(S,"](").concat(u,`)
`):f.indexOf(".apng")===0||f.indexOf(".bmp")===0||f.indexOf(".gif")===0||f.indexOf(".ico")===0||f.indexOf(".cur")===0||f.indexOf(".jpg")===0||f.indexOf(".jpeg")===0||f.indexOf(".jfif")===0||f.indexOf(".pjp")===0||f.indexOf(".pjpeg")===0||f.indexOf(".png")===0||f.indexOf(".svg")===0||f.indexOf(".webp")===0?t.currentMode==="wysiwyg"?a+='<img alt="'.concat(S,'" src="').concat(u,`">
`):a+="![".concat(S,"](").concat(u,`)
`):t.currentMode==="wysiwyg"?a+='<a href="'.concat(u,'">').concat(S,`</a>
`):a+="[".concat(S,"](").concat(u,`)
`)}),(0,M.jl)(t.upload.range),document.execCommand("insertHTML",!1,a),t.upload.range=getSelection().getRangeAt(0).cloneRange()},Pt=function(e,t,n){return or(void 0,void 0,void 0,function(){var r,i,H,a,o,o,u,h,f,S,k,I,N,H,F,B;return sr(this,function(O){switch(O.label){case 0:for(r=[],i=e.options.upload.multiple===!0?t.length:1,H=0;H<i;H++)a=t[H],a instanceof DataTransferItem&&(a=a.getAsFile()),r.push(a);return e.options.upload.handler?[4,e.options.upload.handler(r)]:[3,2];case 1:return o=O.sent(),n&&(n.value=""),typeof o=="string"?(e.tip.show(o),[2]):[2];case 2:return!e.options.upload.url||!e.upload?(n&&(n.value=""),e.tip.show("please config: options.upload.url"),[2]):e.options.upload.file?[4,e.options.upload.file(r)]:[3,4];case 3:r=O.sent(),O.label=4;case 4:if(e.options.upload.validate&&(o=e.options.upload.validate(r),typeof o=="string"))return e.tip.show(o),[2];if(u=rn(e),e.upload.range=(0,M.RN)(e),h=cr(e,r),h.length===0)return n&&(n.value=""),[2];for(f=new FormData,S=e.options.upload.extraData,k=0,I=Object.keys(S);k<I.length;k++)N=I[k],f.append(N,S[N]);for(H=0,F=h.length;H<F;H++)f.append(e.options.upload.fieldName,h[H]);return B=new XMLHttpRequest,B.open("POST",e.options.upload.url),e.options.upload.token&&B.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&(B.withCredentials=!0),an(e,B),e.upload.isUploading=!0,u.setAttribute("contenteditable","false"),B.onreadystatechange=function(){if(B.readyState===XMLHttpRequest.DONE){if(e.upload.isUploading=!1,u.setAttribute("contenteditable","true"),B.status>=200&&B.status<300)if(e.options.upload.success)e.options.upload.success(u,B.responseText);else{var z=B.responseText;e.options.upload.format&&(z=e.options.upload.format(t,B.responseText)),ur(z,e)}else e.options.upload.error?e.options.upload.error(B.responseText):e.tip.show(B.responseText);n&&(n.value=""),e.upload.element.style.display="none"}},B.upload.onprogress=function(z){if(z.lengthComputable){var K=z.loaded/z.total*100;e.upload.element.style.display="block";var Z=e.upload.element;Z.style.width=K+"%"}},B.send(f),[2]}})})},bt=function(e,t,n){var r,i=(0,s.pt)(t.startContainer);if(i||(i=e.wysiwyg.element),n&&n.inputType!=="formatItalic"&&n.inputType!=="deleteByDrag"&&n.inputType!=="insertFromDrop"&&n.inputType!=="formatBold"&&n.inputType!=="formatRemove"&&n.inputType!=="formatStrikeThrough"&&n.inputType!=="insertUnorderedList"&&n.inputType!=="insertOrderedList"&&n.inputType!=="formatOutdent"&&n.inputType!=="formatIndent"&&n.inputType!==""||!n){var a=Gn(t.startContainer);a&&a.remove(),e.wysiwyg.element.querySelectorAll("wbr").forEach(function(K){K.remove()}),t.insertNode(document.createElement("wbr")),i.querySelectorAll("[style]").forEach(function(K){K.removeAttribute("style")}),i.querySelectorAll(".vditor-comment").forEach(function(K){K.textContent.trim()===""&&(K.classList.remove("vditor-comment","vditor-comment--focus"),K.removeAttribute("data-cmtids"))}),(r=i.previousElementSibling)===null||r===void 0||r.querySelectorAll(".vditor-comment").forEach(function(K){K.textContent.trim()===""&&(K.classList.remove("vditor-comment","vditor-comment--focus"),K.removeAttribute("data-cmtids"))});var o="";i.getAttribute("data-type")==="link-ref-defs-block"&&(i=e.wysiwyg.element);var u=i.isEqualNode(e.wysiwyg.element),h=(0,s.Th)(i,"data-type","footnotes-block");if(u)o=i.innerHTML;else{var f=(0,s.KR)(t.startContainer);if(f&&!h){var S=(0,T.f)(t.startContainer,"BLOCKQUOTE");S?i=(0,s.pt)(t.startContainer)||i:i=f}if(h&&(i=h),o=i.outerHTML,i.tagName==="UL"||i.tagName==="OL"){var k=i.previousElementSibling,I=i.nextElementSibling;k&&(k.tagName==="UL"||k.tagName==="OL")&&(o=k.outerHTML+o,k.remove()),I&&(I.tagName==="UL"||I.tagName==="OL")&&(o=o+I.outerHTML,I.remove()),o=o.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}i.innerText.startsWith("```")||(e.wysiwyg.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(K){K&&!i.isEqualNode(K)&&(o+=K.outerHTML,K.remove())}),e.wysiwyg.element.querySelectorAll("[data-type='footnotes-block']").forEach(function(K){K&&!i.isEqualNode(K)&&(o+=K.outerHTML,K.remove())}))}if(o=o.replace(/<\/(strong|b)><strong data-marker="\W{2}">/g,"").replace(/<\/(em|i)><em data-marker="\W{1}">/g,"").replace(/<\/(s|strike)><s data-marker="~{1,2}">/g,""),o==='<p data-block="0">```<wbr></p>'&&e.hint.recentLanguage&&(o='<p data-block="0">```<wbr></p>'.replace("```","```"+e.hint.recentLanguage)),A("SpinVditorDOM",o,"argument",e.options.debugger),o=e.lute.SpinVditorDOM(o),A("SpinVditorDOM",o,"result",e.options.debugger),u)i.innerHTML=o;else if(i.outerHTML=o,h){var N=(0,s.Ab)(e.wysiwyg.element.querySelector("wbr"),"LI");if(N){var H=e.wysiwyg.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'.concat(N.getAttribute("data-marker"),'"]'));H&&H.setAttribute("aria-label",N.textContent.trim().substr(0,24))}}var F,B=e.wysiwyg.element.querySelectorAll("[data-type='link-ref-defs-block']");B.forEach(function(K,Z){Z===0?F=K:(F.insertAdjacentHTML("beforeend",K.innerHTML),K.remove())}),B.length>0&&e.wysiwyg.element.insertAdjacentElement("beforeend",B[0]);var O,z=e.wysiwyg.element.querySelectorAll("[data-type='footnotes-block']");z.forEach(function(K,Z){Z===0?O=K:(O.insertAdjacentHTML("beforeend",K.innerHTML),K.remove())}),z.length>0&&e.wysiwyg.element.insertAdjacentElement("beforeend",z[0]),(0,M.ir)(e.wysiwyg.element,t),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(K){Ee(K,e)}),n&&(n.inputType==="deleteContentBackward"||n.inputType==="deleteContentForward")&&e.options.comment.enable&&(e.wysiwyg.triggerRemoveComment(e),e.options.comment.adjustTop(e.wysiwyg.getComments(e,!0)))}Be(e),ce(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},fr=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},dr=function(e,t,n,r){function i(a){return a instanceof n?a:new n(function(o){o(a)})}return new(n||(n=Promise))(function(a,o){function u(S){try{f(r.next(S))}catch(k){o(k)}}function h(S){try{f(r.throw(S))}catch(k){o(k)}}function f(S){S.done?a(S.value):i(S.value).then(u,h)}f((r=r.apply(e,t||[])).next())})},pr=function(e,t){var n={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function u(f){return function(S){return h([f,S])}}function h(f){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,f[0]&&(n=0)),n;)try{if(r=1,i&&(a=f[0]&2?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[f[0]&2,a.value]),f[0]){case 0:case 1:a=f;break;case 4:return n.label++,{value:f[1],done:!1};case 5:n.label++,i=f[1],f=[0];continue;case 7:f=n.ops.pop(),n.trys.pop();continue;default:if(a=n.trys,!(a=a.length>0&&a[a.length-1])&&(f[0]===6||f[0]===2)){n=0;continue}if(f[0]===3&&(!a||f[1]>a[0]&&f[1]<a[3])){n.label=f[1];break}if(f[0]===6&&n.label<a[1]){n.label=a[1],a=f;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(f);break}a[2]&&n.ops.pop(),n.trys.pop();continue}f=t.call(e,n)}catch(S){f=[6,S],i=0}finally{r=a=0}if(f[0]&5)throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}},on=function(e,t,n){if(e.keyCode===229&&e.code===""&&e.key==="Unidentified"&&t.currentMode!=="sv"){var r=(0,s.pt)(n);if(r&&r.textContent.trim()==="")return t[t.currentMode].composingLock=!0,!1}return!0},sn=function(e,t,n){if(!(n.key==="Enter"||n.key==="Tab"||n.key==="Backspace"||n.key.indexOf("Arrow")>-1||(0,l._0)(n)||n.key==="Escape"||n.shiftKey||n.altKey)){var r=(0,s._Y)(e.startContainer,"P")||(0,s._Y)(e.startContainer,"LI");if(r&&(0,M.ED)(r,t[t.currentMode].element,e).start===0){r.nodeValue&&(r.nodeValue=r.nodeValue.replace(/\u2006/g,""));var i=document.createTextNode(E.Y.ZWSP);e.insertNode(i),e.setStartAfter(i)}}},ln=function(e,t){if(t==="ArrowDown"||t==="ArrowUp"){var n=(0,s.Th)(e.startContainer,"data-type","math-inline")||(0,s.Th)(e.startContainer,"data-type","html-entity")||(0,s.Th)(e.startContainer,"data-type","html-inline");n&&(t==="ArrowDown"&&e.setStartAfter(n.parentElement),t==="ArrowUp"&&e.setStartBefore(n.parentElement))}},lt=function(e,t){var n=(0,M.RN)(e),r=(0,s.pt)(n.startContainer);r&&(r.insertAdjacentHTML(t,'<p data-block="0">'.concat(E.Y.ZWSP,`<wbr>
</p>`)),(0,M.ir)(e[e.currentMode].element,n),Qe(e),ee(e))},hr=function(e){var t=(0,s._Y)(e,"TABLE");return t&&t.rows[0].cells[0].isSameNode(e)?t:!1},mr=function(e){var t=(0,s._Y)(e,"TABLE");return t&&t.lastElementChild.lastElementChild.lastElementChild.isSameNode(e)?t:!1},cn=function(e,t,n){n===void 0&&(n=!0);var r=e.previousElementSibling;return r||(e.parentElement.previousElementSibling?r=e.parentElement.previousElementSibling.lastElementChild:e.parentElement.parentElement.tagName==="TBODY"&&e.parentElement.parentElement.previousElementSibling?r=e.parentElement.parentElement.previousElementSibling.lastElementChild.lastElementChild:r=null),r&&(t.selectNodeContents(r),n||t.collapse(!1),(0,M.jl)(t)),r},ct=function(e,t,n,r,i){var a=(0,M.ED)(r,e[e.currentMode].element,n);if(t.key==="ArrowDown"&&r.textContent.trimRight().substr(a.start).indexOf(`
`)===-1||t.key==="ArrowRight"&&a.start>=r.textContent.trimRight().length){var o=i.nextElementSibling;return!o||o&&(o.tagName==="TABLE"||o.getAttribute("data-type"))?(i.insertAdjacentHTML("afterend",'<p data-block="0">'.concat(E.Y.ZWSP,"<wbr></p>")),(0,M.ir)(e[e.currentMode].element,n)):(n.selectNodeContents(o),n.collapse(!0),(0,M.jl)(n)),t.preventDefault(),!0}return!1},et=function(e,t,n,r,i){var a=(0,M.ED)(r,e[e.currentMode].element,n);if(t.key==="ArrowUp"&&r.textContent.substr(0,a.start).indexOf(`
`)===-1||(t.key==="ArrowLeft"||t.key==="Backspace"&&n.toString()==="")&&a.start===0){var o=i.previousElementSibling;return!o||o&&(o.tagName==="TABLE"||o.getAttribute("data-type"))?(i.insertAdjacentHTML("beforebegin",'<p data-block="0">'.concat(E.Y.ZWSP,"<wbr></p>")),(0,M.ir)(e[e.currentMode].element,n)):(n.selectNodeContents(o),n.collapse(!1),(0,M.jl)(n)),t.preventDefault(),!0}return!1},wt=function(e,t,n,r){r===void 0&&(r=!0);var i=(0,s._Y)(t.startContainer,"LI");if(e[e.currentMode].element.querySelectorAll("wbr").forEach(function(S){S.remove()}),t.insertNode(document.createElement("wbr")),r&&i){for(var a="",o=0;o<i.parentElement.childElementCount;o++){var u=i.parentElement.children[o].querySelector("input");u&&u.remove(),a+='<p data-block="0">'.concat(i.parentElement.children[o].innerHTML.trimLeft(),"</p>")}i.parentElement.insertAdjacentHTML("beforebegin",a),i.parentElement.remove()}else if(i)if(n==="check")i.parentElement.querySelectorAll("li").forEach(function(S){S.insertAdjacentHTML("afterbegin",'<input type="checkbox" />'.concat(S.textContent.indexOf(" ")===0?"":" ")),S.classList.add("vditor-task")});else{i.querySelector("input")&&i.parentElement.querySelectorAll("li").forEach(function(S){S.querySelector("input").remove(),S.classList.remove("vditor-task")});var f=void 0;n==="list"?(f=document.createElement("ul"),f.setAttribute("data-marker","*")):(f=document.createElement("ol"),f.setAttribute("data-marker","1.")),f.setAttribute("data-block","0"),f.setAttribute("data-tight",i.parentElement.getAttribute("data-tight")),f.innerHTML=i.parentElement.innerHTML,i.parentElement.parentNode.replaceChild(f,i.parentElement)}else{var h=(0,s.Th)(t.startContainer,"data-block","0");h||(e[e.currentMode].element.querySelector("wbr").remove(),h=e[e.currentMode].element.querySelector("p"),h.innerHTML="<wbr>"),n==="check"?(h.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li class="vditor-task"><input type="checkbox" /> '.concat(h.innerHTML,"</li></ul>")),h.remove()):n==="list"?(h.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li>'.concat(h.innerHTML,"</li></ul>")),h.remove()):n==="ordered-list"&&(h.insertAdjacentHTML("beforebegin",'<ol data-block="0"><li>'.concat(h.innerHTML,"</li></ol>")),h.remove())}},un=function(e,t,n){var r=t.previousElementSibling;if(t&&r){var i=[t];Array.from(n.cloneContents().children).forEach(function(h,f){h.nodeType!==3&&t&&h.textContent.trim()!==""&&t.getAttribute("data-node-id")===h.getAttribute("data-node-id")&&(f!==0&&i.push(t),t=t.nextElementSibling)}),e[e.currentMode].element.querySelectorAll("wbr").forEach(function(h){h.remove()}),n.insertNode(document.createElement("wbr"));var a=r.parentElement,o="";i.forEach(function(h){var f=h.getAttribute("data-marker");f.length!==1&&(f="1".concat(f.slice(-1))),o+='<li data-node-id="'.concat(h.getAttribute("data-node-id"),'" data-marker="').concat(f,'">').concat(h.innerHTML,"</li>"),h.remove()}),r.insertAdjacentHTML("beforeend","<".concat(a.tagName,' data-block="0">').concat(o,"</").concat(a.tagName,">")),e.currentMode==="wysiwyg"?a.outerHTML=e.lute.SpinVditorDOM(a.outerHTML):a.outerHTML=e.lute.SpinVditorIRDOM(a.outerHTML),(0,M.ir)(e[e.currentMode].element,n);var u=(0,s.KR)(n.startContainer);u&&u.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(h){Ee(h,e),e.currentMode==="wysiwyg"&&h.previousElementSibling.setAttribute("style","display:none")}),ee(e),Qe(e)}else e[e.currentMode].element.focus()},Bt=function(e,t,n,r){var i=(0,s._Y)(t.parentElement,"LI");if(i){e[e.currentMode].element.querySelectorAll("wbr").forEach(function(k){k.remove()}),n.insertNode(document.createElement("wbr"));var a=t.parentElement,o=a.cloneNode(),u=[t];Array.from(n.cloneContents().children).forEach(function(k,I){k.nodeType!==3&&t&&k.textContent.trim()!==""&&t.getAttribute("data-node-id")===k.getAttribute("data-node-id")&&(I!==0&&u.push(t),t=t.nextElementSibling)});var h=!1,f="";a.querySelectorAll("li").forEach(function(k){h&&(f+=k.outerHTML,!k.nextElementSibling&&!k.previousElementSibling?k.parentElement.remove():k.remove()),k.isSameNode(u[u.length-1])&&(h=!0)}),u.reverse().forEach(function(k){i.insertAdjacentElement("afterend",k)}),f&&(o.innerHTML=f,u[0].insertAdjacentElement("beforeend",o)),e.currentMode==="wysiwyg"?r.outerHTML=e.lute.SpinVditorDOM(r.outerHTML):r.outerHTML=e.lute.SpinVditorIRDOM(r.outerHTML),(0,M.ir)(e[e.currentMode].element,n);var S=(0,s.KR)(n.startContainer);S&&S.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(k){Ee(k,e),e.currentMode==="wysiwyg"&&k.previousElementSibling.setAttribute("style","display:none")}),ee(e),Qe(e)}else e[e.currentMode].element.focus()},vt=function(e,t){for(var n=getSelection().getRangeAt(0).startContainer.parentElement,r=e.rows[0].cells.length,i=e.rows.length,a=0,o=0;o<i;o++)for(var u=0;u<r;u++)if(e.rows[o].cells[u].isSameNode(n)){a=u;break}for(var h=0;h<i;h++)e.rows[h].cells[a].setAttribute("align",t)},Ut=function(e){var t=e.trimRight().split(`
`).pop();return t===""?!1:(t.replace(/ |-/g,"")===""||t.replace(/ |_/g,"")===""||t.replace(/ |\*/g,"")==="")&&t.replace(/ /g,"").length>2?!(t.indexOf("-")>-1&&t.trimLeft().indexOf(" ")===-1&&e.trimRight().split(`
`).length>1||t.indexOf("    ")===0||t.indexOf("	")===0):!1},Vt=function(e){var t=e.trimRight().split(`
`);return e=t.pop(),e.indexOf("    ")===0||e.indexOf("	")===0||(e=e.trimLeft(),e===""||t.length===0)?!1:e.replace(/-/g,"")===""||e.replace(/=/g,"")===""},ee=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),e.currentMode==="wysiwyg"?ce(e,t):e.currentMode==="ir"?Ke(e,t):e.currentMode==="sv"&&Se(e,t)},fn=function(e,t,n,r){var i,a=e.startContainer,o=(0,s._Y)(a,"LI");if(o){if(!(0,l._0)(r)&&!r.altKey&&r.key==="Enter"&&!r.shiftKey&&n&&o.contains(n)&&n.nextElementSibling)return o&&!o.textContent.endsWith(`
`)&&o.insertAdjacentText("beforeend",`
`),e.insertNode(document.createTextNode(`

`)),e.collapse(!1),ee(t),r.preventDefault(),!0;if(!(0,l._0)(r)&&!r.shiftKey&&!r.altKey&&r.key==="Backspace"&&!o.previousElementSibling&&e.toString()===""&&(0,M.ED)(o,t[t.currentMode].element,e).start===0)return o.nextElementSibling?(o.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'.concat(o.innerHTML,"</p>")),o.remove()):o.parentElement.outerHTML='<p data-block="0"><wbr>'.concat(o.innerHTML,"</p>"),(0,M.ir)(t[t.currentMode].element,e),ee(t),r.preventDefault(),!0;if(!(0,l._0)(r)&&!r.shiftKey&&!r.altKey&&r.key==="Backspace"&&o.textContent.trim().replace(E.Y.ZWSP,"")===""&&e.toString()===""&&((i=o.previousElementSibling)===null||i===void 0?void 0:i.tagName)==="LI")return o.previousElementSibling.insertAdjacentText("beforeend",`

`),e.selectNodeContents(o.previousElementSibling),e.collapse(!1),o.remove(),(0,M.ir)(t[t.currentMode].element,e),ee(t),r.preventDefault(),!0;if(!(0,l._0)(r)&&!r.altKey&&r.key==="Tab"){var u=!1;if((e.startOffset===0&&(a.nodeType===3&&!a.previousSibling||a.nodeType!==3&&a.nodeName==="LI")||o.classList.contains("vditor-task")&&e.startOffset===1&&a.previousSibling.nodeType!==3&&a.previousSibling.tagName==="INPUT")&&(u=!0),u||e.toString()!=="")return r.shiftKey?Bt(t,o,e,o.parentElement):un(t,o,e),r.preventDefault(),!0}}return!1},Kt=function(e,t,n){if(e.options.tab&&n.key==="Tab")return n.shiftKey||(t.toString()===""?(t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1)):(t.extractContents(),t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1))),(0,M.jl)(t),ee(e),n.preventDefault(),!0},dn=function(e,t,n,r){if(n){if(!(0,l._0)(e)&&!e.altKey&&e.key==="Enter"){var i=String.raw(_n||(_n=fr(["",""],["",""])),n.textContent).replace(/\\\|/g,"").trim(),a=i.split("|");if(i.startsWith("|")&&i.endsWith("|")&&a.length>3){var o=a.map(function(){return"---"}).join("|");return o=n.textContent+`
`+o.substring(3,o.length-3)+`
|<wbr>`,n.outerHTML=t.lute.SpinVditorDOM(o),(0,M.ir)(t[t.currentMode].element,r),ee(t),ye(t),e.preventDefault(),!0}if(Ut(n.innerHTML)&&n.previousElementSibling){var u="",h=n.innerHTML.trimRight().split(`
`);return h.length>1&&(h.pop(),u='<p data-block="0">'.concat(h.join(`
`),"</p>")),n.insertAdjacentHTML("afterend","".concat(u,`<hr data-block="0"><p data-block="0"><wbr>
</p>`)),n.remove(),(0,M.ir)(t[t.currentMode].element,r),ee(t),ye(t),e.preventDefault(),!0}if(Vt(n.innerHTML))return t.currentMode==="wysiwyg"?n.outerHTML=t.lute.SpinVditorDOM(n.innerHTML+`<p data-block="0"><wbr>
</p>`):n.outerHTML=t.lute.SpinVditorIRDOM(n.innerHTML+`<p data-block="0"><wbr>
</p>`),(0,M.ir)(t[t.currentMode].element,r),ee(t),ye(t),e.preventDefault(),!0}if(r.collapsed&&n.previousElementSibling&&e.key==="Backspace"&&!(0,l._0)(e)&&!e.altKey&&!e.shiftKey&&n.textContent.trimRight().split(`
`).length>1&&(0,M.ED)(n,t[t.currentMode].element,r).start===0){var f=(0,s.Rp)(n.previousElementSibling);return f.textContent.endsWith(`
`)||(f.textContent=f.textContent+`
`),f.parentElement.insertAdjacentHTML("beforeend","<wbr>".concat(n.innerHTML)),n.remove(),(0,M.ir)(t[t.currentMode].element,r),!1}return!1}},pn=function(e,t,n){for(var r="",i=0;i<n.parentElement.childElementCount;i++)r+='<td align="'.concat(n.parentElement.children[i].getAttribute("align"),'"> </td>');n.tagName==="TH"?n.parentElement.parentElement.insertAdjacentHTML("afterend","<tbody><tr>".concat(r,"</tr></tbody>")):n.parentElement.insertAdjacentHTML("afterend","<tr>".concat(r,"</tr>")),ee(e)},hn=function(e,t,n){for(var r="",i=0;i<n.parentElement.childElementCount;i++)n.tagName==="TH"?r+='<th align="'.concat(n.parentElement.children[i].getAttribute("align"),'"> </th>'):r+='<td align="'.concat(n.parentElement.children[i].getAttribute("align"),'"> </td>');if(n.tagName==="TH"){n.parentElement.parentElement.insertAdjacentHTML("beforebegin","<thead><tr>".concat(r,"</tr></thead>")),t.insertNode(document.createElement("wbr"));var a=n.parentElement.innerHTML.replace(/<th>/g,"<td>").replace(/<\/th>/g,"</td>");n.parentElement.parentElement.nextElementSibling.insertAdjacentHTML("afterbegin",a),n.parentElement.parentElement.remove(),(0,M.ir)(e.ir.element,t)}else n.parentElement.insertAdjacentHTML("beforebegin","<tr>".concat(r,"</tr>"));ee(e)},Et=function(e,t,n,r){r===void 0&&(r="afterend");for(var i=0,a=n.previousElementSibling;a;)i++,a=a.previousElementSibling;for(var o=0;o<t.rows.length;o++)o===0?t.rows[o].cells[i].insertAdjacentHTML(r,"<th> </th>"):t.rows[o].cells[i].insertAdjacentHTML(r,"<td> </td>");ee(e)},mn=function(e,t,n){if(n.tagName==="TD"){var r=n.parentElement.parentElement;n.parentElement.previousElementSibling?t.selectNodeContents(n.parentElement.previousElementSibling.lastElementChild):t.selectNodeContents(r.previousElementSibling.lastElementChild.lastElementChild),r.childElementCount===1?r.remove():n.parentElement.remove(),t.collapse(!1),(0,M.jl)(t),ee(e)}},gn=function(e,t,n,r){for(var i=0,a=r.previousElementSibling;a;)i++,a=a.previousElementSibling;(r.previousElementSibling||r.nextElementSibling)&&(t.selectNodeContents(r.previousElementSibling||r.nextElementSibling),t.collapse(!0));for(var o=0;o<n.rows.length;o++){var u=n.rows[o].cells;if(u.length===1){n.remove(),Qe(e);break}u[i].remove()}(0,M.jl)(t),ee(e)},yn=function(e,t,n){var r=n.startContainer,i=(0,s._Y)(r,"TD")||(0,s._Y)(r,"TH");if(i){if(!(0,l._0)(t)&&!t.altKey&&t.key==="Enter"){(!i.lastElementChild||i.lastElementChild&&(!i.lastElementChild.isSameNode(i.lastChild)||i.lastElementChild.tagName!=="BR"))&&i.insertAdjacentHTML("beforeend","<br>");var a=document.createElement("br");return n.insertNode(a),n.setStartAfter(a),ee(e),ye(e),t.preventDefault(),!0}if(t.key==="Tab"){if(t.shiftKey)return cn(i,n),t.preventDefault(),!0;var o=i.nextElementSibling;return o||(i.parentElement.nextElementSibling?o=i.parentElement.nextElementSibling.firstElementChild:i.parentElement.parentElement.tagName==="THEAD"&&i.parentElement.parentElement.nextElementSibling?o=i.parentElement.parentElement.nextElementSibling.firstElementChild.firstElementChild:o=null),o&&(n.selectNodeContents(o),(0,M.jl)(n)),t.preventDefault(),!0}var u=i.parentElement.parentElement.parentElement;if(t.key==="ArrowUp"){if(t.preventDefault(),i.tagName==="TH")return u.previousElementSibling?(n.selectNodeContents(u.previousElementSibling),n.collapse(!1),(0,M.jl)(n)):lt(e,"beforebegin"),!0;for(var h=0,f=i.parentElement;h<f.cells.length&&!f.cells[h].isSameNode(i);h++);var S=f.previousElementSibling;return S||(S=f.parentElement.previousElementSibling.firstChild),n.selectNodeContents(S.cells[h]),n.collapse(!1),(0,M.jl)(n),!0}if(t.key==="ArrowDown"){t.preventDefault();var f=i.parentElement;if(!f.nextElementSibling&&i.tagName==="TD")return u.nextElementSibling?(n.selectNodeContents(u.nextElementSibling),n.collapse(!0),(0,M.jl)(n)):lt(e,"afterend"),!0;for(var h=0;h<f.cells.length&&!f.cells[h].isSameNode(i);h++);var o=f.nextElementSibling;return o||(o=f.parentElement.nextElementSibling.firstChild),n.selectNodeContents(o.cells[h]),n.collapse(!0),(0,M.jl)(n),!0}if(e.currentMode==="wysiwyg"&&!(0,l._0)(t)&&t.key==="Enter"&&!t.shiftKey&&t.altKey){var k=e.wysiwyg.popover.querySelector(".vditor-input");return k.focus(),k.select(),t.preventDefault(),!0}if(!(0,l._0)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Backspace"&&n.startOffset===0&&n.toString()===""){var I=cn(i,n,!1);return!I&&u&&(u.textContent.trim()===""?(u.outerHTML=`<p data-block="0"><wbr>
</p>`,(0,M.ir)(e[e.currentMode].element,n)):(n.setStartBefore(u),n.collapse(!0)),ee(e)),t.preventDefault(),!0}if(R("⇧⌘F",t))return hn(e,n,i),t.preventDefault(),!0;if(R("⌘=",t))return pn(e,n,i),t.preventDefault(),!0;if(R("⇧⌘G",t))return Et(e,u,i,"beforebegin"),t.preventDefault(),!0;if(R("⇧⌘=",t))return Et(e,u,i),t.preventDefault(),!0;if(R("⌘-",t))return mn(e,n,i),t.preventDefault(),!0;if(R("⇧⌘-",t))return gn(e,n,u,i),t.preventDefault(),!0;if(R("⇧⌘L",t)){if(e.currentMode==="ir")return vt(u,"left"),ee(e),t.preventDefault(),!0;var N=e.wysiwyg.popover.querySelector('[data-type="left"]');if(N)return N.click(),t.preventDefault(),!0}if(R("⇧⌘C",t)){if(e.currentMode==="ir")return vt(u,"center"),ee(e),t.preventDefault(),!0;var N=e.wysiwyg.popover.querySelector('[data-type="center"]');if(N)return N.click(),t.preventDefault(),!0}if(R("⇧⌘R",t)){if(e.currentMode==="ir")return vt(u,"right"),ee(e),t.preventDefault(),!0;var N=e.wysiwyg.popover.querySelector('[data-type="right"]');if(N)return N.click(),t.preventDefault(),!0}}return!1},bn=function(e,t,n,r){if(n.tagName==="PRE"&&R("⌘A",t))return r.selectNodeContents(n.firstElementChild),t.preventDefault(),!0;if(e.options.tab&&t.key==="Tab"&&!t.shiftKey&&r.toString()==="")return r.insertNode(document.createTextNode(e.options.tab)),r.collapse(!1),ee(e),t.preventDefault(),!0;if(t.key==="Backspace"&&!(0,l._0)(t)&&!t.shiftKey&&!t.altKey){var i=(0,M.ED)(n,e[e.currentMode].element,r);if((i.start===0||i.start===1&&n.innerText===`
`)&&r.toString()==="")return n.parentElement.outerHTML='<p data-block="0"><wbr>'.concat(n.firstElementChild.innerHTML,"</p>"),(0,M.ir)(e[e.currentMode].element,r),ee(e),t.preventDefault(),!0}return!(0,l._0)(t)&&!t.altKey&&t.key==="Enter"?(n.firstElementChild.textContent.endsWith(`
`)||n.firstElementChild.insertAdjacentText("beforeend",`
`),r.extractContents(),r.insertNode(document.createTextNode(`
`)),r.collapse(!1),(0,M.jl)(r),(0,l.gm)()||(e.currentMode==="wysiwyg"?bt(e,r):Fe(e,r)),ye(e),t.preventDefault(),!0):!1},wn=function(e,t,n,r){var i=t.startContainer,a=(0,s._Y)(i,"BLOCKQUOTE");if(a&&t.toString()===""){if(n.key==="Backspace"&&!(0,l._0)(n)&&!n.shiftKey&&!n.altKey&&(0,M.ED)(a,e[e.currentMode].element,t).start===0)return t.insertNode(document.createElement("wbr")),a.outerHTML=a.innerHTML,(0,M.ir)(e[e.currentMode].element,t),ee(e),n.preventDefault(),!0;if(r&&n.key==="Enter"&&!(0,l._0)(n)&&!n.shiftKey&&!n.altKey&&r.parentElement.tagName==="BLOCKQUOTE"){var o=!1;if(r.innerHTML.replace(E.Y.ZWSP,"")===`
`||r.innerHTML.replace(E.Y.ZWSP,"")===""?(o=!0,r.remove()):r.innerHTML.endsWith(`

`)&&(0,M.ED)(r,e[e.currentMode].element,t).start===r.textContent.length-1&&(r.innerHTML=r.innerHTML.substr(0,r.innerHTML.length-2),o=!0),o)return a.insertAdjacentHTML("afterend",'<p data-block="0">'.concat(E.Y.ZWSP,`<wbr>
</p>`)),(0,M.ir)(e[e.currentMode].element,t),ee(e),n.preventDefault(),!0}var u=(0,s.pt)(i);if(e.currentMode==="wysiwyg"&&u&&R("⇧⌘;",n))return t.insertNode(document.createElement("wbr")),u.outerHTML='<blockquote data-block="0">'.concat(u.outerHTML,"</blockquote>"),(0,M.ir)(e.wysiwyg.element,t),ce(e),n.preventDefault(),!0;if(ct(e,n,t,a,a)||et(e,n,t,a,a))return!0}return!1},vn=function(e,t,n){var r=t.startContainer,i=(0,s._Y)(r,"li");if(i&&i.classList.contains("vditor-task")){if(R("⇧⌘J",n)){var a=i.firstElementChild;return a.checked?a.removeAttribute("checked"):a.setAttribute("checked","checked"),ee(e),n.preventDefault(),!0}if(n.key==="Backspace"&&!(0,l._0)(n)&&!n.shiftKey&&!n.altKey&&t.toString()===""&&t.startOffset===1&&(r.nodeType===3&&r.previousSibling&&r.previousSibling.tagName==="INPUT"||r.nodeType!==3)){var o=i.previousElementSibling;if(i.querySelector("input").remove(),o){var u=(0,s.Rp)(o);u.parentElement.insertAdjacentHTML("beforeend","<wbr>"+i.innerHTML.trim()),i.remove()}else i.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'.concat(i.innerHTML.trim()||`
`,"</p>")),i.nextElementSibling?i.remove():i.parentElement.remove();return(0,M.ir)(e[e.currentMode].element,t),ee(e),n.preventDefault(),!0}if(n.key==="Enter"&&!(0,l._0)(n)&&!n.shiftKey&&!n.altKey){if(i.textContent.trim()==="")if((0,s.KJ)(i.parentElement,"vditor-task")){var h=(0,s.KR)(r);h&&Bt(e,i,t,h)}else if(i.nextElementSibling){var f="",S="",k=!1;Array.from(i.parentElement.children).forEach(function(F){i.isSameNode(F)?k=!0:k?f+=F.outerHTML:S+=F.outerHTML});var I=i.parentElement.tagName,N=i.parentElement.tagName==="OL"?"":' data-marker="'.concat(i.parentElement.getAttribute("data-marker"),'"'),H="";S&&(H=i.parentElement.tagName==="UL"?"":' start="1"',S="<".concat(I,' data-tight="true"').concat(N,' data-block="0">').concat(S,"</").concat(I,">")),i.parentElement.outerHTML="".concat(S,`<p data-block="0"><wbr>
</p><`).concat(I,`
 data-tight="true"`).concat(N,' data-block="0"').concat(H,">").concat(f,"</").concat(I,">")}else i.parentElement.insertAdjacentHTML("afterend",`<p data-block="0"><wbr>
</p>`),i.parentElement.querySelectorAll("li").length===1?i.parentElement.remove():i.remove();else r.nodeType!==3&&t.startOffset===0&&r.firstChild.tagName==="INPUT"?t.setStart(r.childNodes[1],1):(t.setEndAfter(i.lastChild),i.insertAdjacentHTML("afterend",'<li class="vditor-task" data-marker="'.concat(i.getAttribute("data-marker"),'"><input type="checkbox"> <wbr></li>')),document.querySelector("wbr").after(t.extractContents()));return(0,M.ir)(e[e.currentMode].element,t),ee(e),ye(e),n.preventDefault(),!0}}return!1},En=function(e,t,n,r){if(t.startContainer.nodeType!==3){var i=t.startContainer.children[t.startOffset];if(i&&i.tagName==="HR")return t.selectNodeContents(i.previousElementSibling),t.collapse(!1),n.preventDefault(),!0}if(r){var a=r.previousElementSibling;if(a&&(0,M.ED)(r,e[e.currentMode].element,t).start===0&&((0,l.gm)()&&a.tagName==="HR"||a.tagName==="TABLE")){if(a.tagName==="TABLE"){var o=a.lastElementChild.lastElementChild.lastElementChild;o.innerHTML=o.innerHTML.trimLeft()+"<wbr>"+r.textContent.trim(),r.remove()}else a.remove();return(0,M.ir)(e[e.currentMode].element,t),ee(e),n.preventDefault(),!0}}return!1},Sn=function(e){(0,l.gm)()&&e.startContainer.nodeType!==3&&e.startContainer.tagName==="HR"&&e.setStartBefore(e.startContainer)},Tn=function(e,t,n){var r,i;if(!(0,l.gm)())return!1;if(e.key==="ArrowUp"&&t&&((r=t.previousElementSibling)===null||r===void 0?void 0:r.tagName)==="TABLE"){var a=t.previousElementSibling;return n.selectNodeContents(a.rows[a.rows.length-1].lastElementChild),n.collapse(!1),e.preventDefault(),!0}return e.key==="ArrowDown"&&t&&((i=t.nextElementSibling)===null||i===void 0?void 0:i.tagName)==="TABLE"?(n.selectNodeContents(t.nextElementSibling.rows[0].cells[0]),n.collapse(!0),e.preventDefault(),!0):!1},St=function(e,t,n){return dr(void 0,void 0,void 0,function(){var r,i,a,o,u,h,f,S,k,I,N,H,F,O,B,O,z;return pr(this,function(K){switch(K.label){case 0:return e[e.currentMode].element.getAttribute("contenteditable")!=="true"?[2]:(t.stopPropagation(),t.preventDefault(),"clipboardData"in t?(r=t.clipboardData.getData("text/html"),i=t.clipboardData.getData("text/plain"),a=t.clipboardData.files):(r=t.dataTransfer.getData("text/html"),i=t.dataTransfer.getData("text/plain"),t.dataTransfer.types.includes("Files")&&(a=t.dataTransfer.items)),o={},u=function(Z,se){if(!se)return["",Lute.WalkContinue];if(e.options.upload.renderLinkDest)return e.options.upload.renderLinkDest(e,Z,se);var ae=Z.TokensStr();if(Z.__internal_object__.Parent.Type===34&&ae&&ae.indexOf("file://")===-1&&e.options.upload.linkToImgUrl){var ue=new XMLHttpRequest;ue.open("POST",e.options.upload.linkToImgUrl),e.options.upload.token&&ue.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&(ue.withCredentials=!0),an(e,ue),ue.setRequestHeader("Content-Type","application/json; charset=utf-8"),ue.onreadystatechange=function(){if(ue.readyState===XMLHttpRequest.DONE){if(ue.status===200){var xe=ue.responseText;e.options.upload.linkToImgFormat&&(xe=e.options.upload.linkToImgFormat(ue.responseText));var Ce=JSON.parse(xe);if(Ce.code!==0){e.tip.show(Ce.msg);return}var De=Ce.data.originalURL;if(e.currentMode==="sv")e.sv.element.querySelectorAll(".vditor-sv__marker--link").forEach(function(be){be.textContent===De&&(be.textContent=Ce.data.url)});else{var $=e[e.currentMode].element.querySelector('img[src="'.concat(De,'"]'));$.src=Ce.data.url,e.currentMode==="ir"&&($.previousElementSibling.previousElementSibling.innerHTML=Ce.data.url)}ee(e)}else e.tip.show(ue.responseText);e.options.upload.linkToImgCallback&&e.options.upload.linkToImgCallback(ue.responseText)}},ue.send(JSON.stringify({url:ae}))}return e.currentMode==="ir"?['<span class="vditor-ir__marker vditor-ir__marker--link">'.concat(Lute.EscapeHTMLStr(ae),"</span>"),Lute.WalkContinue]:e.currentMode==="wysiwyg"?["",Lute.WalkContinue]:['<span class="vditor-sv__marker--link">'.concat(Lute.EscapeHTMLStr(ae),"</span>"),Lute.WalkContinue]},(r.replace(/&amp;/g,"&").replace(/<(|\/)(html|body|meta)[^>]*?>/ig,"").trim()==='<a href="'.concat(i,'">').concat(i,"</a>")||r.replace(/&amp;/g,"&").replace(/<(|\/)(html|body|meta)[^>]*?>/ig,"").trim()==='<!--StartFragment--><a href="'.concat(i,'">').concat(i,"</a><!--EndFragment-->"))&&(r=""),h=new DOMParser().parseFromString(r,"text/html"),h.body&&(r=h.body.innerHTML),r=Lute.Sanitize(r),e.wysiwyg.getComments(e),f=e[e.currentMode].element.scrollHeight,S=Le(r,i,e.currentMode),k=e.currentMode==="sv"?(0,s.Th)(t.target,"data-type","code-block"):(0,s._Y)(t.target,"CODE"),k?(e.currentMode==="sv"?document.execCommand("insertHTML",!1,i.replace(/&/g,"&amp;").replace(/</g,"&lt;")):(I=(0,M.ED)(t.target,e[e.currentMode].element),k.parentElement.tagName!=="PRE"&&(i+=E.Y.ZWSP),k.textContent=k.textContent.substring(0,I.start)+i+k.textContent.substring(I.end),(0,M.Fm)(I.start+i.length,I.start+i.length,k.parentElement),!((z=k.parentElement)===null||z===void 0)&&z.nextElementSibling.classList.contains("vditor-".concat(e.currentMode,"__preview"))&&(k.parentElement.nextElementSibling.innerHTML=k.outerHTML,Ee(k.parentElement.nextElementSibling,e))),[3,8]):[3,1]);case 1:return S?(n.pasteCode(S),[3,8]):[3,2];case 2:return r.trim()===""?[3,3]:(N=document.createElement("div"),N.innerHTML=r,N.querySelectorAll("[style]").forEach(function(Z){Z.removeAttribute("style")}),N.querySelectorAll(".vditor-copy").forEach(function(Z){Z.remove()}),e.currentMode==="ir"?(o.HTML2VditorIRDOM={renderLinkDest:u},e.lute.SetJSRenderers({renderers:o}),(0,M.Z2)(e.lute.HTML2VditorIRDOM(N.innerHTML),e)):e.currentMode==="wysiwyg"?(o.HTML2VditorDOM={renderLinkDest:u},e.lute.SetJSRenderers({renderers:o}),(0,M.Z2)(e.lute.HTML2VditorDOM(N.innerHTML),e)):(o.Md2VditorSVDOM={renderLinkDest:u},e.lute.SetJSRenderers({renderers:o}),jt(e,e.lute.HTML2Md(N.innerHTML).trimRight())),e.outline.render(e),[3,8]);case 3:return a.length>0?e.options.upload.url||e.options.upload.handler?[4,Pt(e,a)]:[3,5]:[3,7];case 4:return K.sent(),[3,6];case 5:H=new FileReader,"clipboardData"in t?(a=t.clipboardData.files,F=a[0]):t.dataTransfer.types.includes("Files")&&(a=t.dataTransfer.items,F=a[0].getAsFile()),F&&F.type.startsWith("image")&&(H.readAsDataURL(F),H.onload=function(){var Z="";e.currentMode==="wysiwyg"?Z+='<img alt="'.concat(F.name,'" src="').concat(H.result.toString(),`">
`):Z+="![".concat(F.name,"](").concat(H.result.toString(),`)
`),document.execCommand("insertHTML",!1,Z)}),K.label=6;case 6:return[3,8];case 7:i.trim()!==""&&a.length===0&&(O=(0,M.RN)(e),O.toString()!==""&&e.lute.IsValidLinkDest(i)&&(i="[".concat(O.toString(),"](").concat(i,")")),e.currentMode==="ir"?(o.Md2VditorIRDOM={renderLinkDest:u},e.lute.SetJSRenderers({renderers:o}),(0,M.Z2)(e.lute.Md2VditorIRDOM(i),e)):e.currentMode==="wysiwyg"?(o.Md2VditorDOM={renderLinkDest:u},e.lute.SetJSRenderers({renderers:o}),(0,M.Z2)(e.lute.Md2VditorDOM(i),e)):(o.Md2VditorSVDOM={renderLinkDest:u},e.lute.SetJSRenderers({renderers:o}),jt(e,i)),e.outline.render(e)),K.label=8;case 8:return e.currentMode!=="sv"&&(B=(0,s.pt)((0,M.RN)(e).startContainer),B&&(O=(0,M.RN)(e),e[e.currentMode].element.querySelectorAll("wbr").forEach(function(Z){Z.remove()}),O.insertNode(document.createElement("wbr")),e.currentMode==="wysiwyg"?B.outerHTML=e.lute.SpinVditorDOM(B.outerHTML):B.outerHTML=e.lute.SpinVditorIRDOM(B.outerHTML),(0,M.ir)(e[e.currentMode].element,O)),e[e.currentMode].element.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(Z){Ee(Z,e)})),e.wysiwyg.triggerRemoveComment(e),ee(e),e[e.currentMode].element.scrollHeight-f>Math.min(e[e.currentMode].element.clientHeight,window.innerHeight)/2&&ye(e),[2]}})})},_n,Cn=function(e){var t,n;e.hint.render(e);var r=(0,M.RN)(e).startContainer,i=(0,s.Th)(r,"data-type","code-block-info");if(i)if(i.textContent.replace(E.Y.ZWSP,"")===""&&e.hint.recentLanguage){i.textContent=E.Y.ZWSP+e.hint.recentLanguage;var a=(0,M.RN)(e);a.selectNodeContents(i)}else{var o=[],u=i.textContent.substring(0,(0,M.ED)(i,e.ir.element).start).replace(E.Y.ZWSP,"");(e.options.preview.hljs.langs||E.Y.ALIAS_CODE_LANGUAGES.concat(((n=(t=window.hljs)===null||t===void 0?void 0:t.listLanguages())!==null&&n!==void 0?n:[]).sort())).forEach(function(h){h.indexOf(u.toLowerCase())>-1&&o.push({html:h,value:h})}),e.hint.genHTML(o,u,e)}},Ke=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&Cn(e),clearTimeout(e.ir.processTimeoutId),e.ir.processTimeoutId=window.setTimeout(function(){if(!e.ir.composingLock){var n=x(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,l.D)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e)}},e.options.undoDelay)},ut=function(e,t){var n=(0,M.RN)(e),r=(0,s.pt)(n.startContainer)||n.startContainer;if(r){var i=r.querySelector(".vditor-ir__marker--heading");i?i.innerHTML=t:(r.insertAdjacentText("afterbegin",t),n.selectNodeContents(r),n.collapse(!1)),Fe(e,n.cloneRange()),it(e)}},Tt=function(e,t,n){var r=(0,s.Th)(e.startContainer,"data-type",n);if(r){r.firstElementChild.remove(),r.lastElementChild.remove(),e.insertNode(document.createElement("wbr"));var i=document.createElement("div");i.innerHTML=t.lute.SpinVditorIRDOM(r.outerHTML),r.outerHTML=i.firstElementChild.innerHTML.trim()}},gr=function(e,t,n,r){var i=(0,M.RN)(e),a=t.getAttribute("data-type"),o=i.startContainer;o.nodeType===3&&(o=o.parentElement);var u=!0;if(t.classList.contains("vditor-menu--current"))if(a==="quote"){var h=(0,s._Y)(o,"BLOCKQUOTE");h&&(i.insertNode(document.createElement("wbr")),h.outerHTML=h.innerHTML.trim()===""?'<p data-block="0">'.concat(h.innerHTML,"</p>"):h.innerHTML)}else if(a==="link"){var f=(0,s.Th)(i.startContainer,"data-type","a");if(f){var S=(0,s.KJ)(i.startContainer,"vditor-ir__link");S?(i.insertNode(document.createElement("wbr")),f.outerHTML=S.innerHTML):f.outerHTML=f.querySelector(".vditor-ir__link").innerHTML+"<wbr>"}}else a==="italic"?Tt(i,e,"em"):a==="bold"?Tt(i,e,"strong"):a==="strike"?Tt(i,e,"s"):a==="inline-code"?Tt(i,e,"code"):(a==="check"||a==="list"||a==="ordered-list")&&(wt(e,i,a),u=!1,t.classList.remove("vditor-menu--current"));else{e.ir.element.childNodes.length===0&&(e.ir.element.innerHTML='<p data-block="0"><wbr></p>',(0,M.ir)(e.ir.element,i));var k=(0,s.pt)(i.startContainer);if(a==="line"){if(k){var I=`<hr data-block="0"><p data-block="0"><wbr>
</p>`;k.innerHTML.trim()===""?k.outerHTML=I:k.insertAdjacentHTML("afterend",I)}}else if(a==="quote")k&&(i.insertNode(document.createElement("wbr")),k.outerHTML='<blockquote data-block="0">'.concat(k.outerHTML,"</blockquote>"),u=!1,t.classList.add("vditor-menu--current"));else if(a==="link"){var N=void 0;i.toString()===""?N="".concat(n,"<wbr>").concat(r):N="".concat(n).concat(i.toString()).concat(r.replace(")","<wbr>)")),document.execCommand("insertHTML",!1,N),u=!1,t.classList.add("vditor-menu--current")}else if(a==="italic"||a==="bold"||a==="strike"||a==="inline-code"||a==="code"||a==="table"){var N=void 0;i.toString()===""?N="".concat(n,"<wbr>").concat(r):(a==="code"?N="".concat(n,`
`).concat(i.toString(),"<wbr>").concat(r):a==="table"?N="".concat(n).concat(i.toString(),"<wbr>").concat(r):N="".concat(n).concat(i.toString()).concat(r,"<wbr>"),i.deleteContents()),(a==="table"||a==="code")&&(N=`
`+N+`

`);var H=document.createElement("span");H.innerHTML=N,i.insertNode(H),Fe(e,i),a==="table"&&(i.selectNodeContents(getSelection().getRangeAt(0).startContainer.parentElement),(0,M.jl)(i))}else(a==="check"||a==="list"||a==="ordered-list")&&(wt(e,i,a,!1),u=!1,g(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current"))}(0,M.ir)(e.ir.element,i),Ke(e),u&&it(e)},yr=function(e,t,n,r){function i(a){return a instanceof n?a:new n(function(o){o(a)})}return new(n||(n=Promise))(function(a,o){function u(S){try{f(r.next(S))}catch(k){o(k)}}function h(S){try{f(r.throw(S))}catch(k){o(k)}}function f(S){S.done?a(S.value):i(S.value).then(u,h)}f((r=r.apply(e,t||[])).next())})},br=function(e,t){var n={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function u(f){return function(S){return h([f,S])}}function h(f){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,f[0]&&(n=0)),n;)try{if(r=1,i&&(a=f[0]&2?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[f[0]&2,a.value]),f[0]){case 0:case 1:a=f;break;case 4:return n.label++,{value:f[1],done:!1};case 5:n.label++,i=f[1],f=[0];continue;case 7:f=n.ops.pop(),n.trys.pop();continue;default:if(a=n.trys,!(a=a.length>0&&a[a.length-1])&&(f[0]===6||f[0]===2)){n=0;continue}if(f[0]===3&&(!a||f[1]>a[0]&&f[1]<a[3])){n.label=f[1];break}if(f[0]===6&&n.label<a[1]){n.label=a[1],a=f;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(f);break}a[2]&&n.ops.pop(),n.trys.pop();continue}f=t.call(e,n)}catch(S){f=[6,S],i=0}finally{r=a=0}if(f[0]&5)throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}},wr=function(){function e(t){var n=this;this.splitChar="",this.lastIndex=-1,this.fillEmoji=function(r,i){n.element.style.display="none";var a=decodeURIComponent(r.getAttribute("data-value")),o=window.getSelection().getRangeAt(0);if(i.currentMode==="ir"){var u=(0,s.Th)(o.startContainer,"data-type","code-block-info");if(u){u.textContent=E.Y.ZWSP+a.trimRight(),o.selectNodeContents(u),o.collapse(!1),Ke(i),u.parentElement.querySelectorAll("code").forEach(function(k){k.className="language-"+a.trimRight()}),Ee(u.parentElement.querySelector(".vditor-ir__preview"),i),n.recentLanguage=a.trimRight();return}}if(i.currentMode==="wysiwyg"&&o.startContainer.nodeType!==3){var h=o.startContainer,f=void 0;if(h.classList.contains("vditor-input")?f=h:f=h.firstElementChild,f&&f.classList.contains("vditor-input")){f.value=a.trimRight(),o.selectNodeContents(f),o.collapse(!1),f.dispatchEvent(new CustomEvent("input",{detail:1})),n.recentLanguage=a.trimRight();return}}if(o.setStart(o.startContainer,n.lastIndex),o.deleteContents(),i.options.hint.parse?i.currentMode==="sv"?(0,M.Z2)(i.lute.SpinVditorSVDOM(a),i):i.currentMode==="wysiwyg"?(0,M.Z2)(i.lute.SpinVditorDOM(a),i):(0,M.Z2)(i.lute.SpinVditorIRDOM(a),i):(0,M.Z2)(a,i),n.splitChar===":"&&a.indexOf(":")>-1&&i.currentMode!=="sv"&&o.insertNode(document.createTextNode(" ")),o.collapse(!1),(0,M.jl)(o),i.currentMode==="wysiwyg"){var S=(0,s.KJ)(o.startContainer,"vditor-wysiwyg__block");S&&S.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(S.lastElementChild.innerHTML=S.firstElementChild.innerHTML,Ee(S.lastElementChild,i))}else if(i.currentMode==="ir"){var S=(0,s.KJ)(o.startContainer,"vditor-ir__marker--pre");S&&S.nextElementSibling.classList.contains("vditor-ir__preview")&&(S.nextElementSibling.innerHTML=S.innerHTML,Ee(S.nextElementSibling,i))}ee(i)},this.timeId=-1,this.element=document.createElement("div"),this.element.className="vditor-hint",this.recentLanguage="",t.push({key:":"})}return e.prototype.render=function(t){var n=this;if(window.getSelection().focusNode){var r,i=getSelection().getRangeAt(0);r=i.startContainer.textContent.substring(0,i.startOffset)||"";var a=this.getKey(r,t.options.hint.extend);if(typeof a=="undefined")this.element.style.display="none",clearTimeout(this.timeId);else if(this.splitChar===":"){var o=a===""?t.options.hint.emoji:t.lute.GetEmojis(),u=[];Object.keys(o).forEach(function(h){h.indexOf(a.toLowerCase())===0&&(o[h].indexOf(".")>-1?u.push({html:'<img src="'.concat(o[h],'" title=":').concat(h,':"/> :').concat(h,":"),value:":".concat(h,":")}):u.push({html:'<span class="vditor-hint__emoji">'.concat(o[h],"</span>").concat(h),value:o[h]}))}),this.genHTML(u,a,t)}else t.options.hint.extend.forEach(function(h){h.key===n.splitChar&&(clearTimeout(n.timeId),n.timeId=window.setTimeout(function(){return yr(n,void 0,void 0,function(){var f;return br(this,function(S){switch(S.label){case 0:return f=this.genHTML,[4,h.hint(a)];case 1:return f.apply(this,[S.sent(),a,t]),[2]}})})},t.options.hint.delay))})}},e.prototype.genHTML=function(t,n,r){var i=this;if(t.length===0){this.element.style.display="none";return}var a=r[r.currentMode].element,o=(0,M.Ey)(a),u=o.left+(r.options.outline.position==="left"?r.outline.element.offsetWidth:0),h=o.top,f="";t.forEach(function(k,I){if(!(I>7)){var N=k.html;if(n!==""){var H=N.lastIndexOf(">")+1,F=N.substr(H),B=F.toLowerCase().indexOf(n.toLowerCase());B>-1&&(F=F.substring(0,B)+"<b>"+F.substring(B,B+n.length)+"</b>"+F.substring(B+n.length),N=N.substr(0,H)+F)}f+='<button type="button" data-value="'.concat(encodeURIComponent(k.value),` "
`).concat(I===0?"class='vditor-hint--current'":"","> ").concat(N,"</button>")}}),this.element.innerHTML=f;var S=parseInt(document.defaultView.getComputedStyle(a,null).getPropertyValue("line-height"),10);this.element.style.top="".concat(h+(S||22),"px"),this.element.style.left="".concat(u,"px"),this.element.style.display="block",this.element.style.right="auto",this.element.querySelectorAll("button").forEach(function(k){k.addEventListener("click",function(I){i.fillEmoji(k,r),I.preventDefault()})}),this.element.getBoundingClientRect().bottom>window.innerHeight&&(this.element.style.top="".concat(h-this.element.offsetHeight,"px")),this.element.getBoundingClientRect().right>window.innerWidth&&(this.element.style.left="auto",this.element.style.right="0")},e.prototype.select=function(t,n){if(this.element.querySelectorAll("button").length===0||this.element.style.display==="none")return!1;var r=this.element.querySelector(".vditor-hint--current");if(t.key==="ArrowDown")return t.preventDefault(),t.stopPropagation(),r.removeAttribute("class"),r.nextElementSibling?r.nextElementSibling.className="vditor-hint--current":this.element.children[0].className="vditor-hint--current",!0;if(t.key==="ArrowUp"){if(t.preventDefault(),t.stopPropagation(),r.removeAttribute("class"),r.previousElementSibling)r.previousElementSibling.className="vditor-hint--current";else{var i=this.element.children.length;this.element.children[i-1].className="vditor-hint--current"}return!0}else if(!(0,l._0)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Enter"&&!t.isComposing)return t.preventDefault(),t.stopPropagation(),this.fillEmoji(r,n),!0;return!1},e.prototype.getKey=function(t,n){var r=this;this.lastIndex=-1,this.splitChar="",n.forEach(function(f){var S=t.lastIndexOf(f.key);r.lastIndex<S&&(r.splitChar=f.key,r.lastIndex=S)});var i;if(this.lastIndex===-1)return i;var a=t.split(this.splitChar),o=a[a.length-1],u=32;if(a.length>1&&o.trim()===o)if(a.length===2&&a[0]===""&&a[1].length<u)i=a[1];else{var h=a[a.length-2].slice(-1);(0,_.p)(h)===" "&&o.length<u&&(i=o)}return i},e}(),vr=function(){function e(t){this.composingLock=!1;var n=document.createElement("div");n.className="vditor-ir",n.innerHTML='<pre class="vditor-reset" placeholder="'.concat(t.options.placeholder,`"
 contenteditable="true" spellcheck="false"></pre>`),this.element=n.firstElementChild,this.bindEvent(t),xt(t,this.element),en(t,this.element),Dt(t,this.element),Nt(t,this.element),It(t,this.element),Ot(t,this.element),Ht(t,this.element,this.copy),Rt(t,this.element,this.copy)}return e.prototype.copy=function(t,n){var r=getSelection().getRangeAt(0);if(r.toString()!==""){t.stopPropagation(),t.preventDefault();var i=document.createElement("div");i.appendChild(r.cloneContents()),t.clipboardData.setData("text/plain",n.lute.VditorIRDOM2Md(i.innerHTML).trim()),t.clipboardData.setData("text/html","")}},e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("paste",function(r){St(t,r,{pasteCode:function(i){document.execCommand("insertHTML",!1,i)}})}),this.element.addEventListener("scroll",function(){b(t,["hint"])}),this.element.addEventListener("compositionstart",function(r){n.composingLock=!0}),this.element.addEventListener("compositionend",function(r){(0,l.gm)()||Fe(t,getSelection().getRangeAt(0).cloneRange()),n.composingLock=!1}),this.element.addEventListener("input",function(r){if(!(r.inputType==="deleteByDrag"||r.inputType==="insertFromDrop")){if(n.preventInput){n.preventInput=!1,Ke(t,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0});return}n.composingLock||r.data==="‘"||r.data==="“"||r.data==="《"||Fe(t,getSelection().getRangeAt(0).cloneRange(),!1,r)}}),this.element.addEventListener("click",function(r){if(r.target.tagName==="INPUT"){r.target.checked?r.target.setAttribute("checked","checked"):r.target.removeAttribute("checked"),n.preventInput=!0,Ke(t);return}var i=(0,M.RN)(t),a=(0,s.KJ)(r.target,"vditor-ir__preview");if(a||(a=(0,s.KJ)(i.startContainer,"vditor-ir__preview")),a&&(a.previousElementSibling.firstElementChild?i.selectNodeContents(a.previousElementSibling.firstElementChild):i.selectNodeContents(a.previousElementSibling),i.collapse(!0),(0,M.jl)(i),ye(t)),r.target.tagName==="IMG"){var o=r.target.parentElement.querySelector(".vditor-ir__marker--link");o&&(i.selectNode(o),(0,M.jl)(i))}var u=(0,s.Th)(r.target,"data-type","a");if(u&&!u.classList.contains("vditor-ir__node--expand")){t.options.link.click?t.options.link.click(u.querySelector(":scope > .vditor-ir__marker--link")):t.options.link.isOpen&&window.open(u.querySelector(":scope > .vditor-ir__marker--link").textContent);return}if(r.target.isEqualNode(n.element)&&n.element.lastElementChild&&i.collapsed){var h=n.element.lastElementChild.getBoundingClientRect();r.y>h.top+h.height&&(n.element.lastElementChild.tagName==="P"&&n.element.lastElementChild.textContent.trim().replace(E.Y.ZWSP,"")===""?(i.selectNodeContents(n.element.lastElementChild),i.collapse(!1)):(n.element.insertAdjacentHTML("beforeend",'<p data-block="0">'.concat(E.Y.ZWSP,"<wbr></p>")),(0,M.ir)(n.element,i)))}i.toString()===""?P(i,t):setTimeout(function(){P((0,M.RN)(t),t)}),pt(r,t),it(t)}),this.element.addEventListener("keyup",function(r){if(!(r.isComposing||(0,l._0)(r))){if(r.key==="Enter"&&ye(t),it(t),(r.key==="Backspace"||r.key==="Delete")&&t.ir.element.innerHTML!==""&&t.ir.element.childNodes.length===1&&t.ir.element.firstElementChild&&t.ir.element.firstElementChild.tagName==="P"&&t.ir.element.firstElementChild.childElementCount===0&&(t.ir.element.textContent===""||t.ir.element.textContent===`
`)){t.ir.element.innerHTML="";return}var i=(0,M.RN)(t);r.key==="Backspace"?((0,l.gm)()&&i.startContainer.textContent===`
`&&i.startOffset===1&&(i.startContainer.textContent="",P(i,t)),n.element.querySelectorAll(".language-math").forEach(function(o){var u=o.querySelector("br");u&&u.remove()})):r.key.indexOf("Arrow")>-1?((r.key==="ArrowLeft"||r.key==="ArrowRight")&&Cn(t),P(i,t)):r.keyCode===229&&r.code===""&&r.key==="Unidentified"&&P(i,t);var a=(0,s.KJ)(i.startContainer,"vditor-ir__preview");if(a){if(r.key==="ArrowUp"||r.key==="ArrowLeft")return a.previousElementSibling.firstElementChild?i.selectNodeContents(a.previousElementSibling.firstElementChild):i.selectNodeContents(a.previousElementSibling),i.collapse(!1),r.preventDefault(),!0;if(a.tagName==="SPAN"&&(r.key==="ArrowDown"||r.key==="ArrowRight"))return a.parentElement.getAttribute("data-type")==="html-entity"?(a.parentElement.insertAdjacentText("afterend",E.Y.ZWSP),i.setStart(a.parentElement.nextSibling,1)):i.selectNodeContents(a.parentElement.lastElementChild),i.collapse(!1),r.preventDefault(),!0}}})},e}(),Mn=function(e){if(e.currentMode==="sv")return e.lute.Md2HTML(x(e));if(e.currentMode==="wysiwyg")return e.lute.VditorDOM2HTML(e.wysiwyg.element.innerHTML);if(e.currentMode==="ir")return e.lute.VditorIRDOM2HTML(e.ir.element.innerHTML)},Er=X(796),kn=X(70),Sr=function(){function e(t){this.element=document.createElement("div"),this.element.className="vditor-outline",this.element.innerHTML='<div class="vditor-outline__title">'.concat(t,`</div>
<div class="vditor-outline__content"></div>`)}return e.prototype.render=function(t){var n="";return t.preview.element.style.display==="block"?n=(0,kn.N)(t.preview.previewElement,this.element.lastElementChild,t):n=(0,kn.N)(t[t.currentMode].element,this.element.lastElementChild,t),n},e.prototype.toggle=function(t,n,r){var i;n===void 0&&(n=!0),r===void 0&&(r=!0);var a=(i=t.toolbar.elements.outline)===null||i===void 0?void 0:i.firstElementChild;if(n&&window.innerWidth>=E.Y.MOBILE_WIDTH?(this.element.style.display="block",this.render(t),a==null||a.classList.add("vditor-menu--current")):(this.element.style.display="none",a==null||a.classList.remove("vditor-menu--current")),r&&getSelection().rangeCount>0){var o=getSelection().getRangeAt(0);t[t.currentMode].element.contains(o.startContainer)&&(0,M.jl)(o)}Ne(t)},e}(),Tr=X(0),_r=function(){function e(t){var n=this;this.element=document.createElement("div"),this.element.className="vditor-preview",this.previewElement=document.createElement("div"),this.previewElement.className="vditor-reset",t.options.classes.preview&&this.previewElement.classList.add(t.options.classes.preview),this.previewElement.style.maxWidth=t.options.preview.maxWidth+"px",this.previewElement.addEventListener("copy",function(h){if(h.target.tagName!=="TEXTAREA"){var f=document.createElement("div");f.className="vditor-reset",f.appendChild(getSelection().getRangeAt(0).cloneContents()),n.copyToX(t,f,"default"),h.preventDefault()}}),this.previewElement.addEventListener("click",function(h){var f=(0,s._Y)(h.target,"SPAN");if(f&&(0,s.KJ)(f,"vditor-toc")){var S=n.previewElement.querySelector("#"+f.getAttribute("data-target-id"));S&&(n.element.scrollTop=S.offsetTop);return}if(h.target.tagName==="A"){t.options.link.click?t.options.link.click(h.target):t.options.link.isOpen&&window.open(h.target.getAttribute("href")),h.preventDefault();return}h.target.tagName==="IMG"&&(t.options.image.preview?t.options.image.preview(h.target):t.options.image.isPreview&&(0,W.o)(h.target,t.options.lang,t.options.theme))}),this.element.appendChild(this.previewElement);var r=t.options.preview.actions;if(r.length!==0){var i=document.createElement("div");i.className="vditor-preview__action";for(var a=[],o=0;o<r.length;o++){var u=r[o];if(typeof u=="object"){a.push('<button type="button" data-type="'.concat(u.key,'" class="').concat(u.className,'"').concat(u.tooltip?' aria-label="'.concat(u.tooltip,'"'):"",'">').concat(u.text,"</button>"));continue}switch(u){case"desktop":a.push('<button type="button" class="vditor-preview__action--current" data-type="desktop">Desktop</button>');break;case"tablet":a.push('<button type="button" data-type="tablet">Tablet</button>');break;case"mobile":a.push('<button type="button" data-type="mobile">Mobile/Wechat</button>');break;case"mp-wechat":a.push('<button type="button" data-type="mp-wechat" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到公众号"><svg><use xlink:href="#vditor-icon-mp-wechat"></use></svg></button>');break;case"zhihu":a.push('<button type="button" data-type="zhihu" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到知乎"><svg><use xlink:href="#vditor-icon-zhihu"></use></svg></button>');break}}i.innerHTML=a.join(""),i.addEventListener((0,l.y3)(),function(h){var f=(0,T.f)(h.target,"BUTTON");if(f){var S=f.getAttribute("data-type"),k=r.find(function(I){return(I==null?void 0:I.key)===S});if(k){k.click(S);return}if(S==="mp-wechat"||S==="zhihu"){n.copyToX(t,n.previewElement.cloneNode(!0),S);return}S==="desktop"?n.previewElement.style.width="auto":S==="tablet"?n.previewElement.style.width="780px":n.previewElement.style.width="360px",n.previewElement.scrollWidth>n.previewElement.parentElement.clientWidth&&(n.previewElement.style.width="auto"),n.render(t),i.querySelectorAll("button").forEach(function(I){I.classList.remove("vditor-preview__action--current")}),f.classList.add("vditor-preview__action--current")}}),this.element.insertBefore(i,this.previewElement)}}return e.prototype.render=function(t,n){var r=this;if(clearTimeout(this.mdTimeoutId),this.element.style.display==="none"){this.element.getAttribute("data-type")==="renderPerformance"&&t.tip.hide();return}if(n){this.previewElement.innerHTML=n;return}if(x(t).replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")===""){this.previewElement.innerHTML="";return}var i=new Date().getTime(),a=x(t);this.mdTimeoutId=window.setTimeout(function(){if(t.options.preview.url){var o=new XMLHttpRequest;o.open("POST",t.options.preview.url),o.setRequestHeader("Content-Type","application/json;charset=UTF-8"),o.onreadystatechange=function(){if(o.readyState===XMLHttpRequest.DONE)if(o.status===200){var h=JSON.parse(o.responseText);if(h.code!==0){t.tip.show(h.msg);return}t.options.preview.transform&&(h.data=t.options.preview.transform(h.data)),r.previewElement.innerHTML=h.data,r.afterRender(t,i)}else{var f=t.lute.Md2HTML(a);t.options.preview.transform&&(f=t.options.preview.transform(f)),r.previewElement.innerHTML=f,r.afterRender(t,i)}},o.send(JSON.stringify({markdownText:a}))}else{var u=t.lute.Md2HTML(a);t.options.preview.transform&&(u=t.options.preview.transform(u)),r.previewElement.innerHTML=u,r.afterRender(t,i)}},t.options.preview.delay)},e.prototype.afterRender=function(t,n){t.options.preview.parse&&t.options.preview.parse(this.element);var r=new Date().getTime()-n;new Date().getTime()-n>2600?(t.tip.show(window.VditorI18n.performanceTip.replace("${x}",r.toString())),t.preview.element.setAttribute("data-type","renderPerformance")):t.preview.element.getAttribute("data-type")==="renderPerformance"&&(t.tip.hide(),t.preview.element.removeAttribute("data-type"));var i=t.preview.element.querySelector(".vditor-comment--focus");i&&i.classList.remove("vditor-comment--focus"),(0,j.o)(t.preview.previewElement,t.options.preview.hljs),(0,Q.$)(t.options.preview.hljs,t.preview.previewElement,t.options.cdn),(0,oe.e)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,me.K)(t.preview.previewElement,t.options.cdn),(0,ve.Y)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,V.D)(t.preview.previewElement,t.options.cdn),(0,q.m)(t.preview.previewElement,t.options.cdn),(0,C.v)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,fe.l)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,le.M)(t.preview.previewElement,t.options.cdn),(0,D.$)(t.preview.previewElement,t.options.cdn),t.options.preview.render.media.enable&&(0,Tr.l)(t.preview.previewElement),t.options.customRenders.forEach(function(u){u.render(t.preview.previewElement,t)});var a=t.preview.element,o=t.outline.render(t);o===""&&(o="[ToC]"),a.querySelectorAll('[data-type="toc-block"]').forEach(function(u){u.innerHTML=o,(0,ne.T)(u,{cdn:t.options.cdn,math:t.options.preview.math})}),(0,ne.T)(t.preview.previewElement,{cdn:t.options.cdn,math:t.options.preview.math})},e.prototype.copyToX=function(t,n,r){r===void 0&&(r="mp-wechat"),r!=="zhihu"?n.querySelectorAll(".katex-html .base").forEach(function(a){a.style.display="initial"}):n.querySelectorAll(".language-math").forEach(function(a){a.outerHTML='<img class="Formula-image" data-eeimg="true" src="//www.zhihu.com/equation?tex=" alt="'.concat(a.getAttribute("data-math"),'\\" style="display: block; margin: 0 auto; max-width: 100%;">')}),n.style.backgroundColor="#fff",n.querySelectorAll("code").forEach(function(a){a.style.backgroundImage="none"}),this.element.append(n);var i=n.ownerDocument.createRange();i.selectNode(n),(0,M.jl)(i),document.execCommand("copy"),n.remove(),t.tip.show(["zhihu","mp-wechat"].includes(r)?"已复制，可到".concat(r==="zhihu"?"知乎":"微信公众号平台","进行粘贴"):"已复制到剪切板")},e}(),Cr=function(){function e(t){this.element=document.createElement("div"),this.element.className="vditor-resize vditor-resize--".concat(t.options.resize.position),this.element.innerHTML='<div><svg><use xlink:href="#vditor-icon-resize"></use></svg></div>',this.bindEvent(t)}return e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("mousedown",function(r){var i=document,a=r.clientY,o=t.element.offsetHeight,u=63+t.element.querySelector(".vditor-toolbar").clientHeight;i.ondragstart=function(){return!1},window.captureEvents&&window.captureEvents(),n.element.classList.add("vditor-resize--selected"),i.onmousemove=function(h){t.options.resize.position==="top"?t.element.style.height=Math.max(u,o+(a-h.clientY))+"px":t.element.style.height=Math.max(u,o+(h.clientY-a))+"px",t.options.typewriterMode&&(t.sv.element.style.paddingBottom=t.sv.element.parentElement.offsetHeight/2+"px")},i.onmouseup=function(){t.options.resize.after&&t.options.resize.after(t.element.offsetHeight-o),window.captureEvents&&window.captureEvents(),i.onmousemove=null,i.onmouseup=null,i.ondragstart=null,i.onselectstart=null,i.onselect=null,n.element.classList.remove("vditor-resize--selected")}})},e}(),Mr=function(){function e(t){this.composingLock=!1,this.element=document.createElement("pre"),this.element.className="vditor-sv vditor-reset",this.element.setAttribute("placeholder",t.options.placeholder),this.element.setAttribute("contenteditable","true"),this.element.setAttribute("spellcheck","false"),this.bindEvent(t),xt(t,this.element),Dt(t,this.element),Nt(t,this.element),It(t,this.element),Ot(t,this.element),Ht(t,this.element,this.copy),Rt(t,this.element,this.copy)}return e.prototype.copy=function(t,n){t.stopPropagation(),t.preventDefault(),t.clipboardData.setData("text/plain",st(n[n.currentMode].element))},e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("paste",function(r){St(t,r,{pasteCode:function(i){document.execCommand("insertHTML",!1,i)}})}),this.element.addEventListener("scroll",function(){if(t.preview.element.style.display==="block"){var r=n.element.scrollTop,i=n.element.clientHeight,a=n.element.scrollHeight-parseFloat(n.element.style.paddingBottom||"0"),o=t.preview.element;r/i>.5?o.scrollTop=(r+i)*o.scrollHeight/a-i:o.scrollTop=r*o.scrollHeight/a}}),this.element.addEventListener("compositionstart",function(r){n.composingLock=!0}),this.element.addEventListener("compositionend",function(r){(0,l.gm)()||ie(t,r),n.composingLock=!1}),this.element.addEventListener("input",function(r){if(!(r.inputType==="deleteByDrag"||r.inputType==="insertFromDrop")&&!(n.composingLock||r.data==="‘"||r.data==="“"||r.data==="《")){if(n.preventInput){n.preventInput=!1,Se(t,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0});return}ie(t,r)}}),this.element.addEventListener("keyup",function(r){if(!(r.isComposing||(0,l._0)(r))){if((r.key==="Backspace"||r.key==="Delete")&&t.sv.element.innerHTML!==""&&t.sv.element.childNodes.length===1&&t.sv.element.firstElementChild&&t.sv.element.firstElementChild.tagName==="DIV"&&t.sv.element.firstElementChild.childElementCount===2&&(t.sv.element.firstElementChild.textContent===""||t.sv.element.textContent===`
`)){t.sv.element.innerHTML="";return}r.key==="Enter"&&ye(t)}})},e}(),Ln=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-tip"}return e.prototype.show=function(t,n){var r=this;n===void 0&&(n=6e3),this.element.className="vditor-tip vditor-tip--show",n===0?(this.element.innerHTML='<div class="vditor-tip__content">'.concat(t,`
<div class="vditor-tip__close">X</div></div>`),this.element.querySelector(".vditor-tip__close").addEventListener("click",function(){r.hide()})):(this.element.innerHTML='<div class="vditor-tip__content">'.concat(t,"</div>"),setTimeout(function(){r.hide()},n)),this.element.removeAttribute("style"),setTimeout(function(){var i=r.element.getBoundingClientRect();i.top<46&&(r.element.style.position="fixed",r.element.style.top="46px")},150)},e.prototype.hide=function(){this.element.className="vditor-messageElementtip",this.element.innerHTML=""},e}(),Yt=function(e,t){if(t.options.preview.mode!==e){switch(t.options.preview.mode=e,e){case"both":t.sv.element.style.display="block",t.preview.element.style.display="block",t.preview.render(t),p(t.toolbar.elements,["both"]);break;case"editor":t.sv.element.style.display="block",t.preview.element.style.display="none",g(t.toolbar.elements,["both"]);break}t.devtools&&t.devtools.renderEchart(t)}},kr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Lr=function(e){kr(t,e);function t(n,r){var i=e.call(this,n,r)||this;return n.options.preview.mode==="both"&&i.element.children[0].classList.add("vditor-menu--current"),i.element.children[0].addEventListener((0,l.y3)(),function(a){var o=i.element.firstElementChild;o.classList.contains(E.Y.CLASS_MENU_DISABLED)||(a.preventDefault(),n.currentMode==="sv"&&(n.options.preview.mode==="both"?Yt("editor",n):Yt("both",n)))}),i}return t}(he),Ar=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__br"}return e}(),An=X(13),xr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Dr=function(e){xr(t,e);function t(n,r){var i=e.call(this,n,r)||this,a=i.element.children[0],o=document.createElement("div");o.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow");var u="";return E.Y.CODE_THEME.forEach(function(h){u+="<button>".concat(h,"</button>")}),o.innerHTML='<div style="overflow: auto;max-height:'.concat(window.innerHeight/2,'px">').concat(u,"</div>"),o.addEventListener((0,l.y3)(),function(h){h.target.tagName==="BUTTON"&&(b(n,["subToolbar"]),n.options.preview.hljs.style=h.target.textContent,(0,An.h)(h.target.textContent,n.options.cdn),h.preventDefault(),h.stopPropagation())}),i.element.appendChild(o),y(n,o,a,r.level),i}return t}(he),Or=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Hr=function(e){Or(t,e);function t(n,r){var i=e.call(this,n,r)||this,a=i.element.children[0],o=document.createElement("div");o.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow");var u="";return Object.keys(n.options.preview.theme.list).forEach(function(h){u+='<button data-type="'.concat(h,'">').concat(n.options.preview.theme.list[h],"</button>")}),o.innerHTML='<div style="overflow: auto;max-height:'.concat(window.innerHeight/2,'px">').concat(u,"</div>"),o.addEventListener((0,l.y3)(),function(h){h.target.tagName==="BUTTON"&&(b(n,["subToolbar"]),n.options.preview.theme.current=h.target.getAttribute("data-type"),(0,J.H)(n.options.preview.theme.current,n.options.preview.theme.path),h.preventDefault(),h.stopPropagation())}),i.element.appendChild(o),y(n,o,a,r.level),i}return t}(he),Rr=function(){function e(t){this.element=document.createElement("span"),this.element.className="vditor-counter vditor-tooltipped vditor-tooltipped__nw",this.render(t,"")}return e.prototype.render=function(t,n){var r=n.endsWith(`
`)?n.length-1:n.length;if(t.options.counter.type==="text"&&t[t.currentMode]){var i=t[t.currentMode].element.cloneNode(!0);i.querySelectorAll(".vditor-wysiwyg__preview").forEach(function(a){a.remove()}),r=i.textContent.length}typeof t.options.counter.max=="number"?(r>t.options.counter.max?this.element.className="vditor-counter vditor-counter--error":this.element.className="vditor-counter",this.element.innerHTML="".concat(r,"/").concat(t.options.counter.max)):this.element.innerHTML="".concat(r),this.element.setAttribute("aria-label",t.options.counter.type),t.options.counter.after&&t.options.counter.after(r,{enable:t.options.counter.enable,max:t.options.counter.max,type:t.options.counter.type})},e}(),Nr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ir=function(e){Nr(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].innerHTML=r.icon,i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),!a.currentTarget.classList.contains(E.Y.CLASS_MENU_DISABLED)&&r.click(a,n)}),i}return t}(he),jr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Pr=function(e){jr(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.firstElementChild.addEventListener((0,l.y3)(),function(a){var o=i.element.firstElementChild;o.classList.contains(E.Y.CLASS_MENU_DISABLED)||(a.preventDefault(),o.classList.contains("vditor-menu--current")?(o.classList.remove("vditor-menu--current"),n.devtools.element.style.display="none",Ne(n)):(o.classList.add("vditor-menu--current"),n.devtools.element.style.display="block",Ne(n),n.devtools.renderEchart(n)))}),i}return t}(he),Br=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__divider"}return e}(),Ur=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Vr=function(e){Ur(t,e);function t(n,r){var i=e.call(this,n,r)||this,a=document.createElement("div");a.className="vditor-panel vditor-panel--arrow";var o="";return Object.keys(n.options.hint.emoji).forEach(function(u){var h=n.options.hint.emoji[u];h.indexOf(".")>-1?o+='<button data-value=":'.concat(u,': " data-key=":').concat(u,`:"><img
data-value=":`).concat(u,': " data-key=":').concat(u,':" class="vditor-emojis__icon" src="').concat(h,'"/></button>'):o+='<button data-value="'.concat(h,` "
 data-key="`).concat(u,'"><span class="vditor-emojis__icon">').concat(h,"</span></button>")}),a.innerHTML='<div class="vditor-emojis" style="max-height: '.concat(n.options.height==="auto"?"auto":n.options.height-80,'px">').concat(o,`</div><div class="vditor-emojis__tail">
    <span class="vditor-emojis__tip"></span><span>`).concat(n.options.hint.emojiTail||"",`</span>
</div>`),i.element.appendChild(a),y(n,a,i.element.firstElementChild,r.level),i.bindEvent(n),i}return t.prototype.bindEvent=function(n){var r=this;this.element.lastElementChild.addEventListener((0,l.y3)(),function(i){var a=(0,T.f)(i.target,"BUTTON");if(a){i.preventDefault();var o=a.getAttribute("data-value"),u=(0,M.RN)(n),h=o;if(n.currentMode==="wysiwyg"?h=n.lute.SpinVditorDOM(o):n.currentMode==="ir"&&(h=n.lute.SpinVditorIRDOM(o)),o.indexOf(":")>-1&&n.currentMode!=="sv"){var f=document.createElement("div");f.innerHTML=h,h=f.firstElementChild.firstElementChild.outerHTML+" ",(0,M.Z2)(h,n)}else u.extractContents(),u.insertNode(document.createTextNode(o)),(0,s.pt)(u.startContainer)||kt(n,u);u.collapse(!1),(0,M.jl)(u),r.element.lastElementChild.style.display="none",ee(n)}}),this.element.lastElementChild.addEventListener("mouseover",function(i){var a=(0,T.f)(i.target,"BUTTON");a&&(r.element.querySelector(".vditor-emojis__tip").innerHTML=a.getAttribute("data-key"))})},t}(he),xn=function(e,t,n){var r=document.createElement("a");"download"in r?(r.download=n,r.style.display="none",r.href=URL.createObjectURL(new Blob([t])),document.body.appendChild(r),r.click(),r.remove()):e.tip.show(window.VditorI18n.downloadTip,0)},Kr=function(e){var t=x(e);xn(e,t,t.substr(0,10)+".md")},Yr=function(e){e.tip.show(window.VditorI18n.generate,3800);var t=document.querySelector("#vditorExportIframe");t.contentDocument.open(),t.contentDocument.write('<link rel="stylesheet" href="'.concat(e.options.cdn,`/dist/index.css"/>
<script src="`).concat(e.options.cdn,`/dist/method.min.js"><\/script>
<div id="preview" style="width: 800px"></div>
<script>
window.addEventListener("message", (e) => {
  if(!e.data) {
    return;
  }
  Vditor.preview(document.getElementById('preview'), e.data, {
    cdn: "`).concat(e.options.cdn,`",
    markdown: {
      theme: `).concat(JSON.stringify(e.options.preview.theme),`
    },
    hljs: {
      style: "`).concat(e.options.preview.hljs.style,`"
    }
  });
  setTimeout(() => {
        window.print();
    }, 3600);
}, false);
<\/script>`)),t.contentDocument.close(),setTimeout(function(){t.contentWindow.postMessage(x(e),"*")},200)},Fr=function(e){var t=Mn(e),n='<html><head><link rel="stylesheet" type="text/css" href="'.concat(e.options.cdn,`/dist/index.css"/>
<script src="`).concat(e.options.cdn,"/dist/js/i18n/").concat(e.options.lang,`.js"><\/script>
<script src="`).concat(e.options.cdn,`/dist/method.min.js"><\/script></head>
<body><div class="vditor-reset" id="preview">`).concat(t,`</div>
<script>
    const previewElement = document.getElementById('preview')
    Vditor.setContentTheme('`).concat(e.options.preview.theme.current,"', '").concat(e.options.preview.theme.path,`');
    Vditor.codeRender(previewElement);
    Vditor.highlightRender(`).concat(JSON.stringify(e.options.preview.hljs),", previewElement, '").concat(e.options.cdn,`');
    Vditor.mathRender(previewElement, {
        cdn: '`).concat(e.options.cdn,`',
        math: `).concat(JSON.stringify(e.options.preview.math),`,
    });
    Vditor.mermaidRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.SMILESRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.markmapRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.flowchartRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.graphvizRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.chartRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.mindmapRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.abcRender(previewElement, '`).concat(e.options.cdn,`');
    `).concat(e.options.preview.render.media.enable?"Vditor.mediaRender(previewElement);":"",`
    Vditor.speechRender(previewElement);
<\/script>
<script src="`).concat(e.options.cdn,"/dist/js/icons/").concat(e.options.icon,'.js"><\/script></body></html>');xn(e,n,t.substr(0,10)+".html")},Wr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),qr=function(e){Wr(t,e);function t(n,r){var i=e.call(this,n,r)||this,a=i.element.children[0],o=document.createElement("div");return o.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow"),o.innerHTML=`<button data-type="markdown">Markdown</button>
<button data-type="pdf">PDF</button>
<button data-type="html">HTML</button>`,o.addEventListener((0,l.y3)(),function(u){var h=u.target;if(h.tagName==="BUTTON"){switch(h.getAttribute("data-type")){case"markdown":Kr(n);break;case"pdf":Yr(n);break;case"html":Fr(n);break}b(n,["subToolbar"]),u.preventDefault(),u.stopPropagation()}}),i.element.appendChild(o),y(n,o,a,r.level),i}return t}(he),zr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Gr=function(e){zr(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i._bindEvent(n,r),i}return t.prototype._bindEvent=function(n,r){this.element.children[0].addEventListener((0,l.y3)(),function(i){i.preventDefault(),n.element.className.includes("vditor--fullscreen")?(r.level||(this.innerHTML=r.icon),n.element.style.zIndex="",document.body.style.overflow="",n.element.classList.remove("vditor--fullscreen"),Object.keys(n.toolbar.elements).forEach(function(a){var o=n.toolbar.elements[a].firstChild;o&&(o.className=o.className.replace("__s","__n"),n.options.toolbar.forEach(function(u){typeof u!="string"&&u.tipPosition&&u.name===o.dataset.type&&(o.className="vditor-tooltipped vditor-tooltipped__".concat(u.tipPosition))}))}),n.counter&&(n.counter.element.className=n.counter.element.className.replace("__s","__n"))):(r.level||(this.innerHTML='<svg><use xlink:href="#vditor-icon-contract"></use></svg>'),n.element.style.zIndex=n.options.fullscreen.index.toString(),document.body.style.overflow="hidden",n.element.classList.add("vditor--fullscreen"),Object.keys(n.toolbar.elements).forEach(function(a){var o=n.toolbar.elements[a].firstChild;o&&(o.className=o.className.replace("__n","__s"))}),n.counter&&(n.counter.element.className=n.counter.element.className.replace("__n","__s"))),n.devtools&&n.devtools.renderEchart(n),r.click&&r.click(i,n),Ne(n),mt(n)})},t}(he),Zr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Jr=function(e){Zr(t,e);function t(n,r){var i=e.call(this,n,r)||this,a=document.createElement("div");return a.className="vditor-hint vditor-panel--arrow",a.innerHTML='<button data-tag="h1" data-value="# ">'.concat(window.VditorI18n.heading1," ").concat((0,l.fG)("&lt;⌥⌘1>"),`</button>
<button data-tag="h2" data-value="## ">`).concat(window.VditorI18n.heading2," &lt;").concat((0,l.fG)("⌥⌘2"),`></button>
<button data-tag="h3" data-value="### ">`).concat(window.VditorI18n.heading3," &lt;").concat((0,l.fG)("⌥⌘3"),`></button>
<button data-tag="h4" data-value="#### ">`).concat(window.VditorI18n.heading4," &lt;").concat((0,l.fG)("⌥⌘4"),`></button>
<button data-tag="h5" data-value="##### ">`).concat(window.VditorI18n.heading5," &lt;").concat((0,l.fG)("⌥⌘5"),`></button>
<button data-tag="h6" data-value="###### ">`).concat(window.VditorI18n.heading6," &lt;").concat((0,l.fG)("⌥⌘6"),"></button>"),i.element.appendChild(a),i._bindEvent(n,a),i}return t.prototype._bindEvent=function(n,r){var i=this.element.children[0];i.addEventListener((0,l.y3)(),function(o){o.preventDefault(),clearTimeout(n.wysiwyg.afterRenderTimeoutId),clearTimeout(n.ir.processTimeoutId),clearTimeout(n.sv.processTimeoutId),!i.classList.contains(E.Y.CLASS_MENU_DISABLED)&&(i.blur(),i.classList.contains("vditor-menu--current")?(n.currentMode==="wysiwyg"?(Lt(n),ce(n)):n.currentMode==="ir"&&ut(n,""),i.classList.remove("vditor-menu--current")):(b(n,["subToolbar"]),r.style.display="block"))});for(var a=0;a<6;a++)r.children.item(a).addEventListener((0,l.y3)(),function(o){o.preventDefault(),n.currentMode==="wysiwyg"?(gt(n,o.target.getAttribute("data-tag")),ce(n),i.classList.add("vditor-menu--current")):n.currentMode==="ir"?(ut(n,o.target.getAttribute("data-value")),i.classList.add("vditor-menu--current")):nn(n,o.target.getAttribute("data-value")),r.style.display="none"})},t}(he),Xr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),$r=function(e){Xr(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),n.tip.show(`<div style="margin-bottom:14px;font-size: 14px;line-height: 22px;min-width:300px;max-width: 360px;display: flex;">
<div style="margin-top: 14px;flex: 1">
    <div>Markdown 使用指南</div>
    <ul style="list-style: none">
        <li><a href="https://ld246.com/article/1583308420519" target="_blank">语法速查手册</a></li>
        <li><a href="https://ld246.com/article/1583129520165" target="_blank">基础语法</a></li>
        <li><a href="https://ld246.com/article/1583305480675" target="_blank">扩展语法</a></li>
        <li><a href="https://ld246.com/article/1582778815353" target="_blank">键盘快捷键</a></li>
    </ul>
</div>
<div style="margin-top: 14px;flex: 1">
    <div>Vditor 支持</div>
    <ul style="list-style: none">
        <li><a href="https://github.com/Vanessa219/vditor/issues" target="_blank">Issues</a></li>
        <li><a href="https://ld246.com/tag/vditor" target="_blank">官方讨论区</a></li>
        <li><a href="https://ld246.com/article/1549638745630" target="_blank">开发手册</a></li>
        <li><a href="https://ld246.com/guide/markdown" target="_blank">演示地址</a></li>
    </ul>
</div></div>`,0)}),i}return t}(he),Qr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ei=function(e){Qr(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].addEventListener((0,l.y3)(),function(a){if(a.preventDefault(),!(i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)||n.currentMode==="sv")){var o=(0,M.RN)(n),u=(0,s._Y)(o.startContainer,"LI");u&&un(n,u,o)}}),i}return t}(he),ti=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ni=function(e){ti(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),n.tip.show(`<div style="max-width: 520px; font-size: 14px;line-height: 22px;margin-bottom: 14px;">
<p style="text-align: center;margin: 14px 0">
    <em>下一代的 Markdown 编辑器，为未来而构建</em>
</p>
<div style="display: flex;margin-bottom: 14px;flex-wrap: wrap;align-items: center">
    <img src="https://unpkg.com/vditor/dist/images/logo.png" style="margin: 0 auto;height: 68px"/>
    <div>&nbsp;&nbsp;</div>
    <div style="flex: 1;min-width: 250px">
        Vditor 是一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。
        它使用 TypeScript 实现，支持原生 JavaScript 以及 Vue、React、Angular 和 Svelte 等框架。
    </div>
</div>
<div style="display: flex;flex-wrap: wrap;">
    <ul style="list-style: none;flex: 1;min-width:148px">
        <li>
        项目地址：<a href="https://b3log.org/vditor" target="_blank">b3log.org/vditor</a>
        </li>
        <li>
        开源协议：MIT
        </li>
    </ul>
    <ul style="list-style: none;margin-right: 18px">
        <li>
        组件版本：Vditor v`.concat(E.g," / Lute v").concat(Lute.Version,`
        </li>
        <li>
        赞助捐赠：<a href="https://ld246.com/sponsor" target="_blank">https://ld246.com/sponsor</a>
        </li>
    </ul>
</div>
</div>`),0)}),i}return t}(he),ri=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ii=function(e){ri(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),!(i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)||n.currentMode==="sv")&&lt(n,"afterend")}),i}return t}(he),ai=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),oi=function(e){ai(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),!(i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)||n.currentMode==="sv")&&lt(n,"beforebegin")}),i}return t}(he),si=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),li=function(e){si(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.element.children[0].addEventListener((0,l.y3)(),function(a){if(a.preventDefault(),!(i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)||n.currentMode==="sv")){var o=(0,M.RN)(n),u=(0,s._Y)(o.startContainer,"LI");u&&Bt(n,u,o,u.parentElement)}}),i}return t}(he),ci=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ui=function(e){ci(t,e);function t(n,r){var i=e.call(this,n,r)||this;return n.options.outline&&i.element.firstElementChild.classList.add("vditor-menu--current"),i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault();var o=n.toolbar.elements.outline.firstElementChild;o.classList.contains(E.Y.CLASS_MENU_DISABLED)||(n.options.outline.enable=!i.element.firstElementChild.classList.contains("vditor-menu--current"),n.outline.toggle(n,n.options.outline.enable))}),i}return t}(he),fi=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),di=function(e){fi(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i._bindEvent(n),i}return t.prototype._bindEvent=function(n){var r=this;this.element.children[0].addEventListener((0,l.y3)(),function(i){i.preventDefault();var a=r.element.firstElementChild;if(!a.classList.contains(E.Y.CLASS_MENU_DISABLED)){var o=E.Y.EDIT_TOOLBARS.concat(["both","edit-mode","devtools"]);a.classList.contains("vditor-menu--current")?(a.classList.remove("vditor-menu--current"),n.currentMode==="sv"?(n.sv.element.style.display="block",n.options.preview.mode==="both"?n.preview.element.style.display="block":n.preview.element.style.display="none"):(n[n.currentMode].element.parentElement.style.display="block",n.preview.element.style.display="none"),m(n.toolbar.elements,o),n.outline.render(n)):(d(n.toolbar.elements,o),n.preview.element.style.display="block",n.currentMode==="sv"?n.sv.element.style.display="none":n[n.currentMode].element.parentElement.style.display="none",n.preview.render(n),a.classList.add("vditor-menu--current"),b(n,["subToolbar","hint","popover"]),setTimeout(function(){n.outline.render(n)},n.options.preview.delay+10)),Ne(n)}})},t}(he),pi=function(){function e(t){this.SAMPLE_RATE=5e3,this.isRecording=!1,this.readyFlag=!1,this.leftChannel=[],this.rightChannel=[],this.recordingLength=0;var n;if(typeof AudioContext!="undefined")n=new AudioContext;else if(webkitAudioContext)n=new webkitAudioContext;else return;this.DEFAULT_SAMPLE_RATE=n.sampleRate;var r=n.createGain(),i=n.createMediaStreamSource(t);i.connect(r),this.recorder=n.createScriptProcessor(2048,2,1),this.recorder.onaudioprocess=null,r.connect(this.recorder),this.recorder.connect(n.destination),this.readyFlag=!0}return e.prototype.cloneChannelData=function(t,n){this.leftChannel.push(new Float32Array(t)),this.rightChannel.push(new Float32Array(n)),this.recordingLength+=2048},e.prototype.startRecordingNewWavFile=function(){this.readyFlag&&(this.isRecording=!0,this.leftChannel.length=this.rightChannel.length=0,this.recordingLength=0)},e.prototype.stopRecording=function(){this.isRecording=!1},e.prototype.buildWavFileBlob=function(){for(var t=this.mergeBuffers(this.leftChannel),n=this.mergeBuffers(this.rightChannel),r=new Float32Array(t.length),i=0;i<t.length;++i)r[i]=.5*(t[i]+n[i]);this.DEFAULT_SAMPLE_RATE>this.SAMPLE_RATE&&(r=this.downSampleBuffer(r,this.SAMPLE_RATE));var a=44+r.length*2,o=new ArrayBuffer(a),u=new DataView(o);this.writeUTFBytes(u,0,"RIFF"),u.setUint32(4,a,!0),this.writeUTFBytes(u,8,"WAVE"),this.writeUTFBytes(u,12,"fmt "),u.setUint32(16,16,!0),u.setUint16(20,1,!0),u.setUint16(22,1,!0),u.setUint32(24,this.SAMPLE_RATE,!0),u.setUint32(28,this.SAMPLE_RATE*2,!0),u.setUint16(32,2,!0),u.setUint16(34,16,!0);var h=r.length*2;this.writeUTFBytes(u,36,"data"),u.setUint32(40,h,!0);for(var f=r.length,S=44,k=1,I=0;I<f;I++)u.setInt16(S,r[I]*(32767*k),!0),S+=2;return new Blob([u],{type:"audio/wav"})},e.prototype.downSampleBuffer=function(t,n){if(n===this.DEFAULT_SAMPLE_RATE||n>this.DEFAULT_SAMPLE_RATE)return t;for(var r=this.DEFAULT_SAMPLE_RATE/n,i=Math.round(t.length/r),a=new Float32Array(i),o=0,u=0;o<a.length;){for(var h=Math.round((o+1)*r),f=0,S=0,k=u;k<h&&k<t.length;k++)f+=t[k],S++;a[o]=f/S,o++,u=h}return a},e.prototype.mergeBuffers=function(t){for(var n=new Float32Array(this.recordingLength),r=0,i=t.length,a=0;a<i;++a){var o=t[a];n.set(o,r),r+=o.length}return n},e.prototype.writeUTFBytes=function(t,n,r){for(var i=r.length,a=0;a<i;a++)t.setUint8(n+a,r.charCodeAt(a))},e}(),hi=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),mi=function(e){hi(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i._bindEvent(n),i}return t.prototype._bindEvent=function(n){var r=this,i;this.element.children[0].addEventListener((0,l.y3)(),function(a){if(a.preventDefault(),!r.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)){var o=n[n.currentMode].element;if(!i){navigator.mediaDevices.getUserMedia({audio:!0}).then(function(h){i=new pi(h),i.recorder.onaudioprocess=function(f){if(i.isRecording){var S=f.inputBuffer.getChannelData(0),k=f.inputBuffer.getChannelData(1);i.cloneChannelData(S,k)}},i.startRecordingNewWavFile(),n.tip.show(window.VditorI18n.recording),o.setAttribute("contenteditable","false"),r.element.children[0].classList.add("vditor-menu--current")}).catch(function(){n.tip.show(window.VditorI18n["record-tip"])});return}if(i.isRecording){i.stopRecording(),n.tip.hide();var u=new File([i.buildWavFileBlob()],"record".concat(new Date().getTime(),".wav"),{type:"video/webm"});Pt(n,[u]),r.element.children[0].classList.remove("vditor-menu--current")}else n.tip.show(window.VditorI18n.recording),o.setAttribute("contenteditable","false"),i.startRecordingNewWavFile(),r.element.children[0].classList.add("vditor-menu--current")}})},t}(he),gi=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),yi=function(e){gi(t,e);function t(n,r){var i=e.call(this,n,r)||this;return d({redo:i.element},["redo"]),i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),!i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)&&n.undo.redo(n)}),i}return t}(he),bi=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),wi=function(e){bi(t,e);function t(n,r){var i=e.call(this,n,r)||this;return d({undo:i.element},["undo"]),i.element.children[0].addEventListener((0,l.y3)(),function(a){a.preventDefault(),!i.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)&&n.undo.undo(n)}),i}return t}(he),vi=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ei=function(e){vi(t,e);function t(n,r){var i=e.call(this,n,r)||this,a='<input type="file"';return n.options.upload.multiple&&(a+=' multiple="multiple"'),n.options.upload.accept&&(a+=' accept="'.concat(n.options.upload.accept,'"')),i.element.children[0].innerHTML="".concat(r.icon||'<svg><use xlink:href="#vditor-icon-upload"></use></svg>').concat(a,">"),i._bindEvent(n),i}return t.prototype._bindEvent=function(n){var r=this;this.element.children[0].addEventListener((0,l.y3)(),function(i){if(r.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)){i.stopPropagation(),i.preventDefault();return}}),this.element.querySelector("input").addEventListener("change",function(i){if(r.element.firstElementChild.classList.contains(E.Y.CLASS_MENU_DISABLED)){i.stopPropagation(),i.preventDefault();return}i.target.files.length!==0&&Pt(n,i.target.files,i.target)})},t}(he),Si=function(){function e(t){var n=this,r=t.options;this.elements={},this.element=document.createElement("div"),this.element.className="vditor-toolbar",r.toolbar.forEach(function(i,a){var o=n.genItem(t,i,a);if(n.element.appendChild(o),i.toolbar){var u=document.createElement("div");u.className="vditor-hint vditor-panel--arrow",u.addEventListener((0,l.y3)(),function(h){u.style.display="none"}),i.toolbar.forEach(function(h,f){h.level=2,u.appendChild(n.genItem(t,h,a+f))}),o.appendChild(u),y(t,u,o.children[0])}}),t.options.toolbarConfig.hide&&this.element.classList.add("vditor-toolbar--hide"),t.options.toolbarConfig.pin&&this.element.classList.add("vditor-toolbar--pin"),t.options.counter.enable&&(t.counter=new Rr(t),this.element.appendChild(t.counter.element))}return e.prototype.updateConfig=function(t,n){t.options.toolbarConfig=Object.assign({hide:!1,pin:!1},n),t.options.toolbarConfig.hide?this.element.classList.add("vditor-toolbar--hide"):this.element.classList.remove("vditor-toolbar--hide"),t.options.toolbarConfig.pin?this.element.classList.add("vditor-toolbar--pin"):this.element.classList.remove("vditor-toolbar--pin")},e.prototype.genItem=function(t,n,r){var i;switch(n.name){case"bold":case"italic":case"more":case"strike":case"line":case"quote":case"list":case"ordered-list":case"check":case"code":case"inline-code":case"link":case"table":i=new he(t,n);break;case"emoji":i=new Vr(t,n);break;case"headings":i=new Jr(t,n);break;case"|":i=new Br;break;case"br":i=new Ar;break;case"undo":i=new wi(t,n);break;case"redo":i=new yi(t,n);break;case"help":i=new $r(t,n);break;case"both":i=new Lr(t,n);break;case"preview":i=new di(t,n);break;case"fullscreen":i=new Gr(t,n);break;case"upload":i=new Ei(t,n);break;case"record":i=new mi(t,n);break;case"info":i=new ni(t,n);break;case"edit-mode":i=new rr(t,n);break;case"devtools":i=new Pr(t,n);break;case"outdent":i=new li(t,n);break;case"indent":i=new ei(t,n);break;case"outline":i=new ui(t,n);break;case"insert-after":i=new ii(t,n);break;case"insert-before":i=new oi(t,n);break;case"code-theme":i=new Dr(t,n);break;case"content-theme":i=new Hr(t,n);break;case"export":i=new qr(t,n);break;default:i=new Ir(t,n);break}if(i){var a=n.name;return(a==="br"||a==="|")&&(a=a+r),this.elements[a]=i.element,i.element}},e}(),Ti=X(192),_i=function(){function e(){this.stackSize=50,this.resetStack(),this.dmp=new Ti}return e.prototype.clearStack=function(t){this.resetStack(),this.resetIcon(t)},e.prototype.resetIcon=function(t){t.toolbar&&(this[t.currentMode].undoStack.length>1?m(t.toolbar.elements,["undo"]):d(t.toolbar.elements,["undo"]),this[t.currentMode].redoStack.length!==0?m(t.toolbar.elements,["redo"]):d(t.toolbar.elements,["redo"]))},e.prototype.undo=function(t){if(t[t.currentMode].element.getAttribute("contenteditable")!=="false"&&!(this[t.currentMode].undoStack.length<2)){var n=this[t.currentMode].undoStack.pop();n&&(this[t.currentMode].redoStack.push(n),this.renderDiff(n,t),this[t.currentMode].hasUndo=!0,b(t,["hint"]))}},e.prototype.redo=function(t){if(t[t.currentMode].element.getAttribute("contenteditable")!=="false"){var n=this[t.currentMode].redoStack.pop();n&&(this[t.currentMode].undoStack.push(n),this.renderDiff(n,t,!0))}},e.prototype.recordFirstPosition=function(t,n){if(getSelection().rangeCount!==0&&!(this[t.currentMode].undoStack.length!==1||this[t.currentMode].undoStack[0].length===0||this[t.currentMode].redoStack.length>0)&&!((0,l.gm)()&&n.key==="Backspace")&&!(0,l.nr)()){var r=this.addCaret(t);r.replace("<wbr>","").replace(" vditor-ir__node--expand","")===this[t.currentMode].undoStack[0][0].diffs[0][1].replace("<wbr>","")&&(this[t.currentMode].undoStack[0][0].diffs[0][1]=r,this[t.currentMode].lastText=r)}},e.prototype.addToUndoStack=function(t){var n=this.addCaret(t,!0),r=this.dmp.diff_main(n,this[t.currentMode].lastText,!0),i=this.dmp.patch_make(n,this[t.currentMode].lastText,r);i.length===0&&this[t.currentMode].undoStack.length>0||(this[t.currentMode].lastText=n,this[t.currentMode].undoStack.push(i),this[t.currentMode].undoStack.length>this.stackSize&&this[t.currentMode].undoStack.shift(),this[t.currentMode].hasUndo&&(this[t.currentMode].redoStack=[],this[t.currentMode].hasUndo=!1,d(t.toolbar.elements,["redo"])),this[t.currentMode].undoStack.length>1&&m(t.toolbar.elements,["undo"]))},e.prototype.renderDiff=function(t,n,r){r===void 0&&(r=!1);var i;if(r){var a=this.dmp.patch_deepCopy(t).reverse();a.forEach(function(u){u.diffs.forEach(function(h){h[0]=-h[0]})}),i=this.dmp.patch_apply(a,this[n.currentMode].lastText)[0]}else i=this.dmp.patch_apply(t,this[n.currentMode].lastText)[0];if(this[n.currentMode].lastText=i,n[n.currentMode].element.innerHTML=i,n.currentMode!=="sv"&&(n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview")).forEach(function(u){u.parentElement.querySelector(".language-echarts")&&(n.currentMode==="ir"?u.parentElement.outerHTML=n.lute.SpinVditorIRDOM(u.parentElement.outerHTML):u.parentElement.outerHTML=n.lute.SpinVditorDOM(u.parentElement.outerHTML))}),n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview[data-render='2']")).forEach(function(u){Ee(u,n)})),n[n.currentMode].element.querySelector("wbr"))(0,M.ir)(n[n.currentMode].element,n[n.currentMode].element.ownerDocument.createRange()),ye(n);else{var o=getSelection().getRangeAt(0);o.setEndBefore(n[n.currentMode].element),o.collapse(!1)}Be(n),ee(n,{enableAddUndoStack:!1,enableHint:!1,enableInput:!0}),Qe(n),n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview[data-render='2']")).forEach(function(u){Ee(u,n)}),this[n.currentMode].undoStack.length>1?m(n.toolbar.elements,["undo"]):d(n.toolbar.elements,["undo"]),this[n.currentMode].redoStack.length!==0?m(n.toolbar.elements,["redo"]):d(n.toolbar.elements,["redo"])},e.prototype.resetStack=function(){this.ir={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]},this.sv={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]},this.wysiwyg={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]}},e.prototype.addCaret=function(t,n){n===void 0&&(n=!1);var r;if(getSelection().rangeCount!==0&&!t[t.currentMode].element.querySelector("wbr")){var i=getSelection().getRangeAt(0);if(t[t.currentMode].element.contains(i.startContainer)){r=i.cloneRange();var a=document.createElement("span");a.className="vditor-wbr",i.insertNode(a)}}var o=t[t.currentMode].element.cloneNode(!0);o.querySelectorAll(".vditor-".concat(t.currentMode,"__preview[data-render='1']")).forEach(function(h){h.firstElementChild&&(h.firstElementChild.classList.contains("language-echarts")||h.firstElementChild.classList.contains("language-plantuml")||h.firstElementChild.classList.contains("language-mindmap")?(h.firstElementChild.removeAttribute("_echarts_instance_"),h.firstElementChild.removeAttribute("data-processed"),h.firstElementChild.innerHTML=h.previousElementSibling.firstElementChild.innerHTML,h.setAttribute("data-render","2")):h.firstElementChild.classList.contains("language-math")&&(h.setAttribute("data-render","2"),h.firstElementChild.textContent=h.firstElementChild.getAttribute("data-math"),h.firstElementChild.removeAttribute("data-math")))});var u=o.innerHTML;return t[t.currentMode].element.querySelectorAll(".vditor-wbr").forEach(function(h){h.remove()}),n&&r&&(0,M.jl)(r),u.replace('<span class="vditor-wbr"></span>',"<wbr>")},e}(),Ci=X(905),Mi=function(){function e(t){this.defaultOptions={rtl:!1,after:void 0,cache:{enable:!0},cdn:E.Y.CDN,classes:{preview:""},comment:{enable:!1},counter:{enable:!1,type:"markdown"},customRenders:[],debugger:!1,fullscreen:{index:90},height:"auto",hint:{delay:200,emoji:{"+1":"👍","-1":"👎",confused:"😕",eyes:"👀️",heart:"❤️",rocket:"🚀️",smile:"😄",tada:"🎉️"},emojiPath:"".concat(E.Y.CDN,"/dist/images/emoji"),extend:[],parse:!0},icon:"ant",lang:"zh_CN",mode:"ir",outline:{enable:!1,position:"left"},placeholder:"",preview:{actions:["desktop","tablet","mobile","mp-wechat","zhihu"],delay:1e3,hljs:E.Y.HLJS_OPTIONS,markdown:E.Y.MARKDOWN_OPTIONS,math:E.Y.MATH_OPTIONS,maxWidth:800,mode:"both",theme:E.Y.THEME_OPTIONS,render:{media:{enable:!0}}},link:{isOpen:!0},image:{isPreview:!0},resize:{enable:!1,position:"bottom"},theme:"classic",toolbar:["emoji","headings","bold","italic","strike","link","|","list","ordered-list","check","outdent","indent","|","quote","line","code","inline-code","insert-before","insert-after","|","upload","record","table","|","undo","redo","|","fullscreen","edit-mode",{name:"more",toolbar:["both","code-theme","content-theme","export","outline","preview","devtools","info","help"]}],toolbarConfig:{hide:!1,pin:!1},typewriterMode:!1,undoDelay:800,upload:{extraData:{},fieldName:"file[]",filename:function(n){return n.replace(/\W/g,"")},linkToImgUrl:"",max:10*1024*1024,multiple:!0,url:"",withCredentials:!1},value:"",width:"auto"},this.options=t}return e.prototype.merge=function(){var t,n,r,i,a,o,u,h,f;this.options&&(this.options.toolbar?this.options.toolbar=this.mergeToolbar(this.options.toolbar):this.options.toolbar=this.mergeToolbar(this.defaultOptions.toolbar),!((n=(t=this.options.preview)===null||t===void 0?void 0:t.theme)===null||n===void 0)&&n.list&&(this.defaultOptions.preview.theme.list=this.options.preview.theme.list),!((a=(i=(r=this.options.preview)===null||r===void 0?void 0:r.render)===null||i===void 0?void 0:i.media)===null||a===void 0)&&a.enable&&(this.defaultOptions.preview.render.media.enable=this.options.preview.render.media.enable),!((o=this.options.hint)===null||o===void 0)&&o.emoji&&(this.defaultOptions.hint.emoji=this.options.hint.emoji),this.options.comment&&(this.defaultOptions.comment=this.options.comment),this.options.cdn&&(!((h=(u=this.options.preview)===null||u===void 0?void 0:u.theme)===null||h===void 0)&&h.path||(this.defaultOptions.preview.theme.path="".concat(this.options.cdn,"/dist/css/content-theme")),!((f=this.options.hint)===null||f===void 0)&&f.emojiPath||(this.defaultOptions.hint.emojiPath="".concat(this.options.cdn,"/dist/images/emoji"))));var S=(0,Ci.h)(this.defaultOptions,this.options);if(S.cache.enable&&!S.cache.id)throw new Error("need options.cache.id, see https://ld246.com/article/1549638745630#options");return S},e.prototype.mergeToolbar=function(t){var n=this,r=[{icon:'<svg><use xlink:href="#vditor-icon-export"></use></svg>',name:"export",tipPosition:"ne"},{hotkey:"⌘E",icon:'<svg><use xlink:href="#vditor-icon-emoji"></use></svg>',name:"emoji",tipPosition:"ne"},{hotkey:"⌘H",icon:'<svg><use xlink:href="#vditor-icon-headings"></use></svg>',name:"headings",tipPosition:"ne"},{hotkey:"⌘B",icon:'<svg><use xlink:href="#vditor-icon-bold"></use></svg>',name:"bold",prefix:"**",suffix:"**",tipPosition:"ne"},{hotkey:"⌘I",icon:'<svg><use xlink:href="#vditor-icon-italic"></use></svg>',name:"italic",prefix:"*",suffix:"*",tipPosition:"ne"},{hotkey:"⌘D",icon:'<svg><use xlink:href="#vditor-icon-strike"></use></svg>',name:"strike",prefix:"~~",suffix:"~~",tipPosition:"ne"},{hotkey:"⌘K",icon:'<svg><use xlink:href="#vditor-icon-link"></use></svg>',name:"link",prefix:"[",suffix:"](https://)",tipPosition:"n"},{name:"|"},{hotkey:"⌘L",icon:'<svg><use xlink:href="#vditor-icon-list"></use></svg>',name:"list",prefix:"* ",tipPosition:"n"},{hotkey:"⌘O",icon:'<svg><use xlink:href="#vditor-icon-ordered-list"></use></svg>',name:"ordered-list",prefix:"1. ",tipPosition:"n"},{hotkey:"⌘J",icon:'<svg><use xlink:href="#vditor-icon-check"></use></svg>',name:"check",prefix:"* [ ] ",tipPosition:"n"},{hotkey:"⇧⌘I",icon:'<svg><use xlink:href="#vditor-icon-outdent"></use></svg>',name:"outdent",tipPosition:"n"},{hotkey:"⇧⌘O",icon:'<svg><use xlink:href="#vditor-icon-indent"></use></svg>',name:"indent",tipPosition:"n"},{name:"|"},{hotkey:"⌘;",icon:'<svg><use xlink:href="#vditor-icon-quote"></use></svg>',name:"quote",prefix:"> ",tipPosition:"n"},{hotkey:"⇧⌘H",icon:'<svg><use xlink:href="#vditor-icon-line"></use></svg>',name:"line",prefix:"---",tipPosition:"n"},{hotkey:"⌘U",icon:'<svg><use xlink:href="#vditor-icon-code"></use></svg>',name:"code",prefix:"```",suffix:"\n```",tipPosition:"n"},{hotkey:"⌘G",icon:'<svg><use xlink:href="#vditor-icon-inline-code"></use></svg>',name:"inline-code",prefix:"`",suffix:"`",tipPosition:"n"},{hotkey:"⇧⌘B",icon:'<svg><use xlink:href="#vditor-icon-before"></use></svg>',name:"insert-before",tipPosition:"n"},{hotkey:"⇧⌘E",icon:'<svg><use xlink:href="#vditor-icon-after"></use></svg>',name:"insert-after",tipPosition:"n"},{name:"|"},{icon:'<svg><use xlink:href="#vditor-icon-upload"></use></svg>',name:"upload",tipPosition:"n"},{icon:'<svg><use xlink:href="#vditor-icon-record"></use></svg>',name:"record",tipPosition:"n"},{hotkey:"⌘M",icon:'<svg><use xlink:href="#vditor-icon-table"></use></svg>',name:"table",prefix:"| col1",suffix:` | col2 | col3 |
| --- | --- | --- |
|  |  |  |
|  |  |  |`,tipPosition:"n"},{name:"|"},{hotkey:"⌘Z",icon:'<svg><use xlink:href="#vditor-icon-undo"></use></svg>',name:"undo",tipPosition:"nw"},{hotkey:"⌘Y",icon:'<svg><use xlink:href="#vditor-icon-redo"></use></svg>',name:"redo",tipPosition:"nw"},{name:"|"},{icon:'<svg><use xlink:href="#vditor-icon-more"></use></svg>',name:"more",tipPosition:"e"},{hotkey:"⌘'",icon:'<svg><use xlink:href="#vditor-icon-fullscreen"></use></svg>',name:"fullscreen",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-edit"></use></svg>',name:"edit-mode",tipPosition:"nw"},{hotkey:"⌘P",icon:'<svg><use xlink:href="#vditor-icon-both"></use></svg>',name:"both",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-preview"></use></svg>',name:"preview",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-align-center"></use></svg>',name:"outline",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-theme"></use></svg>',name:"content-theme",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-code-theme"></use></svg>',name:"code-theme",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-bug"></use></svg>',name:"devtools",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-info"></use></svg>',name:"info",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-help"></use></svg>',name:"help",tipPosition:"nw"},{name:"br"}],i=[];return t.forEach(function(a){var o=a;r.forEach(function(u){typeof a=="string"&&u.name===a&&(o=u),typeof a=="object"&&u.name===a.name&&(o=Object.assign({},u,a))}),a.toolbar&&(o.toolbar=n.mergeToolbar(a.toolbar)),i.push(o)}),i},e}(),ki=function(){function e(t){var n=this;this.composingLock=!1,this.commentIds=[];var r=document.createElement("div");r.className="vditor-wysiwyg",r.innerHTML='<pre class="vditor-reset" placeholder="'.concat(t.options.placeholder,`"
 contenteditable="true" spellcheck="false"></pre>
<div class="vditor-panel vditor-panel--none"></div>
<div class="vditor-panel vditor-panel--none">
    <button type="button" aria-label="`).concat(window.VditorI18n.comment,`" class="vditor-icon vditor-tooltipped vditor-tooltipped__n">
        <svg><use xlink:href="#vditor-icon-comment"></use></svg>
    </button>
</div>`),this.element=r.firstElementChild,this.popover=r.firstElementChild.nextElementSibling,this.selectPopover=r.lastElementChild,this.bindEvent(t),xt(t,this.element),en(t,this.element),Dt(t,this.element),Nt(t,this.element),It(t,this.element),Ot(t,this.element),Ht(t,this.element,this.copy),Rt(t,this.element,this.copy),t.options.comment.enable&&(this.selectPopover.querySelector("button").onclick=function(){var i=Lute.NewNodeID(),a=getSelection().getRangeAt(0),o=a.cloneRange(),u=a.extractContents(),h,f,S=!1,k=!1;u.childNodes.forEach(function(H,F){var B=!1;if(H.nodeType===3?B=!0:H.classList.contains("vditor-comment")?H.classList.contains("vditor-comment")&&H.setAttribute("data-cmtids",H.getAttribute("data-cmtids")+" "+i):B=!0,B)if(H.nodeType!==3&&H.getAttribute("data-block")==="0"&&F===0&&o.startOffset>0)H.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(i,'">').concat(H.innerHTML,"</span>"),h=H;else if(H.nodeType!==3&&H.getAttribute("data-block")==="0"&&F===u.childNodes.length-1&&o.endOffset<o.endContainer.textContent.length)H.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(i,'">').concat(H.innerHTML,"</span>"),f=H;else if(H.nodeType!==3&&H.getAttribute("data-block")==="0")F===0?S=!0:F===u.childNodes.length-1&&(k=!0),H.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(i,'">').concat(H.innerHTML,"</span>");else{var O=document.createElement("span");O.classList.add("vditor-comment"),O.setAttribute("data-cmtids",i),H.parentNode.insertBefore(O,H),O.appendChild(H)}});var I=(0,s.pt)(o.startContainer);I&&(h?(I.insertAdjacentHTML("beforeend",h.innerHTML),h.remove()):I.textContent.trim().replace(E.Y.ZWSP,"")===""&&S&&I.remove());var N=(0,s.pt)(o.endContainer);N&&(f?(N.insertAdjacentHTML("afterbegin",f.innerHTML),f.remove()):N.textContent.trim().replace(E.Y.ZWSP,"")===""&&k&&N.remove()),a.insertNode(u),t.options.comment.add(i,a.toString(),n.getComments(t,!0)),ce(t,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),n.hideComment()})}return e.prototype.getComments=function(t,n){var r=this;if(n===void 0&&(n=!1),t.currentMode==="wysiwyg"&&t.options.comment.enable){this.commentIds=[],this.element.querySelectorAll(".vditor-comment").forEach(function(a){r.commentIds=r.commentIds.concat(a.getAttribute("data-cmtids").split(" "))}),this.commentIds=Array.from(new Set(this.commentIds));var i=[];if(n)return this.commentIds.forEach(function(a){i.push({id:a,top:r.element.querySelector('.vditor-comment[data-cmtids="'.concat(a,'"]')).offsetTop})}),i}else return[]},e.prototype.triggerRemoveComment=function(t){var n=function(a,o){var u=new Set(o);return a.filter(function(h){return!u.has(h)})};if(t.currentMode==="wysiwyg"&&t.options.comment.enable&&t.wysiwyg.commentIds.length>0){var r=JSON.parse(JSON.stringify(this.commentIds));this.getComments(t);var i=n(r,this.commentIds);i.length>0&&t.options.comment.remove(i)}},e.prototype.showComment=function(){var t=(0,M.Ey)(this.element);this.selectPopover.setAttribute("style","left:".concat(t.left,"px;display:block;top:").concat(Math.max(-8,t.top-21),"px"))},e.prototype.hideComment=function(){this.selectPopover.setAttribute("style","display:none")},e.prototype.unbindListener=function(){window.removeEventListener("scroll",this.scrollListener)},e.prototype.copy=function(t,n){var r=getSelection().getRangeAt(0);if(r.toString()!==""){t.stopPropagation(),t.preventDefault();var i=(0,s._Y)(r.startContainer,"CODE"),a=(0,s._Y)(r.endContainer,"CODE");if(i&&a&&a.isSameNode(i)){var o="";i.parentElement.tagName==="PRE"?o=r.toString():o="`"+r.toString()+"`",t.clipboardData.setData("text/plain",o),t.clipboardData.setData("text/html","");return}var u=(0,s._Y)(r.startContainer,"A"),h=(0,s._Y)(r.endContainer,"A");if(u&&h&&h.isSameNode(u)){var f=u.getAttribute("title")||"";f&&(f=' "'.concat(f,'"')),t.clipboardData.setData("text/plain","[".concat(r.toString(),"](").concat(u.getAttribute("href")).concat(f,")")),t.clipboardData.setData("text/html","");return}var S=document.createElement("div");S.appendChild(r.cloneContents()),t.clipboardData.setData("text/plain",n.lute.VditorDOM2Md(S.innerHTML).trim()),t.clipboardData.setData("text/html","")}},e.prototype.bindEvent=function(t){var n=this;this.unbindListener(),window.addEventListener("scroll",this.scrollListener=function(){if(b(t,["hint"]),!(n.popover.style.display!=="block"||n.selectPopover.style.display!=="block")){var r=parseInt(n.popover.getAttribute("data-top"),10);if(t.options.height!=="auto"){if(t.options.toolbarConfig.pin&&t.toolbar.element.getBoundingClientRect().top===0){var i=Math.max(window.scrollY-t.element.offsetTop-8,Math.min(r-t.wysiwyg.element.scrollTop,n.element.clientHeight-21))+"px";n.popover.style.display==="block"&&(n.popover.style.top=i),n.selectPopover.style.display==="block"&&(n.selectPopover.style.top=i)}return}else if(!t.options.toolbarConfig.pin)return;var a=Math.max(r,window.scrollY-t.element.offsetTop-8)+"px";n.popover.style.display==="block"&&(n.popover.style.top=a),n.selectPopover.style.display==="block"&&(n.selectPopover.style.top=a)}}),this.element.addEventListener("scroll",function(){if(b(t,["hint"]),t.options.comment&&t.options.comment.enable&&t.options.comment.scroll&&t.options.comment.scroll(t.wysiwyg.element.scrollTop),n.popover.style.display==="block"){var r=parseInt(n.popover.getAttribute("data-top"),10)-t.wysiwyg.element.scrollTop,i=-8;t.options.toolbarConfig.pin&&t.toolbar.element.getBoundingClientRect().top===0&&(i=window.scrollY-t.element.offsetTop+i);var a=Math.max(i,Math.min(r,n.element.clientHeight-21))+"px";n.popover.style.top=a,n.selectPopover.style.top=a}}),this.element.addEventListener("paste",function(r){St(t,r,{pasteCode:function(i){var a=(0,M.RN)(t),o=document.createElement("template");o.innerHTML=i,a.insertNode(o.content.cloneNode(!0));var u=(0,s.Th)(a.startContainer,"data-block","0");u?u.outerHTML=t.lute.SpinVditorDOM(u.outerHTML):t.wysiwyg.element.innerHTML=t.lute.SpinVditorDOM(t.wysiwyg.element.innerHTML),(0,M.ir)(t.wysiwyg.element,a)}})}),this.element.addEventListener("compositionstart",function(){n.composingLock=!0}),this.element.addEventListener("compositionend",function(r){var i=(0,T.c)(getSelection().getRangeAt(0).startContainer);if(i&&i.textContent===""){Be(t);return}(0,l.gm)()||bt(t,getSelection().getRangeAt(0).cloneRange(),r),n.composingLock=!1}),this.element.addEventListener("input",function(r){if(!(r.inputType==="deleteByDrag"||r.inputType==="insertFromDrop")){if(n.preventInput){n.preventInput=!1,ce(t);return}if(n.composingLock||r.data==="‘"||r.data==="“"||r.data==="《"){ce(t);return}var i=getSelection().getRangeAt(0),a=(0,s.pt)(i.startContainer);if(a||(kt(t,i),a=(0,s.pt)(i.startContainer)),!!a){for(var o=(0,M.ED)(a,t.wysiwyg.element,i).start,u=!0,h=o-1;h>a.textContent.substr(0,o).lastIndexOf(`
`);h--)if(a.textContent.charAt(h)!==" "&&a.textContent.charAt(h)!=="	"){u=!1;break}o===0&&(u=!1);for(var f=!0,h=o-1;h<a.textContent.length;h++)if(a.textContent.charAt(h)!==" "&&a.textContent.charAt(h)!==`
`){f=!1;break}f&&/^#{1,6} $/.test(a.textContent)&&(f=!1);var S=(0,T.c)(getSelection().getRangeAt(0).startContainer);if(S&&S.textContent===""&&(Be(t),S.remove()),u&&a.getAttribute("data-type")!=="code-block"||f||Vt(a.innerHTML)||Ut(a.innerHTML)&&a.previousElementSibling){typeof t.options.input=="function"&&t.options.input(x(t));return}r.inputType==="insertParagraph"&&n.element.innerHTML==="<p><br></p><p><br></p>"&&a.previousElementSibling.remove(),bt(t,i,r)}}}),this.element.addEventListener("click",function(r){if(r.target.tagName==="INPUT"){var i=r.target;i.checked?i.setAttribute("checked","checked"):i.removeAttribute("checked"),n.preventInput=!0,getSelection().rangeCount>0&&(0,M.jl)(getSelection().getRangeAt(0)),ce(t);return}if(r.target.tagName==="IMG"&&!r.target.parentElement.classList.contains("vditor-wysiwyg__preview")){r.target.getAttribute("data-type")==="link-ref"?$t(t,r.target):Qn(r,t);return}var a=(0,s._Y)(r.target,"A");if(a){t.options.link.click?t.options.link.click(a):t.options.link.isOpen&&window.open(a.getAttribute("href")),r.preventDefault();return}var o=(0,M.RN)(t);if(r.target.isEqualNode(n.element)&&n.element.lastElementChild&&o.collapsed){var u=n.element.lastElementChild.getBoundingClientRect();r.y>u.top+u.height&&(n.element.lastElementChild.tagName==="P"&&n.element.lastElementChild.textContent.trim().replace(E.Y.ZWSP,"")===""?(o.selectNodeContents(n.element.lastElementChild),o.collapse(!1)):(n.element.insertAdjacentHTML("beforeend",'<p data-block="0">'.concat(E.Y.ZWSP,"<wbr></p>")),(0,M.ir)(n.element,o)))}Ge(t);var h=(0,s.KJ)(r.target,"vditor-wysiwyg__preview");h||(h=(0,s.KJ)((0,M.RN)(t).startContainer,"vditor-wysiwyg__preview")),h&&at(h,t),pt(r,t)}),this.element.addEventListener("keyup",function(r){if(!(r.isComposing||(0,l._0)(r))){r.key==="Enter"&&ye(t),(r.key==="Backspace"||r.key==="Delete")&&t.wysiwyg.element.innerHTML!==""&&t.wysiwyg.element.childNodes.length===1&&t.wysiwyg.element.firstElementChild&&t.wysiwyg.element.firstElementChild.tagName==="P"&&t.wysiwyg.element.firstElementChild.childElementCount===0&&(t.wysiwyg.element.textContent===""||t.wysiwyg.element.textContent===`
`)&&(t.wysiwyg.element.innerHTML="");var i=(0,M.RN)(t);if(r.key==="Backspace"&&(0,l.gm)()&&i.startContainer.textContent===`
`&&i.startOffset===1&&(i.startContainer.textContent=""),kt(t,i),Ge(t),!(r.key!=="ArrowDown"&&r.key!=="ArrowRight"&&r.key!=="Backspace"&&r.key!=="ArrowLeft"&&r.key!=="ArrowUp")){(r.key==="ArrowLeft"||r.key==="ArrowRight")&&t.hint.render(t);var a=(0,s.KJ)(i.startContainer,"vditor-wysiwyg__preview");if(!a&&i.startContainer.nodeType!==3&&i.startOffset>0){var o=i.startContainer;o.classList.contains("vditor-wysiwyg__block")&&(a=o.lastElementChild)}if(a){var u=a.previousElementSibling;if(u.style.display==="none"){r.key==="ArrowDown"||r.key==="ArrowRight"?at(a,t):at(a,t,!1);return}var h=a.previousElementSibling;if(h.tagName==="PRE"&&(h=h.firstElementChild),r.key==="ArrowDown"||r.key==="ArrowRight"){var o=a.parentElement,f=Jn(o);if(f&&f.nodeType!==3){var S=f.querySelector(".vditor-wysiwyg__preview");if(S){at(S,t);return}}if(f.nodeType===3){for(;f.textContent.length===0&&f.nextSibling;)f=f.nextSibling;i.setStart(f,1)}else i.setStart(f.firstChild,0)}else i.selectNodeContents(h),i.collapse(!1)}}}})},e}(),Li=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ai=function(e){Li(t,e);function t(n,r){var i=e.call(this)||this;if(i.version=E.g,typeof n=="string"){if(r?r.cache?r.cache.id||(r.cache.id="vditor".concat(n)):r.cache={id:"vditor".concat(n)}:r={cache:{id:"vditor".concat(n)}},!document.getElementById(n))return i.showErrorTip("Failed to get element by id: ".concat(n)),i;n=document.getElementById(n)}var a=new Mi(r),o=a.merge();if(o.i18n)window.VditorI18n=o.i18n,i.init(n,o);else if(["en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(o.lang)){var u="vditorI18nScript",h=u+o.lang;document.querySelectorAll('head script[id^="'.concat(u,'"]')).forEach(function(f){f.id!==h&&document.head.removeChild(f)}),(0,L.Z)("".concat(o.cdn,"/dist/js/i18n/").concat(o.lang,".js"),h).then(function(){i.init(n,o)}).catch(function(f){i.showErrorTip("GET ".concat(o.cdn,"/dist/js/i18n/").concat(o.lang,".js net::ERR_ABORTED 404 (Not Found)"))})}else throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");return i}return t.prototype.showErrorTip=function(n){var r=new Ln;document.body.appendChild(r.element),r.show(n,0)},t.prototype.updateToolbarConfig=function(n){this.vditor.toolbar.updateConfig(this.vditor,n)},t.prototype.setTheme=function(n,r,i,a){this.vditor.options.theme=n,de(this.vditor),r&&(this.vditor.options.preview.theme.current=r,(0,J.H)(r,a||this.vditor.options.preview.theme.path)),i&&(this.vditor.options.preview.hljs.style=i,(0,An.h)(i,this.vditor.options.cdn))},t.prototype.getValue=function(){return x(this.vditor)},t.prototype.getCurrentMode=function(){return this.vditor.currentMode},t.prototype.focus=function(){this.vditor.currentMode==="sv"?this.vditor.sv.element.focus():this.vditor.currentMode==="wysiwyg"?this.vditor.wysiwyg.element.focus():this.vditor.currentMode==="ir"&&this.vditor.ir.element.focus()},t.prototype.blur=function(){this.vditor.currentMode==="sv"?this.vditor.sv.element.blur():this.vditor.currentMode==="wysiwyg"?this.vditor.wysiwyg.element.blur():this.vditor.currentMode==="ir"&&this.vditor.ir.element.blur()},t.prototype.disabled=function(){b(this.vditor,["subToolbar","hint","popover"]),d(this.vditor.toolbar.elements,E.Y.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","false")},t.prototype.enable=function(){m(this.vditor.toolbar.elements,E.Y.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor.undo.resetIcon(this.vditor),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","true")},t.prototype.getSelection=function(){if(this.vditor.currentMode==="wysiwyg")return st(this.vditor.wysiwyg.element);if(this.vditor.currentMode==="sv")return st(this.vditor.sv.element);if(this.vditor.currentMode==="ir")return st(this.vditor.ir.element)},t.prototype.renderPreview=function(n){this.vditor.preview.render(this.vditor,n)},t.prototype.getCursorPosition=function(){return(0,M.Ey)(this.vditor[this.vditor.currentMode].element)},t.prototype.isUploading=function(){return this.vditor.upload.isUploading},t.prototype.clearCache=function(){this.vditor.options.cache.enable&&(0,l.D)()&&localStorage.removeItem(this.vditor.options.cache.id)},t.prototype.disabledCache=function(){this.vditor.options.cache.enable=!1},t.prototype.enableCache=function(){if(!this.vditor.options.cache.id)throw new Error("need options.cache.id, see https://ld246.com/article/1549638745630#options");this.vditor.options.cache.enable=!0},t.prototype.html2md=function(n){return this.vditor.lute.HTML2Md(n)},t.prototype.exportJSON=function(n){return this.vditor.lute.RenderJSON(n)},t.prototype.getHTML=function(){return Mn(this.vditor)},t.prototype.tip=function(n,r){this.vditor.tip.show(n,r)},t.prototype.setPreviewMode=function(n){Yt(n,this.vditor)},t.prototype.deleteValue=function(){window.getSelection().isCollapsed||document.execCommand("delete",!1)},t.prototype.updateValue=function(n){document.execCommand("insertHTML",!1,n)},t.prototype.insertValue=function(n,r){r===void 0&&(r=!0);var i=(0,M.RN)(this.vditor);i.collapse(!0);var a=document.createElement("template");a.innerHTML=n,i.insertNode(a.content.cloneNode(!0)),i.collapse(!1),this.vditor.currentMode==="sv"?(this.vditor.sv.preventInput=!0,r&&ie(this.vditor)):this.vditor.currentMode==="wysiwyg"?r&&bt(this.vditor,getSelection().getRangeAt(0)):this.vditor.currentMode==="ir"&&(this.vditor.ir.preventInput=!0,r&&Fe(this.vditor,getSelection().getRangeAt(0),!0))},t.prototype.insertMD=function(n){this.vditor.currentMode==="ir"?(0,M.Z2)(this.vditor.lute.Md2VditorIRDOM(n),this.vditor):this.vditor.currentMode==="wysiwyg"?(0,M.Z2)(this.vditor.lute.Md2VditorDOM(n),this.vditor):jt(this.vditor,n),this.vditor.outline.render(this.vditor),ee(this.vditor)},t.prototype.setValue=function(n,r){var i=this;r===void 0&&(r=!1),this.vditor.currentMode==="sv"?(this.vditor.sv.element.innerHTML="<div data-block='0'>".concat(this.vditor.lute.SpinVditorSVDOM(n),"</div>"),Se(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})):this.vditor.currentMode==="wysiwyg"?Qt(this.vditor,n,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}):(this.vditor.ir.element.innerHTML=this.vditor.lute.Md2VditorIRDOM(n),this.vditor.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(a){Ee(a,i.vditor)}),Ke(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})),this.vditor.outline.render(this.vditor),n||(b(this.vditor,["emoji","headings","submenu","hint"]),this.vditor.wysiwyg.popover&&(this.vditor.wysiwyg.popover.style.display="none"),this.clearCache()),r&&this.clearStack()},t.prototype.insertEmptyBlock=function(n){lt(this.vditor,n)},t.prototype.clearStack=function(){this.vditor.undo.clearStack(this.vditor),this.vditor.undo.addToUndoStack(this.vditor)},t.prototype.destroy=function(){this.vditor.element.innerHTML=this.vditor.originalInnerHTML,this.vditor.element.classList.remove("vditor"),this.vditor.element.removeAttribute("style");var n=document.getElementById("vditorIconScript");n&&n.remove(),this.clearCache(),Zt(),this.vditor.wysiwyg.unbindListener()},t.prototype.getCommentIds=function(){return this.vditor.currentMode!=="wysiwyg"?[]:this.vditor.wysiwyg.getComments(this.vditor,!0)},t.prototype.hlCommentIds=function(n){if(this.vditor.currentMode==="wysiwyg"){var r=function(i){i.classList.remove("vditor-comment--hover"),n.forEach(function(a){i.getAttribute("data-cmtids").indexOf(a)>-1&&i.classList.add("vditor-comment--hover")})};this.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(i){r(i)}),this.vditor.preview.element.style.display!=="none"&&this.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(i){r(i)})}},t.prototype.unHlCommentIds=function(n){if(this.vditor.currentMode==="wysiwyg"){var r=function(i){n.forEach(function(a){i.getAttribute("data-cmtids").indexOf(a)>-1&&i.classList.remove("vditor-comment--hover")})};this.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(i){r(i)}),this.vditor.preview.element.style.display!=="none"&&this.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(i){r(i)})}},t.prototype.removeCommentIds=function(n){var r=this;if(this.vditor.currentMode==="wysiwyg"){var i=function(a,o){var u=a.getAttribute("data-cmtids").split(" ");u.find(function(h,f){if(h===o)return u.splice(f,1),!0}),u.length===0?(a.outerHTML=a.innerHTML,(0,M.RN)(r.vditor).collapse(!0)):a.setAttribute("data-cmtids",u.join(" "))};n.forEach(function(a){r.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(o){i(o,a)}),r.vditor.preview.element.style.display!=="none"&&r.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(o){i(o,a)})}),ce(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})}},t.prototype.init=function(n,r){var i=this;this.vditor={currentMode:r.mode,element:n,hint:new wr(r.hint.extend),lute:void 0,options:r,originalInnerHTML:n.innerHTML,outline:new Sr(window.VditorI18n.outline),tip:new Ln},this.vditor.sv=new Mr(this.vditor),this.vditor.undo=new _i,this.vditor.wysiwyg=new ki(this.vditor),this.vditor.ir=new vr(this.vditor),this.vditor.toolbar=new Si(this.vditor),r.resize.enable&&(this.vditor.resize=new Cr(this.vditor)),this.vditor.toolbar.elements.devtools&&(this.vditor.devtools=new c),(r.upload.url||r.upload.handler)&&(this.vditor.upload=new lr),(0,L.Z)(r._lutePath||"".concat(r.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then(function(){i.vditor.lute=(0,Er.X)({autoSpace:i.vditor.options.preview.markdown.autoSpace,gfmAutoLink:i.vditor.options.preview.markdown.gfmAutoLink,codeBlockPreview:i.vditor.options.preview.markdown.codeBlockPreview,emojiSite:i.vditor.options.hint.emojiPath,emojis:i.vditor.options.hint.emoji,fixTermTypo:i.vditor.options.preview.markdown.fixTermTypo,footnotes:i.vditor.options.preview.markdown.footnotes,headingAnchor:!1,inlineMathDigit:i.vditor.options.preview.math.inlineDigit,linkBase:i.vditor.options.preview.markdown.linkBase,linkPrefix:i.vditor.options.preview.markdown.linkPrefix,listStyle:i.vditor.options.preview.markdown.listStyle,mark:i.vditor.options.preview.markdown.mark,mathBlockPreview:i.vditor.options.preview.markdown.mathBlockPreview,paragraphBeginningSpace:i.vditor.options.preview.markdown.paragraphBeginningSpace,sanitize:i.vditor.options.preview.markdown.sanitize,toc:i.vditor.options.preview.markdown.toc}),i.vditor.preview=new _r(i.vditor),He(i.vditor),r.after&&r.after(),r.icon&&(0,L.U)("".concat(r.cdn,"/dist/js/icons/").concat(r.icon,".js"),"vditorIconScript")})},t}(G.default);const xi=Ai})(),ke=ke.default,ke})())}(Mt)),Mt.exports}var zi=qi();const Gi=Ni(zi),Zi=Wn({__name:"preview",props:Bn({height:{default:500},options:{default:()=>({})}},{value:{type:String,default:""},valueModifiers:{}}),emits:Bn(["mounted"],["update:value"]),setup(Oe,{emit:Me}){const we=Oe,Je=Me,X=Ii("vditorRef"),ke=ji(null),{isDark:G,locale:E}=Yi();Un(G,x=>{var c;const L=x?"dark":"light";(c=ke.value)==null||c.setTheme(x?"dark":"classic",L,L)});const _=Pi(Oe,"value");return Un(_,x=>{var L;(L=ke.value)==null||L.setValue(x)}),Bi(()=>{ke.value=new Gi(X.value,Pn({mode:"wysiwyg",value:_.value,height:we.height,lang:E.value.replace("-","_"),cache:{enable:!1},theme:G.value?"dark":"classic",toolbar:[],after(){var x;Je("mounted"),(x=ke.value)==null||x.disabled()}},we.options))}),Ui(()=>{var x;(x=ke.value)==null||x.destroy(),ke.value=null}),(x,L)=>(qn(),Vi("div",{ref_key:"vditorRef",ref:X},null,512))}}),Ji=`# 1.4.0

**FEATURES**

- 菜单管理(通用方法) 保存表格滚动/展开状态并执行回调 用于树表在执行 新增/编辑/删除等操作后 依然在当前位置(体验优化)

- 菜单管理 级联删除 删除菜单和children

**REFACTOR**

- 除个人中心外所有本地路由改为从后端返回(需要执行更新sql)
- 流程图预览改为logicflow预览而非图片 ...然后后端又更新了 又改成iframe了
- 菜单管理 新增角色校验(与后端权限保持一致) 只有superadmin可进行增删改

# 1.3.6

**BUG FIX**

- oss配置switch切换 导致报错\`存储类型找不到\`
- 文件上传无法正确清除(innerList)

# 1.3.5

**BUG FIX**

- 某些带Vxe表格弹窗 关闭后没有正常清理表格数据的问题

# 1.3.4

**BUG FIX**

- 文件上传多次触发导致数据不一致 https://gitee.com/dapppp/ruoyi-plus-vben5/issues/IC3BK6

**PREFORMANCE**

- 浏览器返回按钮/手势操作时 弹窗不会被关闭(keepAlive导致)

# 1.3.3

**BUG FIX**

- 工作流list展示在开启缩放会有误差导致触底逻辑不会触发

**OTHER**

- 代码生成预览对模板的提示...（下载都懒得点一下吗）

# 1.3.2

**REFACTOR**

- 所有表格操作列宽度调整为'auto', 这样会根据子元素宽度适配(比如没有分配权限的情况)
- 菜单图标更新了一部分 sql同步更新

**OTHER**

- 暂时锁死vite依赖 i18n会报错

# 1.3.1

**REFACTOR**

- 所有Modal/Drawer表单关闭前会进行表单数据对比来弹出提示框
- 字典项颜色选择从\`原生input type=color\`改为\`vue3-colorpicker\`组件
- 全局Header: ClientID 更改大小写 [spring的问题导致](https://gitee.com/dapppp/ruoyi-plus-vben5/issues/IC0BDS)

**BUG FIX**

- getVxePopupContainer逻辑调整 解决表格固定高度展开不全的问题

**FEATURES**

- 字典渲染支持loading(length为0情况)

**OTHERS**

- useForm的组件改为异步导入(官方更新) bootstrap.js体积从2M降到600K 首屏加载速度提升

# 1.3.0

注意: 如果你使用老版本的\`文件上传\`/\`图片上传\` 可暂时使用

- \`component: 'ImageUploadOld'\`
- \`component: 'FileUploadOld'\`

代替 **建议替换为新版本**

大致变动:

- \`accept string[] -> string\`
- \`resultField 已经移除 统一使用ossId\`
- \`maxNumber -> maxCount\`

具体参数查看: \`apps/web-antd/src/components/upload/src/props.d.ts\`

不再推荐使用useDescription, 这个版本会标记为@deprecated, 下个次版本将会移除

框架所有使用useDescription组件的会替换为原生(TODO)

**REFACTOR**

- **文件上传/图片上传重构(破坏性更新 不兼容之前的api)**
- **文件上传/图片上传不再支持url用法 强制使用ossId**
- TableSwitch组件重构
- 管理员租户切换不再返回首页 直接刷新当前页(除特殊页面外会回到首页)
- 租户切换Select增加loading
- ~~modalLoading/drawerLoading改为调用内部的lock/unlock方法~~ 有待商榷暂时按老版本逻辑不变
- 登录验证码 增加loading
- DictEnum使用const代替enum
- TinyMCE组件重构 移除冗余代码/功能 增加loading

**ALPHA功能**

- 弹窗表单数据更改关闭时的提示框(可能最终不会加入) 测试页面: 参数管理

**BUG FIX**

- 重新登录 字典会unknown的情况[详细分析](https://gitee.com/dapppp/ruoyi-plus-vben5/issues/IBY27D)
- 测试菜单 请假申请 选中删除 需要根据状态判断
- 修复文件/图片在Safari中无法上传 file-type库与Safari不兼容导致
- 头像裁剪 图片加载失败一直处于loading无法上传
- 头像裁剪 私有桶会拼接timestamp参数导致sign计算异常无法上传 感谢cropperjs作者 https://github.com/fengyuanchen/cropperjs/issues/1230
- 租户选择下拉框会跟随body滚动(将下拉框样式的默认absolute改为fixed)

**OTHER**

- 字典管理 字典类型 表格选中行增加bold效果
- 全局圆角修改 与antd保持一致
- vditor(Markdown)升级3.10.9
- 老版本的文件/图片上传将于下个版本移除
- useDescription将于下个版本移除
- getVxePopupContainer与新版Vxe不兼容 先返回body(会导致滚动不跟随)后续版本再优化

# 1.2.3

**BUG FIX**

- \`withDefaultPlaceholder\`中将\`placeholder\`修改为computed, 解决后续使用\`updateSchema\`无法正常更新显示placeholder(响应式问题)

- 流程定义 修改accept类型 解决无法拖拽上传

**FEATURES**

- 增加\`环境变量\`打包配置demo -> build:antd:test
- 角色管理 勾选权限组件添加对错误用法的校验提示

**REFACTOR**

- OAuth内部逻辑重构 增加新的默认OAuth登录方式
- 重构部分setup组件为setup语法糖形式

# 1.2.2

**FEATURES**

- 代码生成支持路径方式生成
- 代码生成 支持选择表单生成类型(需要模板支持)
- 工作流 支持按钮权限

# 1.2.1

# BUG FIXES

- 客户端管理 错误的status disabled
- modal/drawer升级后zIndex(2000)会遮挡Tinymce的下拉框zIndex(1300)

# 1.2.0

**REFACTOR**

- 菜单选择组件重构为Table形式
- 字典相关功能重构 采用一个Map储存字典(之前为两个Map)
- 代码生成配置页面重构 去除步骤条

**Features**

- 对接后端工作流
- ~~通用的vxe-table排序事件(排序逻辑改为在排序事件中处理而非在api处理)~~
- getDict/getDictOptions 提取公共逻辑 减少冗余代码
- 字典新增对Number类型的支持 -> \`getDictOptions('', true);\`即可获取number类型的value
- 文件上传 增加上传进度条 下方上传提示
- 图片上传 增加上传进度条 下方上传提示
- oss下载进度提示

**BUG FIXES**

- 字典项为空时getDict方法无限调用接口(无奈兼容 不给字典item本来就是错误用法)
- 表格排序翻页会丢失排序参数
- 下载文件时(responseType === 'blob')需要判断下载失败(返回json而非二进制)的情况
- requestClient缺失i18n内容

**OTHERS**

- 用户管理 新增只获取一次(mounted)默认密码而非每次打开modal都获取
- \`apps/web-antd/src/utils/dict.ts\` \`getDict\`方法将于下个版本删除 使用\`getDictOptions\`替代
- VxeTable升级V4.10.0
- 移除\`@deprecated\` \`apps/web-antd/src/adapter/vxe-table.ts\`的\`tableCheckboxEvent\`方法
- 移除\`由于更新方案弃用的\` \`apps/web-antd/src/adapter/vxe-table.ts\`的\`vxeSortEvent\`方法
- 移除apps下的ele和naive目录

# 1.1.3

**REFACTOR**

- 重构: 判断vxe-table的复选框是否选中

**Bug Fixes**

- 节点树在编辑 & 空数组(不勾选)情况 勾选节点会造成watch延迟触发 导致会带上父节点id造成id重复
- 节点树在节点独立情况下的控制台warning: Invalid prop: type check failed for prop "value". Expected Array, got Object

**Others**

- 角色管理 优化Drawer布局
- unplugin-vue-components插件(默认未开启) 需要排除Button组件 全局已经默认导入了

**BUG FIXES**

- 操作日志详情 在description组件中json预览样式异常
- 微服务版本 区间查询和中文搜索条件一起使用 无法正确查询

# 1.1.2

**Features**

- Options转Enum工具函数

**OTHERS**

- 菜单管理 改为虚拟滚动
- 移除requestClient的一些冗余参数
- 主动退出登录(右上角个人选项)不需要带跳转地址

**BUG FIXES**

- 语言 漏加Content-Language请求头
- 用户管理/岗位管理 左边部门树错误emit导致会调用两次列表api

# 1.1.1

**REFACTOR**

- 使用VxeTable重构OAuth账号绑定列表(替代antdv的Table)
- commonDownloadExcel方法 支持处理区间选择器字段导出excel

**BUG FIXES**

- 修复在Modal/Drawer中使用VxeTable时, 第二次打开表单参数依旧为第一次提交的参数

**OTHERS**

- 废弃downloadExcel方法 统一使用commonDownloadExcel方法

# 1.1.0

**FEATURES**

- 支持离线图标功能(全局可在内网环境中使用)

**BUG FIXES**

- 在VxeTable固定列时, getPopupContainer会导致宽度不够, 弹出层样式异常 解决办法(将弹窗元素挂载到VXe滚动容器上)

**OTHERS**

- 代码生成 - 字段信息修改 改为minWidth 防止在高分辨率屏幕出现空白

# 1.0.0

**FEATURES**

- 用户管理 新增从参数取默认密码
- 全局表格加上id 方便进行缓存列排序的操作
- 支持菜单名称i18n
- 登录页 验证码登录
- Markdown编辑/预览组件(基于vditor)
- VxeTable搜索表单 enter提交

**BUG FIXES**

- 登录页面 关闭租户后下拉框没有正常隐藏
- 字典管理 关闭租户不应显示\`同步租户字典\`按钮
- 登录日志 漏掉了登录日志日期查询
- 登出相关逻辑在并发(非await)情况下重复执行的问题
- VxeTable在开启/关闭查询表单时 需要使用不同的padding
- VxeTable表格刷新 默认为reload 修改为在当前页刷新(query)
- 岗位管理 部门参数错误
- 角色管理 菜单分配 节点独立下的回显及提交问题
- 租户管理 套餐管理 回显时候\`已选中节点\`数量为0
- 用户管理 更新用户时打开drawer需要加载该部门下的岗位信息

**OTHERS**

- 登录页 租户选择框浮层固定高度[256px] 超过高度自动滚动
- 表单的Label默认方向改为\`top\` 支持\\n换行
- 所有表格的搜索加上allowClear属性 支持清除
- vxe表格loading 只加载表格 不加载上面的表单

# 1.0.0-beta (2024-10-8)

**FEATURES**

- 基础功能已经开发完毕
- 工作流相关模块等待后端重构后开发
`,na=Wn({__name:"index",setup(Oe){const Me=Vn(Ji),we=Vn(!0);return(Je,X)=>(qn(),Ki(zt(Fi),{"auto-content-height":!0},{default:Kn(()=>[Yn(zt(Ri),{spinning:we.value,tip:"加载markdown中..."},{default:Kn(()=>[Yn(zt(Zi),{value:Me.value,"onUpdate:value":X[0]||(X[0]=ke=>Me.value=ke),height:"100%",class:"min-h-[50vh]",onMounted:X[1]||(X[1]=ke=>we.value=!1)},null,8,["value"])]),_:1},8,["spinning"])]),_:1}))}});export{na as default};
