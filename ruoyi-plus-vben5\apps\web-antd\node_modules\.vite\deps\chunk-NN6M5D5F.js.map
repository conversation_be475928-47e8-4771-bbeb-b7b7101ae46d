{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/shallowequal.js"], "sourcesContent": ["import { toRaw } from 'vue';\nfunction shallowEqual(objA, objB, compare, compareContext) {\n  let ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n  if (ret !== void 0) {\n    return !!ret;\n  }\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n  // Test for A's keys different from B.\n  for (let idx = 0; idx < keysA.length; idx++) {\n    const key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    const valueA = objA[key];\n    const valueB = objB[key];\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n    if (ret === false || ret === void 0 && valueA !== valueB) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default function (value, other) {\n  return shallowEqual(toRaw(value), toRaw(other));\n}"], "mappings": ";;;;;AACA,SAAS,aAAa,MAAM,MAAM,SAAS,gBAAgB;AACzD,MAAI,MAAM,UAAU,QAAQ,KAAK,gBAAgB,MAAM,IAAI,IAAI;AAC/D,MAAI,QAAQ,QAAQ;AAClB,WAAO,CAAC,CAAC;AAAA,EACX;AACA,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM;AAC1E,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,OAAO,UAAU,eAAe,KAAK,IAAI;AAEjE,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,UAAM,MAAM,MAAM,GAAG;AACrB,QAAI,CAAC,gBAAgB,GAAG,GAAG;AACzB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,KAAK,GAAG;AACvB,UAAM,SAAS,KAAK,GAAG;AACvB,UAAM,UAAU,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,IAAI;AACpE,QAAI,QAAQ,SAAS,QAAQ,UAAU,WAAW,QAAQ;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACe,SAAR,qBAAkB,OAAO,OAAO;AACrC,SAAO,aAAa,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC;AAChD;", "names": []}