var f=(m,l,s)=>new Promise((d,e)=>{var p=t=>{try{o(s.next(t))}catch(i){e(i)}},u=t=>{try{o(s.throw(t))}catch(i){e(i)}},o=t=>t.done?d(t.value):Promise.resolve(t.value).then(p,u);o((s=s.apply(m,l)).next())});import"./vxe-table-DzEj5Fop.js";import{_ as b,s as C}from"./send-msg-modal.vue_vue_type_script_setup_true_lang-Dq6NnfRe.js";import h from"./index-BeyziwLP.js";import{_ as k}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as S,l as c,h as w,o as N,w as n,a,b as r,k as _,j as V}from"../jse/index-index-C-MnMZEz.js";import{u as v}from"./use-vxe-grid-BC7vZzEr.js";import{u as y}from"./use-modal-CeMSCP2m.js";import"./bootstrap-DCMzVRvD.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./x-Bfkqqjgb.js";const O=S({__name:"index",setup(m){const l={columns:[{title:"用户ID",field:"userId"},{title:"用户账号",field:"userName"},{title:"用户昵称",field:"nickName"},{title:"用户部门",field:"deptName"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:()=>f(null,null,function*(){return{rows:yield C()}})}},rowConfig:{isHover:!1,keyField:"userId",height:48},id:"sse-index"},[s]=v({gridOptions:l}),[d,e]=y({connectedComponent:b});function p(){e.setData({}),e.open()}function u(o){e.setData({userId:o}),e.open()}return(o,t)=>{const i=c("a-button"),g=c("ghost-button");return N(),w(r(k),{"auto-content-height":!0,description:"这这里可以进行[Server-sent events]测试 非官方功能",title:"SSE测试"},{default:n(()=>[a(r(s),null,{"toolbar-actions":n(()=>t[0]||(t[0]=[V("span",{class:"pl-[7px] text-[16px]"},"在线用户列表",-1)])),"toolbar-tools":n(()=>[a(r(h),null,{default:n(()=>[a(i,{onClick:p},{default:n(()=>t[1]||(t[1]=[_("发送全体消息")])),_:1,__:[1]})]),_:1})]),action:n(({row:x})=>[a(g,{onClick:B=>u(x.userId)},{default:n(()=>t[2]||(t[2]=[_(" 发送消息 ")])),_:2,__:[2]},1032,["onClick"])]),_:1}),a(r(d))]),_:1})}}});export{O as default};
