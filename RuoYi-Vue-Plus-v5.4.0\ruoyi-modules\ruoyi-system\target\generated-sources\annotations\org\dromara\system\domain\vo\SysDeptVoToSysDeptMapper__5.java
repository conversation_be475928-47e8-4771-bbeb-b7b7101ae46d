package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__5;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysDeptBoToSysDeptMapper__5.class,SysDeptToSysDeptVoMapper__5.class,SysDeptToSysDeptVoMapper__5.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper__5 extends BaseMapper<SysDeptVo, SysDept> {
}
