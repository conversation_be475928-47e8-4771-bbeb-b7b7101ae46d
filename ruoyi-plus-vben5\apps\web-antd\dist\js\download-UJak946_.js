var p=(o,r,n)=>new Promise((e,a)=>{var c=d=>{try{t(n.next(d))}catch(l){a(l)}},i=d=>{try{t(n.throw(d))}catch(l){a(l)}},t=d=>d.done?e(d.value):Promise.resolve(d.value).then(c,i);t((n=n.apply(o,r)).next())});import{ap as w,$ as s,i as R}from"./bootstrap-DCMzVRvD.js";import{P as m,Q as b}from"../jse/index-index-C-MnMZEz.js";function f(o,r){return o=m(o),!r||!Array.isArray(r)||r.forEach(([n,[e,a],c="YYYY-MM-DD"])=>{if(e&&a&&o[n]===null&&(Reflect.deleteProperty(o,e),Reflect.deleteProperty(o,a)),!o[n]){Reflect.deleteProperty(o,n);return}const[i,t]=o[n];if(c===null)o[e]=i,o[a]=t;else if(R(c))o[e]=c(i,e),o[a]=c(t,a);else{const[d,l]=Array.isArray(c)?c:[c,c];o[e]=i?b(i,d):void 0,o[a]=t?b(t,l):void 0}Reflect.deleteProperty(o,n)}),o}function g(a,c){return p(this,arguments,function*(o,r,n={},e={}){const i=w.loading(s("pages.common.downloadLoading"),0);try{const{withRandomName:t=!0,fieldMappingTime:d}=e,l=yield o(f(n,d));D(l,r,t)}catch(t){console.error(t)}finally{i()}})}function D(o,r,n=!0){let e=r;n&&(e=`${r}-${Date.now()}.xlsx`),L(o,e)}function L(o,r,n,e){const a=[o],c=new Blob(a,{type:"application/octet-stream"}),i=window.URL.createObjectURL(c),t=document.createElement("a");t.style.display="none",t.href=i,t.setAttribute("download",r),t.download===void 0&&t.setAttribute("target","_blank"),document.body.append(t),t.click(),t.remove(),window.URL.revokeObjectURL(i)}export{g as c,L as d};
