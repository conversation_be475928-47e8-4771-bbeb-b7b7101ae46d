{"doc": " 正则相关工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "extractFromString", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 从输入字符串中提取匹配的部分，如果没有匹配则返回默认值\n\n @param input        要提取的输入字符串\n @param regex        用于匹配的正则表达式，可以使用 {@link RegexConstants} 中定义的常量\n @param defaultInput 如果没有匹配时返回的默认值\n @return 如果找到匹配的部分，则返回匹配的部分，否则返回默认值\n"}], "constructors": []}