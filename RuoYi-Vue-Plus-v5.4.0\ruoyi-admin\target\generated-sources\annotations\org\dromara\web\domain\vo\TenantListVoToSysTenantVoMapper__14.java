package org.dromara.web.domain.vo;

import io.github.linpeilie.AutoMapperConfig__695;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.domain.vo.SysTenantVoToTenantListVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__695.class,
    uses = {SysTenantVoToTenantListVoMapper__14.class},
    imports = {}
)
public interface TenantListVoToSysTenantVoMapper__14 extends BaseMapper<TenantListVo, SysTenantVo> {
}
