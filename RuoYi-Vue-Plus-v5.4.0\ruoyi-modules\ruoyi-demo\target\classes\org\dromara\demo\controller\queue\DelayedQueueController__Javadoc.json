{"doc": " 延迟队列 演示案例\n <p>\n 轻量级队列 重量级数据量 请使用 MQ\n 例如: 创建订单30分钟后过期处理\n <p>\n 集群测试通过 同一个数据只会被消费一次 做好事务补偿\n 集群测试流程 两台集群分别开启订阅 在其中一台发送数据 观察接收消息的规律\n\n <AUTHOR> Li\n @version 3.6.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "subscribe", "paramTypes": ["java.lang.String"], "doc": " 订阅队列\n\n @param queueName 队列名\n"}, {"name": "add", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 添加队列数据\n\n @param queueName 队列名\n @param orderNum  订单号\n @param time      延迟时间(秒)\n"}, {"name": "remove", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除队列数据\n\n @param queueName 队列名\n @param orderNum  订单号\n"}, {"name": "destroy", "paramTypes": ["java.lang.String"], "doc": " 销毁队列\n\n @param queueName 队列名\n"}], "constructors": []}