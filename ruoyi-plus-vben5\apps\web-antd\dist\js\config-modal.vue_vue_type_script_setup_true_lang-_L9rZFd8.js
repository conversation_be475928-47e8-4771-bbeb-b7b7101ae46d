var f=(c,d,a)=>new Promise((i,m)=>{var u=e=>{try{s(a.next(e))}catch(n){m(n)}},t=e=>{try{s(a.throw(e))}catch(n){m(n)}},s=e=>e.done?i(e.value):Promise.resolve(e.value).then(u,t);s((a=a.apply(c,d)).next())});import{ar as p,$ as g,aj as C}from"./bootstrap-DCMzVRvD.js";import{c as I,a as V,b as k}from"./index-D1nLcUEe.js";import{u as T,d as b}from"./popup-D6rC6QBG.js";import{g as N}from"./dict-BLkXAGS5.js";import{r as x}from"./render-BxXtQdeV.js";import{a as B}from"./get-popup-container-P4S1sr5h.js";import{d as v,p as D,B as Y,P as q,h as P,o as z,w as E,a as F,b as h}from"../jse/index-index-C-MnMZEz.js";import{u as O}from"./use-modal-CeMSCP2m.js";const H=()=>[{component:"Input",fieldName:"configName",label:"参数名称"},{component:"Input",fieldName:"configKey",label:"参数键名"},{component:"Select",componentProps:{getPopupContainer:B,options:N(p.SYS_YES_NO)},fieldName:"configType",label:"系统内置"},{component:"RangePicker",fieldName:"createTime",label:"创建时间"}],J=[{type:"checkbox",width:60},{title:"参数名称",field:"configName"},{title:"参数KEY",field:"configKey"},{title:"参数Value",field:"configValue"},{title:"系统内置",field:"configType",width:120,slots:{default:({row:c})=>x(c.configType,p.SYS_YES_NO)}},{title:"备注",field:"remark"},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],A=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"configId",label:"参数主键"},{component:"Input",fieldName:"configName",label:"参数名称",rules:"required"},{component:"Input",fieldName:"configKey",label:"参数键名",rules:"required"},{component:"Textarea",formItemClass:"items-start",fieldName:"configValue",label:"参数键值",componentProps:{autoSize:!0},rules:"required"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:N(p.SYS_YES_NO),optionType:"button"},defaultValue:"N",fieldName:"configType",label:"是否内置",rules:"required"},{component:"Textarea",fieldName:"remark",formItemClass:"items-start",label:"备注"}],Q=v({__name:"config-modal",emits:["reload"],setup(c,{emit:d}){const a=d,i=D(!1),m=Y(()=>i.value?g("pages.common.edit"):g("pages.common.add")),[u,t]=C({commonConfig:{labelWidth:80},schema:A(),showDefaultActions:!1}),{onBeforeClose:s,markInitialized:e,resetInitialized:n}=T({initializedGetter:b(t),currentGetter:b(t)}),[y,l]=O({fullscreenButton:!1,onBeforeClose:s,onClosed:w,onConfirm:_,onOpenChange:r=>f(null,null,function*(){if(!r)return null;l.modalLoading(!0);const{id:o}=l.getData();if(i.value=!!o,i.value&&o){const S=yield I(o);yield t.setValues(S)}yield e(),l.modalLoading(!1)})});function _(){return f(this,null,function*(){try{l.lock(!0);const{valid:r}=yield t.validate();if(!r)return;const o=q(yield t.getValues());yield i.value?V(o):k(o),n(),a("reload"),l.close()}catch(r){console.error(r)}finally{l.lock(!1)}})}function w(){return f(this,null,function*(){yield t.resetForm(),n()})}return(r,o)=>(z(),P(h(y),{title:m.value,class:"w-[550px]"},{default:E(()=>[F(h(u))]),_:1},8,["title"]))}});export{Q as _,J as c,H as q};
