var F=Object.defineProperty;var S=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var V=(n,e,t)=>e in n?F(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,E=(n,e)=>{for(var t in e||(e={}))j.call(e,t)&&V(n,t,e[t]);if(S)for(var t of S(e))G.call(e,t)&&V(n,t,e[t]);return n};var f=(n,e,t)=>new Promise((x,p)=>{var _=m=>{try{w(t.next(m))}catch(v){p(v)}},$=m=>{try{w(t.throw(m))}catch(v){p(v)}},w=m=>m.done?x(m.value):Promise.resolve(m.value).then(_,$);w((t=t.apply(n,e)).next())});import{aB as L,as as D,an as W}from"./bootstrap-DCMzVRvD.js";import{v as H}from"./vxe-table-DzEj5Fop.js";import{c as J}from"./index-DCFckLr6.js";import{c as K}from"./download-UJak946_.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";/* empty css                                                          */import{_ as Q}from"./flow-info-modal.vue_vue_type_script_setup_true_lang-CB-fzQzq.js";import{l as U,a as P,b as X}from"./index-Bt61TO42.js";import{c as Y,q as Z}from"./data-DSNSln3q.js";import T from"./index-BeyziwLP.js";import{_ as oo}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as to,l as A,S as eo,h as l,o as c,w as i,a as g,b as a,T as k,f as b,k as d,t as h}from"../jse/index-index-C-MnMZEz.js";import{u as io}from"./use-vxe-grid-BC7vZzEr.js";import{u as ro}from"./use-modal-CeMSCP2m.js";import{P as I}from"./index-DNdMANjv.js";import{g as B}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./relativeTime-DEqmspR6.js";import"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import"./index-BxBCzu2M.js";import"./Search-ClCped_G.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import"./index-CZhogUxH.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./index-Ollxi7Rl.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import"./user-select-modal-9zMWfXzj.js";import"./index-ocPq22VW.js";import"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import"./index-BLwHKR_M.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./x-Bfkqqjgb.js";import"./index-BELOxkuV.js";import"./index-qvRUEWLR.js";import"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import"./index-i2_yEmR1.js";import"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import"./index-D59rZjD-.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BIMmoqOy.js";import"./move-DLDqWE9R.js";import"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import"./rotate-cw-DzZTu9nW.js";import"./options-tag.vue_vue_type_script_setup_true_lang-k3ySxERw.js";const Tt=to({__name:"index",setup(n){const e={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:Z(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},t={checkboxConfig:{highlight:!0,reserve:!0,checkMethod:({row:o})=>["back","cancel","draft"].includes(o.status)},columns:Y,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(C,...y)=>f(null,[C,...y],function*({page:o},r={}){return yield U(E({pageNum:o.currentPage,pageSize:o.pageSize},r))})}},rowConfig:{keyField:"id"},id:"workflow-leave-index"},[x,p]=io({formOptions:e,gridOptions:t}),_=L();function $(){_.push("/workflow/leaveEdit/index")}function w(o){return f(this,null,function*(){_.push({path:"/workflow/leaveEdit/index",query:{id:o.id}})})}function m(o){return f(this,null,function*(){yield P(o.id),yield p.query()})}function v(o){return f(this,null,function*(){yield J({businessId:o.id,message:"申请人撤销流程！"}),yield p.query()})}function N(){const r=p.grid.getCheckboxRecords().map(C=>C.id);W.confirm({title:"提示",okType:"danger",content:`确认删除选中的${r.length}条记录吗？`,onOk:()=>f(null,null,function*(){yield P(r),yield p.query()})})}function R(){K(X,"请假申请数据",p.formApi.form.values,{fieldMappingTime:e.fieldMappingTime})}const[O,q]=ro({connectedComponent:Q});function z(o){q.setData({businessId:o.id}),q.open()}return(o,r)=>{const C=A("a-button"),y=A("ghost-button"),u=eo("access");return c(),l(a(oo),{"auto-content-height":!0},{default:i(()=>[g(a(x),{"table-title":"请假申请列表"},{"toolbar-tools":i(()=>[g(a(T),null,{default:i(()=>[k((c(),l(C,{onClick:R},{default:i(()=>[d(h(o.$t("pages.common.export")),1)]),_:1})),[[u,["workflow:leave:export"],"code"]]),k((c(),l(C,{disabled:!a(H)(a(p)),danger:"",type:"primary",onClick:N},{default:i(()=>[d(h(o.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[u,["workflow:leave:remove"],"code"]]),k((c(),l(C,{type:"primary",onClick:$},{default:i(()=>[d(h(o.$t("pages.common.add")),1)]),_:1})),[[u,["workflow:leave:add"],"code"]])]),_:1})]),action:i(({row:s})=>[g(a(T),null,{default:i(()=>[["draft","cancel","back"].includes(s.status)?k((c(),l(y,{key:0,onClick:D(M=>w(s),["stop"])},{default:i(()=>[d(h(o.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[u,["workflow:leave:edit"],"code"]]):b("",!0),g(a(I),{"get-popup-container":a(B),placement:"left",title:"确认撤销？",onConfirm:M=>v(s)},{default:i(()=>[["waiting"].includes(s.status)?k((c(),l(y,{key:0,onClick:r[0]||(r[0]=D(()=>{},["stop"]))},{default:i(()=>r[2]||(r[2]=[d(" 撤销 ")])),_:1,__:[2]})),[[u,["workflow:leave:edit"],"code"]]):b("",!0)]),_:2},1032,["get-popup-container","onConfirm"]),s.status!=="draft"?(c(),l(y,{key:1,onClick:M=>z(s)},{default:i(()=>r[3]||(r[3]=[d(" 详情 ")])),_:2,__:[3]},1032,["onClick"])):b("",!0),g(a(I),{"get-popup-container":a(B),placement:"left",title:"确认删除？",onConfirm:M=>m(s)},{default:i(()=>[["draft","cancel","back"].includes(s.status)?k((c(),l(y,{key:0,danger:"",onClick:r[1]||(r[1]=D(()=>{},["stop"]))},{default:i(()=>[d(h(o.$t("pages.common.delete")),1)]),_:1})),[[u,["workflow:leave:remove"],"code"]]):b("",!0)]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)]),_:1}),g(a(O))]),_:1})}}});export{Tt as default};
