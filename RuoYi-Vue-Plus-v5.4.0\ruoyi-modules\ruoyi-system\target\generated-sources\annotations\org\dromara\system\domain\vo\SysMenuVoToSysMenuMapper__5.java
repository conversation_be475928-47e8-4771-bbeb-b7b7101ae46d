package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__692;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMenu;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__692.class,
    uses = {SysMenuToSysMenuVoMapper__5.class,SysMenuToSysMenuVoMapper__5.class},
    imports = {}
)
public interface SysMenuVoToSysMenuMapper__5 extends BaseMapper<SysMenuVo, SysMenu> {
}
