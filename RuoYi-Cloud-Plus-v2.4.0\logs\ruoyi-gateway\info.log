2025-06-17 08:14:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-17 08:14:05 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 31212 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-17 08:14:05 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-17 08:14:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-17 08:14:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-17 08:14:13 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-17 08:14:13 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-17 08:14:13 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-17 08:14:14 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:14:14 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-17 08:14:25 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway *************:8080 register finished
2025-06-17 08:14:25 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-17 08:14:25 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 24.674 seconds (process running for 25.633)
2025-06-17 08:14:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-17 08:14:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-17 08:15:13 [boundedElastic-5] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:15:33 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:15:53 [boundedElastic-7] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:16:13 [boundedElastic-11] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:16:33 [boundedElastic-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:16:53 [boundedElastic-11] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:17:13 [boundedElastic-14] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:17:33 [boundedElastic-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:17:53 [boundedElastic-1] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:13 [boundedElastic-11] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:18:49 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 08:18:50 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[187]毫秒
2025-06-17 08:18:50 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 08:18:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[132]毫秒
2025-06-17 08:18:53 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 08:18:53 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 08:18:53 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[231]毫秒
2025-06-17 08:18:53 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[500]毫秒
2025-06-17 08:18:53 [boundedElastic-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:19:07 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 08:19:08 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[932]毫秒
2025-06-17 08:19:09 [boundedElastic-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 08:19:09 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[666]毫秒
2025-06-17 08:19:09 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 08:19:09 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-06-17 08:19:10 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJpTVpXWGNtY3JLVFhtMmdINmZST2dzSGFBQzBYUlNPRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Jaf9kcF-ntD4hP1H7Us_-GxjWPr1caaBb3uqYMs6rk0"]}]
2025-06-17 08:19:10 [boundedElastic-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 08:19:10 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[64]毫秒
2025-06-17 08:19:11 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[1173]毫秒
2025-06-17 08:19:11 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 08:19:11 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 08:19:11 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[30]毫秒
2025-06-17 08:19:11 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[95]毫秒
2025-06-17 08:19:11 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 08:19:11 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[19]毫秒
2025-06-17 08:19:13 [boundedElastic-10] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:19:14 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 08:19:14 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 08:19:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 08:19:14 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[25]毫秒
2025-06-17 08:19:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[25]毫秒
2025-06-17 08:19:14 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 08:19:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[55]毫秒
2025-06-17 08:19:14 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[149]毫秒
2025-06-17 08:19:30 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-17 08:19:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[14]毫秒
2025-06-17 08:19:30 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 08:19:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-17 08:19:30 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-17 08:19:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[10]毫秒
2025-06-17 08:19:30 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[20]毫秒
2025-06-17 08:19:30 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[29]毫秒
2025-06-17 08:19:30 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:19:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[54]毫秒
2025-06-17 08:19:33 [boundedElastic-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:19:34 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:19:34 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[31]毫秒
2025-06-17 08:19:36 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-17 08:19:36 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[10]毫秒
2025-06-17 08:19:36 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-17 08:19:36 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[33]毫秒
2025-06-17 08:19:53 [boundedElastic-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-17 08:19:53 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[27]毫秒
2025-06-17 08:19:53 [boundedElastic-10] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:19:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/1934536432063508481],无参数
2025-06-17 08:19:56 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/1934536432063508481],耗时:[16]毫秒
2025-06-17 08:19:56 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list/dept/1934536432063508481],无参数
2025-06-17 08:19:56 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list/dept/1934536432063508481],耗时:[32]毫秒
2025-06-17 08:19:56 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list/exclude/1934536432063508481],无参数
2025-06-17 08:19:56 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list/exclude/1934536432063508481],耗时:[16]毫秒
2025-06-17 08:19:59 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 08:19:59 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-17 08:19:59 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:19:59 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[21]毫秒
2025-06-17 08:20:01 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 08:20:01 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[16]毫秒
2025-06-17 08:20:05 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:05 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:05 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[30]毫秒
2025-06-17 08:20:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[34]毫秒
2025-06-17 08:20:07 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-17 08:20:07 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[17]毫秒
2025-06-17 08:20:11 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_yes_no],无参数
2025-06-17 08:20:11 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_yes_no],耗时:[11]毫秒
2025-06-17 08:20:11 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:11 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/list],耗时:[26]毫秒
2025-06-17 08:20:13 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:20:19 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-06-17 08:20:19 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[24]毫秒
2025-06-17 08:20:19 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:19 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[22]毫秒
2025-06-17 08:20:22 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_oper_type],无参数
2025-06-17 08:20:22 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_common_status],无参数
2025-06-17 08:20:22 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_common_status],耗时:[15]毫秒
2025-06-17 08:20:22 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_oper_type],耗时:[17]毫秒
2025-06-17 08:20:22 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/operlog/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:22 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/operlog/list],耗时:[34]毫秒
2025-06-17 08:20:31 [boundedElastic-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/logininfor/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:31 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/logininfor/list],耗时:[24]毫秒
2025-06-17 08:20:33 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],无参数
2025-06-17 08:20:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],耗时:[20]毫秒
2025-06-17 08:20:33 [boundedElastic-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:33 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:20:33 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[166]毫秒
2025-06-17 08:20:38 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:20:38 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/config/list],耗时:[73]毫秒
2025-06-17 08:20:53 [boundedElastic-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:20:56 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/config/5],无参数
2025-06-17 08:20:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/config/5],耗时:[34]毫秒
2025-06-17 08:21:05 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/config/1],无参数
2025-06-17 08:21:05 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/config/1],耗时:[28]毫秒
2025-06-17 08:21:13 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:21:15 [boundedElastic-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/config/5],无参数
2025-06-17 08:21:15 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/config/5],耗时:[18]毫秒
2025-06-17 08:21:17 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/oss/config/4],无参数
2025-06-17 08:21:17 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/oss/config/4],耗时:[19]毫秒
2025-06-17 08:21:30 [boundedElastic-23] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_grant_type],无参数
2025-06-17 08:21:30 [boundedElastic-23] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_device_type],无参数
2025-06-17 08:21:30 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_grant_type],耗时:[19]毫秒
2025-06-17 08:21:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_device_type],耗时:[16]毫秒
2025-06-17 08:21:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/client/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:21:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/client/list],耗时:[26]毫秒
2025-06-17 08:21:33 [boundedElastic-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:21:35 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/client/1],无参数
2025-06-17 08:21:35 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/client/1],耗时:[14]毫秒
2025-06-17 08:21:53 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:22:13 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:22:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:22:53 [boundedElastic-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:23:13 [boundedElastic-10] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:23:23 [boundedElastic-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 08:23:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[11]毫秒
2025-06-17 08:23:23 [boundedElastic-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 08:23:23 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[46]毫秒
2025-06-17 08:23:24 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 08:23:24 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 08:23:24 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-17 08:23:24 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[18]毫秒
2025-06-17 08:23:30 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 08:23:30 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[165]毫秒
2025-06-17 08:23:30 [boundedElastic-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 08:23:30 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[27]毫秒
2025-06-17 08:23:30 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 08:23:30 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[22]毫秒
2025-06-17 08:23:30 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 08:23:30 [boundedElastic-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 08:23:30 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-06-17 08:23:30 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJJZDlTaTMydllWTWdSdm53RjQ2eEFhZUtscTBsaDVteSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ZEPjInNRj2zSqMqALz97rnlv07qrIuxD36psOkWpYjE"]}]
2025-06-17 08:23:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[31]毫秒
2025-06-17 08:23:30 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-17 08:23:30 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 08:23:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[21]毫秒
2025-06-17 08:23:30 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 08:23:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[15]毫秒
2025-06-17 08:23:30 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 08:23:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[15]毫秒
2025-06-17 08:23:33 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:23:34 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-17 08:23:34 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[9]毫秒
2025-06-17 08:23:34 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-17 08:23:34 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[19]毫秒
2025-06-17 08:23:36 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/treeselect],无参数
2025-06-17 08:23:36 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/treeselect],耗时:[20]毫秒
2025-06-17 08:23:53 [boundedElastic-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:24:13 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:24:33 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:24:53 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:25:13 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:25:33 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:25:53 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:26:13 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:26:33 [boundedElastic-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:26:53 [boundedElastic-5] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:27:13 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:27:33 [boundedElastic-10] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:27:53 [boundedElastic-5] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:28:13 [boundedElastic-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:28:33 [boundedElastic-26] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:28:47 [boundedElastic-26] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-17 08:28:47 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-17 08:28:47 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[153]毫秒
2025-06-17 08:28:47 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[369]毫秒
2025-06-17 08:28:49 [boundedElastic-26] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-17 08:28:49 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[19]毫秒
2025-06-17 08:28:50 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-17 08:28:50 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[60]毫秒
2025-06-17 08:28:50 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-17 08:28:50 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[21]毫秒
2025-06-17 08:28:53 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:29:13 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:29:26 [boundedElastic-23] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":["master"]}]
2025-06-17 08:29:26 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[42]毫秒
2025-06-17 08:29:33 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:29:53 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:30:13 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:30:33 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:30:53 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:31:13 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:31:33 [boundedElastic-28] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:31:53 [boundedElastic-27] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:32:13 [boundedElastic-23] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:32:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:32:53 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:33:13 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:33:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:33:53 [boundedElastic-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:34:13 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:34:33 [boundedElastic-27] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:34:53 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:35:13 [boundedElastic-30] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:35:33 [boundedElastic-33] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:35:53 [boundedElastic-29] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:36:13 [boundedElastic-23] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:36:33 [boundedElastic-32] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:36:53 [boundedElastic-26] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:37:13 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:37:33 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:37:53 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:38:13 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:38:33 [boundedElastic-35] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:38:53 [boundedElastic-1] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:39:13 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:39:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:39:53 [boundedElastic-23] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:40:13 [boundedElastic-27] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:40:33 [boundedElastic-23] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:40:53 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:41:13 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:41:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:41:53 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:42:13 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:42:33 [boundedElastic-36] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:42:53 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:43:13 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:43:33 [boundedElastic-33] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:43:53 [boundedElastic-36] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:44:13 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:44:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:44:53 [boundedElastic-12] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:45:13 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:45:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:45:53 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:46:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:46:33 [boundedElastic-34] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:46:53 [boundedElastic-27] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:47:13 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:47:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:47:53 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:48:13 [boundedElastic-33] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:48:33 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:48:53 [boundedElastic-42] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:49:13 [boundedElastic-33] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:49:33 [boundedElastic-18] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:49:53 [boundedElastic-34] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:50:13 [boundedElastic-30] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:50:33 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:50:53 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:51:13 [boundedElastic-23] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:51:33 [boundedElastic-34] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:51:53 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:52:13 [boundedElastic-30] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:52:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:52:53 [boundedElastic-36] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:53:13 [boundedElastic-30] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:53:33 [boundedElastic-42] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:53:53 [boundedElastic-45] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:54:13 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:54:33 [boundedElastic-45] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:54:53 [boundedElastic-42] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:55:13 [boundedElastic-34] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:55:33 [boundedElastic-36] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:55:53 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:56:13 [boundedElastic-46] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:56:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:56:53 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:57:13 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:57:33 [boundedElastic-36] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:57:53 [boundedElastic-42] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:58:13 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:58:33 [boundedElastic-45] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:58:53 [boundedElastic-41] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:59:13 [boundedElastic-34] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:59:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 08:59:53 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:00:13 [boundedElastic-45] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:00:33 [boundedElastic-45] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:00:53 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:01:13 [boundedElastic-41] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:01:33 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:01:53 [boundedElastic-50] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:02:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:02:33 [boundedElastic-40] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:02:53 [boundedElastic-45] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:03:13 [boundedElastic-30] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:03:33 [boundedElastic-51] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:03:53 [boundedElastic-55] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:04:13 [boundedElastic-51] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:04:33 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:04:53 [boundedElastic-55] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:05:13 [boundedElastic-48] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:05:33 [boundedElastic-55] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:05:53 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:06:13 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:06:33 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:06:53 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:07:13 [boundedElastic-53] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:07:33 [boundedElastic-51] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:07:53 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:08:13 [boundedElastic-52] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:08:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:08:53 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:09:13 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:09:33 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:09:53 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:10:13 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:10:33 [boundedElastic-51] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:10:53 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:11:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:11:33 [boundedElastic-59] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:11:53 [boundedElastic-48] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:12:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:12:33 [boundedElastic-60] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:12:53 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:13:13 [boundedElastic-61] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:13:18 [boundedElastic-49] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJJZDlTaTMydllWTWdSdm53RjQ2eEFhZUtscTBsaDVteSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ZEPjInNRj2zSqMqALz97rnlv07qrIuxD36psOkWpYjE"]}]
2025-06-17 09:13:18 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[20]毫秒
2025-06-17 09:13:19 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJJZDlTaTMydllWTWdSdm53RjQ2eEFhZUtscTBsaDVteSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ZEPjInNRj2zSqMqALz97rnlv07qrIuxD36psOkWpYjE"]}]
2025-06-17 09:13:19 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[5]毫秒
2025-06-17 09:13:22 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJJZDlTaTMydllWTWdSdm53RjQ2eEFhZUtscTBsaDVteSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.ZEPjInNRj2zSqMqALz97rnlv07qrIuxD36psOkWpYjE"]}]
2025-06-17 09:13:22 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-17 09:13:27 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 09:13:27 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[7]毫秒
2025-06-17 09:13:27 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 09:13:27 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[9]毫秒
2025-06-17 09:13:28 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 09:13:28 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 09:13:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[18]毫秒
2025-06-17 09:13:28 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[33]毫秒
2025-06-17 09:13:33 [boundedElastic-62] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:13:35 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 09:13:36 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[164]毫秒
2025-06-17 09:13:36 [boundedElastic-36] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 09:13:36 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[24]毫秒
2025-06-17 09:13:36 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 09:13:36 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-06-17 09:13:36 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 09:13:36 [boundedElastic-36] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 09:13:36 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-17 09:13:36 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJzS1JqcDh4eGhtaDcwUDlha1BwQTBwSXJVQ05GTDdZTCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.k9gpYil4dJ0pzl35cgXYmZ755jEbvWO3BssxQmygC5s"]}]
2025-06-17 09:13:36 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-17 09:13:36 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[32]毫秒
2025-06-17 09:13:36 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 09:13:36 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-17 09:13:36 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 09:13:36 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[19]毫秒
2025-06-17 09:13:36 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 09:13:36 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[13]毫秒
2025-06-17 09:13:53 [boundedElastic-61] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:14:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:14:33 [boundedElastic-36] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:14:53 [boundedElastic-61] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:15:13 [boundedElastic-62] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:15:33 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:15:53 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:16:13 [boundedElastic-62] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:16:16 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 09:16:16 [boundedElastic-36] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 09:16:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-17 09:16:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[10]毫秒
2025-06-17 09:16:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 09:16:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-17 09:16:16 [boundedElastic-36] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 09:16:16 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[30]毫秒
2025-06-17 09:16:19 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931679466777804800],无参数
2025-06-17 09:16:19 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931679466777804800],耗时:[41]毫秒
2025-06-17 09:16:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 09:16:20 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-17 09:16:20 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 09:16:21 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-17 09:16:21 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 09:16:21 [boundedElastic-36] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 09:16:21 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 09:16:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 09:16:21 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 09:16:21 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-17 09:16:21 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[7]毫秒
2025-06-17 09:16:21 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-17 09:16:21 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-17 09:16:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-17 09:16:21 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 09:16:21 [boundedElastic-36] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJzS1JqcDh4eGhtaDcwUDlha1BwQTBwSXJVQ05GTDdZTCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.k9gpYil4dJ0pzl35cgXYmZ755jEbvWO3BssxQmygC5s"]}]
2025-06-17 09:16:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[26]毫秒
2025-06-17 09:16:21 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 09:16:21 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 09:16:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-17 09:16:21 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931682136745902081],无参数
2025-06-17 09:16:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931682136745902081],耗时:[20]毫秒
2025-06-17 09:16:23 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-17 09:16:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[13]毫秒
2025-06-17 09:16:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 09:16:23 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 09:16:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 09:16:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-17 09:16:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-17 09:16:23 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-17 09:16:23 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 09:16:23 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[53]毫秒
2025-06-17 09:16:33 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:16:53 [boundedElastic-66] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:17:04 [boundedElastic-66] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931679466777804800],无参数
2025-06-17 09:17:04 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931679466777804800],耗时:[25]毫秒
2025-06-17 09:17:05 [boundedElastic-66] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 09:17:05 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[29]毫秒
2025-06-17 09:17:05 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 09:17:05 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-17 09:17:05 [boundedElastic-66] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 09:17:05 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 09:17:05 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 09:17:05 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 09:17:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 09:17:05 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[7]毫秒
2025-06-17 09:17:05 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-17 09:17:05 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-17 09:17:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-17 09:17:05 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-06-17 09:17:05 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 09:17:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[22]毫秒
2025-06-17 09:17:05 [boundedElastic-66] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJzS1JqcDh4eGhtaDcwUDlha1BwQTBwSXJVQ05GTDdZTCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.k9gpYil4dJ0pzl35cgXYmZ755jEbvWO3BssxQmygC5s"]}]
2025-06-17 09:17:05 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-17 09:17:05 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 09:17:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 09:17:13 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:17:33 [boundedElastic-52] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:17:53 [boundedElastic-64] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:18:13 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:18:33 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:18:53 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:19:13 [boundedElastic-63] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:19:33 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:19:53 [boundedElastic-52] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:20:13 [boundedElastic-62] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:20:33 [boundedElastic-16] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:20:53 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:21:13 [boundedElastic-52] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:21:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:21:53 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:22:13 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:22:33 [boundedElastic-62] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:22:53 [boundedElastic-66] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:23:13 [boundedElastic-60] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:23:33 [boundedElastic-65] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:23:53 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:24:13 [boundedElastic-69] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:24:33 [boundedElastic-70] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:24:53 [boundedElastic-69] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:25:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:25:33 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:25:53 [boundedElastic-73] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:26:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:26:33 [boundedElastic-73] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:26:53 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:27:13 [boundedElastic-70] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:27:33 [boundedElastic-73] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:27:53 [boundedElastic-60] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:28:13 [boundedElastic-69] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:28:33 [boundedElastic-65] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:28:53 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:29:13 [boundedElastic-73] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:29:33 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:29:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:30:13 [boundedElastic-77] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:30:33 [boundedElastic-70] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:30:53 [boundedElastic-73] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:31:13 [boundedElastic-75] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:31:33 [boundedElastic-75] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:31:53 [boundedElastic-77] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:32:13 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:32:33 [boundedElastic-74] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:32:53 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:33:13 [boundedElastic-79] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:33:33 [boundedElastic-70] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:33:53 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:34:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:34:33 [boundedElastic-81] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:34:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:35:13 [boundedElastic-77] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:35:33 [boundedElastic-70] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:35:53 [boundedElastic-65] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:36:13 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:36:33 [boundedElastic-69] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:36:53 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:37:13 [boundedElastic-69] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:37:33 [boundedElastic-84] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:37:53 [boundedElastic-85] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:38:13 [boundedElastic-65] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:38:33 [boundedElastic-84] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:38:53 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:39:13 [boundedElastic-81] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:39:33 [boundedElastic-77] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:39:53 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:40:13 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:40:33 [boundedElastic-85] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:40:53 [boundedElastic-86] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:41:13 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:41:33 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:41:53 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:42:13 [boundedElastic-31] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:42:33 [boundedElastic-65] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:42:53 [boundedElastic-87] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:43:13 [boundedElastic-86] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:43:33 [boundedElastic-87] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:43:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:44:13 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:44:33 [boundedElastic-67] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:44:53 [boundedElastic-90] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:45:13 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:45:33 [boundedElastic-85] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:45:53 [boundedElastic-74] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:46:13 [boundedElastic-89] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:46:33 [boundedElastic-74] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:46:53 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:47:13 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:47:33 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:47:53 [boundedElastic-77] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:48:13 [boundedElastic-74] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:48:33 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:48:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:49:13 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:49:33 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:49:53 [boundedElastic-90] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:50:13 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:50:33 [boundedElastic-92] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:50:53 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:51:13 [boundedElastic-90] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:51:33 [boundedElastic-88] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:51:53 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:52:13 [boundedElastic-90] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:52:33 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:52:53 [boundedElastic-88] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:53:13 [boundedElastic-87] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:53:33 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:53:53 [boundedElastic-100] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:54:13 [boundedElastic-87] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:54:33 [boundedElastic-98] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:54:53 [boundedElastic-102] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:55:13 [boundedElastic-88] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:55:33 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:55:53 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:56:13 [boundedElastic-100] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:56:33 [boundedElastic-96] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:56:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:57:13 [boundedElastic-92] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:57:33 [boundedElastic-92] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:57:53 [boundedElastic-100] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:58:13 [boundedElastic-95] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:58:33 [boundedElastic-68] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:58:53 [boundedElastic-101] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:59:13 [boundedElastic-90] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:59:33 [boundedElastic-100] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 09:59:53 [boundedElastic-104] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:00:13 [boundedElastic-90] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:00:33 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:00:53 [boundedElastic-103] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:01:13 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:01:33 [boundedElastic-101] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:01:53 [boundedElastic-109] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:02:13 [boundedElastic-101] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:02:33 [boundedElastic-103] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:02:53 [boundedElastic-104] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:03:13 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:03:33 [boundedElastic-105] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:03:53 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:04:13 [boundedElastic-113] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:04:33 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:04:53 [boundedElastic-106] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:05:13 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:05:33 [boundedElastic-105] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:05:53 [boundedElastic-102] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:06:13 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:06:33 [boundedElastic-113] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:06:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:07:13 [boundedElastic-80] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:07:33 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:07:53 [boundedElastic-100] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:08:13 [boundedElastic-116] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:08:33 [boundedElastic-116] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:08:53 [boundedElastic-103] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:09:13 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:09:33 [boundedElastic-100] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:09:53 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:10:13 [boundedElastic-118] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:10:33 [boundedElastic-91] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:10:53 [boundedElastic-114] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:11:13 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:11:33 [boundedElastic-118] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:11:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:12:13 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:12:33 [boundedElastic-114] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:12:53 [boundedElastic-122] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:13:13 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:13:33 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:13:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:14:13 [boundedElastic-120] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:14:33 [boundedElastic-122] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:14:53 [boundedElastic-123] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:15:13 [boundedElastic-120] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:15:33 [boundedElastic-103] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:15:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:16:13 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:16:33 [boundedElastic-119] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:16:53 [boundedElastic-103] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:17:13 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:17:33 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:17:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:18:13 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:18:33 [boundedElastic-118] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:18:53 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:19:13 [boundedElastic-125] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:19:33 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:19:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:20:13 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:20:33 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:20:53 [boundedElastic-107] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:21:13 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:21:33 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:21:53 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:22:13 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:22:33 [boundedElastic-127] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:22:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:23:13 [boundedElastic-103] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:23:33 [boundedElastic-120] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:23:53 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:24:13 [boundedElastic-137] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:24:33 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:24:53 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:25:13 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:25:33 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:25:53 [boundedElastic-76] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:26:13 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:26:33 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:26:53 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:27:13 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:27:33 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:27:53 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:13 [boundedElastic-118] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:33 [boundedElastic-138] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:28:35 [boundedElastic-121] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 10:28:35 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[9]毫秒
2025-06-17 10:28:35 [boundedElastic-121] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 10:28:35 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[9]毫秒
2025-06-17 10:28:35 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 10:28:35 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 10:28:35 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[17]毫秒
2025-06-17 10:28:35 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-17 10:28:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 10:28:44 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[136]毫秒
2025-06-17 10:28:44 [boundedElastic-138] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 10:28:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[18]毫秒
2025-06-17 10:28:44 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 10:28:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[16]毫秒
2025-06-17 10:28:44 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 10:28:44 [boundedElastic-138] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 10:28:44 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMR1FiMWNaN2VvamxtWmlQSm5XWmJPOHNOelF5TkVYSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.xnRhhJB1BJQ_lAdWKQLQsBR70Vxh91mo48NSLeG0re8"]}]
2025-06-17 10:28:44 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-17 10:28:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 10:28:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[27]毫秒
2025-06-17 10:28:44 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 10:28:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[9]毫秒
2025-06-17 10:28:44 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 10:28:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[14]毫秒
2025-06-17 10:28:44 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 10:28:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-17 10:28:53 [boundedElastic-130] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:29:13 [boundedElastic-141] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:29:33 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:29:53 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:30:13 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:30:33 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:30:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:31:13 [boundedElastic-142] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:31:33 [boundedElastic-143] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:31:35 [boundedElastic-126] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 10:31:35 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[25]毫秒
2025-06-17 10:31:36 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 10:31:36 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-17 10:31:36 [boundedElastic-126] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 10:31:36 [boundedElastic-126] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 10:31:36 [boundedElastic-126] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMR1FiMWNaN2VvamxtWmlQSm5XWmJPOHNOelF5TkVYSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.xnRhhJB1BJQ_lAdWKQLQsBR70Vxh91mo48NSLeG0re8"]}]
2025-06-17 10:31:36 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[20]毫秒
2025-06-17 10:31:36 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-17 10:31:36 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[20]毫秒
2025-06-17 10:31:36 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 10:31:36 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-17 10:31:53 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:32:13 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:32:33 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:32:53 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:33:13 [boundedElastic-143] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:33:33 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:33:53 [boundedElastic-144] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:34:13 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:34:33 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:34:53 [boundedElastic-141] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:35:13 [boundedElastic-143] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:35:33 [boundedElastic-142] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:35:53 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:36:13 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:36:33 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:36:53 [boundedElastic-144] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:37:13 [boundedElastic-117] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:37:33 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:37:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:38:13 [boundedElastic-142] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:38:33 [boundedElastic-146] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:38:53 [boundedElastic-130] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:39:13 [boundedElastic-147] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:39:33 [boundedElastic-144] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:39:53 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:40:13 [boundedElastic-144] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:40:33 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:40:53 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:41:13 [boundedElastic-130] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:41:33 [boundedElastic-147] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:41:53 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:42:13 [boundedElastic-132] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:42:33 [boundedElastic-142] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:42:53 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:43:13 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:43:33 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:43:53 [boundedElastic-141] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:44:13 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:44:33 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:44:53 [boundedElastic-153] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:45:13 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:45:33 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:45:53 [boundedElastic-144] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:46:13 [boundedElastic-154] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:46:33 [boundedElastic-152] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:46:53 [boundedElastic-129] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:47:13 [boundedElastic-155] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:47:33 [boundedElastic-121] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:47:53 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:48:13 [boundedElastic-155] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:48:33 [boundedElastic-155] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:48:53 [boundedElastic-144] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:49:13 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:49:33 [boundedElastic-157] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:49:53 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:50:13 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:50:33 [boundedElastic-155] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:50:53 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:51:13 [boundedElastic-141] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:51:33 [boundedElastic-155] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:51:53 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:52:13 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:52:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:52:53 [boundedElastic-164] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:53:13 [boundedElastic-160] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:53:33 [boundedElastic-152] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:53:53 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:54:13 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:54:33 [boundedElastic-136] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:54:53 [boundedElastic-152] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:55:13 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:55:33 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:55:53 [boundedElastic-164] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:56:13 [boundedElastic-142] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:56:33 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:56:53 [boundedElastic-160] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:57:13 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:57:33 [boundedElastic-160] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:57:53 [boundedElastic-158] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:58:13 [boundedElastic-164] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:58:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:58:53 [boundedElastic-166] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:59:13 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:59:33 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 10:59:53 [boundedElastic-167] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:00:05 [boundedElastic-167] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMR1FiMWNaN2VvamxtWmlQSm5XWmJPOHNOelF5TkVYSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.xnRhhJB1BJQ_lAdWKQLQsBR70Vxh91mo48NSLeG0re8"]}]
2025-06-17 11:00:05 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 11:00:13 [boundedElastic-160] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:00:19 [boundedElastic-164] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 11:00:19 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[24]毫秒
2025-06-17 11:00:19 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 11:00:19 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[17]毫秒
2025-06-17 11:00:20 [boundedElastic-164] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 11:00:20 [boundedElastic-164] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 11:00:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMR1FiMWNaN2VvamxtWmlQSm5XWmJPOHNOelF5TkVYSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.xnRhhJB1BJQ_lAdWKQLQsBR70Vxh91mo48NSLeG0re8"]}]
2025-06-17 11:00:20 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-17 11:00:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-17 11:00:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[28]毫秒
2025-06-17 11:00:20 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 11:00:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 11:00:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 11:00:23 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 11:00:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-17 11:00:23 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[7]毫秒
2025-06-17 11:00:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 11:00:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-17 11:00:23 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 11:00:23 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[26]毫秒
2025-06-17 11:00:33 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:00:53 [boundedElastic-158] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:01:13 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:01:33 [boundedElastic-156] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:01:53 [boundedElastic-171] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:02:13 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:02:33 [boundedElastic-166] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:02:53 [boundedElastic-158] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:03:13 [boundedElastic-167] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:03:33 [boundedElastic-166] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:03:53 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:04:13 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:04:33 [boundedElastic-173] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:04:53 [boundedElastic-164] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:05:13 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:05:33 [boundedElastic-175] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:05:53 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:06:13 [boundedElastic-158] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:06:33 [boundedElastic-158] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:06:53 [boundedElastic-171] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:07:13 [boundedElastic-158] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:07:33 [boundedElastic-142] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:07:53 [boundedElastic-171] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:08:13 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:08:33 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:08:53 [boundedElastic-173] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:09:13 [boundedElastic-164] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:09:33 [boundedElastic-179] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:09:53 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:10:13 [boundedElastic-175] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:10:33 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:10:53 [boundedElastic-171] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:11:13 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:11:33 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:11:53 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:12:13 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:12:33 [boundedElastic-175] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:12:53 [boundedElastic-175] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:13:13 [boundedElastic-126] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:13:33 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:13:53 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:14:13 [boundedElastic-167] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:14:33 [boundedElastic-175] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:14:53 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:15:13 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:15:33 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:15:53 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:16:13 [boundedElastic-184] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:16:33 [boundedElastic-167] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:16:53 [boundedElastic-183] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:17:13 [boundedElastic-186] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:17:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:17:53 [boundedElastic-186] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:18:13 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:18:33 [boundedElastic-186] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:18:53 [boundedElastic-188] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:19:13 [boundedElastic-187] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:19:33 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:19:53 [boundedElastic-188] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:20:13 [boundedElastic-164] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:20:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:20:53 [boundedElastic-187] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:21:13 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:21:33 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:21:53 [boundedElastic-189] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:22:13 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:22:33 [boundedElastic-181] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:22:53 [boundedElastic-186] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:23:13 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:23:33 [boundedElastic-177] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:23:53 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:24:13 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:24:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:24:53 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:25:13 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:25:33 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:25:53 [boundedElastic-181] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:26:13 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:26:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:26:53 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:27:13 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:27:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:27:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:28:13 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:28:33 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:28:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:29:13 [boundedElastic-163] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:29:33 [boundedElastic-189] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:29:53 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:30:13 [boundedElastic-177] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:30:33 [boundedElastic-189] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:30:53 [boundedElastic-191] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:31:13 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:31:33 [boundedElastic-186] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:31:53 [boundedElastic-200] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:32:13 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:32:33 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:32:53 [boundedElastic-139] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:33:13 [boundedElastic-200] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:33:33 [boundedElastic-180] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:33:53 [boundedElastic-186] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:34:13 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:34:33 [boundedElastic-200] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:34:53 [boundedElastic-199] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:35:13 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:35:33 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:35:53 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:36:13 [boundedElastic-199] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:36:33 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:36:53 [boundedElastic-162] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:37:13 [boundedElastic-206] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:37:33 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:37:53 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:38:13 [boundedElastic-209] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:38:33 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:38:53 [boundedElastic-200] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:39:13 [boundedElastic-189] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:39:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:39:53 [boundedElastic-204] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:40:13 [boundedElastic-210] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:40:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:40:53 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:41:13 [boundedElastic-210] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:41:33 [boundedElastic-206] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:41:53 [boundedElastic-210] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:42:13 [boundedElastic-181] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:42:33 [boundedElastic-205] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:42:53 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:43:13 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:43:33 [boundedElastic-206] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:43:53 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:44:13 [boundedElastic-178] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:44:33 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:44:53 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:45:13 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:45:33 [boundedElastic-208] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:45:53 [boundedElastic-211] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:46:13 [boundedElastic-206] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:46:33 [boundedElastic-209] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:46:53 [boundedElastic-202] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:47:13 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:47:33 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:47:53 [boundedElastic-215] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:48:13 [boundedElastic-182] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:48:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:48:53 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:49:13 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:49:33 [boundedElastic-208] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:49:53 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:50:13 [boundedElastic-213] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:50:33 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:50:53 [boundedElastic-206] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:51:13 [boundedElastic-215] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:51:33 [boundedElastic-213] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:51:53 [boundedElastic-214] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:52:13 [boundedElastic-215] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:52:33 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:52:53 [boundedElastic-215] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:53:13 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:53:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:53:53 [boundedElastic-208] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:54:13 [boundedElastic-208] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:54:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:54:53 [boundedElastic-214] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:55:13 [boundedElastic-222] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:55:33 [boundedElastic-214] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:55:53 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:56:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:56:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:56:53 [boundedElastic-219] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:57:13 [boundedElastic-215] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:57:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:57:53 [boundedElastic-211] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:58:13 [boundedElastic-214] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:58:33 [boundedElastic-219] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:58:53 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:59:13 [boundedElastic-219] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:59:33 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 11:59:53 [boundedElastic-215] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:00:13 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:00:33 [boundedElastic-195] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:00:53 [boundedElastic-220] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:01:13 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:01:33 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:01:53 [boundedElastic-222] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:02:13 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:02:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:02:53 [boundedElastic-224] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:03:13 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:03:33 [boundedElastic-225] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:03:53 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:04:13 [boundedElastic-216] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:04:33 [boundedElastic-225] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:04:53 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:05:13 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:05:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:05:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:06:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:06:33 [boundedElastic-225] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:06:53 [boundedElastic-211] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:07:13 [boundedElastic-219] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:07:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:07:53 [boundedElastic-228] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:08:13 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:08:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:08:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:09:13 [boundedElastic-224] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:09:33 [boundedElastic-230] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:09:53 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:10:13 [boundedElastic-223] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:10:33 [boundedElastic-223] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:10:53 [boundedElastic-230] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:11:13 [boundedElastic-223] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:11:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:11:53 [boundedElastic-220] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:12:13 [boundedElastic-226] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:12:33 [boundedElastic-196] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:12:53 [boundedElastic-224] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:13:13 [boundedElastic-234] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:13:33 [boundedElastic-226] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:13:53 [boundedElastic-224] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:14:13 [boundedElastic-234] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:14:33 [boundedElastic-226] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:14:53 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:15:13 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:15:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:15:53 [boundedElastic-211] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:16:13 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:16:33 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:16:53 [boundedElastic-232] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:17:13 [boundedElastic-238] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:17:33 [boundedElastic-220] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:17:53 [boundedElastic-238] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:18:13 [boundedElastic-226] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:18:33 [boundedElastic-226] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:18:53 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:19:13 [boundedElastic-226] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:19:33 [boundedElastic-238] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:19:53 [boundedElastic-240] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:20:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:20:33 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:20:53 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:21:13 [boundedElastic-233] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:21:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:21:53 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:22:13 [boundedElastic-240] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:22:33 [boundedElastic-220] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:22:53 [boundedElastic-239] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:23:13 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:23:33 [boundedElastic-244] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:23:53 [boundedElastic-241] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:24:13 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:24:33 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:24:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:25:13 [boundedElastic-247] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:25:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:25:53 [boundedElastic-239] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:26:13 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:26:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:26:53 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:27:13 [boundedElastic-233] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:27:33 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:27:53 [boundedElastic-220] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:28:13 [boundedElastic-240] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:28:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:28:53 [boundedElastic-244] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:29:13 [boundedElastic-217] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:29:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:29:53 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:30:13 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:30:33 [boundedElastic-239] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:30:53 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:31:13 [boundedElastic-247] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:31:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:31:53 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:32:13 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:32:33 [boundedElastic-244] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:32:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:33:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:33:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:33:53 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:34:13 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:34:33 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:34:53 [boundedElastic-233] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:35:13 [boundedElastic-211] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:35:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:35:53 [boundedElastic-244] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:36:13 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:36:33 [boundedElastic-197] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:36:53 [boundedElastic-220] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:37:13 [boundedElastic-233] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:37:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:37:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:38:13 [boundedElastic-247] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:38:33 [boundedElastic-247] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:38:53 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:39:13 [boundedElastic-227] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:39:33 [boundedElastic-250] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:39:53 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:40:13 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:40:33 [boundedElastic-233] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:40:53 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:41:13 [boundedElastic-235] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:41:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:41:53 [boundedElastic-251] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:42:13 [boundedElastic-257] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:42:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:42:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:43:13 [boundedElastic-259] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:43:33 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:43:53 [boundedElastic-250] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:44:13 [boundedElastic-240] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:44:33 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:44:53 [boundedElastic-240] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:45:13 [boundedElastic-257] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:45:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:45:53 [boundedElastic-259] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:46:13 [boundedElastic-251] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:46:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:46:53 [boundedElastic-250] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:47:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:47:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:47:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:48:13 [boundedElastic-251] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:48:33 [boundedElastic-247] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:48:53 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:49:13 [boundedElastic-250] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:49:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:49:53 [boundedElastic-247] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:50:13 [boundedElastic-262] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:50:33 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:50:53 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:51:13 [boundedElastic-261] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:51:33 [boundedElastic-262] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:51:53 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:52:13 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:52:33 [boundedElastic-261] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:52:53 [boundedElastic-250] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:53:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:53:33 [boundedElastic-255] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:53:53 [boundedElastic-263] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:54:13 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:54:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:54:53 [boundedElastic-263] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:55:13 [boundedElastic-261] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:55:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:55:53 [boundedElastic-265] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:56:13 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:56:33 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:56:53 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:57:13 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:57:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:57:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:58:13 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:58:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:58:53 [boundedElastic-266] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:59:13 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:59:33 [boundedElastic-218] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 12:59:53 [boundedElastic-266] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:00:13 [boundedElastic-263] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:00:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:00:53 [boundedElastic-267] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:01:13 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:01:33 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:01:53 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:02:13 [boundedElastic-261] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:02:33 [boundedElastic-248] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:02:53 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:03:13 [boundedElastic-266] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:03:33 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:03:53 [boundedElastic-266] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:04:13 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:04:33 [boundedElastic-267] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:04:53 [boundedElastic-266] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:05:13 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:05:33 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:05:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:06:13 [boundedElastic-255] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:06:33 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:06:53 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:07:13 [boundedElastic-256] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:07:33 [boundedElastic-257] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:07:53 [boundedElastic-272] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:08:13 [boundedElastic-265] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:08:33 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:08:53 [boundedElastic-275] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:09:13 [boundedElastic-273] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:09:33 [boundedElastic-272] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:09:53 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:10:13 [boundedElastic-267] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:10:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:10:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:11:13 [boundedElastic-255] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:11:33 [boundedElastic-268] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:11:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:12:13 [boundedElastic-268] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:12:33 [boundedElastic-255] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:12:53 [boundedElastic-268] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:13:13 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:13:33 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:13:53 [boundedElastic-278] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:14:13 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:14:33 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:14:53 [boundedElastic-254] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:15:13 [boundedElastic-275] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:15:33 [boundedElastic-267] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:15:53 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:16:13 [boundedElastic-268] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:16:33 [boundedElastic-267] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:16:53 [boundedElastic-282] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:17:13 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:17:33 [boundedElastic-277] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:17:53 [boundedElastic-267] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:18:13 [boundedElastic-282] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:18:33 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:18:53 [boundedElastic-273] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:19:13 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:19:33 [boundedElastic-273] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:19:53 [boundedElastic-275] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:20:13 [boundedElastic-282] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:20:33 [boundedElastic-287] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:20:53 [boundedElastic-273] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:21:13 [boundedElastic-287] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:21:33 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:21:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:22:13 [boundedElastic-282] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:22:33 [boundedElastic-281] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:22:53 [boundedElastic-275] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:23:13 [boundedElastic-283] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:23:33 [boundedElastic-288] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:23:53 [boundedElastic-275] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:24:13 [boundedElastic-283] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:24:33 [boundedElastic-275] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:24:53 [boundedElastic-281] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:25:13 [boundedElastic-286] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:25:33 [boundedElastic-272] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:25:53 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:26:13 [boundedElastic-286] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:26:33 [boundedElastic-281] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:26:53 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:27:13 [boundedElastic-281] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:27:33 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:27:53 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:28:13 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:28:33 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:28:53 [boundedElastic-298] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:29:13 [boundedElastic-291] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:29:33 [boundedElastic-299] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:29:53 [boundedElastic-291] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:30:13 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:30:33 [boundedElastic-283] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:30:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:31:13 [boundedElastic-295] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:31:33 [boundedElastic-294] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:31:53 [boundedElastic-298] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:32:13 [boundedElastic-290] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:32:33 [boundedElastic-286] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:32:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:33:13 [boundedElastic-279] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:33:33 [boundedElastic-300] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:33:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:34:13 [boundedElastic-305] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:34:33 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:34:53 [boundedElastic-286] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:35:13 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:35:33 [boundedElastic-298] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:35:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:36:13 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:36:33 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:36:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:37:13 [boundedElastic-270] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:37:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:37:53 [boundedElastic-307] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:38:13 [boundedElastic-295] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:38:33 [boundedElastic-290] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:38:53 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:39:13 [boundedElastic-306] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:39:33 [boundedElastic-305] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:39:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:40:13 [boundedElastic-306] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:40:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:40:53 [boundedElastic-306] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:41:13 [boundedElastic-309] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:41:33 [boundedElastic-310] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:41:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:42:13 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:42:33 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:42:53 [boundedElastic-260] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:43:13 [boundedElastic-294] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:43:33 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:43:53 [boundedElastic-314] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:44:13 [boundedElastic-312] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:44:33 [boundedElastic-293] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:44:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:45:13 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:45:33 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:45:53 [boundedElastic-293] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:46:13 [boundedElastic-313] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:46:33 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:46:53 [boundedElastic-306] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:47:13 [boundedElastic-293] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:47:33 [boundedElastic-290] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:47:53 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:48:13 [boundedElastic-314] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:48:33 [boundedElastic-312] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:48:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:49:13 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:49:33 [boundedElastic-294] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:49:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:50:13 [boundedElastic-317] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:50:33 [boundedElastic-307] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:50:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:51:13 [boundedElastic-320] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:51:33 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:51:53 [boundedElastic-307] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:52:13 [boundedElastic-314] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:52:33 [boundedElastic-293] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:52:53 [boundedElastic-314] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:53:13 [boundedElastic-320] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:53:33 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:53:53 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:54:13 [boundedElastic-310] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:54:33 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:54:53 [boundedElastic-320] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:55:13 [boundedElastic-293] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:55:33 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:55:53 [boundedElastic-314] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:56:13 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:56:33 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:56:53 [boundedElastic-310] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:57:13 [boundedElastic-307] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:57:33 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:57:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:58:13 [boundedElastic-314] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:58:33 [boundedElastic-306] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:58:53 [boundedElastic-303] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:59:13 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:59:33 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 13:59:53 [boundedElastic-310] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:00:13 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:00:33 [boundedElastic-310] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:00:53 [boundedElastic-317] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:01:13 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:01:33 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:01:53 [boundedElastic-285] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:02:13 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:02:33 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:02:53 [boundedElastic-307] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:03:13 [boundedElastic-327] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:03:33 [boundedElastic-329] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:03:53 [boundedElastic-243] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:04:13 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:04:33 [boundedElastic-332] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:04:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:05:13 [boundedElastic-326] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:05:33 [boundedElastic-333] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:05:53 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:06:13 [boundedElastic-326] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:06:33 [boundedElastic-333] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:06:53 [boundedElastic-326] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:07:13 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:07:33 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:07:53 [boundedElastic-327] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:08:13 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:08:33 [boundedElastic-310] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:08:53 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:09:13 [boundedElastic-339] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:09:33 [boundedElastic-337] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:09:53 [boundedElastic-326] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:10:13 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:10:33 [boundedElastic-336] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:10:53 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:11:13 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:11:33 [boundedElastic-329] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:11:53 [boundedElastic-341] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:12:13 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:12:33 [boundedElastic-340] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:12:53 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:13:13 [boundedElastic-340] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:13:33 [boundedElastic-343] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:13:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:14:13 [boundedElastic-339] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:14:33 [boundedElastic-344] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:14:53 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:15:13 [boundedElastic-341] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:15:33 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:15:53 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:16:13 [boundedElastic-341] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:16:33 [boundedElastic-328] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:16:53 [boundedElastic-325] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:17:13 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:17:33 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:17:53 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:18:13 [boundedElastic-337] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:18:33 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:18:53 [boundedElastic-335] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:19:13 [boundedElastic-336] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:19:33 [boundedElastic-339] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:19:53 [boundedElastic-307] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:20:13 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:20:33 [boundedElastic-344] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:20:53 [boundedElastic-350] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:21:13 [boundedElastic-301] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:21:33 [boundedElastic-350] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:21:53 [boundedElastic-346] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:22:13 [boundedElastic-347] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:22:33 [boundedElastic-347] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:22:53 [boundedElastic-344] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:23:13 [boundedElastic-336] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:23:33 [boundedElastic-351] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:23:53 [boundedElastic-350] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:24:13 [boundedElastic-346] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:24:33 [boundedElastic-350] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:24:53 [boundedElastic-340] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:25:13 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:25:33 [boundedElastic-348] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:25:53 [boundedElastic-340] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:26:13 [boundedElastic-345] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:26:33 [boundedElastic-344] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:26:53 [boundedElastic-351] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:27:13 [boundedElastic-340] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:27:33 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:27:53 [boundedElastic-350] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:28:13 [boundedElastic-355] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:28:33 [boundedElastic-347] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:28:53 [boundedElastic-359] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:29:13 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:29:33 [boundedElastic-351] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:29:53 [boundedElastic-354] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:30:13 [boundedElastic-345] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:30:33 [boundedElastic-350] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:30:53 [boundedElastic-355] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:31:13 [boundedElastic-345] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:31:33 [boundedElastic-355] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:31:53 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:32:13 [boundedElastic-348] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:32:33 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:32:53 [boundedElastic-359] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:33:13 [boundedElastic-362] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:33:33 [boundedElastic-359] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:33:53 [boundedElastic-344] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:34:13 [boundedElastic-361] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:34:33 [boundedElastic-336] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:34:53 [boundedElastic-355] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:35:13 [boundedElastic-345] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:35:33 [boundedElastic-364] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:35:53 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:36:13 [boundedElastic-362] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:36:33 [boundedElastic-366] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:36:53 [boundedElastic-321] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:37:13 [boundedElastic-336] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:37:33 [boundedElastic-355] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:37:53 [boundedElastic-351] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:38:13 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:38:33 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:38:53 [boundedElastic-336] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:39:13 [boundedElastic-345] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:39:33 [boundedElastic-362] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:39:53 [boundedElastic-368] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:40:13 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:40:33 [boundedElastic-365] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:40:53 [boundedElastic-374] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:41:13 [boundedElastic-368] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:41:33 [boundedElastic-373] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:41:53 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:42:13 [boundedElastic-373] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:42:33 [boundedElastic-373] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:42:53 [boundedElastic-365] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:43:13 [boundedElastic-351] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:43:33 [boundedElastic-362] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:43:53 [boundedElastic-365] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:44:13 [boundedElastic-370] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:44:33 [boundedElastic-365] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:44:53 [boundedElastic-370] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:45:13 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:45:33 [boundedElastic-353] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:45:53 [boundedElastic-370] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:46:13 [boundedElastic-374] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:46:33 [boundedElastic-374] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:46:53 [boundedElastic-378] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:47:13 [boundedElastic-383] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:47:33 [boundedElastic-379] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:47:53 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:48:13 [boundedElastic-382] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:48:33 [boundedElastic-385] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:48:53 [boundedElastic-372] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:49:13 [boundedElastic-383] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:49:33 [boundedElastic-362] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:49:53 [boundedElastic-370] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:50:13 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:50:33 [boundedElastic-378] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:50:53 [boundedElastic-385] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:51:13 [boundedElastic-372] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:51:33 [boundedElastic-385] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:51:53 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:52:13 [boundedElastic-378] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:52:33 [boundedElastic-386] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:52:53 [boundedElastic-379] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:53:13 [boundedElastic-391] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:53:33 [boundedElastic-388] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:53:53 [boundedElastic-382] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:54:13 [boundedElastic-389] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:54:33 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:54:53 [boundedElastic-384] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:55:13 [boundedElastic-392] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:55:33 [boundedElastic-392] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:55:53 [boundedElastic-389] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:56:13 [boundedElastic-392] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:56:33 [boundedElastic-382] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:56:53 [boundedElastic-362] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:57:13 [boundedElastic-383] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:57:33 [boundedElastic-395] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:57:53 [boundedElastic-386] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:58:13 [boundedElastic-388] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:58:33 [boundedElastic-396] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:58:53 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:59:13 [boundedElastic-388] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:59:33 [boundedElastic-396] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 14:59:53 [boundedElastic-398] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:00:13 [boundedElastic-396] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:00:33 [boundedElastic-397] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:00:53 [boundedElastic-394] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:13 [boundedElastic-398] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:16 [boundedElastic-400] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 15:01:16 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[6]毫秒
2025-06-17 15:01:16 [boundedElastic-400] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 15:01:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[7]毫秒
2025-06-17 15:01:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 15:01:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:01:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-17 15:01:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-17 15:01:25 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 15:01:25 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[147]毫秒
2025-06-17 15:01:25 [boundedElastic-398] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:01:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-17 15:01:25 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:01:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[16]毫秒
2025-06-17 15:01:25 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:01:25 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:01:25 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:01:25 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:01:25 [boundedElastic-398] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[7]毫秒
2025-06-17 15:01:25 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-17 15:01:25 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[12]毫秒
2025-06-17 15:01:25 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-17 15:01:25 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[21]毫秒
2025-06-17 15:01:25 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:25 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-17 15:01:25 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:01:25 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[26]毫秒
2025-06-17 15:01:25 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0QTVkemg1OTl5cHZVVzE0NGFUSGZUQzJMN1lCMEtUciIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.F2FIJpTSeU7N1lzzVu6NQNZc-ysYQWxXpHjXtuaDTBE"]}]
2025-06-17 15:01:25 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:01:25 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:25 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:01:25 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:25 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:01:28 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":[""],"isAsc":[""],"deptId":["1934430959876390913"]}]
2025-06-17 15:01:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[18]毫秒
2025-06-17 15:01:29 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":[""],"isAsc":[""],"deptId":["1934431002532462594"]}]
2025-06-17 15:01:29 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-17 15:01:33 [boundedElastic-399] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:38 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 15:01:38 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-17 15:01:38 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 15:01:38 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[30]毫秒
2025-06-17 15:01:38 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 15:01:38 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:01:38 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[9]毫秒
2025-06-17 15:01:38 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-06-17 15:01:44 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 15:01:44 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[148]毫秒
2025-06-17 15:01:44 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:01:44 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[22]毫秒
2025-06-17 15:01:44 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:01:44 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-17 15:01:44 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:44 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-17 15:01:44 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:44 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:01:44 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-17 15:01:44 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:01:44 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:44 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[13]毫秒
2025-06-17 15:01:44 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:44 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:01:48 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544605054894000],无参数
2025-06-17 15:01:48 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544605054894000],耗时:[17]毫秒
2025-06-17 15:01:49 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:01:49 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-17 15:01:50 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:01:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-17 15:01:50 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:50 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-17 15:01:50 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:01:50 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:01:50 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:50 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:01:50 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:01:50 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[16]毫秒
2025-06-17 15:01:52 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544605054894000],无参数
2025-06-17 15:01:52 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544605054894000],耗时:[16]毫秒
2025-06-17 15:01:53 [boundedElastic-398] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:01:53 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:01:53 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-17 15:01:53 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:01:53 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[14]毫秒
2025-06-17 15:01:54 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:54 [boundedElastic-389] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:01:54 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 15:01:54 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[16]毫秒
2025-06-17 15:01:54 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:54 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:01:54 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:01:54 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[17]毫秒
2025-06-17 15:01:57 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934545280719519700],无参数
2025-06-17 15:01:57 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934545280719519700],耗时:[17]毫秒
2025-06-17 15:01:58 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:01:58 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-17 15:01:58 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:01:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[8]毫秒
2025-06-17 15:01:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:01:58 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:01:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[13]毫秒
2025-06-17 15:01:58 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:01:58 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:01:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-17 15:01:58 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:01:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[19]毫秒
2025-06-17 15:02:02 [boundedElastic-389] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:02:02 [boundedElastic-378] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:02:02 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-17 15:02:02 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-17 15:02:02 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:02:02 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[7]毫秒
2025-06-17 15:02:02 [boundedElastic-389] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:02:02 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[41]毫秒
2025-06-17 15:02:04 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544736567296000],无参数
2025-06-17 15:02:04 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544736567296000],耗时:[23]毫秒
2025-06-17 15:02:05 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:02:05 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[20]毫秒
2025-06-17 15:02:05 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:02:05 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[8]毫秒
2025-06-17 15:02:06 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:02:06 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:02:06 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:02:06 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:02:06 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-17 15:02:06 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-17 15:02:06 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-17 15:02:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-17 15:02:06 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:02:06 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:02:06 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 15:02:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[39]毫秒
2025-06-17 15:02:06 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:02:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[8]毫秒
2025-06-17 15:02:06 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:02:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[11]毫秒
2025-06-17 15:02:13 [boundedElastic-385] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:02:18 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934545357651443700],无参数
2025-06-17 15:02:18 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934545357651443700],耗时:[19]毫秒
2025-06-17 15:02:19 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:02:19 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-17 15:02:19 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:02:19 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-17 15:02:19 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:02:19 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:02:19 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:02:19 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:02:19 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-17 15:02:19 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-17 15:02:19 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-17 15:02:19 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[10]毫秒
2025-06-17 15:02:19 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:02:20 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:02:20 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:02:20 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[38]毫秒
2025-06-17 15:02:20 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:02:20 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[7]毫秒
2025-06-17 15:02:20 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:02:20 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[14]毫秒
2025-06-17 15:02:33 [boundedElastic-403] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:02:53 [boundedElastic-399] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:03:13 [boundedElastic-401] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:03:33 [boundedElastic-400] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:03:53 [boundedElastic-402] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:04:13 [boundedElastic-401] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:04:33 [boundedElastic-402] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:04:53 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:13 [boundedElastic-393] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:33 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:43 [boundedElastic-385] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:05:43 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[22]毫秒
2025-06-17 15:05:43 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:05:43 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-17 15:05:43 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:05:43 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:05:43 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:05:43 [boundedElastic-376] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:05:43 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-17 15:05:43 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-17 15:05:43 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-17 15:05:43 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[16]毫秒
2025-06-17 15:05:43 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:05:43 [boundedElastic-376] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:05:43 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 15:05:43 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[47]毫秒
2025-06-17 15:05:43 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:05:43 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[8]毫秒
2025-06-17 15:05:45 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544605054894000],无参数
2025-06-17 15:05:45 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544605054894000],耗时:[16]毫秒
2025-06-17 15:05:46 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:05:46 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[16]毫秒
2025-06-17 15:05:46 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:05:46 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[9]毫秒
2025-06-17 15:05:47 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:05:47 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:05:47 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:05:47 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:05:47 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[4]毫秒
2025-06-17 15:05:47 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[4]毫秒
2025-06-17 15:05:47 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-17 15:05:47 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-17 15:05:47 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:05:47 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:05:47 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:05:47 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[44]毫秒
2025-06-17 15:05:47 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:05:47 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-17 15:05:47 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:05:47 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[15]毫秒
2025-06-17 15:05:49 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934545280719519700],无参数
2025-06-17 15:05:49 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934545280719519700],耗时:[19]毫秒
2025-06-17 15:05:50 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:05:50 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[22]毫秒
2025-06-17 15:05:50 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:05:50 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-17 15:05:50 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:05:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:05:50 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:05:50 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:05:50 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-17 15:05:50 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-17 15:05:50 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-17 15:05:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[12]毫秒
2025-06-17 15:05:50 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:05:50 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:05:50 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-17 15:05:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[47]毫秒
2025-06-17 15:05:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:05:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[8]毫秒
2025-06-17 15:05:51 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:05:51 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[15]毫秒
2025-06-17 15:05:53 [boundedElastic-403] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:05:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934545727056380000],无参数
2025-06-17 15:05:58 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934545727056380000],耗时:[17]毫秒
2025-06-17 15:05:59 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:05:59 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-17 15:05:59 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:05:59 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-17 15:06:00 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:06:00 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:06:00 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:06:00 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:06:00 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[4]毫秒
2025-06-17 15:06:00 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[4]毫秒
2025-06-17 15:06:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[9]毫秒
2025-06-17 15:06:00 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-17 15:06:00 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:06:00 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:06:00 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:06:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[45]毫秒
2025-06-17 15:06:00 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:06:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[8]毫秒
2025-06-17 15:06:00 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:06:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[14]毫秒
2025-06-17 15:06:06 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934545808203579400],无参数
2025-06-17 15:06:06 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934545808203579400],耗时:[21]毫秒
2025-06-17 15:06:07 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:06:07 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[23]毫秒
2025-06-17 15:06:07 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:06:07 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-17 15:06:08 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:06:08 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:06:08 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:06:08 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:06:08 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-17 15:06:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-17 15:06:08 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-17 15:06:08 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-17 15:06:08 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:06:08 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:06:08 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-17 15:06:08 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[47]毫秒
2025-06-17 15:06:08 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:06:08 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:06:08 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:06:08 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[13]毫秒
2025-06-17 15:06:13 [boundedElastic-399] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:06:33 [boundedElastic-390] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:06:53 [boundedElastic-402] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:07:13 [boundedElastic-407] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:07:33 [boundedElastic-376] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:07:53 [boundedElastic-407] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:08:13 [boundedElastic-403] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:08:33 [boundedElastic-407] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:08:53 [boundedElastic-405] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:09:13 [boundedElastic-402] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:09:33 [boundedElastic-408] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:09:53 [boundedElastic-392] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:10:13 [boundedElastic-400] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:10:33 [boundedElastic-407] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:10:53 [boundedElastic-412] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:11:13 [boundedElastic-407] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:11:33 [boundedElastic-406] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:11:53 [boundedElastic-408] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:12:05 [boundedElastic-408] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544736567296000],无参数
2025-06-17 15:12:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544736567296000],耗时:[18]毫秒
2025-06-17 15:12:06 [boundedElastic-408] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:12:06 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[18]毫秒
2025-06-17 15:12:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:12:06 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[14]毫秒
2025-06-17 15:12:06 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:12:06 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-17 15:12:06 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-17 15:12:06 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-17 15:12:07 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[7]毫秒
2025-06-17 15:12:07 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[7]毫秒
2025-06-17 15:12:07 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-17 15:12:07 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-17 15:12:07 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["warehouseNumber"],"isAsc":["asc"]}]
2025-06-17 15:12:07 [boundedElastic-408] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ4Zmc4M2ZVdFJCUWRoMldQR1VqeVY1N21keDl5Z0JiWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.KDhO3uTyMDy4eTa1p7SHiKk-s3m_8crqFoijOiPuCpw"]}]
2025-06-17 15:12:07 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-17 15:12:07 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[43]毫秒
2025-06-17 15:12:07 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:12:07 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[8]毫秒
2025-06-17 15:12:07 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-17 15:12:07 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[14]毫秒
2025-06-17 15:12:10 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-17 15:12:10 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-17 15:12:10 [boundedElastic-408] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-17 15:12:10 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[29]毫秒
2025-06-17 15:12:10 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:12:10 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-17 15:12:10 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-06-17 15:12:10 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-17 15:12:13 [boundedElastic-412] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-17 15:12:16 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-17 15:12:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[132]毫秒
2025-06-17 15:12:16 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:12:16 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[13]毫秒
2025-06-17 15:12:16 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:12:16 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-17 15:12:16 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:12:16 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:12:16 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[13]毫秒
2025-06-17 15:12:16 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-17 15:12:16 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:12:16 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSMzRuWmV2YmRoV09rMWhMRUhjWXNaRmljVlNRSUlOTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.q3qtNxC-J3mKgIlKMv0r5dqvpaLSpiXULGpbE6_MDVw"]}]
2025-06-17 15:12:16 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[10]毫秒
2025-06-17 15:12:16 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[5]毫秒
2025-06-17 15:12:16 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:12:16 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-17 15:12:16 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:12:16 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-17 15:12:17 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931679466777804800],无参数
2025-06-17 15:12:17 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931679466777804800],耗时:[12]毫秒
2025-06-17 15:12:19 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:12:19 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[18]毫秒
2025-06-17 15:12:19 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:12:19 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-17 15:12:19 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:12:19 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:12:19 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-17 15:12:19 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSMzRuWmV2YmRoV09rMWhMRUhjWXNaRmljVlNRSUlOTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.q3qtNxC-J3mKgIlKMv0r5dqvpaLSpiXULGpbE6_MDVw"]}]
2025-06-17 15:12:19 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-17 15:12:19 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-17 15:12:19 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:12:19 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-17 15:12:19 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931682136745902081],无参数
2025-06-17 15:12:19 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931682136745902081],耗时:[18]毫秒
2025-06-17 15:12:21 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931679466777804800],无参数
2025-06-17 15:12:21 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931679466777804800],耗时:[12]毫秒
2025-06-17 15:12:22 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-17 15:12:22 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-17 15:12:22 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-17 15:12:22 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[9]毫秒
2025-06-17 15:12:22 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-17 15:12:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-17 15:12:22 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSMzRuWmV2YmRoV09rMWhMRUhjWXNaRmljVlNRSUlOTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.q3qtNxC-J3mKgIlKMv0r5dqvpaLSpiXULGpbE6_MDVw"]}]
2025-06-17 15:12:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-17 15:12:22 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-17 15:12:22 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-17 15:12:22 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-17 15:12:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-17 15:12:22 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931682136745902081],无参数
2025-06-17 15:12:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931682136745902081],耗时:[15]毫秒
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-17 15:12:33 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-17 15:12:33 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-17 15:12:33 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-17 15:12:33 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-17 15:12:33 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-17 15:12:35 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
