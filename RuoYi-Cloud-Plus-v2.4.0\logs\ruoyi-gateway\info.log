2025-06-16 00:05:07 [boundedElastic-74] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:05:07 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[23]毫秒
2025-06-16 00:05:07 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:05:07 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-16 00:05:08 [boundedElastic-74] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[18]毫秒
2025-06-16 00:05:08 [boundedElastic-74] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-16 00:05:08 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-16 00:05:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:05:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-16 00:05:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:05:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-16 00:05:28 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:28 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:05:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-16 00:05:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 00:05:32 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:05:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[20]毫秒
2025-06-16 00:05:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:05:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 00:05:33 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:33 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:05:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-16 00:05:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 00:05:55 [boundedElastic-91] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 00:05:55 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[5]毫秒
2025-06-16 00:05:55 [boundedElastic-91] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 00:05:55 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[31]毫秒
2025-06-16 00:05:55 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:55 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:05:55 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-06-16 00:05:55 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:09:28 [boundedElastic-97] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:09:28 [boundedElastic-85] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:09:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[16]毫秒
2025-06-16 00:09:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[20]毫秒
2025-06-16 00:10:12 [boundedElastic-87] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:10:12 [boundedElastic-88] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:10:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-06-16 00:10:12 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[9]毫秒
2025-06-16 00:10:17 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:10:17 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:10:17 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-06-16 00:10:17 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 00:10:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:10:20 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:10:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-16 00:10:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:11 [boundedElastic-83] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:11 [boundedElastic-85] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:11 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:11:11 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:11:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:19 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:19 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:19 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:11:19 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 00:11:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:21 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-06-16 00:11:21 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-16 00:11:24 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:24 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:24 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-16 00:11:24 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:28 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:28 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-16 00:11:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:40 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 00:11:40 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[24]毫秒
2025-06-16 00:11:40 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:40 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 00:11:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 00:11:44 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[140]毫秒
2025-06-16 00:11:44 [boundedElastic-87] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:11:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-16 00:11:44 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:11:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 00:11:45 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:45 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 00:11:45 [boundedElastic-87] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJZREFsSUJtY3BFV0ExUkhRTEZxdWlJME5GcVZVMVFNYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fRVVxbxLZN4d6_rmTO3yU4bXOXAhSXVLX1YHO68ML0s"]}]
2025-06-16 00:11:45 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 00:13:00 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:13:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:13:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 08:23:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:23:32 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 29352 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:23:32 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 08:23:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 08:23:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:23:34 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:23:34 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:23:35 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:23:35 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:23:35 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:23:43 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 08:23:43 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 08:23:43 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 14.267 seconds (process running for 15.013)
2025-06-16 08:23:43 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 08:23:43 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:00:56 [boundedElastic-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 09:00:56 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[145]毫秒
2025-06-16 09:00:56 [boundedElastic-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 09:00:56 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[115]毫秒
2025-06-16 09:00:56 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 09:00:56 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:00:57 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[238]毫秒
2025-06-16 09:00:57 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[484]毫秒
2025-06-16 09:01:02 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 09:01:03 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1118]毫秒
2025-06-16 09:01:04 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:01:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[831]毫秒
2025-06-16 09:01:05 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:01:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-06-16 09:01:05 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:01:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-06-16 09:01:05 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:01:05 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[67]毫秒
2025-06-16 09:01:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:01:08 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:01:09 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[25]毫秒
2025-06-16 09:01:09 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[25]毫秒
2025-06-16 09:01:09 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:01:10 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1101]毫秒
2025-06-16 09:06:19 [boundedElastic-52] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:06:19 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[86]毫秒
2025-06-16 09:06:19 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:06:19 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[22]毫秒
2025-06-16 09:06:32 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:06:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[53]毫秒
2025-06-16 09:06:32 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:06:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-06-16 09:06:52 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:06:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[97]毫秒
2025-06-16 09:06:52 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:06:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-06-16 09:07:07 [boundedElastic-46] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:07:07 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[51]毫秒
2025-06-16 09:07:07 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:07:07 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[48]毫秒
2025-06-16 09:07:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:07:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[38]毫秒
2025-06-16 09:07:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:07:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-06-16 09:07:23 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:07:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:07:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:07:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[20]毫秒
2025-06-16 09:07:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[14]毫秒
2025-06-16 09:07:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[35]毫秒
2025-06-16 09:07:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:07:23 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[54]毫秒
2025-06-16 09:07:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:07:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[41]毫秒
2025-06-16 09:11:52 [boundedElastic-52] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:11:52 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[28]毫秒
2025-06-16 09:11:52 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:11:52 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 09:12:25 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:12:25 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[26]毫秒
2025-06-16 09:12:25 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:12:25 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 09:12:26 [boundedElastic-61] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:12:26 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:12:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:12:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:12:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[8]毫秒
2025-06-16 09:12:26 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 09:12:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-16 09:12:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[32]毫秒
2025-06-16 09:12:26 [boundedElastic-61] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:12:26 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[22]毫秒
2025-06-16 09:12:26 [boundedElastic-61] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:12:26 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[11]毫秒
2025-06-16 09:12:29 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 09:12:29 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:12:32 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 09:12:32 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[72]毫秒
2025-06-16 09:12:48 [boundedElastic-52] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:08 [boundedElastic-48] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:18 [boundedElastic-48] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 09:13:19 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 09:13:20 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 09:13:20 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 09:13:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 09:13:21 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[13]毫秒
2025-06-16 09:13:28 [boundedElastic-59] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:48 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:08 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:17 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:17 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-16 09:14:18 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[21]毫秒
2025-06-16 09:14:18 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:18 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 09:14:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 09:14:20 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:20 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 09:14:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:23 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:23 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[13]毫秒
2025-06-16 09:14:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[15]毫秒
2025-06-16 09:14:24 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:24 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 09:14:24 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:24 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 09:14:26 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["50"]}]
2025-06-16 09:14:26 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[12]毫秒
2025-06-16 09:14:28 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:29 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:29 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:14:33 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 09:14:33 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[8]毫秒
2025-06-16 09:14:33 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 09:14:33 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 09:14:33 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:33 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[7]毫秒
2025-06-16 09:14:33 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 09:14:33 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[16]毫秒
2025-06-16 09:14:33 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:33 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[37]毫秒
2025-06-16 09:14:48 [boundedElastic-59] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:08 [boundedElastic-48] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:13 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:15:13 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[24]毫秒
2025-06-16 09:15:13 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:15:13 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-16 09:15:14 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:15:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:15:14 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:15:14 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:14 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[7]毫秒
2025-06-16 09:15:14 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-16 09:15:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 09:15:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-16 09:15:14 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:15:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 09:15:14 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:15:14 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-16 09:15:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:16 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 09:15:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 09:15:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[10]毫秒
2025-06-16 09:15:26 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:15:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-16 09:15:26 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:15:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 09:15:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:15:27 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:15:27 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:27 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:15:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-16 09:15:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-16 09:15:27 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 09:15:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 09:15:27 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:15:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:15:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:15:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 09:15:28 [boundedElastic-57] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:15:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[20]毫秒
2025-06-16 09:15:42 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:15:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 09:15:44 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:15:44 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:15:44 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:15:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:44 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-16 09:15:44 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-16 09:15:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[9]毫秒
2025-06-16 09:15:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 09:15:44 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:15:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[17]毫秒
2025-06-16 09:15:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:15:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-16 09:15:48 [boundedElastic-60] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:08 [boundedElastic-64] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:09 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:16:09 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[23]毫秒
2025-06-16 09:16:09 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:16:09 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 09:16:10 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:16:10 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:16:10 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:16:10 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:16:10 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-16 09:16:10 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-16 09:16:10 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-16 09:16:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-16 09:16:10 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:16:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:16:10 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:16:10 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 09:16:11 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:16:11 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[15]毫秒
2025-06-16 09:16:11 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 09:16:11 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[11]毫秒
2025-06-16 09:16:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"193167946**********","warehouseNumber":"W002","warehouseName":"测试仓库1","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","remark":"测试专用","deptIds":[100]}]
2025-06-16 09:16:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[217]毫秒
2025-06-16 09:16:14 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:16:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[17]毫秒
2025-06-16 09:16:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:16:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[20]毫秒
2025-06-16 09:16:28 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:17:10 [SpringApplicationShutdownHook] INFO  o.s.c.s.DefaultLifecycleProcessor - Shutdown phase 2147482623 ends with 1 bean still running after timeout of 30000ms: [webServerGracefulShutdown]
2025-06-16 09:17:10 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown aborted with one or more requests still active
2025-06-16 09:17:12 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:17:12 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:17:12 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:32:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:32:12 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 39152 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:32:12 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 09:32:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:14 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:32:14 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:32:14 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:32:15 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:15 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 09:32:23 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 09:32:23 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 13.948 seconds (process running for 14.591)
2025-06-16 09:32:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 09:32:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:33:13 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:33:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1043]毫秒
2025-06-16 09:33:14 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:33:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[85]毫秒
2025-06-16 09:33:15 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:33:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:33:15 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:33:15 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:33:15 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[27]毫秒
2025-06-16 09:33:15 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[28]毫秒
2025-06-16 09:33:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[65]毫秒
2025-06-16 09:33:15 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:33:16 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[910]毫秒
2025-06-16 09:33:17 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1646]毫秒
2025-06-16 09:33:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:33:27 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[43]毫秒
2025-06-16 09:33:27 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:33:27 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-06-16 09:33:28 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:33:28 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:33:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:33:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:33:28 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[15]毫秒
2025-06-16 09:33:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[15]毫秒
2025-06-16 09:33:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-06-16 09:33:28 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[28]毫秒
2025-06-16 09:33:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:33:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[42]毫秒
2025-06-16 09:35:26 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:35:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[39]毫秒
2025-06-16 09:35:26 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:35:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-06-16 09:35:27 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:35:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:35:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:35:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:35:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-16 09:35:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[11]毫秒
2025-06-16 09:35:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[22]毫秒
2025-06-16 09:35:27 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-06-16 09:35:27 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:35:27 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[36]毫秒
2025-06-16 09:35:56 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:35:56 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[35]毫秒
2025-06-16 09:35:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:35:56 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[14]毫秒
2025-06-16 09:35:57 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:35:57 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:35:57 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:35:57 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:35:57 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 09:35:57 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[9]毫秒
2025-06-16 09:35:57 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[15]毫秒
2025-06-16 09:35:57 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-16 09:35:57 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:35:57 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[35]毫秒
2025-06-16 09:36:24 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:36:24 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:36:24 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:42:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:42:15 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 30052 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:42:15 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 09:42:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 09:42:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:42:18 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:42:18 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:42:18 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:42:18 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:42:18 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:42:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 09:42:28 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 09:42:28 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 16.604 seconds (process running for 17.488)
2025-06-16 09:42:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 09:42:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:43:56 [boundedElastic-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:43:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1595]毫秒
2025-06-16 09:43:58 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:43:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[92]毫秒
2025-06-16 09:43:59 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:43:59 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:43:59 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:43:59 [boundedElastic-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:43:59 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[36]毫秒
2025-06-16 09:43:59 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[36]毫秒
2025-06-16 09:43:59 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[64]毫秒
2025-06-16 09:43:59 [boundedElastic-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:44:00 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1060]毫秒
2025-06-16 09:44:01 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1511]毫秒
2025-06-16 09:44:36 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:44:36 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[85]毫秒
2025-06-16 09:44:36 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:44:36 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-06-16 09:44:37 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:44:37 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:44:37 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:44:37 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:44:37 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[12]毫秒
2025-06-16 09:44:37 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[12]毫秒
2025-06-16 09:44:37 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 09:44:37 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-06-16 09:44:37 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:44:37 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[39]毫秒
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:46:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:59:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:59:05 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 49456 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:59:05 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 09:59:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:59:06 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:59:06 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:59:07 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:59:07 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:59:07 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:59:15 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 09:59:16 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 09:59:16 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 14.29 seconds (process running for 15.003)
2025-06-16 09:59:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 09:59:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:00:16 [boundedElastic-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:00:18 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1316]毫秒
2025-06-16 10:00:18 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:00:18 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[103]毫秒
2025-06-16 10:00:19 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:00:19 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:00:19 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:00:19 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:00:19 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[157]毫秒
2025-06-16 10:00:19 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[157]毫秒
2025-06-16 10:00:19 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[199]毫秒
2025-06-16 10:00:19 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:00:20 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1218]毫秒
2025-06-16 10:00:20 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1314]毫秒
2025-06-16 10:00:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:00:31 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[27]毫秒
2025-06-16 10:00:31 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 10:00:31 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[60]毫秒
2025-06-16 10:00:37 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 10:00:37 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 10:00:37 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[13]毫秒
2025-06-16 10:00:37 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[23]毫秒
2025-06-16 10:00:40 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 10:00:40 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[17]毫秒
2025-06-16 10:00:55 [boundedElastic-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":100,"deptName":"测试部门1","orderNum":1}]
2025-06-16 10:00:55 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[134]毫秒
2025-06-16 10:00:55 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 10:00:55 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[20]毫秒
2025-06-16 10:00:58 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 10:00:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[20]毫秒
2025-06-16 10:01:05 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":100,"deptName":"测试部门2","orderNum":2}]
2025-06-16 10:01:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[40]毫秒
2025-06-16 10:01:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 10:01:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[19]毫秒
2025-06-16 10:01:10 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:01:10 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 10:01:10 [boundedElastic-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:01:10 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[41]毫秒
2025-06-16 10:01:13 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 10:01:13 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[36]毫秒
2025-06-16 10:01:14 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1934430959876390913"]}]
2025-06-16 10:01:14 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[21]毫秒
2025-06-16 10:01:14 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1934431002532462594"]}]
2025-06-16 10:01:14 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[19]毫秒
2025-06-16 10:01:15 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 10:01:15 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[21]毫秒
2025-06-16 10:01:16 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:01:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[22]毫秒
2025-06-16 10:01:16 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:01:16 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[24]毫秒
2025-06-16 10:01:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:01:18 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[17]毫秒
2025-06-16 10:01:18 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 10:01:18 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[20]毫秒
2025-06-16 10:01:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"193167946**********","warehouseNumber":"W002","warehouseName":"测试仓库1","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1934430959876390913","1934431002532462594"],"remark":"测试专用"}]
2025-06-16 10:01:23 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[191]毫秒
2025-06-16 10:01:23 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:01:23 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[29]毫秒
2025-06-16 10:01:25 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:01:25 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[19]毫秒
2025-06-16 10:01:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931682136745902081],无参数
2025-06-16 10:01:25 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931682136745902081],耗时:[26]毫秒
2025-06-16 10:01:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1931682136745902081","warehouseNumber":"W001","warehouseName":"测试仓库1","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1934430959876390913","1934431002532462594"],"remark":"测试专用"}]
2025-06-16 10:01:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[89]毫秒
2025-06-16 10:01:29 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:01:29 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[28]毫秒
2025-06-16 10:01:32 [boundedElastic-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:01:32 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[23]毫秒
2025-06-16 10:01:32 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931682136745902081],无参数
2025-06-16 10:01:32 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931682136745902081],耗时:[16]毫秒
2025-06-16 10:01:37 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1931682136745902081","warehouseNumber":"W001","warehouseName":"测试仓库1","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1934430959876390913"],"remark":"测试专用"}]
2025-06-16 10:01:37 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[37]毫秒
2025-06-16 10:01:37 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:01:37 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[31]毫秒
2025-06-16 10:04:45 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 10:04:45 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 10:04:46 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 10:11:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:11:40 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 29624 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:11:40 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 10:11:40 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 10:11:40 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:11:43 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:11:43 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:11:43 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:11:44 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:11:44 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:11:55 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 10:11:55 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 10:11:55 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 19.467 seconds (process running for 20.698)
2025-06-16 10:11:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 10:11:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:12:12 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 10:12:12 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 10:12:12 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 10:12:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:12:22 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 41128 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:12:22 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 10:12:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 10:12:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:12:26 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:12:26 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:12:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:12:27 [redisson-netty-3-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:12:27 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:12:37 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 10:12:38 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 10:12:38 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 20.124 seconds (process running for 21.213)
2025-06-16 10:12:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 10:12:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:13:00 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 10:13:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 10:13:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 10:30:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 10:30:46 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 47540 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 10:30:46 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 10:30:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 10:30:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 10:30:48 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 10:30:48 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 10:30:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 10:30:49 [redisson-netty-3-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:30:49 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 10:30:58 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 10:30:59 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 10:30:59 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 16.757 seconds (process running for 17.624)
2025-06-16 10:30:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 10:30:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 10:32:17 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:32:19 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1401]毫秒
2025-06-16 10:32:19 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:32:19 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[102]毫秒
2025-06-16 10:32:20 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:32:20 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:32:20 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:32:20 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:32:20 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[29]毫秒
2025-06-16 10:32:20 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[30]毫秒
2025-06-16 10:32:20 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:32:20 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[70]毫秒
2025-06-16 10:32:20 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:32:21 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1061]毫秒
2025-06-16 10:32:21 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1214]毫秒
2025-06-16 10:32:21 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[1374]毫秒
2025-06-16 10:33:11 [boundedElastic-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:33:11 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[70]毫秒
2025-06-16 10:33:11 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:33:11 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-06-16 10:33:12 [boundedElastic-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:33:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:33:12 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:33:12 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:33:12 [boundedElastic-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:33:12 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[12]毫秒
2025-06-16 10:33:12 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[12]毫秒
2025-06-16 10:33:12 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[21]毫秒
2025-06-16 10:33:12 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-06-16 10:33:12 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[27]毫秒
2025-06-16 10:33:12 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:33:12 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[38]毫秒
2025-06-16 10:33:42 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:33:42 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[33]毫秒
2025-06-16 10:33:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:33:42 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[22]毫秒
2025-06-16 10:33:43 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:33:43 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:33:43 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:33:43 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:33:43 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:33:43 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[9]毫秒
2025-06-16 10:33:43 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[9]毫秒
2025-06-16 10:33:43 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[19]毫秒
2025-06-16 10:33:43 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-06-16 10:33:43 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[23]毫秒
2025-06-16 10:33:43 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:33:43 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[41]毫秒
2025-06-16 10:50:53 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:50:53 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[89]毫秒
2025-06-16 10:50:53 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:50:53 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[21]毫秒
2025-06-16 10:50:54 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:50:54 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-16 10:50:54 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:50:54 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:50:54 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:50:54 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:50:54 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:50:54 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[9]毫秒
2025-06-16 10:50:54 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[18]毫秒
2025-06-16 10:50:54 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-06-16 10:50:55 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[1157]毫秒
2025-06-16 10:50:55 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1152]毫秒
2025-06-16 10:50:55 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:50:55 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[25]毫秒
2025-06-16 10:50:55 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/193167946**********],无参数
2025-06-16 10:50:56 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/193167946**********],耗时:[37]毫秒
2025-06-16 10:50:58 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931682136745902081],无参数
2025-06-16 10:50:58 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931682136745902081],耗时:[25]毫秒
2025-06-16 10:51:00 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:51:00 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[26]毫秒
2025-06-16 10:51:00 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:51:00 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[16]毫秒
2025-06-16 10:51:01 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:51:01 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:51:01 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:51:01 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:51:01 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:51:01 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:51:01 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[13]毫秒
2025-06-16 10:51:01 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[13]毫秒
2025-06-16 10:51:01 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-06-16 10:51:02 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[165]毫秒
2025-06-16 10:51:02 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[176]毫秒
2025-06-16 10:51:02 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[189]毫秒
2025-06-16 10:51:02 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:51:02 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[14]毫秒
2025-06-16 10:51:09 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:51:09 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[19]毫秒
2025-06-16 10:51:09 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/193167946**********],无参数
2025-06-16 10:51:09 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/193167946**********],耗时:[35]毫秒
2025-06-16 10:51:14 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"193167946**********","warehouseNumber":"W002","warehouseName":"测试仓库2","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[100],"remark":"测试专用"}]
2025-06-16 10:51:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[224]毫秒
2025-06-16 10:51:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:51:14 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[30]毫秒
2025-06-16 10:51:20 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:51:20 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-16 10:51:20 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:51:20 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[18]毫秒
2025-06-16 10:51:21 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:51:21 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:51:21 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:51:21 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:51:21 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:51:21 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:51:21 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[9]毫秒
2025-06-16 10:51:21 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[9]毫秒
2025-06-16 10:51:21 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 10:51:21 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-06-16 10:51:21 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[30]毫秒
2025-06-16 10:51:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[45]毫秒
2025-06-16 10:51:21 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:51:21 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[13]毫秒
2025-06-16 10:51:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/193167946**********],无参数
2025-06-16 10:51:25 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/193167946**********],耗时:[20]毫秒
2025-06-16 10:51:26 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:51:27 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[24]毫秒
2025-06-16 10:51:27 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:51:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 10:51:28 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:51:28 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:51:28 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:51:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:51:28 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:51:28 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:51:28 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 10:51:28 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[9]毫秒
2025-06-16 10:51:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[15]毫秒
2025-06-16 10:51:28 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-16 10:51:28 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[19]毫秒
2025-06-16 10:51:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[31]毫秒
2025-06-16 10:51:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:51:28 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[12]毫秒
2025-06-16 10:51:30 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931682136745902081],无参数
2025-06-16 10:51:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931682136745902081],耗时:[20]毫秒
2025-06-16 10:51:32 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:51:32 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[24]毫秒
2025-06-16 10:51:32 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:51:32 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 10:51:33 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:51:33 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 10:51:33 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:51:33 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 10:51:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 10:51:33 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 10:51:33 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[8]毫秒
2025-06-16 10:51:33 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[11]毫秒
2025-06-16 10:51:33 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[16]毫秒
2025-06-16 10:51:33 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[19]毫秒
2025-06-16 10:51:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[32]毫秒
2025-06-16 10:51:33 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[34]毫秒
2025-06-16 10:51:33 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:51:34 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 10:53:19 [boundedElastic-22] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 10:53:19 [boundedElastic-27] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:53:19 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[15]毫秒
2025-06-16 10:53:19 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[183]毫秒
2025-06-16 10:53:26 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 10:53:27 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[519]毫秒
2025-06-16 10:53:27 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:53:27 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[30]毫秒
2025-06-16 10:53:27 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:53:27 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[16]毫秒
2025-06-16 10:53:28 [boundedElastic-25] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:53:28 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[18]毫秒
2025-06-16 10:53:28 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:53:28 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[13]毫秒
2025-06-16 10:54:15 [boundedElastic-26] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:54:15 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[60]毫秒
2025-06-16 10:54:16 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:54:16 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-06-16 10:54:16 [boundedElastic-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:54:16 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-16 10:54:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:54:16 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:54:16 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[13]毫秒
2025-06-16 10:54:16 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[13]毫秒
2025-06-16 10:54:16 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:54:16 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[12]毫秒
2025-06-16 10:54:31 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:54:31 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[22]毫秒
2025-06-16 10:54:31 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:54:31 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[17]毫秒
2025-06-16 10:54:32 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:54:32 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[74]毫秒
2025-06-16 10:54:32 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:54:32 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:54:32 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 10:54:32 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[17]毫秒
2025-06-16 10:54:32 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:54:32 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[38]毫秒
2025-06-16 10:56:17 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 10:56:17 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:56:17 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[54]毫秒
2025-06-16 10:56:17 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[54]毫秒
2025-06-16 10:56:23 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 10:56:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[141]毫秒
2025-06-16 10:56:23 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:56:23 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-16 10:56:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:56:23 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-16 10:56:24 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:56:24 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k"]}]
2025-06-16 10:56:24 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:56:24 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[15]毫秒
2025-06-16 10:56:24 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 10:56:24 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:56:24 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[9]毫秒
2025-06-16 10:56:24 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:56:24 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-16 10:56:24 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[137]毫秒
2025-06-16 10:56:24 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:56:24 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 10:56:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/193167946**********],无参数
2025-06-16 10:56:28 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/193167946**********],耗时:[13]毫秒
2025-06-16 10:56:30 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:56:30 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[18]毫秒
2025-06-16 10:56:30 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:56:30 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-16 10:56:31 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:56:31 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k"]}]
2025-06-16 10:56:31 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:56:31 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[16]毫秒
2025-06-16 10:56:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-16 10:56:31 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-16 10:56:31 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:56:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 10:56:34 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:56:34 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[37]毫秒
2025-06-16 10:56:34 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:56:34 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[23]毫秒
2025-06-16 10:56:37 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k"]}]
2025-06-16 10:56:37 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[13]毫秒
2025-06-16 10:56:38 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k"]}]
2025-06-16 10:56:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[5]毫秒
2025-06-16 10:56:39 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTWlFrV1IyUGpzbFB5Q2wzNlVZSEJYVFZCRk5YSWpnbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fcdnDK9GVJ8SRfSsLwlx0VuZ2zmeGh3sFUuQR8bVG5k"]}]
2025-06-16 10:56:39 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-16 10:56:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 10:56:42 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:56:42 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-06-16 10:56:42 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[151]毫秒
2025-06-16 10:56:47 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 10:56:47 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[121]毫秒
2025-06-16 10:56:47 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 10:56:47 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[18]毫秒
2025-06-16 10:56:47 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 10:56:47 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-16 10:56:47 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:56:47 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJLMmROdjU3SVBLMWhLTVNqRGpVZlJ1VHB0MkZDMm5jeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.FrOSIa_uqLao6oRnfa01UtXVer21vz2YzrY3W0K42vA"]}]
2025-06-16 10:56:47 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:56:47 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[15]毫秒
2025-06-16 10:56:47 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-06-16 10:56:47 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[16]毫秒
2025-06-16 10:56:47 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 10:56:47 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-16 10:56:47 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:56:47 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 10:56:48 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 10:56:48 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 10:56:53 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:56:53 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-16 10:56:53 [boundedElastic-21] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:56:53 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[22]毫秒
2025-06-16 10:56:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJLMmROdjU3SVBLMWhLTVNqRGpVZlJ1VHB0MkZDMm5jeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.FrOSIa_uqLao6oRnfa01UtXVer21vz2YzrY3W0K42vA"]}]
2025-06-16 10:56:56 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-16 10:56:57 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJLMmROdjU3SVBLMWhLTVNqRGpVZlJ1VHB0MkZDMm5jeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.FrOSIa_uqLao6oRnfa01UtXVer21vz2YzrY3W0K42vA"]}]
2025-06-16 10:56:57 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-16 10:56:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJLMmROdjU3SVBLMWhLTVNqRGpVZlJ1VHB0MkZDMm5jeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.FrOSIa_uqLao6oRnfa01UtXVer21vz2YzrY3W0K42vA"]}]
2025-06-16 10:56:58 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 10:57:18 [boundedElastic-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:57:18 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-16 10:57:18 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:57:18 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[3]毫秒
2025-06-16 10:58:51 [boundedElastic-28] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:58:51 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[5]毫秒
2025-06-16 10:58:51 [boundedElastic-28] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:58:51 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[4]毫秒
2025-06-16 10:59:01 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:59:01 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-16 10:59:01 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:59:01 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[3]毫秒
2025-06-16 10:59:17 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:59:17 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[5]毫秒
2025-06-16 10:59:17 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:59:17 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[20]毫秒
2025-06-16 10:59:31 [boundedElastic-29] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 10:59:31 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[3]毫秒
2025-06-16 10:59:31 [boundedElastic-29] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 10:59:31 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[3]毫秒
2025-06-16 10:59:58 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 10:59:58 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 10:59:58 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[34]毫秒
2025-06-16 10:59:58 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[44]毫秒
2025-06-16 11:00:09 [boundedElastic-29] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:00:09 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:00:09 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[16]毫秒
2025-06-16 11:00:09 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-16 11:00:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:00:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:00:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[15]毫秒
2025-06-16 11:00:28 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-06-16 11:00:41 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:00:41 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:00:41 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[24]毫秒
2025-06-16 11:00:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[35]毫秒
2025-06-16 11:01:44 [boundedElastic-35] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:01:44 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[243]毫秒
2025-06-16 11:01:44 [boundedElastic-35] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:01:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[27]毫秒
2025-06-16 11:01:44 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:01:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[20]毫秒
2025-06-16 11:01:45 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:01:45 [boundedElastic-35] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:01:45 [boundedElastic-35] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJaNWlzbVFCRG1kbDN6VzlldXl3NlJoNmk0OWphVFd1ZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.KnEzZGGItABSQ6pwhmRVJkI0ZtoUwVDFm1Ekxb_QSGA"]}]
2025-06-16 11:01:45 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[55]毫秒
2025-06-16 11:01:45 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:01:45 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[78]毫秒
2025-06-16 11:01:45 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[23]毫秒
2025-06-16 11:01:45 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[59]毫秒
2025-06-16 11:01:45 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:01:45 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[17]毫秒
2025-06-16 11:01:45 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:01:45 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[56]毫秒
2025-06-16 11:01:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 11:01:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-16 11:01:50 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 11:01:50 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[22]毫秒
2025-06-16 11:01:51 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:01:51 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:01:51 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-06-16 11:01:51 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 11:01:56 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:01:56 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[241]毫秒
2025-06-16 11:01:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:01:56 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-16 11:01:56 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:01:56 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[70]毫秒
2025-06-16 11:01:56 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:01:56 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:01:56 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-16 11:01:56 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-16 11:01:57 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:01:57 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJEN0k2NUpYNVN0akpyNzg2bjRSbVhrNEtuTmJvN2FYWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fY8I3zD7ZSMqkjd3cPHwIlJluCvrD2uyOqafvQFb4VU"]}]
2025-06-16 11:01:57 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-16 11:01:57 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[12]毫秒
2025-06-16 11:01:57 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:01:57 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 11:01:57 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:01:57 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[8]毫秒
2025-06-16 11:02:00 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1931682136745902081],无参数
2025-06-16 11:02:00 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1931682136745902081],耗时:[15]毫秒
2025-06-16 11:02:02 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:02:02 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[158]毫秒
2025-06-16 11:02:02 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:02:02 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[9]毫秒
2025-06-16 11:02:03 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:02:03 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:02:03 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJEN0k2NUpYNVN0akpyNzg2bjRSbVhrNEtuTmJvN2FYWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fY8I3zD7ZSMqkjd3cPHwIlJluCvrD2uyOqafvQFb4VU"]}]
2025-06-16 11:02:03 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-16 11:02:03 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 11:02:03 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-16 11:02:03 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:02:03 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:02:06 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 11:02:06 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[3]毫秒
2025-06-16 11:02:06 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 11:02:06 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[20]毫秒
2025-06-16 11:02:06 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:02:06 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:02:06 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 11:02:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-06-16 11:02:11 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:02:11 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[257]毫秒
2025-06-16 11:02:11 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:02:11 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-16 11:02:11 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:02:11 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[9]毫秒
2025-06-16 11:02:12 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:02:12 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1ZXdPVVA2ZHA3S1Rka3BwcTNoVVp6bnp3YTF2dWdzNCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.WJhzOrTLjlKQjCdCxYnR1XZIutscMzxjCb1H5kw8T2w"]}]
2025-06-16 11:02:12 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:02:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-16 11:02:12 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[15]毫秒
2025-06-16 11:02:12 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-16 11:02:12 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:02:12 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[9]毫秒
2025-06-16 11:02:12 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:02:12 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:02:12 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:02:12 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:02:16 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 11:02:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[3]毫秒
2025-06-16 11:02:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 11:02:16 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[23]毫秒
2025-06-16 11:02:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:02:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:02:16 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 11:02:16 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[12]毫秒
2025-06-16 11:02:22 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:02:22 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[129]毫秒
2025-06-16 11:02:22 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:02:22 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[16]毫秒
2025-06-16 11:02:23 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:02:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[8]毫秒
2025-06-16 11:02:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJaUnJBaXZWc3FKRE10M3ZGUFBRTlZneEdiWDVnM0wwZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.4eJa8MChZxLREdFDtD2z2LjdztbylDk--zdQSyda0ZU"]}]
2025-06-16 11:02:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:02:23 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-16 11:02:23 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-16 11:02:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:02:23 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:02:23 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[9]毫秒
2025-06-16 11:02:23 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:02:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:02:23 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:02:29 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJaUnJBaXZWc3FKRE10M3ZGUFBRTlZneEdiWDVnM0wwZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.4eJa8MChZxLREdFDtD2z2LjdztbylDk--zdQSyda0ZU"]}]
2025-06-16 11:02:29 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 11:02:58 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 11:02:58 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[6]毫秒
2025-06-16 11:02:58 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 11:02:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[23]毫秒
2025-06-16 11:02:58 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:02:58 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:02:58 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[9]毫秒
2025-06-16 11:02:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-06-16 11:03:04 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:03:04 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[269]毫秒
2025-06-16 11:03:04 [boundedElastic-41] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:03:04 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[25]毫秒
2025-06-16 11:03:04 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:03:04 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 11:03:05 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:03:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwaXdrZ1daTkxkSzBZV0RTVjVydDY5M1MyeWZEUkZVTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.rRdbCzLRVV2NdJXb2LZQ6KVivFmlFKPCZB3zkxpl8Wg"]}]
2025-06-16 11:03:05 [boundedElastic-41] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:03:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 11:03:05 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 11:03:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 11:03:05 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:03:05 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-16 11:03:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:03:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 11:03:05 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:03:05 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:03:09 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:03:09 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/list],耗时:[51]毫秒
2025-06-16 11:03:10 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/1931985631759740929],无参数
2025-06-16 11:03:10 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/1931985631759740929],耗时:[35]毫秒
2025-06-16 11:03:10 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/tenantPackageMenuTreeselect/1931985631759740929],无参数
2025-06-16 11:03:10 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/tenantPackageMenuTreeselect/1931985631759740929],耗时:[34]毫秒
2025-06-16 11:03:20 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/tenant/package],参数类型[json],参数:[{"menuIds":[1,100,101,102,103,104,105,106,107,108,118,123,"1931961869597331458",500,501,1001,1002,1003,1004,1005,1006,1007,131,1008,1009,1010,1011,1012,130,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,132,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1050,1600,1601,1602,1603,1620,1621,1622,1623,133,1061,1062,1063,1064,1065,"1934242057848930306"],"packageId":"1931985631759740929","menuCheckStrictly":true,"packageName":"测试套餐"}]
2025-06-16 11:03:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/tenant/package],耗时:[123]毫秒
2025-06-16 11:03:20 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:03:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/list],耗时:[14]毫秒
2025-06-16 11:03:24 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:03:24 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[14]毫秒
2025-06-16 11:03:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/syncTenantPackage],参数类型[param],参数:[{"packageId":["1931985631759740929"],"tenantId":["666366"]}]
2025-06-16 11:03:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/syncTenantPackage],耗时:[55]毫秒
2025-06-16 11:03:27 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:03:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[11]毫秒
2025-06-16 11:03:36 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 11:03:36 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:03:36 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-16 11:03:36 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[8]毫秒
2025-06-16 11:03:36 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[9]毫秒
2025-06-16 11:03:36 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[22]毫秒
2025-06-16 11:03:41 [boundedElastic-24] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 11:03:41 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[20]毫秒
2025-06-16 11:03:44 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 11:03:44 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[10]毫秒
2025-06-16 11:03:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:03:44 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 11:03:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[150]毫秒
2025-06-16 11:03:44 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[160]毫秒
2025-06-16 11:03:51 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 11:03:51 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 11:03:51 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:03:51 [boundedElastic-35] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:03:51 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 11:03:51 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[8]毫秒
2025-06-16 11:03:51 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[13]毫秒
2025-06-16 11:03:51 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 11:03:59 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1931985777029459969"]}]
2025-06-16 11:03:59 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 11:04:03 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:03 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[12]毫秒
2025-06-16 11:04:05 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/selectList],无参数
2025-06-16 11:04:05 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/selectList],耗时:[12]毫秒
2025-06-16 11:04:05 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/1931985776903630849],无参数
2025-06-16 11:04:05 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/1931985776903630849],耗时:[10]毫秒
2025-06-16 11:04:13 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/tenant],参数类型[json],参数:[{"contactPhone":"***********","expireTime":"2026-06-16 00:00:00","accountCount":-1,"id":"1931985776903630849","tenantId":"666366","companyName":"泰兴市康威塑业有限公司","contactUserName":"樊道翔","password":"admin123","packageId":"1931985631759740929","address":"祥福街道"}]
2025-06-16 11:04:13 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/tenant],耗时:[104]毫秒
2025-06-16 11:04:13 [boundedElastic-38] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:04:13 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:13 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[12]毫秒
2025-06-16 11:04:13 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-16 11:04:20 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:20 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 11:04:20 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:20 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[12]毫秒
2025-06-16 11:04:22 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:22 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[9]毫秒
2025-06-16 11:04:22 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:22 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[12]毫秒
2025-06-16 11:04:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 11:04:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 11:04:25 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:25 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:25 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[11]毫秒
2025-06-16 11:04:25 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 11:04:25 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[20]毫秒
2025-06-16 11:04:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[25]毫秒
2025-06-16 11:04:26 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:26 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[9]毫秒
2025-06-16 11:04:27 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[18]毫秒
2025-06-16 11:04:27 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 11:04:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:27 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[14]毫秒
2025-06-16 11:04:30 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:04:30 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[20]毫秒
2025-06-16 11:04:30 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:04:30 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-16 11:04:31 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:04:31 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:04:31 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 11:04:31 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 11:04:31 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:04:31 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[7]毫秒
2025-06-16 11:04:31 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[7]毫秒
2025-06-16 11:04:31 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-16 11:04:31 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[13]毫秒
2025-06-16 11:04:31 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 11:04:31 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-06-16 11:04:31 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:31 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[14]毫秒
2025-06-16 11:04:31 [boundedElastic-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwaXdrZ1daTkxkSzBZV0RTVjVydDY5M1MyeWZEUkZVTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.rRdbCzLRVV2NdJXb2LZQ6KVivFmlFKPCZB3zkxpl8Wg"]}]
2025-06-16 11:04:31 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 11:04:31 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:04:31 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 11:04:35 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 11:04:35 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:35 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:35 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 11:04:35 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-16 11:04:35 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-16 11:04:35 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 11:04:35 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[20]毫秒
2025-06-16 11:04:38 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 11:04:38 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[9]毫秒
2025-06-16 11:04:38 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:38 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 11:04:38 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 11:04:38 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-16 11:04:38 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-16 11:04:38 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 11:04:38 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:38 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 11:04:41 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 11:04:42 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:42 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[12]毫秒
2025-06-16 11:04:42 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:42 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[13]毫秒
2025-06-16 11:04:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:42 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 11:04:44 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1931985777029459969"]}]
2025-06-16 11:04:44 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[10]毫秒
2025-06-16 11:04:49 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:04:49 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-16 11:04:50 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 11:04:50 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:50 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 11:04:50 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[5]毫秒
2025-06-16 11:04:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[5]毫秒
2025-06-16 11:04:50 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 11:04:50 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[15]毫秒
2025-06-16 11:04:51 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:51 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 11:04:51 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:51 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[16]毫秒
2025-06-16 11:04:52 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:04:52 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 11:04:52 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[13]毫秒
2025-06-16 11:04:56 [boundedElastic-21] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:04:56 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[11]毫秒
2025-06-16 11:05:00 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/selectList],无参数
2025-06-16 11:05:00 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/selectList],耗时:[9]毫秒
2025-06-16 11:05:00 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/1931985776903630849],无参数
2025-06-16 11:05:00 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/1931985776903630849],耗时:[10]毫秒
2025-06-16 11:05:03 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/tenant],参数类型[json],参数:[{"contactPhone":"***********","expireTime":"2026-06-16 00:00:00","accountCount":-1,"id":"1931985776903630849","tenantId":"666366","companyName":"泰兴市康威塑业有限公司","contactUserName":"樊道翔","password":"admin123","packageId":"1931985631759740929","address":"祥福街道"}]
2025-06-16 11:05:03 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/tenant],耗时:[19]毫秒
2025-06-16 11:05:03 [boundedElastic-24] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:05:03 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:05:03 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[12]毫秒
2025-06-16 11:05:03 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[15]毫秒
2025-06-16 11:05:05 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/syncTenantPackage],参数类型[param],参数:[{"packageId":["1931985631759740929"],"tenantId":["666366"]}]
2025-06-16 11:05:05 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/syncTenantPackage],耗时:[24]毫秒
2025-06-16 11:05:05 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:05:05 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[20]毫秒
2025-06-16 11:05:15 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:05:15 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[16]毫秒
2025-06-16 11:05:15 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:05:15 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 11:05:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:05:16 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 11:05:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:05:16 [boundedElastic-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:05:16 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:05:16 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 11:05:16 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[5]毫秒
2025-06-16 11:05:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[5]毫秒
2025-06-16 11:05:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[5]毫秒
2025-06-16 11:05:16 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 11:05:16 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 11:05:16 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 11:05:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:05:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[14]毫秒
2025-06-16 11:05:16 [boundedElastic-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwaXdrZ1daTkxkSzBZV0RTVjVydDY5M1MyeWZEUkZVTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.rRdbCzLRVV2NdJXb2LZQ6KVivFmlFKPCZB3zkxpl8Wg"]}]
2025-06-16 11:05:16 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:05:16 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 11:05:17 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[153]毫秒
2025-06-16 11:05:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 11:05:22 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[7]毫秒
2025-06-16 11:05:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:05:23 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 11:05:23 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:05:23 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 11:05:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-16 11:05:23 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[5]毫秒
2025-06-16 11:05:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[5]毫秒
2025-06-16 11:05:23 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[8]毫秒
2025-06-16 11:05:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:05:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[14]毫秒
2025-06-16 11:05:34 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 11:05:34 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[4]毫秒
2025-06-16 11:05:34 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 11:05:34 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[27]毫秒
2025-06-16 11:05:34 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:05:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:05:34 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-16 11:05:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[12]毫秒
2025-06-16 11:05:41 [boundedElastic-42] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:05:41 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[196]毫秒
2025-06-16 11:05:41 [boundedElastic-42] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:05:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-16 11:05:42 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:05:42 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 11:05:42 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJockdPUzdXN25mQ2dscFBBRXZJU3F4emVPYnhhRDdldCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4mumCrueuseWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Yfv6l93wKcb6aFUMVjbF5jmZYE6PbWRugh7glfF8M1A"]}]
2025-06-16 11:05:42 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:05:42 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 11:05:42 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[14]毫秒
2025-06-16 11:05:42 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:05:42 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-16 11:05:42 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:05:42 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-16 11:05:42 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:05:42 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-16 11:05:52 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 11:05:52 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:05:52 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 11:05:52 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 11:05:52 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:05:52 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[7]毫秒
2025-06-16 11:05:52 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[7]毫秒
2025-06-16 11:05:52 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[7]毫秒
2025-06-16 11:05:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 11:05:52 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[14]毫秒
2025-06-16 11:05:54 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 11:05:54 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[10]毫秒
2025-06-16 11:05:57 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/1931985777029459969],无参数
2025-06-16 11:05:57 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/1931985777029459969],耗时:[13]毫秒
2025-06-16 11:05:57 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list/dept/1931985777029459969],无参数
2025-06-16 11:05:57 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list/dept/1931985777029459969],耗时:[17]毫秒
2025-06-16 11:05:57 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list/exclude/1931985777029459969],无参数
2025-06-16 11:05:57 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list/exclude/1931985777029459969],耗时:[12]毫秒
2025-06-16 11:06:03 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dept],参数类型[json],参数:[{"status":"0","deptId":"1931985777029459969","parentId":0,"deptName":"泰兴市康威塑业有限公司","orderNum":0,"leader":"1931985777415335938"}]
2025-06-16 11:06:03 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dept],耗时:[168]毫秒
2025-06-16 11:06:03 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 11:06:03 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[12]毫秒
2025-06-16 11:06:14 [boundedElastic-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 11:06:14 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[6]毫秒
2025-06-16 11:06:14 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 11:06:14 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[27]毫秒
2025-06-16 11:06:14 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 11:06:14 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:06:14 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 11:06:14 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-06-16 11:06:26 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 11:06:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[239]毫秒
2025-06-16 11:06:26 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:06:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-16 11:06:26 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:06:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 11:06:26 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:06:26 [boundedElastic-42] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:06:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 11:06:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[13]毫秒
2025-06-16 11:06:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:06:26 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJNaURnZ2p4THRvU0l0dHlLbDljUjhxVXhiVTl4ZzNuUiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.YcKABU6vJWa-6TS30BQ57RISuMxg_j0Q8-DGskyWz2M"]}]
2025-06-16 11:06:26 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 11:06:26 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[12]毫秒
2025-06-16 11:06:26 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:06:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 11:06:26 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:06:26 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 11:06:30 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:06:30 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[10]毫秒
2025-06-16 11:07:08 [boundedElastic-28] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 11:07:08 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[157]毫秒
2025-06-16 11:07:08 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 11:07:09 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-16 11:07:09 [boundedElastic-28] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 11:07:09 [boundedElastic-28] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 11:07:09 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[12]毫秒
2025-06-16 11:07:09 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-16 11:07:09 [boundedElastic-28] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJNaURnZ2p4THRvU0l0dHlLbDljUjhxVXhiVTl4ZzNuUiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.YcKABU6vJWa-6TS30BQ57RISuMxg_j0Q8-DGskyWz2M"]}]
2025-06-16 11:07:09 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:09 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-16 11:07:09 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[12]毫秒
2025-06-16 11:07:10 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 11:07:10 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 11:07:15 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:15 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/list],耗时:[10]毫秒
2025-06-16 11:07:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 11:07:16 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[6]毫秒
2025-06-16 11:07:17 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:17 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/list],耗时:[11]毫秒
2025-06-16 11:07:21 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 11:07:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:21 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 11:07:21 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:07:21 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-16 11:07:21 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-16 11:07:21 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 11:07:21 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 11:07:24 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 11:07:24 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[9]毫秒
2025-06-16 11:07:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_yes_no],无参数
2025-06-16 11:07:32 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:32 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_yes_no],耗时:[12]毫秒
2025-06-16 11:07:32 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/list],耗时:[17]毫秒
2025-06-16 11:07:44 [boundedElastic-44] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:44 [boundedElastic-42] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 11:07:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[16]毫秒
2025-06-16 11:07:44 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[23]毫秒
2025-06-16 11:07:56 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 11:07:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[15]毫秒
2025-06-16 13:45:33 [boundedElastic-138] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 13:45:33 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[7]毫秒
2025-06-16 13:45:33 [boundedElastic-138] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 13:45:33 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[159]毫秒
2025-06-16 13:45:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:45:34 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 13:45:34 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[14]毫秒
2025-06-16 13:45:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[201]毫秒
2025-06-16 13:45:39 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 13:45:40 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[885]毫秒
2025-06-16 13:45:41 [boundedElastic-114] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 13:45:41 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[290]毫秒
2025-06-16 13:45:41 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 13:45:41 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[269]毫秒
2025-06-16 13:45:42 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:45:42 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIyVWp0bXpXM0RXYjNudTVISE5wS0U0Z0o0dUVEZXRWRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.Ck8zNQeZYPConAToCBaDKiGa89tpUKskwYfJ7zevLy8"]}]
2025-06-16 13:45:42 [boundedElastic-114] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:45:42 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 13:45:42 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[19]毫秒
2025-06-16 13:45:42 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-06-16 13:45:42 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:45:42 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[12]毫秒
2025-06-16 13:45:42 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:45:42 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[168]毫秒
2025-06-16 13:45:42 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:45:42 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[9]毫秒
2025-06-16 13:45:52 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 13:45:52 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-16 13:45:52 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[23]毫秒
2025-06-16 13:45:52 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[23]毫秒
2025-06-16 13:45:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:45:53 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[506]毫秒
2025-06-16 13:46:05 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:46:06 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[305]毫秒
2025-06-16 13:46:06 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/116],无参数
2025-06-16 13:46:06 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/116],耗时:[173]毫秒
2025-06-16 13:47:57 [boundedElastic-114] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:47:58 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[180]毫秒
2025-06-16 13:47:58 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/116],无参数
2025-06-16 13:47:58 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/116],耗时:[38]毫秒
2025-06-16 13:48:04 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/menu],参数类型[json],参数:[{"parentId":3,"menuType":"C","orderNum":2,"component":"tool/gen/editTable","isFrame":"1","visible":"1","status":"0","isCache":"1","menuId":116,"icon":"tabler:code","menuName":"修改生成配置","path":"gen-edit/index/:tableId","perms":"tool:gen:edit","queryParam":""}]
2025-06-16 13:48:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/menu],耗时:[689]毫秒
2025-06-16 13:48:05 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:48:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[451]毫秒
2025-06-16 13:48:16 [boundedElastic-168] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-16 13:48:16 [boundedElastic-160] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-16 13:48:17 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[807]毫秒
2025-06-16 13:48:19 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[2142]毫秒
2025-06-16 13:48:25 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-16 13:48:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[186]毫秒
2025-06-16 13:48:25 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-16 13:48:25 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[46]毫秒
2025-06-16 13:48:25 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:48:25 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[57]毫秒
2025-06-16 13:48:36 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 13:48:36 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[39]毫秒
2025-06-16 13:48:36 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 13:48:36 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-06-16 13:48:37 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:48:37 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-16 13:48:37 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:48:37 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-16 13:48:37 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIyVWp0bXpXM0RXYjNudTVISE5wS0U0Z0o0dUVEZXRWRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.Ck8zNQeZYPConAToCBaDKiGa89tpUKskwYfJ7zevLy8"]}]
2025-06-16 13:48:37 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[8]毫秒
2025-06-16 13:48:37 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-16 13:48:37 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[21]毫秒
2025-06-16 13:48:37 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[26]毫秒
2025-06-16 13:48:37 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[36]毫秒
2025-06-16 13:48:37 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:48:38 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[10]毫秒
2025-06-16 13:48:40 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-16 13:48:40 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[23]毫秒
2025-06-16 13:48:40 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-16 13:48:40 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[22]毫秒
2025-06-16 13:48:40 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:48:41 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[74]毫秒
2025-06-16 13:49:08 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 13:49:08 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-06-16 13:49:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 13:49:08 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[114]毫秒
2025-06-16 13:49:09 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:49:09 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:49:09 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-16 13:49:09 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[18]毫秒
2025-06-16 13:49:09 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-16 13:49:09 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[48]毫秒
2025-06-16 13:49:09 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[75]毫秒
2025-06-16 13:49:09 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIyVWp0bXpXM0RXYjNudTVISE5wS0U0Z0o0dUVEZXRWRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.Ck8zNQeZYPConAToCBaDKiGa89tpUKskwYfJ7zevLy8"]}]
2025-06-16 13:49:09 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[10]毫秒
2025-06-16 13:49:09 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[108]毫秒
2025-06-16 13:49:09 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:49:09 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[11]毫秒
2025-06-16 13:49:12 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-06-16 13:49:12 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[83]毫秒
2025-06-16 13:49:12 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-16 13:49:12 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[166]毫秒
2025-06-16 13:49:12 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:49:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[280]毫秒
2025-06-16 13:49:22 [boundedElastic-165] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 13:49:22 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[7]毫秒
2025-06-16 13:49:22 [boundedElastic-165] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 13:49:22 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[27]毫秒
2025-06-16 13:49:22 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 13:49:22 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:49:22 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-16 13:49:22 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[20]毫秒
2025-06-16 13:49:30 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 13:49:31 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[912]毫秒
2025-06-16 13:49:31 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 13:49:31 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[107]毫秒
2025-06-16 13:49:31 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 13:49:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-06-16 13:49:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJ2WjdpSzhZbUlnMFZKU1JyeTNRTUJUblRQTUpYZjJOZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Pjpg_9vvJwnUUfms2VIkBdz9JSMHrpYASEaSUva36eU"]}]
2025-06-16 13:49:32 [boundedElastic-165] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:49:32 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-16 13:49:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[13]毫秒
2025-06-16 13:49:32 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:49:32 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:49:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[12]毫秒
2025-06-16 13:49:32 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[13]毫秒
2025-06-16 13:49:32 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:49:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[69]毫秒
2025-06-16 13:49:39 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 13:49:39 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 13:49:39 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 13:49:39 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:49:39 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 13:49:39 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[19]毫秒
2025-06-16 13:49:39 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[19]毫秒
2025-06-16 13:49:39 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[39]毫秒
2025-06-16 13:49:45 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1931985777029459969"]}]
2025-06-16 13:49:45 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[15]毫秒
2025-06-16 13:50:04 [boundedElastic-170] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 13:50:04 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[5]毫秒
2025-06-16 13:50:05 [boundedElastic-170] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 13:50:05 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[29]毫秒
2025-06-16 13:50:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 13:50:05 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:50:05 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-16 13:50:05 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-06-16 13:50:12 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 13:50:12 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[810]毫秒
2025-06-16 13:50:12 [boundedElastic-171] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 13:50:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[260]毫秒
2025-06-16 13:50:13 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 13:50:13 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[252]毫秒
2025-06-16 13:50:13 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 13:50:13 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJrWkdJblVMeWtISEdmeGdJQ3pveDhDd0N4dDFXTGF6NyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.2Crlul0C3XfCoUpsWLFMCdaQiRsugegXYtrtxQYAryU"]}]
2025-06-16 13:50:13 [boundedElastic-171] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:50:13 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 13:50:13 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[15]毫秒
2025-06-16 13:50:13 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 13:50:13 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-06-16 13:50:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[11]毫秒
2025-06-16 13:50:13 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:50:13 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[7]毫秒
2025-06-16 13:50:13 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 13:50:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[168]毫秒
2025-06-16 13:50:16 [boundedElastic-167] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-06-16 13:50:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":[""]}]
2025-06-16 13:50:16 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[159]毫秒
2025-06-16 13:50:16 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[17]毫秒
2025-06-16 13:50:17 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1934177478799532034],无参数
2025-06-16 13:50:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1934177478799532034],耗时:[173]毫秒
2025-06-16 13:50:18 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:50:18 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[292]毫秒
2025-06-16 13:50:21 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 13:50:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 13:50:21 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 13:50:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:50:21 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[14]毫秒
2025-06-16 13:50:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[15]毫秒
2025-06-16 13:50:21 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[23]毫秒
2025-06-16 13:50:21 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[40]毫秒
2025-06-16 13:50:34 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 13:50:34 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:50:34 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 13:50:34 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 13:50:34 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 13:50:34 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[15]毫秒
2025-06-16 13:50:34 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[16]毫秒
2025-06-16 13:50:34 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[32]毫秒
2025-06-16 13:50:34 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[39]毫秒
2025-06-16 13:50:34 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[114]毫秒
2025-06-16 13:50:37 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-16 13:50:37 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[12]毫秒
2025-06-16 13:50:37 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 13:50:38 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[296]毫秒
2025-06-16 13:51:02 [boundedElastic-171] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 13:51:02 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[249]毫秒
2025-06-16 13:51:07 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 13:51:07 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:07 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[31]毫秒
2025-06-16 13:51:07 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[278]毫秒
2025-06-16 13:51:12 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:12 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/list],耗时:[40]毫秒
2025-06-16 13:51:13 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/1931985631759740929],无参数
2025-06-16 13:51:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/1931985631759740929],耗时:[177]毫秒
2025-06-16 13:51:13 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/tenantPackageMenuTreeselect/1931985631759740929],无参数
2025-06-16 13:51:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/tenantPackageMenuTreeselect/1931985631759740929],耗时:[776]毫秒
2025-06-16 13:51:21 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/tenant/package],参数类型[json],参数:[{"menuIds":[1,100,101,102,103,104,105,106,107,108,118,123,"1931961869597331458","1934242057848930306",500,501,1001,1002,1003,1004,1005,1006,1007,131,1008,1009,1010,1011,1012,130,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,132,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1050,1600,1601,1602,1603,1620,1621,1622,1623,133,1061,1062,1063,1064,1065,"1934242057848930307","1934242057848930308","1934242057848930309","1934242057848930310","1934242057848930311"],"packageId":"1931985631759740929","menuCheckStrictly":true,"packageName":"测试套餐"}]
2025-06-16 13:51:21 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/tenant/package],耗时:[307]毫秒
2025-06-16 13:51:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/package/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:21 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/package/list],耗时:[188]毫秒
2025-06-16 13:51:25 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[48]毫秒
2025-06-16 13:51:28 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/syncTenantPackage],参数类型[param],参数:[{"packageId":["1931985631759740929"],"tenantId":["666366"]}]
2025-06-16 13:51:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/syncTenantPackage],耗时:[392]毫秒
2025-06-16 13:51:28 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:28 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/list],耗时:[24]毫秒
2025-06-16 13:51:39 [boundedElastic-171] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_yes_no],无参数
2025-06-16 13:51:39 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:39 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_yes_no],耗时:[13]毫秒
2025-06-16 13:51:39 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/list],耗时:[27]毫秒
2025-06-16 13:51:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 13:51:42 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[13]毫秒
2025-06-16 13:51:42 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_yes_no],无参数
2025-06-16 13:51:42 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:42 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_yes_no],耗时:[179]毫秒
2025-06-16 13:51:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/list],耗时:[29]毫秒
2025-06-16 13:51:48 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:48 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:48 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[245]毫秒
2025-06-16 13:51:48 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[246]毫秒
2025-06-16 13:51:50 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 13:51:50 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[25]毫秒
2025-06-16 13:51:51 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:51:51 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[172]毫秒
2025-06-16 13:53:14 [boundedElastic-164] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/000000],无参数
2025-06-16 13:53:15 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/000000],耗时:[226]毫秒
2025-06-16 13:53:15 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:53:15 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:53:15 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[28]毫秒
2025-06-16 13:53:15 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[31]毫秒
2025-06-16 13:53:17 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 13:53:17 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[28]毫秒
2025-06-16 13:53:19 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/1934238728431386625],无参数
2025-06-16 13:53:19 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/1934238728431386625],耗时:[24]毫秒
2025-06-16 13:53:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/1934238728431386625],无参数
2025-06-16 13:53:22 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/1934238728431386625],耗时:[22]毫秒
2025-06-16 13:53:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:53:26 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[41]毫秒
2025-06-16 13:53:50 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 13:53:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[75]毫秒
2025-06-16 13:53:51 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:53:51 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:53:51 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[33]毫秒
2025-06-16 13:53:51 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[33]毫秒
2025-06-16 13:54:19 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/type],参数类型[json],参数:[{"dictType":"wms_warehouse_type","dictName":"仓库属性","remark":"仓库属性"}]
2025-06-16 13:54:20 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/type],耗时:[294]毫秒
2025-06-16 13:54:20 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:54:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[171]毫秒
2025-06-16 13:54:22 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 13:54:22 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[173]毫秒
2025-06-16 13:54:23 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:54:23 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[86]毫秒
2025-06-16 13:54:49 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"green","dictLabel":"普通仓库","dictValue":"0","dictSort":0,"remark":"普通仓库"}]
2025-06-16 13:54:50 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[316]毫秒
2025-06-16 13:54:50 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:54:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[176]毫秒
2025-06-16 13:55:05 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"primary","dictLabel":"车间仓库","dictValue":"1","dictSort":1,"remark":"车间仓库"}]
2025-06-16 13:55:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[273]毫秒
2025-06-16 13:55:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:55:06 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[183]毫秒
2025-06-16 13:55:21 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"","dictLabel":"供应商仓库","dictValue":"2","dictSort":2,"remark":"供应商仓库"}]
2025-06-16 13:55:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[223]毫秒
2025-06-16 13:55:22 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:55:22 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[183]毫秒
2025-06-16 13:55:24 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/1934489958743175170],无参数
2025-06-16 13:55:24 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/1934489958743175170],耗时:[25]毫秒
2025-06-16 13:55:28 [boundedElastic-172] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/dict/data],参数类型[json],参数:[{"dictCode":"1934489958743175170","dictType":"wms_warehouse_type","listClass":"orange","dictLabel":"供应商仓库","dictValue":"2","dictSort":2,"remark":"供应商仓库"}]
2025-06-16 13:55:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/dict/data],耗时:[280]毫秒
2025-06-16 13:55:28 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:55:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[180]毫秒
2025-06-16 13:55:43 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"orange","dictLabel":"客户仓库","dictValue":"3","dictSort":3,"remark":"客户仓库"}]
2025-06-16 13:55:43 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[275]毫秒
2025-06-16 13:55:43 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:55:43 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[239]毫秒
2025-06-16 13:56:03 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_type","listClass":"warning","dictLabel":"第三方仓储","dictValue":"4","dictSort":4,"remark":"第三方仓储"}]
2025-06-16 13:56:03 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[309]毫秒
2025-06-16 13:56:03 [boundedElastic-159] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_type"]}]
2025-06-16 13:56:03 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[179]毫秒
2025-06-16 13:56:08 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/000000],无参数
2025-06-16 13:56:08 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/000000],耗时:[14]毫秒
2025-06-16 13:56:08 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:56:08 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:56:08 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[30]毫秒
2025-06-16 13:56:08 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[30]毫秒
2025-06-16 13:56:11 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 13:56:11 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[27]毫秒
2025-06-16 13:56:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:56:13 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[31]毫秒
2025-06-16 13:56:37 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tenant/dynamic/666366],无参数
2025-06-16 13:56:37 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tenant/dynamic/666366],耗时:[16]毫秒
2025-06-16 13:56:37 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:56:37 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:56:37 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[184]毫秒
2025-06-16 13:56:37 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[184]毫秒
2025-06-16 13:56:39 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 13:56:39 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[30]毫秒
2025-06-16 13:57:09 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/type],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","dictName":"库存状态类型","remark":"库存状态类型"}]
2025-06-16 13:57:09 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/type],耗时:[200]毫秒
2025-06-16 13:57:09 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-06-16 13:57:09 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[173]毫秒
2025-06-16 13:57:10 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:57:11 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[176]毫秒
2025-06-16 13:57:28 [boundedElastic-174] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"green","dictLabel":"可用","dictValue":"0","dictSort":0,"remark":"可用"}]
2025-06-16 13:57:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[277]毫秒
2025-06-16 13:57:28 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:57:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[245]毫秒
2025-06-16 13:57:41 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"orange","dictLabel":"待检","dictValue":"1","dictSort":1,"remark":"待检"}]
2025-06-16 13:57:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[277]毫秒
2025-06-16 13:57:41 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:57:41 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[183]毫秒
2025-06-16 13:57:56 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"冻结","dictValue":"2","dictSort":2,"remark":"冻结"}]
2025-06-16 13:57:56 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[279]毫秒
2025-06-16 13:57:57 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:57:57 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[28]毫秒
2025-06-16 13:58:11 [boundedElastic-159] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"退回冻结","dictValue":"3","dictSort":3,"remark":"退回冻结"}]
2025-06-16 13:58:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[120]毫秒
2025-06-16 13:58:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:58:12 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[30]毫秒
2025-06-16 13:58:26 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"primary","dictLabel":"在途","dictValue":"4","dictSort":4,"remark":"在途"}]
2025-06-16 13:58:26 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[259]毫秒
2025-06-16 13:58:26 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:58:26 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[250]毫秒
2025-06-16 13:58:41 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"收货冻结","dictValue":"5","dictSort":5,"remark":"收货冻结"}]
2025-06-16 13:58:42 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[273]毫秒
2025-06-16 13:58:42 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:58:42 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[32]毫秒
2025-06-16 13:59:00 [boundedElastic-174] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"废品","dictValue":"6","dictSort":6,"remark":"废品"}]
2025-06-16 13:59:00 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[280]毫秒
2025-06-16 13:59:00 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:59:00 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[185]毫秒
2025-06-16 13:59:15 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"red","dictLabel":"不良","dictValue":"7","dictSort":7,"remark":"不良"}]
2025-06-16 13:59:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[275]毫秒
2025-06-16 13:59:16 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:59:16 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[32]毫秒
2025-06-16 13:59:31 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dict/data],参数类型[json],参数:[{"dictType":"wms_warehouse_instocktype","listClass":"info","dictLabel":"不参与核算","dictValue":"8","dictSort":8,"remark":"不参与核算"}]
2025-06-16 13:59:31 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dict/data],耗时:[276]毫秒
2025-06-16 13:59:32 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dictType":["wms_warehouse_instocktype"]}]
2025-06-16 13:59:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[31]毫秒
2025-06-16 13:59:41 [boundedElastic-152] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 13:59:41 [boundedElastic-177] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 13:59:41 [boundedElastic-175] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 13:59:41 [boundedElastic-174] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 13:59:41 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[15]毫秒
2025-06-16 13:59:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[15]毫秒
2025-06-16 13:59:41 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[16]毫秒
2025-06-16 13:59:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[119]毫秒
2025-06-16 13:59:50 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 13:59:50 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 13:59:50 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[80]毫秒
2025-06-16 13:59:50 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[197]毫秒
2025-06-16 16:23:56 [boundedElastic-285] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 16:23:56 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[5]毫秒
2025-06-16 16:23:56 [boundedElastic-285] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 16:23:56 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[6]毫秒
2025-06-16 16:23:57 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 16:23:57 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 16:23:57 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[14]毫秒
2025-06-16 16:23:57 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[47]毫秒
2025-06-16 16:24:04 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 16:24:04 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[33]毫秒
2025-06-16 16:24:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 16:24:05 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[8]毫秒
2025-06-16 16:24:09 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 16:24:09 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[191]毫秒
2025-06-16 16:24:09 [boundedElastic-281] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 16:24:09 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[57]毫秒
2025-06-16 16:24:10 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 16:24:10 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-06-16 16:24:10 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJCZ2xFbjNSdVB6MU1ZSlNTOGtMZk9ORkNEak5STnlGUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Hm_zCe5Unb5CrH-9gqkNcDr6LhSdmqAOsj1uOMDyySI"]}]
2025-06-16 16:24:10 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 16:24:10 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 16:24:10 [boundedElastic-281] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 16:24:10 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 16:24:10 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[18]毫秒
2025-06-16 16:24:10 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[24]毫秒
2025-06-16 16:24:10 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 16:24:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 16:24:10 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[13]毫秒
2025-06-16 16:24:10 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[49]毫秒
2025-06-16 16:24:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[12]毫秒
2025-06-16 16:24:10 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 16:24:10 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[7]毫秒
2025-06-16 16:24:12 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 16:24:12 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 16:24:12 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[10]毫秒
2025-06-16 16:24:12 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-16 16:24:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 16:24:12 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 16:24:12 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[9]毫秒
2025-06-16 16:24:12 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 16:24:14 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 16:24:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[22]毫秒
2025-06-16 16:52:26 [boundedElastic-316] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 16:52:26 [boundedElastic-321] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 16:52:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[35]毫秒
2025-06-16 16:52:26 [boundedElastic-316] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 16:52:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[173]毫秒
2025-06-16 16:52:27 [boundedElastic-316] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 16:52:27 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[301]毫秒
2025-06-16 16:52:27 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[250]毫秒
2025-06-16 16:52:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 16:52:27 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 16:52:27 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-16 16:52:27 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-06-16 16:52:33 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 16:52:34 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[861]毫秒
2025-06-16 16:52:34 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 16:52:34 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[429]毫秒
2025-06-16 16:52:34 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 16:52:34 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[268]毫秒
2025-06-16 16:52:35 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJGbktBcnV5STd5aTBXSnpERFlvZGVENFM2MndMUWZCNyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XllYOlAi6pNmM0wHXJd8533kZVfdyuCkEzXnTernKZk"]}]
2025-06-16 16:52:35 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 16:52:35 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 16:52:35 [boundedElastic-316] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 16:52:35 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[12]毫秒
2025-06-16 16:52:35 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[28]毫秒
2025-06-16 16:52:35 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[38]毫秒
2025-06-16 16:52:35 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 16:52:35 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 16:52:35 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[12]毫秒
2025-06-16 16:52:35 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[118]毫秒
2025-06-16 16:52:35 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[72]毫秒
2025-06-16 16:52:35 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 16:52:35 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[32]毫秒
2025-06-16 16:59:29 [boundedElastic-330] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 16:59:29 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[437]毫秒
2025-06-16 17:00:01 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"总经办","orderNum":1,"deptCategory":"BM000001"}]
2025-06-16 17:00:02 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[469]毫秒
2025-06-16 17:00:02 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:00:02 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[186]毫秒
2025-06-16 17:01:17 [boundedElastic-309] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:01:17 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[37]毫秒
2025-06-16 17:01:36 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"人事行政","orderNum":2,"deptCategory":"BM000002"}]
2025-06-16 17:01:37 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[273]毫秒
2025-06-16 17:01:37 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:01:37 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[256]毫秒
2025-06-16 17:01:41 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:01:41 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[182]毫秒
2025-06-16 17:01:55 [boundedElastic-332] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"供应链","orderNum":3,"deptCategory":"BM000003"}]
2025-06-16 17:01:55 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[282]毫秒
2025-06-16 17:01:55 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:01:55 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[248]毫秒
2025-06-16 17:01:58 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:01:58 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[34]毫秒
2025-06-16 17:02:15 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"财务部","orderNum":4,"deptCategory":"BM000004"}]
2025-06-16 17:02:15 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[125]毫秒
2025-06-16 17:02:15 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:02:15 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[244]毫秒
2025-06-16 17:04:02 [boundedElastic-318] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:04:02 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[40]毫秒
2025-06-16 17:04:27 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"品控部","orderNum":5,"deptCategory":"BM000005"}]
2025-06-16 17:04:27 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[206]毫秒
2025-06-16 17:04:27 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:04:27 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[185]毫秒
2025-06-16 17:04:30 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:04:30 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[33]毫秒
2025-06-16 17:04:45 [boundedElastic-328] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"注塑车间","orderNum":6,"deptCategory":"BM000006"}]
2025-06-16 17:04:45 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[267]毫秒
2025-06-16 17:04:45 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:04:45 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[258]毫秒
2025-06-16 17:04:47 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:04:47 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[34]毫秒
2025-06-16 17:05:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"中空车间","orderNum":7,"deptCategory":"BM000007"}]
2025-06-16 17:05:08 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[274]毫秒
2025-06-16 17:05:08 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:05:08 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[35]毫秒
2025-06-16 17:05:24 [boundedElastic-323] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:05:24 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[34]毫秒
2025-06-16 17:11:24 [boundedElastic-341] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"注拉吹车间","orderNum":8,"deptCategory":"BM000008"}]
2025-06-16 17:11:25 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[542]毫秒
2025-06-16 17:11:25 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:11:25 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[248]毫秒
2025-06-16 17:11:36 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:11:36 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[37]毫秒
2025-06-16 17:11:49 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"薄膜车间","orderNum":9,"deptCategory":"BM000009"}]
2025-06-16 17:11:49 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[124]毫秒
2025-06-16 17:11:49 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:11:49 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[37]毫秒
2025-06-16 17:11:53 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:11:53 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[36]毫秒
2025-06-16 17:12:07 [boundedElastic-336] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"外购","orderNum":10,"deptCategory":"BM000010"}]
2025-06-16 17:12:07 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[120]毫秒
2025-06-16 17:12:07 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:12:07 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[36]毫秒
2025-06-16 17:12:10 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:12:10 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[249]毫秒
2025-06-16 17:12:27 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"公用工程","orderNum":11,"deptCategory":"BM000011"}]
2025-06-16 17:12:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[133]毫秒
2025-06-16 17:12:27 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:12:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[249]毫秒
2025-06-16 17:12:30 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:12:30 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[37]毫秒
2025-06-16 17:13:17 [boundedElastic-335] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"工程部","orderNum":12,"deptCategory":"BM000012"}]
2025-06-16 17:13:17 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[56]毫秒
2025-06-16 17:13:18 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:13:18 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[23]毫秒
2025-06-16 17:13:28 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:13:28 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[24]毫秒
2025-06-16 17:13:47 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"印刷车间","orderNum":13,"deptCategory":"BM000013"}]
2025-06-16 17:13:47 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[181]毫秒
2025-06-16 17:13:47 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:13:47 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[50]毫秒
2025-06-16 17:13:48 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:13:48 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[25]毫秒
2025-06-16 17:14:02 [boundedElastic-343] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"配电车间","orderNum":14,"deptCategory":"BM000014"}]
2025-06-16 17:14:02 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[39]毫秒
2025-06-16 17:14:02 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:14:02 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[22]毫秒
2025-06-16 17:14:04 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:14:04 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[23]毫秒
2025-06-16 17:14:29 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"模具车间","orderNum":15,"deptCategory":"BM000015"}]
2025-06-16 17:14:29 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[43]毫秒
2025-06-16 17:14:29 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:14:29 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[27]毫秒
2025-06-16 17:14:32 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:14:32 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[26]毫秒
2025-06-16 17:14:50 [boundedElastic-333] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"吹膜制袋车间","orderNum":16,"deptCategory":"BM000016"}]
2025-06-16 17:14:50 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[43]毫秒
2025-06-16 17:14:50 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:14:50 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[25]毫秒
2025-06-16 17:14:51 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:14:51 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[24]毫秒
2025-06-16 17:15:06 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"安环部","orderNum":17,"deptCategory":"BM000017"}]
2025-06-16 17:15:06 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[182]毫秒
2025-06-16 17:15:06 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:15:06 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[24]毫秒
2025-06-16 17:15:09 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:15:09 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[26]毫秒
2025-06-16 17:15:26 [boundedElastic-340] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"后勤","orderNum":18,"deptCategory":"BM000018"}]
2025-06-16 17:15:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[38]毫秒
2025-06-16 17:15:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:15:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[27]毫秒
2025-06-16 17:15:30 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:15:30 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[25]毫秒
2025-06-16 17:15:42 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"质检部","orderNum":19,"deptCategory":"BM000019"}]
2025-06-16 17:15:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[41]毫秒
2025-06-16 17:15:43 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:15:43 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[25]毫秒
2025-06-16 17:19:03 [boundedElastic-318] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:19:03 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[49]毫秒
2025-06-16 17:19:36 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"综合部","orderNum":20,"deptCategory":"BM000020"}]
2025-06-16 17:19:36 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[70]毫秒
2025-06-16 17:19:36 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:19:36 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[24]毫秒
2025-06-16 17:20:07 [boundedElastic-334] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:20:07 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[170]毫秒
2025-06-16 17:20:18 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/dept],参数类型[json],参数:[{"status":"0","parentId":"1931985777029459969","deptName":"研发部","orderNum":21,"deptCategory":"BM000021"}]
2025-06-16 17:20:18 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/dept],耗时:[41]毫秒
2025-06-16 17:20:19 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],无参数
2025-06-16 17:20:19 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[28]毫秒
2025-06-16 17:20:47 [boundedElastic-318] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 17:20:47 [boundedElastic-352] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:20:47 [boundedElastic-354] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 17:20:47 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[24]毫秒
2025-06-16 17:20:47 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[25]毫秒
2025-06-16 17:20:47 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[50]毫秒
2025-06-16 17:20:47 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:20:47 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[30]毫秒
2025-06-16 17:20:54 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/1931985777415335938],无参数
2025-06-16 17:20:54 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/1931985777415335938],耗时:[90]毫秒
2025-06-16 17:20:55 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:20:55 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/optionselect],参数类型[param],参数:[{"deptId":["1931985777029459969"]}]
2025-06-16 17:20:55 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/optionselect],耗时:[23]毫秒
2025-06-16 17:20:55 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[23]毫秒
2025-06-16 17:21:08 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:21:08 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[50]毫秒
2025-06-16 17:21:17 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-06-16 17:21:17 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[53]毫秒
2025-06-16 17:23:17 [boundedElastic-354] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-06-16 17:23:17 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[21]毫秒
2025-06-16 17:23:17 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-06-16 17:23:17 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[54]毫秒
2025-06-16 17:23:24 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/1934536432063508481],无参数
2025-06-16 17:23:24 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/1934536432063508481],耗时:[42]毫秒
2025-06-16 17:23:24 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list/dept/1934536432063508481],无参数
2025-06-16 17:23:24 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list/dept/1934536432063508481],耗时:[19]毫秒
2025-06-16 17:23:24 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list/exclude/1934536432063508481],无参数
2025-06-16 17:23:24 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list/exclude/1934536432063508481],耗时:[28]毫秒
2025-06-16 17:23:30 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/1934536830216204290],无参数
2025-06-16 17:23:31 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/1934536830216204290],耗时:[186]毫秒
2025-06-16 17:23:31 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list/dept/1934536830216204290],无参数
2025-06-16 17:23:31 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list/dept/1934536830216204290],耗时:[18]毫秒
2025-06-16 17:23:31 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list/exclude/1934536830216204290],无参数
2025-06-16 17:23:31 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list/exclude/1934536830216204290],耗时:[29]毫秒
2025-06-16 17:23:35 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:23:35 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:23:35 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[18]毫秒
2025-06-16 17:23:35 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[25]毫秒
2025-06-16 17:23:39 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:23:39 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[25]毫秒
2025-06-16 17:23:39 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:23:39 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[18]毫秒
2025-06-16 17:23:41 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:23:41 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[31]毫秒
2025-06-16 17:30:23 [boundedElastic-350] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 17:30:23 [boundedElastic-354] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:30:23 [boundedElastic-365] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:30:23 [boundedElastic-360] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 17:30:23 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[20]毫秒
2025-06-16 17:30:23 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[41]毫秒
2025-06-16 17:30:23 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[41]毫秒
2025-06-16 17:30:23 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[60]毫秒
2025-06-16 17:30:28 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1934537620498575362"]}]
2025-06-16 17:30:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 17:30:30 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:30:30 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[25]毫秒
2025-06-16 17:31:59 [boundedElastic-360] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W001","warehouseName":"成品库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:31:59 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[171]毫秒
2025-06-16 17:31:59 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1934537620498575362"]}]
2025-06-16 17:31:59 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[10]毫秒
2025-06-16 17:32:04 [boundedElastic-360] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:32:04 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[27]毫秒
2025-06-16 17:32:04 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:32:04 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 17:32:08 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:32:08 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[28]毫秒
2025-06-16 17:32:30 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W002","warehouseName":"成品立库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:32:30 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[22]毫秒
2025-06-16 17:32:30 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:32:30 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[15]毫秒
2025-06-16 17:32:35 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:32:35 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[25]毫秒
2025-06-16 17:33:01 [boundedElastic-360] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W003","warehouseName":"半成品库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:33:01 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[163]毫秒
2025-06-16 17:33:02 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:33:02 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[17]毫秒
2025-06-16 17:34:49 [boundedElastic-354] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:34:49 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[175]毫秒
2025-06-16 17:35:11 [boundedElastic-355] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W003","warehouseName":"原辅料库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:35:11 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[23]毫秒
2025-06-16 17:35:11 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:35:11 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[19]毫秒
2025-06-16 17:35:13 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:35:13 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[27]毫秒
2025-06-16 17:35:30 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W005","warehouseName":"车间仓库","warehouseType":"1","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:35:30 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[20]毫秒
2025-06-16 17:35:30 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:35:30 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[22]毫秒
2025-06-16 17:36:34 [boundedElastic-355] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:36:34 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[30]毫秒
2025-06-16 17:36:58 [boundedElastic-343] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W006","warehouseName":"五金办公库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:36:58 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[22]毫秒
2025-06-16 17:36:58 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:36:58 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[27]毫秒
2025-06-16 17:36:59 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:37:00 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[28]毫秒
2025-06-16 17:37:17 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W007","warehouseName":"客供仓库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:37:17 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[20]毫秒
2025-06-16 17:37:17 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:37:17 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[27]毫秒
2025-06-16 17:37:18 [boundedElastic-369] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:37:18 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[28]毫秒
2025-06-16 17:37:40 [boundedElastic-360] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W008","warehouseName":"模具仓库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:37:40 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[19]毫秒
2025-06-16 17:37:40 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:37:40 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[28]毫秒
2025-06-16 17:37:41 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:37:41 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[27]毫秒
2025-06-16 17:37:57 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W009","warehouseName":"待检库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:37:57 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[19]毫秒
2025-06-16 17:37:57 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:37:57 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[29]毫秒
2025-06-16 17:37:59 [boundedElastic-354] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 17:37:59 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[30]毫秒
2025-06-16 17:38:19 [boundedElastic-373] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse],参数类型[json],参数:[{"warehouseNumber":"W010","warehouseName":"报废库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":[]}]
2025-06-16 17:38:19 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse],耗时:[21]毫秒
2025-06-16 17:38:20 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 17:38:20 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[33]毫秒
2025-06-16 17:39:00 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 17:39:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 17:39:01 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 17:57:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 17:57:09 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 31328 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 17:57:09 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 17:57:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 17:57:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 17:57:11 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 17:57:11 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 17:57:11 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 17:57:11 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:57:11 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 17:57:14 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway *************:8080 register finished
2025-06-16 17:57:14 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 17:57:15 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 7.751 seconds (process running for 8.602)
2025-06-16 17:57:15 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 17:57:15 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 17:58:17 [boundedElastic-8] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 17:58:37 [boundedElastic-5] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 17:58:57 [boundedElastic-5] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 17:59:17 [boundedElastic-2] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 17:59:37 [boundedElastic-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 17:59:57 [boundedElastic-13] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:00:17 [boundedElastic-7] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:00:37 [boundedElastic-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:00:38 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 18:00:39 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1414]毫秒
2025-06-16 18:00:40 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 18:00:40 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[68]毫秒
2025-06-16 18:00:41 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJGbktBcnV5STd5aTBXSnpERFlvZGVENFM2MndMUWZCNyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XllYOlAi6pNmM0wHXJd8533kZVfdyuCkEzXnTernKZk"]}]
2025-06-16 18:00:41 [boundedElastic-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 18:00:42 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[206]毫秒
2025-06-16 18:00:43 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[1864]毫秒
2025-06-16 18:00:43 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 18:00:43 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[103]毫秒
2025-06-16 18:00:45 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 18:00:45 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 18:00:45 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:00:45 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[33]毫秒
2025-06-16 18:00:45 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[33]毫秒
2025-06-16 18:00:45 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:00:45 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[144]毫秒
2025-06-16 18:00:45 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[209]毫秒
2025-06-16 18:00:57 [boundedElastic-11] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:01:17 [boundedElastic-7] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:01:32 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:01:32 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[30]毫秒
2025-06-16 18:01:32 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:01:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[89]毫秒
2025-06-16 18:01:36 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:01:36 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[27]毫秒
2025-06-16 18:01:36 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934544474083557378],无参数
2025-06-16 18:01:36 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934544474083557378],耗时:[38]毫秒
2025-06-16 18:01:37 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:01:45 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:01:45 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[22]毫秒
2025-06-16 18:01:45 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:01:45 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[69]毫秒
2025-06-16 18:01:57 [boundedElastic-11] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:02:00 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1931985777029459969"]}]
2025-06-16 18:02:00 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[26]毫秒
2025-06-16 18:02:04 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:04 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[27]毫秒
2025-06-16 18:02:04 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:04 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[57]毫秒
2025-06-16 18:02:06 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:06 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[18]毫秒
2025-06-16 18:02:06 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934544474083557378],无参数
2025-06-16 18:02:06 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934544474083557378],耗时:[35]毫秒
2025-06-16 18:02:11 [boundedElastic-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934544474083557378","warehouseNumber":"W001","warehouseName":"成品库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:11 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[254]毫秒
2025-06-16 18:02:11 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:11 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[68]毫秒
2025-06-16 18:02:12 [boundedElastic-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:12 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[26]毫秒
2025-06-16 18:02:12 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934544605054894081],无参数
2025-06-16 18:02:13 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934544605054894081],耗时:[26]毫秒
2025-06-16 18:02:15 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934544605054894081","warehouseNumber":"W002","warehouseName":"成品立库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[38]毫秒
2025-06-16 18:02:15 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[55]毫秒
2025-06-16 18:02:17 [boundedElastic-8] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:02:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 18:02:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[45]毫秒
2025-06-16 18:02:20 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 18:02:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-06-16 18:02:21 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:21 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 18:02:21 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 18:02:21 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 18:02:21 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[10]毫秒
2025-06-16 18:02:21 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-16 18:02:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[22]毫秒
2025-06-16 18:02:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[24]毫秒
2025-06-16 18:02:21 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:21 [boundedElastic-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJGbktBcnV5STd5aTBXSnpERFlvZGVENFM2MndMUWZCNyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XllYOlAi6pNmM0wHXJd8533kZVfdyuCkEzXnTernKZk"]}]
2025-06-16 18:02:21 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-16 18:02:21 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 18:02:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[61]毫秒
2025-06-16 18:02:21 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[15]毫秒
2025-06-16 18:02:21 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544474083557378],无参数
2025-06-16 18:02:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544474083557378],耗时:[22]毫秒
2025-06-16 18:02:24 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:24 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[19]毫秒
2025-06-16 18:02:24 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934544736567296001],无参数
2025-06-16 18:02:24 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934544736567296001],耗时:[21]毫秒
2025-06-16 18:02:27 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934544736567296001","warehouseNumber":"W003","warehouseName":"半成品库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[36]毫秒
2025-06-16 18:02:28 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:28 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[49]毫秒
2025-06-16 18:02:30 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:30 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[18]毫秒
2025-06-16 18:02:30 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934545280719519746],无参数
2025-06-16 18:02:30 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934545280719519746],耗时:[21]毫秒
2025-06-16 18:02:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934545280719519746","warehouseNumber":"W003","warehouseName":"原辅料库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:32 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[40]毫秒
2025-06-16 18:02:32 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[48]毫秒
2025-06-16 18:02:35 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:35 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 18:02:35 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934545357651443713],无参数
2025-06-16 18:02:35 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934545357651443713],耗时:[21]毫秒
2025-06-16 18:02:37 [boundedElastic-10] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:02:37 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934545357651443713","warehouseNumber":"W005","warehouseName":"车间仓库","warehouseType":"1","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:37 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[35]毫秒
2025-06-16 18:02:38 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:38 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[52]毫秒
2025-06-16 18:02:39 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:39 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[17]毫秒
2025-06-16 18:02:39 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934545727056379906],无参数
2025-06-16 18:02:39 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934545727056379906],耗时:[19]毫秒
2025-06-16 18:02:42 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934545727056379906","warehouseNumber":"W006","warehouseName":"五金办公库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:42 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[26]毫秒
2025-06-16 18:02:42 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[48]毫秒
2025-06-16 18:02:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:44 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[18]毫秒
2025-06-16 18:02:44 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934545808203579393],无参数
2025-06-16 18:02:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934545808203579393],耗时:[17]毫秒
2025-06-16 18:02:46 [boundedElastic-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934545808203579393","warehouseNumber":"W007","warehouseName":"客供仓库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:46 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[26]毫秒
2025-06-16 18:02:46 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:46 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[40]毫秒
2025-06-16 18:02:49 [boundedElastic-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:49 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[18]毫秒
2025-06-16 18:02:49 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934545903200370689],无参数
2025-06-16 18:02:49 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934545903200370689],耗时:[21]毫秒
2025-06-16 18:02:51 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934545903200370689","warehouseNumber":"W008","warehouseName":"模具仓库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:51 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[35]毫秒
2025-06-16 18:02:51 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:51 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[46]毫秒
2025-06-16 18:02:53 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:53 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[17]毫秒
2025-06-16 18:02:53 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934545975296262145],无参数
2025-06-16 18:02:53 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934545975296262145],耗时:[17]毫秒
2025-06-16 18:02:55 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934545975296262145","warehouseNumber":"W009","warehouseName":"待检库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:56 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[36]毫秒
2025-06-16 18:02:56 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:56 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[47]毫秒
2025-06-16 18:02:57 [boundedElastic-7] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:02:57 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:02:57 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[18]毫秒
2025-06-16 18:02:57 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1934546070007840769],无参数
2025-06-16 18:02:57 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1934546070007840769],耗时:[21]毫秒
2025-06-16 18:02:59 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1934546070007840769","warehouseNumber":"W010","warehouseName":"报废库","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","deptIds":["1931985777029459969"],"remark":null}]
2025-06-16 18:02:59 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[106]毫秒
2025-06-16 18:02:59 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:02:59 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[57]毫秒
2025-06-16 18:03:05 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 18:03:05 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[30]毫秒
2025-06-16 18:03:05 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 18:03:05 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[23]毫秒
2025-06-16 18:03:06 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 18:03:06 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 18:03:06 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 18:03:06 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:03:06 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-16 18:03:06 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[10]毫秒
2025-06-16 18:03:06 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[19]毫秒
2025-06-16 18:03:06 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 18:03:06 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:03:06 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJGbktBcnV5STd5aTBXSnpERFlvZGVENFM2MndMUWZCNyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XllYOlAi6pNmM0wHXJd8533kZVfdyuCkEzXnTernKZk"]}]
2025-06-16 18:03:06 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-16 18:03:06 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[56]毫秒
2025-06-16 18:03:06 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 18:03:06 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[12]毫秒
2025-06-16 18:03:15 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /wms/warehouse/current/1934544605054894081],无参数
2025-06-16 18:03:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /wms/warehouse/current/1934544605054894081],耗时:[17]毫秒
2025-06-16 18:03:16 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 18:03:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[33]毫秒
2025-06-16 18:03:16 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 18:03:16 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[17]毫秒
2025-06-16 18:03:17 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 18:03:17 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 18:03:17 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/accessible],无参数
2025-06-16 18:03:17 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 18:03:17 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[8]毫秒
2025-06-16 18:03:17 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 18:03:17 [boundedElastic-1] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:03:17 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/accessible],耗时:[23]毫秒
2025-06-16 18:03:17 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 18:03:17 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[36]毫秒
2025-06-16 18:03:17 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTMxOTg1Nzc3NDE1MzM1OTM4Iiwicm5TdHIiOiJGbktBcnV5STd5aTBXSnpERFlvZGVENFM2MndMUWZCNyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjY2NjM2NiIsInVzZXJJZCI6MTkzMTk4NTc3NzQxNTMzNTkzOCwidXNlck5hbWUiOiJrd3N5MDAxIiwiZGVwdElkIjoxOTMxOTg1Nzc3MDI5NDU5OTY5LCJkZXB0TmFtZSI6IuazsOWFtOW4guW6t-WogeWhkeS4muaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XllYOlAi6pNmM0wHXJd8533kZVfdyuCkEzXnTernKZk"]}]
2025-06-16 18:03:17 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[20]毫秒
2025-06-16 18:03:17 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/current],无参数
2025-06-16 18:03:17 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/current],耗时:[15]毫秒
2025-06-16 18:03:17 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[63]毫秒
2025-06-16 18:03:37 [boundedElastic-13] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:03:57 [boundedElastic-3] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:04:17 [boundedElastic-10] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:04:37 [boundedElastic-17] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:04:57 [boundedElastic-7] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:05:17 [boundedElastic-11] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:05:37 [boundedElastic-4] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
2025-06-16 18:05:57 [boundedElastic-19] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='*************, port=8718}]
