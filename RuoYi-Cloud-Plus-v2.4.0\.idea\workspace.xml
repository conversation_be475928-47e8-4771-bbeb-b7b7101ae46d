<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="32cfeba7-8126-42db-8fd1-f8f73c8ab929" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yXstcdcnxYUsPzTPNy9RttsaMX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;Spring Boot.DashboardApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.Nacos.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiAuthApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiDemoApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiGatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiGenApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiJobApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiMonitorApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiResourceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiSystemApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiTestMqApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiWMSApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiWorkflowApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SeataServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SnailJobServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/kwsywms_test/RuoYi-Cloud-Plus-v2.4.0/ruoyi-modules/ruoyi-wms/src/main/resources&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;问题&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;File.Encoding&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\src\main\resources" />
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\src" />
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\src\main\java\org\dromara\gen\util" />
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\src\main\resources\vm" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.dromara.wms" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="configurationStatuses">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <map>
              <entry key="DashboardApplication" value="STOPPED" />
              <entry key="Nacos" value="STOPPED" />
              <entry key="RuoYiAuthApplication" value="STOPPED" />
              <entry key="RuoYiDemoApplication" value="STOPPED" />
              <entry key="RuoYiGatewayApplication" value="STOPPED" />
              <entry key="RuoYiGenApplication" value="STOPPED" />
              <entry key="RuoYiJobApplication" value="STOPPED" />
              <entry key="RuoYiMonitorApplication" value="STOPPED" />
              <entry key="RuoYiResourceApplication" value="STOPPED" />
              <entry key="RuoYiSystemApplication" value="STOPPED" />
              <entry key="RuoYiTestMqApplication" value="FAILED" />
              <entry key="RuoYiWMSApplication" value="STOPPED" />
              <entry key="RuoYiWorkflowApplication" value="STOPPED" />
              <entry key="SeataServerApplication" value="STOPPED" />
              <entry key="SnailJobServerApplication" value="STOPPED" />
            </map>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Docker.ruoyi-auth">
    <configuration name="DashboardApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-sentinel-dashboard" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.alibaba.csp.sentinel.dashboard.DashboardApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Nacos" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-nacos" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.alibaba.nacos.Nacos" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.auth.RuoYiAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiDemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-demo" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.demo.RuoYiDemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.gateway.RuoYiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.gen.RuoYiGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiJobApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-job" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.job.RuoYiJobApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiMonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-monitor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.modules.monitor.RuoYiMonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiResourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-resource" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.resource.RuoYiResourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-system" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.system.RuoYiSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiTestMqApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-test-mq" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.stream.RuoYiTestMqApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiWMSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-wms" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.wms.RuoYiWMSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiWorkflowApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-workflow" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.workflow.RuoYiWorkflowApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SeataServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-seata-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.apache.seata.server.SeataServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SnailJobServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-snailjob-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.snailjob.SnailJobServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-64226f0f93ec-intellij.indexing.shared.core-IU-252.19874.12" />
        <option value="bundled-js-predefined-d6986cc7102b-e0793c8bcbd6-JavaScript-IU-252.19874.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="32cfeba7-8126-42db-8fd1-f8f73c8ab929" name="更改" comment="" />
      <created>1749992467452</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749992467452</updated>
      <workItem from="1749992468739" duration="9397000" />
      <workItem from="1750033039651" duration="3873000" />
      <workItem from="1750037432336" duration="1137000" />
      <workItem from="1750038581052" duration="164000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>