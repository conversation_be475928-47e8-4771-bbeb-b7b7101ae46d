<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="32cfeba7-8126-42db-8fd1-f8f73c8ab929" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yXstcdcnxYUsPzTPNy9RttsaMX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "Spring Boot.DashboardApplication.executor": "Run",
    "Spring Boot.Nacos.executor": "Run",
    "Spring Boot.RuoYiAuthApplication.executor": "Run",
    "Spring Boot.RuoYiDemoApplication.executor": "Run",
    "Spring Boot.RuoYiGatewayApplication.executor": "Run",
    "Spring Boot.RuoYiGenApplication.executor": "Run",
    "Spring Boot.RuoYiJobApplication.executor": "Run",
    "Spring Boot.RuoYiMonitorApplication.executor": "Run",
    "Spring Boot.RuoYiResourceApplication.executor": "Run",
    "Spring Boot.RuoYiSystemApplication.executor": "Run",
    "Spring Boot.RuoYiTestMqApplication.executor": "Run",
    "Spring Boot.RuoYiWMSApplication.executor": "Run",
    "Spring Boot.RuoYiWorkflowApplication.executor": "Run",
    "Spring Boot.SeataServerApplication.executor": "Run",
    "Spring Boot.SnailJobServerApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/kwsywms_test/RuoYi-Cloud-Plus-v2.4.0/ruoyi-modules/ruoyi-wms/src/main/resources",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "问题",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "File.Encoding",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\src\main\resources" />
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-wms\src" />
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\src\main\java\org\dromara\gen\util" />
      <recent name="D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-gen\src\main\resources\vm" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.dromara.wms" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="configurationStatuses">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <map>
              <entry key="DashboardApplication" value="STOPPED" />
              <entry key="Nacos" value="STOPPED" />
              <entry key="RuoYiAuthApplication" value="STOPPED" />
              <entry key="RuoYiDemoApplication" value="STOPPED" />
              <entry key="RuoYiGatewayApplication" value="STOPPED" />
              <entry key="RuoYiGenApplication" value="STOPPED" />
              <entry key="RuoYiJobApplication" value="STOPPED" />
              <entry key="RuoYiMonitorApplication" value="STOPPED" />
              <entry key="RuoYiResourceApplication" value="STOPPED" />
              <entry key="RuoYiSystemApplication" value="STOPPED" />
              <entry key="RuoYiTestMqApplication" value="FAILED" />
              <entry key="RuoYiWMSApplication" value="STOPPED" />
              <entry key="RuoYiWorkflowApplication" value="STOPPED" />
              <entry key="SeataServerApplication" value="STOPPED" />
              <entry key="SnailJobServerApplication" value="STOPPED" />
            </map>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Docker.ruoyi-auth">
    <configuration name="DashboardApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-sentinel-dashboard" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.alibaba.csp.sentinel.dashboard.DashboardApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Nacos" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-nacos" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.alibaba.nacos.Nacos" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.auth.RuoYiAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiDemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-demo" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.demo.RuoYiDemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.gateway.RuoYiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.gen.RuoYiGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiJobApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-job" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.job.RuoYiJobApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiMonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-monitor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.modules.monitor.RuoYiMonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiResourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-resource" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.resource.RuoYiResourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-system" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.system.RuoYiSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiTestMqApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-test-mq" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.stream.RuoYiTestMqApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiWMSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-wms" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.wms.RuoYiWMSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiWorkflowApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-workflow" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.workflow.RuoYiWorkflowApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SeataServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-seata-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.apache.seata.server.SeataServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SnailJobServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-snailjob-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.snailjob.SnailJobServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-64226f0f93ec-intellij.indexing.shared.core-IU-252.19874.12" />
        <option value="bundled-js-predefined-d6986cc7102b-e0793c8bcbd6-JavaScript-IU-252.19874.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="32cfeba7-8126-42db-8fd1-f8f73c8ab929" name="更改" comment="" />
      <created>1749992467452</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749992467452</updated>
      <workItem from="1749992468739" duration="9397000" />
      <workItem from="1750033039651" duration="3117000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>