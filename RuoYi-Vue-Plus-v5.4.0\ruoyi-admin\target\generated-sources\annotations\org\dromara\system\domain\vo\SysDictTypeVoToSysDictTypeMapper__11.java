package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__11.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__11 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
