{"doc": " 响应信息主体\n\n <AUTHOR> Li\n", "fields": [{"name": "SUCCESS", "doc": " 成功\n"}, {"name": "FAIL", "doc": " 失败\n"}, {"name": "code", "doc": " 消息状态码\n"}, {"name": "msg", "doc": " 消息内容\n"}, {"name": "data", "doc": " 数据对象\n"}], "enumConstants": [], "methods": [{"name": "warn", "paramTypes": ["java.lang.String"], "doc": " 返回警告消息\n\n @param msg 返回内容\n @return 警告消息\n"}, {"name": "warn", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 返回警告消息\n\n @param msg 返回内容\n @param data 数据对象\n @return 警告消息\n"}], "constructors": []}