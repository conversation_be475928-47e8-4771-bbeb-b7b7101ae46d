{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useState.js"], "sourcesContent": ["import { ref } from 'vue';\nexport default function useState(defaultStateValue) {\n  const initValue = typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;\n  const innerValue = ref(initValue);\n  function triggerChange(newValue) {\n    innerValue.value = newValue;\n  }\n  return [innerValue, triggerChange];\n}"], "mappings": ";;;;;AACe,SAAR,SAA0B,mBAAmB;AAClD,QAAM,YAAY,OAAO,sBAAsB,aAAa,kBAAkB,IAAI;AAClF,QAAM,aAAa,IAAI,SAAS;AAChC,WAAS,cAAc,UAAU;AAC/B,eAAW,QAAQ;AAAA,EACrB;AACA,SAAO,CAAC,YAAY,aAAa;AACnC;", "names": []}