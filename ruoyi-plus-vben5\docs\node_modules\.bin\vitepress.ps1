#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\kwsywms_test\ruoyi-plus-vben5\node_modules\.pnpm\vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f\node_modules\vitepress\bin\node_modules;D:\kwsywms_test\ruoyi-plus-vben5\node_modules\.pnpm\vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f\node_modules\vitepress\node_modules;D:\kwsywms_test\ruoyi-plus-vben5\node_modules\.pnpm\vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f\node_modules;D:\kwsywms_test\ruoyi-plus-vben5\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules/vitepress/bin/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules/vitepress/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_3453506cf8d8b3d248ed204cba5f6e3f/node_modules:/mnt/d/kwsywms_test/ruoyi-plus-vben5/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../vitepress/bin/vitepress.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../vitepress/bin/vitepress.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../vitepress/bin/vitepress.js" $args
  } else {
    & "node$exe"  "$basedir/../vitepress/bin/vitepress.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
