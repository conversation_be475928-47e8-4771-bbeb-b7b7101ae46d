2025-06-15 21:11:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 21:11:00 [main] INFO  o.d.system.RuoYiSystemApplication - Starting RuoYiSystemApplication using Java 17.0.14 with PID 42460 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-system\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 21:11:00 [main] INFO  o.d.system.RuoYiSystemApplication - The following 1 profile is active: "dev"
2025-06-15 21:11:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-15 21:11:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:11:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:11:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 21:11:07 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 21:11:07 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 21:11:07 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:11:07 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 21:11:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 21:11:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 21:11:08 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5bf9c995
2025-06-15 21:11:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 21:11:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 21:11:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 21:11:09 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 21:11:26 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@54e43bfe
2025-06-15 21:11:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 21:11:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 21:11:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 21:11:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 21:11:29 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.112:9201 register finished
2025-06-15 21:11:32 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:11:35 [main] INFO  o.d.system.RuoYiSystemApplication - Started RuoYiSystemApplication in 38.324 seconds (process running for 39.099)
2025-06-15 21:11:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 21:11:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 21:11:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
2025-06-15 21:15:12 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-15 21:15:13 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:15:13 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:15:13 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[52ms]
2025-06-15 21:15:13 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[66ms]
2025-06-15 21:15:14 [DubboServerHandler-192.168.0.112:20880-thread-7] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:15:14 [DubboServerHandler-192.168.0.112:20880-thread-7] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[23ms]
2025-06-15 21:16:02 [DubboServerHandler-192.168.0.112:20880-thread-8] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:16:02 [DubboServerHandler-192.168.0.112:20880-thread-8] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-06-15 21:16:25 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:25 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:25 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:16:25 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-15 21:16:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-15 21:16:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:17:03 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:17:03 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:17:03 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:17:03 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-06-15 21:17:14 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:17:14 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:17:14 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:17:14 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:17:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:17:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:17:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:17:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:18:12 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:12 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:12 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:18:12 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:18:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:18:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:18:39 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:39 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:39 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:18:39 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:18:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:18:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:18:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:19:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:19:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:19:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:19:26 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:19:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:19:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:19:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:19:40 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:19:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:19:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:19:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:19:56 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:20:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:20:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:20:28 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:20:28 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:20:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:20:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:20:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:20:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:21:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:21:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:21:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:21:04 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:21:22 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:21:22 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:21:22 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:21:22 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:21:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:21:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:21:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:21:44 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:22:01 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:22:01 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:22:01 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:22:01 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 21:22:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:22:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:22:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:22:27 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:22:57 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:22:57 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:22:57 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:22:57 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:23:18 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:23:18 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:23:18 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:23:18 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:23:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:23:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:23:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:23:31 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:23:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:23:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:23:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:23:46 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:24:23 [DubboServerHandler-192.168.0.112:20880-thread-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:23 [DubboServerHandler-192.168.0.112:20880-thread-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:24:28 [DubboServerHandler-192.168.0.112:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:28 [DubboServerHandler-192.168.0.112:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:24:33 [DubboServerHandler-192.168.0.112:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:24:33 [DubboServerHandler-192.168.0.112:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:25:04 [DubboServerHandler-192.168.0.112:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:25:04 [DubboServerHandler-192.168.0.112:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:25:30 [DubboServerHandler-192.168.0.112:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:25:30 [DubboServerHandler-192.168.0.112:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:25:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:25:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:25:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:25:34 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:25:34 [DubboServerHandler-192.168.0.112:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:25:34 [DubboServerHandler-192.168.0.112:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 21:26:01 [DubboServerHandler-192.168.0.112:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:26:01 [DubboServerHandler-192.168.0.112:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:26:04 [DubboServerHandler-192.168.0.112:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:26:04 [DubboServerHandler-192.168.0.112:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 21:26:45 [DubboServerHandler-192.168.0.112:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:26:45 [DubboServerHandler-192.168.0.112:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[12ms]
2025-06-15 21:26:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:26:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:26:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:26:58 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-15 21:26:59 [DubboServerHandler-192.168.0.112:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:26:59 [DubboServerHandler-192.168.0.112:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 21:27:38 [DubboServerHandler-192.168.0.112:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:27:38 [DubboServerHandler-192.168.0.112:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:27:41 [DubboServerHandler-192.168.0.112:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:27:41 [DubboServerHandler-192.168.0.112:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:29:36 [DubboServerHandler-192.168.0.112:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:29:36 [DubboServerHandler-192.168.0.112:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-15 21:29:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:29:38 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:29:39 [DubboServerHandler-192.168.0.112:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:29:39 [DubboServerHandler-192.168.0.112:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 21:29:39 [DubboServerHandler-192.168.0.112:20880-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:29:39 [DubboServerHandler-192.168.0.112:20880-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 21:29:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:29:50 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:29:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:29:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:29:54 [DubboServerHandler-192.168.0.112:20880-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:29:54 [DubboServerHandler-192.168.0.112:20880-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 21:29:54 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:54 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:29:54 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:29:54 [XNIO-1 task-5] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:30:17 [DubboServerHandler-192.168.0.112:20880-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 21:30:17 [DubboServerHandler-192.168.0.112:20880-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 21:49:22 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 21:55:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:55:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:55:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:55:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:55:26 [DubboServerHandler-192.168.0.112:20880-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:55:26 [DubboServerHandler-192.168.0.112:20880-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 21:55:28 [DubboServerHandler-192.168.0.112:20880-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:55:28 [DubboServerHandler-192.168.0.112:20880-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[6ms]
2025-06-15 21:55:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:55:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:55:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:55:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:55:38 [DubboServerHandler-192.168.0.112:20880-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:55:38 [DubboServerHandler-192.168.0.112:20880-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
2025-06-15 21:57:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:57:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 21:57:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 21:57:41 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 21:57:41 [DubboServerHandler-192.168.0.112:20880-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 21:57:41 [DubboServerHandler-192.168.0.112:20880-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[7ms]
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:03:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:06 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:05:07 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:35:27 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-15 22:35:27 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-15 22:35:27 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-15 22:35:27 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-15 22:35:28 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-15 22:35:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-15 22:35:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-15 22:35:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-15 22:35:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-15 22:35:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-15 22:44:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-15 22:44:27 [main] INFO  o.d.system.RuoYiSystemApplication - Starting RuoYiSystemApplication using Java 17.0.14 with PID 51916 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-modules\ruoyi-system\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-15 22:44:27 [main] INFO  o.d.system.RuoYiSystemApplication - The following 1 profile is active: "dev"
2025-06-15 22:44:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-system.yml, group=DEFAULT_GROUP] success
2025-06-15 22:44:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-06-15 22:44:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-15 22:44:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-15 22:44:34 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-15 22:44:34 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-15 22:44:35 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:44:35 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-15 22:44:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-15 22:44:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-15 22:44:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1a2b5f27
2025-06-15 22:44:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-15 22:44:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-15 22:44:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-15 22:44:36 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-15 22:44:54 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5503de1
2025-06-15 22:44:55 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-15 22:44:55 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-15 22:44:55 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-15 22:44:55 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-15 22:44:57 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.112:9201 register finished
2025-06-15 22:45:02 [main] INFO  o.d.system.RuoYiSystemApplication - Started RuoYiSystemApplication in 38.229 seconds (process running for 38.998)
2025-06-15 22:45:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-06-15 22:45:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-15 22:45:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
2025-06-15 22:45:02 [RMI TCP Connection(2)-192.168.0.112] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 22:53:40 [DubboServerHandler-192.168.0.112:20880-thread-8] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:53:40 [DubboServerHandler-192.168.0.112:20880-thread-8] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[257ms]
2025-06-15 22:53:45 [DubboServerHandler-192.168.0.112:20880-thread-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[38ms]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-10] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[77ms]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-11] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-12] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-15 22:53:46 [DubboServerHandler-192.168.0.112:20880-thread-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[27ms]
2025-06-15 22:53:46 [XNIO-1 task-2] INFO  o.a.s.c.l.EnhancedServiceLoader$InnerEnhancedServiceLoader - Load compatible class io.seata.core.context.ContextCore
2025-06-15 22:53:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:53:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:53:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 22:53:47 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-06-15 22:53:48 [DubboServerHandler-192.168.0.112:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:53:48 [DubboServerHandler-192.168.0.112:20880-thread-14] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-15 22:56:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:56:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:56:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 22:56:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-15 22:56:01 [DubboServerHandler-192.168.0.112:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:56:01 [DubboServerHandler-192.168.0.112:20880-thread-15] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[26ms]
2025-06-15 22:57:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:57:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:57:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 22:57:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-15 22:57:28 [DubboServerHandler-192.168.0.112:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:57:28 [DubboServerHandler-192.168.0.112:20880-thread-16] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[13ms]
2025-06-15 22:58:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:58:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:58:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 22:58:00 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-15 22:58:01 [DubboServerHandler-192.168.0.112:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:58:01 [DubboServerHandler-192.168.0.112:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-15 22:58:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:58:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:58:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 22:58:41 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 22:58:41 [DubboServerHandler-192.168.0.112:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:58:41 [DubboServerHandler-192.168.0.112:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[15ms]
2025-06-15 22:59:11 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:59:11 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 22:59:11 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 22:59:11 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 22:59:11 [DubboServerHandler-192.168.0.112:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 22:59:11 [DubboServerHandler-192.168.0.112:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[17ms]
2025-06-15 23:11:57 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:11:57 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:11:57 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:11:57 [XNIO-1 task-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:11:58 [DubboServerHandler-192.168.0.112:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 23:11:58 [DubboServerHandler-192.168.0.112:20880-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[11ms]
2025-06-15 23:12:52 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:12:52 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:12:52 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:12:52 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:12:52 [DubboServerHandler-192.168.0.112:20880-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 23:12:52 [DubboServerHandler-192.168.0.112:20880-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[10ms]
2025-06-15 23:13:31 [DubboServerHandler-192.168.0.112:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:13:31 [DubboServerHandler-192.168.0.112:20880-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[14ms]
2025-06-15 23:13:37 [DubboServerHandler-192.168.0.112:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:13:37 [DubboServerHandler-192.168.0.112:20880-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 23:13:50 [DubboServerHandler-192.168.0.112:20880-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:13:50 [DubboServerHandler-192.168.0.112:20880-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 23:13:53 [DubboServerHandler-192.168.0.112:20880-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:13:53 [DubboServerHandler-192.168.0.112:20880-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[5ms]
2025-06-15 23:13:58 [DubboServerHandler-192.168.0.112:20880-thread-26] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:13:58 [DubboServerHandler-192.168.0.112:20880-thread-26] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[7ms]
2025-06-15 23:13:59 [DubboServerHandler-192.168.0.112:20880-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:13:59 [DubboServerHandler-192.168.0.112:20880-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-06-15 23:14:32 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-06-15 23:14:36 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-06-15 23:14:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:14:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:14:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:14:38 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:14:42 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:14:42 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:14:42 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 23:14:42 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:14:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:14:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:14:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-06-15 23:14:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:15:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:15:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:15:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[12ms]
2025-06-15 23:15:38 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[12ms]
2025-06-15 23:15:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:15:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:15:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 23:15:44 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:15:48 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:15:48 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:15:48 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:15:48 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:15:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:15:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:15:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 23:15:50 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 23:15:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:15:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:15:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 23:15:53 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:16:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:16:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:16:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-06-15 23:16:25 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:16:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:16:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:16:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:16:26 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[8ms]
2025-06-15 23:16:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:16:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:16:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 23:16:28 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-06-15 23:16:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:16:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-06-15 23:16:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:16:40 [XNIO-1 task-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-06-15 23:53:44 [DubboServerHandler-192.168.0.112:20880-thread-28] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-15 23:53:44 [DubboServerHandler-192.168.0.112:20880-thread-28] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-06-15 23:53:44 [DubboServerHandler-192.168.0.112:20880-thread-29] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-15 23:53:44 [DubboServerHandler-192.168.0.112:20880-thread-29] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-15 23:53:44 [DubboServerHandler-192.168.0.112:20880-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 23:53:44 [DubboServerHandler-192.168.0.112:20880-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[9ms]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[25ms]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-34] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-34] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-35] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-35] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[5ms]
2025-06-15 23:53:51 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:53:51 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-06-15 23:53:51 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-06-15 23:53:51 [XNIO-1 task-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-36] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList]
2025-06-15 23:53:51 [DubboServerHandler-192.168.0.112:20880-thread-36] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteTenantService],MethodName=[queryList],SpendTime=[8ms]
