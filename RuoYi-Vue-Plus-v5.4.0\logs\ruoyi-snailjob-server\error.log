2025-06-15 15:55:29 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-dispatcher-42] ERROR c.a.s.s.j.t.s.d.JobExecutorActor - job executor exception. [TaskExecuteDTO(jobId=2, taskBatchId=298, workflowTaskBatchId=null, workflowNodeId=null, taskExecutorScene=1, tmpArgsStr=null)]
java.lang.NullPointerException: Cannot invoke "com.aizuda.snailjob.template.datasource.persistence.po.Job.getGroupName()" because "job" is null
	at com.aizuda.snailjob.server.job.task.support.dispatch.JobExecutorActor.doExecute(JobExecutorActor.java:129)
	at com.aizuda.snailjob.server.job.task.support.dispatch.JobExecutorActor$1.doInTransactionWithoutResult(JobExecutorActor.java:88)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.aizuda.snailjob.server.job.task.support.dispatch.JobExecutorActor.lambda$createReceive$0(JobExecutorActor.java:85)
	at org.apache.pekko.japi.pf.UnitCaseStatement.apply(CaseStatements.scala:33)
	at org.apache.pekko.japi.pf.UnitCaseStatement.apply(CaseStatements.scala:29)
	at scala.PartialFunction.applyOrElse(PartialFunction.scala:214)
	at scala.PartialFunction.applyOrElse$(PartialFunction.scala:213)
	at org.apache.pekko.japi.pf.UnitCaseStatement.applyOrElse(CaseStatements.scala:29)
	at scala.PartialFunction$OrElse.applyOrElse(PartialFunction.scala:269)
	at org.apache.pekko.actor.Actor.aroundReceive(Actor.scala:547)
	at org.apache.pekko.actor.Actor.aroundReceive$(Actor.scala:545)
	at org.apache.pekko.actor.AbstractActor.aroundReceive(AbstractActor.scala:229)
	at org.apache.pekko.actor.ActorCell.receiveMessage(ActorCell.scala:590)
	at org.apache.pekko.actor.ActorCell.invoke(ActorCell.scala:557)
	at org.apache.pekko.dispatch.Mailbox.processMailbox(Mailbox.scala:273)
	at org.apache.pekko.dispatch.Mailbox.run(Mailbox.scala:234)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
