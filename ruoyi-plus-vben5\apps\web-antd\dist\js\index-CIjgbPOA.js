import{c as fe,aJ as me,g as W,aX as Ne,cQ as $e,aM as K,aE as je,_ as P,e as Ae,bd as Ce,cR as we,h as se,bO as He,bk as de,o as Be,cx as Ve,aG as Ge,bQ as Ke,p as We,ck as ze,aH as Ue,b5 as Xe,cS as Qe,cT as qe}from"./bootstrap-DCMzVRvD.js";import{u as Je,T as Ye,i as ge,a as Ze,b as et,f as tt,c as lt,t as nt,B as ot,d as at,e as it,g as st}from"./index-B-GBMyZJ.js";import{L as ut,c as rt}from"./List-DFkqSBvs.js";import{u as ct}from"./useMemo-BwJyMulH.js";import{a4 as dt,a5 as vt,d as Se,B as h,s as ft,q as be,a as L,F as mt,A as Fe,I as xe,C as ie,D as he,$ as J,p as Me}from"../jse/index-index-C-MnMZEz.js";import{g as pt,a as gt}from"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./Overflow-DmNzxpBy.js";import"./index-CHpIOV4R.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CERO2SW5.js";import"./SearchOutlined-BOD_ZIye.js";import"./move-DLDqWE9R.js";import"./slide-B82O6h2Y.js";function bt(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}const De=Symbol("SelectContextKey");function ht(e){return dt(De,e)}function St(){return vt(De,{})}var Ot=function(e,S){var g={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&S.indexOf(a)<0&&(g[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)S.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(g[a[l]]=e[a[l]]);return g};function _e(e){return typeof e=="string"||typeof e=="number"}const yt=Se({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,setup(e,S){let{expose:g,slots:a}=S;const l=Je(),u=St(),f=h(()=>`${l.prefixCls}-item`),v=ct(()=>u.flattenOptions,[()=>l.open,()=>u.flattenOptions],o=>o[0]),p=rt(),b=o=>{o.preventDefault()},m=o=>{p.current&&p.current.scrollTo(typeof o=="number"?{index:o}:o)},y=function(o){let O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const V=v.value.length;for(let d=0;d<V;d+=1){const N=(o+d*O+V)%V,{group:$,data:j}=v.value[N];if(!$&&!j.disabled)return N}return-1},M=ft({activeIndex:y(0)}),x=function(o){let O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;M.activeIndex=o;const V={source:O?"keyboard":"mouse"},d=v.value[o];if(!d){u.onActiveValue(null,-1,V);return}u.onActiveValue(d.value,o,V)};be([()=>v.value.length,()=>l.searchValue],()=>{x(u.defaultActiveFirstOption!==!1?y(0):-1)},{immediate:!0});const I=o=>u.rawValues.has(o)&&l.mode!=="combobox";be([()=>l.open,()=>l.searchValue],()=>{if(!l.multiple&&l.open&&u.rawValues.size===1){const o=Array.from(u.rawValues)[0],O=Fe(v.value).findIndex(V=>{let{data:d}=V;return d[u.fieldNames.value]===o});O!==-1&&(x(O),xe(()=>{m(O)}))}l.open&&xe(()=>{var o;(o=p.current)===null||o===void 0||o.scrollTo(void 0)})},{immediate:!0,flush:"post"});const C=o=>{o!==void 0&&u.onSelect(o,{selected:!u.rawValues.has(o)}),l.multiple||l.toggleOpen(!1)},w=o=>typeof o.label=="function"?o.label():o.label;function T(o){const O=v.value[o];if(!O)return null;const V=O.data||{},{value:d}=V,{group:N}=O,$=$e(V,!0),j=w(O);return O?L("div",W(W({"aria-label":typeof j=="string"&&!N?j:null},$),{},{key:o,role:N?"presentation":"option",id:`${l.id}_list_${o}`,"aria-selected":I(d)}),[d]):null}return g({onKeydown:o=>{const{which:O,ctrlKey:V}=o;switch(O){case K.N:case K.P:case K.UP:case K.DOWN:{let d=0;if(O===K.UP?d=-1:O===K.DOWN?d=1:bt()&&V&&(O===K.N?d=1:O===K.P&&(d=-1)),d!==0){const N=y(M.activeIndex+d,d);m(N),x(N,!0)}break}case K.ENTER:{const d=v.value[M.activeIndex];d&&!d.data.disabled?C(d.value):C(void 0),l.open&&o.preventDefault();break}case K.ESC:l.toggleOpen(!1),l.open&&o.stopPropagation()}},onKeyup:()=>{},scrollTo:o=>{m(o)}}),()=>{const{id:o,notFoundContent:O,onPopupScroll:V}=l,{menuItemSelectedIcon:d,fieldNames:N,virtual:$,listHeight:j,listItemHeight:ne}=u,z=a.option,{activeIndex:R}=M,ue=Object.keys(N).map(D=>N[D]);return v.value.length===0?L("div",{role:"listbox",id:`${o}_list`,class:`${f.value}-empty`,onMousedown:b},[O]):L(mt,null,[L("div",{role:"listbox",id:`${o}_list`,style:{height:0,width:0,overflow:"hidden"}},[T(R-1),T(R),T(R+1)]),L(ut,{itemKey:"key",ref:p,data:v.value,height:j,itemHeight:ne,fullHeight:!1,onMousedown:b,onScroll:V,virtual:$},{default:(D,Z)=>{var ee;const{group:oe,groupOption:re,data:r,value:A}=D,{key:k}=r,B=typeof D.label=="function"?D.label():D.label;if(oe){const le=(ee=r.title)!==null&&ee!==void 0?ee:_e(B)&&B;return L("div",{class:fe(f.value,`${f.value}-group`),title:le},[z?z(r):B!==void 0?B:k])}const{disabled:U,title:ae,children:te,style:ce,class:t,className:n}=r,i=Ot(r,["disabled","title","children","style","class","className"]),c=me(i,ue),s=I(A),_=`${f.value}-option`,X=fe(f.value,_,t,n,{[`${_}-grouped`]:re,[`${_}-active`]:R===Z&&!U,[`${_}-disabled`]:U,[`${_}-selected`]:s}),H=w(D),G=!d||typeof d=="function"||s,F=typeof H=="number"?H:H||A;let ve=_e(F)?F.toString():void 0;return ae!==void 0&&(ve=ae),L("div",W(W({},c),{},{"aria-selected":s,class:X,title:ve,onMousemove:le=>{i.onMousemove&&i.onMousemove(le),!(R===Z||U)&&x(Z)},onClick:le=>{U||C(A),i.onClick&&i.onClick(le)},style:ce}),[L("div",{class:`${_}-content`},[z?z(r):F]),Ne(d)||s,G&&L(Ye,{class:`${f.value}-option-state`,customizeIcon:d,customizeIconProps:{isSelected:s}},{default:()=>[s?"✓":null]})])}})])}}});var It=function(e,S){var g={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&S.indexOf(a)<0&&(g[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)S.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(g[a[l]]=e[a[l]]);return g};function Ct(e){const S=e,{key:g,children:a}=S,l=S.props,{value:u,disabled:f}=l,v=It(l,["value","disabled"]),p=a==null?void 0:a.default;return P({key:g,value:u!==void 0?u:g,children:p,disabled:f||f===""},v)}function Te(e){let S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return je(e).map((a,l)=>{var u;if(!Ne(a)||!a.type)return null;const{type:{isSelectOptGroup:f},key:v,children:p,props:b}=a;if(S||!f)return Ct(a);const m=p&&p.default?p.default():void 0,y=(b==null?void 0:b.label)||((u=p.label)===null||u===void 0?void 0:u.call(p))||v;return P(P({key:`__RC_SELECT_GRP__${v===null?l:String(v)}__`},b),{label:y,options:Te(m||[])})}).filter(a=>a)}function wt(e,S,g){const a=ie(),l=ie(),u=ie(),f=ie([]);return be([e,S],()=>{e.value?f.value=Fe(e.value).slice():f.value=Te(S.value)},{immediate:!0,deep:!0}),he(()=>{const v=f.value,p=new Map,b=new Map,m=g.value;function y(M){let x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(let I=0;I<M.length;I+=1){const C=M[I];!C[m.options]||x?(p.set(C[m.value],C),b.set(C[m.label],C)):y(C[m.options],!0)}}y(v),a.value=v,l.value=p,u.value=b}),{options:a,valueOptions:l,labelOptions:u}}function Ee(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}function pe(e,S){return Ee(e).join("").toUpperCase().includes(S)}const Vt=(e,S,g,a,l)=>h(()=>{const u=g.value,f=l==null?void 0:l.value,v=a==null?void 0:a.value;if(!u||v===!1)return e.value;const{options:p,label:b,value:m}=S.value,y=[],M=typeof v=="function",x=u.toUpperCase(),I=M?v:(w,T)=>f?pe(T[f],x):T[p]?pe(T[b!=="children"?b:"label"],x):pe(T[m],x),C=M?w=>ge(w):w=>w;return e.value.forEach(w=>{if(w[p]){if(I(u,C(w)))y.push(w);else{const Y=w[p].filter(q=>I(u,C(q)));Y.length&&y.push(P(P({},w),{[p]:Y}))}return}I(u,C(w))&&y.push(w)}),y}),xt=(e,S)=>{const g=ie({values:new Map,options:new Map});return[h(()=>{const{values:u,options:f}=g.value,v=e.value.map(m=>{var y;return m.label===void 0?P(P({},m),{label:(y=u.get(m.value))===null||y===void 0?void 0:y.label}):m}),p=new Map,b=new Map;return v.forEach(m=>{p.set(m.value,m),b.set(m.value,S.value.get(m.value)||f.get(m.value))}),g.value.values=p,g.value.options=b,v}),u=>S.value.get(u)||g.value.options.get(u)]},_t=["inputValue"];function Re(){return P(P({},at()),{prefixCls:String,id:String,backfill:{type:Boolean,default:void 0},fieldNames:Object,inputValue:String,searchValue:String,onSearch:Function,autoClearSearchValue:{type:Boolean,default:void 0},onSelect:Function,onDeselect:Function,filterOption:{type:[Boolean,Function],default:void 0},filterSort:Function,optionFilterProp:String,optionLabelProp:String,options:Array,defaultActiveFirstOption:{type:Boolean,default:void 0},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,menuItemSelectedIcon:se.any,mode:String,labelInValue:{type:Boolean,default:void 0},value:se.any,defaultValue:se.any,onChange:Function,children:Array})}function Pt(e){return!e||typeof e!="object"}const Nt=Se({compatConfig:{MODE:3},name:"VcSelect",inheritAttrs:!1,props:Ae(Re(),{prefixCls:"vc-select",autoClearSearchValue:!0,listHeight:200,listItemHeight:20,dropdownMatchSelectWidth:!0}),setup(e,S){let{expose:g,attrs:a,slots:l}=S;const u=Ze(J(e,"id")),f=h(()=>et(e.mode)),v=h(()=>!!(!e.options&&e.children)),p=h(()=>e.filterOption===void 0&&e.mode==="combobox"?!1:e.filterOption),b=h(()=>tt(e.fieldNames,v.value)),[m,y]=Ce("",{value:h(()=>e.searchValue!==void 0?e.searchValue:e.inputValue),postState:t=>t||""}),M=wt(J(e,"options"),J(e,"children"),b),{valueOptions:x,labelOptions:I,options:C}=M,w=t=>Ee(t).map(i=>{var c,s;let _,X,H,G;Pt(i)?_=i:(H=i.key,X=i.label,_=(c=i.value)!==null&&c!==void 0?c:H);const F=x.value.get(_);return F&&(X===void 0&&(X=F==null?void 0:F[e.optionLabelProp||b.value.label]),H===void 0&&(H=(s=F==null?void 0:F.key)!==null&&s!==void 0?s:_),G=F==null?void 0:F.disabled),{label:X,value:_,key:H,disabled:G,option:F}}),[T,Y]=Ce(e.defaultValue,{value:J(e,"value")}),q=h(()=>{var t;const n=w(T.value);return e.mode==="combobox"&&!(!((t=n[0])===null||t===void 0)&&t.value)?[]:n}),[E,o]=xt(q,x),O=h(()=>{if(!e.mode&&E.value.length===1){const t=E.value[0];if(t.value===null&&(t.label===null||t.label===void 0))return[]}return E.value.map(t=>{var n;return P(P({},t),{label:(n=typeof t.label=="function"?t.label():t.label)!==null&&n!==void 0?n:t.value})})}),V=h(()=>new Set(E.value.map(t=>t.value)));he(()=>{var t;if(e.mode==="combobox"){const n=(t=E.value[0])===null||t===void 0?void 0:t.value;n!=null&&y(String(n))}},{flush:"post"});const d=(t,n)=>{const i=n!=null?n:t;return{[b.value.value]:t,[b.value.label]:i}},N=ie();he(()=>{if(e.mode!=="tags"){N.value=C.value;return}const t=C.value.slice(),n=i=>x.value.has(i);[...E.value].sort((i,c)=>i.value<c.value?-1:1).forEach(i=>{const c=i.value;n(c)||t.push(d(c,i.label))}),N.value=t});const $=Vt(N,b,m,p,J(e,"optionFilterProp")),j=h(()=>e.mode!=="tags"||!m.value||$.value.some(t=>t[e.optionFilterProp||"value"]===m.value)?$.value:[d(m.value),...$.value]),ne=h(()=>e.filterSort?[...j.value].sort((t,n)=>e.filterSort(t,n)):j.value),z=h(()=>lt(ne.value,{fieldNames:b.value,childrenAsData:v.value})),R=t=>{const n=w(t);if(Y(n),e.onChange&&(n.length!==E.value.length||n.some((i,c)=>{var s;return((s=E.value[c])===null||s===void 0?void 0:s.value)!==(i==null?void 0:i.value)}))){const i=e.labelInValue?n.map(s=>P(P({},s),{originLabel:s.label,label:typeof s.label=="function"?s.label():s.label})):n.map(s=>s.value),c=n.map(s=>ge(o(s.value)));e.onChange(f.value?i:i[0],f.value?c:c[0])}},[ue,D]=we(null),[Z,ee]=we(0),oe=h(()=>e.defaultActiveFirstOption!==void 0?e.defaultActiveFirstOption:e.mode!=="combobox"),re=function(t,n){let{source:i="keyboard"}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};ee(n),e.backfill&&e.mode==="combobox"&&t!==null&&i==="keyboard"&&D(String(t))},r=(t,n)=>{const i=()=>{var c;const s=o(t),_=s==null?void 0:s[b.value.label];return[e.labelInValue?{label:typeof _=="function"?_():_,originLabel:_,value:t,key:(c=s==null?void 0:s.key)!==null&&c!==void 0?c:t}:t,ge(s)]};if(n&&e.onSelect){const[c,s]=i();e.onSelect(c,s)}else if(!n&&e.onDeselect){const[c,s]=i();e.onDeselect(c,s)}},A=(t,n)=>{let i;const c=f.value?n.selected:!0;c?i=f.value?[...E.value,t]:[t]:i=E.value.filter(s=>s.value!==t),R(i),r(t,c),e.mode==="combobox"?D(""):(!f.value||e.autoClearSearchValue)&&(y(""),D(""))},k=(t,n)=>{R(t),(n.type==="remove"||n.type==="clear")&&n.values.forEach(i=>{r(i.value,!1)})},B=(t,n)=>{var i;if(y(t),D(null),n.source==="submit"){const c=(t||"").trim();if(c){const s=Array.from(new Set([...V.value,c]));R(s),r(c,!0),y("")}return}n.source!=="blur"&&(e.mode==="combobox"&&R(t),(i=e.onSearch)===null||i===void 0||i.call(e,t))},U=t=>{let n=t;e.mode!=="tags"&&(n=t.map(c=>{const s=I.value.get(c);return s==null?void 0:s.value}).filter(c=>c!==void 0));const i=Array.from(new Set([...V.value,...n]));R(i),i.forEach(c=>{r(c,!0)})},ae=h(()=>e.virtual!==!1&&e.dropdownMatchSelectWidth!==!1);ht(nt(P(P({},M),{flattenOptions:z,onActiveValue:re,defaultActiveFirstOption:oe,onSelect:A,menuItemSelectedIcon:J(e,"menuItemSelectedIcon"),rawValues:V,fieldNames:b,virtual:ae,listHeight:J(e,"listHeight"),listItemHeight:J(e,"listItemHeight"),childrenAsData:v})));const te=Me();g({focus(){var t;(t=te.value)===null||t===void 0||t.focus()},blur(){var t;(t=te.value)===null||t===void 0||t.blur()},scrollTo(t){var n;(n=te.value)===null||n===void 0||n.scrollTo(t)}});const ce=h(()=>me(e,["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","listHeight","listItemHeight","value","defaultValue","labelInValue","onChange"]));return()=>L(ot,W(W(W({},ce.value),a),{},{id:u,prefixCls:e.prefixCls,ref:te,omitDomProps:_t,mode:e.mode,displayValues:O.value,onDisplayValuesChange:k,searchValue:m.value,onSearch:B,onSearchSplit:U,dropdownMatchSelectWidth:e.dropdownMatchSelectWidth,OptionList:yt,emptyOptions:!z.value.length,activeValue:ue.value,activeDescendantId:`${u}_list_${Z.value}`}),l)}}),Oe=()=>null;Oe.isSelectOption=!0;Oe.displayName="ASelectOption";const ye=()=>null;ye.isSelectOptGroup=!0;ye.displayName="ASelectOptGroup";const At=()=>P(P({},me(Re(),["inputIcon","mode","getInputElement","getRawInputElement","backfill"])),{value:Ve([Array,Object,String,Number]),defaultValue:Ve([Array,Object,String,Number]),notFoundContent:se.any,suffixIcon:se.any,itemIcon:se.any,size:de(),mode:de(),bordered:Be(!0),transitionName:String,choiceTransitionName:de(""),popupClassName:String,dropdownClassName:String,placement:de(),status:de(),"onUpdate:value":He()}),Pe="SECRET_COMBOBOX_MODE_DO_NOT_USE",Q=Se({compatConfig:{MODE:3},name:"ASelect",Option:Oe,OptGroup:ye,inheritAttrs:!1,props:Ae(At(),{listHeight:256,listItemHeight:24}),SECRET_COMBOBOX_MODE_DO_NOT_USE:Pe,slots:Object,setup(e,S){let{attrs:g,emit:a,slots:l,expose:u}=S;const f=Me(),v=Ge(),p=Ke.useInject(),b=h(()=>pt(p.status,e.status)),m=()=>{var r;(r=f.value)===null||r===void 0||r.focus()},y=()=>{var r;(r=f.value)===null||r===void 0||r.blur()},M=r=>{var A;(A=f.value)===null||A===void 0||A.scrollTo(r)},x=h(()=>{const{mode:r}=e;if(r!=="combobox")return r===Pe?"combobox":r}),{prefixCls:I,direction:C,renderEmpty:w,size:T,getPrefixCls:Y,getPopupContainer:q,disabled:E,select:o}=We("select",e),{compactSize:O,compactItemClassnames:V}=ze(I,C),d=h(()=>O.value||T.value),N=Ue(),$=h(()=>{var r;return(r=E.value)!==null&&r!==void 0?r:N.value}),[j,ne]=it(I),z=h(()=>Y()),R=h(()=>e.placement!==void 0?e.placement:C.value==="rtl"?"bottomRight":"bottomLeft"),ue=h(()=>Xe(z.value,Qe(R.value),e.transitionName)),D=h(()=>fe({[`${I.value}-lg`]:d.value==="large",[`${I.value}-sm`]:d.value==="small",[`${I.value}-rtl`]:C.value==="rtl",[`${I.value}-borderless`]:!e.bordered,[`${I.value}-in-form-item`]:p.isFormItemInput},gt(I.value,b.value,p.hasFeedback),V.value,ne.value)),Z=function(){for(var r=arguments.length,A=new Array(r),k=0;k<r;k++)A[k]=arguments[k];a("update:value",A[0]),a("change",...A),v.onFieldChange()},ee=r=>{a("blur",r),v.onFieldBlur()};u({blur:y,focus:m,scrollTo:M});const oe=h(()=>x.value==="multiple"||x.value==="tags"),re=h(()=>e.showArrow!==void 0?e.showArrow:e.loading||!(oe.value||x.value==="combobox"));return()=>{var r,A,k,B;const{notFoundContent:U,listHeight:ae=256,listItemHeight:te=24,popupClassName:ce,dropdownClassName:t,virtual:n,dropdownMatchSelectWidth:i,id:c=v.id.value,placeholder:s=(r=l.placeholder)===null||r===void 0?void 0:r.call(l),showArrow:_}=e,{hasFeedback:X,feedbackIcon:H}=p;let G;U!==void 0?G=U:l.notFoundContent?G=l.notFoundContent():x.value==="combobox"?G=null:G=(w==null?void 0:w("Select"))||L(qe,{componentName:"Select"},null);const{suffixIcon:F,itemIcon:ve,removeIcon:le,clearIcon:ke}=st(P(P({},e),{multiple:oe.value,prefixCls:I.value,hasFeedback:X,feedbackIcon:H,showArrow:re.value}),l),Ie=me(e,["prefixCls","suffixIcon","itemIcon","removeIcon","clearIcon","size","bordered","status"]),Le=fe(ce||t,{[`${I.value}-dropdown-${C.value}`]:C.value==="rtl"},ne.value);return j(L(Nt,W(W(W({ref:f,virtual:n,dropdownMatchSelectWidth:i},Ie),g),{},{showSearch:(A=e.showSearch)!==null&&A!==void 0?A:(k=o==null?void 0:o.value)===null||k===void 0?void 0:k.showSearch,placeholder:s,listHeight:ae,listItemHeight:te,mode:x.value,prefixCls:I.value,direction:C.value,inputIcon:F,menuItemSelectedIcon:ve,removeIcon:le,clearIcon:ke,notFoundContent:G,class:[D.value,g.class],getPopupContainer:q==null?void 0:q.value,dropdownClassName:Le,onChange:Z,onBlur:ee,id:c,dropdownRender:Ie.dropdownRender||l.dropdownRender,transitionName:ue.value,children:(B=l.default)===null||B===void 0?void 0:B.call(l),tagRender:e.tagRender||l.tagRender,optionLabelRender:l.optionLabel,maxTagPlaceholder:e.maxTagPlaceholder||l.maxTagPlaceholder,showArrow:X||_,disabled:$.value}),{option:l.option}))}}});Q.install=function(e){return e.component(Q.name,Q),e.component(Q.Option.displayName,Q.Option),e.component(Q.OptGroup.displayName,Q.OptGroup),e};const zt=Q.Option,Ut=Q.OptGroup;export{Ut as SelectOptGroup,zt as SelectOption,Q as default,At as selectProps};
