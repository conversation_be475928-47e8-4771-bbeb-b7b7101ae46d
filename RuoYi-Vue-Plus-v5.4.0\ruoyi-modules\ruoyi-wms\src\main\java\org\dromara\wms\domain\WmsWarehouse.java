package org.dromara.wms.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 仓库列表对象 wms_warehouse
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_warehouse")
public class WmsWarehouse extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 仓库id
     */
    @TableId(value = "warehouse_id")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    private String warehouseNumber;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）
     */
    private String warehouseType;

    /**
     * 库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
     */
    private String warehouseInventoryStatus;

    /**
     * 收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
     */
    private String warehouseRecevingStatus;

    /**
     * 备注
     */
    private String remark;

}
