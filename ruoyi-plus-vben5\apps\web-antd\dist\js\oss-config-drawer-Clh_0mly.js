var p=(e,t,n)=>new Promise((l,f)=>{var m=o=>{try{d(n.next(o))}catch(r){f(r)}},s=o=>{try{d(n.throw(o))}catch(r){f(r)}},d=o=>o.done?l(o.value):Promise.resolve(o.value).then(m,s);d((n=n.apply(e,t)).next())});import{y as u,al as D,ar as P,$ as C,aj as S,z as k}from"./bootstrap-DCMzVRvD.js";import{u as q,d as h}from"./popup-D6rC6QBG.js";import{g as x}from"./dict-BLkXAGS5.js";import{T as B}from"./index-B6iusSRX.js";import{a as b,d as K,p as V,B as L,P as O,h as T,o as $,b as g,w,j as z}from"../jse/index-index-C-MnMZEz.js";import{u as A}from"./use-drawer-6qcpK-D1.js";import{A as F}from"./index-kC0HFDdy.js";function oe(e){return u.get("/resource/oss/config/list",{params:e})}function G(e){return u.get(`/resource/oss/config/${e}`)}function M(e){return u.postWithMsg("/resource/oss/config",e)}function W(e){return u.putWithMsg("/resource/oss/config",e)}function ne(e){return u.deleteWithMsg(`/resource/oss/config/${e}`)}function se(e){const t={ossConfigId:e.ossConfigId,status:e.status,configKey:e.configKey};return u.putWithMsg("/resource/oss/config/changeStatus",t)}const y=[{color:"orange",label:"私有",value:"0"},{color:"green",label:"公开",value:"1"},{color:"blue",label:"自定义",value:"2"}],ae=()=>[{component:"Input",fieldName:"configKey",label:"配置名称"},{component:"Input",fieldName:"bucketName",label:"桶名称"},{component:"Select",componentProps:{options:[{label:"是",value:"0"},{label:"否",value:"1"}]},fieldName:"status",label:"是否默认"}],le=[{type:"checkbox",width:60},{title:"配置名称",field:"configKey"},{title:"访问站点",field:"endpoint",showOverflow:!0},{title:"桶名称",field:"bucketName"},{title:"域",field:"region"},{title:"权限桶类型",field:"accessPolicy",slots:{default:({row:e})=>{const t=y.find(n=>n.value===e.accessPolicy);return t?b(B,{color:t.color},{default:()=>[t.label]}):"未知类型"}}},{title:"是否默认",field:"status",slots:{default:"status"}},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],j=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"ossConfigId"},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider1",hideLabel:!0,renderComponentContent:()=>({default:()=>"基本信息"})},{component:"Input",fieldName:"configKey",label:"配置名称",rules:"required"},{component:"Input",fieldName:"endpoint",label:"服务地址",renderComponentContent:e=>({addonBefore:()=>e.isHttps==="Y"?"https://":"http://"}),rules:D().refine(e=>e&&!/^https?:\/\/.*/.test(e),{message:"请输入正确的域名, 不需要http(s)"})},{component:"Input",fieldName:"domain",label:"自定义域名"},{component:"Input",fieldName:"tip",label:"占位作为提示使用",hideLabel:!0},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider2",hideLabel:!0,renderComponentContent:()=>({default:()=>"认证信息"})},{component:"Input",fieldName:"accessKey",label:"accessKey",rules:"required"},{component:"Input",fieldName:"secretKey",label:"secretKey",rules:"required"},{component:"Divider",componentProps:{orientation:"center"},fieldName:"divider3",hideLabel:!0,renderComponentContent:()=>({default:()=>"其他信息"})},{component:"Input",fieldName:"bucketName",label:"桶名称",rules:"required"},{component:"Input",fieldName:"prefix",label:"前缀"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:y,optionType:"button"},defaultValue:"0",fieldName:"accessPolicy",formItemClass:"col-span-3 lg:col-span-2",label:"权限桶类型"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:x(P.SYS_YES_NO),optionType:"button"},defaultValue:"N",fieldName:"isHttps",formItemClass:"col-span-3 lg:col-span-1",label:"是否https",rules:"required"},{component:"Input",fieldName:"region",label:"区域"},{component:"Textarea",fieldName:"remark",formItemClass:"items-start",label:"备注"}],R={class:"ml-7 w-full"},Y=K({__name:"oss-config-drawer",emits:["reload"],setup(e,{emit:t}){const n=t,l=V(!1),f=L(()=>l.value?C("pages.common.edit"):C("pages.common.add")),[m,s]=S({commonConfig:{formItemClass:"col-span-3",labelWidth:100},schema:j(),showDefaultActions:!1,wrapperClass:"grid-cols-3"}),{onBeforeClose:d,markInitialized:o,resetInitialized:r}=q({initializedGetter:h(s),currentGetter:h(s)}),[N,i]=A({onBeforeClose:d,onClosed:I,onConfirm:v,onOpenChange(c){return p(this,null,function*(){if(!c)return null;i.drawerLoading(!0);const{id:a}=i.getData();if(l.value=!!a,l.value&&a){const _=yield G(a);yield s.setValues(_)}yield o(),i.drawerLoading(!1)})}});function v(){return p(this,null,function*(){try{i.lock(!0);const{valid:c}=yield s.validate();if(!c)return;const a=O(yield s.getValues());yield l.value?W(a):M(a),r(),n("reload"),i.close()}catch(c){console.error(c)}finally{i.lock(!1)}})}function I(){return p(this,null,function*(){yield s.resetForm(),r()})}return(c,a)=>($(),T(g(N),{title:f.value,class:"w-[650px]"},{default:w(()=>[b(g(m),null,{tip:w(()=>[z("div",R,[b(g(F),{message:"私有桶使用自定义域名无法预览, 但可以正常上传/下载","show-icon":"",type:"warning"})])]),_:1})]),_:1},8,["title"]))}}),E=k(Y,[["__scopeId","data-v-e5b35095"]]),re=Object.freeze(Object.defineProperty({__proto__:null,default:E},Symbol.toStringTag,{value:"Module"}));export{se as a,oe as b,le as c,ne as d,re as e,E as o,ae as q};
