package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__12;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__12;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOperLogBoToSysOperLogMapper__12.class,SysOperLogBoToOperLogEventMapper__12.class},
    imports = {}
)
public interface OperLogEventToSysOperLogBoMapper__12 extends BaseMapper<OperLogEvent, SysOperLogBo> {
}
