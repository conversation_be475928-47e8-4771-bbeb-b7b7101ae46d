{"doc": " spring-cache 演示案例\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "test1", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试 @Cacheable\n <p>\n 表示这个方法有了缓存的功能,方法的返回值会被缓存下来\n 下一次调用该方法前,会去检查是否缓存中已经有值\n 如果有就直接返回,不调用方法\n 如果没有,就调用方法,然后把结果缓存起来\n 这个注解「一般用在查询方法上」\n <p>\n 重点说明: 缓存注解严谨与其他筛选数据功能一起使用\n 例如: 数据权限注解 会造成 缓存击穿 与 数据不一致问题\n <p>\n cacheNames 命名规则 查看 {@link CacheNames} 注释 支持多参数\n"}, {"name": "test2", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试 @CachePut\n <p>\n 加了@CachePut注解的方法,会把方法的返回值put到缓存里面缓存起来,供其它地方使用\n 它「通常用在新增或者实时更新方法上」\n <p>\n cacheNames 命名规则 查看 {@link CacheNames} 注释 支持多参数\n"}, {"name": "test3", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试 @CacheEvict\n <p>\n 使用了CacheEvict注解的方法,会清空指定缓存\n 「一般用在删除的方法上」\n <p>\n cacheNames 命名规则 查看 {@link CacheNames} 注释 支持多参数\n"}, {"name": "test6", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试设置过期时间\n 手动设置过期时间10秒\n 11秒后获取 判断是否相等\n"}], "constructors": []}