var T=(c,l,a)=>new Promise((i,s)=>{var d=e=>{try{u(a.next(e))}catch(m){s(m)}},r=e=>{try{u(a.throw(e))}catch(m){s(m)}},u=e=>e.done?i(e.value):Promise.resolve(e.value).then(d,r);u((a=a.apply(c,l)).next())});import{br as S,aB as $,$ as n,aN as v,al as P}from"./bootstrap-DCMzVRvD.js";import{T as V}from"./auth-title-CzJiGVH3.js";import{d as B,s as C,B as _,c as F,o as k,a as p,j as N,w as h,r as w,k as g,t as b,b as o,n as x,p as y,h as A}from"../jse/index-index-C-MnMZEz.js";const L=B({name:"ForgetPassword",__name:"forget-password",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(c,{expose:l,emit:a}){const i=c,s=a,[d,r]=S(C({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:_(()=>i.formSchema),showDefaultActions:!1})),u=$();function e(){return T(this,null,function*(){const{valid:t}=yield r.validate(),f=yield r.getValues();t&&s("submit",f)})}function m(){u.push(i.loginPath)}return l({getFormApi:()=>r}),(t,f)=>(k(),F("div",null,[p(V,null,{desc:h(()=>[w(t.$slots,"subTitle",{},()=>[g(b(t.subTitle||o(n)("authentication.forgetPasswordSubtitle")),1)])]),default:h(()=>[w(t.$slots,"title",{},()=>[g(b(t.title||o(n)("authentication.forgetPassword"))+" 🤦🏻‍♂️ ",1)])]),_:3}),p(o(d)),N("div",null,[p(o(v),{class:x([{"cursor-wait":t.loading},"mt-2 w-full"]),"aria-label":"submit",onClick:e},{default:h(()=>[w(t.$slots,"submitButtonText",{},()=>[g(b(t.submitButtonText||o(n)("authentication.sendResetLink")),1)])]),_:3},8,["class"]),p(o(v),{class:"mt-4 w-full",variant:"outline",onClick:f[0]||(f[0]=R=>m())},{default:h(()=>[g(b(o(n)("common.back")),1)]),_:1})])]))}}),z=B({name:"ForgetPassword",__name:"forget-password",setup(c){const l=y(!1),a=_(()=>[{component:"VbenInput",componentProps:{placeholder:"<EMAIL>"},fieldName:"email",label:n("authentication.email"),rules:P().min(1,{message:n("authentication.emailTip")}).email(n("authentication.emailValidErrorTip"))}]);function i(s){console.log("reset email:",s)}return(s,d)=>(k(),A(o(L),{"form-schema":a.value,loading:l.value,onSubmit:i},null,8,["form-schema","loading"]))}});export{z as default};
