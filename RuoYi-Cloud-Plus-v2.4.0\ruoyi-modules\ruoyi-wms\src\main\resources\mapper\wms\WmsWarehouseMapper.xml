<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.wms.mapper.WmsWarehouseMapper">

    <!-- 查询仓库关联的部门ID列表 -->
    <select id="selectDeptIdsByWarehouseId" parameterType="Long" resultType="Long">
        SELECT dept_id
        FROM wms_warehouse_dept
        WHERE warehouse_id = #{warehouseId}
    </select>

    <!-- 查询仓库关联的部门名称 -->
    <select id="selectDeptNamesByWarehouseId" parameterType="Long" resultType="String">
        SELECT GROUP_CONCAT(d.dept_name)
        FROM wms_warehouse_dept wd
        LEFT JOIN sys_dept d ON wd.dept_id = d.dept_id
        WHERE wd.warehouse_id = #{warehouseId}
    </select>

    <!-- 查询用户可访问的仓库列表（基于用户部门权限） -->
    <select id="selectUserAccessibleWarehouses" parameterType="Long" resultType="org.dromara.wms.domain.vo.WmsWarehouseVo">
        SELECT DISTINCT w.warehouse_id, w.warehouse_name, w.warehouse_number
        FROM wms_warehouse w
        INNER JOIN wms_warehouse_dept wd ON w.warehouse_id = wd.warehouse_id
        WHERE wd.dept_id IN (
            -- 用户直接关联的部门
            SELECT u.dept_id
            FROM sys_user u
            WHERE u.user_id = #{userId}
            AND u.del_flag = '0'
            AND u.dept_id IS NOT NULL

            UNION

            -- 用户通过角色关联的部门
            SELECT rd.dept_id
            FROM sys_user_role ur
            INNER JOIN sys_role_dept rd ON ur.role_id = rd.role_id
            WHERE ur.user_id = #{userId}
        )
        ORDER BY w.warehouse_number
    </select>

</mapper>
