var Le=Object.defineProperty,Ne=Object.defineProperties;var je=Object.getOwnPropertyDescriptors;var K=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var Y=(o,t,s)=>t in o?Le(o,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[t]=s,y=(o,t)=>{for(var s in t||(t={}))ne.call(t,s)&&Y(o,s,t[s]);if(K)for(var s of K(t))le.call(t,s)&&Y(o,s,t[s]);return o},T=(o,t)=>Ne(o,je(t));var E=(o,t)=>{var s={};for(var a in o)ne.call(o,a)&&t.indexOf(a)<0&&(s[a]=o[a]);if(o!=null&&K)for(var a of K(o))t.indexOf(a)<0&&le.call(o,a)&&(s[a]=o[a]);return s};var R=(o,t,s)=>Y(o,typeof t!="symbol"?t+"":t,s);var U=(o,t,s)=>new Promise((a,c)=>{var r=b=>{try{_(s.next(b))}catch(i){c(i)}},v=b=>{try{_(s.throw(b))}catch(i){c(i)}},_=b=>b.done?a(b.value):Promise.resolve(b.value).then(r,v);_((s=s.apply(o,t)).next())});import{d as C,B as I,h as p,o as d,b as e,O as H,G as O,w as f,c as J,f as g,t as P,n as A,e as ye,g as he,r as h,a5 as be,p as V,a as x,H as We,ay as Ke,q as Ue,j as Z,k as M,L as re,a4 as _e,ah as He,ai as Je,s as Xe,az as qe,M as ie,I as ge}from"../jse/index-index-C-MnMZEz.js";import{d5 as Ge,cc as Ye,bG as ve,cV as Ze,cY as Qe,cW as et,cX as tt,a_ as st,cZ as ot,c_ as at,c$ as nt,d0 as lt,d1 as rt,d2 as it,co as dt,d3 as ct,bC as de,cq as ut,d4 as ce,cs as pt,aN as ue,cn as ft,ct as mt}from"./bootstrap-DCMzVRvD.js";import{X as pe}from"./x-Bfkqqjgb.js";const yt=C({__name:"Separator",props:{class:{},label:{},orientation:{},decorative:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(o){const t=o,s=I(()=>{const r=t,{class:a}=r;return E(r,["class"])});return(a,c)=>(d(),p(e(Ge),H(s.value,{class:e(O)("bg-border relative shrink-0",t.orientation==="vertical"?"h-full w-px":"h-px w-full",t.class)}),{default:f(()=>[t.label?(d(),J("span",{key:0,class:A(e(O)("text-muted-foreground bg-background absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 items-center justify-center text-xs",t.orientation==="vertical"?"w-[1px] px-1 py-2":"h-[1px] px-2 py-1"))},P(t.label),3)):g("",!0)]),_:1},16,["class"]))}}),ht=Ye("bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),bt=C({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:t}){const c=ve(o,t);return(r,v)=>(d(),p(e(Ze),ye(he(e(c))),{default:f(()=>[h(r.$slots,"default")]),_:3},16))}}),fe=C({__name:"SheetClose",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(o){const t=o;return(s,a)=>(d(),p(e(Qe),ye(he(t)),{default:f(()=>[h(s.$slots,"default")]),_:3},16))}}),_t=["data-dismissable-drawer"],gt=C({__name:"SheetOverlay",setup(o){et();const t=be("DISMISSABLE_DRAWER_ID");return(s,a)=>(d(),J("div",{"data-dismissable-drawer":e(t),class:"bg-overlay z-popup inset-0"},null,8,_t))}}),vt=C({inheritAttrs:!1,__name:"SheetContent",props:{appendTo:{default:"body"},class:{},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},side:{},zIndex:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(o,{emit:t}){const s=o,a=t,c=I(()=>{const $=s,{class:l,modal:m,open:u,side:B}=$;return E($,["class","modal","open","side"])});function r(){return s.appendTo==="body"||s.appendTo===document.body||!s.appendTo}const v=I(()=>r()?"fixed":"absolute"),_=ve(c,a),b=V(null);function i(l){var m;l.target===((m=b.value)==null?void 0:m.$el)&&(s.open?a("opened"):a("closed"))}return(l,m)=>(d(),p(e(tt),{to:l.appendTo},{default:f(()=>[x(st,{name:"fade"},{default:f(()=>[l.open&&l.modal?(d(),p(gt,{key:0,style:We(T(y({},l.zIndex?{zIndex:l.zIndex}:{}),{position:v.value,backdropFilter:l.overlayBlur&&l.overlayBlur>0?`blur(${l.overlayBlur}px)`:"none"}))},null,8,["style"])):g("",!0)]),_:1}),x(e(ot),H({ref_key:"contentRef",ref:b,class:e(O)("z-popup",e(ht)({side:l.side}),s.class),style:T(y({},l.zIndex?{zIndex:l.zIndex}:{}),{position:v.value}),onAnimationend:i},y(y({},e(_)),l.$attrs)),{default:f(()=>[h(l.$slots,"default")]),_:3},16,["class","style"])]),_:3},8,["to"]))}}),Q=C({__name:"SheetDescription",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(o){const t=o,s=I(()=>{const r=t,{class:a}=r;return E(r,["class"])});return(a,c)=>(d(),p(e(at),H({class:e(O)("text-muted-foreground text-sm",t.class)},s.value),{default:f(()=>[h(a.$slots,"default")]),_:3},16,["class"]))}}),wt=C({__name:"SheetFooter",props:{class:{}},setup(o){const t=o;return(s,a)=>(d(),J("div",{class:A(e(O)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[h(s.$slots,"default")],2))}}),Ct=C({__name:"SheetHeader",props:{class:{}},setup(o){const t=o;return(s,a)=>(d(),J("div",{class:A(e(O)("flex flex-col text-center sm:text-left",t.class))},[h(s.$slots,"default")],2))}}),ee=C({__name:"SheetTitle",props:{class:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(o){const t=o,s=I(()=>{const r=t,{class:a}=r;return E(r,["class"])});return(a,c)=>(d(),p(e(nt),H({class:e(O)("text-foreground font-medium",t.class)},s.value),{default:f(()=>[h(a.$slots,"default")]),_:3},16,["class"]))}}),Ot={class:"flex items-center"},Bt={class:"flex-center"},St=C({__name:"drawer",props:{drawerApi:{default:void 0},appendToMain:{type:Boolean,default:!1},cancelText:{},class:{},closable:{type:Boolean},closeIconPlacement:{default:"right"},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},placement:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean,default:!1},title:{},titleTooltip:{},zIndex:{default:1e3}},setup(o){var j,ae;const t=o,s=lt.getComponents(),a=Ke();_e("DISMISSABLE_DRAWER_ID",a);const c=V(),{$t:r}=rt(),{isMobile:v}=it(),_=(ae=(j=t.drawerApi)==null?void 0:j.useStore)==null?void 0:ae.call(j),{appendToMain:b,cancelText:i,class:l,closable:m,closeIconPlacement:u,closeOnClickModal:B,closeOnPressEscape:D,confirmLoading:$,confirmText:X,contentClass:we,description:L,destroyOnClose:Ce,footer:Oe,footerClass:Be,header:Se,headerClass:xe,loading:te,modal:De,openAutoFocus:ke,overlayBlur:$e,placement:z,showCancelButton:Ae,showConfirmButton:Ie,submitting:S,title:N,titleTooltip:se,zIndex:Te}=dt(t,_);function Ee(n){(!B.value||S.value)&&n.preventDefault()}function Pe(n){(!D.value||S.value)&&n.preventDefault()}function ze(n){const w=n.target,W=w==null?void 0:w.dataset.dismissableDrawer;(S.value||!B.value||W!==a)&&n.preventDefault()}function Fe(n){ke.value||n==null||n.preventDefault()}function oe(n){n.preventDefault(),n.stopPropagation()}const Re=I(()=>b.value?`#${ct}>div:not(.absolute)>div`:void 0),q=V(!1),G=V(!0);Ue(()=>{var n;return(n=_==null?void 0:_.value)==null?void 0:n.isOpen},n=>{G.value=!1,n&&!e(q)&&(q.value=!0)});function Me(){var n;G.value=!0,(n=t.drawerApi)==null||n.onClosed()}const Ve=I(()=>!e(Ce)&&e(q));return(n,w)=>{var W;return d(),p(e(bt),{modal:!1,open:(W=e(_))==null?void 0:W.isOpen,"onUpdate:open":w[3]||(w[3]=()=>{var F;return(F=n.drawerApi)==null?void 0:F.close()})},{default:f(()=>{var F;return[x(e(vt),{"append-to":Re.value,class:A(e(O)("flex w-[520px] flex-col",e(l),{"!w-full":e(v)||e(z)==="bottom"||e(z)==="top","max-h-[100vh]":e(z)==="bottom"||e(z)==="top",hidden:G.value})),modal:e(De),open:(F=e(_))==null?void 0:F.isOpen,side:e(z),"z-index":e(Te),"force-mount":Ve.value,"overlay-blur":e($e),onCloseAutoFocus:oe,onClosed:Me,onEscapeKeyDown:Pe,onFocusOutside:oe,onInteractOutside:Ee,onOpenAutoFocus:Fe,onOpened:w[2]||(w[2]=()=>{var k;return(k=n.drawerApi)==null?void 0:k.onOpened()}),onPointerDownOutside:ze},{default:f(()=>[e(Se)?(d(),p(e(Ct),{key:0,class:A(e(O)("!flex flex-row items-center justify-between border-b px-6 py-5",e(xe),{"px-4 py-3":e(m),"pl-2":e(m)&&e(u)==="left"}))},{default:f(()=>[Z("div",Ot,[e(m)&&e(u)==="left"?(d(),p(e(fe),{key:0,"as-child":"",disabled:e(S),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:f(()=>[h(n.$slots,"close-icon",{},()=>[x(e(de),null,{default:f(()=>[x(e(pe),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):g("",!0),e(m)&&e(u)==="left"?(d(),p(e(yt),{key:1,class:"ml-1 mr-2 h-8",decorative:"",orientation:"vertical"})):g("",!0),e(N)?(d(),p(e(ee),{key:2,class:"text-left"},{default:f(()=>[h(n.$slots,"title",{},()=>[M(P(e(N))+" ",1),e(se)?(d(),p(e(ut),{key:0,"trigger-class":"pb-1"},{default:f(()=>[M(P(e(se)),1)]),_:1})):g("",!0)])]),_:3})):g("",!0),e(L)?(d(),p(e(Q),{key:3,class:"mt-1 text-xs"},{default:f(()=>[h(n.$slots,"description",{},()=>[M(P(e(L)),1)])]),_:3})):g("",!0)]),!e(N)||!e(L)?(d(),p(e(ce),{key:0},{default:f(()=>[e(N)?g("",!0):(d(),p(e(ee),{key:0})),e(L)?g("",!0):(d(),p(e(Q),{key:1}))]),_:1})):g("",!0),Z("div",Bt,[h(n.$slots,"extra"),e(m)&&e(u)==="right"?(d(),p(e(fe),{key:0,"as-child":"",disabled:e(S),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:f(()=>[h(n.$slots,"close-icon",{},()=>[x(e(de),null,{default:f(()=>[x(e(pe),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):g("",!0)])]),_:3},8,["class"])):(d(),p(e(ce),{key:1},{default:f(()=>[x(e(ee)),x(e(Q))]),_:1})),Z("div",{ref_key:"wrapperRef",ref:c,class:A(e(O)("relative flex-1 overflow-y-auto p-3",e(we),{"pointer-events-none":e(te)||e(S)}))},[h(n.$slots,"default")],2),e(te)||e(S)?(d(),p(e(pt),{key:2,spinning:""})):g("",!0),e(Oe)?(d(),p(e(wt),{key:3,class:A(e(O)("w-full flex-row items-center justify-end border-t p-2 px-3",e(Be)))},{default:f(()=>[h(n.$slots,"prepend-footer"),h(n.$slots,"footer",{},()=>[e(Ae)?(d(),p(re(e(s).DefaultButton||e(ue)),{key:0,variant:"ghost",disabled:e(S),onClick:w[0]||(w[0]=()=>{var k;return(k=n.drawerApi)==null?void 0:k.onCancel()})},{default:f(()=>[h(n.$slots,"cancelText",{},()=>[M(P(e(i)||e(r)("cancel")),1)])]),_:3},8,["disabled"])):g("",!0),h(n.$slots,"center-footer"),e(Ie)?(d(),p(re(e(s).PrimaryButton||e(ue)),{key:1,loading:e($)||e(S),onClick:w[1]||(w[1]=()=>{var k;return(k=n.drawerApi)==null?void 0:k.onConfirm()})},{default:f(()=>[h(n.$slots,"confirmText",{},()=>[M(P(e(X)||e(r)("confirm")),1)])]),_:3},8,["loading"])):g("",!0)]),h(n.$slots,"append-footer")]),_:3},8,["class"])):g("",!0)]),_:3},8,["append-to","class","modal","open","side","z-index","force-mount","overlay-blur"])]}),_:3},8,["open"])}}});class xt{constructor(t={}){R(this,"sharedData",{payload:{}});R(this,"store");R(this,"api");R(this,"state");const m=t,{connectedComponent:s,onBeforeClose:a,onCancel:c,onClosed:r,onConfirm:v,onOpenChange:_,onOpened:b}=m,i=E(m,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),l={class:"",closable:!0,closeIconPlacement:"right",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,placement:"right",showCancelButton:!0,showConfirmButton:!0,submitting:!1,title:""};this.store=new ft(y(y({},l),i),{onUpdate:()=>{var B,D,$;const u=this.store.state;(u==null?void 0:u.isOpen)===((B=this.state)==null?void 0:B.isOpen)?this.state=u:(this.state=u,($=(D=this.api).onOpenChange)==null||$.call(D,!!(u!=null&&u.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:a,onCancel:c,onClosed:r,onConfirm:v,onOpenChange:_,onOpened:b},He(this)}close(){return U(this,null,function*(){var s,a,c;((c=yield(a=(s=this.api).onBeforeClose)==null?void 0:a.call(s))!=null?c:!0)&&this.store.setState(r=>T(y({},r),{isOpen:!1,submitting:!1}))})}drawerLoading(t){this.setState({confirmLoading:t,loading:t})}getData(){var t,s;return(s=(t=this.sharedData)==null?void 0:t.payload)!=null?s:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,s;this.api.onCancel?(s=(t=this.api).onCancel)==null||s.call(t):this.close()}onClosed(){var t,s;this.state.isOpen||(s=(t=this.api).onClosed)==null||s.call(t)}onConfirm(){var t,s;(s=(t=this.api).onConfirm)==null||s.call(t)}onOpened(){var t,s;this.state.isOpen&&((s=(t=this.api).onOpened)==null||s.call(t))}open(){this.store.setState(t=>T(y({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return Je(t)?this.store.setState(t):this.store.setState(s=>y(y({},s),t)),this}unlock(){return this.lock(!1)}}const me=Symbol("VBEN_DRAWER_INJECT"),Dt={};function Et(o={}){var b;const{connectedComponent:t}=o;if(t){const i=Xe({}),l=V(!0),m=C((u,{attrs:B,slots:D})=>(_e(me,{extendApi(X){Object.setPrototypeOf(i,X)},options:o,reCreateDrawer(){return U(this,null,function*(){l.value=!1,yield ge(),l.value=!0})}}),kt(i,y(y(y({},u),B),D)),()=>ie(l.value?t:"div",y(y({},u),B),D)),{name:"VbenParentDrawer",inheritAttrs:!1});return qe(()=>{var u;(u=i==null?void 0:i.close)==null||u.call(i)}),[m,i]}const s=be(me,{}),a=y(y(y({},Dt),s.options),o);a.onOpenChange=i=>{var l,m,u;(l=o.onOpenChange)==null||l.call(o,i),(u=(m=s.options)==null?void 0:m.onOpenChange)==null||u.call(m,i)};const c=a.onClosed;a.onClosed=()=>{var i;c==null||c(),a.destroyOnClose&&((i=s.reCreateDrawer)==null||i.call(s))};const r=new xt(a),v=r;v.useStore=i=>mt(r.store,i);const _=C((i,{attrs:l,slots:m})=>()=>ie(St,T(y(y({},i),l),{drawerApi:v}),m),{name:"VbenDrawer",inheritAttrs:!1});return(b=s.extendApi)==null||b.call(s,v),[_,v]}function kt(o,t){return U(this,null,function*(){var c;if(!t||Object.keys(t).length===0)return;yield ge();const s=(c=o==null?void 0:o.store)==null?void 0:c.state;if(!s)return;const a=new Set(Object.keys(s));for(const r of Object.keys(t))a.has(r)&&!["class"].includes(r)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${r}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}export{Et as u};
