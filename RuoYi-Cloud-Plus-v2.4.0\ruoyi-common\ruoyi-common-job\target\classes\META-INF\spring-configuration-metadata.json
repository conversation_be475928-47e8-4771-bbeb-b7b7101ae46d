{"groups": [{"name": "snail-job.server", "type": "org.dromara.common.job.config.properties.SnailJobServerProperties", "sourceType": "org.dromara.common.job.config.properties.SnailJobServerProperties"}], "properties": [{"name": "snail-job.server.port", "type": "java.lang.String", "sourceType": "org.dromara.common.job.config.properties.SnailJobServerProperties"}, {"name": "snail-job.server.server-name", "type": "java.lang.String", "sourceType": "org.dromara.common.job.config.properties.SnailJobServerProperties"}], "hints": []}