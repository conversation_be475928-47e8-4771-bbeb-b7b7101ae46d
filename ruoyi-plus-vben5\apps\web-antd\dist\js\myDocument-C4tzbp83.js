var A=Object.defineProperty,H=Object.defineProperties;var q=Object.getOwnPropertyDescriptors;var B=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable;var F=(p,r,e)=>r in p?A(p,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):p[r]=e,k=(p,r)=>{for(var e in r||(r={}))G.call(r,e)&&F(p,e,r[e]);if(B)for(var e of B(r))K.call(r,e)&&F(p,e,r[e]);return p},N=(p,r)=>H(p,q(r));var C=(p,r,e)=>new Promise((g,d)=>{var c=m=>{try{i(e.next(m))}catch(b){d(b)}},x=m=>{try{i(e.throw(m))}catch(b){d(b)}},i=m=>m.done?g(m.value):Promise.resolve(m.value).then(c,x);i((e=e.apply(p,r)).next())});import{b as D}from"./index-DCFckLr6.js";import{au as $,aw as L,bL as J,T as Q,aq as W,bn as S,z as X}from"./bootstrap-DCMzVRvD.js";import Y from"./approval-card-6R4jdcAw.js";import{_ as Z}from"./approval-panel.vue_vue_type_script_setup_true_lang-B_m5j5Mw.js";import"./vxe-table-DzEj5Fop.js";/* empty css                                                          */import{b as tt,R as et,F as ot}from"./constant-BxyJFj0E.js";import"./index-BxBCzu2M.js";import{a as at,I as P}from"./Search-ClCped_G.js";import{S as lt}from"./index-Ollxi7Rl.js";import{_ as rt}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{d as it,p as v,B as st,E as pt,v as nt,l as mt,h as T,o as _,w as s,j as u,a as l,b as a,k as j,c as E,f as z,F as ut,K as ft,t as dt}from"../jse/index-index-C-MnMZEz.js";import{u as ct}from"./use-tabs-Zz_nc_n2.js";import{P as vt}from"./index-qvRUEWLR.js";import{a as gt}from"./get-popup-container-P4S1sr5h.js";import"./render-BxXtQdeV.js";import"./index-C0wIoq37.js";import"./index-D6-099PU.js";import"./index-DjJOU2eu.js";import"./data-1kX019oc.js";import"./index-B6iusSRX.js";import"./dict-BLkXAGS5.js";import"./helper-Bc7QQ92Q.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./approval-card.vue_vue_type_style_index_0_scoped_90981e93_lang-CCcVSBpc.js";import"./relativeTime-DEqmspR6.js";import"./approval-content.vue_vue_type_script_setup_true_lang-55IpV6QI.js";import"./index-D59rZjD-.js";import"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";import"./statusUtils-d85DZFMd.js";import"./BaseInput-B4f3ADM3.js";import"./css-Dmgy8YJo.js";import"./index-CFj2VWFk.js";import"./SearchOutlined-BOD_ZIye.js";import"./index-CHpIOV4R.js";import"./index-CZhogUxH.js";import"./approval-modal.vue_vue_type_script_setup_true_lang-Cy95Z7DQ.js";import"./copy-component.vue_vue_type_script_setup_true_lang-dAYgXBvO.js";import"./user-select-modal-9zMWfXzj.js";import"./index-ocPq22VW.js";import"./dept-tree.vue_vue_type_script_setup_true_lang-D8f_s4jU.js";import"./index-BLwHKR_M.js";import"./SyncOutlined-GoH9kFJY.js";import"./index-DdFaZtFR.js";import"./index-BM5PBg44.js";import"./List-DFkqSBvs.js";import"./eagerComputed-CeBU4kWY.js";import"./index-DabkQ3D7.js";import"./use-vxe-grid-BC7vZzEr.js";import"./init-C8TKSdFQ.js";import"./use-modal-CeMSCP2m.js";import"./x-Bfkqqjgb.js";import"./index-BY49C_DM.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-BELOxkuV.js";import"./approval-details.vue_vue_type_script_setup_true_lang-DSKkMZbu.js";import"./approval-timeline.vue_vue_type_script_setup_true_lang-D4RprojW.js";import"./approval-timeline-item.vue_vue_type_script_setup_true_lang-C59mxgG1.js";import"./index-i2_yEmR1.js";import"./flow-preview.vue_vue_type_script_setup_true_lang-GVKtAqck.js";import"./approval-rejection-modal.vue_vue_type_script_setup_true_lang-DmK-Yc2B.js";import"./flow-interfere-modal.vue_vue_type_script_setup_true_lang-COIbtjES.js";import"./index-C1KbofmV.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BeyziwLP.js";import"./index-BIMmoqOy.js";import"./move-DLDqWE9R.js";import"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import"./rotate-cw-DzZTu9nW.js";const bt={class:"flex h-full gap-2"},_t={class:"bg-background relative flex h-full min-w-[320px] max-w-[320px] flex-col rounded-lg"},yt={class:"bg-background z-100 sticky left-0 top-0 w-full rounded-t-lg border-b-[1px] border-solid p-2"},kt={class:"flex items-center gap-1"},xt={class:"flex"},wt={key:2,class:"flex items-center justify-center text-[14px] opacity-50"},Ct={key:3,class:"absolute left-0 top-0 flex h-full w-full items-center justify-center bg-[rgba(0,0,0,0.1)]"},ht={class:"bg-background sticky bottom-0 w-full rounded-b-lg border-t-[1px] py-2"},Nt={class:"flex items-center justify-center"},St=it({__name:"myDocument",setup(p){const r=$.PRESENTED_IMAGE_SIMPLE,e=v([]),g=v(0),d=v(1),c=v(!1),x={flowName:"",nodeName:"",flowCode:"",category:null},i=v(L(x)),m=st(()=>e.value.length===g.value),b=pt("cardContainerRef");function y(f=!1){return C(this,null,function*(){var n;(n=b.value)==null||n.scroll({top:0,behavior:"auto"}),d.value=1,w.value=void 0,g.value=0,h.value="",f&&(i.value=L(x)),c.value=!0;const t=yield D(k({pageSize:10,pageNum:d.value},i.value));if(e.value=t.rows.map(o=>N(k({},o),{active:!1})),g.value=t.total,c.value=!1,e.value.length>0){const o=e.value[0];w.value=o,R(o)}})}nt(y);const I=J(f=>C(null,null,function*(){if(!f.target)return;const{scrollTop:t,clientHeight:n,scrollHeight:o}=f.target;if(t+n>=o-tt&&!m.value){c.value=!0,d.value+=1;const O=yield D(k({pageSize:10,pageNum:d.value},i.value));e.value.push(...O.rows.map(U=>N(k({},U),{active:!1}))),c.value=!1}}),200),h=v(""),w=v();function R(f){return C(this,null,function*(){const{id:t}=f;h.value!==t&&(w.value=f,e.value.forEach(n=>{n.active=n.id===t}),h.value=t)})}const{refreshTab:V}=ct();return(f,t)=>{const n=mt("a-button");return _(),T(a(rt),{"auto-content-height":!0},{default:s(()=>[u("div",bt,[u("div",_t,[u("div",yt,[u("div",kt,[l(a(at),{value:i.value.flowName,"onUpdate:value":t[0]||(t[0]=o=>i.value.flowName=o),placeholder:"流程名称搜索",onSearch:t[1]||(t[1]=o=>y(!1))},null,8,["value"]),l(a(Q),{placement:"top",title:"重置"},{default:s(()=>[l(n,{onClick:t[2]||(t[2]=o=>y(!0))},{default:s(()=>[l(a(et))]),_:1})]),_:1}),l(a(vt),{"get-popup-container":a(gt),placement:"rightTop",trigger:"click"},{title:s(()=>t[8]||(t[8]=[u("div",{class:"w-full border-b pb-[12px] text-[16px]"},"搜索",-1)])),content:s(()=>[l(a(W),{colon:!1,"label-col":{span:6},model:i.value,autocomplete:"off",class:"w-[300px]",onFinish:t[6]||(t[6]=()=>y(!1))},{default:s(()=>[l(a(S),{label:"任务名称"},{default:s(()=>[l(a(P),{value:i.value.nodeName,"onUpdate:value":t[3]||(t[3]=o=>i.value.nodeName=o),placeholder:"请输入"},null,8,["value"])]),_:1}),l(a(S),{label:"流程编码"},{default:s(()=>[l(a(P),{value:i.value.flowCode,"onUpdate:value":t[4]||(t[4]=o=>i.value.flowCode=o),placeholder:"请输入"},null,8,["value"])]),_:1}),l(a(S),null,{default:s(()=>[u("div",xt,[l(n,{block:"","html-type":"submit",type:"primary"},{default:s(()=>t[9]||(t[9]=[j(" 搜索 ")])),_:1,__:[9]}),l(n,{block:"",class:"ml-2",onClick:t[5]||(t[5]=o=>y(!0))},{default:s(()=>t[10]||(t[10]=[j(" 重置 ")])),_:1,__:[10]})])]),_:1})]),_:1},8,["model"])]),default:s(()=>[l(n,null,{default:s(()=>[l(a(ot))]),_:1})]),_:1},8,["get-popup-container"])])]),u("div",{ref_key:"cardContainerRef",ref:b,class:"thin-scrollbar flex flex-1 flex-col gap-2 overflow-y-auto py-3",onScroll:t[7]||(t[7]=(...o)=>a(I)&&a(I)(...o))},[e.value.length>0?(_(!0),E(ut,{key:0},ft(e.value,o=>(_(),T(a(Y),{key:o.id,info:o,class:"mx-2",onClick:M=>R(o)},null,8,["info","onClick"]))),128)):(_(),T(a($),{key:1,image:a(r)},null,8,["image"])),m.value&&e.value.length>0?(_(),E("div",wt," 没有更多数据了 ")):z("",!0),c.value?(_(),E("div",Ct,[l(a(lt),{tip:"加载中..."})])):z("",!0)],544),u("div",ht,[u("div",Nt," 共 "+dt(g.value)+" 条记录 ",1)])]),l(a(Z),{task:w.value,type:"myself",onReload:a(V)},null,8,["task","onReload"])])]),_:1})}}}),Xe=X(St,[["__scopeId","data-v-70147c01"]]);export{Xe as default};
