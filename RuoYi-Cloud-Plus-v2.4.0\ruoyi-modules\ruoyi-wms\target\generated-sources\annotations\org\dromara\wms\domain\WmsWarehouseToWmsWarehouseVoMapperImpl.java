package org.dromara.wms.domain;

import javax.annotation.processing.Generated;
import org.dromara.wms.domain.vo.WmsWarehouseVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T10:21:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class WmsWarehouseToWmsWarehouseVoMapperImpl implements WmsWarehouseToWmsWarehouseVoMapper {

    @Override
    public WmsWarehouseVo convert(WmsWarehouse arg0) {
        if ( arg0 == null ) {
            return null;
        }

        WmsWarehouseVo wmsWarehouseVo = new WmsWarehouseVo();

        wmsWarehouseVo.setWarehouseId( arg0.getWarehouseId() );
        wmsWarehouseVo.setWarehouseNumber( arg0.getWarehouseNumber() );
        wmsWarehouseVo.setWarehouseName( arg0.getWarehouseName() );
        wmsWarehouseVo.setWarehouseType( arg0.getWarehouseType() );
        wmsWarehouseVo.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        wmsWarehouseVo.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        wmsWarehouseVo.setRemark( arg0.getRemark() );

        return wmsWarehouseVo;
    }

    @Override
    public WmsWarehouseVo convert(WmsWarehouse arg0, WmsWarehouseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setWarehouseId( arg0.getWarehouseId() );
        arg1.setWarehouseNumber( arg0.getWarehouseNumber() );
        arg1.setWarehouseName( arg0.getWarehouseName() );
        arg1.setWarehouseType( arg0.getWarehouseType() );
        arg1.setWarehouseInventoryStatus( arg0.getWarehouseInventoryStatus() );
        arg1.setWarehouseRecevingStatus( arg0.getWarehouseRecevingStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
