{"doc": " 业务 数据层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectGenTableAll", "paramTypes": [], "doc": " 查询所有表信息\n\n @return 表信息集合\n"}, {"name": "selectGenTableById", "paramTypes": ["java.lang.Long"], "doc": " 查询表ID业务信息\n\n @param id 业务ID\n @return 业务信息\n"}, {"name": "selectGenTableByName", "paramTypes": ["java.lang.String"], "doc": " 查询表名称业务信息\n\n @param tableName 表名称\n @return 业务信息\n"}, {"name": "selectTableNameList", "paramTypes": ["java.lang.String"], "doc": " 查询指定数据源下的所有表名列表\n\n @param dataName 数据源名称，用于选择不同的数据源\n @return 当前数据库中的表名列表\n\n @DS(\"\") 使用默认数据源执行查询操作\n"}], "constructors": []}