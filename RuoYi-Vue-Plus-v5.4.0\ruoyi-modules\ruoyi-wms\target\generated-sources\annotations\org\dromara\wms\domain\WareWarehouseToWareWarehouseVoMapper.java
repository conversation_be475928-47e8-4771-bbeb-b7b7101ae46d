package org.dromara.wms.domain;

import io.github.linpeilie.AutoMapperConfig__698;
import io.github.linpeilie.BaseMapper;
import org.dromara.wms.domain.bo.WareWarehouseBoToWareWarehouseMapper;
import org.dromara.wms.domain.vo.WareWarehouseVo;
import org.dromara.wms.domain.vo.WareWarehouseVoToWareWarehouseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__698.class,
    uses = {WareWarehouseVoToWareWarehouseMapper.class,WareWarehouseBoToWareWarehouseMapper.class},
    imports = {}
)
public interface WareWarehouseToWareWarehouseVoMapper extends BaseMapper<WareWarehouse, WareWarehouseVo> {
}
