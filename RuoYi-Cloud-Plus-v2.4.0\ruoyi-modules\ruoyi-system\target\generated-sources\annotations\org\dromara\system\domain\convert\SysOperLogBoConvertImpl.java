package org.dromara.system.domain.convert;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.bo.RemoteOperLogBo;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T10:28:19+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Oracle Corporation)"
)
@Component
public class SysOperLogBoConvertImpl implements SysOperLogBoConvert {

    @Override
    public SysOperLogBo convert(RemoteOperLogBo arg0, SysOperLogBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setOperId( arg0.getOperId() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setBusinessType( arg0.getBusinessType() );
        arg1.setMethod( arg0.getMethod() );
        arg1.setRequestMethod( arg0.getRequestMethod() );
        arg1.setOperatorType( arg0.getOperatorType() );
        arg1.setOperName( arg0.getOperName() );
        arg1.setDeptName( arg0.getDeptName() );
        arg1.setOperUrl( arg0.getOperUrl() );
        arg1.setOperIp( arg0.getOperIp() );
        arg1.setOperLocation( arg0.getOperLocation() );
        arg1.setOperParam( arg0.getOperParam() );
        arg1.setJsonResult( arg0.getJsonResult() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setErrorMsg( arg0.getErrorMsg() );
        arg1.setOperTime( arg0.getOperTime() );
        arg1.setCostTime( arg0.getCostTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }

        return arg1;
    }

    @Override
    public SysOperLogBo convert(RemoteOperLogBo remoteOperLogBo) {
        if ( remoteOperLogBo == null ) {
            return null;
        }

        SysOperLogBo sysOperLogBo = new SysOperLogBo();

        sysOperLogBo.setOperId( remoteOperLogBo.getOperId() );
        sysOperLogBo.setTenantId( remoteOperLogBo.getTenantId() );
        sysOperLogBo.setTitle( remoteOperLogBo.getTitle() );
        sysOperLogBo.setBusinessType( remoteOperLogBo.getBusinessType() );
        sysOperLogBo.setMethod( remoteOperLogBo.getMethod() );
        sysOperLogBo.setRequestMethod( remoteOperLogBo.getRequestMethod() );
        sysOperLogBo.setOperatorType( remoteOperLogBo.getOperatorType() );
        sysOperLogBo.setOperName( remoteOperLogBo.getOperName() );
        sysOperLogBo.setDeptName( remoteOperLogBo.getDeptName() );
        sysOperLogBo.setOperUrl( remoteOperLogBo.getOperUrl() );
        sysOperLogBo.setOperIp( remoteOperLogBo.getOperIp() );
        sysOperLogBo.setOperLocation( remoteOperLogBo.getOperLocation() );
        sysOperLogBo.setOperParam( remoteOperLogBo.getOperParam() );
        sysOperLogBo.setJsonResult( remoteOperLogBo.getJsonResult() );
        sysOperLogBo.setStatus( remoteOperLogBo.getStatus() );
        sysOperLogBo.setErrorMsg( remoteOperLogBo.getErrorMsg() );
        sysOperLogBo.setOperTime( remoteOperLogBo.getOperTime() );
        sysOperLogBo.setCostTime( remoteOperLogBo.getCostTime() );
        Map<String, Object> map = remoteOperLogBo.getParams();
        if ( map != null ) {
            sysOperLogBo.setParams( new LinkedHashMap<String, Object>( map ) );
        }

        return sysOperLogBo;
    }
}
