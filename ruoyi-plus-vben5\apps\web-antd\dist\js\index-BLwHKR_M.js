import{c as g,j as V,m as Q,_ as l,K as U,e as N,p as C,g as S,aJ as W}from"./bootstrap-DCMzVRvD.js";import{d as h,a as r,B as A}from"../jse/index-index-C-MnMZEz.js";const Y=()=>({prefixCls:String,width:{type:[Number,String]}}),O=h({compatConfig:{MODE:3},name:"SkeletonTitle",props:Y(),setup(e){return()=>{const{prefixCls:t,width:n}=e,o=typeof n=="number"?`${n}px`:n;return r("h3",{class:t,style:{width:o}},null)}}}),Z=()=>({prefixCls:String,width:{type:[Number,String,Array]},rows:Number}),ee=h({compatConfig:{MODE:3},name:"SkeletonParagraph",props:Z(),setup(e){const t=n=>{const{width:o,rows:a=2}=e;if(Array.isArray(o))return o[n];if(a-1===n)return o};return()=>{const{prefixCls:n,rows:o}=e,a=[...Array(o)].map((i,s)=>{const d=t(s);return r("li",{key:s,style:{width:typeof d=="number"?`${d}px`:d}},null)});return r("ul",{class:n},[a])}}}),P=()=>({prefixCls:String,size:[String,Number],shape:String,active:{type:Boolean,default:void 0}}),w=e=>{const{prefixCls:t,size:n,shape:o}=e,a=g({[`${t}-lg`]:n==="large",[`${t}-sm`]:n==="small"}),i=g({[`${t}-circle`]:o==="circle",[`${t}-square`]:o==="square",[`${t}-round`]:o==="round"}),s=typeof n=="number"?{width:`${n}px`,height:`${n}px`,lineHeight:`${n}px`}:{};return r("span",{class:g(t,a,i),style:s},null)};w.displayName="SkeletonElement";const te=new U("ant-skeleton-loading",{"0%":{transform:"translateX(-37.5%)"},"100%":{transform:"translateX(37.5%)"}}),E=e=>({height:e,lineHeight:`${e}px`}),k=e=>l({width:e},E(e)),ne=e=>({position:"relative",zIndex:0,overflow:"hidden",background:"transparent","&::after":{position:"absolute",top:0,insetInlineEnd:"-150%",bottom:0,insetInlineStart:"-150%",background:e.skeletonLoadingBackground,animationName:te,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite",content:'""'}}),T=e=>l({width:e*5,minWidth:e*5},E(e)),oe=e=>{const{skeletonAvatarCls:t,color:n,controlHeight:o,controlHeightLG:a,controlHeightSM:i}=e;return{[`${t}`]:l({display:"inline-block",verticalAlign:"top",background:n},k(o)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:l({},k(a)),[`${t}${t}-sm`]:l({},k(i))}},le=e=>{const{controlHeight:t,borderRadiusSM:n,skeletonInputCls:o,controlHeightLG:a,controlHeightSM:i,color:s}=e;return{[`${o}`]:l({display:"inline-block",verticalAlign:"top",background:s,borderRadius:n},T(t)),[`${o}-lg`]:l({},T(a)),[`${o}-sm`]:l({},T(i))}},G=e=>l({width:e},E(e)),ae=e=>{const{skeletonImageCls:t,imageSizeBase:n,color:o,borderRadiusSM:a}=e;return{[`${t}`]:l(l({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:o,borderRadius:a},G(n*2)),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:l(l({},G(n)),{maxWidth:n*4,maxHeight:n*4}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},L=(e,t,n)=>{const{skeletonButtonCls:o}=e;return{[`${n}${o}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${n}${o}-round`]:{borderRadius:t}}},q=e=>l({width:e*2,minWidth:e*2},E(e)),re=e=>{const{borderRadiusSM:t,skeletonButtonCls:n,controlHeight:o,controlHeightLG:a,controlHeightSM:i,color:s}=e;return l(l(l(l(l({[`${n}`]:l({display:"inline-block",verticalAlign:"top",background:s,borderRadius:t,width:o*2,minWidth:o*2},q(o))},L(e,o,n)),{[`${n}-lg`]:l({},q(a))}),L(e,a,`${n}-lg`)),{[`${n}-sm`]:l({},q(i))}),L(e,i,`${n}-sm`))},ie=e=>{const{componentCls:t,skeletonAvatarCls:n,skeletonTitleCls:o,skeletonParagraphCls:a,skeletonButtonCls:i,skeletonInputCls:s,skeletonImageCls:d,controlHeight:H,controlHeightLG:b,controlHeightSM:$,color:p,padding:M,marginSM:R,borderRadius:u,skeletonTitleHeight:v,skeletonBlockRadius:m,skeletonParagraphLineHeight:f,controlHeightXS:B,skeletonParagraphMarginTop:x}=e;return{[`${t}`]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:M,verticalAlign:"top",[`${n}`]:l({display:"inline-block",verticalAlign:"top",background:p},k(H)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:l({},k(b)),[`${n}-sm`]:l({},k($))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[`${o}`]:{width:"100%",height:v,background:p,borderRadius:m,[`+ ${a}`]:{marginBlockStart:$}},[`${a}`]:{padding:0,"> li":{width:"100%",height:f,listStyle:"none",background:p,borderRadius:m,"+ li":{marginBlockStart:B}}},[`${a}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${o}, ${a} > li`]:{borderRadius:u}}},[`${t}-with-avatar ${t}-content`]:{[`${o}`]:{marginBlockStart:R,[`+ ${a}`]:{marginBlockStart:x}}},[`${t}${t}-element`]:l(l(l(l({display:"inline-block",width:"auto"},re(e)),oe(e)),le(e)),ae(e)),[`${t}${t}-block`]:{width:"100%",[`${i}`]:{width:"100%"},[`${s}`]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${o},
        ${a} > li,
        ${n},
        ${i},
        ${s},
        ${d}
      `]:l({},ne(e))}}},y=V("Skeleton",e=>{const{componentCls:t}=e,n=Q(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:e.controlHeight*1.5,skeletonTitleHeight:e.controlHeight/2,skeletonBlockRadius:e.borderRadiusSM,skeletonParagraphLineHeight:e.controlHeight/2,skeletonParagraphMarginTop:e.marginLG+e.marginXXS,borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.color} 25%, ${e.colorGradientEnd} 37%, ${e.color} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[ie(n)]},e=>{const{colorFillContent:t,colorFill:n}=e;return{color:t,colorGradientEnd:n}}),se=()=>({active:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},prefixCls:String,avatar:{type:[Boolean,Object],default:void 0},title:{type:[Boolean,Object],default:void 0},paragraph:{type:[Boolean,Object],default:void 0},round:{type:Boolean,default:void 0}});function D(e){return e&&typeof e=="object"?e:{}}function ce(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function ue(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function de(e,t){const n={};return(!e||!t)&&(n.width="61%"),!e&&t?n.rows=3:n.rows=2,n}const c=h({compatConfig:{MODE:3},name:"ASkeleton",props:N(se(),{avatar:!1,title:!0,paragraph:!0}),setup(e,t){let{slots:n}=t;const{prefixCls:o,direction:a}=C("skeleton",e),[i,s]=y(o);return()=>{var d;const{loading:H,avatar:b,title:$,paragraph:p,active:M,round:R}=e,u=o.value;if(H||e.loading===void 0){const v=!!b||b==="",m=!!$||$==="",f=!!p||p==="";let B;if(v){const I=l(l({prefixCls:`${u}-avatar`},ce(m,f)),D(b));B=r("div",{class:`${u}-header`},[r(w,I,null)])}let x;if(m||f){let I;if(m){const z=l(l({prefixCls:`${u}-title`},ue(v,f)),D($));I=r(O,z,null)}let j;if(f){const z=l(l({prefixCls:`${u}-paragraph`},de(v,m)),D(p));j=r(ee,z,null)}x=r("div",{class:`${u}-content`},[I,j])}const J=g(u,{[`${u}-with-avatar`]:v,[`${u}-active`]:M,[`${u}-rtl`]:a.value==="rtl",[`${u}-round`]:R,[s.value]:!0});return i(r("div",{class:J},[B,x]))}return(d=n.default)===null||d===void 0?void 0:d.call(n)}}}),ge=()=>l(l({},P()),{size:String,block:Boolean}),X=h({compatConfig:{MODE:3},name:"ASkeletonButton",props:N(ge(),{size:"default"}),setup(e){const{prefixCls:t}=C("skeleton",e),[n,o]=y(t),a=A(()=>g(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},o.value));return()=>n(r("div",{class:a.value},[r(w,S(S({},e),{},{prefixCls:`${t.value}-button`}),null)]))}}),_=h({compatConfig:{MODE:3},name:"ASkeletonInput",props:l(l({},W(P(),["shape"])),{size:String,block:Boolean}),setup(e){const{prefixCls:t}=C("skeleton",e),[n,o]=y(t),a=A(()=>g(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},o.value));return()=>n(r("div",{class:a.value},[r(w,S(S({},e),{},{prefixCls:`${t.value}-input`}),null)]))}}),pe="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",F=h({compatConfig:{MODE:3},name:"ASkeletonImage",props:W(P(),["size","shape","active"]),setup(e){const{prefixCls:t}=C("skeleton",e),[n,o]=y(t),a=A(()=>g(t.value,`${t.value}-element`,o.value));return()=>n(r("div",{class:a.value},[r("div",{class:`${t.value}-image`},[r("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",class:`${t.value}-image-svg`},[r("path",{d:pe,class:`${t.value}-image-path`},null)])])]))}}),me=()=>l(l({},P()),{shape:String}),K=h({compatConfig:{MODE:3},name:"ASkeletonAvatar",props:N(me(),{size:"default",shape:"circle"}),setup(e){const{prefixCls:t}=C("skeleton",e),[n,o]=y(t),a=A(()=>g(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active},o.value));return()=>n(r("div",{class:a.value},[r(w,S(S({},e),{},{prefixCls:`${t.value}-avatar`}),null)]))}});c.Button=X;c.Avatar=K;c.Input=_;c.Image=F;c.Title=O;c.install=function(e){return e.component(c.name,c),e.component(c.Button.name,X),e.component(c.Avatar.name,K),e.component(c.Input.name,_),e.component(c.Image.name,F),e.component(c.Title.name,O),e};export{c as S};
