{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/raf.js"], "sourcesContent": ["let raf = callback => setTimeout(callback, 16);\nlet caf = num => clearTimeout(num);\nif (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {\n  raf = callback => window.requestAnimationFrame(callback);\n  caf = handle => window.cancelAnimationFrame(handle);\n}\nlet rafUUID = 0;\nconst rafIds = new Map();\nfunction cleanup(id) {\n  rafIds.delete(id);\n}\nexport default function wrapperRaf(callback) {\n  let times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  rafUUID += 1;\n  const id = rafUUID;\n  function callRef(leftTimes) {\n    if (leftTimes === 0) {\n      // Clean up\n      cleanup(id);\n      // Trigger\n      callback();\n    } else {\n      // Next raf\n      const realId = raf(() => {\n        callRef(leftTimes - 1);\n      });\n      // Bind real raf id\n      rafIds.set(id, realId);\n    }\n  }\n  callRef(times);\n  return id;\n}\nwrapperRaf.cancel = id => {\n  const realId = rafIds.get(id);\n  cleanup(realId);\n  return caf(realId);\n};"], "mappings": ";AAAA,IAAI,MAAM,cAAY,WAAW,UAAU,EAAE;AAC7C,IAAI,MAAM,SAAO,aAAa,GAAG;AACjC,IAAI,OAAO,WAAW,eAAe,2BAA2B,QAAQ;AACtE,QAAM,cAAY,OAAO,sBAAsB,QAAQ;AACvD,QAAM,YAAU,OAAO,qBAAqB,MAAM;AACpD;AACA,IAAI,UAAU;AACd,IAAM,SAAS,oBAAI,IAAI;AACvB,SAAS,QAAQ,IAAI;AACnB,SAAO,OAAO,EAAE;AAClB;AACe,SAAR,WAA4B,UAAU;AAC3C,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,aAAW;AACX,QAAM,KAAK;AACX,WAAS,QAAQ,WAAW;AAC1B,QAAI,cAAc,GAAG;AAEnB,cAAQ,EAAE;AAEV,eAAS;AAAA,IACX,OAAO;AAEL,YAAM,SAAS,IAAI,MAAM;AACvB,gBAAQ,YAAY,CAAC;AAAA,MACvB,CAAC;AAED,aAAO,IAAI,IAAI,MAAM;AAAA,IACvB;AAAA,EACF;AACA,UAAQ,KAAK;AACb,SAAO;AACT;AACA,WAAW,SAAS,QAAM;AACxB,QAAM,SAAS,OAAO,IAAI,EAAE;AAC5B,UAAQ,MAAM;AACd,SAAO,IAAI,MAAM;AACnB;", "names": []}