import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-tabler@1.2.95/node_modules/@iconify/icons-tabler/code.js
var require_code = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-tabler@1.2.95/node_modules/@iconify/icons-tabler/code.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m7 8l-4 4l4 4m10-8l4 4l-4 4M14 4l-4 16"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_code();
//# sourceMappingURL=@iconify_icons-tabler_code.js.map
