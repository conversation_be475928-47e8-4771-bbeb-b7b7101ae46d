import"./bootstrap-DCMzVRvD.js";import"./image-upload.vue_vue_type_style_index_0_lang-DExXFAky.js";import{_ as u}from"./image-upload.vue_vue_type_script_setup_true_lang-Bn5a9jyU.js";import{d as c,p as m,h as f,o as p,w as d,j as _,a as v,b as l}from"../jse/index-index-C-MnMZEz.js";import{u as x}from"./use-modal-CeMSCP2m.js";const g={class:"flex flex-col gap-4"},w=c({__name:"image-upload-modal",emits:["reload"],setup(h,{emit:t}){const s=t,e=m([]),[n,r]=x({onOpenChange:a=>{if(a)return null;if(e.value.length>0)return e.value=[],s("reload"),r.close(),null}});return(a,o)=>(p(),f(l(n),{"close-on-click-modal":!1,footer:!1,"fullscreen-button":!1,title:"图片上传"},{default:d(()=>[_("div",g,[v(l(u),{value:e.value,"onUpdate:value":o[0]||(o[0]=i=>e.value=i),"max-count":3},null,8,["value"])])]),_:1}))}});export{w as _};
