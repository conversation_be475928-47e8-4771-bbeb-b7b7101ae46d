{"doc": " 加密响应参数包装类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getEncryptContent", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String", "java.lang.String"], "doc": " 获取加密内容\n\n @param servletResponse response\n @param publicKey       RSA公钥 (用于加密 AES 秘钥)\n @param headerFlag      请求头标志\n @return 加密内容\n @throws IOException\n"}], "constructors": []}