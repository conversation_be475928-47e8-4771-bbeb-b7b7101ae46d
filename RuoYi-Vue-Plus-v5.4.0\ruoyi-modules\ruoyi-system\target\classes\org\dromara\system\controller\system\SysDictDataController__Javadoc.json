{"doc": " 数据字典信息\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询字典数据列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出字典数据列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 查询字典数据详细\n\n @param dictCode 字典code\n"}, {"name": "dictType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询字典数据信息\n\n @param dictType 字典类型\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": " 新增字典类型\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": " 修改保存字典类型\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除字典类型\n\n @param dictCodes 字典code串\n"}], "constructors": []}