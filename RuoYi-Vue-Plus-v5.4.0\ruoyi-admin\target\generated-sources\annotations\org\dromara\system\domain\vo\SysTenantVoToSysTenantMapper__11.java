package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenant;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__11;
import org.dromara.web.domain.vo.TenantListVoToSysTenantVoMapper__13;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {TenantListVoToSysTenantVoMapper__13.class,SysTenantVoToTenantListVoMapper__13.class,SysTenantToSysTenantVoMapper__11.class},
    imports = {}
)
public interface SysTenantVoToSysTenantMapper__11 extends BaseMapper<SysTenantVo, SysTenant> {
}
