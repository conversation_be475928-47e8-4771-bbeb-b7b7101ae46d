{"doc": " 代码生成 操作处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "genList", "paramTypes": ["org.dromara.generator.domain.GenTable", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询代码生成列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 修改代码生成业务\n\n @param tableId 表ID\n"}, {"name": "dataList", "paramTypes": ["org.dromara.generator.domain.GenTable", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询数据库列表\n"}, {"name": "columnList", "paramTypes": ["java.lang.Long"], "doc": " 查询数据表字段列表\n\n @param tableId 表ID\n"}, {"name": "importTableSave", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 导入表结构（保存）\n\n @param tables 表名串\n"}, {"name": "editSave", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": " 修改保存代码生成业务\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除代码生成\n\n @param tableIds 表ID串\n"}, {"name": "preview", "paramTypes": ["java.lang.Long"], "doc": " 预览代码\n\n @param tableId 表ID\n"}, {"name": "download", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.Long"], "doc": " 生成代码（下载方式）\n\n @param tableId 表ID\n"}, {"name": "genCode", "paramTypes": ["java.lang.Long"], "doc": " 生成代码（自定义路径）\n\n @param tableId 表ID\n"}, {"name": "synchDb", "paramTypes": ["java.lang.Long"], "doc": " 同步数据库\n\n @param tableId 表ID\n"}, {"name": "batchGenCode", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": " 批量生成代码\n\n @param tableIdStr 表ID串\n"}, {"name": "genCode", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "byte[]"], "doc": " 生成zip文件\n"}, {"name": "getCurrentDataSourceNameList", "paramTypes": [], "doc": " 查询数据源名称列表\n"}], "constructors": []}