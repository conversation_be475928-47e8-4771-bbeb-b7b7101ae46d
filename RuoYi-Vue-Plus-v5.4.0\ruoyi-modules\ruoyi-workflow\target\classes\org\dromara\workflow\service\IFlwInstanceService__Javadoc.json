{"doc": " 流程实例 服务层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRunningInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询正在运行的流程实例\n\n @param flowInstanceBo 流程实例\n @param pageQuery      分页\n @return 结果\n"}, {"name": "selectFinishInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询已结束的流程实例\n\n @param flowInstanceBo 流程实例\n @param pageQuery      分页\n @return 结果\n"}, {"name": "queryByBusinessId", "paramTypes": ["java.lang.Long"], "doc": " 根据业务id查询流程实例详细信息\n\n @param businessId 业务id\n @return 结果\n"}, {"name": "selectInstByBusinessId", "paramTypes": ["java.lang.String"], "doc": " 按照业务id查询流程实例\n\n @param businessId 业务id\n @return 结果\n"}, {"name": "selectInstById", "paramTypes": ["java.lang.Long"], "doc": " 按照实例id查询流程实例\n\n @param instanceId 实例id\n @return 结果\n"}, {"name": "selectInstListByIdList", "paramTypes": ["java.util.List"], "doc": " 按照实例id查询流程实例\n\n @param instanceIds 实例id\n @return 结果\n"}, {"name": "deleteByBusinessIds", "paramTypes": ["java.util.List"], "doc": " 按照业务id删除流程实例\n\n @param businessIds 业务id\n @return 结果\n"}, {"name": "deleteByInstanceIds", "paramTypes": ["java.util.List"], "doc": " 按照实例id删除流程实例\n\n @param instanceIds 实例id\n @return 结果\n"}, {"name": "cancelProcessApply", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCancelBo"], "doc": " 撤销流程\n\n @param bo 参数\n @return 结果\n"}, {"name": "selectCurrentInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前登陆人发起的流程实例\n\n @param instanceBo 流程实例\n @param pageQuery  分页\n @return 结果\n"}, {"name": "flowHisTaskList", "paramTypes": ["java.lang.String"], "doc": " 获取流程图,流程记录\n\n @param businessId 业务id\n @return 结果\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 按照实例id更新状态\n\n @param instanceId 实例id\n @param status     状态\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": " 获取流程变量\n\n @param instanceId 实例id\n @return 结果\n"}, {"name": "setVariable", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 设置流程变量\n\n @param instanceId 实例id\n @param variable   流程变量\n"}, {"name": "selectByTaskId", "paramTypes": ["java.lang.Long"], "doc": " 按任务id查询实例\n\n @param taskId 任务id\n @return 结果\n"}, {"name": "selectByTaskIdList", "paramTypes": ["java.util.List"], "doc": " 按任务id查询实例\n\n @param taskIdList 任务id\n @return 结果\n"}, {"name": "processInvalid", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInvalidBo"], "doc": " 作废流程\n\n @param bo 流程实例\n @return 结果\n"}], "constructors": []}