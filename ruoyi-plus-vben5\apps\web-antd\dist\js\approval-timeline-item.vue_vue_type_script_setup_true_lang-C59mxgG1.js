var D=(e,t,a)=>new Promise((l,i)=>{var s=n=>{try{d(a.next(n))}catch(r){i(r)}},$=n=>{try{d(a.throw(n))}catch(r){i(r)}},d=n=>n.done?l(n.value):Promise.resolve(n.value).then(s,$);d((a=a.apply(e,t)).next())});import{e as V,p as F,h as v,o as K,t as R,j as Y,m as Z,_ as A,r as k,v as ee,aK as te,c as E,g as M,bN as ie,ar as le}from"./bootstrap-DCMzVRvD.js";import{r as ae}from"./render-BxXtQdeV.js";import{d as H,B,a as c,ad as ne,p as re,v as se,h as O,o as h,b as y,w as X,j as m,c as x,f as j,t as b,L as oe,F as me,K as de}from"../jse/index-index-C-MnMZEz.js";import{_ as ce}from"./avatar.vue_vue_type_script_setup_true_lang-DyQ-Ba-K.js";const ue=()=>({prefixCls:String,color:String,dot:v.any,pending:K(),position:v.oneOf(R("left","right","")).def(""),label:v.any}),I=H({compatConfig:{MODE:3},name:"ATimelineItem",props:V(ue(),{color:"blue",pending:!1}),slots:Object,setup(e,t){let{slots:a}=t;const{prefixCls:l}=F("timeline",e),i=B(()=>({[`${l.value}-item`]:!0,[`${l.value}-item-pending`]:e.pending})),s=B(()=>/blue|red|green|gray/.test(e.color||"")?void 0:e.color||"blue"),$=B(()=>({[`${l.value}-item-head`]:!0,[`${l.value}-item-head-${e.color||"blue"}`]:!s.value}));return()=>{var d,n,r;const{label:u=(d=a.label)===null||d===void 0?void 0:d.call(a),dot:o=(n=a.dot)===null||n===void 0?void 0:n.call(a)}=e;return c("li",{class:i.value},[u&&c("div",{class:`${l.value}-item-label`},[u]),c("div",{class:`${l.value}-item-tail`},null),c("div",{class:[$.value,!!o&&`${l.value}-item-head-custom`],style:{borderColor:s.value,color:s.value}},[o]),c("div",{class:`${l.value}-item-content`},[(r=a.default)===null||r===void 0?void 0:r.call(a)])])}}}),ge=e=>{const{componentCls:t}=e;return{[t]:A(A({},k(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.timeLineItemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.timeLineItemHeadSize,insetInlineStart:(e.timeLineItemHeadSize-e.timeLineItemTailWidth)/2,height:`calc(100% - ${e.timeLineItemHeadSize}px)`,borderInlineStart:`${e.timeLineItemTailWidth}px ${e.lineType} ${e.colorSplit}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.timeLineItemHeadSize,height:e.timeLineItemHeadSize,backgroundColor:e.colorBgContainer,border:`${e.timeLineHeadBorderWidth}px ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:e.timeLineItemHeadSize/2,insetInlineStart:e.timeLineItemHeadSize/2,width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.timeLineItemCustomHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:-(e.fontSize*e.lineHeight-e.fontSize)+e.lineWidth,marginInlineStart:e.margin+e.timeLineItemHeadSize,marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:e.controlHeightLG*1.2}}},[`&${t}-alternate,
        &${t}-right,
        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:`-${e.marginXXS}px`,"&-custom":{marginInlineStart:e.timeLineItemTailWidth/2}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${e.marginXXS}px)`,width:`calc(50% - ${e.marginSM}px)`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${e.marginSM}px)`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,
            ${t}-item-head,
            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(e.timeLineItemHeadSize+e.timeLineItemTailWidth)/2}px)`},[`${t}-item-content`]:{width:`calc(100% - ${e.timeLineItemHeadSize+e.marginXS}px)`}}},[`&${t}-pending
        ${t}-item-last
        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${e.margin}px)`,borderInlineStart:`${e.timeLineItemTailWidth}px dotted ${e.colorSplit}`},[`&${t}-reverse
        ${t}-item-last
        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${e.margin}px)`,borderInlineStart:`${e.timeLineItemTailWidth}px dotted ${e.colorSplit}`},[`${t}-item-content`]:{minHeight:e.controlHeightLG*1.2}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:-(e.fontSize*e.lineHeight-e.fontSize)+e.timeLineItemTailWidth,width:`calc(50% - ${e.marginSM}px)`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${e.marginSM}px)`,width:`calc(50% - ${e.marginSM}px)`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},pe=Y("Timeline",e=>{const t=Z(e,{timeLineItemPaddingBottom:e.padding*1.25,timeLineItemHeadSize:10,timeLineItemCustomHeadPaddingVertical:e.paddingXXS,timeLinePaddingInlineEnd:2,timeLineItemTailWidth:e.lineWidthBold,timeLineHeadBorderWidth:e.wireframe?e.lineWidthBold:e.lineWidth*3});return[ge(t)]}),he=()=>({prefixCls:String,pending:v.any,pendingDot:v.any,reverse:K(),mode:v.oneOf(R("left","alternate","right",""))}),L=H({compatConfig:{MODE:3},name:"ATimeline",inheritAttrs:!1,props:V(he(),{reverse:!1,mode:""}),slots:Object,setup(e,t){let{slots:a,attrs:l}=t;const{prefixCls:i,direction:s}=F("timeline",e),[$,d]=pe(i),n=(r,u)=>{const o=r.props||{};return e.mode==="alternate"?o.position==="right"?`${i.value}-item-right`:o.position==="left"?`${i.value}-item-left`:u%2===0?`${i.value}-item-left`:`${i.value}-item-right`:e.mode==="left"?`${i.value}-item-left`:e.mode==="right"?`${i.value}-item-right`:o.position==="right"?`${i.value}-item-right`:""};return()=>{var r,u,o;const{pending:p=(r=a.pending)===null||r===void 0?void 0:r.call(a),pendingDot:G=(u=a.pendingDot)===null||u===void 0?void 0:u.call(a),reverse:_,mode:z}=e,U=typeof p=="boolean"?null:p,C=ee((o=a.default)===null||o===void 0?void 0:o.call(a)),w=p?c(I,{pending:!!p,dot:G||c(te,null,null)},{default:()=>[U]}):null;w&&C.push(w);const T=_?C.reverse():C,W=T.length,N=`${i.value}-item-last`,q=T.map((f,g)=>{const S=g===W-2?N:"",Q=g===W-1?N:"";return ne(f,{class:E([!_&&p?S:Q,n(f,g)])})}),P=T.some(f=>{var g,S;return!!(!((g=f.props)===null||g===void 0)&&g.label||!((S=f.children)===null||S===void 0)&&S.label)}),J=E(i.value,{[`${i.value}-pending`]:!!p,[`${i.value}-reverse`]:!!_,[`${i.value}-${z}`]:!!z&&!P,[`${i.value}-label`]:P,[`${i.value}-rtl`]:s.value==="rtl"},l.class,d.value);return $(c("ul",M(M({},l),{},{class:J}),[q]))}}});L.Item=I;L.install=function(e){return e.component(L.name,L),e.component(I.name,I),e};const ve={class:"relative rounded-full border"},$e={class:"ml-2 flex flex-col gap-0.5"},fe={class:"flex items-center gap-1"},Se={class:"font-bold"},be={key:0,class:"rounded-lg border p-1"},Ie={class:"break-all opacity-70"},ye={key:1,class:"flex flex-wrap gap-2"},xe=["href"],Le={class:"flex items-center gap-1"},ze=H({name:"ApprovalTimelineItem",__name:"approval-timeline-item",props:{item:{}},setup(e){const t=e,a=re([]);return se(()=>D(null,null,function*(){if(!t.item.ext)return null;const l=yield ie(t.item.ext.split(","));a.value=l.map(i=>({ossId:i.ossId,url:i.url,name:i.originalName}))})),(l,i)=>(h(),O(y(I),null,{dot:X(()=>[m("div",ve,[c(y(ce),{alt:l.item.approveName,class:"bg-primary size-[36px] rounded-full text-white",src:""},null,8,["alt"])])]),default:X(()=>[m("div",$e,[m("div",fe,[m("div",Se,b(l.item.nodeName),1),(h(),O(oe(y(ae)(l.item.flowStatus,y(le).WF_TASK_STATUS))))]),m("div",null,b(l.item.approveName),1),m("div",null,b(l.item.updateTime),1),l.item.message?(h(),x("div",be,[m("div",Ie,b(l.item.message),1)])):j("",!0),a.value.length>0?(h(),x("div",ye,[(h(!0),x(me,null,de(a.value,s=>(h(),x("a",{key:s.ossId,href:s.url,class:"text-primary",target:"_blank"},[m("div",Le,[i[0]||(i[0]=m("span",{class:"icon-[mingcute--attachment-line] size-[18px]"},null,-1)),m("span",null,b(s.name),1)])],8,xe))),128))])):j("",!0)])]),_:1}))}});export{L as T,ze as _};
