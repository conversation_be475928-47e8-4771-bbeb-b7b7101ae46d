{"doc": " 代码生成通用常量\n\n <AUTHOR> Li\n", "fields": [{"name": "TPL_CRUD", "doc": " 单表（增删改查）\n"}, {"name": "TPL_TREE", "doc": " 树表（增删改查）\n"}, {"name": "TREE_CODE", "doc": " 树编码字段\n"}, {"name": "TREE_PARENT_CODE", "doc": " 树父编码字段\n"}, {"name": "TREE_NAME", "doc": " 树名称字段\n"}, {"name": "PARENT_MENU_ID", "doc": " 上级菜单ID字段\n"}, {"name": "PARENT_MENU_NAME", "doc": " 上级菜单名称字段\n"}, {"name": "COLUMNTYPE_STR", "doc": " 数据库字符串类型\n"}, {"name": "COLUMNTYPE_TEXT", "doc": " 数据库文本类型\n"}, {"name": "COLUMNTYPE_TIME", "doc": " 数据库时间类型\n"}, {"name": "COLUMNTYPE_NUMBER", "doc": " 数据库数字类型\n"}, {"name": "COLUMNNAME_NOT_ADD", "doc": " BO对象 不需要添加字段\n"}, {"name": "COLUMNNAME_NOT_EDIT", "doc": " BO对象 不需要编辑字段\n"}, {"name": "COLUMNNAME_NOT_LIST", "doc": " VO对象 不需要返回字段\n"}, {"name": "COLUMNNAME_NOT_QUERY", "doc": " BO对象 不需要查询字段\n"}, {"name": "BASE_ENTITY", "doc": " Entity基类字段\n"}, {"name": "HTML_INPUT", "doc": " 文本框\n"}, {"name": "HTML_TEXTAREA", "doc": " 文本域\n"}, {"name": "HTML_SELECT", "doc": " 下拉框\n"}, {"name": "HTML_RADIO", "doc": " 单选框\n"}, {"name": "HTML_CHECKBOX", "doc": " 复选框\n"}, {"name": "HTML_DATETIME", "doc": " 日期控件\n"}, {"name": "HTML_IMAGE_UPLOAD", "doc": " 图片上传控件\n"}, {"name": "HTML_FILE_UPLOAD", "doc": " 文件上传控件\n"}, {"name": "HTML_EDITOR", "doc": " 富文本控件\n"}, {"name": "TYPE_STRING", "doc": " 字符串类型\n"}, {"name": "TYPE_INTEGER", "doc": " 整型\n"}, {"name": "TYPE_LONG", "doc": " 长整型\n"}, {"name": "TYPE_DOUBLE", "doc": " 浮点型\n"}, {"name": "TYPE_BIGDECIMAL", "doc": " 高精度计算类型\n"}, {"name": "TYPE_DATE", "doc": " 时间类型\n"}, {"name": "QUERY_LIKE", "doc": " 模糊查询\n"}, {"name": "QUERY_EQ", "doc": " 相等查询\n"}, {"name": "REQUIRE", "doc": " 需要\n"}], "enumConstants": [], "methods": [], "constructors": []}