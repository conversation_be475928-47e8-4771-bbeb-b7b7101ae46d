var R=Object.defineProperty;var p=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var F=(a,t,s)=>t in a?R(a,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[t]=s,I=(a,t)=>{for(var s in t||(t={}))j.call(t,s)&&F(a,s,t[s]);if(p)for(var s of p(t))N.call(t,s)&&F(a,s,t[s]);return a};var E=(a,t)=>{var s={};for(var e in a)j.call(a,e)&&t.indexOf(e)<0&&(s[e]=a[e]);if(a!=null&&p)for(var e of p(a))t.indexOf(e)<0&&N.call(a,e)&&(s[e]=a[e]);return s};import{cu as U,cv as W,bm as X}from"./bootstrap-DCMzVRvD.js";import{d as Y,e as Z,f as ee,g as te}from"./index-DjJOU2eu.js";import{_ as ae}from"./analytics-trends.vue_vue_type_script_setup_true_lang-B3-wEqmQ.js";import{_ as se}from"./analytics-visits-data.vue_vue_type_script_setup_true_lang-zNMNptav.js";import{_ as le}from"./analytics-visits-sales.vue_vue_type_script_setup_true_lang-C4FEftxQ.js";import{_ as ne}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-wx7RLJ0r.js";import{_ as oe}from"./analytics-visits.vue_vue_type_script_setup_true_lang-D773X4qP.js";import{_ as re,a as ie,b as ce,c as ue,d as h}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-BFJE8xi5.js";import{d as f,c as d,o as c,n as de,b as l,G as A,r as w,B as V,h as v,w as r,O as fe,p as O,am as P,D as me,q as _e,v as pe,an as ve,ao as ge,H as be,t as g,a as n,F as $,K as y,k as D,j as q}from"../jse/index-index-C-MnMZEz.js";import{_ as xe,a as he,b as $e}from"./TabsList.vue_vue_type_script_setup_true_lang-QZrN9Wp8.js";import"./index-D6-099PU.js";import"./use-echarts-CF-NZzbo.js";const ye=f({__name:"CardFooter",props:{class:{}},setup(a){const t=a;return(s,e)=>(c(),d("div",{class:de(l(A)("flex items-center p-6 pt-0",t.class))},[w(s.$slots,"default")],2))}}),we=f({__name:"TabsTrigger",props:{class:{},value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(a){const t=a,s=V(()=>{const _=t,{class:i}=_;return E(_,["class"])}),e=U(s);return(i,o)=>(c(),v(l(W),fe(l(e),{class:l(A)("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",t.class)}),{default:r(()=>[w(i.$slots,"default")]),_:3},16,["class"]))}}),z=f({name:"CountToAnimator",__name:"count-to-animator",props:{autoplay:{type:Boolean,default:!0},color:{default:""},decimal:{default:"."},decimals:{default:0},duration:{default:1500},endVal:{default:2021},prefix:{default:""},separator:{default:","},startVal:{default:0},suffix:{default:""},transition:{default:"linear"},useEasing:{type:Boolean,default:!0}},emits:["finished","onFinished","onStarted","started"],setup(a,{expose:t,emit:s}){const e=a,i=s,o=O(e.startVal),_=O(!1);let T=P(o);const G=V(()=>K(l(T)));me(()=>{o.value=e.startVal}),_e([()=>e.startVal,()=>e.endVal],()=>{e.autoplay&&k()}),pe(()=>{e.autoplay&&k()});function k(){S(),o.value=e.endVal}function H(){o.value=e.startVal,S()}function S(){T=P(o,I({disabled:_,duration:e.duration,onFinished:()=>{i("finished"),i("onFinished")},onStarted:()=>{i("started"),i("onStarted")}},e.useEasing?{transition:ve[e.transition]}:{}))}function K(u){if(!u&&u!==0)return"";const{decimal:B,decimals:L,prefix:M,separator:b,suffix:J}=e;u=Number(u).toFixed(L),u+="";const x=u.split(".");let m=x[0];const Q=x.length>1?B+x[1]:"",C=/(\d+)(\d{3})/;if(b&&!ge(b)&&m)for(;C.test(m);)m=m.replace(C,`$1${b}$2`);return M+m+Q+J}return t({reset:H}),(u,B)=>(c(),d("span",{style:be({color:u.color})},g(G.value),5))}}),Ve={class:"card-box w-full px-4 pb-5 pt-3"},Te=f({name:"AnalysisChartsTabs",__name:"analysis-charts-tabs",props:{tabs:{default:()=>[]}},setup(a){const t=a,s=V(()=>{var e,i;return(i=(e=t.tabs)==null?void 0:e[0])==null?void 0:i.value});return(e,i)=>(c(),d("div",Ve,[n(l(xe),{"default-value":s.value},{default:r(()=>[n(l(he),null,{default:r(()=>[(c(!0),d($,null,y(e.tabs,o=>(c(),v(l(we),{key:o.label,value:o.value},{default:r(()=>[D(g(o.label),1)]),_:2},1032,["value"]))),128))]),_:1}),(c(!0),d($,null,y(e.tabs,o=>(c(),v(l($e),{key:o.label,value:o.value,class:"pt-4"},{default:r(()=>[w(e.$slots,o.value)]),_:2},1032,["value"]))),128))]),_:3},8,["default-value"])]))}}),ke={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},Se=f({name:"AnalysisOverview",__name:"analysis-overview",props:{items:{default:()=>[]}},setup(a){return(t,s)=>(c(),d("div",ke,[(c(!0),d($,null,y(t.items,e=>(c(),v(l(ue),{key:e.title,title:e.title,class:"w-full"},{default:r(()=>[n(l(re),null,{default:r(()=>[n(l(ie),{class:"text-xl"},{default:r(()=>[D(g(e.title),1)]),_:2},1024)]),_:2},1024),n(l(ce),{class:"flex items-center justify-between"},{default:r(()=>[n(l(z),{"end-val":e.value,"start-val":1,class:"text-xl",prefix:""},null,8,["end-val"]),n(l(X),{icon:e.icon,class:"size-8 flex-shrink-0"},null,8,["icon"])]),_:2},1024),n(l(ye),{class:"justify-between"},{default:r(()=>[q("span",null,g(e.totalTitle),1),n(l(z),{"end-val":e.totalValue,"start-val":1,prefix:""},null,8,["end-val"])]),_:2},1024)]),_:2},1032,["title"]))),128))]))}}),Be={class:"p-5"},Ce={class:"mt-5 w-full md:flex"},Ke=f({__name:"index",setup(a){const t=[{icon:Y,title:"用户量",totalTitle:"总用户量",totalValue:12e4,value:2e3},{icon:Z,title:"访问量",totalTitle:"总访问量",totalValue:5e5,value:2e4},{icon:ee,title:"下载量",totalTitle:"总下载量",totalValue:12e4,value:8e3},{icon:te,title:"使用量",totalTitle:"总使用量",totalValue:5e4,value:5e3}],s=[{label:"流量趋势",value:"trends"},{label:"月访问量",value:"visits"}];return(e,i)=>(c(),d("div",Be,[n(l(Se),{items:t}),n(l(Te),{tabs:s,class:"mt-5"},{trends:r(()=>[n(ae)]),visits:r(()=>[n(oe)]),_:1}),q("div",Ce,[n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问数量"},{default:r(()=>[n(se)]),_:1}),n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(ne)]),_:1}),n(l(h),{class:"mt-5 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(le)]),_:1})])]))}});export{Ke as default};
