import {
  input_default
} from "./chunk-JMNKTVHL.js";
import {
  dynamicApp
} from "./chunk-2K5G4TR6.js";
import {
  VxeUI
} from "./chunk-TETVOAVO.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/input/index.js
var VxeInput = Object.assign(input_default, {
  install(app) {
    app.component(input_default.name, input_default);
  }
});
dynamicApp.use(VxeInput);
VxeUI.component(input_default);
var Input = VxeInput;
var input_default2 = VxeInput;

export {
  VxeInput,
  Input,
  input_default2 as input_default
};
//# sourceMappingURL=chunk-HIIRYMMH.js.map
