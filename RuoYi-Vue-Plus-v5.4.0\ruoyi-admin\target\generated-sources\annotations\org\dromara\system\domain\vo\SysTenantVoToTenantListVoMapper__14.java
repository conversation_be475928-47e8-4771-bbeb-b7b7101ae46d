package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__695;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__10;
import org.dromara.web.domain.vo.TenantListVo;
import org.dromara.web.domain.vo.TenantListVoToSysTenantVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__695.class,
    uses = {TenantListVoToSysTenantVoMapper__14.class,SysTenantVoToSysTenantMapper__10.class,SysTenantToSysTenantVoMapper__10.class},
    imports = {}
)
public interface SysTenantVoToTenantListVoMapper__14 extends BaseMapper<SysTenantVo, TenantListVo> {
}
