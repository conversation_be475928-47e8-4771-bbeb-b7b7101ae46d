import{$ as u,T as d}from"./bootstrap-DCMzVRvD.js";import{h as p,a as m}from"./oauth-common-CrHfL2p7.js";import{C as f,R as y}from"./index-By0xk_Xq.js";import{d as _,c as l,o as r,j as s,a as i,t as x,b as t,w as o,F as b,K as h,h as c,f as g,L as k,H as w}from"../jse/index-index-C-MnMZEz.js";const C={class:"w-full sm:mx-auto md:max-w-md"},v={class:"my-4 flex items-center justify-between"},B={class:"text-muted-foreground text-center text-xs uppercase"},L={class:"flex cursor-pointer items-center justify-center"},F=_({name:"OAuthLogin",__name:"oauth-login",setup(j){return($,a)=>(r(),l("div",C,[s("div",v,[a[0]||(a[0]=s("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1)),s("span",B,x(t(u)("authentication.thirdPartyLogin")),1),a[1]||(a[1]=s("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1))]),i(t(y),{class:"enter-x flex items-center justify-evenly"},{default:o(()=>[(r(!0),l(b,null,h(t(m),e=>(r(),c(t(f),{key:e.source,span:4,class:"my-2"},{default:o(()=>[i(t(d),{title:`${e.title}登录`},{default:o(()=>{var n;return[s("span",L,[e.avatar?(r(),c(k(e.avatar),{key:0,style:w((n=e==null?void 0:e.style)!=null?n:{}),class:"size-[24px]",onClick:N=>t(p)(e.source)},null,8,["style","onClick"])):g("",!0)])]}),_:2},1032,["title"])]),_:2},1024))),128))]),_:1})]))}});export{F as _};
