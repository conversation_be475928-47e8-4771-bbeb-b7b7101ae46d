import { defineStore } from 'pinia';
import { ref } from 'vue';

import { 
  getAccessibleWarehouses, 
  getCurrentWarehouse, 
  setCurrentWarehouse 
} from '#/api/wms/warehouse';
import type { WarehouseVO } from '#/api/wms/warehouse/model';

export const useWarehouseStore = defineStore('warehouse', () => {
  // 当前选择的仓库ID
  const currentWarehouseId = ref<number | null>(null);
  
  // 用户可访问的仓库列表
  const accessibleWarehouses = ref<WarehouseVO[]>([]);
  
  // 是否已初始化
  const initialized = ref(false);

  /**
   * 初始化仓库数据
   */
  async function initWarehouse() {
    try {
      // 获取用户可访问的仓库列表
      const warehouses = await getAccessibleWarehouses();
      accessibleWarehouses.value = warehouses;
      
      // 获取当前用户选择的仓库
      const currentId = await getCurrentWarehouse();
      
      // 如果有保存的仓库选择，且该仓库在可访问列表中，则使用保存的选择
      if (currentId && warehouses.some(w => w.warehouseId === currentId)) {
        currentWarehouseId.value = currentId;
      } else if (warehouses.length > 0) {
        // 否则默认选择第一个可访问的仓库
        currentWarehouseId.value = warehouses[0].warehouseId;
        await setCurrentWarehouse(warehouses[0].warehouseId);
      }
      
      initialized.value = true;
    } catch (error) {
      console.error('初始化仓库数据失败:', error);
    }
  }

  /**
   * 切换仓库
   */
  async function switchWarehouse(warehouseId: number) {
    try {
      await setCurrentWarehouse(warehouseId);
      currentWarehouseId.value = warehouseId;
    } catch (error) {
      console.error('切换仓库失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前仓库信息
   */
  function getCurrentWarehouseInfo() {
    if (!currentWarehouseId.value) return null;
    return accessibleWarehouses.value.find(w => w.warehouseId === currentWarehouseId.value);
  }

  /**
   * 重置仓库状态
   */
  function resetWarehouse() {
    currentWarehouseId.value = null;
    accessibleWarehouses.value = [];
    initialized.value = false;
  }

  return {
    currentWarehouseId,
    accessibleWarehouses,
    initialized,
    initWarehouse,
    switchWarehouse,
    getCurrentWarehouseInfo,
    resetWarehouse,
  };
});
