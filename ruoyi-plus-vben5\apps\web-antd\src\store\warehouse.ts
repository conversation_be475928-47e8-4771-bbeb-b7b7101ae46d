import { defineStore } from 'pinia';
import { ref } from 'vue';

import {
  getAccessibleWarehouses,
  getCurrentWarehouse,
  setCurrentWarehouse
} from '#/api/wms/warehouse';
import type { WarehouseVO } from '#/api/wms/warehouse/model';

// 本地存储键名
const WAREHOUSE_STORAGE_KEY = 'current_warehouse_info';

export const useWarehouseStore = defineStore('warehouse', () => {
  // 当前选择的仓库ID
  const currentWarehouseId = ref<number | null>(null);

  // 用户可访问的仓库列表
  const accessibleWarehouses = ref<WarehouseVO[]>([]);

  // 是否已初始化
  const initialized = ref(false);

  /**
   * 从本地存储获取仓库信息
   */
  function getWarehouseFromStorage() {
    try {
      const stored = localStorage.getItem(WAREHOUSE_STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  /**
   * 保存仓库信息到本地存储
   */
  function saveWarehouseToStorage(warehouseId: number, warehouseInfo: WarehouseVO) {
    try {
      localStorage.setItem(WAREHOUSE_STORAGE_KEY, JSON.stringify({
        warehouseId,
        warehouseName: warehouseInfo.warehouseName,
        warehouseNumber: warehouseInfo.warehouseNumber,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('保存仓库信息到本地存储失败:', error);
    }
  }

  /**
   * 清除本地存储的仓库信息
   */
  function clearWarehouseFromStorage() {
    try {
      localStorage.removeItem(WAREHOUSE_STORAGE_KEY);
    } catch (error) {
      console.warn('清除本地存储的仓库信息失败:', error);
    }
  }

  /**
   * 初始化仓库数据
   */
  async function initWarehouse() {
    try {
      // 先从本地存储获取仓库信息，避免初始化时的闪烁
      const storedInfo = getWarehouseFromStorage();
      if (storedInfo && storedInfo.warehouseId) {
        currentWarehouseId.value = storedInfo.warehouseId;
      }

      // 获取用户可访问的仓库列表
      const warehouses = await getAccessibleWarehouses();
      accessibleWarehouses.value = warehouses;

      // 获取当前用户选择的仓库
      const currentId = await getCurrentWarehouse();

      // 如果有保存的仓库选择，且该仓库在可访问列表中，则使用保存的选择
      if (currentId && warehouses.some(w => w.warehouseId === currentId)) {
        currentWarehouseId.value = currentId;
        // 更新本地存储
        const warehouseInfo = warehouses.find(w => w.warehouseId === currentId);
        if (warehouseInfo) {
          saveWarehouseToStorage(currentId, warehouseInfo);
        }
      } else if (warehouses.length > 0) {
        // 否则默认选择第一个可访问的仓库
        currentWarehouseId.value = warehouses[0].warehouseId;
        await setCurrentWarehouse(warehouses[0].warehouseId);
        saveWarehouseToStorage(warehouses[0].warehouseId, warehouses[0]);
      }

      initialized.value = true;
    } catch (error) {
      console.error('初始化仓库数据失败:', error);
      // 如果是认证错误，不抛出异常，避免影响登出流程
      if (error?.response?.status === 401) {
        return;
      }
      throw error;
    }
  }

  /**
   * 切换仓库
   */
  async function switchWarehouse(warehouseId: number) {
    console.log('Store切换仓库:', warehouseId, '当前:', currentWarehouseId.value);

    try {
      // 先更新本地状态，避免UI闪烁
      const oldWarehouseId = currentWarehouseId.value;
      currentWarehouseId.value = warehouseId;

      console.log('更新后的当前仓库ID:', currentWarehouseId.value);

      // 获取仓库信息并保存到本地存储
      const warehouseInfo = accessibleWarehouses.value.find(w => w.warehouseId === warehouseId);
      if (warehouseInfo) {
        saveWarehouseToStorage(warehouseId, warehouseInfo);
        console.log('保存仓库信息到本地存储:', warehouseInfo.warehouseName);
      }

      // 然后保存到服务器
      await setCurrentWarehouse(warehouseId);
      console.log('保存到服务器成功');
    } catch (error) {
      console.error('切换仓库失败:', error);
      // 如果是认证错误，不抛出异常，避免影响登出流程
      if (error?.response?.status === 401) {
        return;
      }
      // 失败时恢复原来的状态
      if (oldWarehouseId) {
        currentWarehouseId.value = oldWarehouseId;
        const oldWarehouseInfo = accessibleWarehouses.value.find(w => w.warehouseId === oldWarehouseId);
        if (oldWarehouseInfo) {
          saveWarehouseToStorage(oldWarehouseId, oldWarehouseInfo);
        }
      }
      throw error;
    }
  }

  /**
   * 获取当前仓库信息
   */
  function getCurrentWarehouseInfo() {
    if (!currentWarehouseId.value) return null;
    return accessibleWarehouses.value.find(w => w.warehouseId === currentWarehouseId.value);
  }

  /**
   * 重置仓库状态
   */
  function resetWarehouse() {
    currentWarehouseId.value = null;
    accessibleWarehouses.value = [];
    initialized.value = false;
    clearWarehouseFromStorage();
  }

  /**
   * Pinia store重置方法
   */
  function $reset() {
    resetWarehouse();
  }

  return {
    $reset,
    currentWarehouseId,
    accessibleWarehouses,
    initialized,
    initWarehouse,
    switchWarehouse,
    getCurrentWarehouseInfo,
    resetWarehouse,
  };
});
