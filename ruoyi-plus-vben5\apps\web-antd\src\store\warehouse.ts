import { defineStore } from 'pinia';
import { ref } from 'vue';

import type { WarehouseVO } from '#/api/wms/warehouse/model';
import {
  getAccessibleWarehouses,
  getCurrentWarehouse,
  setCurrentWarehouse,
} from '#/api/wms/warehouse';

export const useWarehouseStore = defineStore(
  'warehouse',
  () => {
    // 当前选择的仓库ID
    const currentWarehouseId = ref<number | null>(null);
    
    // 用户可访问的仓库列表
    const accessibleWarehouses = ref<WarehouseVO[]>([]);
    
    // 当前选择的仓库信息
    const currentWarehouse = ref<WarehouseVO | null>(null);

    /**
     * 获取用户可访问的仓库列表
     */
    async function fetchAccessibleWarehouses() {
      try {
        const warehouses = await getAccessibleWarehouses();
        accessibleWarehouses.value = warehouses;
        
        // 如果当前没有选择仓库且有可访问的仓库，自动选择第一个
        if (!currentWarehouseId.value && warehouses.length > 0) {
          await selectWarehouse(warehouses[0].warehouseId);
        }
        
        return warehouses;
      } catch (error) {
        console.error('获取可访问仓库列表失败:', error);
        return [];
      }
    }

    /**
     * 获取当前用户选择的仓库
     */
    async function fetchCurrentWarehouse() {
      try {
        const warehouseId = await getCurrentWarehouse();
        if (warehouseId) {
          currentWarehouseId.value = warehouseId;
          updateCurrentWarehouse();
        }
        return warehouseId;
      } catch (error) {
        console.error('获取当前仓库失败:', error);
        return null;
      }
    }

    /**
     * 选择仓库
     */
    async function selectWarehouse(warehouseId: number) {
      try {
        await setCurrentWarehouse(warehouseId);
        currentWarehouseId.value = warehouseId;
        updateCurrentWarehouse();
        return true;
      } catch (error) {
        console.error('设置当前仓库失败:', error);
        return false;
      }
    }

    /**
     * 更新当前仓库信息
     */
    function updateCurrentWarehouse() {
      if (currentWarehouseId.value) {
        const warehouse = accessibleWarehouses.value.find(
          (w) => w.warehouseId === currentWarehouseId.value,
        );
        currentWarehouse.value = warehouse || null;
      } else {
        currentWarehouse.value = null;
      }
    }

    /**
     * 初始化仓库数据
     */
    async function initWarehouse() {
      await fetchAccessibleWarehouses();
      await fetchCurrentWarehouse();
    }

    /**
     * 重置仓库状态
     */
    function resetWarehouse() {
      currentWarehouseId.value = null;
      accessibleWarehouses.value = [];
      currentWarehouse.value = null;
    }

    return {
      currentWarehouseId,
      accessibleWarehouses,
      currentWarehouse,
      fetchAccessibleWarehouses,
      fetchCurrentWarehouse,
      selectWarehouse,
      initWarehouse,
      resetWarehouse,
    };
  },
  {
    persist: {
      key: 'warehouse-store',
      pick: ['currentWarehouseId'],
    },
  },
);
