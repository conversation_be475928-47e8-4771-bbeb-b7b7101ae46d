import{j as ca,m as pa,_ as s,r as ga,n as T,c4 as Y,h as m,p as _,c7 as y,aE as ua,g as J,c8 as $a,c9 as ba,bx as ha,ca as ma,cb as D,aL as R}from"./bootstrap-DCMzVRvD.js";import{a as Z}from"./index-BaVK9zYh.js";import{S as ya}from"./index-BLwHKR_M.js";import{d as I,a as n,N as Q,B as fa}from"../jse/index-index-C-MnMZEz.js";const va=a=>{const{antCls:e,componentCls:t,cardHeadHeight:i,cardPaddingBase:r,cardHeadTabsMarginBottom:d}=a;return s(s({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:i,marginBottom:-1,padding:`0 ${r}px`,color:a.colorTextHeading,fontWeight:a.fontWeightStrong,fontSize:a.fontSizeLG,background:"transparent",borderBottom:`${a.lineWidth}px ${a.lineType} ${a.colorBorderSecondary}`,borderRadius:`${a.borderRadiusLG}px ${a.borderRadiusLG}px 0 0`},T()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":s(s({display:"inline-block",flex:1},Y),{[`
          > ${t}-typography,
          > ${t}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${e}-tabs-top`]:{clear:"both",marginBottom:d,color:a.colorText,fontWeight:"normal",fontSize:a.fontSize,"&-bar":{borderBottom:`${a.lineWidth}px ${a.lineType} ${a.colorBorderSecondary}`}}})},Sa=a=>{const{cardPaddingBase:e,colorBorderSecondary:t,cardShadow:i,lineWidth:r}=a;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:`
      ${r}px 0 0 0 ${t},
      0 ${r}px 0 0 ${t},
      ${r}px ${r}px 0 0 ${t},
      ${r}px 0 0 0 ${t} inset,
      0 ${r}px 0 0 ${t} inset;
    `,transition:`all ${a.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:i}}},xa=a=>{const{componentCls:e,iconCls:t,cardActionsLiMargin:i,cardActionsIconSize:r,colorBorderSecondary:d}=a;return s(s({margin:0,padding:0,listStyle:"none",background:a.colorBgContainer,borderTop:`${a.lineWidth}px ${a.lineType} ${d}`,display:"flex",borderRadius:`0 0 ${a.borderRadiusLG}px ${a.borderRadiusLG}px `},T()),{"& > li":{margin:i,color:a.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:a.cardActionsIconSize*2,fontSize:a.fontSize,lineHeight:a.lineHeight,cursor:"pointer","&:hover":{color:a.colorPrimary,transition:`color ${a.motionDurationMid}`},[`a:not(${e}-btn), > ${t}`]:{display:"inline-block",width:"100%",color:a.colorTextDescription,lineHeight:`${a.fontSize*a.lineHeight}px`,transition:`color ${a.motionDurationMid}`,"&:hover":{color:a.colorPrimary}},[`> ${t}`]:{fontSize:r,lineHeight:`${r*a.lineHeight}px`}},"&:not(:last-child)":{borderInlineEnd:`${a.lineWidth}px ${a.lineType} ${d}`}}})},Ca=a=>s(s({margin:`-${a.marginXXS}px 0`,display:"flex"},T()),{"&-avatar":{paddingInlineEnd:a.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:a.marginXS}},"&-title":s({color:a.colorTextHeading,fontWeight:a.fontWeightStrong,fontSize:a.fontSizeLG},Y),"&-description":{color:a.colorTextDescription}}),Ta=a=>{const{componentCls:e,cardPaddingBase:t,colorFillAlter:i}=a;return{[`${e}-head`]:{padding:`0 ${t}px`,background:i,"&-title":{fontSize:a.fontSize}},[`${e}-body`]:{padding:`${a.padding}px ${t}px`}}},Ba=a=>{const{componentCls:e}=a;return{overflow:"hidden",[`${e}-body`]:{userSelect:"none"}}},za=a=>{const{componentCls:e,cardShadow:t,cardHeadPadding:i,colorBorderSecondary:r,boxShadow:d,cardPaddingBase:g}=a;return{[e]:s(s({},ga(a)),{position:"relative",background:a.colorBgContainer,borderRadius:a.borderRadiusLG,[`&:not(${e}-bordered)`]:{boxShadow:d},[`${e}-head`]:va(a),[`${e}-extra`]:{marginInlineStart:"auto",color:"",fontWeight:"normal",fontSize:a.fontSize},[`${e}-body`]:s({padding:g,borderRadius:` 0 0 ${a.borderRadiusLG}px ${a.borderRadiusLG}px`},T()),[`${e}-grid`]:Sa(a),[`${e}-cover`]:{"> *":{display:"block",width:"100%"},img:{borderRadius:`${a.borderRadiusLG}px ${a.borderRadiusLG}px 0 0`}},[`${e}-actions`]:xa(a),[`${e}-meta`]:Ca(a)}),[`${e}-bordered`]:{border:`${a.lineWidth}px ${a.lineType} ${r}`,[`${e}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${e}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${a.motionDurationMid}, border-color ${a.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:t}},[`${e}-contain-grid`]:{[`${e}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${e}-loading) ${e}-body`]:{marginBlockStart:-a.lineWidth,marginInlineStart:-a.lineWidth,padding:0}},[`${e}-contain-tabs`]:{[`> ${e}-head`]:{[`${e}-head-title, ${e}-extra`]:{paddingTop:i}}},[`${e}-type-inner`]:Ta(a),[`${e}-loading`]:Ba(a),[`${e}-rtl`]:{direction:"rtl"}}},Ha=a=>{const{componentCls:e,cardPaddingSM:t,cardHeadHeightSM:i}=a;return{[`${e}-small`]:{[`> ${e}-head`]:{minHeight:i,padding:`0 ${t}px`,fontSize:a.fontSize,[`> ${e}-head-wrapper`]:{[`> ${e}-extra`]:{fontSize:a.fontSize}}},[`> ${e}-body`]:{padding:t}},[`${e}-small${e}-contain-tabs`]:{[`> ${e}-head`]:{[`${e}-head-title, ${e}-extra`]:{minHeight:i,paddingTop:0,display:"flex",alignItems:"center"}}}}},Ga=ca("Card",a=>{const e=pa(a,{cardShadow:a.boxShadowCard,cardHeadHeight:a.fontSizeLG*a.lineHeightLG+a.padding*2,cardHeadHeightSM:a.fontSize*a.lineHeight+a.paddingXS*2,cardHeadPadding:a.padding,cardPaddingBase:a.paddingLG,cardHeadTabsMarginBottom:-a.padding-a.lineWidth,cardActionsLiMargin:`${a.paddingSM}px 0`,cardActionsIconSize:a.fontSize,cardPaddingSM:12});return[za(e),Ha(e)]}),{TabPane:La}=Z,Ma=()=>({prefixCls:String,title:m.any,extra:m.any,bordered:{type:Boolean,default:!0},bodyStyle:{type:Object,default:void 0},headStyle:{type:Object,default:void 0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},type:{type:String},size:{type:String},actions:m.any,tabList:{type:Array},tabBarExtraContent:m.any,activeTabKey:String,defaultActiveTabKey:String,cover:m.any,onTabChange:{type:Function}}),f=I({compatConfig:{MODE:3},name:"ACard",inheritAttrs:!1,props:Ma(),slots:Object,setup(a,e){let{slots:t,attrs:i}=e;const{prefixCls:r,direction:d,size:g}=_("card",a),[v,B]=Ga(r),S=p=>p.map((l,u)=>Q(l)&&!$a(l)||!Q(l)?n("li",{style:{width:`${100/p.length}%`},key:`action-${u}`},[n("span",null,[l])]):null),x=p=>{var c;(c=a.onTabChange)===null||c===void 0||c.call(a,p)},z=function(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],c;return p.forEach(l=>{l&&ba(l.type)&&l.type.__ANT_CARD_GRID&&(c=!0)}),c};return()=>{var p,c,l,u,H,G;const{headStyle:k={},bodyStyle:aa={},loading:E,bordered:ea=!0,type:j,tabList:$,hoverable:ta,activeTabKey:O,defaultActiveTabKey:ia,tabBarExtraContent:K=y((p=t.tabBarExtraContent)===null||p===void 0?void 0:p.call(t)),title:L=y((c=t.title)===null||c===void 0?void 0:c.call(t)),extra:M=y((l=t.extra)===null||l===void 0?void 0:l.call(t)),actions:w=y((u=t.actions)===null||u===void 0?void 0:u.call(t)),cover:N=y((H=t.cover)===null||H===void 0?void 0:H.call(t))}=a,b=ua((G=t.default)===null||G===void 0?void 0:G.call(t)),o=r.value,ra={[`${o}`]:!0,[B.value]:!0,[`${o}-loading`]:E,[`${o}-bordered`]:ea,[`${o}-hoverable`]:!!ta,[`${o}-contain-grid`]:z(b),[`${o}-contain-tabs`]:$&&$.length,[`${o}-${g.value}`]:g.value,[`${o}-type-${j}`]:!!j,[`${o}-rtl`]:d.value==="rtl"},na=n(ya,{loading:!0,active:!0,paragraph:{rows:4},title:!1},{default:()=>[b]}),X=O!==void 0,oa={size:"large",[X?"activeKey":"defaultActiveKey"]:X?O:ia,onChange:x,class:`${o}-head-tabs`};let F;const V=$&&$.length?n(Z,oa,{default:()=>[$.map(h=>{const{tab:U,slots:C}=h,q=C==null?void 0:C.tab;ha(!C,"Card","tabList slots is deprecated, Please use `customTab` instead.");let A=U!==void 0?U:t[q]?t[q](h):null;return A=ma(t,"customTab",h,()=>[A]),n(La,{tab:A,key:h.key,disabled:h.disabled},null)})],rightExtra:K?()=>K:null}):null;(L||M||V)&&(F=n("div",{class:`${o}-head`,style:k},[n("div",{class:`${o}-head-wrapper`},[L&&n("div",{class:`${o}-head-title`},[L]),M&&n("div",{class:`${o}-extra`},[M])]),V]));const da=N?n("div",{class:`${o}-cover`},[N]):null,la=n("div",{class:`${o}-body`,style:aa},[E?na:b]),sa=w&&w.length?n("ul",{class:`${o}-actions`},[S(w)]):null;return v(n("div",J(J({ref:"cardContainerRef"},i),{},{class:[ra,i.class]}),[F,da,b&&b.length?la:null,sa]))}}}),wa=()=>({prefixCls:String,title:D(),description:D(),avatar:D()}),P=I({compatConfig:{MODE:3},name:"ACardMeta",props:wa(),slots:Object,setup(a,e){let{slots:t}=e;const{prefixCls:i}=_("card",a);return()=>{const r={[`${i.value}-meta`]:!0},d=R(t,a,"avatar"),g=R(t,a,"title"),v=R(t,a,"description"),B=d?n("div",{class:`${i.value}-meta-avatar`},[d]):null,S=g?n("div",{class:`${i.value}-meta-title`},[g]):null,x=v?n("div",{class:`${i.value}-meta-description`},[v]):null,z=S||x?n("div",{class:`${i.value}-meta-detail`},[S,x]):null;return n("div",{class:r},[B,z])}}}),Aa=()=>({prefixCls:String,hoverable:{type:Boolean,default:!0}}),W=I({compatConfig:{MODE:3},name:"ACardGrid",__ANT_CARD_GRID:!0,props:Aa(),setup(a,e){let{slots:t}=e;const{prefixCls:i}=_("card",a),r=fa(()=>({[`${i.value}-grid`]:!0,[`${i.value}-grid-hoverable`]:a.hoverable}));return()=>{var d;return n("div",{class:r.value},[(d=t.default)===null||d===void 0?void 0:d.call(t)])}}});f.Meta=P;f.Grid=W;f.install=function(a){return a.component(f.name,f),a.component(P.name,P),a.component(W.name,W),a};export{f as C};
