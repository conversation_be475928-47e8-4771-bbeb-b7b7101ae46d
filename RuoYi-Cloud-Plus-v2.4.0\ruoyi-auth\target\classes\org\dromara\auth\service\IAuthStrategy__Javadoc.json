{"doc": " 授权策略\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["java.lang.String", "org.dromara.system.api.domain.vo.RemoteClientVo", "java.lang.String"], "doc": " 登录\n\n @param body      登录对象\n @param client    授权管理视图对象\n @param grantType 授权类型\n @return 登录验证信息\n"}, {"name": "login", "paramTypes": ["java.lang.String", "org.dromara.system.api.domain.vo.RemoteClientVo"], "doc": " 登录\n\n @param body   登录对象\n @param client 授权管理视图对象\n @return 登录验证信息\n"}], "constructors": []}