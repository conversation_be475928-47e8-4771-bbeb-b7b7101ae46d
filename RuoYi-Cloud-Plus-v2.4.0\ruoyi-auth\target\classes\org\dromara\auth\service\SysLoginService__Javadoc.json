{"doc": " 登录校验方法\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "socialRegister", "paramTypes": ["me.zhyd.oauth.model.AuthUser"], "doc": " 绑定第三方用户\n\n @param authUserData 授权响应实体\n"}, {"name": "logout", "paramTypes": [], "doc": " 退出登录\n"}, {"name": "register", "paramTypes": ["org.dromara.auth.form.RegisterBody"], "doc": " 注册\n"}, {"name": "validate<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 校验验证码\n\n @param username 用户名\n @param code     验证码\n @param uuid     唯一标识\n"}, {"name": "recordLogininfor", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 记录登录信息\n\n @param username 用户名\n @param status   状态\n @param message  消息内容\n @return\n"}, {"name": "checkLogin", "paramTypes": ["org.dromara.common.core.enums.LoginType", "java.lang.String", "java.lang.String", "java.util.function.Supplier"], "doc": " 登录校验\n"}, {"name": "checkTenant", "paramTypes": ["java.lang.String"], "doc": " 校验租户\n\n @param tenantId 租户ID\n"}], "constructors": []}