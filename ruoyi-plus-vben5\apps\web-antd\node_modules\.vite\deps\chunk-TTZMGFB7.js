import {
  toolbar_default
} from "./chunk-5TXDSDAI.js";
import {
  VxeUI
} from "./chunk-TETVOAVO.js";

// ../../node_modules/.pnpm/vxe-table@4.13.35_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/index.js
var VxeToolbar = Object.assign({}, toolbar_default, {
  install(app) {
    app.component(toolbar_default.name, toolbar_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(toolbar_default.name, toolbar_default);
}
VxeUI.component(toolbar_default);
var Toolbar = VxeToolbar;
var toolbar_default2 = VxeToolbar;

export {
  VxeToolbar,
  Toolbar,
  toolbar_default2 as toolbar_default
};
//# sourceMappingURL=chunk-TTZMGFB7.js.map
