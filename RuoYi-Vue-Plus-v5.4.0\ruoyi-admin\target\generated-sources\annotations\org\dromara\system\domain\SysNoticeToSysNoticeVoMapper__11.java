package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__11;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysNoticeVoToSysNoticeMapper__11.class,SysNoticeBoToSysNoticeMapper__11.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__11 extends BaseMapper<SysNotice, SysNoticeVo> {
}
