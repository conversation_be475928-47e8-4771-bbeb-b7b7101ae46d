import {
  button_default
} from "./chunk-ES4YO5YO.js";
import {
  dynamicApp
} from "./chunk-2K5G4TR6.js";
import {
  VxeUI
} from "./chunk-TETVOAVO.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.17_vue@3.5.16_typescript@5.8.3_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

export {
  VxeButton,
  Button,
  button_default2 as button_default
};
//# sourceMappingURL=chunk-E4OWAKAA.js.map
