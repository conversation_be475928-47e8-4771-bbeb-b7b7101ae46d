package org.dromara.resource.domain;

import io.github.linpeilie.AutoMapperConfig__1137;
import io.github.linpeilie.BaseMapper;
import org.dromara.resource.domain.bo.SysOssConfigBoToSysOssConfigMapper__10;
import org.dromara.resource.domain.vo.SysOssConfigVo;
import org.dromara.resource.domain.vo.SysOssConfigVoToSysOssConfigMapper__10;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1137.class,
    uses = {SysOssConfigBoToSysOssConfigMapper__10.class,SysOssConfigVoToSysOssConfigMapper__10.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__10 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
