var F=Object.defineProperty;var D=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var T=(c,s,t)=>s in c?F(c,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):c[s]=t,q=(c,s)=>{for(var t in s||(s={}))G.call(s,t)&&T(c,t,s[t]);if(D)for(var t of D(s))L.call(s,t)&&T(c,t,s[t]);return c};var f=(c,s,t)=>new Promise(($,i)=>{var S=p=>{try{k(t.next(p))}catch(v){i(v)}},C=p=>{try{k(t.throw(p))}catch(v){i(v)}},k=p=>p.done?$(p.value):Promise.resolve(p.value).then(S,C);k((t=t.apply(c,s)).next())});import{at as W,as as H,an as A}from"./bootstrap-DCMzVRvD.js";import{v as J}from"./vxe-table-DzEj5Fop.js";import{u as K,t as Q,a as X,b as Y,c as V,d as Z,e as ee}from"./tenant-DKIZPfcZ.js";import{_ as te}from"./table-switch.vue_vue_type_script_setup_true_lang-BPKnQ2Wy.js";import{c as ne}from"./download-UJak946_.js";import{c as ae,q as oe,t as se}from"./tenant-drawer-zQfIuDhD.js";import B from"./index-BeyziwLP.js";import{_ as ie}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{_ as re}from"./fallback.vue_vue_type_script_setup_true_lang-Cl7I0lq3.js";import{d as ce,B as me,l as P,S as de,h as m,o as d,w as o,a as h,b as n,f as pe,T as g,k as y,t as _}from"../jse/index-index-C-MnMZEz.js";import{u as le}from"./use-vxe-grid-BC7vZzEr.js";import{u as ue}from"./use-drawer-6qcpK-D1.js";import{P as N}from"./index-DNdMANjv.js";import{g as R}from"./get-popup-container-P4S1sr5h.js";import"./init-C8TKSdFQ.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./helper-Bc7QQ92Q.js";import"./index-D-hwdOI6.js";import"./index-C9ZGxfH6.js";import"./popup-D6rC6QBG.js";import"./rotate-cw-DzZTu9nW.js";import"./x-Bfkqqjgb.js";import"./index-qvRUEWLR.js";const Ie=ce({__name:"index",setup(c){const s={commonConfig:{labelWidth:80,componentProps:{allowClear:!0}},schema:oe(),wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},t={checkboxConfig:{highlight:!0,reserve:!0,checkMethod:({row:e})=>(e==null?void 0:e.id)!==1},columns:ae,height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(l,...w)=>f(null,[l,...w],function*({page:e},a={}){return yield X(q({pageNum:e.currentPage,pageSize:e.pageSize},a))})}},rowConfig:{keyField:"id"},id:"system-tenant-index"},[$,i]=le({formOptions:s,gridOptions:t}),[S,C]=ue({connectedComponent:se});function k(){C.setData({}),C.open()}function p(e){return f(this,null,function*(){C.setData({id:e.id}),C.open()})}function v(e){return f(this,null,function*(){const{tenantId:a,packageId:l}=e;yield Y(a,l),yield i.query()})}const x=K();function E(e){return f(this,null,function*(){yield V([e.id]),yield i.query(),x.initTenant()})}function M(){const a=i.grid.getCheckboxRecords().map(l=>l.id);A.confirm({title:"提示",okType:"danger",content:`确认删除选中的${a.length}条记录吗？`,onOk:()=>f(null,null,function*(){yield V(a),yield i.query(),x.initTenant()})})}function O(){ne(Z,"租户数据",i.formApi.form.values)}const{hasAccessByCodes:z,hasAccessByRoles:I}=W(),U=me(()=>I(["superadmin"]));function j(){A.confirm({title:"提示",iconType:"warning",content:"确认同步租户字典？",onOk:()=>f(null,null,function*(){yield ee(),yield i.query()})})}return(e,a)=>{const l=P("a-button"),w=P("ghost-button"),u=de("access");return U.value?(d(),m(n(ie),{key:0,"auto-content-height":!0},{default:o(()=>[h(n($),{"table-title":"租户列表"},{"toolbar-tools":o(()=>[h(n(B),null,{default:o(()=>[g((d(),m(l,{onClick:j},{default:o(()=>a[3]||(a[3]=[y(" 同步租户字典 ")])),_:1,__:[3]})),[[u,["system:tenant:edit"],"code"]]),g((d(),m(l,{onClick:O},{default:o(()=>[y(_(e.$t("pages.common.export")),1)]),_:1})),[[u,["system:tenant:export"],"code"]]),g((d(),m(l,{disabled:!n(J)(n(i)),danger:"",type:"primary",onClick:M},{default:o(()=>[y(_(e.$t("pages.common.delete")),1)]),_:1},8,["disabled"])),[[u,["system:tenant:remove"],"code"]]),g((d(),m(l,{type:"primary",onClick:k},{default:o(()=>[y(_(e.$t("pages.common.add")),1)]),_:1})),[[u,["system:tenant:add"],"code"]])]),_:1})]),status:o(({row:r})=>[h(n(te),{value:r.status,"onUpdate:value":b=>r.status=b,api:()=>n(Q)(r),disabled:r.id===1||!n(z)(["system:tenant:edit"]),onReload:a[0]||(a[0]=b=>n(i).query())},null,8,["value","onUpdate:value","api","disabled"])]),action:o(({row:r})=>[r.id!==1?(d(),m(n(B),{key:0},{default:o(()=>[g((d(),m(w,{onClick:b=>p(r)},{default:o(()=>[y(_(e.$t("pages.common.edit")),1)]),_:2},1032,["onClick"])),[[u,["system:tenant:edit"],"code"]]),h(n(N),{"get-popup-container":n(R),title:`确认同步[${r.companyName}]的套餐吗?`,placement:"left",onConfirm:b=>v(r)},{default:o(()=>[g((d(),m(w,{class:"btn-success"},{default:o(()=>[y(_(e.$t("pages.common.sync")),1)]),_:1})),[[u,["system:tenant:edit"],"code"]])]),_:2},1032,["get-popup-container","title","onConfirm"]),h(n(N),{"get-popup-container":n(R),placement:"left",title:"确认删除？",onConfirm:b=>E(r)},{default:o(()=>[g((d(),m(w,{danger:"",onClick:a[1]||(a[1]=H(()=>{},["stop"]))},{default:o(()=>[y(_(e.$t("pages.common.delete")),1)]),_:1})),[[u,["system:tenant:remove"],"code"]])]),_:2},1032,["get-popup-container","onConfirm"])]),_:2},1024)):pe("",!0)]),_:1}),h(n(S),{onReload:a[2]||(a[2]=r=>n(i).query())})]),_:1})):(d(),m(n(re),{key:1,description:"您没有租户的访问权限",status:"403"}))}}});export{Ie as default};
