var p=(d,c,s)=>new Promise((l,m)=>{var u=t=>{try{n(s.next(t))}catch(i){m(i)}},e=t=>{try{n(s.throw(t))}catch(i){m(i)}},n=t=>t.done?l(t.value):Promise.resolve(t.value).then(u,e);n((s=s.apply(d,c)).next())});import{ar as f,$ as b,aj as y}from"./bootstrap-DCMzVRvD.js";import{c as A,d as L,e as B}from"./index-ErD4UjKl.js";import{g as v}from"./index-ocPq22VW.js";import{u as x,d as N}from"./popup-D6rC6QBG.js";import{g}from"./dict-BLkXAGS5.js";import{r as P}from"./render-BxXtQdeV.js";import{a as h}from"./get-popup-container-P4S1sr5h.js";import{d as k,p as q,B as V,P as F,h as T,o as E,w as O,a as R,b as I}from"../jse/index-index-C-MnMZEz.js";import{u as z}from"./use-drawer-6qcpK-D1.js";import{a as G}from"./tree-DFBawhPd.js";const ee=()=>[{component:"Input",fieldName:"postCode",label:"岗位编码"},{component:"Input",fieldName:"postName",label:"岗位名称"},{component:"Select",componentProps:{getPopupContainer:h,options:g(f.SYS_NORMAL_DISABLE)},fieldName:"status",label:"状态"}],te=[{type:"checkbox",width:60},{title:"岗位编码",field:"postCode"},{title:"类别编码",field:"postCategory"},{title:"岗位名称",field:"postName"},{title:"排序",field:"postSort"},{title:"状态",field:"status",slots:{default:({row:d})=>P(d.status,f.SYS_NORMAL_DISABLE)}},{title:"创建时间",field:"createTime"},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",resizable:!1,width:"auto"}],M=()=>[{component:"Input",dependencies:{show:()=>!1,triggerFields:[""]},fieldName:"postId",label:"postId"},{component:"TreeSelect",componentProps:{getPopupContainer:h},fieldName:"deptId",label:"所属部门",rules:"selectRequired"},{component:"Input",fieldName:"postName",label:"岗位名称",rules:"required"},{component:"Input",fieldName:"postCode",label:"岗位编码",rules:"required"},{component:"Input",fieldName:"postCategory",label:"类别编码"},{component:"InputNumber",fieldName:"postSort",label:"岗位排序",rules:"required"},{component:"RadioGroup",componentProps:{buttonStyle:"solid",options:g(f.SYS_NORMAL_DISABLE),optionType:"button"},defaultValue:"0",fieldName:"status",label:"岗位状态",rules:"required"},{component:"Textarea",fieldName:"remark",formItemClass:"items-start",label:"备注"}],ae=k({__name:"post-drawer",emits:["reload"],setup(d,{emit:c}){const s=c,l=q(!1),m=V(()=>l.value?b("pages.common.edit"):b("pages.common.add")),[u,e]=y({commonConfig:{formItemClass:"col-span-2",componentProps:{class:"w-full"},labelWidth:80},schema:M(),showDefaultActions:!1,wrapperClass:"grid-cols-2"});function n(){return p(this,null,function*(){const a=yield v();G(a,"label"," / "),e.updateSchema([{componentProps:{fieldNames:{label:"label",value:"id"},treeData:a,treeDefaultExpandAll:!0,treeLine:{showLeafIcon:!1},treeNodeLabelProp:"fullName"},fieldName:"deptId"}])})}const{onBeforeClose:t,markInitialized:i,resetInitialized:w}=x({initializedGetter:N(e),currentGetter:N(e)}),[C,r]=z({onBeforeClose:t,onClosed:D,onConfirm:S,onOpenChange(a){return p(this,null,function*(){if(!a)return null;r.drawerLoading(!0);const{id:o}=r.getData();if(l.value=!!o,yield n(),l.value&&o){const _=yield A(o);yield e.setValues(_)}yield i(),r.drawerLoading(!1)})}});function S(){return p(this,null,function*(){try{r.lock(!0);const{valid:a}=yield e.validate();if(!a)return;const o=F(yield e.getValues());yield l.value?L(o):B(o),w(),s("reload"),r.close()}catch(a){console.error(a)}finally{r.lock(!1)}})}function D(){return p(this,null,function*(){yield e.resetForm(),w()})}return(a,o)=>(E(),T(I(C),{title:m.value,class:"w-[600px]"},{default:O(()=>[R(I(u))]),_:1},8,["title"]))}});export{ae as _,te as c,ee as q};
