---
description: 
globs: 
alwaysApply: true
---
# 开发工作流和最佳实践

## 开发环境设置

### 依赖管理
项目使用pnpm作为包管理器，要求版本 >= 9.12.0：
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev:antd

# 构建生产版本
pnpm build:antd
```

### 工作空间配置
- 根配置：[package.json](mdc:package.json)
- 工作空间：[pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)
- 构建系统：[turbo.json](mdc:turbo.json)

## 代码规范

### ESLint配置
项目使用严格的ESLint规则，配置在：
- ESLint配置：[eslint.config.mjs](mdc:eslint.config.mjs)
- 内部规范包：`internal/lint-configs/eslint-config/`

### 提交规范
使用Conventional Commits规范：
- feat: 新功能
- fix: 修复问题
- docs: 文档变更
- style: 代码风格调整
- refactor: 重构
- perf: 性能优化
- test: 测试相关
- chore: 工具配置等

### Git工作流
配置文件：[lefthook.yml](mdc:lefthook.yml)
- pre-commit: 代码格式化和lint检查
- commit-msg: 提交信息格式验证

## 开发最佳实践

### 新增页面开发流程
1. 在 `apps/web-antd/src/views/` 下创建页面目录
2. 创建主页面组件 `index.vue`
3. 创建表格配置 `data.tsx`
4. 创建表单组件 `xxx-drawer.vue`
5. 在 `apps/web-antd/src/api/` 下创建对应API

### 新增API开发流程
1. 在对应模块目录下创建API文件
2. 定义TypeScript接口
3. 实现CRUD操作函数
4. 配置加密和权限
5. 添加错误处理

### 组件开发规范
1. 使用Vue 3 Composition API
2. TypeScript类型定义完整
3. Props和Emits明确定义
4. 组件文档和使用示例

## 调试和测试

### 开发工具
- Vue DevTools: 用于调试Vue组件
- Vite DevTools: 构建和性能分析
- Network面板: 检查API请求和响应

### 测试框架
- 单元测试：Vitest
- 组件测试：@vue/test-utils
- E2E测试：Playwright

### 运行测试
```bash
# 单元测试
pnpm test:unit

# E2E测试
pnpm test:e2e
```

## 性能优化

### 构建优化
- 代码分割：按路由和功能模块分割
- 懒加载：页面和组件按需加载
- Tree Shaking：移除未使用的代码

### 运行时优化
- 虚拟列表：大数据量表格优化
- 图片懒加载：优化页面加载速度
- 缓存策略：API数据和资源缓存

### 监控和分析
```bash
# 构建分析
pnpm build:analyze

# 依赖检查
pnpm check:dep

# 循环依赖检查
pnpm check:circular
```

## 国际化开发

### 语言包管理
- 中文语言包：[apps/web-antd/src/locales/zh-CN/](mdc:apps/web-antd/src/locales/zh-CN)
- 英文语言包：[apps/web-antd/src/locales/en-US/](mdc:apps/web-antd/src/locales/en-US)

### 国际化使用
```vue
<template>
  <div>{{ $t('common.button.add') }}</div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
</script>
```

## 部署流程

### 本地构建
```bash
# 构建生产版本
pnpm build:antd

# 预览构建结果
pnpm preview
```

### Docker部署
参考根目录下的 Dockerfile 和 docker-compose.yml

### 环境配置
- 开发环境：`.env.development`
- 生产环境：`.env.production`
- 测试环境：`.env.test`

## 常见问题排查

### 依赖问题
```bash
# 清理依赖重新安装
pnpm clean
pnpm install
```

### 构建问题
```bash
# 清理构建缓存
pnpm clean
pnpm build:antd
```

### 类型检查
```bash
# 运行类型检查
pnpm check:type
```
