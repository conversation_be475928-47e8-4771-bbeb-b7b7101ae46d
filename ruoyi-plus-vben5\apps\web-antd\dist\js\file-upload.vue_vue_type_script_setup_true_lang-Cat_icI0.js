import{aC as P,a as h,U as k,$ as B,u as D,I as V}from"./bootstrap-DCMzVRvD.js";import{u as j,b as F,c as R}from"./image-upload.vue_vue_type_style_index_0_lang-DExXFAky.js";import{a as r,d as H,m as C,B as L,u as N,c as f,r as T,w as u,b as a,i as E,e as _,g as $,o as p,f as b,l as q,k as A,t as c,j as m,h as G,n as g}from"../jse/index-index-C-MnMZEz.js";var J={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"};function O(l){for(var t=1;t<arguments.length;t++){var o=arguments[t]!=null?Object(arguments[t]):{},s=Object.keys(o);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(o).filter(function(n){return Object.getOwnPropertyDescriptor(o,n).enumerable}))),s.forEach(function(n){Q(l,n,o[n])})}return l}function Q(l,t,o){return t in l?Object.defineProperty(l,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):l[t]=o,l}var v=function(t,o){var s=O({},t,o.attrs);return r(P,O({},s,{icon:J}),null)};v.displayName="InboxOutlined";v.inheritAttrs=!1;const W={key:0},X={key:1},Y={class:"ant-upload-drag-icon"},Z={class:"ant-upload-text"},te=H({__name:"file-upload",props:C({listType:{default:"text"},api:{default:()=>D},removeOnError:{type:Boolean,default:!0},showSuccessMsg:{type:Boolean,default:!0},removeConfirm:{type:Boolean,default:!1},accept:{default:R.join(",")},acceptFormat:{},data:{default:()=>{}},maxCount:{default:1},maxSize:{default:5},disabled:{type:Boolean,default:!1},helpMessage:{type:Boolean,default:!0},multiple:{type:Boolean},directory:{type:Boolean,default:!1},enableDragUpload:{type:Boolean,default:!1},keepMissingId:{type:Boolean},preview:{type:Function,default:F},abortOnUnmounted:{type:Boolean,default:!0},customFilename:{},customThumbUrl:{}},{value:{default:()=>[]},valueModifiers:{}}),emits:C(["success","remove","change"],["update:value"]),setup(l,{emit:t}){const o=l,s=t,n=L(()=>o.enableDragUpload?h.Dragger:h),U=N(l,"value"),{customRequest:w,acceptStr:S,handleChange:x,handleRemove:M,beforeUpload:z,innerFileList:d}=j(o,s,U,"file");return(e,y)=>{const I=q("a-button");return p(),f("div",null,[r(a(n),{"file-list":a(d),"onUpdate:fileList":y[0]||(y[0]=i=>E(d)?d.value=i:null),accept:e.accept,"list-type":e.listType,disabled:e.disabled,directory:e.directory,"max-count":e.maxCount,progress:{showInfo:!0},multiple:e.multiple,"before-upload":a(z),"custom-request":a(w),onPreview:e.preview,onChange:a(x),onRemove:a(M)},{default:u(()=>{var i;return[!e.enableDragUpload&&((i=a(d))==null?void 0:i.length)<e.maxCount?(p(),f("div",W,[r(I,{disabled:e.disabled},{default:u(()=>[r(a(k)),A(" "+c(a(B)("component.upload.upload")),1)]),_:1},8,["disabled"])])):b("",!0),e.enableDragUpload?(p(),f("div",X,[m("p",Y,[r(a(v))]),m("p",Z,c(a(B)("component.upload.clickOrDrag")),1)])):b("",!0)]}),_:1},8,["file-list","accept","list-type","disabled","directory","max-count","multiple","before-upload","custom-request","onPreview","onChange","onRemove"]),T(e.$slots,"helpMessage",_($({maxCount:e.maxCount,disabled:e.disabled,maxSize:e.maxSize,accept:e.accept})),()=>[e.helpMessage?(p(),G(a(V),{key:0,scope:"global",keypath:"component.upload.uploadHelpMessage",tag:"div",class:g(["mt-2",{"upload-text__disabled":e.disabled}])},{size:u(()=>[m("span",{class:g(["text-primary mx-1 font-medium",{"upload-text__disabled":e.disabled}])},c(e.maxSize)+"MB ",3)]),ext:u(()=>[m("span",{class:g(["text-primary mx-1 font-medium",{"upload-text__disabled":e.disabled}])},c(a(S)),3)]),_:1},8,["class"])):b("",!0)])])}}});export{te as _};
