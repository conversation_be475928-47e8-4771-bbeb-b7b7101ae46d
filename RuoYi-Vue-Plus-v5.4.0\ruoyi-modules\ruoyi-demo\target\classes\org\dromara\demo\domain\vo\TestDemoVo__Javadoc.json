{"doc": " 测试单表视图对象 test_demo\n\n <AUTHOR>\n @date 2021-07-26\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "deptId", "doc": " 部门id\n"}, {"name": "userId", "doc": " 用户id\n"}, {"name": "orderNum", "doc": " 排序号\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " key键\n"}, {"name": "value", "doc": " 值\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "createBy", "doc": " 创建人\n"}, {"name": "createByName", "doc": " 创建人账号\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "updateBy", "doc": " 更新人\n"}, {"name": "updateByName", "doc": " 更新人账号\n"}], "enumConstants": [], "methods": [], "constructors": []}