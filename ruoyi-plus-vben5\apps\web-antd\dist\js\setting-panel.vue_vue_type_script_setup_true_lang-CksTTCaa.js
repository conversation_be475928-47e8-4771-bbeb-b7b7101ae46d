import m from"./account-bind-DVRafLmQ.js";import{_ as c}from"./base-setting.vue_vue_type_script_setup_true_lang-BL_DVXFu.js";import{_ as p}from"./online-device.vue_vue_type_script_setup_true_lang-DS8ioiKf.js";import{_}from"./secure-setting.vue_vue_type_script_setup_true_lang-D2jcoVG-.js";import{T as f,a as i}from"./index-BaVK9zYh.js";import{d as l,h as n,o as a,w as o,c as u,F as d,K as k,a as b,b as t,L as g,O as y}from"../jse/index-index-C-MnMZEz.js";const w=l({__name:"setting-panel",setup(x){const r=[{component:c,key:"1",name:"基本设置"},{component:_,key:"2",name:"安全设置"},{component:m,key:"3",name:"账号绑定"},{component:p,key:"4",name:"在线设备"}];return(s,B)=>(a(),n(t(i),{class:"bg-background rounded-[var(--radius)] px-[16px] lg:flex-1"},{default:o(()=>[(a(),u(d,null,k(r,e=>b(t(f),{key:e.key,tab:e.name},{default:o(()=>[(a(),n(g(e.component),y({ref_for:!0},s.$attrs),null,16))]),_:2},1032,["tab"])),64))]),_:1}))}});export{w as _};
