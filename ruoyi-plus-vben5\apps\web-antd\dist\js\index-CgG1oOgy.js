import{_ as f}from"./browser.vue_vue_type_script_setup_true_lang-DzJK-mWk.js";import{_ as n}from"./device.vue_vue_type_script_setup_true_lang-npgmt-FO.js";import{_ as c}from"./isp.vue_vue_type_script_setup_true_lang-CUbeNZq9.js";import{_ as l}from"./loginLine.vue_vue_type_script_setup_true_lang-DZBSKPAL.js";import{_ as d}from"./map.vue_vue_type_script_setup_true_lang-BHiKhZG1.js";import{a as p}from"./index-BaVK9zYh.js";import{d as u,p as b,c as y,o as t,a,w as e,h as i,b as o}from"../jse/index-index-C-MnMZEz.js";import"./api-CwjVPMpk.js";import"./bootstrap-DCMzVRvD.js";import"./use-echarts-CF-NZzbo.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./index-CHpIOV4R.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";const k={class:"pt-[16px]"},G=u({__name:"index",setup(v){const r=p.TabPane,m=b(1);return(x,s)=>(t(),y("div",k,[a(o(p),{activeKey:m.value,"onUpdate:activeKey":s[0]||(s[0]=_=>m.value=_),class:"h-full","tab-position":"left"},{default:e(()=>[(t(),i(o(r),{key:1,tab:"访问量数据"},{default:e(()=>[a(d)]),_:1})),(t(),i(o(r),{key:2,tab:"使用设备"},{default:e(()=>[a(n)]),_:1})),(t(),i(o(r),{key:3,tab:"使用浏览器"},{default:e(()=>[a(f)]),_:1})),(t(),i(o(r),{key:4,tab:"登录量"},{default:e(()=>[a(l)]),_:1})),(t(),i(o(r),{key:5,tab:"运营商占比"},{default:e(()=>[a(c)]),_:1}))]),_:1},8,["activeKey"])]))}});export{G as default};
