{"doc": " WebFlux 工具类\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "getOriginalRequestUrl", "paramTypes": ["org.springframework.web.server.ServerWebExchange"], "doc": " 获取原请求路径\n"}, {"name": "isJsonRequest", "paramTypes": ["org.springframework.web.server.ServerWebExchange"], "doc": " 是否是Json请求\n\n @param exchange HTTP请求\n"}, {"name": "resolveBodyFromRequest", "paramTypes": ["org.springframework.http.server.reactive.ServerHttpRequest"], "doc": " 读取request内的body\n\n 注意一个request只能读取一次 读取之后需要重新包装\n"}, {"name": "resolveBodyFromCacheRequest", "paramTypes": ["org.springframework.web.server.ServerWebExchange"], "doc": " 从缓存中读取request内的body\n\n 注意要求经过 {@link ServerWebExchangeUtils#cacheRequestBody(ServerWebExchange, Function)} 此方法创建缓存\n 框架内已经使用 {@link WebCacheRequestFilter} 全局创建了body缓存\n\n @return body\n"}, {"name": "webFluxResponseWriter", "paramTypes": ["org.springframework.http.server.reactive.ServerHttpResponse", "java.lang.Object"], "doc": " 设置webflux模型响应\n\n @param response ServerHttpResponse\n @param value    响应内容\n @return Mono<Void>\n"}, {"name": "webFluxResponseWriter", "paramTypes": ["org.springframework.http.server.reactive.ServerHttpResponse", "java.lang.Object", "int"], "doc": " 设置webflux模型响应\n\n @param response ServerHttpResponse\n @param code     响应状态码\n @param value    响应内容\n @return Mono<Void>\n"}, {"name": "webFluxResponseWriter", "paramTypes": ["org.springframework.http.server.reactive.ServerHttpResponse", "org.springframework.http.HttpStatus", "java.lang.Object", "int"], "doc": " 设置webflux模型响应\n\n @param response ServerHttpResponse\n @param status   http状态码\n @param code     响应状态码\n @param value    响应内容\n @return Mono<Void>\n"}, {"name": "webFluxResponseWriter", "paramTypes": ["org.springframework.http.server.reactive.ServerHttpResponse", "java.lang.String", "org.springframework.http.HttpStatus", "java.lang.Object", "int"], "doc": " 设置webflux模型响应\n\n @param response    ServerHttpResponse\n @param contentType content-type\n @param status      http状态码\n @param code        响应状态码\n @param value       响应内容\n @return Mono<Void>\n"}], "constructors": []}