{"doc": " 自定义 Date 类型反序列化处理器（支持多种格式）\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "deserialize", "paramTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.databind.DeserializationContext"], "doc": " 反序列化逻辑：将字符串转换为 Date 对象\n\n @param p    JSON 解析器，用于获取字符串值\n @param ctxt 上下文环境（可用于获取更多配置）\n @return 转换后的 Date 对象，若为空字符串返回 null\n @throws IOException 当字符串格式非法或转换失败时抛出\n"}], "constructors": []}