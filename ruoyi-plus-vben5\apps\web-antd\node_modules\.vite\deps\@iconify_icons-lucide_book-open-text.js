import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// ../../node_modules/.pnpm/@iconify+icons-lucide@1.2.135/node_modules/@iconify/icons-lucide/book-open-text.js
var require_book_open_text = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-lucide@1.2.135/node_modules/@iconify/icons-lucide/book-open-text.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2zm20 0h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7zM6 8h2m-2 4h2m8-4h2m-2 4h2"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_book_open_text();
//# sourceMappingURL=@iconify_icons-lucide_book-open-text.js.map
