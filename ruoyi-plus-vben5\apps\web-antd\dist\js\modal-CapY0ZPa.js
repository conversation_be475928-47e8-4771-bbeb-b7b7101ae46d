var y=Object.defineProperty,h=Object.defineProperties;var F=Object.getOwnPropertyDescriptors;var u=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var f=(e,o,t)=>o in e?y(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,p=(e,o)=>{for(var t in o||(o={}))I.call(o,t)&&f(e,t,o[t]);if(u)for(var t of u(o))V.call(o,t)&&f(e,t,o[t]);return e},v=(e,o)=>h(e,F(o));var g=(e,o,t)=>new Promise((a,i)=>{var s=r=>{try{n(t.next(r))}catch(l){i(l)}},m=r=>{try{n(t.throw(r))}catch(l){i(l)}},n=r=>r.done?a(r.value):Promise.resolve(r.value).then(s,m);n((t=t.apply(e,o)).next())});import{aq as d,an as w,i as P}from"./bootstrap-DCMzVRvD.js";import"./index-BxBCzu2M.js";import{I as k}from"./Search-ClCped_G.js";import{s as x,a as c}from"../jse/index-index-C-MnMZEz.js";import{A as q}from"./index-kC0HFDdy.js";function E(e){const o=e.placeholder||"输入'确认删除'",t=e.confirmText||"确认删除",a=x({content:""}),i=x({content:[{message:"校验不通过",required:!0,trigger:"change",validator(r,l){return l!==t?Promise.reject(new Error("校验不通过")):Promise.resolve()}}]}),s=d.useForm,{validate:m,validateInfos:n}=s(a,i);w.confirm(v(p({},e),{centered:!0,content:c("div",{class:"flex flex-col gap-[8px]"},[c(q,{message:"确认删除后将无法恢复，请谨慎操作！",type:"error"},null),c(d,{layout:"vertical",model:a},{default:()=>[c(d.Item,n.content,{default:()=>[c(k,{placeholder:o,value:a.content,"onUpdate:value":r=>a.content=r},null)]})]})]),okButtonProps:{danger:!0,type:"primary"},onOk:()=>g(null,null,function*(){yield m(),P(e.onValidated)&&e.onValidated()}),title:"提示",type:"warning"}))}export{E as c};
