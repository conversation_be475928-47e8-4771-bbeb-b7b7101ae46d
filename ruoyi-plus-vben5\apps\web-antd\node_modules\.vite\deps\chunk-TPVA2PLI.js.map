{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.16_typescript@5.8.3_/node_modules/ant-design-vue/es/button/buttonTypes.js"], "sourcesContent": ["import PropTypes from '../_util/vue-types';\nimport { eventType } from '../_util/type';\nexport function convertLegacyProps(type) {\n  if (type === 'danger') {\n    return {\n      danger: true\n    };\n  }\n  return {\n    type\n  };\n}\nexport const buttonProps = () => ({\n  prefixCls: String,\n  type: String,\n  htmlType: {\n    type: String,\n    default: 'button'\n  },\n  shape: {\n    type: String\n  },\n  size: {\n    type: String\n  },\n  loading: {\n    type: [Boolean, Object],\n    default: () => false\n  },\n  disabled: {\n    type: Boolean,\n    default: undefined\n  },\n  ghost: {\n    type: Boolean,\n    default: undefined\n  },\n  block: {\n    type: Boolean,\n    default: undefined\n  },\n  danger: {\n    type: Boolean,\n    default: undefined\n  },\n  icon: PropTypes.any,\n  href: String,\n  target: String,\n  title: String,\n  onClick: eventType(),\n  onMousedown: eventType()\n});\nexport default buttonProps;"], "mappings": ";;;;;;;;AAEO,SAAS,mBAAmB,MAAM;AACvC,MAAI,SAAS,UAAU;AACrB,WAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,EACF;AACF;AACO,IAAM,cAAc,OAAO;AAAA,EAChC,WAAW;AAAA,EACX,MAAM;AAAA,EACN,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS,MAAM;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM,kBAAU;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS,UAAU;AAAA,EACnB,aAAa,UAAU;AACzB;AACA,IAAO,sBAAQ;", "names": []}