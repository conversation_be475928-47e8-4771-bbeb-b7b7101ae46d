{"doc": " <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "rabbitSend", "paramTypes": [], "doc": " rabbitmq 普通消息\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["long"], "doc": " rabbitmq 延迟队列消息\n"}, {"name": "rocketSend", "paramTypes": [], "doc": " rocketmq 发送消息\n 需要手动创建相关的Topic和group\n"}, {"name": "rocketTransaction", "paramTypes": [], "doc": " rocketmq 事务消息\n"}, {"name": "kafkaSend", "paramTypes": [], "doc": " kafka 发送消息\n"}], "constructors": []}