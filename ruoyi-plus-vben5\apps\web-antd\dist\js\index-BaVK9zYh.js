import{cN as me,c as ie,aM as q,_ as T,bO as F,h as we,cR as k,cg as vt,g as te,bj as ge,d7 as he,aY as bt,j as pt,m as ft,r as et,c4 as ht,aF as tt,e as at,aE as mt,aJ as gt,aX as $t,d6 as yt,bx as ke,p as xt,bd as Xe,P as St,aW as Ct,bP as _t,bk as xe,o as Tt,cx as wt}from"./bootstrap-DCMzVRvD.js";import{C as W,ac as Me,d as le,p as J,B as H,a as m,D as _e,v as Te,q as oe,a5 as Pt,a4 as Rt}from"../jse/index-index-C-MnMZEz.js";import{u as Et,E as It,M as Bt,a as Lt}from"./index-Bg2oL4a6.js";import{D as At}from"./Dropdown-BOZk78PH.js";import{R as je}from"./index-CHpIOV4R.js";import{p as nt}from"./pick-CyUZAAhv.js";import{i as kt}from"./isMobile-8sZ0LT6r.js";import{i as Fe}from"./slide-B82O6h2Y.js";function Dt(e){const t=W(),a=W(!1);function i(){for(var o=arguments.length,l=new Array(o),n=0;n<o;n++)l[n]=arguments[n];a.value||(me.cancel(t.value),t.value=me(()=>{e(...l)}))}return Me(()=>{a.value=!0,me.cancel(t.value)}),i}function Mt(e){const t=W([]),a=W(typeof e=="function"?e():e),i=Dt(()=>{let l=a.value;t.value.forEach(n=>{l=n(l)}),t.value=[],a.value=l});function o(l){t.value.push(l),i()}return[a,o]}const Nt=le({compatConfig:{MODE:3},name:"TabNode",props:{id:{type:String},prefixCls:{type:String},tab:{type:Object},active:{type:Boolean},closable:{type:Boolean},editable:{type:Object},onClick:{type:Function},onResize:{type:Function},renderWrapper:{type:Function},removeAriaLabel:{type:String},onFocus:{type:Function}},emits:["click","resize","remove","focus"],setup(e,t){let{expose:a,attrs:i}=t;const o=J();function l(f){var u;!((u=e.tab)===null||u===void 0)&&u.disabled||e.onClick(f)}a({domRef:o});function n(f){var u;f.preventDefault(),f.stopPropagation(),e.editable.onEdit("remove",{key:(u=e.tab)===null||u===void 0?void 0:u.key,event:f})}const p=H(()=>{var f;return e.editable&&e.closable!==!1&&!(!((f=e.tab)===null||f===void 0)&&f.disabled)});return()=>{var f;const{prefixCls:u,id:b,active:C,tab:{key:h,tab:s,disabled:y,closeIcon:x},renderWrapper:w,removeAriaLabel:_,editable:D,onFocus:K}=e,M=`${u}-tab`,r=m("div",{key:h,ref:o,class:ie(M,{[`${M}-with-remove`]:p.value,[`${M}-active`]:C,[`${M}-disabled`]:y}),style:i.style,onClick:l},[m("div",{role:"tab","aria-selected":C,id:b&&`${b}-tab-${h}`,class:`${M}-btn`,"aria-controls":b&&`${b}-panel-${h}`,"aria-disabled":y,tabindex:y?null:0,onClick:g=>{g.stopPropagation(),l(g)},onKeydown:g=>{[q.SPACE,q.ENTER].includes(g.which)&&(g.preventDefault(),l(g))},onFocus:K},[typeof s=="function"?s():s]),p.value&&m("button",{type:"button","aria-label":_||"remove",tabindex:0,class:`${M}-remove`,onClick:g=>{g.stopPropagation(),n(g)}},[(x==null?void 0:x())||((f=D.removeIcon)===null||f===void 0?void 0:f.call(D))||"×"])]);return w?w(r):r}}}),Ve={width:0,height:0,left:0,top:0};function Ot(e,t){const a=J(new Map);return _e(()=>{var i,o;const l=new Map,n=e.value,p=t.value.get((i=n[0])===null||i===void 0?void 0:i.key)||Ve,f=p.left+p.width;for(let u=0;u<n.length;u+=1){const{key:b}=n[u];let C=t.value.get(b);C||(C=t.value.get((o=n[u-1])===null||o===void 0?void 0:o.key)||Ve);const h=l.get(b)||T({},C);h.right=f-h.left-h.width,l.set(b,h)}a.value=new Map(l)}),a}const ot=le({compatConfig:{MODE:3},name:"AddButton",inheritAttrs:!1,props:{prefixCls:String,editable:{type:Object},locale:{type:Object,default:void 0}},setup(e,t){let{expose:a,attrs:i}=t;const o=J();return a({domRef:o}),()=>{const{prefixCls:l,editable:n,locale:p}=e;return!n||n.showAdd===!1?null:m("button",{ref:o,type:"button",class:`${l}-nav-add`,style:i.style,"aria-label":(p==null?void 0:p.addAriaLabel)||"Add tab",onClick:f=>{n.onEdit("add",{event:f})}},[n.addIcon?n.addIcon():"+"])}}}),Wt={prefixCls:{type:String},id:{type:String},tabs:{type:Object},rtl:{type:Boolean},tabBarGutter:{type:Number},activeKey:{type:[String,Number]},mobile:{type:Boolean},moreIcon:we.any,moreTransitionName:{type:String},editable:{type:Object},locale:{type:Object,default:void 0},removeAriaLabel:String,onTabClick:{type:Function},popupClassName:String,getPopupContainer:F()},Ht=le({compatConfig:{MODE:3},name:"OperationNode",inheritAttrs:!1,props:Wt,emits:["tabClick"],slots:Object,setup(e,t){let{attrs:a,slots:i}=t;const[o,l]=k(!1),[n,p]=k(null),f=s=>{const y=e.tabs.filter(_=>!_.disabled);let x=y.findIndex(_=>_.key===n.value)||0;const w=y.length;for(let _=0;_<w;_+=1){x=(x+s+w)%w;const D=y[x];if(!D.disabled){p(D.key);return}}},u=s=>{const{which:y}=s;if(!o.value){[q.DOWN,q.SPACE,q.ENTER].includes(y)&&(l(!0),s.preventDefault());return}switch(y){case q.UP:f(-1),s.preventDefault();break;case q.DOWN:f(1),s.preventDefault();break;case q.ESC:l(!1);break;case q.SPACE:case q.ENTER:n.value!==null&&e.onTabClick(n.value,s);break}},b=H(()=>`${e.id}-more-popup`),C=H(()=>n.value!==null?`${b.value}-${n.value}`:null),h=(s,y)=>{s.preventDefault(),s.stopPropagation(),e.editable.onEdit("remove",{key:y,event:s})};return Te(()=>{oe(n,()=>{const s=document.getElementById(C.value);s&&s.scrollIntoView&&s.scrollIntoView(!1)},{flush:"post",immediate:!0})}),oe(o,()=>{o.value||p(null)}),Et({}),()=>{var s;const{prefixCls:y,id:x,tabs:w,locale:_,mobile:D,moreIcon:K=((s=i.moreIcon)===null||s===void 0?void 0:s.call(i))||m(It,null,null),moreTransitionName:M,editable:r,tabBarGutter:g,rtl:c,onTabClick:$,popupClassName:I}=e;if(!w.length)return null;const R=`${y}-dropdown`,G=_==null?void 0:_.dropdownAriaLabel,re={[c?"marginRight":"marginLeft"]:g};w.length||(re.visibility="hidden",re.order=1);const se=ie({[`${R}-rtl`]:c,[`${I}`]:!0}),de=D?null:m(At,{prefixCls:R,trigger:["hover"],visible:o.value,transitionName:M,onVisibleChange:l,overlayClassName:se,mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:e.getPopupContainer},{overlay:()=>m(Bt,{onClick:E=>{let{key:Q,domEvent:N}=E;$(Q,N),l(!1)},id:b.value,tabindex:-1,role:"listbox","aria-activedescendant":C.value,selectedKeys:[n.value],"aria-label":G!==void 0?G:"expanded dropdown"},{default:()=>[w.map(E=>{var Q,N;const V=r&&E.closable!==!1&&!E.disabled;return m(Lt,{key:E.key,id:`${b.value}-${E.key}`,role:"option","aria-controls":x&&`${x}-panel-${E.key}`,disabled:E.disabled},{default:()=>[m("span",null,[typeof E.tab=="function"?E.tab():E.tab]),V&&m("button",{type:"button","aria-label":e.removeAriaLabel||"remove",tabindex:0,class:`${R}-menu-item-remove`,onClick:Y=>{Y.stopPropagation(),h(Y,E.key)}},[((Q=E.closeIcon)===null||Q===void 0?void 0:Q.call(E))||((N=r.removeIcon)===null||N===void 0?void 0:N.call(r))||"×"])]})})]}),default:()=>m("button",{type:"button",class:`${y}-nav-more`,style:re,tabindex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":b.value,id:`${x}-more`,"aria-expanded":o.value,onKeydown:u},[K])});return m("div",{class:ie(`${y}-nav-operations`,a.class),style:a.style},[de,m(ot,{prefixCls:y,locale:_,editable:r},null)])}}}),it=Symbol("tabsContextKey"),zt=e=>{Rt(it,e)},lt=()=>Pt(it,{tabs:J([]),prefixCls:J()}),Kt=.1,Ye=.01,Se=20,Ue=Math.pow(.995,Se);function Gt(e,t){const[a,i]=k(),[o,l]=k(0),[n,p]=k(0),[f,u]=k(),b=J();function C(r){const{screenX:g,screenY:c}=r.touches[0];i({x:g,y:c}),clearInterval(b.value)}function h(r){if(!a.value)return;r.preventDefault();const{screenX:g,screenY:c}=r.touches[0],$=g-a.value.x,I=c-a.value.y;t($,I),i({x:g,y:c});const R=Date.now();p(R-o.value),l(R),u({x:$,y:I})}function s(){if(!a.value)return;const r=f.value;if(i(null),u(null),r){const g=r.x/n.value,c=r.y/n.value,$=Math.abs(g),I=Math.abs(c);if(Math.max($,I)<Kt)return;let R=g,G=c;b.value=setInterval(()=>{if(Math.abs(R)<Ye&&Math.abs(G)<Ye){clearInterval(b.value);return}R*=Ue,G*=Ue,t(R*Se,G*Se)},Se)}}const y=J();function x(r){const{deltaX:g,deltaY:c}=r;let $=0;const I=Math.abs(g),R=Math.abs(c);I===R?$=y.value==="x"?g:c:I>R?($=g,y.value="x"):($=c,y.value="y"),t(-$,-$)&&r.preventDefault()}const w=J({onTouchStart:C,onTouchMove:h,onTouchEnd:s,onWheel:x});function _(r){w.value.onTouchStart(r)}function D(r){w.value.onTouchMove(r)}function K(r){w.value.onTouchEnd(r)}function M(r){w.value.onWheel(r)}Te(()=>{var r,g;document.addEventListener("touchmove",D,{passive:!1}),document.addEventListener("touchend",K,{passive:!1}),(r=e.value)===null||r===void 0||r.addEventListener("touchstart",_,{passive:!1}),(g=e.value)===null||g===void 0||g.addEventListener("wheel",M,{passive:!1})}),Me(()=>{document.removeEventListener("touchmove",D),document.removeEventListener("touchend",K)})}function Ze(e,t){const a=J(e);function i(o){const l=typeof o=="function"?o(a.value):o;l!==a.value&&t(l,a.value),a.value=l}return[a,i]}const qe={width:0,height:0,left:0,top:0,right:0},Xt=()=>({id:{type:String},tabPosition:{type:String},activeKey:{type:[String,Number]},rtl:{type:Boolean},animated:ge(),editable:ge(),moreIcon:we.any,moreTransitionName:{type:String},mobile:{type:Boolean},tabBarGutter:{type:Number},renderTabBar:{type:Function},locale:ge(),popupClassName:String,getPopupContainer:F(),onTabClick:{type:Function},onTabScroll:{type:Function}}),jt=(e,t)=>{const{offsetWidth:a,offsetHeight:i,offsetTop:o,offsetLeft:l}=e,{width:n,height:p,x:f,y:u}=e.getBoundingClientRect();return Math.abs(n-a)<1?[n,p,f-t.x,u-t.y]:[a,i,l,o]},Je=le({compatConfig:{MODE:3},name:"TabNavList",inheritAttrs:!1,props:Xt(),slots:Object,emits:["tabClick","tabScroll"],setup(e,t){let{attrs:a,slots:i}=t;const{tabs:o,prefixCls:l}=lt(),n=W(),p=W(),f=W(),u=W(),[b,C]=vt(),h=H(()=>e.tabPosition==="top"||e.tabPosition==="bottom"),[s,y]=Ze(0,(v,d)=>{h.value&&e.onTabScroll&&e.onTabScroll({direction:v>d?"left":"right"})}),[x,w]=Ze(0,(v,d)=>{!h.value&&e.onTabScroll&&e.onTabScroll({direction:v>d?"top":"bottom"})}),[_,D]=k(0),[K,M]=k(0),[r,g]=k(null),[c,$]=k(null),[I,R]=k(0),[G,re]=k(0),[se,de]=Mt(new Map),E=Ot(o,se),Q=H(()=>`${l.value}-nav-operations-hidden`),N=W(0),V=W(0);_e(()=>{h.value?e.rtl?(N.value=0,V.value=Math.max(0,_.value-r.value)):(N.value=Math.min(0,r.value-_.value),V.value=0):(N.value=Math.min(0,c.value-K.value),V.value=0)});const Y=v=>v<N.value?N.value:v>V.value?V.value:v,ue=W(),[z,ve]=k(),be=()=>{ve(Date.now())},pe=()=>{clearTimeout(ue.value)},$e=(v,d)=>{v(S=>Y(S+d))};Gt(n,(v,d)=>{if(h.value){if(r.value>=_.value)return!1;$e(y,v)}else{if(c.value>=K.value)return!1;$e(w,d)}return pe(),be(),!0}),oe(z,()=>{pe(),z.value&&(ue.value=setTimeout(()=>{ve(0)},100))});const ce=function(){let v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activeKey;const d=E.value.get(v)||{width:0,height:0,left:0,right:0,top:0};if(h.value){let S=s.value;e.rtl?d.right<s.value?S=d.right:d.right+d.width>s.value+r.value&&(S=d.right+d.width-r.value):d.left<-s.value?S=-d.left:d.left+d.width>-s.value+r.value&&(S=-(d.left+d.width-r.value)),w(0),y(Y(S))}else{let S=x.value;d.top<-x.value?S=-d.top:d.top+d.height>-x.value+c.value&&(S=-(d.top+d.height-c.value)),y(0),w(Y(S))}},Pe=W(0),Re=W(0);_e(()=>{let v,d,S,P,L,B;const U=E.value;["top","bottom"].includes(e.tabPosition)?(v="width",P=r.value,L=_.value,B=I.value,d=e.rtl?"right":"left",S=Math.abs(s.value)):(v="height",P=c.value,L=_.value,B=G.value,d="top",S=-x.value);let O=P;L+B>P&&L<P&&(O=P-B);const j=o.value;if(!j.length)return[Pe.value,Re.value]=[0,0];const Z=j.length;let ne=Z;for(let X=0;X<Z;X+=1){const ee=U.get(j[X].key)||qe;if(ee[d]+ee[v]>S+O){ne=X-1;break}}let A=0;for(let X=Z-1;X>=0;X-=1)if((U.get(j[X].key)||qe)[d]<S){A=X+1;break}return[Pe.value,Re.value]=[A,ne]});const Ne=()=>{de(()=>{var v;const d=new Map,S=(v=p.value)===null||v===void 0?void 0:v.getBoundingClientRect();return o.value.forEach(P=>{let{key:L}=P;const B=C.value.get(L),U=(B==null?void 0:B.$el)||B;if(U){const[O,j,Z,ne]=jt(U,S);d.set(L,{width:O,height:j,left:Z,top:ne})}}),d})};oe(()=>o.value.map(v=>v.key).join("%%"),()=>{Ne()},{flush:"post"});const Ee=()=>{var v,d,S,P,L;const B=((v=n.value)===null||v===void 0?void 0:v.offsetWidth)||0,U=((d=n.value)===null||d===void 0?void 0:d.offsetHeight)||0,O=((S=u.value)===null||S===void 0?void 0:S.$el)||{},j=O.offsetWidth||0,Z=O.offsetHeight||0;g(B),$(U),R(j),re(Z);const ne=(((P=p.value)===null||P===void 0?void 0:P.offsetWidth)||0)-j,A=(((L=p.value)===null||L===void 0?void 0:L.offsetHeight)||0)-Z;D(ne),M(A),Ne()},Oe=H(()=>[...o.value.slice(0,Pe.value),...o.value.slice(Re.value+1)]),[st,ct]=k(),ae=H(()=>E.value.get(e.activeKey)),We=W(),He=()=>{me.cancel(We.value)};oe([ae,h,()=>e.rtl],()=>{const v={};ae.value&&(h.value?(e.rtl?v.right=he(ae.value.right):v.left=he(ae.value.left),v.width=he(ae.value.width)):(v.top=he(ae.value.top),v.height=he(ae.value.height))),He(),We.value=me(()=>{ct(v)})}),oe([()=>e.activeKey,ae,E,h],()=>{ce()},{flush:"post"}),oe([()=>e.rtl,()=>e.tabBarGutter,()=>e.activeKey,()=>o.value],()=>{Ee()},{flush:"post"});const Ie=v=>{let{position:d,prefixCls:S,extra:P}=v;if(!P)return null;const L=P==null?void 0:P({position:d});return L?m("div",{class:`${S}-extra-content`},[L]):null};return Me(()=>{pe(),He()}),()=>{const{id:v,animated:d,activeKey:S,rtl:P,editable:L,locale:B,tabPosition:U,tabBarGutter:O,onTabClick:j}=e,{class:Z,style:ne}=a,A=l.value,X=!!Oe.value.length,ee=`${A}-nav-wrap`;let Be,Le,ze,Ke;h.value?P?(Le=s.value>0,Be=s.value+r.value<_.value):(Be=s.value<0,Le=-s.value+r.value<_.value):(ze=x.value<0,Ke=-x.value+c.value<K.value);const ye={};U==="top"||U==="bottom"?ye[P?"marginRight":"marginLeft"]=typeof O=="number"?`${O}px`:O:ye.marginTop=typeof O=="number"?`${O}px`:O;const Ge=o.value.map((Ae,dt)=>{const{key:fe}=Ae;return m(Nt,{id:v,prefixCls:A,key:fe,tab:Ae,style:dt===0?void 0:ye,closable:Ae.closable,editable:L,active:fe===S,removeAriaLabel:B==null?void 0:B.removeAriaLabel,ref:b(fe),onClick:ut=>{j(fe,ut)},onFocus:()=>{ce(fe),be(),n.value&&(P||(n.value.scrollLeft=0),n.value.scrollTop=0)}},i)});return m("div",{role:"tablist",class:ie(`${A}-nav`,Z),style:ne,onKeydown:()=>{be()}},[m(Ie,{position:"left",prefixCls:A,extra:i.leftExtra},null),m(je,{onResize:Ee},{default:()=>[m("div",{class:ie(ee,{[`${ee}-ping-left`]:Be,[`${ee}-ping-right`]:Le,[`${ee}-ping-top`]:ze,[`${ee}-ping-bottom`]:Ke}),ref:n},[m(je,{onResize:Ee},{default:()=>[m("div",{ref:p,class:`${A}-nav-list`,style:{transform:`translate(${s.value}px, ${x.value}px)`,transition:z.value?"none":void 0}},[Ge,m(ot,{ref:u,prefixCls:A,locale:B,editable:L,style:T(T({},Ge.length===0?void 0:ye),{visibility:X?"hidden":null})},null),m("div",{class:ie(`${A}-ink-bar`,{[`${A}-ink-bar-animated`]:d.inkBar}),style:st.value},null)])]})])]}),m(Ht,te(te({},e),{},{removeAriaLabel:B==null?void 0:B.removeAriaLabel,ref:f,prefixCls:A,tabs:Oe.value,class:!X&&Q.value}),nt(i,["moreIcon"])),m(Ie,{position:"right",prefixCls:A,extra:i.rightExtra},null),m(Ie,{position:"right",prefixCls:A,extra:i.tabBarExtraContent},null)])}}}),Ft=le({compatConfig:{MODE:3},name:"TabPanelList",inheritAttrs:!1,props:{activeKey:{type:[String,Number]},id:{type:String},rtl:{type:Boolean},animated:{type:Object,default:void 0},tabPosition:{type:String},destroyInactiveTabPane:{type:Boolean}},setup(e){const{tabs:t,prefixCls:a}=lt();return()=>{const{id:i,activeKey:o,animated:l,tabPosition:n,rtl:p,destroyInactiveTabPane:f}=e,u=l.tabPane,b=a.value,C=t.value.findIndex(h=>h.key===o);return m("div",{class:`${b}-content-holder`},[m("div",{class:[`${b}-content`,`${b}-content-${n}`,{[`${b}-content-animated`]:u}],style:C&&u?{[p?"marginRight":"marginLeft"]:`-${C}00%`}:null},[t.value.map(h=>bt(h.node,{key:h.key,prefixCls:b,tabKey:h.key,id:i,animated:u,active:h.key===o,destroyInactiveTabPane:f}))])])}}}),Vt=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[Fe(e,"slide-up"),Fe(e,"slide-down")]]},Yt=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeadBackground:i,tabsCardGutter:o,colorSplit:l}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:i,border:`${e.lineWidth}px ${e.lineType} ${l}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:e.colorPrimary,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:`${o}px`}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:`${o}px`}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadiusLG}px 0 0 ${e.borderRadiusLG}px`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},Ut=e=>{const{componentCls:t,tabsHoverColor:a,dropdownEdgeChildVerticalPadding:i}=e;return{[`${t}-dropdown`]:T(T({},et(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${i}px 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":T(T({},ht),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${e.paddingXXS}px ${e.paddingSM}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Zt=e=>{const{componentCls:t,margin:a,colorSplit:i}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:`0 0 ${a}px 0`,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${e.lineWidth}px ${e.lineType} ${i}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:`${a}px`,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:e.controlHeight*1.25,[`${t}-tab`]:{padding:`${e.paddingXS}px ${e.paddingLG}px`,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:`${e.margin}px 0 0 0`},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:`-${e.lineWidth}px`},borderLeft:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:-e.lineWidth},borderRight:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},qt=e=>{const{componentCls:t,padding:a}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px 0`,fontSize:e.fontSize}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${a}px 0`,fontSize:e.fontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXXS*1.5}px ${a}px`}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${e.borderRadius}px ${e.borderRadius}px`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${e.borderRadius}px ${e.borderRadius}px 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadius}px ${e.borderRadius}px 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadius}px 0 0 ${e.borderRadius}px`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px ${a}px ${e.paddingXXS*1.5}px`}}}}}},Jt=e=>{const{componentCls:t,tabsActiveColor:a,tabsHoverColor:i,iconCls:o,tabsHorizontalGutter:l}=e,n=`${t}-tab`;return{[n]:{position:"relative",display:"inline-flex",alignItems:"center",padding:`${e.paddingSM}px 0`,fontSize:`${e.fontSize}px`,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":T({"&:focus:not(:focus-visible), &:active":{color:a}},tt(e)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-e.marginXXS},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:i},[`&${n}-active ${n}-btn`]:{color:e.colorPrimary,textShadow:e.tabsActiveTextShadow},[`&${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${n}-disabled ${n}-btn, &${n}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${n}-remove ${o}`]:{margin:0},[o]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${n} + ${n}`]:{margin:{_skip_check_:!0,value:`0 0 0 ${l}px`}}}},Qt=e=>{const{componentCls:t,tabsHorizontalGutter:a,iconCls:i,tabsCardGutter:o}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:`0 0 0 ${a}px`},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[i]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:`${e.marginSM}px`}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:`${e.marginXS}px`},marginLeft:{_skip_check_:!0,value:`-${e.marginXXS}px`},[i]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:`${o}px`},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ea=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeight:i,tabsCardGutter:o,tabsHoverColor:l,tabsActiveColor:n,colorSplit:p}=e;return{[t]:T(T(T(T({},et(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:T({minWidth:`${i}px`,marginLeft:{_skip_check_:!0,value:`${o}px`},padding:`0 ${e.paddingXS}px`,background:"transparent",border:`${e.lineWidth}px ${e.lineType} ${p}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:l},"&:active, &:focus:not(:focus-visible)":{color:n}},tt(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.colorPrimary,pointerEvents:"none"}}),Jt(e)),{[`${t}-content`]:{position:"relative",display:"flex",width:"100%","&-animated":{transition:"margin 0.3s"}},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none",flex:"none",width:"100%"}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},ta=pt("Tabs",e=>{const t=e.controlHeightLG,a=ft(e,{tabsHoverColor:e.colorPrimaryHover,tabsActiveColor:e.colorPrimaryActive,tabsCardHorizontalPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,tabsCardHeight:t,tabsCardGutter:e.marginXXS/2,tabsHorizontalGutter:32,tabsCardHeadBackground:e.colorFillAlter,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120});return[qt(a),Qt(a),Zt(a),Ut(a),Yt(a),ea(a),Vt(a)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));let Qe=0;const rt=()=>({prefixCls:{type:String},id:{type:String},popupClassName:String,getPopupContainer:F(),activeKey:{type:[String,Number]},defaultActiveKey:{type:[String,Number]},direction:xe(),animated:wt([Boolean,Object]),renderTabBar:F(),tabBarGutter:{type:Number},tabBarStyle:ge(),tabPosition:xe(),destroyInactiveTabPane:Tt(),hideAdd:Boolean,type:xe(),size:xe(),centered:Boolean,onEdit:F(),onChange:F(),onTabClick:F(),onTabScroll:F(),"onUpdate:activeKey":F(),locale:ge(),onPrevClick:F(),onNextClick:F(),tabBarExtraContent:we.any});function aa(e){return e.map(t=>{if($t(t)){const a=T({},t.props||{});for(const[h,s]of Object.entries(a))delete a[h],a[yt(h)]=s;const i=t.children||{},o=t.key!==void 0?t.key:void 0,{tab:l=i.tab,disabled:n,forceRender:p,closable:f,animated:u,active:b,destroyInactiveTabPane:C}=a;return T(T({key:o},a),{node:t,closeIcon:i.closeIcon,tab:l,disabled:n===""||n,forceRender:p===""||p,closable:f===""||f,animated:u===""||u,active:b===""||b,destroyInactiveTabPane:C===""||C})}return null}).filter(t=>t)}const na=le({compatConfig:{MODE:3},name:"InternalTabs",inheritAttrs:!1,props:T(T({},at(rt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}})),{tabs:_t()}),slots:Object,setup(e,t){let{attrs:a,slots:i}=t;ke(e.onPrevClick===void 0&&e.onNextClick===void 0,"Tabs","`onPrevClick / @prevClick` and `onNextClick / @nextClick` has been removed. Please use `onTabScroll / @tabScroll` instead."),ke(e.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` prop has been removed. Please use `rightExtra` slot instead."),ke(i.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` slot is deprecated. Please use `rightExtra` slot instead.");const{prefixCls:o,direction:l,size:n,rootPrefixCls:p,getPopupContainer:f}=xt("tabs",e),[u,b]=ta(o),C=H(()=>l.value==="rtl"),h=H(()=>{const{animated:c,tabPosition:$}=e;return c===!1||["left","right"].includes($)?{inkBar:!1,tabPane:!1}:c===!0?{inkBar:!0,tabPane:!0}:T({inkBar:!0,tabPane:!1},typeof c=="object"?c:{})}),[s,y]=k(!1);Te(()=>{y(kt())});const[x,w]=Xe(()=>{var c;return(c=e.tabs[0])===null||c===void 0?void 0:c.key},{value:H(()=>e.activeKey),defaultValue:e.defaultActiveKey}),[_,D]=k(()=>e.tabs.findIndex(c=>c.key===x.value));_e(()=>{var c;let $=e.tabs.findIndex(I=>I.key===x.value);$===-1&&($=Math.max(0,Math.min(_.value,e.tabs.length-1)),w((c=e.tabs[$])===null||c===void 0?void 0:c.key)),D($)});const[K,M]=Xe(null,{value:H(()=>e.id)}),r=H(()=>s.value&&!["left","right"].includes(e.tabPosition)?"top":e.tabPosition);Te(()=>{e.id||(M(`rc-tabs-${Qe}`),Qe+=1)});const g=(c,$)=>{var I,R;(I=e.onTabClick)===null||I===void 0||I.call(e,c,$);const G=c!==x.value;w(c),G&&((R=e.onChange)===null||R===void 0||R.call(e,c))};return zt({tabs:H(()=>e.tabs),prefixCls:o}),()=>{const{id:c,type:$,tabBarGutter:I,tabBarStyle:R,locale:G,destroyInactiveTabPane:re,renderTabBar:se=i.renderTabBar,onTabScroll:de,hideAdd:E,centered:Q}=e,N={id:K.value,activeKey:x.value,animated:h.value,tabPosition:r.value,rtl:C.value,mobile:s.value};let V;$==="editable-card"&&(V={onEdit:(ve,be)=>{let{key:pe,event:$e}=be;var ce;(ce=e.onEdit)===null||ce===void 0||ce.call(e,ve==="add"?$e:pe,ve)},removeIcon:()=>m(Ct,null,null),addIcon:i.addIcon?i.addIcon:()=>m(St,null,null),showAdd:E!==!0});let Y;const ue=T(T({},N),{moreTransitionName:`${p.value}-slide-up`,editable:V,locale:G,tabBarGutter:I,onTabClick:g,onTabScroll:de,style:R,getPopupContainer:f.value,popupClassName:ie(e.popupClassName,b.value)});se?Y=se(T(T({},ue),{DefaultTabBar:Je})):Y=m(Je,ue,nt(i,["moreIcon","leftExtra","rightExtra","tabBarExtraContent"]));const z=o.value;return u(m("div",te(te({},a),{},{id:c,class:ie(z,`${z}-${r.value}`,{[b.value]:!0,[`${z}-${n.value}`]:n.value,[`${z}-card`]:["card","editable-card"].includes($),[`${z}-editable-card`]:$==="editable-card",[`${z}-centered`]:Q,[`${z}-mobile`]:s.value,[`${z}-editable`]:$==="editable-card",[`${z}-rtl`]:C.value},a.class)}),[Y,m(Ft,te(te({destroyInactiveTabPane:re},N),{},{animated:h.value}),null)]))}}}),Ce=le({compatConfig:{MODE:3},name:"ATabs",inheritAttrs:!1,props:at(rt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}}),slots:Object,setup(e,t){let{attrs:a,slots:i,emit:o}=t;const l=n=>{o("update:activeKey",n),o("change",n)};return()=>{var n;const p=aa(mt((n=i.default)===null||n===void 0?void 0:n.call(i)));return m(na,te(te(te({},gt(e,["onUpdate:activeKey"])),a),{},{onChange:l,tabs:p}),i)}}}),oa=()=>({tab:we.any,disabled:{type:Boolean},forceRender:{type:Boolean},closable:{type:Boolean},animated:{type:Boolean},active:{type:Boolean},destroyInactiveTabPane:{type:Boolean},prefixCls:{type:String},tabKey:{type:[String,Number]},id:{type:String}}),De=le({compatConfig:{MODE:3},name:"ATabPane",inheritAttrs:!1,__ANT_TAB_PANE:!0,props:oa(),slots:Object,setup(e,t){let{attrs:a,slots:i}=t;const o=J(e.forceRender);oe([()=>e.active,()=>e.destroyInactiveTabPane],()=>{e.active?o.value=!0:e.destroyInactiveTabPane&&(o.value=!1)},{immediate:!0});const l=H(()=>e.active?{}:e.animated?{visibility:"hidden",height:0,overflowY:"hidden"}:{display:"none"});return()=>{var n;const{prefixCls:p,forceRender:f,id:u,active:b,tabKey:C}=e;return m("div",{id:u&&`${u}-panel-${C}`,role:"tabpanel",tabindex:b?0:-1,"aria-labelledby":u&&`${u}-tab-${C}`,"aria-hidden":!b,style:[l.value,a.style],class:[`${p}-tabpane`,b&&`${p}-tabpane-active`,a.class]},[(b||o.value||f)&&((n=i.default)===null||n===void 0?void 0:n.call(i))])}}});Ce.TabPane=De;Ce.install=function(e){return e.component(Ce.name,Ce),e.component(De.name,De),e};export{De as T,Ce as a};
