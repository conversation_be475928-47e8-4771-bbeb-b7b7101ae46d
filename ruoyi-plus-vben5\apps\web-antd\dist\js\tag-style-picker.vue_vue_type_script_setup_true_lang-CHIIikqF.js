var ai=Object.defineProperty,oi=Object.defineProperties;var ii=Object.getOwnPropertyDescriptors;var Nn=Object.getOwnPropertySymbols;var li=Object.prototype.hasOwnProperty,si=Object.prototype.propertyIsEnumerable;var Fn=(e,t,r)=>t in e?ai(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Je=(e,t)=>{for(var r in t||(t={}))li.call(t,r)&&Fn(e,r,t[r]);if(Nn)for(var r of Nn(t))si.call(t,r)&&Fn(e,r,t[r]);return e},Qe=(e,t)=>oi(e,ii(t));import{bK as Qr,dQ as ui,bY as ci,dR as to,dC as fi,dS as di,dT as hi,dU as ro,dV as pi,dW as vi,dX as Wn,dD as gi,dY as yi,dZ as mi,d_ as bi,c9 as Ci,d$ as zn,i as xi,e0 as wi,e1 as Si,e2 as _i,e3 as M,ax as Dr,e4 as ki,cp as Ai}from"./bootstrap-DCMzVRvD.js";import{d as he,p as J,s as Ee,q as lt,B as Q,aJ as vt,z as Ei,I as Oi,aK as ie,ab as Ri,at as $e,aB as en,a5 as no,a as Pe,v as Mi,l as Y,c as I,o as O,h as Z,f as j,w as Kn,L as Un,O as Gn,r as Vr,H as ne,F as Ge,j as k,n as le,T as Xt,aC as Ti,a4 as $i,a1 as Pi,aL as Hi,K as Tt,k as ao,t as Yt,aM as gt,aN as yt,m as Li,u as qn,b as Nt}from"../jse/index-index-C-MnMZEz.js";import{a as Ii}from"./data-1kX019oc.js";import{a as Bi}from"./Group-oWwucTzK.js";import"./index-BB2u3Md-.js";import Di from"./index-CIjgbPOA.js";function Vi(e,t,r){if(!Qr(r))return!1;var n=typeof t;return(n=="number"?ui(r)&&ci(t,r.length):n=="string"&&t in r)?to(r[t],e):!1}function ji(e){return fi(function(t,r){var n=-1,a=r.length,o=a>1?r[a-1]:void 0,s=a>2?r[2]:void 0;for(o=e.length>3&&typeof o=="function"?(a--,o):void 0,s&&Vi(r[0],r[1],s)&&(o=a<3?void 0:o,a=1),t=Object(t);++n<a;){var i=r[n];i&&e(t,i,n,o)}return t})}function jr(e,t,r){(r!==void 0&&!to(e[t],r)||r===void 0&&!(t in e))&&di(e,t,r)}function Nr(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function Ni(e){return hi(e,ro(e))}function Fi(e,t,r,n,a,o,s){var i=Nr(e,r),l=Nr(t,r),c=s.get(l);if(c){jr(e,r,c);return}var u=o?o(i,l,r+"",e,t,s):void 0,f=u===void 0;if(f){var d=Wn(l),h=!d&&pi(l),y=!d&&!h&&vi(l);u=l,d||h||y?Wn(i)?u=i:gi(i)?u=yi(i):h?(f=!1,u=mi(l,!0)):y?(f=!1,u=bi(l,!0)):u=[]:Ci(l)||zn(l)?(u=i,zn(i)?u=Ni(i):(!Qr(i)||xi(i))&&(u=wi(l))):f=!1}f&&(s.set(l,u),a(u,l,n,o,s),s.delete(l)),jr(e,r,u)}function oo(e,t,r,n,a){e!==t&&Si(t,function(o,s){if(a||(a=new _i),Qr(o))Fi(e,t,s,r,oo,n,a);else{var i=n?n(Nr(e,s),o,s+"",e,t,a):void 0;i===void 0&&(i=o),jr(e,s,i)}},ro)}var ur=ji(function(e,t,r){oo(e,t,r)});function Zt(e){"@babel/helpers - typeof";return Zt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zt(e)}var Wi=/^\s+/,zi=/\s+$/;function C(e,t){if(e=e||"",t=t||{},e instanceof C)return e;if(!(this instanceof C))return new C(e,t);var r=Ki(e);this._originalInput=e,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=Math.round(100*this._a)/100,this._format=t.format||r.format,this._gradientType=t.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=r.ok}C.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(t.r*299+t.g*587+t.b*114)/1e3},getLuminance:function(){var t=this.toRgb(),r,n,a,o,s,i;return r=t.r/255,n=t.g/255,a=t.b/255,r<=.03928?o=r/12.92:o=Math.pow((r+.055)/1.055,2.4),n<=.03928?s=n/12.92:s=Math.pow((n+.055)/1.055,2.4),a<=.03928?i=a/12.92:i=Math.pow((a+.055)/1.055,2.4),.2126*o+.7152*s+.0722*i},setAlpha:function(t){return this._a=io(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=Yn(this._r,this._g,this._b);return{h:t.h*360,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=Yn(this._r,this._g,this._b),r=Math.round(t.h*360),n=Math.round(t.s*100),a=Math.round(t.v*100);return this._a==1?"hsv("+r+", "+n+"%, "+a+"%)":"hsva("+r+", "+n+"%, "+a+"%, "+this._roundA+")"},toHsl:function(){var t=Xn(this._r,this._g,this._b);return{h:t.h*360,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=Xn(this._r,this._g,this._b),r=Math.round(t.h*360),n=Math.round(t.s*100),a=Math.round(t.l*100);return this._a==1?"hsl("+r+", "+n+"%, "+a+"%)":"hsla("+r+", "+n+"%, "+a+"%, "+this._roundA+")"},toHex:function(t){return Zn(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return Xi(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return this._a==1?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(W(this._r,255)*100)+"%",g:Math.round(W(this._g,255)*100)+"%",b:Math.round(W(this._b,255)*100)+"%",a:this._a}},toPercentageRgbString:function(){return this._a==1?"rgb("+Math.round(W(this._r,255)*100)+"%, "+Math.round(W(this._g,255)*100)+"%, "+Math.round(W(this._b,255)*100)+"%)":"rgba("+Math.round(W(this._r,255)*100)+"%, "+Math.round(W(this._g,255)*100)+"%, "+Math.round(W(this._b,255)*100)+"%, "+this._roundA+")"},toName:function(){return this._a===0?"transparent":this._a<1?!1:ll[Zn(this._r,this._g,this._b,!0)]||!1},toFilter:function(t){var r="#"+Jn(this._r,this._g,this._b,this._a),n=r,a=this._gradientType?"GradientType = 1, ":"";if(t){var o=C(t);n="#"+Jn(o._r,o._g,o._b,o._a)}return"progid:DXImageTransform.Microsoft.gradient("+a+"startColorstr="+r+",endColorstr="+n+")"},toString:function(t){var r=!!t;t=t||this._format;var n=!1,a=this._a<1&&this._a>=0,o=!r&&a&&(t==="hex"||t==="hex6"||t==="hex3"||t==="hex4"||t==="hex8"||t==="name");return o?t==="name"&&this._a===0?this.toName():this.toRgbString():(t==="rgb"&&(n=this.toRgbString()),t==="prgb"&&(n=this.toPercentageRgbString()),(t==="hex"||t==="hex6")&&(n=this.toHexString()),t==="hex3"&&(n=this.toHexString(!0)),t==="hex4"&&(n=this.toHex8String(!0)),t==="hex8"&&(n=this.toHex8String()),t==="name"&&(n=this.toName()),t==="hsl"&&(n=this.toHslString()),t==="hsv"&&(n=this.toHsvString()),n||this.toHexString())},clone:function(){return C(this.toString())},_applyModification:function(t,r){var n=t.apply(null,[this].concat([].slice.call(r)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(Qi,arguments)},brighten:function(){return this._applyModification(el,arguments)},darken:function(){return this._applyModification(tl,arguments)},desaturate:function(){return this._applyModification(Yi,arguments)},saturate:function(){return this._applyModification(Zi,arguments)},greyscale:function(){return this._applyModification(Ji,arguments)},spin:function(){return this._applyModification(rl,arguments)},_applyCombination:function(t,r){return t.apply(null,[this].concat([].slice.call(r)))},analogous:function(){return this._applyCombination(ol,arguments)},complement:function(){return this._applyCombination(nl,arguments)},monochromatic:function(){return this._applyCombination(il,arguments)},splitcomplement:function(){return this._applyCombination(al,arguments)},triad:function(){return this._applyCombination(Qn,[3])},tetrad:function(){return this._applyCombination(Qn,[4])}};C.fromRatio=function(e,t){if(Zt(e)=="object"){var r={};for(var n in e)e.hasOwnProperty(n)&&(n==="a"?r[n]=e[n]:r[n]=kt(e[n]));e=r}return C(e,t)};function Ki(e){var t={r:0,g:0,b:0},r=1,n=null,a=null,o=null,s=!1,i=!1;return typeof e=="string"&&(e=fl(e)),Zt(e)=="object"&&(Me(e.r)&&Me(e.g)&&Me(e.b)?(t=Ui(e.r,e.g,e.b),s=!0,i=String(e.r).substr(-1)==="%"?"prgb":"rgb"):Me(e.h)&&Me(e.s)&&Me(e.v)?(n=kt(e.s),a=kt(e.v),t=qi(e.h,n,a),s=!0,i="hsv"):Me(e.h)&&Me(e.s)&&Me(e.l)&&(n=kt(e.s),o=kt(e.l),t=Gi(e.h,n,o),s=!0,i="hsl"),e.hasOwnProperty("a")&&(r=e.a)),r=io(r),{ok:s,format:e.format||i,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:r}}function Ui(e,t,r){return{r:W(e,255)*255,g:W(t,255)*255,b:W(r,255)*255}}function Xn(e,t,r){e=W(e,255),t=W(t,255),r=W(r,255);var n=Math.max(e,t,r),a=Math.min(e,t,r),o,s,i=(n+a)/2;if(n==a)o=s=0;else{var l=n-a;switch(s=i>.5?l/(2-n-a):l/(n+a),n){case e:o=(t-r)/l+(t<r?6:0);break;case t:o=(r-e)/l+2;break;case r:o=(e-t)/l+4;break}o/=6}return{h:o,s,l:i}}function Gi(e,t,r){var n,a,o;e=W(e,360),t=W(t,100),r=W(r,100);function s(c,u,f){return f<0&&(f+=1),f>1&&(f-=1),f<1/6?c+(u-c)*6*f:f<1/2?u:f<2/3?c+(u-c)*(2/3-f)*6:c}if(t===0)n=a=o=r;else{var i=r<.5?r*(1+t):r+t-r*t,l=2*r-i;n=s(l,i,e+1/3),a=s(l,i,e),o=s(l,i,e-1/3)}return{r:n*255,g:a*255,b:o*255}}function Yn(e,t,r){e=W(e,255),t=W(t,255),r=W(r,255);var n=Math.max(e,t,r),a=Math.min(e,t,r),o,s,i=n,l=n-a;if(s=n===0?0:l/n,n==a)o=0;else{switch(n){case e:o=(t-r)/l+(t<r?6:0);break;case t:o=(r-e)/l+2;break;case r:o=(e-t)/l+4;break}o/=6}return{h:o,s,v:i}}function qi(e,t,r){e=W(e,360)*6,t=W(t,100),r=W(r,100);var n=Math.floor(e),a=e-n,o=r*(1-t),s=r*(1-a*t),i=r*(1-(1-a)*t),l=n%6,c=[r,s,o,o,i,r][l],u=[i,r,r,s,o,o][l],f=[o,o,i,r,r,s][l];return{r:c*255,g:u*255,b:f*255}}function Zn(e,t,r,n){var a=[Ae(Math.round(e).toString(16)),Ae(Math.round(t).toString(16)),Ae(Math.round(r).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function Xi(e,t,r,n,a){var o=[Ae(Math.round(e).toString(16)),Ae(Math.round(t).toString(16)),Ae(Math.round(r).toString(16)),Ae(lo(n))];return a&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1)?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0):o.join("")}function Jn(e,t,r,n){var a=[Ae(lo(n)),Ae(Math.round(e).toString(16)),Ae(Math.round(t).toString(16)),Ae(Math.round(r).toString(16))];return a.join("")}C.equals=function(e,t){return!e||!t?!1:C(e).toRgbString()==C(t).toRgbString()};C.random=function(){return C.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})};function Yi(e,t){t=t===0?0:t||10;var r=C(e).toHsl();return r.s-=t/100,r.s=cr(r.s),C(r)}function Zi(e,t){t=t===0?0:t||10;var r=C(e).toHsl();return r.s+=t/100,r.s=cr(r.s),C(r)}function Ji(e){return C(e).desaturate(100)}function Qi(e,t){t=t===0?0:t||10;var r=C(e).toHsl();return r.l+=t/100,r.l=cr(r.l),C(r)}function el(e,t){t=t===0?0:t||10;var r=C(e).toRgb();return r.r=Math.max(0,Math.min(255,r.r-Math.round(255*-(t/100)))),r.g=Math.max(0,Math.min(255,r.g-Math.round(255*-(t/100)))),r.b=Math.max(0,Math.min(255,r.b-Math.round(255*-(t/100)))),C(r)}function tl(e,t){t=t===0?0:t||10;var r=C(e).toHsl();return r.l-=t/100,r.l=cr(r.l),C(r)}function rl(e,t){var r=C(e).toHsl(),n=(r.h+t)%360;return r.h=n<0?360+n:n,C(r)}function nl(e){var t=C(e).toHsl();return t.h=(t.h+180)%360,C(t)}function Qn(e,t){if(isNaN(t)||t<=0)throw new Error("Argument to polyad must be a positive number");for(var r=C(e).toHsl(),n=[C(e)],a=360/t,o=1;o<t;o++)n.push(C({h:(r.h+o*a)%360,s:r.s,l:r.l}));return n}function al(e){var t=C(e).toHsl(),r=t.h;return[C(e),C({h:(r+72)%360,s:t.s,l:t.l}),C({h:(r+216)%360,s:t.s,l:t.l})]}function ol(e,t,r){t=t||6,r=r||30;var n=C(e).toHsl(),a=360/r,o=[C(e)];for(n.h=(n.h-(a*t>>1)+720)%360;--t;)n.h=(n.h+a)%360,o.push(C(n));return o}function il(e,t){t=t||6;for(var r=C(e).toHsv(),n=r.h,a=r.s,o=r.v,s=[],i=1/t;t--;)s.push(C({h:n,s:a,v:o})),o=(o+i)%1;return s}C.mix=function(e,t,r){r=r===0?0:r||50;var n=C(e).toRgb(),a=C(t).toRgb(),o=r/100,s={r:(a.r-n.r)*o+n.r,g:(a.g-n.g)*o+n.g,b:(a.b-n.b)*o+n.b,a:(a.a-n.a)*o+n.a};return C(s)};C.readability=function(e,t){var r=C(e),n=C(t);return(Math.max(r.getLuminance(),n.getLuminance())+.05)/(Math.min(r.getLuminance(),n.getLuminance())+.05)};C.isReadable=function(e,t,r){var n=C.readability(e,t),a,o;switch(o=!1,a=dl(r),a.level+a.size){case"AAsmall":case"AAAlarge":o=n>=4.5;break;case"AAlarge":o=n>=3;break;case"AAAsmall":o=n>=7;break}return o};C.mostReadable=function(e,t,r){var n=null,a=0,o,s,i,l;r=r||{},s=r.includeFallbackColors,i=r.level,l=r.size;for(var c=0;c<t.length;c++)o=C.readability(e,t[c]),o>a&&(a=o,n=C(t[c]));return C.isReadable(e,n,{level:i,size:l})||!s?n:(r.includeFallbackColors=!1,C.mostReadable(e,["#fff","#000"],r))};var Fr=C.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},ll=C.hexNames=sl(Fr);function sl(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}function io(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function W(e,t){ul(e)&&(e="100%");var r=cl(e);return e=Math.min(t,Math.max(0,parseFloat(e))),r&&(e=parseInt(e*t,10)/100),Math.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function cr(e){return Math.min(1,Math.max(0,e))}function pe(e){return parseInt(e,16)}function ul(e){return typeof e=="string"&&e.indexOf(".")!=-1&&parseFloat(e)===1}function cl(e){return typeof e=="string"&&e.indexOf("%")!=-1}function Ae(e){return e.length==1?"0"+e:""+e}function kt(e){return e<=1&&(e=e*100+"%"),e}function lo(e){return Math.round(parseFloat(e)*255).toString(16)}function ea(e){return pe(e)/255}var ke=function(){var e="[-\\+]?\\d+%?",t="[-\\+]?\\d*\\.\\d+%?",r="(?:"+t+")|(?:"+e+")",n="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?",a="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?";return{CSS_UNIT:new RegExp(r),rgb:new RegExp("rgb"+n),rgba:new RegExp("rgba"+a),hsl:new RegExp("hsl"+n),hsla:new RegExp("hsla"+a),hsv:new RegExp("hsv"+n),hsva:new RegExp("hsva"+a),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function Me(e){return!!ke.CSS_UNIT.exec(e)}function fl(e){e=e.replace(Wi,"").replace(zi,"").toLowerCase();var t=!1;if(Fr[e])e=Fr[e],t=!0;else if(e=="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var r;return(r=ke.rgb.exec(e))?{r:r[1],g:r[2],b:r[3]}:(r=ke.rgba.exec(e))?{r:r[1],g:r[2],b:r[3],a:r[4]}:(r=ke.hsl.exec(e))?{h:r[1],s:r[2],l:r[3]}:(r=ke.hsla.exec(e))?{h:r[1],s:r[2],l:r[3],a:r[4]}:(r=ke.hsv.exec(e))?{h:r[1],s:r[2],v:r[3]}:(r=ke.hsva.exec(e))?{h:r[1],s:r[2],v:r[3],a:r[4]}:(r=ke.hex8.exec(e))?{r:pe(r[1]),g:pe(r[2]),b:pe(r[3]),a:ea(r[4]),format:t?"name":"hex8"}:(r=ke.hex6.exec(e))?{r:pe(r[1]),g:pe(r[2]),b:pe(r[3]),format:t?"name":"hex"}:(r=ke.hex4.exec(e))?{r:pe(r[1]+""+r[1]),g:pe(r[2]+""+r[2]),b:pe(r[3]+""+r[3]),a:ea(r[4]+""+r[4]),format:t?"name":"hex8"}:(r=ke.hex3.exec(e))?{r:pe(r[1]+""+r[1]),g:pe(r[2]+""+r[2]),b:pe(r[3]+""+r[3]),format:t?"name":"hex"}:!1}function dl(e){var t,r;return e=e||{level:"AA",size:"small"},t=(e.level||"AA").toUpperCase(),r=(e.size||"small").toLowerCase(),t!=="AA"&&t!=="AAA"&&(t="AA"),r!=="small"&&r!=="large"&&(r="small"),{level:t,size:r}}var Ft={},ta;function hl(){if(ta)return Ft;ta=1;var e=e||{};e.stringify=function(){var t={"visit_linear-gradient":function(r){return t.visit_gradient(r)},"visit_repeating-linear-gradient":function(r){return t.visit_gradient(r)},"visit_radial-gradient":function(r){return t.visit_gradient(r)},"visit_repeating-radial-gradient":function(r){return t.visit_gradient(r)},visit_gradient:function(r){var n=t.visit(r.orientation);return n&&(n+=", "),r.type+"("+n+t.visit(r.colorStops)+")"},visit_shape:function(r){var n=r.value,a=t.visit(r.at),o=t.visit(r.style);return o&&(n+=" "+o),a&&(n+=" at "+a),n},"visit_default-radial":function(r){var n="",a=t.visit(r.at);return a&&(n+=a),n},"visit_extent-keyword":function(r){var n=r.value,a=t.visit(r.at);return a&&(n+=" at "+a),n},"visit_position-keyword":function(r){return r.value},visit_position:function(r){return t.visit(r.value.x)+" "+t.visit(r.value.y)},"visit_%":function(r){return r.value+"%"},visit_em:function(r){return r.value+"em"},visit_px:function(r){return r.value+"px"},visit_calc:function(r){return"calc("+r.value+")"},visit_literal:function(r){return t.visit_color(r.value,r)},visit_hex:function(r){return t.visit_color("#"+r.value,r)},visit_rgb:function(r){return t.visit_color("rgb("+r.value.join(", ")+")",r)},visit_rgba:function(r){return t.visit_color("rgba("+r.value.join(", ")+")",r)},visit_hsl:function(r){return t.visit_color("hsl("+r.value[0]+", "+r.value[1]+"%, "+r.value[2]+"%)",r)},visit_hsla:function(r){return t.visit_color("hsla("+r.value[0]+", "+r.value[1]+"%, "+r.value[2]+"%, "+r.value[3]+")",r)},visit_var:function(r){return t.visit_color("var("+r.value+")",r)},visit_color:function(r,n){var a=r,o=t.visit(n.length);return o&&(a+=" "+o),a},visit_angular:function(r){return r.value+"deg"},visit_directional:function(r){return"to "+r.value},visit_array:function(r){var n="",a=r.length;return r.forEach(function(o,s){n+=t.visit(o),s<a-1&&(n+=", ")}),n},visit_object:function(r){return r.width&&r.height?t.visit(r.width)+" "+t.visit(r.height):""},visit:function(r){if(!r)return"";if(r instanceof Array)return t.visit_array(r);if(typeof r=="object"&&!r.type)return t.visit_object(r);if(r.type){var n=t["visit_"+r.type];if(n)return n(r);throw Error("Missing visitor visit_"+r.type)}else throw Error("Invalid node.")}};return function(r){return t.visit(r)}}();var e=e||{};return e.parse=function(){var t={linearGradient:/^(\-(webkit|o|ms|moz)\-)?(linear\-gradient)/i,repeatingLinearGradient:/^(\-(webkit|o|ms|moz)\-)?(repeating\-linear\-gradient)/i,radialGradient:/^(\-(webkit|o|ms|moz)\-)?(radial\-gradient)/i,repeatingRadialGradient:/^(\-(webkit|o|ms|moz)\-)?(repeating\-radial\-gradient)/i,sideOrCorner:/^to (left (top|bottom)|right (top|bottom)|top (left|right)|bottom (left|right)|left|right|top|bottom)/i,extentKeywords:/^(closest\-side|closest\-corner|farthest\-side|farthest\-corner|contain|cover)/,positionKeywords:/^(left|center|right|top|bottom)/i,pixelValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))px/,percentageValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))\%/,emValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))em/,angleValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))deg/,radianValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))rad/,startCall:/^\(/,endCall:/^\)/,comma:/^,/,hexColor:/^\#([0-9a-fA-F]+)/,literalColor:/^([a-zA-Z]+)/,rgbColor:/^rgb/i,rgbaColor:/^rgba/i,varColor:/^var/i,calcValue:/^calc/i,variableName:/^(--[a-zA-Z0-9-,\s\#]+)/,number:/^(([0-9]*\.[0-9]+)|([0-9]+\.?))/,hslColor:/^hsl/i,hslaColor:/^hsla/i},r="";function n(S){var E=new Error(r+": "+S);throw E.source=r,E}function a(){var S=o();return r.length>0&&n("Invalid input not EOF"),S}function o(){return w(s)}function s(){return i("linear-gradient",t.linearGradient,c)||i("repeating-linear-gradient",t.repeatingLinearGradient,c)||i("radial-gradient",t.radialGradient,d)||i("repeating-radial-gradient",t.repeatingRadialGradient,d)}function i(S,E,$){return l(E,function(z){var ue=$();return ue&&(N(t.comma)||n("Missing comma before color stops")),{type:S,orientation:ue,colorStops:w(A)}})}function l(S,E){var $=N(S);if($){N(t.startCall)||n("Missing (");var z=E($);return N(t.endCall)||n("Missing )"),z}}function c(){var S=u();if(S)return S;var E=q("position-keyword",t.positionKeywords,1);return E?{type:"directional",value:E.value}:f()}function u(){return q("directional",t.sideOrCorner,1)}function f(){return q("angular",t.angleValue,1)||q("angular",t.radianValue,1)}function d(){var S,E=h(),$;return E&&(S=[],S.push(E),$=r,N(t.comma)&&(E=h(),E?S.push(E):r=$)),S}function h(){var S=y()||g();if(S)S.at=p();else{var E=v();if(E){S=E;var $=p();$&&(S.at=$)}else{var z=p();if(z)S={type:"default-radial",at:z};else{var ue=b();ue&&(S={type:"default-radial",at:ue})}}}return S}function y(){var S=q("shape",/^(circle)/i,0);return S&&(S.style=ye()||v()),S}function g(){var S=q("shape",/^(ellipse)/i,0);return S&&(S.style=b()||G()||v()),S}function v(){return q("extent-keyword",t.extentKeywords,1)}function p(){if(q("position",/^at/,0)){var S=b();return S||n("Missing positioning value"),S}}function b(){var S=x();if(S.x||S.y)return{type:"position",value:S}}function x(){return{x:G(),y:G()}}function w(S){var E=S(),$=[];if(E)for($.push(E);N(t.comma);)E=S(),E?$.push(E):n("One extra comma");return $}function A(){var S=_();return S||n("Expected color definition"),S.length=G(),S}function _(){return L()||P()||R()||D()||H()||m()||T()}function T(){return q("literal",t.literalColor,0)}function L(){return q("hex",t.hexColor,1)}function H(){return l(t.rgbColor,function(){return{type:"rgb",value:w(B)}})}function D(){return l(t.rgbaColor,function(){return{type:"rgba",value:w(B)}})}function m(){return l(t.varColor,function(){return{type:"var",value:V()}})}function R(){return l(t.hslColor,function(){var S=N(t.percentageValue);S&&n("HSL hue value must be a number in degrees (0-360) or normalized (-360 to 360), not a percentage");var E=B();N(t.comma);var $=N(t.percentageValue),z=$?$[1]:null;N(t.comma),$=N(t.percentageValue);var ue=$?$[1]:null;return(!z||!ue)&&n("Expected percentage value for saturation and lightness in HSL"),{type:"hsl",value:[E,z,ue]}})}function P(){return l(t.hslaColor,function(){var S=B();N(t.comma);var E=N(t.percentageValue),$=E?E[1]:null;N(t.comma),E=N(t.percentageValue);var z=E?E[1]:null;N(t.comma);var ue=B();return(!$||!z)&&n("Expected percentage value for saturation and lightness in HSLA"),{type:"hsla",value:[S,$,z,ue]}})}function V(){return N(t.variableName)[1]}function B(){return N(t.number)[1]}function G(){return q("%",t.percentageValue,1)||ae()||De()||ye()}function ae(){return q("position-keyword",t.positionKeywords,1)}function De(){return l(t.calcValue,function(){for(var S=1,E=0;S>0&&E<r.length;){var $=r.charAt(E);$==="("?S++:$===")"&&S--,E++}S>0&&n("Missing closing parenthesis in calc() expression");var z=r.substring(0,E-1);return _e(E-1),{type:"calc",value:z}})}function ye(){return q("px",t.pixelValue,1)||q("em",t.emValue,1)}function q(S,E,$){var z=N(E);if(z)return{type:S,value:z[$]}}function N(S){var E,$;return $=/^[\n\r\t\s]+/.exec(r),$&&_e($[0].length),E=S.exec(r),E&&_e(E[0].length),E}function _e(S){r=r.substr(S)}return function(S){return r=S.toString().trim(),r.endsWith(";")&&(r=r.slice(0,-1)),a()}}(),Ft.parse=e.parse,Ft.stringify=e.stringify,Ft}var ra=hl(),fe="top",xe="bottom",we="right",de="left",tn="auto",It=[fe,xe,we,de],st="start",$t="end",pl="clippingParents",so="viewport",xt="popper",vl="reference",na=It.reduce(function(e,t){return e.concat([t+"-"+st,t+"-"+$t])},[]),uo=[].concat(It,[tn]).reduce(function(e,t){return e.concat([t,t+"-"+st,t+"-"+$t])},[]),gl="beforeRead",yl="read",ml="afterRead",bl="beforeMain",Cl="main",xl="afterMain",wl="beforeWrite",Sl="write",_l="afterWrite",kl=[gl,yl,ml,bl,Cl,xl,wl,Sl,_l];function Re(e){return e?(e.nodeName||"").toLowerCase():null}function ve(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function qe(e){var t=ve(e).Element;return e instanceof t||e instanceof Element}function be(e){var t=ve(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function rn(e){if(typeof ShadowRoot=="undefined")return!1;var t=ve(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Al(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},a=t.attributes[r]||{},o=t.elements[r];!be(o)||!Re(o)||(Object.assign(o.style,n),Object.keys(a).forEach(function(s){var i=a[s];i===!1?o.removeAttribute(s):o.setAttribute(s,i===!0?"":i)}))})}function El(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var a=t.elements[n],o=t.attributes[n]||{},s=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),i=s.reduce(function(l,c){return l[c]="",l},{});!be(a)||!Re(a)||(Object.assign(a.style,i),Object.keys(o).forEach(function(l){a.removeAttribute(l)}))})}}const Ol={name:"applyStyles",enabled:!0,phase:"write",fn:Al,effect:El,requires:["computeStyles"]};function Oe(e){return e.split("-")[0]}var Ue=Math.max,Jt=Math.min,ut=Math.round;function Wr(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function co(){return!/^((?!chrome|android).)*safari/i.test(Wr())}function ct(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var n=e.getBoundingClientRect(),a=1,o=1;t&&be(e)&&(a=e.offsetWidth>0&&ut(n.width)/e.offsetWidth||1,o=e.offsetHeight>0&&ut(n.height)/e.offsetHeight||1);var s=qe(e)?ve(e):window,i=s.visualViewport,l=!co()&&r,c=(n.left+(l&&i?i.offsetLeft:0))/a,u=(n.top+(l&&i?i.offsetTop:0))/o,f=n.width/a,d=n.height/o;return{width:f,height:d,top:u,right:c+f,bottom:u+d,left:c,x:c,y:u}}function nn(e){var t=ct(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function fo(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&rn(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Le(e){return ve(e).getComputedStyle(e)}function Rl(e){return["table","td","th"].indexOf(Re(e))>=0}function Fe(e){return((qe(e)?e.ownerDocument:e.document)||window.document).documentElement}function fr(e){return Re(e)==="html"?e:e.assignedSlot||e.parentNode||(rn(e)?e.host:null)||Fe(e)}function aa(e){return!be(e)||Le(e).position==="fixed"?null:e.offsetParent}function Ml(e){var t=/firefox/i.test(Wr()),r=/Trident/i.test(Wr());if(r&&be(e)){var n=Le(e);if(n.position==="fixed")return null}var a=fr(e);for(rn(a)&&(a=a.host);be(a)&&["html","body"].indexOf(Re(a))<0;){var o=Le(a);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||t&&o.willChange==="filter"||t&&o.filter&&o.filter!=="none")return a;a=a.parentNode}return null}function Bt(e){for(var t=ve(e),r=aa(e);r&&Rl(r)&&Le(r).position==="static";)r=aa(r);return r&&(Re(r)==="html"||Re(r)==="body"&&Le(r).position==="static")?t:r||Ml(e)||t}function an(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Et(e,t,r){return Ue(e,Jt(t,r))}function Tl(e,t,r){var n=Et(e,t,r);return n>r?r:n}function ho(){return{top:0,right:0,bottom:0,left:0}}function po(e){return Object.assign({},ho(),e)}function vo(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var $l=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,po(typeof t!="number"?t:vo(t,It))};function Pl(e){var t,r=e.state,n=e.name,a=e.options,o=r.elements.arrow,s=r.modifiersData.popperOffsets,i=Oe(r.placement),l=an(i),c=[de,we].indexOf(i)>=0,u=c?"height":"width";if(!(!o||!s)){var f=$l(a.padding,r),d=nn(o),h=l==="y"?fe:de,y=l==="y"?xe:we,g=r.rects.reference[u]+r.rects.reference[l]-s[l]-r.rects.popper[u],v=s[l]-r.rects.reference[l],p=Bt(o),b=p?l==="y"?p.clientHeight||0:p.clientWidth||0:0,x=g/2-v/2,w=f[h],A=b-d[u]-f[y],_=b/2-d[u]/2+x,T=Et(w,_,A),L=l;r.modifiersData[n]=(t={},t[L]=T,t.centerOffset=T-_,t)}}function Hl(e){var t=e.state,r=e.options,n=r.element,a=n===void 0?"[data-popper-arrow]":n;a!=null&&(typeof a=="string"&&(a=t.elements.popper.querySelector(a),!a)||fo(t.elements.popper,a)&&(t.elements.arrow=a))}const Ll={name:"arrow",enabled:!0,phase:"main",fn:Pl,effect:Hl,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function ft(e){return e.split("-")[1]}var Il={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Bl(e,t){var r=e.x,n=e.y,a=t.devicePixelRatio||1;return{x:ut(r*a)/a||0,y:ut(n*a)/a||0}}function oa(e){var t,r=e.popper,n=e.popperRect,a=e.placement,o=e.variation,s=e.offsets,i=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,f=e.isFixed,d=s.x,h=d===void 0?0:d,y=s.y,g=y===void 0?0:y,v=typeof u=="function"?u({x:h,y:g}):{x:h,y:g};h=v.x,g=v.y;var p=s.hasOwnProperty("x"),b=s.hasOwnProperty("y"),x=de,w=fe,A=window;if(c){var _=Bt(r),T="clientHeight",L="clientWidth";if(_===ve(r)&&(_=Fe(r),Le(_).position!=="static"&&i==="absolute"&&(T="scrollHeight",L="scrollWidth")),_=_,a===fe||(a===de||a===we)&&o===$t){w=xe;var H=f&&_===A&&A.visualViewport?A.visualViewport.height:_[T];g-=H-n.height,g*=l?1:-1}if(a===de||(a===fe||a===xe)&&o===$t){x=we;var D=f&&_===A&&A.visualViewport?A.visualViewport.width:_[L];h-=D-n.width,h*=l?1:-1}}var m=Object.assign({position:i},c&&Il),R=u===!0?Bl({x:h,y:g},ve(r)):{x:h,y:g};if(h=R.x,g=R.y,l){var P;return Object.assign({},m,(P={},P[w]=b?"0":"",P[x]=p?"0":"",P.transform=(A.devicePixelRatio||1)<=1?"translate("+h+"px, "+g+"px)":"translate3d("+h+"px, "+g+"px, 0)",P))}return Object.assign({},m,(t={},t[w]=b?g+"px":"",t[x]=p?h+"px":"",t.transform="",t))}function Dl(e){var t=e.state,r=e.options,n=r.gpuAcceleration,a=n===void 0?!0:n,o=r.adaptive,s=o===void 0?!0:o,i=r.roundOffsets,l=i===void 0?!0:i,c={placement:Oe(t.placement),variation:ft(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,oa(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,oa(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Vl={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Dl,data:{}};var Wt={passive:!0};function jl(e){var t=e.state,r=e.instance,n=e.options,a=n.scroll,o=a===void 0?!0:a,s=n.resize,i=s===void 0?!0:s,l=ve(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(u){u.addEventListener("scroll",r.update,Wt)}),i&&l.addEventListener("resize",r.update,Wt),function(){o&&c.forEach(function(u){u.removeEventListener("scroll",r.update,Wt)}),i&&l.removeEventListener("resize",r.update,Wt)}}const Nl={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:jl,data:{}};var Fl={left:"right",right:"left",bottom:"top",top:"bottom"};function Gt(e){return e.replace(/left|right|bottom|top/g,function(t){return Fl[t]})}var Wl={start:"end",end:"start"};function ia(e){return e.replace(/start|end/g,function(t){return Wl[t]})}function on(e){var t=ve(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function ln(e){return ct(Fe(e)).left+on(e).scrollLeft}function zl(e,t){var r=ve(e),n=Fe(e),a=r.visualViewport,o=n.clientWidth,s=n.clientHeight,i=0,l=0;if(a){o=a.width,s=a.height;var c=co();(c||!c&&t==="fixed")&&(i=a.offsetLeft,l=a.offsetTop)}return{width:o,height:s,x:i+ln(e),y:l}}function Kl(e){var t,r=Fe(e),n=on(e),a=(t=e.ownerDocument)==null?void 0:t.body,o=Ue(r.scrollWidth,r.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),s=Ue(r.scrollHeight,r.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),i=-n.scrollLeft+ln(e),l=-n.scrollTop;return Le(a||r).direction==="rtl"&&(i+=Ue(r.clientWidth,a?a.clientWidth:0)-o),{width:o,height:s,x:i,y:l}}function sn(e){var t=Le(e),r=t.overflow,n=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+a+n)}function go(e){return["html","body","#document"].indexOf(Re(e))>=0?e.ownerDocument.body:be(e)&&sn(e)?e:go(fr(e))}function Ot(e,t){var r;t===void 0&&(t=[]);var n=go(e),a=n===((r=e.ownerDocument)==null?void 0:r.body),o=ve(n),s=a?[o].concat(o.visualViewport||[],sn(n)?n:[]):n,i=t.concat(s);return a?i:i.concat(Ot(fr(s)))}function zr(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Ul(e,t){var r=ct(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function la(e,t,r){return t===so?zr(zl(e,r)):qe(t)?Ul(t,r):zr(Kl(Fe(e)))}function Gl(e){var t=Ot(fr(e)),r=["absolute","fixed"].indexOf(Le(e).position)>=0,n=r&&be(e)?Bt(e):e;return qe(n)?t.filter(function(a){return qe(a)&&fo(a,n)&&Re(a)!=="body"}):[]}function ql(e,t,r,n){var a=t==="clippingParents"?Gl(e):[].concat(t),o=[].concat(a,[r]),s=o[0],i=o.reduce(function(l,c){var u=la(e,c,n);return l.top=Ue(u.top,l.top),l.right=Jt(u.right,l.right),l.bottom=Jt(u.bottom,l.bottom),l.left=Ue(u.left,l.left),l},la(e,s,n));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function yo(e){var t=e.reference,r=e.element,n=e.placement,a=n?Oe(n):null,o=n?ft(n):null,s=t.x+t.width/2-r.width/2,i=t.y+t.height/2-r.height/2,l;switch(a){case fe:l={x:s,y:t.y-r.height};break;case xe:l={x:s,y:t.y+t.height};break;case we:l={x:t.x+t.width,y:i};break;case de:l={x:t.x-r.width,y:i};break;default:l={x:t.x,y:t.y}}var c=a?an(a):null;if(c!=null){var u=c==="y"?"height":"width";switch(o){case st:l[c]=l[c]-(t[u]/2-r[u]/2);break;case $t:l[c]=l[c]+(t[u]/2-r[u]/2);break}}return l}function Pt(e,t){t===void 0&&(t={});var r=t,n=r.placement,a=n===void 0?e.placement:n,o=r.strategy,s=o===void 0?e.strategy:o,i=r.boundary,l=i===void 0?pl:i,c=r.rootBoundary,u=c===void 0?so:c,f=r.elementContext,d=f===void 0?xt:f,h=r.altBoundary,y=h===void 0?!1:h,g=r.padding,v=g===void 0?0:g,p=po(typeof v!="number"?v:vo(v,It)),b=d===xt?vl:xt,x=e.rects.popper,w=e.elements[y?b:d],A=ql(qe(w)?w:w.contextElement||Fe(e.elements.popper),l,u,s),_=ct(e.elements.reference),T=yo({reference:_,element:x,placement:a}),L=zr(Object.assign({},x,T)),H=d===xt?L:_,D={top:A.top-H.top+p.top,bottom:H.bottom-A.bottom+p.bottom,left:A.left-H.left+p.left,right:H.right-A.right+p.right},m=e.modifiersData.offset;if(d===xt&&m){var R=m[a];Object.keys(D).forEach(function(P){var V=[we,xe].indexOf(P)>=0?1:-1,B=[fe,xe].indexOf(P)>=0?"y":"x";D[P]+=R[B]*V})}return D}function Xl(e,t){t===void 0&&(t={});var r=t,n=r.placement,a=r.boundary,o=r.rootBoundary,s=r.padding,i=r.flipVariations,l=r.allowedAutoPlacements,c=l===void 0?uo:l,u=ft(n),f=u?i?na:na.filter(function(y){return ft(y)===u}):It,d=f.filter(function(y){return c.indexOf(y)>=0});d.length===0&&(d=f);var h=d.reduce(function(y,g){return y[g]=Pt(e,{placement:g,boundary:a,rootBoundary:o,padding:s})[Oe(g)],y},{});return Object.keys(h).sort(function(y,g){return h[y]-h[g]})}function Yl(e){if(Oe(e)===tn)return[];var t=Gt(e);return[ia(e),t,ia(t)]}function Zl(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var a=r.mainAxis,o=a===void 0?!0:a,s=r.altAxis,i=s===void 0?!0:s,l=r.fallbackPlacements,c=r.padding,u=r.boundary,f=r.rootBoundary,d=r.altBoundary,h=r.flipVariations,y=h===void 0?!0:h,g=r.allowedAutoPlacements,v=t.options.placement,p=Oe(v),b=p===v,x=l||(b||!y?[Gt(v)]:Yl(v)),w=[v].concat(x).reduce(function(E,$){return E.concat(Oe($)===tn?Xl(t,{placement:$,boundary:u,rootBoundary:f,padding:c,flipVariations:y,allowedAutoPlacements:g}):$)},[]),A=t.rects.reference,_=t.rects.popper,T=new Map,L=!0,H=w[0],D=0;D<w.length;D++){var m=w[D],R=Oe(m),P=ft(m)===st,V=[fe,xe].indexOf(R)>=0,B=V?"width":"height",G=Pt(t,{placement:m,boundary:u,rootBoundary:f,altBoundary:d,padding:c}),ae=V?P?we:de:P?xe:fe;A[B]>_[B]&&(ae=Gt(ae));var De=Gt(ae),ye=[];if(o&&ye.push(G[R]<=0),i&&ye.push(G[ae]<=0,G[De]<=0),ye.every(function(E){return E})){H=m,L=!1;break}T.set(m,ye)}if(L)for(var q=y?3:1,N=function($){var z=w.find(function(ue){var We=T.get(ue);if(We)return We.slice(0,$).every(function(yr){return yr})});if(z)return H=z,"break"},_e=q;_e>0;_e--){var S=N(_e);if(S==="break")break}t.placement!==H&&(t.modifiersData[n]._skip=!0,t.placement=H,t.reset=!0)}}const Jl={name:"flip",enabled:!0,phase:"main",fn:Zl,requiresIfExists:["offset"],data:{_skip:!1}};function sa(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function ua(e){return[fe,we,xe,de].some(function(t){return e[t]>=0})}function Ql(e){var t=e.state,r=e.name,n=t.rects.reference,a=t.rects.popper,o=t.modifiersData.preventOverflow,s=Pt(t,{elementContext:"reference"}),i=Pt(t,{altBoundary:!0}),l=sa(s,n),c=sa(i,a,o),u=ua(l),f=ua(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":f})}const es={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ql};function ts(e,t,r){var n=Oe(e),a=[de,fe].indexOf(n)>=0?-1:1,o=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,s=o[0],i=o[1];return s=s||0,i=(i||0)*a,[de,we].indexOf(n)>=0?{x:i,y:s}:{x:s,y:i}}function rs(e){var t=e.state,r=e.options,n=e.name,a=r.offset,o=a===void 0?[0,0]:a,s=uo.reduce(function(u,f){return u[f]=ts(f,t.rects,o),u},{}),i=s[t.placement],l=i.x,c=i.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=s}const ns={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:rs};function as(e){var t=e.state,r=e.name;t.modifiersData[r]=yo({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const os={name:"popperOffsets",enabled:!0,phase:"read",fn:as,data:{}};function is(e){return e==="x"?"y":"x"}function ls(e){var t=e.state,r=e.options,n=e.name,a=r.mainAxis,o=a===void 0?!0:a,s=r.altAxis,i=s===void 0?!1:s,l=r.boundary,c=r.rootBoundary,u=r.altBoundary,f=r.padding,d=r.tether,h=d===void 0?!0:d,y=r.tetherOffset,g=y===void 0?0:y,v=Pt(t,{boundary:l,rootBoundary:c,padding:f,altBoundary:u}),p=Oe(t.placement),b=ft(t.placement),x=!b,w=an(p),A=is(w),_=t.modifiersData.popperOffsets,T=t.rects.reference,L=t.rects.popper,H=typeof g=="function"?g(Object.assign({},t.rects,{placement:t.placement})):g,D=typeof H=="number"?{mainAxis:H,altAxis:H}:Object.assign({mainAxis:0,altAxis:0},H),m=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(_){if(o){var P,V=w==="y"?fe:de,B=w==="y"?xe:we,G=w==="y"?"height":"width",ae=_[w],De=ae+v[V],ye=ae-v[B],q=h?-L[G]/2:0,N=b===st?T[G]:L[G],_e=b===st?-L[G]:-T[G],S=t.elements.arrow,E=h&&S?nn(S):{width:0,height:0},$=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:ho(),z=$[V],ue=$[B],We=Et(0,T[G],E[G]),yr=x?T[G]/2-q-We-z-D.mainAxis:N-We-z-D.mainAxis,Jo=x?-T[G]/2+q+We+ue+D.mainAxis:_e+We+ue+D.mainAxis,mr=t.elements.arrow&&Bt(t.elements.arrow),Qo=mr?w==="y"?mr.clientTop||0:mr.clientLeft||0:0,$n=(P=m==null?void 0:m[w])!=null?P:0,ei=ae+yr-$n-Qo,ti=ae+Jo-$n,Pn=Et(h?Jt(De,ei):De,ae,h?Ue(ye,ti):ye);_[w]=Pn,R[w]=Pn-ae}if(i){var Hn,ri=w==="x"?fe:de,ni=w==="x"?xe:we,ze=_[A],jt=A==="y"?"height":"width",Ln=ze+v[ri],In=ze-v[ni],br=[fe,de].indexOf(p)!==-1,Bn=(Hn=m==null?void 0:m[A])!=null?Hn:0,Dn=br?Ln:ze-T[jt]-L[jt]-Bn+D.altAxis,Vn=br?ze+T[jt]+L[jt]-Bn-D.altAxis:In,jn=h&&br?Tl(Dn,ze,Vn):Et(h?Dn:Ln,ze,h?Vn:In);_[A]=jn,R[A]=jn-ze}t.modifiersData[n]=R}}const ss={name:"preventOverflow",enabled:!0,phase:"main",fn:ls,requiresIfExists:["offset"]};function us(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function cs(e){return e===ve(e)||!be(e)?on(e):us(e)}function fs(e){var t=e.getBoundingClientRect(),r=ut(t.width)/e.offsetWidth||1,n=ut(t.height)/e.offsetHeight||1;return r!==1||n!==1}function ds(e,t,r){r===void 0&&(r=!1);var n=be(t),a=be(t)&&fs(t),o=Fe(t),s=ct(e,a,r),i={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(n||!n&&!r)&&((Re(t)!=="body"||sn(o))&&(i=cs(t)),be(t)?(l=ct(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):o&&(l.x=ln(o))),{x:s.left+i.scrollLeft-l.x,y:s.top+i.scrollTop-l.y,width:s.width,height:s.height}}function hs(e){var t=new Map,r=new Set,n=[];e.forEach(function(o){t.set(o.name,o)});function a(o){r.add(o.name);var s=[].concat(o.requires||[],o.requiresIfExists||[]);s.forEach(function(i){if(!r.has(i)){var l=t.get(i);l&&a(l)}}),n.push(o)}return e.forEach(function(o){r.has(o.name)||a(o)}),n}function ps(e){var t=hs(e);return kl.reduce(function(r,n){return r.concat(t.filter(function(a){return a.phase===n}))},[])}function vs(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function gs(e){var t=e.reduce(function(r,n){var a=r[n.name];return r[n.name]=a?Object.assign({},a,n,{options:Object.assign({},a.options,n.options),data:Object.assign({},a.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var ca={placement:"bottom",modifiers:[],strategy:"absolute"};function fa(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function ys(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,a=t.defaultOptions,o=a===void 0?ca:a;return function(i,l,c){c===void 0&&(c=o);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},ca,o),modifiersData:{},elements:{reference:i,popper:l},attributes:{},styles:{}},f=[],d=!1,h={state:u,setOptions:function(p){var b=typeof p=="function"?p(u.options):p;g(),u.options=Object.assign({},o,u.options,b),u.scrollParents={reference:qe(i)?Ot(i):i.contextElement?Ot(i.contextElement):[],popper:Ot(l)};var x=ps(gs([].concat(n,u.options.modifiers)));return u.orderedModifiers=x.filter(function(w){return w.enabled}),y(),h.update()},forceUpdate:function(){if(!d){var p=u.elements,b=p.reference,x=p.popper;if(fa(b,x)){u.rects={reference:ds(b,Bt(x),u.options.strategy==="fixed"),popper:nn(x)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(D){return u.modifiersData[D.name]=Object.assign({},D.data)});for(var w=0;w<u.orderedModifiers.length;w++){if(u.reset===!0){u.reset=!1,w=-1;continue}var A=u.orderedModifiers[w],_=A.fn,T=A.options,L=T===void 0?{}:T,H=A.name;typeof _=="function"&&(u=_({state:u,options:L,name:H,instance:h})||u)}}}},update:vs(function(){return new Promise(function(v){h.forceUpdate(),v(u)})}),destroy:function(){g(),d=!0}};if(!fa(i,l))return h;h.setOptions(c).then(function(v){!d&&c.onFirstUpdate&&c.onFirstUpdate(v)});function y(){u.orderedModifiers.forEach(function(v){var p=v.name,b=v.options,x=b===void 0?{}:b,w=v.effect;if(typeof w=="function"){var A=w({state:u,name:p,instance:h,options:x}),_=function(){};f.push(A||_)}})}function g(){f.forEach(function(v){return v()}),f=[]}return h}}var ms=[Nl,os,Vl,Ol,ns,Jl,ss,Ll,es],bs=ys({defaultModifiers:ms}),da=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function un(e){var t={exports:{}};return e(t,t.exports),t.exports}var zt=function(e){return e&&e.Math==Math&&e},te=zt(typeof globalThis=="object"&&globalThis)||zt(typeof window=="object"&&window)||zt(typeof self=="object"&&self)||zt(typeof da=="object"&&da)||function(){return this}()||Function("return this")(),F=function(e){try{return!!e()}catch(t){return!0}},me=!F(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7}),ha={}.propertyIsEnumerable,pa=Object.getOwnPropertyDescriptor,Cs={f:pa&&!ha.call({1:2},1)?function(e){var t=pa(this,e);return!!t&&t.enumerable}:ha},dr=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},xs={}.toString,He=function(e){return xs.call(e).slice(8,-1)},ws="".split,hr=F(function(){return!Object("z").propertyIsEnumerable(0)})?function(e){return He(e)=="String"?ws.call(e,""):Object(e)}:Object,Ve=function(e){if(e==null)throw TypeError("Can't call method on "+e);return e},mt=function(e){return hr(Ve(e))},re=function(e){return typeof e=="object"?e!==null:typeof e=="function"},cn=function(e,t){if(!re(e))return e;var r,n;if(t&&typeof(r=e.toString)=="function"&&!re(n=r.call(e))||typeof(r=e.valueOf)=="function"&&!re(n=r.call(e))||!t&&typeof(r=e.toString)=="function"&&!re(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},Ss={}.hasOwnProperty,ee=function(e,t){return Ss.call(e,t)},Kr=te.document,_s=re(Kr)&&re(Kr.createElement),mo=function(e){return _s?Kr.createElement(e):{}},bo=!me&&!F(function(){return Object.defineProperty(mo("div"),"a",{get:function(){return 7}}).a!=7}),va=Object.getOwnPropertyDescriptor,fn={f:me?va:function(e,t){if(e=mt(e),t=cn(t,!0),bo)try{return va(e,t)}catch(r){}if(ee(e,t))return dr(!Cs.f.call(e,t),e[t])}},ce=function(e){if(!re(e))throw TypeError(String(e)+" is not an object");return e},ga=Object.defineProperty,Ie={f:me?ga:function(e,t,r){if(ce(e),t=cn(t,!0),ce(r),bo)try{return ga(e,t,r)}catch(n){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},Ce=me?function(e,t,r){return Ie.f(e,t,dr(1,r))}:function(e,t,r){return e[t]=r,e},dn=function(e,t){try{Ce(te,e,t)}catch(r){te[e]=t}return t},Xe=te["__core-js_shared__"]||dn("__core-js_shared__",{}),ks=Function.toString;typeof Xe.inspectSource!="function"&&(Xe.inspectSource=function(e){return ks.call(e)});var Qt,Rt,er,Co=Xe.inspectSource,ya=te.WeakMap,As=typeof ya=="function"&&/native code/.test(Co(ya)),xo=un(function(e){(e.exports=function(t,r){return Xe[t]||(Xe[t]=r!==void 0?r:{})})("versions",[]).push({version:"3.8.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})}),Es=0,Os=Math.random(),hn=function(e){return"Symbol("+String(e===void 0?"":e)+")_"+(++Es+Os).toString(36)},ma=xo("keys"),pn=function(e){return ma[e]||(ma[e]=hn(e))},pr={},Rs=te.WeakMap;if(As){var et=Xe.state||(Xe.state=new Rs),Ms=et.get,Ts=et.has,$s=et.set;Qt=function(e,t){return t.facade=e,$s.call(et,e,t),t},Rt=function(e){return Ms.call(et,e)||{}},er=function(e){return Ts.call(et,e)}}else{var wt=pn("state");pr[wt]=!0,Qt=function(e,t){return t.facade=e,Ce(e,wt,t),t},Rt=function(e){return ee(e,wt)?e[wt]:{}},er=function(e){return ee(e,wt)}}var je={set:Qt,get:Rt,has:er,enforce:function(e){return er(e)?Rt(e):Qt(e,{})},getterFor:function(e){return function(t){var r;if(!re(t)||(r=Rt(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},Ne=un(function(e){var t=je.get,r=je.enforce,n=String(String).split("String");(e.exports=function(a,o,s,i){var l,c=!!i&&!!i.unsafe,u=!!i&&!!i.enumerable,f=!!i&&!!i.noTargetGet;typeof s=="function"&&(typeof o!="string"||ee(s,"name")||Ce(s,"name",o),(l=r(s)).source||(l.source=n.join(typeof o=="string"?o:""))),a!==te?(c?!f&&a[o]&&(u=!0):delete a[o],u?a[o]=s:Ce(a,o,s)):u?a[o]=s:dn(o,s)})(Function.prototype,"toString",function(){return typeof this=="function"&&t(this).source||Co(this)})}),Cr=te,ba=function(e){return typeof e=="function"?e:void 0},vr=function(e,t){return arguments.length<2?ba(Cr[e])||ba(te[e]):Cr[e]&&Cr[e][t]||te[e]&&te[e][t]},Ps=Math.ceil,Hs=Math.floor,bt=function(e){return isNaN(e=+e)?0:(e>0?Hs:Ps)(e)},Ls=Math.min,ge=function(e){return e>0?Ls(bt(e),9007199254740991):0},Is=Math.max,Bs=Math.min,tr=function(e,t){var r=bt(e);return r<0?Is(r+t,0):Bs(r,t)},Ds=function(e){return function(t,r,n){var a,o=mt(t),s=ge(o.length),i=tr(n,s);if(e&&r!=r){for(;s>i;)if((a=o[i++])!=a)return!0}else for(;s>i;i++)if((e||i in o)&&o[i]===r)return e||i||0;return!e&&-1}},wo={indexOf:Ds(!1)},Vs=wo.indexOf,So=function(e,t){var r,n=mt(e),a=0,o=[];for(r in n)!ee(pr,r)&&ee(n,r)&&o.push(r);for(;t.length>a;)ee(n,r=t[a++])&&(~Vs(o,r)||o.push(r));return o},rr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],js=rr.concat("length","prototype"),Ns={f:Object.getOwnPropertyNames||function(e){return So(e,js)}},Fs={f:Object.getOwnPropertySymbols},Ws=vr("Reflect","ownKeys")||function(e){var t=Ns.f(ce(e)),r=Fs.f;return r?t.concat(r(e)):t},zs=function(e,t){for(var r=Ws(t),n=Ie.f,a=fn.f,o=0;o<r.length;o++){var s=r[o];ee(e,s)||n(e,s,a(t,s))}},Ks=/#|\.prototype\./,Dt=function(e,t){var r=Gs[Us(e)];return r==Xs||r!=qs&&(typeof t=="function"?F(t):!!t)},Us=Dt.normalize=function(e){return String(e).replace(Ks,".").toLowerCase()},Gs=Dt.data={},qs=Dt.NATIVE="N",Xs=Dt.POLYFILL="P",Ur=Dt,Ys=fn.f,se=function(e,t){var r,n,a,o,s,i=e.target,l=e.global,c=e.stat;if(r=l?te:c?te[i]||dn(i,{}):(te[i]||{}).prototype)for(n in t){if(o=t[n],a=e.noTargetGet?(s=Ys(r,n))&&s.value:r[n],!Ur(l?n:i+(c?".":"#")+n,e.forced)&&a!==void 0){if(typeof o==typeof a)continue;zs(o,a)}(e.sham||a&&a.sham)&&Ce(o,"sham",!0),Ne(r,n,o,e)}},vn=function(e,t){var r=[][e];return!!r&&F(function(){r.call(null,t||function(){throw 1},1)})},Zs=Object.defineProperty,xr={},Ca=function(e){throw e},Ct=function(e,t){if(ee(xr,e))return xr[e];t||(t={});var r=[][e],n=!!ee(t,"ACCESSORS")&&t.ACCESSORS,a=ee(t,0)?t[0]:Ca,o=ee(t,1)?t[1]:void 0;return xr[e]=!!r&&!F(function(){if(n&&!me)return!0;var s={length:-1};n?Zs(s,1,{enumerable:!0,get:Ca}):s[1]=1,r.call(s,a,o)})},Js=wo.indexOf,_o=[].indexOf,xa=!!_o&&1/[1].indexOf(1,-0)<0,Qs=vn("indexOf"),eu=Ct("indexOf",{ACCESSORS:!0,1:0});function Ye(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function tu(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ze(e,t,r){return r&&tu(e,r),e}se({target:"Array",proto:!0,forced:xa||!Qs||!eu},{indexOf:function(e){return xa?_o.apply(this,arguments)||0:Js(this,e,arguments.length>1?arguments[1]:void 0)}});(function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"isInBrowser",value:function(){return typeof window!="undefined"}},{key:"isServer",value:function(){return typeof window=="undefined"}},{key:"getUA",value:function(){return e.isInBrowser()?window.navigator.userAgent.toLowerCase():""}},{key:"isMobile",value:function(){return/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(navigator.appVersion)}},{key:"isOpera",value:function(){return navigator.userAgent.indexOf("Opera")!==-1}},{key:"isIE",value:function(){var t=e.getUA();return t!==""&&t.indexOf("msie")>0}},{key:"isIE9",value:function(){var t=e.getUA();return t!==""&&t.indexOf("msie 9.0")>0}},{key:"isEdge",value:function(){var t=e.getUA();return t!==""&&t.indexOf("edge/")>0}},{key:"isChrome",value:function(){var t=e.getUA();return t!==""&&/chrome\/\d+/.test(t)&&!e.isEdge()}},{key:"isPhantomJS",value:function(){var t=e.getUA();return t!==""&&/phantomjs/.test(t)}},{key:"isFirefox",value:function(){var t=e.getUA();return t!==""&&/firefox/.test(t)}}]),e})();var ru=[].join,nu=hr!=Object,au=vn("join",",");se({target:"Array",proto:!0,forced:nu||!au},{join:function(e){return ru.call(mt(this),e===void 0?",":e)}});var tt,nr,Be=function(e){return Object(Ve(e))},dt=Array.isArray||function(e){return He(e)=="Array"},ko=!!Object.getOwnPropertySymbols&&!F(function(){return!String(Symbol())}),ou=ko&&!Symbol.sham&&typeof Symbol.iterator=="symbol",Kt=xo("wks"),Mt=te.Symbol,iu=ou?Mt:Mt&&Mt.withoutSetter||hn,U=function(e){return ee(Kt,e)||(ko&&ee(Mt,e)?Kt[e]=Mt[e]:Kt[e]=iu("Symbol."+e)),Kt[e]},lu=U("species"),gr=function(e,t){var r;return dt(e)&&(typeof(r=e.constructor)!="function"||r!==Array&&!dt(r.prototype)?re(r)&&(r=r[lu])===null&&(r=void 0):r=void 0),new(r===void 0?Array:r)(t===0?0:t)},ht=function(e,t,r){var n=cn(t);n in e?Ie.f(e,n,dr(0,r)):e[n]=r},wr=vr("navigator","userAgent")||"",wa=te.process,Sa=wa&&wa.versions,_a=Sa&&Sa.v8;_a?nr=(tt=_a.split("."))[0]+tt[1]:wr&&(!(tt=wr.match(/Edge\/(\d+)/))||tt[1]>=74)&&(tt=wr.match(/Chrome\/(\d+)/))&&(nr=tt[1]);var ar=nr&&+nr,su=U("species"),gn=function(e){return ar>=51||!F(function(){var t=[];return(t.constructor={})[su]=function(){return{foo:1}},t[e](Boolean).foo!==1})},uu=gn("splice"),cu=Ct("splice",{ACCESSORS:!0,0:0,1:2}),fu=Math.max,du=Math.min;se({target:"Array",proto:!0,forced:!uu||!cu},{splice:function(e,t){var r,n,a,o,s,i,l=Be(this),c=ge(l.length),u=tr(e,c),f=arguments.length;if(f===0?r=n=0:f===1?(r=0,n=c-u):(r=f-2,n=du(fu(bt(t),0),c-u)),c+r-n>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(a=gr(l,n),o=0;o<n;o++)(s=u+o)in l&&ht(a,o,l[s]);if(a.length=n,r<n){for(o=u;o<c-n;o++)i=o+r,(s=o+n)in l?l[i]=l[s]:delete l[i];for(o=c;o>c-n+r;o--)delete l[o-1]}else if(r>n)for(o=c-n;o>u;o--)i=o+r-1,(s=o+n-1)in l?l[i]=l[s]:delete l[i];for(o=0;o<r;o++)l[o+u]=arguments[o+2];return l.length=c-n+r,a}});var Ao={};Ao[U("toStringTag")]="z";var yn=String(Ao)==="[object z]",hu=U("toStringTag"),pu=He(function(){return arguments}())=="Arguments",Eo=yn?He:function(e){var t,r,n;return e===void 0?"Undefined":e===null?"Null":typeof(r=function(a,o){try{return a[o]}catch(s){}}(t=Object(e),hu))=="string"?r:pu?He(t):(n=He(t))=="Object"&&typeof t.callee=="function"?"Arguments":n},vu=yn?{}.toString:function(){return"[object "+Eo(this)+"]"};yn||Ne(Object.prototype,"toString",vu,{unsafe:!0});var Oo=function(){var e=ce(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function ka(e,t){return RegExp(e,t)}var Sr,_r,Aa={UNSUPPORTED_Y:F(function(){var e=ka("a","y");return e.lastIndex=2,e.exec("abcd")!=null}),BROKEN_CARET:F(function(){var e=ka("^r","gy");return e.lastIndex=2,e.exec("str")!=null})},or=RegExp.prototype.exec,gu=String.prototype.replace,Ro=or,kr=(Sr=/a/,_r=/b*/g,or.call(Sr,"a"),or.call(_r,"a"),Sr.lastIndex!==0||_r.lastIndex!==0),Ea=Aa.UNSUPPORTED_Y||Aa.BROKEN_CARET,Ar=/()??/.exec("")[1]!==void 0;(kr||Ar||Ea)&&(Ro=function(e){var t,r,n,a,o=this,s=Ea&&o.sticky,i=Oo.call(o),l=o.source,c=0,u=e;return s&&((i=i.replace("y","")).indexOf("g")===-1&&(i+="g"),u=String(e).slice(o.lastIndex),o.lastIndex>0&&(!o.multiline||o.multiline&&e[o.lastIndex-1]!==`
`)&&(l="(?: "+l+")",u=" "+u,c++),r=new RegExp("^(?:"+l+")",i)),Ar&&(r=new RegExp("^"+l+"$(?!\\s)",i)),kr&&(t=o.lastIndex),n=or.call(s?r:o,u),s?n?(n.input=n.input.slice(c),n[0]=n[0].slice(c),n.index=o.lastIndex,o.lastIndex+=n[0].length):o.lastIndex=0:kr&&n&&(o.lastIndex=o.global?n.index+n[0].length:t),Ar&&n&&n.length>1&&gu.call(n[0],r,function(){for(a=1;a<arguments.length-2;a++)arguments[a]===void 0&&(n[a]=void 0)}),n});var Ht=Ro;se({target:"RegExp",proto:!0,forced:/./.exec!==Ht},{exec:Ht});var Mo=RegExp.prototype,To=Mo.toString,yu=F(function(){return To.call({source:"a",flags:"b"})!="/a/b"}),mu=To.name!="toString";(yu||mu)&&Ne(RegExp.prototype,"toString",function(){var e=ce(this),t=String(e.source),r=e.flags;return"/"+t+"/"+String(r===void 0&&e instanceof RegExp&&!("flags"in Mo)?Oo.call(e):r)},{unsafe:!0});var bu=U("species"),Cu=!F(function(){var e=/./;return e.exec=function(){var t=[];return t.groups={a:"7"},t},"".replace(e,"$<a>")!=="7"}),Oa="a".replace(/./,"$0")==="$0",Ra=U("replace"),Ma=!!/./[Ra]&&/./[Ra]("a","$0")==="",xu=!F(function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var r="ab".split(e);return r.length!==2||r[0]!=="a"||r[1]!=="b"}),$o=function(e,t,r,n){var a=U(e),o=!F(function(){var f={};return f[a]=function(){return 7},""[e](f)!=7}),s=o&&!F(function(){var f=!1,d=/a/;return e==="split"&&((d={}).constructor={},d.constructor[bu]=function(){return d},d.flags="",d[a]=/./[a]),d.exec=function(){return f=!0,null},d[a](""),!f});if(!o||!s||e==="replace"&&(!Cu||!Oa||Ma)||e==="split"&&!xu){var i=/./[a],l=r(a,""[e],function(f,d,h,y,g){return d.exec===Ht?o&&!g?{done:!0,value:i.call(d,h,y)}:{done:!0,value:f.call(h,d,y)}:{done:!1}},{REPLACE_KEEPS_$0:Oa,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:Ma}),c=l[0],u=l[1];Ne(String.prototype,e,c),Ne(RegExp.prototype,a,function(f,d){return u.call(f,this,d)})}n&&Ce(RegExp.prototype[a],"sham",!0)},wu=U("match"),Po=function(e){var t;return re(e)&&((t=e[wu])!==void 0?!!t:He(e)=="RegExp")},mn=function(e){if(typeof e!="function")throw TypeError(String(e)+" is not a function");return e},Su=U("species"),_u=function(e){return function(t,r){var n,a,o=String(Ve(t)),s=bt(r),i=o.length;return s<0||s>=i?e?"":void 0:(n=o.charCodeAt(s))<55296||n>56319||s+1===i||(a=o.charCodeAt(s+1))<56320||a>57343?e?o.charAt(s):n:e?o.slice(s,s+2):a-56320+(n-55296<<10)+65536}},Ho={charAt:_u(!0)},ku=Ho.charAt,Lo=function(e,t,r){return t+(r?ku(e,t).length:1)},Gr=function(e,t){var r=e.exec;if(typeof r=="function"){var n=r.call(e,t);if(typeof n!="object")throw TypeError("RegExp exec method returned something other than an Object or null");return n}if(He(e)!=="RegExp")throw TypeError("RegExp#exec called on incompatible receiver");return Ht.call(e,t)},Au=[].push,Eu=Math.min,rt=!F(function(){return!RegExp(4294967295,"y")});$o("split",2,function(e,t,r){var n;return n="abbc".split(/(b)*/)[1]=="c"||"test".split(/(?:)/,-1).length!=4||"ab".split(/(?:ab)*/).length!=2||".".split(/(.?)(.?)/).length!=4||".".split(/()()/).length>1||"".split(/.?/).length?function(a,o){var s=String(Ve(this)),i=o===void 0?4294967295:o>>>0;if(i===0)return[];if(a===void 0)return[s];if(!Po(a))return t.call(s,a,i);for(var l,c,u,f=[],d=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(a.sticky?"y":""),h=0,y=new RegExp(a.source,d+"g");(l=Ht.call(y,s))&&!((c=y.lastIndex)>h&&(f.push(s.slice(h,l.index)),l.length>1&&l.index<s.length&&Au.apply(f,l.slice(1)),u=l[0].length,h=c,f.length>=i));)y.lastIndex===l.index&&y.lastIndex++;return h===s.length?!u&&y.test("")||f.push(""):f.push(s.slice(h)),f.length>i?f.slice(0,i):f}:"0".split(void 0,0).length?function(a,o){return a===void 0&&o===0?[]:t.call(this,a,o)}:t,[function(a,o){var s=Ve(this),i=a==null?void 0:a[e];return i!==void 0?i.call(a,s,o):n.call(String(s),a,o)},function(a,o){var s=r(n,a,this,o,n!==t);if(s.done)return s.value;var i=ce(a),l=String(this),c=function(w,A){var _,T=ce(w).constructor;return T===void 0||(_=ce(T)[Su])==null?A:mn(_)}(i,RegExp),u=i.unicode,f=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(rt?"y":"g"),d=new c(rt?i:"^(?:"+i.source+")",f),h=o===void 0?4294967295:o>>>0;if(h===0)return[];if(l.length===0)return Gr(d,l)===null?[l]:[];for(var y=0,g=0,v=[];g<l.length;){d.lastIndex=rt?g:0;var p,b=Gr(d,rt?l:l.slice(g));if(b===null||(p=Eu(ge(d.lastIndex+(rt?0:g)),l.length))===y)g=Lo(l,g,u);else{if(v.push(l.slice(y,g)),v.length===h)return v;for(var x=1;x<=b.length-1;x++)if(v.push(b[x]),v.length===h)return v;g=y=p}}return v.push(l.slice(y)),v}]},!rt);var qr=`	
\v\f\r                　\u2028\u2029\uFEFF`,ir="["+qr+"]",Ou=RegExp("^"+ir+ir+"*"),Ru=RegExp(ir+ir+"*$"),Mu=function(e){return function(t){var r=String(Ve(t));return 1&e&&(r=r.replace(Ou,"")),2&e&&(r=r.replace(Ru,"")),r}},Tu={trim:Mu(3)},$u=Tu.trim;se({target:"String",proto:!0,forced:function(e){return F(function(){return!!qr[e]()||"​᠎"[e]()!="​᠎"||qr[e].name!==e})}("trim")},{trim:function(){return $u(this)}});var Pu=gn("slice"),Hu=Ct("slice",{ACCESSORS:!0,0:0,1:2}),Lu=U("species"),Iu=[].slice,Bu=Math.max;se({target:"Array",proto:!0,forced:!Pu||!Hu},{slice:function(e,t){var r,n,a,o=mt(this),s=ge(o.length),i=tr(e,s),l=tr(t===void 0?s:t,s);if(dt(o)&&(typeof(r=o.constructor)!="function"||r!==Array&&!dt(r.prototype)?re(r)&&(r=r[Lu])===null&&(r=void 0):r=void 0,r===Array||r===void 0))return Iu.call(o,i,l);for(n=new(r===void 0?Array:r)(Bu(l-i,0)),a=0;i<l;i++,a++)i in o&&ht(n,a,o[i]);return n.length=a,n}});var bn=Object.keys||function(e){return So(e,rr)},Du=F(function(){bn(1)});se({target:"Object",stat:!0,forced:Du},{keys:function(e){return bn(Be(e))}});var Er,Vu=function(e){if(Po(e))throw TypeError("The method doesn't accept regular expressions");return e},ju=U("match"),Nu=fn.f,Ta="".startsWith,Fu=Math.min,Io=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[ju]=!1,"/./"[e](t)}catch(n){}}return!1}("startsWith"),Wu=!(Io||(Er=Nu(String.prototype,"startsWith"),!Er||Er.writable));function Bo(e){return(Bo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}se({target:"String",proto:!0,forced:!Wu&&!Io},{startsWith:function(e){var t=String(Ve(this));Vu(e);var r=ge(Fu(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return Ta?Ta.call(t,n,r):t.slice(r,r+n.length)===n}});var nt=function(e){return typeof e=="string"},at=function(e){return e!==null&&Bo(e)==="object"},Lt=function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"isWindow",value:function(t){return t===window}},{key:"addEventListener",value:function(t,r,n){var a=arguments.length>3&&arguments[3]!==void 0&&arguments[3];t&&r&&n&&t.addEventListener(r,n,a)}},{key:"removeEventListener",value:function(t,r,n){var a=arguments.length>3&&arguments[3]!==void 0&&arguments[3];t&&r&&n&&t.removeEventListener(r,n,a)}},{key:"triggerDragEvent",value:function(t,r){var n=!1,a=function(s){var i;(i=r.drag)===null||i===void 0||i.call(r,s)},o=function s(i){var l;e.removeEventListener(document,"mousemove",a),e.removeEventListener(document,"mouseup",s),document.onselectstart=null,document.ondragstart=null,n=!1,(l=r.end)===null||l===void 0||l.call(r,i)};e.addEventListener(t,"mousedown",function(s){var i;n||(document.onselectstart=function(){return!1},document.ondragstart=function(){return!1},e.addEventListener(document,"mousemove",a),e.addEventListener(document,"mouseup",o),n=!0,(i=r.start)===null||i===void 0||i.call(r,s))})}},{key:"getBoundingClientRect",value:function(t){return t&&at(t)&&t.nodeType===1?t.getBoundingClientRect():null}},{key:"hasClass",value:function(t,r){return!!(t&&at(t)&&nt(r)&&t.nodeType===1)&&t.classList.contains(r.trim())}},{key:"addClass",value:function(t,r){if(t&&at(t)&&nt(r)&&t.nodeType===1&&(r=r.trim(),!e.hasClass(t,r))){var n=t.className;t.className=n?n+" "+r:r}}},{key:"removeClass",value:function(t,r){if(t&&at(t)&&nt(r)&&t.nodeType===1&&typeof t.className=="string"){r=r.trim();for(var n=t.className.trim().split(" "),a=n.length-1;a>=0;a--)n[a]=n[a].trim(),n[a]&&n[a]!==r||n.splice(a,1);t.className=n.join(" ")}}},{key:"toggleClass",value:function(t,r,n){t&&at(t)&&nt(r)&&t.nodeType===1&&t.classList.toggle(r,n)}},{key:"replaceClass",value:function(t,r,n){t&&at(t)&&nt(r)&&nt(n)&&t.nodeType===1&&(r=r.trim(),n=n.trim(),e.removeClass(t,r),e.addClass(t,n))}},{key:"getScrollTop",value:function(t){var r="scrollTop"in t?t.scrollTop:t.pageYOffset;return Math.max(r,0)}},{key:"setScrollTop",value:function(t,r){"scrollTop"in t?t.scrollTop=r:t.scrollTo(t.scrollX,r)}},{key:"getRootScrollTop",value:function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}},{key:"setRootScrollTop",value:function(t){e.setScrollTop(window,t),e.setScrollTop(document.body,t)}},{key:"getElementTop",value:function(t,r){if(e.isWindow(t))return 0;var n=r?e.getScrollTop(r):e.getRootScrollTop();return t.getBoundingClientRect().top+n}},{key:"getVisibleHeight",value:function(t){return e.isWindow(t)?t.innerHeight:t.getBoundingClientRect().height}},{key:"isHidden",value:function(t){if(!t)return!1;var r=window.getComputedStyle(t),n=r.display==="none",a=t.offsetParent===null&&r.position!=="fixed";return n||a}},{key:"triggerEvent",value:function(t,r){if("createEvent"in document){var n=document.createEvent("HTMLEvents");n.initEvent(r,!1,!0),t.dispatchEvent(n)}}},{key:"calcAngle",value:function(t,r){var n=t.getBoundingClientRect(),a=n.left+n.width/2,o=n.top+n.height/2,s=Math.abs(a-r.clientX),i=Math.abs(o-r.clientY),l=i/Math.sqrt(Math.pow(s,2)+Math.pow(i,2)),c=Math.acos(l),u=Math.floor(180/(Math.PI/c));return r.clientX>a&&r.clientY>o&&(u=180-u),r.clientX==a&&r.clientY>o&&(u=180),r.clientX>a&&r.clientY==o&&(u=90),r.clientX<a&&r.clientY>o&&(u=180+u),r.clientX<a&&r.clientY==o&&(u=270),r.clientX<a&&r.clientY<o&&(u=360-u),u}},{key:"querySelector",value:function(t,r){return r?r.querySelector(t):document.querySelector(t)}},{key:"createElement",value:function(t){for(var r=document.createElement(t),n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];for(var s=0;s<a.length;s++)a[s]&&r.classList.add(a[s]);return r}},{key:"appendChild",value:function(t){for(var r=0;r<(arguments.length<=1?0:arguments.length-1);r++)t.appendChild(r+1<1||arguments.length<=r+1?void 0:arguments[r+1])}},{key:"getWindow",value:function(t){if(t.toString()!=="[object Window]"){var r=t.ownerDocument;return r&&r.defaultView||window}return t}},{key:"isElement",value:function(t){return t instanceof this.getWindow(t).Element||t instanceof Element}},{key:"isHTMLElement",value:function(t){return t instanceof this.getWindow(t).HTMLElement||t instanceof HTMLElement}},{key:"isShadowRoot",value:function(t){return typeof ShadowRoot!="undefined"&&(t instanceof this.getWindow(t).ShadowRoot||t instanceof ShadowRoot)}},{key:"getWindowScroll",value:function(t){var r=this.getWindow(t);return{scrollLeft:r.pageXOffset||0,scrollTop:r.pageYOffset||0}}}]),e}(),zu=Math.floor,Ku="".replace,Uu=/\$([$&'`]|\d\d?|<[^>]*>)/g,Gu=/\$([$&'`]|\d\d?)/g,qu=function(e,t,r,n,a,o){var s=r+e.length,i=n.length,l=Gu;return a!==void 0&&(a=Be(a),l=Uu),Ku.call(o,l,function(c,u){var f;switch(u.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(s);case"<":f=a[u.slice(1,-1)];break;default:var d=+u;if(d===0)return c;if(d>i){var h=zu(d/10);return h===0?c:h<=i?n[h-1]===void 0?u.charAt(1):n[h-1]+u.charAt(1):c}f=n[d-1]}return f===void 0?"":f})},Xu=Math.max,Yu=Math.min;$o("replace",2,function(e,t,r,n){var a=n.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,o=n.REPLACE_KEEPS_$0,s=a?"$":"$0";return[function(i,l){var c=Ve(this),u=i==null?void 0:i[e];return u!==void 0?u.call(i,c,l):t.call(String(c),i,l)},function(i,l){if(!a&&o||typeof l=="string"&&l.indexOf(s)===-1){var c=r(t,i,this,l);if(c.done)return c.value}var u=ce(i),f=String(this),d=typeof l=="function";d||(l=String(l));var h=u.global;if(h){var y=u.unicode;u.lastIndex=0}for(var g=[];;){var v=Gr(u,f);if(v===null||(g.push(v),!h))break;String(v[0])===""&&(u.lastIndex=Lo(f,ge(u.lastIndex),y))}for(var p,b="",x=0,w=0;w<g.length;w++){v=g[w];for(var A=String(v[0]),_=Xu(Yu(bt(v.index),f.length),0),T=[],L=1;L<v.length;L++)T.push((p=v[L])===void 0?p:String(p));var H=v.groups;if(d){var D=[A].concat(T,_,f);H!==void 0&&D.push(H);var m=String(l.apply(void 0,D))}else m=qu(A,f,_,T,H,l);_>=x&&(b+=f.slice(x,_)+m,x=_+A.length)}return b+f.slice(x)}]});(function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"camelize",value:function(t){return t.replace(/-(\w)/g,function(r,n){return n?n.toUpperCase():""})}},{key:"capitalize",value:function(t){return t.charAt(0).toUpperCase()+t.slice(1)}}]),e})();(function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"_clone",value:function(){}}]),e})();var Do=U("isConcatSpreadable"),Zu=ar>=51||!F(function(){var e=[];return e[Do]=!1,e.concat()[0]!==e}),Ju=gn("concat"),Qu=function(e){if(!re(e))return!1;var t=e[Do];return t!==void 0?!!t:dt(e)};se({target:"Array",proto:!0,forced:!Zu||!Ju},{concat:function(e){var t,r,n,a,o,s=Be(this),i=gr(s,0),l=0;for(t=-1,n=arguments.length;t<n;t++)if(Qu(o=t===-1?s:arguments[t])){if(l+(a=ge(o.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<a;r++,l++)r in o&&ht(i,l,o[r])}else{if(l>=9007199254740991)throw TypeError("Maximum allowed index exceeded");ht(i,l++,o)}return i.length=l,i}});var Or,Vt=function(e,t,r){if(mn(e),t===void 0)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,a){return e.call(t,n,a)};case 3:return function(n,a,o){return e.call(t,n,a,o)}}return function(){return e.apply(t,arguments)}},$a=[].push,Pa=function(e){var t=e==1,r=e==2,n=e==3,a=e==4,o=e==6,s=e==7,i=e==5||o;return function(l,c,u,f){for(var d,h,y=Be(l),g=hr(y),v=Vt(c,u,3),p=ge(g.length),b=0,x=f||gr,w=t?x(l,p):r||s?x(l,0):void 0;p>b;b++)if((i||b in g)&&(h=v(d=g[b],b,y),e))if(t)w[b]=h;else if(h)switch(e){case 3:return!0;case 5:return d;case 6:return b;case 2:$a.call(w,d)}else switch(e){case 4:return!1;case 7:$a.call(w,d)}return o?-1:n||a?a:w}},Vo={find:Pa(5),findIndex:Pa(6)},ec=me?Object.defineProperties:function(e,t){ce(e);for(var r,n=bn(t),a=n.length,o=0;a>o;)Ie.f(e,r=n[o++],t[r]);return e},tc=vr("document","documentElement"),jo=pn("IE_PROTO"),Rr=function(){},Ha=function(e){return"<script>"+e+"<\/script>"},qt=function(){try{Or=document.domain&&new ActiveXObject("htmlfile")}catch(n){}var e,t;qt=Or?function(n){n.write(Ha("")),n.close();var a=n.parentWindow.Object;return n=null,a}(Or):((t=mo("iframe")).style.display="none",tc.appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write(Ha("document.F=Object")),e.close(),e.F);for(var r=rr.length;r--;)delete qt.prototype[rr[r]];return qt()};pr[jo]=!0;var Cn=Object.create||function(e,t){var r;return e!==null?(Rr.prototype=ce(e),r=new Rr,Rr.prototype=null,r[jo]=e):r=qt(),t===void 0?r:ec(r,t)},Xr=U("unscopables"),Yr=Array.prototype;Yr[Xr]==null&&Ie.f(Yr,Xr,{configurable:!0,value:Cn(null)});var it=function(e){Yr[Xr][e]=!0},rc=Vo.find,La=!0,nc=Ct("find");"find"in[]&&Array(1).find(function(){La=!1}),se({target:"Array",proto:!0,forced:La||!nc},{find:function(e){return rc(this,e,arguments.length>1?arguments[1]:void 0)}}),it("find");var ac=Vo.findIndex,Ia=!0,oc=Ct("findIndex");"findIndex"in[]&&Array(1).findIndex(function(){Ia=!1}),se({target:"Array",proto:!0,forced:Ia||!oc},{findIndex:function(e){return ac(this,e,arguments.length>1?arguments[1]:void 0)}}),it("findIndex");var No=function(e,t,r,n,a,o,s,i){for(var l,c=a,u=0,f=!!s&&Vt(s,i,3);u<n;){if(u in r){if(l=f?f(r[u],u,t):r[u],o>0&&dt(l))c=No(e,t,l,ge(l.length),c,o-1)-1;else{if(c>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[c]=l}c++}u++}return c},ic=No;se({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,t=Be(this),r=ge(t.length),n=gr(t,0);return n.length=ic(n,t,t,r,0,e===void 0?1:bt(e)),n}});var Zr=function(e){var t=e.return;if(t!==void 0)return ce(t.call(e)).value},lc=function(e,t,r,n){try{return n?t(ce(r)[0],r[1]):t(r)}catch(a){throw Zr(e),a}},pt={},sc=U("iterator"),uc=Array.prototype,Fo=function(e){return e!==void 0&&(pt.Array===e||uc[sc]===e)},cc=U("iterator"),Wo=function(e){if(e!=null)return e[cc]||e["@@iterator"]||pt[Eo(e)]},zo=U("iterator"),Ko=!1;try{var fc=0,Ba={next:function(){return{done:!!fc++}},return:function(){Ko=!0}};Ba[zo]=function(){return this},Array.from(Ba,function(){throw 2})}catch(e){}var Uo=function(e,t){if(!Ko)return!1;var r=!1;try{var n={};n[zo]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(a){}return r},dc=!Uo(function(e){Array.from(e)});se({target:"Array",stat:!0,forced:dc},{from:function(e){var t,r,n,a,o,s,i=Be(e),l=typeof this=="function"?this:Array,c=arguments.length,u=c>1?arguments[1]:void 0,f=u!==void 0,d=Wo(i),h=0;if(f&&(u=Vt(u,c>2?arguments[2]:void 0,2)),d==null||l==Array&&Fo(d))for(r=new l(t=ge(i.length));t>h;h++)s=f?u(i[h],h):i[h],ht(r,h,s);else for(o=(a=d.call(i)).next,r=new l;!(n=o.call(a)).done;h++)s=f?lc(a,u,[n.value,h],!0):n.value,ht(r,h,s);return r.length=h,r}});var hc=function(e){return function(t,r,n,a){mn(r);var o=Be(t),s=hr(o),i=ge(o.length),l=e?i-1:0,c=e?-1:1;if(n<2)for(;;){if(l in s){a=s[l],l+=c;break}if(l+=c,e?l<0:i<=l)throw TypeError("Reduce of empty array with no initial value")}for(;e?l>=0:i>l;l+=c)l in s&&(a=r(a,s[l],l,o));return a}},pc={left:hc(!1)},vc=He(te.process)=="process",gc=pc.left,yc=vn("reduce"),mc=Ct("reduce",{1:0});se({target:"Array",proto:!0,forced:!yc||!mc||!vc&&ar>79&&ar<83},{reduce:function(e){return gc(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}}),it("flat");var Ke,Da,Va,bc=!F(function(){return Object.isExtensible(Object.preventExtensions({}))}),Go=un(function(e){var t=Ie.f,r=hn("meta"),n=0,a=Object.isExtensible||function(){return!0},o=function(i){t(i,r,{value:{objectID:"O"+ ++n,weakData:{}}})},s=e.exports={REQUIRED:!1,fastKey:function(i,l){if(!re(i))return typeof i=="symbol"?i:(typeof i=="string"?"S":"P")+i;if(!ee(i,r)){if(!a(i))return"F";if(!l)return"E";o(i)}return i[r].objectID},getWeakData:function(i,l){if(!ee(i,r)){if(!a(i))return!0;if(!l)return!1;o(i)}return i[r].weakData},onFreeze:function(i){return bc&&s.REQUIRED&&a(i)&&!ee(i,r)&&o(i),i}};pr[r]=!0}),St=function(e,t){this.stopped=e,this.result=t},ja=function(e,t,r){var n,a,o,s,i,l,c,u=r&&r.that,f=!(!r||!r.AS_ENTRIES),d=!(!r||!r.IS_ITERATOR),h=!(!r||!r.INTERRUPTED),y=Vt(t,u,1+f+h),g=function(p){return n&&Zr(n),new St(!0,p)},v=function(p){return f?(ce(p),h?y(p[0],p[1],g):y(p[0],p[1])):h?y(p,g):y(p)};if(d)n=e;else{if(typeof(a=Wo(e))!="function")throw TypeError("Target is not iterable");if(Fo(a)){for(o=0,s=ge(e.length);s>o;o++)if((i=v(e[o]))&&i instanceof St)return i;return new St(!1)}n=a.call(e)}for(l=n.next;!(c=l.call(n)).done;){try{i=v(c.value)}catch(p){throw Zr(n),p}if(typeof i=="object"&&i&&i instanceof St)return i}return new St(!1)},Na=function(e,t,r){if(!(e instanceof t))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation");return e},Cc=Ie.f,Fa=U("toStringTag"),Jr=function(e,t,r){e&&!ee(e=r?e:e.prototype,Fa)&&Cc(e,Fa,{configurable:!0,value:t})},lr=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),t=r instanceof Array}catch(n){}return function(n,a){return ce(n),function(o){if(!re(o)&&o!==null)throw TypeError("Can't set "+String(o)+" as a prototype")}(a),t?e.call(n,a):n.__proto__=a,n}}():void 0),Wa=function(e,t,r){for(var n in t)Ne(e,n,t[n],r);return e},xc=!F(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),za=pn("IE_PROTO"),wc=Object.prototype,sr=xc?Object.getPrototypeOf:function(e){return e=Be(e),ee(e,za)?e[za]:typeof e.constructor=="function"&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?wc:null},Mr=U("iterator"),qo=!1;[].keys&&("next"in(Va=[].keys())?(Da=sr(sr(Va)))!==Object.prototype&&(Ke=Da):qo=!0),(Ke==null||F(function(){var e={};return Ke[Mr].call(e)!==e}))&&(Ke={}),ee(Ke,Mr)||Ce(Ke,Mr,function(){return this});var xn={IteratorPrototype:Ke,BUGGY_SAFARI_ITERATORS:qo},Sc=xn.IteratorPrototype,_c=function(){return this},Tr=xn.IteratorPrototype,Ut=xn.BUGGY_SAFARI_ITERATORS,_t=U("iterator"),kc=function(){return this},wn=function(e,t,r,n,a,o,s){(function(p,b,x){var w=b+" Iterator";p.prototype=Cn(Sc,{next:dr(1,x)}),Jr(p,w,!1),pt[w]=_c})(r,t,n);var i,l,c,u=function(p){if(p===a&&g)return g;if(!Ut&&p in h)return h[p];switch(p){case"keys":case"values":case"entries":return function(){return new r(this,p)}}return function(){return new r(this)}},f=t+" Iterator",d=!1,h=e.prototype,y=h[_t]||h["@@iterator"]||a&&h[a],g=!Ut&&y||u(a),v=t=="Array"&&h.entries||y;if(v&&(i=sr(v.call(new e)),Tr!==Object.prototype&&i.next&&(sr(i)!==Tr&&(lr?lr(i,Tr):typeof i[_t]!="function"&&Ce(i,_t,kc)),Jr(i,f,!0))),a=="values"&&y&&y.name!=="values"&&(d=!0,g=function(){return y.call(this)}),h[_t]!==g&&Ce(h,_t,g),pt[t]=g,a)if(l={values:u("values"),keys:o?g:u("keys"),entries:u("entries")},s)for(c in l)(Ut||d||!(c in h))&&Ne(h,c,l[c]);else se({target:t,proto:!0,forced:Ut||d},l);return l},Ka=U("species"),Ac=Ie.f,Ua=Go.fastKey,Ga=je.set,$r=je.getterFor;(function(e,t,r){var n=e.indexOf("Map")!==-1,a=e.indexOf("Weak")!==-1,o=n?"set":"add",s=te[e],i=s&&s.prototype,l=s,c={},u=function(v){var p=i[v];Ne(i,v,v=="add"?function(b){return p.call(this,b===0?0:b),this}:v=="delete"?function(b){return!(a&&!re(b))&&p.call(this,b===0?0:b)}:v=="get"?function(b){return a&&!re(b)?void 0:p.call(this,b===0?0:b)}:v=="has"?function(b){return!(a&&!re(b))&&p.call(this,b===0?0:b)}:function(b,x){return p.call(this,b===0?0:b,x),this})};if(Ur(e,typeof s!="function"||!(a||i.forEach&&!F(function(){new s().entries().next()}))))l=r.getConstructor(t,e,n,o),Go.REQUIRED=!0;else if(Ur(e,!0)){var f=new l,d=f[o](a?{}:-0,1)!=f,h=F(function(){f.has(1)}),y=Uo(function(v){new s(v)}),g=!a&&F(function(){for(var v=new s,p=5;p--;)v[o](p,p);return!v.has(-0)});y||((l=t(function(v,p){Na(v,l,e);var b=function(x,w,A){var _,T;return lr&&typeof(_=w.constructor)=="function"&&_!==A&&re(T=_.prototype)&&T!==A.prototype&&lr(x,T),x}(new s,v,l);return p!=null&&ja(p,b[o],{that:b,AS_ENTRIES:n}),b})).prototype=i,i.constructor=l),(h||g)&&(u("delete"),u("has"),n&&u("get")),(g||d)&&u(o),a&&i.clear&&delete i.clear}c[e]=l,se({global:!0,forced:l!=s},c),Jr(l,e),a||r.setStrong(l,e,n)})("Set",function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},{getConstructor:function(e,t,r,n){var a=e(function(l,c){Na(l,a,t),Ga(l,{type:t,index:Cn(null),first:void 0,last:void 0,size:0}),me||(l.size=0),c!=null&&ja(c,l[n],{that:l,AS_ENTRIES:r})}),o=$r(t),s=function(l,c,u){var f,d,h=o(l),y=i(l,c);return y?y.value=u:(h.last=y={index:d=Ua(c,!0),key:c,value:u,previous:f=h.last,next:void 0,removed:!1},h.first||(h.first=y),f&&(f.next=y),me?h.size++:l.size++,d!=="F"&&(h.index[d]=y)),l},i=function(l,c){var u,f=o(l),d=Ua(c);if(d!=="F")return f.index[d];for(u=f.first;u;u=u.next)if(u.key==c)return u};return Wa(a.prototype,{clear:function(){for(var l=o(this),c=l.index,u=l.first;u;)u.removed=!0,u.previous&&(u.previous=u.previous.next=void 0),delete c[u.index],u=u.next;l.first=l.last=void 0,me?l.size=0:this.size=0},delete:function(l){var c=this,u=o(c),f=i(c,l);if(f){var d=f.next,h=f.previous;delete u.index[f.index],f.removed=!0,h&&(h.next=d),d&&(d.previous=h),u.first==f&&(u.first=d),u.last==f&&(u.last=h),me?u.size--:c.size--}return!!f},forEach:function(l){for(var c,u=o(this),f=Vt(l,arguments.length>1?arguments[1]:void 0,3);c=c?c.next:u.first;)for(f(c.value,c.key,this);c&&c.removed;)c=c.previous},has:function(l){return!!i(this,l)}}),Wa(a.prototype,r?{get:function(l){var c=i(this,l);return c&&c.value},set:function(l,c){return s(this,l===0?0:l,c)}}:{add:function(l){return s(this,l=l===0?0:l,l)}}),me&&Ac(a.prototype,"size",{get:function(){return o(this).size}}),a},setStrong:function(e,t,r){var n=t+" Iterator",a=$r(t),o=$r(n);wn(e,t,function(s,i){Ga(this,{type:n,target:s,state:a(s),kind:i,last:void 0})},function(){for(var s=o(this),i=s.kind,l=s.last;l&&l.removed;)l=l.previous;return s.target&&(s.last=l=l?l.next:s.state.first)?i=="keys"?{value:l.key,done:!1}:i=="values"?{value:l.value,done:!1}:{value:[l.key,l.value],done:!1}:(s.target=void 0,{value:void 0,done:!0})},r?"entries":"values",!r,!0),function(s){var i=vr(s),l=Ie.f;me&&i&&!i[Ka]&&l(i,Ka,{configurable:!0,get:function(){return this}})}(t)}});var Ec=Ho.charAt,Oc=je.set,Rc=je.getterFor("String Iterator");wn(String,"String",function(e){Oc(this,{type:"String Iterator",string:String(e),index:0})},function(){var e,t=Rc(this),r=t.string,n=t.index;return n>=r.length?{value:void 0,done:!0}:(e=Ec(r,n),t.index+=e.length,{value:e,done:!1})});var qa={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Mc=je.set,Tc=je.getterFor("Array Iterator"),At=wn(Array,"Array",function(e,t){Mc(this,{type:"Array Iterator",target:mt(e),index:0,kind:t})},function(){var e=Tc(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):r=="keys"?{value:n,done:!1}:r=="values"?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}},"values");pt.Arguments=pt.Array,it("keys"),it("values"),it("entries");var Pr=U("iterator"),Xa=U("toStringTag"),Hr=At.values;for(var Lr in qa){var Ya=te[Lr],Te=Ya&&Ya.prototype;if(Te){if(Te[Pr]!==Hr)try{Ce(Te,Pr,Hr)}catch(e){Te[Pr]=Hr}if(Te[Xa]||Ce(Te,Xa,Lr),qa[Lr]){for(var ot in At)if(Te[ot]!==At[ot])try{Ce(Te,ot,At[ot])}catch(e){Te[ot]=At[ot]}}}}(function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"deduplicate",value:function(t){return Array.from(new Set(t))}},{key:"flat",value:function(t){return t.reduce(function(r,n){var a=Array.isArray(n)?e.flat(n):n;return r.concat(a)},[])}},{key:"find",value:function(t,r){return t.find(r)}},{key:"findIndex",value:function(t,r){return t.findIndex(r)}}]),e})();(function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"today",value:function(){return new Date}}]),e})();(function(){function e(){Ye(this,e)}return Ze(e,null,[{key:"range",value:function(t,r,n){return Math.min(Math.max(t,r),n)}},{key:"clamp",value:function(t,r,n){return r<n?t<r?r:t>n?n:t:t<n?n:t>r?r:t}}]),e})();var $c=Object.defineProperty,Pc=(e,t,r)=>t in e?$c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,oe=(e,t,r)=>(Pc(e,typeof t!="symbol"?t+"":t,r),r);const X=e=>Math.round(e*100)/100;class K{constructor(t){oe(this,"instance"),oe(this,"alphaValue",0),oe(this,"redValue",0),oe(this,"greenValue",0),oe(this,"blueValue",0),oe(this,"hueValue",0),oe(this,"saturationValue",0),oe(this,"brightnessValue",0),oe(this,"hslSaturationValue",0),oe(this,"lightnessValue",0),oe(this,"initAlpha",()=>{const r=this.instance.getAlpha();this.alphaValue=Math.min(1,r)*100}),oe(this,"initLightness",()=>{const{s:r,l:n}=this.instance.toHsl();this.hslSaturationValue=X(r),this.lightnessValue=X(n)}),oe(this,"initRgb",()=>{const{r,g:n,b:a}=this.instance.toRgb();this.redValue=X(r),this.greenValue=X(n),this.blueValue=X(a)}),oe(this,"initHsb",()=>{const{h:r,s:n,v:a}=this.instance.toHsv();this.hueValue=Math.min(360,Math.ceil(r)),this.saturationValue=X(n),this.brightnessValue=X(a)}),oe(this,"toHexString",()=>this.instance.toHexString()),oe(this,"toRgbString",()=>this.instance.toRgbString()),this.instance=C(t),this.initRgb(),this.initHsb(),this.initLightness(),this.initAlpha()}toString(t){return this.instance.toString(t)}get hex(){return this.instance.toHex()}set hex(t){this.instance=C(t),this.initHsb(),this.initRgb(),this.initAlpha(),this.initLightness()}set hue(t){this.saturation===0&&this.brightness===0&&(this.saturationValue=1,this.brightnessValue=1),this.instance=C({h:X(t),s:this.saturation,v:this.brightness,a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.hueValue=X(t)}get hue(){return this.hueValue}set saturation(t){this.instance=C({h:this.hue,s:X(t),v:this.brightness,a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.saturationValue=X(t)}get saturation(){return this.saturationValue}set brightness(t){this.instance=C({h:this.hue,s:this.saturation,v:X(t),a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.brightnessValue=X(t)}get brightness(){return this.brightnessValue}set lightness(t){this.instance=C({h:this.hue,s:this.hslSaturationValue,l:X(t),a:this.alphaValue/100}),this.initRgb(),this.initHsb(),this.lightnessValue=X(t)}get lightness(){return this.lightnessValue}set red(t){const r=this.instance.toRgb();this.instance=C(Qe(Je({},r),{r:X(t),a:this.alphaValue/100})),this.initHsb(),this.initLightness(),this.redValue=X(t)}get red(){return this.redValue}set green(t){const r=this.instance.toRgb();this.instance=C(Qe(Je({},r),{g:X(t),a:this.alphaValue/100})),this.initHsb(),this.initLightness(),this.greenValue=X(t)}get green(){return this.greenValue}set blue(t){const r=this.instance.toRgb();this.instance=C(Qe(Je({},r),{b:X(t),a:this.alphaValue/100})),this.initHsb(),this.initLightness(),this.blueValue=X(t)}get blue(){return this.blueValue}set alpha(t){this.instance.setAlpha(t/100),this.alphaValue=t}get alpha(){return this.alphaValue}get RGB(){return[this.red,this.green,this.blue,parseFloat((this.alpha/100).toFixed(2))]}get HSB(){return[this.hue,this.saturation,this.brightness,parseFloat((this.alpha/100).toFixed(2))]}get HSL(){return[this.hue,this.hslSaturationValue,this.lightness,parseFloat((this.alpha/100).toFixed(2))]}}function Za(e,t,r,n){return`rgba(${[e,t,r,n/100].join(",")})`}const Ir=(e,t,r)=>t<r?e<t?t:e>r?r:e:e<r?r:e>t?t:e,Sn="color-history",_n=8,Se=(e,t)=>{const r=e.__vccOpts||e;for(const[n,a]of t)r[n]=a;return r},Hc=he({name:"Alpha",props:{color:M.instanceOf(K),size:M.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=J(null),n=J(null);let a=e.color||new K;const o=Ee({red:a.red,green:a.green,blue:a.blue,alpha:a.alpha});lt(()=>e.color,f=>{f&&(a=f,ur(o,{red:f.red,green:f.green,blue:f.blue,alpha:f.alpha}))},{deep:!0});const s=Q(()=>{const f=Za(o.red,o.green,o.blue,0),d=Za(o.red,o.green,o.blue,100);return{background:`linear-gradient(to right, ${f} , ${d})`}}),i=()=>{if(r.value&&n.value){const f=o.alpha/100,d=r.value.getBoundingClientRect(),h=n.value.offsetWidth;return Math.round(f*(d.width-h)+h/2)}return 0},l=Q(()=>({left:i()+"px",top:0})),c=f=>{f.target!==r.value&&u(f)},u=f=>{if(f.stopPropagation(),r.value&&n.value){const d=r.value.getBoundingClientRect(),h=n.value.offsetWidth;let y=f.clientX-d.left;y=Math.max(h/2,y),y=Math.min(y,d.width-h/2);const g=Math.round((y-h/2)/(d.width-h)*100);a.alpha=g,o.alpha=g,t("change",g)}};return vt(()=>{const f={drag:d=>{u(d)},end:d=>{u(d)}};r.value&&n.value&&Lt.triggerDragEvent(r.value,f)}),{barElement:r,cursorElement:n,getCursorStyle:l,getBackgroundStyle:s,onClickSider:c}}}),Lc=e=>(gt("data-v-18925ba6"),e=e(),yt(),e),Ic=Lc(()=>k("div",{class:"vc-alpha-slider__bar-handle"},null,-1)),Bc=[Ic];function Dc(e,t,r,n,a,o){return O(),I("div",{class:le(["vc-alpha-slider","transparent",{"small-slider":e.size==="small"}])},[k("div",{ref:"barElement",class:"vc-alpha-slider__bar",style:ne(e.getBackgroundStyle),onClick:t[0]||(t[0]=(...s)=>e.onClickSider&&e.onClickSider(...s))},[k("div",{class:le(["vc-alpha-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:ne(e.getCursorStyle)},Bc,6)],4)],2)}const kn=Se(Hc,[["render",Dc],["__scopeId","data-v-18925ba6"]]),Vc=[["#fcc02e","#f67c01","#e64a19","#d81b43","#8e24aa","#512da7","#1f87e8","#008781","#05a045"],["#fed835","#fb8c00","#f5511e","#eb1d4e","#9c28b1","#5d35b0","#2097f3","#029688","#4cb050"],["#ffeb3c","#ffa727","#fe5722","#eb4165","#aa47bc","#673bb7","#42a5f6","#26a59a","#83c683"],["#fff176","#ffb74e","#ff8a66","#f1627e","#b968c7","#7986cc","#64b5f6","#80cbc4","#a5d6a7"],["#fff59c","#ffcc80","#ffab91","#fb879e","#cf93d9","#9ea8db","#90caf8","#b2dfdc","#c8e6ca"],["transparent","#ffffff","#dedede","#a9a9a9","#4b4b4b","#353535","#212121","#000000","advance"]],jc=he({name:"Palette",emits:["change"],setup(e,{emit:t}){return{palettes:Vc,computedBgStyle:r=>r==="transparent"?r:r==="advance"?{}:{background:C(r).toRgbString()},onColorChange:r=>{t("change",r)}}}}),Nc={class:"vc-compact"},Fc=["onClick"];function Wc(e,t,r,n,a,o){return O(),I("div",Nc,[(O(!0),I(Ge,null,Tt(e.palettes,(s,i)=>(O(),I("div",{key:i,class:"vc-compact__row"},[(O(!0),I(Ge,null,Tt(s,(l,c)=>(O(),I("div",{key:c,class:"vc-compact__color-cube--wrap",onClick:u=>e.onColorChange(l)},[k("div",{class:le(["vc-compact__color_cube",{advance:l==="advance",transparent:l==="transparent"}]),style:ne(e.computedBgStyle(l))},null,6)],8,Fc))),128))]))),128))])}const Xo=Se(jc,[["render",Wc],["__scopeId","data-v-b969fd48"]]),zc=he({name:"Board",props:{color:M.instanceOf(K),round:M.bool.def(!1),hide:M.bool.def(!0)},emits:["change"],setup(e,{emit:t}){var r,n,a;const o=Ei(),s={h:((r=e.color)==null?void 0:r.hue)||0,s:1,v:1},i=new K(s).toHexString(),l=Ee({hueColor:i,saturation:((n=e.color)==null?void 0:n.saturation)||0,brightness:((a=e.color)==null?void 0:a.brightness)||0}),c=J(0),u=J(0),f=J(),d=Q(()=>({top:c.value+"px",left:u.value+"px"})),h=()=>{if(o){const x=o.vnode.el;u.value=l.saturation*(x==null?void 0:x.clientWidth),c.value=(1-l.brightness)*(x==null?void 0:x.clientHeight)}};let y=!1;const g=x=>{y=!0,b(x)},v=x=>{y&&b(x)},p=()=>{y=!1},b=x=>{if(o){const w=o.vnode.el,A=w==null?void 0:w.getBoundingClientRect();let _=x.clientX-A.left,T=x.clientY-A.top;_=Ir(_,0,A.width),T=Ir(T,0,A.height);const L=_/A.width,H=Ir(-(T/A.height)+1,0,1);u.value=_,c.value=T,l.saturation=L,l.brightness=H,t("change",L,H)}};return vt(()=>{o&&o.vnode.el&&f.value&&Oi(()=>{h()})}),ie(()=>e.color,x=>{ur(l,{hueColor:new K({h:x.hue,s:1,v:1}).toHexString(),saturation:x.saturation,brightness:x.brightness}),h()},{deep:!0}),{state:l,cursorElement:f,getCursorStyle:d,onClickBoard:g,onDrag:v,onDragEnd:p}}}),An=e=>(gt("data-v-7f0cdcdf"),e=e(),yt(),e),Kc=An(()=>k("div",{class:"vc-saturation__white"},null,-1)),Uc=An(()=>k("div",{class:"vc-saturation__black"},null,-1)),Gc=An(()=>k("div",null,null,-1)),qc=[Gc];function Xc(e,t,r,n,a,o){return O(),I("div",{ref:"boardElement",class:le(["vc-saturation",{"vc-saturation__chrome":e.round,"vc-saturation__hidden":e.hide}]),style:ne({backgroundColor:e.state.hueColor}),onMousedown:t[0]||(t[0]=(...s)=>e.onClickBoard&&e.onClickBoard(...s)),onMousemove:t[1]||(t[1]=(...s)=>e.onDrag&&e.onDrag(...s)),onMouseup:t[2]||(t[2]=(...s)=>e.onDragEnd&&e.onDragEnd(...s))},[Kc,Uc,k("div",{class:"vc-saturation__cursor",ref:"cursorElement",style:ne(e.getCursorStyle)},qc,4)],38)}const En=Se(zc,[["render",Xc],["__scopeId","data-v-7f0cdcdf"]]),Yc=he({name:"Hue",props:{color:M.instanceOf(K),size:M.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=J(null),n=J(null);let a=e.color||new K;const o=Ee({hue:a.hue||0});lt(()=>e.color,u=>{u&&(a=u,ur(o,{hue:a.hue}))},{deep:!0});const s=()=>{if(r.value&&n.value){const u=r.value.getBoundingClientRect(),f=n.value.offsetWidth;return o.hue===360?u.width-f/2:o.hue%360*(u.width-f)/360+f/2}return 0},i=Q(()=>({left:s()+"px",top:0})),l=u=>{u.target!==r.value&&c(u)},c=u=>{if(u.stopPropagation(),r.value&&n.value){const f=r.value.getBoundingClientRect(),d=n.value.offsetWidth;let h=u.clientX-f.left;h=Math.min(h,f.width-d/2),h=Math.max(d/2,h);const y=Math.round((h-d/2)/(f.width-d)*360);a.hue=y,o.hue=y,t("change",y)}};return vt(()=>{const u={drag:f=>{c(f)},end:f=>{c(f)}};r.value&&n.value&&Lt.triggerDragEvent(r.value,u)}),{barElement:r,cursorElement:n,getCursorStyle:i,onClickSider:l}}}),Zc=e=>(gt("data-v-e1a08576"),e=e(),yt(),e),Jc=Zc(()=>k("div",{class:"vc-hue-slider__bar-handle"},null,-1)),Qc=[Jc];function ef(e,t,r,n,a,o){return O(),I("div",{class:le(["vc-hue-slider",{"small-slider":e.size==="small"}])},[k("div",{ref:"barElement",class:"vc-hue-slider__bar",onClick:t[0]||(t[0]=(...s)=>e.onClickSider&&e.onClickSider(...s))},[k("div",{class:le(["vc-hue-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:ne(e.getCursorStyle)},Qc,6)],512)],2)}const On=Se(Yc,[["render",ef],["__scopeId","data-v-e1a08576"]]),tf=he({name:"Lightness",props:{color:M.instanceOf(K),size:M.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=J(null),n=J(null);let a=e.color||new K;const[o,s,i]=a.HSL,l=Ee({hue:o,saturation:s,lightness:i});lt(()=>e.color,y=>{if(y){a=y;const[g,v,p]=a.HSL;ur(l,{hue:g,saturation:v,lightness:p})}},{deep:!0});const c=Q(()=>{const y=C({h:l.hue,s:l.saturation,l:.8}).toPercentageRgbString(),g=C({h:l.hue,s:l.saturation,l:.6}).toPercentageRgbString(),v=C({h:l.hue,s:l.saturation,l:.4}).toPercentageRgbString(),p=C({h:l.hue,s:l.saturation,l:.2}).toPercentageRgbString();return{background:[`linear-gradient(to right, rgb(255, 255, 255), ${y}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`,`-webkit-linear-gradient(left, rgb(255, 255, 255), ${y}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`,`-moz-linear-gradient(left, rgb(255, 255, 255), ${y}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`,`-ms-linear-gradient(left, rgb(255, 255, 255), ${y}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`]}}),u=()=>{if(r.value&&n.value){const y=l.lightness,g=r.value.getBoundingClientRect(),v=n.value.offsetWidth;return(1-y)*(g.width-v)+v/2}return 0},f=Q(()=>({left:u()+"px",top:0})),d=y=>{y.target!==r.value&&h(y)},h=y=>{if(y.stopPropagation(),r.value&&n.value){const g=r.value.getBoundingClientRect(),v=n.value.offsetWidth;let p=y.clientX-g.left;p=Math.max(v/2,p),p=Math.min(p,g.width-v/2);const b=1-(p-v/2)/(g.width-v);a.lightness=b,t("change",b)}};return vt(()=>{const y={drag:g=>{h(g)},end:g=>{h(g)}};r.value&&n.value&&Lt.triggerDragEvent(r.value,y)}),{barElement:r,cursorElement:n,getCursorStyle:f,getBackgroundStyle:c,onClickSider:d}}}),rf=e=>(gt("data-v-94a50a9e"),e=e(),yt(),e),nf=rf(()=>k("div",{class:"vc-lightness-slider__bar-handle"},null,-1)),af=[nf];function of(e,t,r,n,a,o){return O(),I("div",{class:le(["vc-lightness-slider",{"small-slider":e.size==="small"}])},[k("div",{ref:"barElement",class:"vc-lightness-slider__bar",style:ne(e.getBackgroundStyle),onClick:t[0]||(t[0]=(...s)=>e.onClickSider&&e.onClickSider(...s))},[k("div",{class:le(["vc-lightness-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:ne(e.getCursorStyle)},af,6)],4)],2)}const Yo=Se(tf,[["render",of],["__scopeId","data-v-94a50a9e"]]),lf=he({name:"History",props:{colors:M.arrayOf(String).def(()=>[]),round:M.bool.def(!1)},emits:["change"],setup(e,{emit:t}){return{onColorSelect:r=>{t("change",r)}}}}),sf={key:0,class:"vc-colorPicker__record"},uf={class:"color-list"},cf=["onClick"];function ff(e,t,r,n,a,o){return e.colors&&e.colors.length>0?(O(),I("div",sf,[k("div",uf,[(O(!0),I(Ge,null,Tt(e.colors,(s,i)=>(O(),I("div",{key:i,class:le(["color-item","transparent",{"color-item__round":e.round}]),onClick:l=>e.onColorSelect(s)},[k("div",{class:"color-item__display",style:ne({backgroundColor:s})},null,4)],10,cf))),128))])])):j("",!0)}const Rn=Se(lf,[["render",ff],["__scopeId","data-v-0f657238"]]),df=he({name:"Display",props:{color:M.instanceOf(K),disableAlpha:M.bool.def(!1)},emits:["update:color","change"],setup(e,{emit:t}){var r,n,a,o;const{copy:s,copied:i,isSupported:l}=Ri(),c=J("hex"),u=Ee({color:e.color,hex:(r=e.color)==null?void 0:r.hex,alpha:Math.round(((n=e.color)==null?void 0:n.alpha)||100),rgba:(a=e.color)==null?void 0:a.RGB,previewBgColor:(o=e.color)==null?void 0:o.toRgbString()}),f=Q(()=>({background:u.previewBgColor})),d=()=>{c.value=c.value==="rgba"?"hex":"rgba"},h=$e(p=>{if(!p.target.value)return;let b=parseInt(p.target.value.replace("%",""));b>100&&(p.target.value="100",b=100),b<0&&(p.target.value="0",b=0),isNaN(b)&&(p.target.value="100",b=100),!isNaN(b)&&u.color&&(u.color.alpha=b),t("change",u.color)},300),y=$e((p,b)=>{if(u.color){if(c.value==="hex"){const x=p.target.value.replace("#","");C(x).isValid()?[3,4].includes(x.length)&&(u.color.hex=x):u.color.hex="000000",t("change",u.color)}else if(c.value==="rgba"&&b===3&&p.target.value.toString()==="0."&&u.rgba){u.rgba[b]=p.target.value;const[x,w,A,_]=u.rgba;u.color.hex=C({r:x,g:w,b:A}).toHex(),u.color.alpha=Math.round(_*100),t("change",u.color)}}},100),g=$e((p,b)=>{if(p.target.value){if(c.value==="hex"){const x=p.target.value.replace("#","");C(x).isValid()&&u.color&&[6,8].includes(x.length)&&(u.color.hex=x)}else if(b!==void 0&&u.rgba&&u.color){if(p.target.value<0&&(p.target.value=0),b===3&&((p.target.value>1||isNaN(p.target.value))&&(p.target.value=1),p.target.value.toString()==="0."))return;b<3&&p.target.value>255&&(p.target.value=255),u.rgba[b]=p.target.value;const[x,w,A,_]=u.rgba;u.color.hex=C({r:x,g:w,b:A}).toHex(),u.color.alpha=Math.round(_*100)}t("change",u.color)}},300),v=()=>{if(l&&u.color){const p=c.value==="hex"?u.color.toString(u.color.alpha===100?"hex6":"hex8"):u.color.toRgbString();s(p||"")}};return ie(()=>e.color,p=>{p&&(u.color=p,u.alpha=Math.round(u.color.alpha),u.hex=u.color.hex,u.rgba=u.color.RGB)},{deep:!0}),ie(()=>u.color,()=>{u.color&&(u.previewBgColor=u.color.toRgbString())},{deep:!0}),{state:u,getBgColorStyle:f,inputType:c,copied:i,onInputTypeChange:d,onAlphaBlur:h,onInputChange:g,onBlurChange:y,onCopyColorStr:v}}}),hf={class:"vc-display"},pf={class:"vc-current-color vc-transparent"},vf={key:0,class:"copy-text"},gf={key:0,style:{display:"flex",flex:"1",gap:"4px",height:"100%"}},yf={class:"vc-color-input"},mf={key:0,class:"vc-alpha-input"},bf=["value"],Cf={key:1,style:{display:"flex",flex:"1",gap:"4px",height:"100%"}},xf=["value","onInput","onBlur"];function wf(e,t,r,n,a,o){return O(),I("div",hf,[k("div",pf,[k("div",{class:"color-cube",style:ne(e.getBgColorStyle),onClick:t[0]||(t[0]=(...s)=>e.onCopyColorStr&&e.onCopyColorStr(...s))},[e.copied?(O(),I("span",vf,"Copied!")):j("",!0)],4)]),e.inputType==="hex"?(O(),I("div",gf,[k("div",yf,[Xt(k("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>e.state.hex=s),maxlength:"8",onInput:t[2]||(t[2]=(...s)=>e.onInputChange&&e.onInputChange(...s)),onBlur:t[3]||(t[3]=(...s)=>e.onBlurChange&&e.onBlurChange(...s))},null,544),[[ki,e.state.hex]])]),e.disableAlpha?j("",!0):(O(),I("div",mf,[k("input",{class:"vc-alpha-input__inner",value:e.state.alpha,onInput:t[4]||(t[4]=(...s)=>e.onAlphaBlur&&e.onAlphaBlur(...s))},null,40,bf),ao("% ")]))])):e.state.rgba?(O(),I("div",Cf,[(O(!0),I(Ge,null,Tt(e.state.rgba,(s,i)=>(O(),I("div",{class:"vc-color-input",key:i},[k("input",{value:s,onInput:l=>e.onInputChange(l,i),onBlur:l=>e.onBlurChange(l,i)},null,40,xf)]))),128))])):j("",!0),k("div",{class:"vc-input-toggle",onClick:t[5]||(t[5]=(...s)=>e.onInputTypeChange&&e.onInputTypeChange(...s))},Yt(e.inputType),1)])}const Mn=Se(df,[["render",wf],["__scopeId","data-v-7334ac20"]]),Sf=he({name:"FkColorPicker",components:{Display:Mn,Alpha:kn,Palette:Xo,Board:En,Hue:On,Lightness:Yo,History:Rn},props:{color:M.instanceOf(K),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),disableAlpha:M.bool.def(!1)},emits:["update:color","change","advanceChange"],setup(e,{emit:t}){const r=e.color||new K,n=Ee({color:r,hex:r.toHexString(),rgb:r.toRgbString()}),a=J(!1),o=Q(()=>({background:n.rgb})),s=()=>{a.value=!1,t("advanceChange",!1)},i=en(Sn,[],{}),l=$e(()=>{if(e.disableHistory)return;const g=n.color.toRgbString();if(i.value=i.value.filter(v=>!C.equals(v,g)),!i.value.includes(g)){for(;i.value.length>_n;)i.value.pop();i.value.unshift(g)}},500),c=g=>{g==="advance"?(a.value=!0,t("advanceChange",!0)):(n.color.hex=g,t("advanceChange",!1))},u=g=>{n.color.alpha=g},f=g=>{n.color.hue=g},d=(g,v)=>{n.color.saturation=g,n.color.brightness=v},h=g=>{n.color.lightness=g},y=g=>{const v=g.target.value.replace("#","");C(v).isValid()&&(n.color.hex=v)};return ie(()=>e.color,g=>{g&&(n.color=g)},{deep:!0}),ie(()=>n.color,()=>{n.hex=n.color.hex,n.rgb=n.color.toRgbString(),l(),t("update:color",n.color),t("change",n.color)},{deep:!0}),{state:n,advancePanelShow:a,onBack:s,onCompactChange:c,onAlphaChange:u,onHueChange:f,onBoardChange:d,onLightChange:h,onInputChange:y,previewStyle:o,historyColors:i}}}),_f=e=>(gt("data-v-48e3c224"),e=e(),yt(),e),kf={class:"vc-fk-colorPicker"},Af={class:"vc-fk-colorPicker__inner"},Ef={class:"vc-fk-colorPicker__header"},Of=_f(()=>k("div",{class:"back"},null,-1)),Rf=[Of];function Mf(e,t,r,n,a,o){const s=Y("Palette"),i=Y("Board"),l=Y("Hue"),c=Y("Lightness"),u=Y("Alpha"),f=Y("Display"),d=Y("History");return O(),I("div",kf,[k("div",Af,[k("div",Ef,[e.advancePanelShow?(O(),I("span",{key:0,style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...h)=>e.onBack&&e.onBack(...h))},Rf)):j("",!0)]),e.advancePanelShow?j("",!0):(O(),Z(s,{key:0,onChange:e.onCompactChange},null,8,["onChange"])),e.advancePanelShow?(O(),Z(i,{key:1,color:e.state.color,onChange:e.onBoardChange},null,8,["color","onChange"])):j("",!0),e.advancePanelShow?(O(),Z(l,{key:2,color:e.state.color,onChange:e.onHueChange},null,8,["color","onChange"])):j("",!0),e.advancePanelShow?j("",!0):(O(),Z(c,{key:3,color:e.state.color,onChange:e.onLightChange},null,8,["color","onChange"])),e.disableAlpha?j("",!0):(O(),Z(u,{key:4,color:e.state.color,onChange:e.onAlphaChange},null,8,["color","onChange"])),Pe(f,{color:e.state.color,"disable-alpha":e.disableAlpha},null,8,["color","disable-alpha"]),e.disableHistory?j("",!0):(O(),Z(d,{key:5,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])])}const Ja=Se(Sf,[["render",Mf],["__scopeId","data-v-48e3c224"]]),Tf=he({name:"ChromeColorPicker",components:{Display:Mn,Alpha:kn,Board:En,Hue:On,History:Rn},props:{color:M.instanceOf(K),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),disableAlpha:M.bool.def(!1)},emits:["update:color","change"],setup(e,{emit:t}){const r=e.color||new K,n=Ee({color:r,hex:r.toHexString(),rgb:r.toRgbString()}),a=Q(()=>({background:n.rgb})),o=en(Sn,[],{}),s=$e(()=>{if(e.disableHistory)return;const d=n.color.toRgbString();if(o.value=o.value.filter(h=>!C.equals(h,d)),!o.value.includes(d)){for(;o.value.length>_n;)o.value.pop();o.value.unshift(d)}},500),i=d=>{n.color.alpha=d},l=d=>{n.color.hue=d},c=d=>{d.hex!==void 0&&(n.color.hex=d.hex),d.alpha!==void 0&&(n.color.alpha=d.alpha)},u=(d,h)=>{n.color.saturation=d,n.color.brightness=h},f=d=>{d!=="advance"&&(n.color.hex=d)};return ie(()=>e.color,d=>{d&&(n.color=d)},{deep:!0}),ie(()=>n.color,()=>{n.hex=n.color.hex,n.rgb=n.color.toRgbString(),s(),t("update:color",n.color),t("change",n.color)},{deep:!0}),{state:n,previewStyle:a,historyColors:o,onAlphaChange:i,onHueChange:l,onBoardChange:u,onInputChange:c,onCompactChange:f}}}),$f={class:"vc-chrome-colorPicker"},Pf={class:"vc-chrome-colorPicker-body"},Hf={class:"chrome-controls"},Lf={class:"chrome-sliders"};function If(e,t,r,n,a,o){const s=Y("Board"),i=Y("Hue"),l=Y("Alpha"),c=Y("Display"),u=Y("History");return O(),I("div",$f,[Pe(s,{round:!0,hide:!1,color:e.state.color,onChange:e.onBoardChange},null,8,["color","onChange"]),k("div",Pf,[k("div",Hf,[k("div",Lf,[Pe(i,{size:"small",color:e.state.color,onChange:e.onHueChange},null,8,["color","onChange"]),e.disableAlpha?j("",!0):(O(),Z(l,{key:0,size:"small",color:e.state.color,onChange:e.onAlphaChange},null,8,["color","onChange"]))])]),Pe(c,{color:e.state.color,"disable-alpha":e.disableAlpha},null,8,["color","disable-alpha"]),e.disableHistory?j("",!0):(O(),Z(u,{key:0,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])])}const Qa=Se(Tf,[["render",If],["__scopeId","data-v-2611d66c"]]),Tn="Vue3ColorPickerProvider",Bf=(e,t)=>{const r=e.getBoundingClientRect(),n=r.left+r.width/2,a=r.top+r.height/2,o=Math.abs(n-t.clientX),s=Math.abs(a-t.clientY),i=Math.sqrt(Math.pow(o,2)+Math.pow(s,2)),l=s/i,c=Math.acos(l);let u=Math.floor(180/(Math.PI/c));return t.clientX>n&&t.clientY>a&&(u=180-u),t.clientX==n&&t.clientY>a&&(u=180),t.clientX>n&&t.clientY==a&&(u=90),t.clientX<n&&t.clientY>a&&(u=180+u),t.clientX<n&&t.clientY==a&&(u=270),t.clientX<n&&t.clientY<a&&(u=360-u),u};let Br=!1;const Df=(e,t)=>{const r=function(a){var o;(o=t.drag)==null||o.call(t,a)},n=function(a){var o;document.removeEventListener("mousemove",r,!1),document.removeEventListener("mouseup",n,!1),document.onselectstart=null,document.ondragstart=null,Br=!1,(o=t.end)==null||o.call(t,a)};e&&e.addEventListener("mousedown",a=>{var o;Br||(document.onselectstart=()=>!1,document.ondragstart=()=>!1,document.addEventListener("mousemove",r,!1),document.addEventListener("mouseup",n,!1),Br=!0,(o=t.start)==null||o.call(t,a))})},Vf={angle:{type:Number,default:0},size:{type:Number,default:16,validator:e=>e>=16},borderWidth:{type:Number,default:1,validator:e=>e>=1},borderColor:{type:String,default:"#666"}},jf=he({name:"Angle",props:Vf,emits:["update:angle","change"],setup(e,{emit:t}){const r=J(null),n=J(0);lt(()=>e.angle,i=>{n.value=i});const a=()=>{let i=Number(n.value);isNaN(i)||(i=i>360||i<0?e.angle:i,n.value=i===360?0:i,t("update:angle",n.value),t("change",n.value))},o=Q(()=>({width:e.size+"px",height:e.size+"px",borderWidth:e.borderWidth+"px",borderColor:e.borderColor,transform:`rotate(${n.value}deg)`})),s=i=>{r.value&&(n.value=Bf(r.value,i)%360,a())};return Mi(()=>{const i={drag:l=>{s(l)},end:l=>{s(l)}};r.value&&Df(r.value,i)}),()=>Pe("div",{class:"bee-angle"},[Pe("div",{class:"bee-angle__round",ref:r,style:o.value},null)])}}),Nf=he({name:"GradientColorPicker",components:{Angle:jf,Display:Mn,Alpha:kn,Palette:Xo,Board:En,Hue:On,Lightness:Yo,History:Rn},props:{startColor:M.instanceOf(K).isRequired,endColor:M.instanceOf(K).isRequired,startColorStop:M.number.def(0),endColorStop:M.number.def(100),angle:M.number.def(0),type:M.oneOf(["linear","radial"]).def("linear"),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),disableAlpha:M.bool.def(!1),pickerType:M.oneOf(["fk","chrome"]).def("fk")},emits:["update:startColor","update:endColor","update:angle","update:startColorStop","update:endColorStop","startColorChange","endColorChange","advanceChange","angleChange","startColorStopChange","endColorStopChange","typeChange"],setup(e,{emit:t}){const r=Ee({startActive:!0,startColor:e.startColor,endColor:e.endColor,startColorStop:e.startColorStop,endColorStop:e.endColorStop,angle:e.angle,type:e.type,startColorRgba:e.startColor.toRgbString(),endColorRgba:e.endColor.toRgbString()}),n=no(Tn),a=J(e.pickerType==="chrome"),o=J(),s=J(),i=J();lt(()=>[e.startColor,e.endColor,e.angle],m=>{r.startColor=m[0],r.endColor=m[1],r.angle=m[2]}),lt(()=>e.type,m=>{r.type=m});const l=Q({get:()=>r.startActive?r.startColor:r.endColor,set:m=>{if(r.startActive){r.startColor=m;return}r.endColor=m}}),c=Q(()=>{if(i.value&&o.value){const m=r.startColorStop/100,R=i.value.getBoundingClientRect(),P=o.value.offsetWidth;return Math.round(m*(R.width-P)+P/2)}return 0}),u=Q(()=>{if(i.value&&s.value){const m=r.endColorStop/100,R=i.value.getBoundingClientRect(),P=s.value.offsetWidth;return Math.round(m*(R.width-P)+P/2)}return 0}),f=Q(()=>{let m=`background: linear-gradient(${r.angle}deg, ${r.startColorRgba} ${r.startColorStop}%, ${r.endColorRgba} ${r.endColorStop}%)`;return r.type==="radial"&&(m=`background: radial-gradient(circle, ${r.startColorRgba} ${r.startColorStop}%, ${r.endColorRgba} ${r.endColorStop}%)`),m}),d=m=>{var R;if(r.startActive=!0,i.value&&o.value){const P=(R=i.value)==null?void 0:R.getBoundingClientRect();let V=m.clientX-P.left;V=Math.max(o.value.offsetWidth/2,V),V=Math.min(V,P.width-o.value.offsetWidth/2),r.startColorStop=Math.round((V-o.value.offsetWidth/2)/(P.width-o.value.offsetWidth)*100),t("update:startColorStop",r.startColorStop),t("startColorStopChange",r.startColorStop)}},h=m=>{var R;if(r.startActive=!1,i.value&&s.value){const P=(R=i.value)==null?void 0:R.getBoundingClientRect();let V=m.clientX-P.left;V=Math.max(s.value.offsetWidth/2,V),V=Math.min(V,P.width-s.value.offsetWidth/2),r.endColorStop=Math.round((V-s.value.offsetWidth/2)/(P.width-s.value.offsetWidth)*100),t("update:endColorStop",r.endColorStop),t("endColorStopChange",r.endColorStop)}},y=m=>{const R=m.target,P=parseInt(R.value.replace("°",""));isNaN(P)||(r.angle=P%360),t("update:angle",r.angle),t("angleChange",r.angle)},g=m=>{r.angle=m,t("update:angle",r.angle),t("angleChange",r.angle)},v=m=>{m==="advance"?(a.value=!0,t("advanceChange",!0)):(l.value.hex=m,t("advanceChange",!1)),_()},p=m=>{l.value.alpha=m,_()},b=m=>{l.value.hue=m,_()},x=(m,R)=>{l.value.saturation=m,l.value.brightness=R,_()},w=m=>{l.value.lightness=m,_()},A=()=>{_()},_=()=>{r.startActive?(t("update:startColor",r.startColor),t("startColorChange",r.startColor)):(t("update:endColor",r.endColor),t("endColorChange",r.endColor))},T=()=>{a.value=!1,t("advanceChange",!1)},L=()=>{r.type=r.type==="linear"?"radial":"linear",t("typeChange",r.type)},H=en(Sn,[],{}),D=$e(()=>{if(e.disableHistory)return;const m=l.value.toRgbString();if(H.value=H.value.filter(R=>!C.equals(R,m)),!H.value.includes(m)){for(;H.value.length>_n;)H.value.pop();H.value.unshift(m)}},500);return vt(()=>{s.value&&o.value&&(Lt.triggerDragEvent(s.value,{drag:m=>{h(m)},end:m=>{h(m)}}),Lt.triggerDragEvent(o.value,{drag:m=>{d(m)},end:m=>{d(m)}}))}),ie(()=>r.startColor,m=>{r.startColorRgba=m.toRgbString()},{deep:!0}),ie(()=>r.endColor,m=>{r.endColorRgba=m.toRgbString()},{deep:!0}),ie(()=>l.value,()=>{D()},{deep:!0}),{startGradientRef:o,stopGradientRef:s,colorRangeRef:i,state:r,currentColor:l,getStartColorLeft:c,getEndColorLeft:u,gradientBg:f,advancePanelShow:a,onDegreeBlur:y,onCompactChange:v,onAlphaChange:p,onHueChange:b,onBoardChange:x,onLightChange:w,historyColors:H,onBack:T,onDegreeChange:g,onDisplayChange:A,onTypeChange:L,lang:n==null?void 0:n.lang}}}),Zo=e=>(gt("data-v-c4d6d6ea"),e=e(),yt(),e),Ff={class:"vc-gradient-picker"},Wf={class:"vc-gradient-picker__header"},zf={class:"vc-gradient__types"},Kf={class:"vc-gradient-wrap__types"},Uf={class:"vc-picker-degree-input vc-degree-input"},Gf={class:"vc-degree-input__control"},qf=["value"],Xf={class:"vc-degree-input__panel"},Yf={class:"vc-degree-input__disk"},Zf={class:"vc-gradient-picker__body"},Jf={class:"vc-color-range",ref:"colorRangeRef"},Qf={class:"vc-color-range__container"},ed={class:"vc-gradient__stop__container"},td=["title"],rd=Zo(()=>k("span",{class:"vc-gradient__stop--inner"},null,-1)),nd=[rd],ad=["title"],od=Zo(()=>k("span",{class:"vc-gradient__stop--inner"},null,-1)),id=[od];function ld(e,t,r,n,a,o){var s,i;const l=Y("Angle"),c=Y("Board"),u=Y("Hue"),f=Y("Palette"),d=Y("Lightness"),h=Y("Alpha"),y=Y("Display"),g=Y("History");return O(),I("div",Ff,[k("div",Wf,[k("div",null,[Xt(k("div",{class:"back",style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...v)=>e.onBack&&e.onBack(...v))},null,512),[[Dr,e.pickerType==="fk"&&e.advancePanelShow]])]),k("div",zf,[k("div",Kf,[(O(),I(Ge,null,Tt(["linear","radial"],v=>k("div",{class:le(["vc-gradient__type",{active:e.state.type===v}]),key:v,onClick:t[1]||(t[1]=(...p)=>e.onTypeChange&&e.onTypeChange(...p))},Yt(e.lang?e.lang[v]:v),3)),64))]),Xt(k("div",Uf,[k("div",Gf,[k("input",{value:e.state.angle,onBlur:t[2]||(t[2]=(...v)=>e.onDegreeBlur&&e.onDegreeBlur(...v))},null,40,qf),ao("deg ")]),k("div",Xf,[k("div",Yf,[Pe(l,{angle:e.state.angle,"onUpdate:angle":t[3]||(t[3]=v=>e.state.angle=v),size:40,onChange:e.onDegreeChange},null,8,["angle","onChange"])])])],512),[[Dr,e.state.type==="linear"]])])]),k("div",Zf,[k("div",Jf,[k("div",Qf,[k("div",{class:"vc-background",style:ne(e.gradientBg)},null,4),k("div",ed,[k("div",{class:le(["vc-gradient__stop",{"vc-gradient__stop--current":e.state.startActive}]),ref:"startGradientRef",title:(s=e.lang)==null?void 0:s.start,style:ne({left:e.getStartColorLeft+"px",backgroundColor:e.state.startColorRgba})},nd,14,td),k("div",{class:le(["vc-gradient__stop",{"vc-gradient__stop--current":!e.state.startActive}]),ref:"stopGradientRef",title:(i=e.lang)==null?void 0:i.end,style:ne({left:e.getEndColorLeft+"px",backgroundColor:e.state.endColorRgba})},id,14,ad)])])],512)]),e.advancePanelShow?(O(),Z(c,{key:0,color:e.currentColor,onChange:e.onBoardChange},null,8,["color","onChange"])):j("",!0),e.advancePanelShow?(O(),Z(u,{key:1,color:e.currentColor,onChange:e.onHueChange},null,8,["color","onChange"])):j("",!0),e.advancePanelShow?j("",!0):(O(),Z(f,{key:2,onChange:e.onCompactChange},null,8,["onChange"])),e.advancePanelShow?j("",!0):(O(),Z(d,{key:3,color:e.currentColor,onChange:e.onLightChange},null,8,["color","onChange"])),e.disableAlpha?j("",!0):(O(),Z(h,{key:4,color:e.currentColor,onChange:e.onAlphaChange},null,8,["color","onChange"])),Pe(y,{color:e.currentColor,"disable-alpha":e.disableAlpha,onChange:e.onDisplayChange},null,8,["color","disable-alpha","onChange"]),e.disableHistory?j("",!0):(O(),Z(g,{key:5,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])}const eo=Se(Nf,[["render",ld],["__scopeId","data-v-c4d6d6ea"]]),sd=he({name:"WrapContainer",props:{theme:M.oneOf(["white","black"]).def("white"),showTab:M.bool.def(!1),activeKey:M.oneOf(["pure","gradient"]).def("pure")},emits:["update:activeKey","change"],setup(e,{emit:t}){const r=Ee({activeKey:e.activeKey}),n=no(Tn),a=o=>{r.activeKey=o,t("update:activeKey",o),t("change",o)};return ie(()=>e.activeKey,o=>{r.activeKey=o}),{state:r,onActiveKeyChange:a,lang:n==null?void 0:n.lang}}}),ud={class:"vc-colorpicker--container"},cd={key:0,class:"vc-colorpicker--tabs"},fd={class:"vc-colorpicker--tabs__inner"},dd={class:"vc-btn__content"},hd={class:"vc-btn__content"};function pd(e,t,r,n,a,o){var s,i;return O(),I("div",{class:le(["vc-colorpicker",e.theme])},[k("div",ud,[e.showTab?(O(),I("div",cd,[k("div",fd,[k("div",{class:le(["vc-colorpicker--tabs__btn",{"vc-btn-active":e.state.activeKey==="pure"}]),onClick:t[0]||(t[0]=l=>e.onActiveKeyChange("pure"))},[k("button",null,[k("div",dd,Yt((s=e.lang)==null?void 0:s.pure),1)])],2),k("div",{class:le(["vc-colorpicker--tabs__btn",{"vc-btn-active":e.state.activeKey==="gradient"}]),onClick:t[1]||(t[1]=l=>e.onActiveKeyChange("gradient"))},[k("button",null,[k("div",hd,Yt((i=e.lang)==null?void 0:i.gradient),1)])],2),k("div",{class:"vc-colorpicker--tabs__bg",style:ne({width:"50%",left:`calc(${e.state.activeKey==="gradient"?50:0}%)`})},null,4)])])):j("",!0),Vr(e.$slots,"default",{},void 0,!0)])],2)}const vd=Se(sd,[["render",pd],["__scopeId","data-v-0492277d"]]),gd={start:"Start",end:"End",pure:"Pure",gradient:"Gradient",linear:"linear",radial:"radial"},yd={start:"开始",end:"结束",pure:"纯色",gradient:"渐变",linear:"线性",radial:"径向"},md={En:gd,"ZH-cn":yd},bd={isWidget:M.bool.def(!1),pickerType:M.oneOf(["fk","chrome"]).def("fk"),shape:M.oneOf(["circle","square"]).def("square"),pureColor:{type:[String,Object],default:"#000000"},gradientColor:M.string.def("linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)"),format:{type:String,default:"rgb"},disableAlpha:M.bool.def(!1),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),useType:M.oneOf(["pure","gradient","both"]).def("pure"),activeKey:M.oneOf(["pure","gradient"]).def("pure"),lang:{type:String,default:"ZH-cn"},zIndex:M.number.def(9999),pickerContainer:{type:[String,HTMLElement],default:"body"},debounce:M.number.def(100),theme:M.oneOf(["white","black"]).def("white"),blurClose:M.bool.def(!1),defaultPopup:M.bool.def(!1)},Cd=he({name:"ColorPicker",components:{FkColorPicker:Ja,ChromeColorPicker:Qa,GradientColorPicker:eo,WrapContainer:vd},inheritAttrs:!1,props:bd,emits:["update:pureColor","pureColorChange","update:gradientColor","gradientColorChange","update:activeKey","activeKeyChange"],setup(e,{emit:t}){$i(Tn,{lang:Q(()=>md[e.lang||"ZH-cn"])});const r=!!Pi().extra,n=Ee({pureColor:e.pureColor||"",activeKey:e.useType==="gradient"?"gradient":e.activeKey,isAdvanceMode:!1}),a=new K("#000"),o=new K("#000"),s=new K(n.pureColor),i=Ee({startColor:a,endColor:o,startColorStop:0,endColorStop:100,angle:0,type:"linear",gradientColor:e.gradientColor}),l=J(s),c=J(e.defaultPopup),u=J(null),f=J(null);let d=null;const h=Q(()=>({background:n.activeKey!=="gradient"?C(n.pureColor).toRgbString():i.gradientColor})),y=Q(()=>n.activeKey==="gradient"?eo.name:e.pickerType==="fk"?Ja.name:Qa.name),g=m=>{n.isAdvanceMode=m},v=Q(()=>{const m={disableAlpha:e.disableAlpha,disableHistory:e.disableHistory,roundHistory:e.roundHistory,pickerType:e.pickerType};return n.activeKey==="gradient"?Qe(Je({},m),{startColor:i.startColor,endColor:i.endColor,angle:i.angle,type:i.type,startColorStop:i.startColorStop,endColorStop:i.endColorStop,onStartColorChange:R=>{i.startColor=R,A()},onEndColorChange:R=>{i.endColor=R,A()},onStartColorStopChange:R=>{i.startColorStop=R,A()},onEndColorStopChange:R=>{i.endColorStop=R,A()},onAngleChange:R=>{i.angle=R,A()},onTypeChange:R=>{i.type=R,A()},onAdvanceChange:g}):Qe(Je({},m),{disableAlpha:e.disableAlpha,disableHistory:e.disableHistory,roundHistory:e.roundHistory,color:l.value,onChange:L,onAdvanceChange:g})}),p=()=>{c.value=!0,d?d.update():T()},b=()=>{c.value=!1},x=$e(()=>{!e.isWidget&&e.blurClose&&b()},100);Hi(f,()=>{b()});const w=()=>{var m,R,P,V;try{const[B]=ra.parse(i.gradientColor);if(B&&B.type.includes("gradient")&&B.colorStops.length>=2){const G=B.colorStops[0],ae=B.colorStops[1];i.startColorStop=Number((m=G.length)==null?void 0:m.value)||0,i.endColorStop=Number((R=ae.length)==null?void 0:R.value)||0,B.type==="linear-gradient"&&((P=B.orientation)==null?void 0:P.type)==="angular"&&(i.angle=Number((V=B.orientation)==null?void 0:V.value)||0),i.type=B.type.split("-")[0];const[De,ye,q,N]=G.value,[_e,S,E,$]=ae.value;i.startColor=new K({r:Number(De),g:Number(ye),b:Number(q),a:Number(N)}),i.endColor=new K({r:Number(_e),g:Number(S),b:Number(E),a:Number($)})}}catch(B){console.log(`[Parse Color]: ${B}`)}},A=$e(()=>{const m=_();try{i.gradientColor=ra.stringify(m),t("update:gradientColor",i.gradientColor),t("gradientColorChange",i.gradientColor)}catch(R){console.log(R)}},e.debounce),_=()=>{const m=[],R=i.startColor.RGB.map(B=>B.toString()),P=i.endColor.RGB.map(B=>B.toString()),V=[{type:"rgba",value:[R[0],R[1],R[2],R[3]],length:{value:i.startColorStop+"",type:"%"}},{type:"rgba",value:[P[0],P[1],P[2],P[3]],length:{value:i.endColorStop+"",type:"%"}}];return i.type==="linear"?m.push({type:"linear-gradient",orientation:{type:"angular",value:i.angle+""},colorStops:V}):i.type==="radial"&&m.push({type:"radial-gradient",orientation:[{type:"shape",value:"circle"}],colorStops:V}),m},T=()=>{u.value&&f.value&&(d=bs(u.value,f.value,{placement:"auto",modifiers:[{name:"offset",options:{offset:[0,8]}},{name:"flip",options:{allowedAutoPlacements:["top","bottom","left","right"],rootBoundary:"viewport"}}]}))},L=m=>{l.value=m,n.pureColor=m.toString(e.format),H()},H=$e(()=>{t("update:pureColor",n.pureColor),t("pureColorChange",n.pureColor)},e.debounce),D=m=>{n.activeKey=m,t("update:activeKey",m),t("activeKeyChange",m)};return vt(()=>{w(),d||T()}),ie(()=>e.gradientColor,m=>{m!=i.gradientColor&&(i.gradientColor=m)}),ie(()=>i.gradientColor,()=>{w()}),ie(()=>e.activeKey,m=>{n.activeKey=m}),ie(()=>e.useType,m=>{n.activeKey!=="gradient"&&m==="gradient"?n.activeKey="gradient":n.activeKey="pure"}),ie(()=>e.pureColor,m=>{C.equals(m,n.pureColor)||(n.pureColor=m,l.value=new K(m))},{deep:!0}),{colorCubeRef:u,pickerRef:f,showPicker:c,colorInstance:l,getBgColorStyle:h,getComponentName:y,getBindArgs:v,state:n,hasExtra:r,onColorChange:L,onShowPicker:p,onActiveKeyChange:D,onAutoClose:x}}}),xd={key:0,class:"vc-color-extra"},wd={key:0,class:"vc-color-extra"};function Sd(e,t,r,n,a,o){const s=Y("WrapContainer");return O(),I(Ge,null,[e.isWidget?(O(),Z(s,{key:0,"active-key":e.state.activeKey,"onUpdate:activeKey":t[0]||(t[0]=i=>e.state.activeKey=i),"show-tab":e.useType==="both",style:ne({zIndex:e.zIndex}),theme:e.theme,onChange:e.onActiveKeyChange},{default:Kn(()=>[(O(),Z(Un(e.getComponentName),Gn({key:e.getComponentName},e.getBindArgs),null,16)),e.hasExtra?(O(),I("div",xd,[Vr(e.$slots,"extra",{},void 0,!0)])):j("",!0)]),_:3},8,["active-key","show-tab","style","theme","onChange"])):j("",!0),e.isWidget?j("",!0):(O(),I(Ge,{key:1},[k("div",{class:le(["vc-color-wrap transparent",{round:e.shape==="circle"}]),ref:"colorCubeRef"},[k("div",{class:"current-color",style:ne(e.getBgColorStyle),onClick:t[1]||(t[1]=(...i)=>e.onShowPicker&&e.onShowPicker(...i))},null,4)],2),(O(),Z(Ti,{to:e.pickerContainer},[Xt(k("div",{ref:"pickerRef",style:ne({zIndex:e.zIndex}),onMouseleave:t[3]||(t[3]=(...i)=>e.onAutoClose&&e.onAutoClose(...i))},[e.showPicker?(O(),Z(s,{key:0,"show-tab":e.useType==="both"&&!e.state.isAdvanceMode,theme:e.theme,"active-key":e.state.activeKey,"onUpdate:activeKey":t[2]||(t[2]=i=>e.state.activeKey=i),onChange:e.onActiveKeyChange},{default:Kn(()=>[(O(),Z(Un(e.getComponentName),Gn({key:e.getComponentName},e.getBindArgs),null,16)),e.hasExtra?(O(),I("div",wd,[Vr(e.$slots,"extra",{},void 0,!0)])):j("",!0)]),_:3},8,["show-tab","theme","active-key","onChange"])):j("",!0)],36),[[Dr,e.showPicker]])],8,["to"]))],64))],64)}const _d=Se(Cd,[["render",Sd],["__scopeId","data-v-354ca836"]]),kd={class:"flex flex-1 items-center gap-[6px]"},Pd=he({inheritAttrs:!1,__name:"tag-style-picker",props:{selectType:{default:"default"},selectTypeModifiers:{},value:{default:void 0},valueModifiers:{}},emits:Li(["deselect"],["update:selectType","update:value"]),setup(e){const t=[{label:"默认颜色",value:"default"},{label:"自定义颜色",value:"custom"}],r=Q(()=>t),n=qn(e,"selectType"),a=qn(e,"value");function o(l){a.value=l.target.value==="custom"?"#1677ff":void 0}const{isDark:s}=Ai(),i=Q(()=>s.value?"black":"white");return(l,c)=>(O(),I("div",kd,[Pe(Nt(Bi),{value:n.value,"onUpdate:value":c[0]||(c[0]=u=>n.value=u),options:r.value,"button-style":"solid","option-type":"button",onChange:o},null,8,["value","options"]),n.value==="default"?(O(),Z(Nt(Di),{key:0,value:a.value,"onUpdate:value":c[1]||(c[1]=u=>a.value=u),"allow-clear":!0,options:Nt(Ii)(),class:"flex-1",placeholder:"请选择标签样式",onDeselect:c[2]||(c[2]=u=>l.$emit("deselect"))},null,8,["value","options"])):j("",!0),n.value==="custom"?(O(),Z(Nt(_d),{key:1,"disable-alpha":"",format:"hex","pure-color":a.value,"onUpdate:pureColor":c[3]||(c[3]=u=>a.value=u),theme:i.value},null,8,["pure-color","theme"])):j("",!0)]))}});export{Pd as _};
