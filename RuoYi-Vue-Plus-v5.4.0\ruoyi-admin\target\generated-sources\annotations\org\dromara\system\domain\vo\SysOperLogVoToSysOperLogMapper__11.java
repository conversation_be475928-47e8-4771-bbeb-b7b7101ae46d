package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__700;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__700.class,
    uses = {SysOperLogToSysOperLogVoMapper__11.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__11 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
