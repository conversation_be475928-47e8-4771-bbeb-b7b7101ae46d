import{r as p,a as _,b as v}from"./render-BxXtQdeV.js";import{D as b,a as o}from"./index-D59rZjD-.js";import{d as C,p as g,h as t,o as r,w as l,f as D,b as a,a as s,L as u,k as i,t as n,j as w,n as x}from"../jse/index-index-C-MnMZEz.js";import{u as N}from"./use-modal-CeMSCP2m.js";import{ar as B}from"./bootstrap-DCMzVRvD.js";const M=C({__name:"login-info-modal",setup(S){const e=g(),[m,f]=N({onOpenChange:d=>{if(!d)return null;const c=f.getData();e.value=c},onClosed(){e.value=void 0}});return(d,c)=>(r(),t(a(m),{footer:!1,"fullscreen-button":!1,class:"w-[550px]",title:"登录日志"},{default:l(()=>[e.value?(r(),t(a(b),{key:0,size:"small",column:1,bordered:""},{default:l(()=>[s(a(o),{label:"登录状态"},{default:l(()=>[(r(),t(u(a(p)(e.value.status,a(B).SYS_COMMON_STATUS))))]),_:1}),s(a(o),{label:"登录平台"},{default:l(()=>[i(n(e.value.clientKey.toLowerCase()),1)]),_:1}),s(a(o),{label:"账号信息"},{default:l(()=>[i(n(`账号: ${e.value.userName} / ${e.value.ipaddr} / ${e.value.loginLocation}`),1)]),_:1}),s(a(o),{label:"登录时间"},{default:l(()=>[i(n(e.value.loginTime),1)]),_:1}),s(a(o),{label:"登录信息"},{default:l(()=>[w("span",{class:x(["font-semibold",{"text-red-500":e.value.status!=="0"}])},n(e.value.msg),3)]),_:1}),s(a(o),{label:"登录设备"},{default:l(()=>[(r(),t(u(a(_)(e.value.os))))]),_:1}),s(a(o),{label:"浏览器"},{default:l(()=>[(r(),t(u(a(v)(e.value.browser))))]),_:1})]),_:1})):D("",!0)]),_:1}))}});export{M as _};
