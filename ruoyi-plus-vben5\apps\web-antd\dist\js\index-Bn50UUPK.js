import{p as f,d as j,ab as B,l as E,h as w,o as I,w as u,a,j as y,b as l,k as m,i as k,t as g,f as R,M as D}from"../jse/index-index-C-MnMZEz.js";import{_ as r}from"./file-upload.vue_vue_type_script_setup_true_lang-Cat_icI0.js";import"./image-upload.vue_vue_type_style_index_0_lang-DExXFAky.js";import{_ as v}from"./image-upload.vue_vue_type_script_setup_true_lang-Bn5a9jyU.js";import{_ as G}from"./upload-modal.vue_vue_type_script_setup_true_lang-hl4GvD-L.js";import{C as o}from"./index-C1KbofmV.js";import{_ as J}from"./code-mirror.vue_vue_type_script_setup_true_lang-DhrTT7Nl.js";import{_ as P}from"./page.vue_vue_type_script_setup_true_lang-CVhRs7Ij.js";import{an as H}from"./bootstrap-DCMzVRvD.js";import{u as K}from"./use-modal-CeMSCP2m.js";import{A as Q}from"./index-kC0HFDdy.js";import M from"./index-D-hwdOI6.js";import{a as V}from"./Group-oWwucTzK.js";import"./index-BY49C_DM.js";import"./css-Dmgy8YJo.js";import"./LeftOutlined-DE4sX_Jv.js";import"./index-BeyziwLP.js";import"./json-preview.vue_vue_type_style_index_0_lang-CxiHr6oM.js";import"./index-BaVK9zYh.js";import"./index-Bg2oL4a6.js";import"./shallowequal-DdADXzCF.js";import"./uniq-CCKK3QTo.js";import"./Overflow-DmNzxpBy.js";import"./index-CHpIOV4R.js";import"./slide-B82O6h2Y.js";import"./Dropdown-BOZk78PH.js";import"./pick-CyUZAAhv.js";import"./isMobile-8sZ0LT6r.js";import"./index-BLwHKR_M.js";import"./x-Bfkqqjgb.js";import"./Checkbox-DRV8G-PI.js";function W(){const s=["text","picture","picture-card"].map(n=>({label:n,value:n})),i=f("picture-card");return{imageListOptions:s,currentImageListType:i}}function X(){const s=["text","picture"].map(n=>({label:n,value:n})),i=f("picture");return{fileListOptions:s,currentFileListType:i}}const x="INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1905430203187712002, '文件上传Demo', 0, 1000, 'upload_test', '演示使用自行删除/upload/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 103, 1, '2025-03-28 09:22:16', 1, '2025-03-28 09:22:16', '');\n",Y={class:"grid grid-cols-2 gap-4"},Z={class:"mt-2 font-semibold text-green-500"},h={class:"my-2"},$e=j({__name:"index",setup(T){const s=f("1905537674682916865"),i=f("1905191167882518529"),n=f(["1905537674682916865"]),p=f(["1905191167882518529"]);function z(d){H.info({content:D("div",{class:"break-all"},JSON.stringify(d,null,2)),maskClosable:!0})}function _(d){return d.split(",").map(e=>e.toUpperCase()).join(",")}const C=f(!0),{imageListOptions:$,currentImageListType:b}=W(),{fileListOptions:O,currentFileListType:U}=X(),S=d=>d.type==="info"?`加上自定义前缀显示 - ${d.response.originalName.toUpperCase()}`:`加上自定义前缀显示 - ${d.response.fileName.toUpperCase()}`,c=()=>"https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp",{copy:q}=B({legacy:!0}),L=f(!1),[A,F]=K({connectedComponent:G});return(d,e)=>{const N=E("a-button");return I(),w(l(P),null,{default:u(()=>[a(l(o),{class:"mb-2",title:"提示",size:"small"},{default:u(()=>[e[24]||(e[24]=m(" 本地想体验可以导入这个sql(mysql的 其他的自行处理或者手动从菜单添加) ")),a(N,{size:"small",onClick:e[0]||(e[0]=t=>l(q)(l(x)))},{default:u(()=>e[23]||(e[23]=[m("复制")])),_:1,__:[23]}),a(l(J),{class:"mt-2",modelValue:l(x),"onUpdate:modelValue":e[1]||(e[1]=t=>k(x)?x.value=t:null),language:"sql",readonly:""},null,8,["modelValue"])]),_:1,__:[24]}),y("div",Y,[a(l(o),{title:"表单上传"},{default:u(()=>[a(N,{onClick:e[2]||(e[2]=t=>l(F).open())},{default:u(()=>e[25]||(e[25]=[m("打开")])),_:1,__:[25]}),a(l(A))]),_:1}),a(l(o),{title:"单上传, 会绑定为string",size:"small"},{default:u(()=>[a(l(v),{value:s.value,"onUpdate:value":e[3]||(e[3]=t=>s.value=t)},null,8,["value"]),m(" 当前绑定值: "+g(s.value)+" ",1),a(l(r),{class:"mt-6",value:i.value,"onUpdate:value":e[4]||(e[4]=t=>i.value=t)},null,8,["value"]),m(" 当前绑定值: "+g(i.value),1)]),_:1}),a(l(o),{title:"多上传, maxCount参数控制(开启深度监听)",size:"small"},{default:u(()=>[a(l(v),{value:n.value,"onUpdate:value":e[5]||(e[5]=t=>n.value=t),"max-count":3,"deep-watch":!0},null,8,["value"]),m(" 当前绑定值: "+g(n.value)+" ",1),a(l(r),{class:"mt-6",value:p.value,"onUpdate:value":e[6]||(e[6]=t=>p.value=t),"max-count":3,"deep-watch":!0},null,8,["value"]),m(" 当前绑定值: "+g(p.value),1)]),_:1}),a(l(o),{title:"文件自定义预览逻辑",size:"small"},{default:u(()=>[a(l(Q),{message:"你可以自定义预览逻辑, 比如改为下载, 回调参数为文件信息",class:"my-2"}),a(l(r),{value:p.value,"onUpdate:value":e[7]||(e[7]=t=>p.value=t),"max-count":3,preview:z,"help-message":!1},null,8,["value"]),a(l(v),{class:"mt-6",value:n.value,"onUpdate:value":e[8]||(e[8]=t=>n.value=t),"max-count":3,preview:z,"help-message":!1},null,8,["value"])]),_:1}),a(l(o),{title:"文件/图片拖拽上传",size:"small"},{default:u(()=>[a(l(r),{value:p.value,"onUpdate:value":e[9]||(e[9]=t=>p.value=t),"max-count":3,"enable-drag-upload":!0},null,8,["value"]),a(l(v),{class:"mt-6",value:n.value,"onUpdate:value":e[10]||(e[10]=t=>n.value=t),"enable-drag-upload":!0,"max-count":6},null,8,["value"])]),_:1}),a(l(o),{title:"禁用上传",size:"small"},{default:u(()=>[a(l(v),{disabled:!0,"max-count":3,"help-message":!1}),a(l(r),{class:"mt-6",disabled:!0,"max-count":3,"help-message":!1})]),_:1}),a(l(o),{title:"文件夹上传/自定义helpMessage",size:"small"},{default:u(()=>[a(l(r),{value:p.value,"onUpdate:value":e[11]||(e[11]=t=>p.value=t),"max-count":3,directory:!0,accept:"*"},{helpMessage:u(t=>[y("div",Z," 自定义helpMessage: "+g(JSON.stringify(t)),1)]),_:1},8,["value"])]),_:1}),a(l(o),{title:"自定义accept显示",size:"small"},{default:u(()=>[a(l(v),{value:s.value,"onUpdate:value":e[12]||(e[12]=t=>s.value=t),"accept-format":_},null,8,["value"]),a(l(v),{class:"mt-6",value:s.value,"onUpdate:value":e[13]||(e[13]=t=>s.value=t),"accept-format":"自定义显示允许的文件类型"},null,8,["value"])]),_:1}),a(l(o),{title:"默认在unMounted会取消上传",size:"small"},{default:u(()=>[e[26]||(e[26]=y("div",null,"将开发者工具调整网络为3G 切换挂载/卸载 可见请求在卸载被取消",-1)),e[27]||(e[27]=m(" 挂载/卸载组件: ")),a(l(M),{checked:C.value,"onUpdate:checked":e[14]||(e[14]=t=>C.value=t)},null,8,["checked"]),C.value?(I(),w(l(r),{key:0,value:i.value,"onUpdate:value":e[15]||(e[15]=t=>i.value=t)},null,8,["value"])):R("",!0)]),_:1,__:[26,27]}),a(l(o),{title:"图片: listType控制上传样式",size:"small"},{default:u(()=>[a(l(V),{value:l(b),"onUpdate:value":e[16]||(e[16]=t=>k(b)?b.value=t:null),options:l($),"button-style":"solid","option-type":"button"},null,8,["value","options"]),a(l(v),{class:"mt-2",value:s.value,"onUpdate:value":e[17]||(e[17]=t=>s.value=t),"list-type":l(b)},null,8,["value","list-type"])]),_:1}),a(l(o),{title:"文件: listType控制上传样式",size:"small"},{default:u(()=>[e[28]||(e[28]=y("div",{class:"mb-2 text-red-500"}," 注意文件上传不支持`picture-card`类型 ",-1)),e[29]||(e[29]=y("div",{class:"mb-2 text-red-500"}," 注意不要中途切换list-type(应该仅作为初始化属性使用) 会导致样式计算问题 helpMessage和文件会重叠 ",-1)),a(l(V),{value:l(U),"onUpdate:value":e[18]||(e[18]=t=>k(U)?U.value=t:null),options:l(O),"button-style":"solid","option-type":"button"},null,8,["value","options"]),a(l(r),{class:"mt-2",value:i.value,"onUpdate:value":e[19]||(e[19]=t=>i.value=t),"list-type":l(U)},null,8,["value","list-type"])]),_:1,__:[28,29]}),a(l(o),{title:"自定义缩略图和文件名",size:"small"},{default:u(()=>[a(l(r),{value:p.value,"onUpdate:value":e[20]||(e[20]=t=>p.value=t),"max-count":5,"list-type":"picture","custom-filename":S,"custom-thumb-url":c},null,8,["value"])]),_:1}),a(l(o),{title:"图片上传的动画效果",size:"small"},{default:u(()=>[y("div",h,[e[30]||(e[30]=m(" 是否启用 ")),e[31]||(e[31]=y("span",{class:"font-semibold"},"list-type: picture-card",-1)),e[32]||(e[32]=m(" 的动画效果: ")),a(l(M),{checked:L.value,"onUpdate:checked":e[21]||(e[21]=t=>L.value=t)},null,8,["checked"])]),a(l(v),{value:s.value,"onUpdate:value":e[22]||(e[22]=t=>s.value=t),"with-animation":L.value},null,8,["value","with-animation"]),m(" 当前绑定值: "+g(s.value),1)]),_:1})])]),_:1})}}});export{$e as default};
