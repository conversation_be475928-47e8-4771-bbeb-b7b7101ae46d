{"doc": " 角色表 数据层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageRoleList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 分页查询角色列表\n\n @param page         分页对象\n @param queryWrapper 查询条件\n @return 包含角色信息的分页结果\n"}, {"name": "selectRoleList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件分页查询角色数据\n\n @param queryWrapper 查询条件\n @return 角色数据集合信息\n"}, {"name": "selectRoleById", "paramTypes": ["java.lang.Long"], "doc": " 根据角色ID查询角色信息\n\n @param roleId 角色ID\n @return 对应的角色信息\n"}, {"name": "selectRolePermissionByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询角色\n\n @param userId 用户ID\n @return 角色列表\n"}, {"name": "selectRolesByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询角色\n\n @param userId 用户ID\n @return 角色列表\n"}], "constructors": []}