package org.dromara.wms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 仓库部门关联对象 wms_warehouse_dept
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_warehouse_dept")
public class WmsWarehouseDept extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 部门ID
     */
    private Long deptId;

}
