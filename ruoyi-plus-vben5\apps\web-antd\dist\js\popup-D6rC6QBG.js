var s=(o,i,n)=>new Promise((c,r)=>{var a=t=>{try{u(n.next(t))}catch(e){r(e)}},f=t=>{try{u(n.throw(t))}catch(e){r(e)}},u=t=>t.done?c(t.value):Promise.resolve(t.value).then(a,f);u((n=n.apply(o,i)).next())});import{i as p,an as v,$ as l}from"./bootstrap-DCMzVRvD.js";import{p as d}from"../jse/index-index-C-MnMZEz.js";function k(o){const{initializedGetter:i,currentGetter:n,compare:c}=o,r=d(""),a=d(!1);function f(e){return s(this,null,function*(){r.value=e||(yield i()),a.value=!0})}function u(){r.value="",a.value=!1}function t(){return s(this,null,function*(){if(!a.value)return!0;try{const e=yield n();return p(c)&&c(r.value,e)||e===r.value?!0:new Promise(m=>{v.confirm({title:l("pages.common.tip"),content:l("pages.common.beforeCloseTip"),centered:!0,okButtonProps:{danger:!0},cancelText:l("common.cancel"),okText:l("common.confirm"),onOk:()=>{m(!0),a.value=!1},onCancel:()=>m(!1)})})}catch(e){return console.error("Failed to compare data:",e),!0}})}return{onBeforeClose:t,markInitialized:f,resetInitialized:u}}function w(o){return()=>s(null,null,function*(){const i=yield o.getValues();return JSON.stringify(i)})}export{w as d,k as u};
