2025-06-16 00:05:07 [boundedElastic-74] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:05:07 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[23]毫秒
2025-06-16 00:05:07 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:05:07 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[15]毫秒
2025-06-16 00:05:08 [boundedElastic-74] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[18]毫秒
2025-06-16 00:05:08 [boundedElastic-74] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-16 00:05:08 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[14]毫秒
2025-06-16 00:05:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:05:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[17]毫秒
2025-06-16 00:05:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:05:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-06-16 00:05:28 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:28 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:05:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-16 00:05:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 00:05:32 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:05:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[20]毫秒
2025-06-16 00:05:32 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:05:32 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 00:05:33 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:33 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:05:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4NWIwa1BGYWRLUWp0bldwQzllMWNndkdxblczcUU1NCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.JahOC86g4xp0EyIOFmWkZ9A_et0wzDHnlZ-bLEsTYDE"]}]
2025-06-16 00:05:33 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[7]毫秒
2025-06-16 00:05:55 [boundedElastic-91] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 00:05:55 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[5]毫秒
2025-06-16 00:05:55 [boundedElastic-91] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 00:05:55 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[31]毫秒
2025-06-16 00:05:55 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:05:55 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:05:55 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-06-16 00:05:55 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:09:28 [boundedElastic-85] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:09:28 [boundedElastic-97] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:09:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[16]毫秒
2025-06-16 00:09:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[20]毫秒
2025-06-16 00:10:12 [boundedElastic-87] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:10:12 [boundedElastic-88] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:10:12 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-06-16 00:10:12 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[9]毫秒
2025-06-16 00:10:17 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:10:17 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:10:17 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-06-16 00:10:17 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 00:10:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:10:20 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:10:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-16 00:10:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:11 [boundedElastic-83] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:11 [boundedElastic-85] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:11 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:11:11 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:16 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:11:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:19 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:19 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:19 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[12]毫秒
2025-06-16 00:11:19 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 00:11:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:21 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:21 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-06-16 00:11:21 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-16 00:11:24 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:24 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:24 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-16 00:11:24 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:28 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:28 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-16 00:11:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-06-16 00:11:40 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 00:11:40 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[24]毫秒
2025-06-16 00:11:40 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 00:11:40 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-06-16 00:11:44 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 00:11:44 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[140]毫秒
2025-06-16 00:11:44 [boundedElastic-87] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 00:11:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[19]毫秒
2025-06-16 00:11:44 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 00:11:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 00:11:45 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 00:11:45 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 00:11:45 [boundedElastic-87] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJZREFsSUJtY3BFV0ExUkhRTEZxdWlJME5GcVZVMVFNYyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.fRVVxbxLZN4d6_rmTO3yU4bXOXAhSXVLX1YHO68ML0s"]}]
2025-06-16 00:11:45 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 00:13:00 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 00:13:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 00:13:00 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=[240e:3a1:bc85:beb1:187:bb4c:9c3e:8a7b], username=ruoyi, userpassword=123456}, registerEnabled=true, ip='*************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 00:13:00 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 08:23:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 08:23:32 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 29352 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 08:23:32 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 08:23:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 08:23:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 08:23:34 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 08:23:34 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 08:23:35 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 08:23:35 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:23:35 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 08:23:43 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 08:23:43 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 08:23:43 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 14.267 seconds (process running for 15.013)
2025-06-16 08:23:43 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 08:23:43 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:00:56 [boundedElastic-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-16 09:00:56 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[145]毫秒
2025-06-16 09:00:56 [boundedElastic-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-06-16 09:00:56 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[115]毫秒
2025-06-16 09:00:56 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-16 09:00:56 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:00:57 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[238]毫秒
2025-06-16 09:00:57 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[484]毫秒
2025-06-16 09:01:02 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-06-16 09:01:03 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1118]毫秒
2025-06-16 09:01:04 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:01:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[831]毫秒
2025-06-16 09:01:05 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:01:05 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-06-16 09:01:05 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:01:05 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-06-16 09:01:05 [boundedElastic-34] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:01:05 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[67]毫秒
2025-06-16 09:01:08 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:01:08 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:01:09 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[25]毫秒
2025-06-16 09:01:09 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[25]毫秒
2025-06-16 09:01:09 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:01:10 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1101]毫秒
2025-06-16 09:06:19 [boundedElastic-52] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:06:19 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[86]毫秒
2025-06-16 09:06:19 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:06:19 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[22]毫秒
2025-06-16 09:06:32 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:06:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[53]毫秒
2025-06-16 09:06:32 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:06:32 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-06-16 09:06:52 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:06:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[97]毫秒
2025-06-16 09:06:52 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:06:52 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-06-16 09:07:07 [boundedElastic-46] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:07:07 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[51]毫秒
2025-06-16 09:07:07 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:07:07 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[48]毫秒
2025-06-16 09:07:20 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:07:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[38]毫秒
2025-06-16 09:07:20 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:07:20 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-06-16 09:07:23 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:07:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:07:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:07:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[20]毫秒
2025-06-16 09:07:23 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[14]毫秒
2025-06-16 09:07:23 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[35]毫秒
2025-06-16 09:07:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:07:23 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[54]毫秒
2025-06-16 09:07:23 [boundedElastic-37] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:07:23 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[41]毫秒
2025-06-16 09:07:49 [reactor-http-nio-13] WARN  reactor.netty.channel.FluxReceive - [afd64d0f-1, L:/[0:0:0:0:0:0:0:1]:8080 - R:/[0:0:0:0:0:0:0:1]:51891] An exception has been observed post termination, use DEBUG level to see the full stack: java.net.SocketException: Connection reset
2025-06-16 09:11:52 [boundedElastic-52] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:11:52 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[28]毫秒
2025-06-16 09:11:52 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:11:52 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 09:12:25 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:12:25 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[26]毫秒
2025-06-16 09:12:25 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:12:25 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 09:12:26 [boundedElastic-61] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:12:26 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:12:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:12:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:12:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[8]毫秒
2025-06-16 09:12:26 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 09:12:26 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-06-16 09:12:26 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[32]毫秒
2025-06-16 09:12:26 [boundedElastic-61] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:12:26 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[22]毫秒
2025-06-16 09:12:26 [boundedElastic-61] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:12:26 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[11]毫秒
2025-06-16 09:12:29 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 09:12:29 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:12:32 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-16 09:12:32 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[72]毫秒
2025-06-16 09:12:48 [boundedElastic-52] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:08 [boundedElastic-48] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:18 [boundedElastic-48] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 09:13:19 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 09:13:20 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["100"]}]
2025-06-16 09:13:20 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 09:13:21 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-16 09:13:21 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[13]毫秒
2025-06-16 09:13:28 [boundedElastic-59] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:13:48 [boundedElastic-56] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:08 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:17 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:17 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-16 09:14:18 [boundedElastic-39] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[21]毫秒
2025-06-16 09:14:18 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:18 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 09:14:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 09:14:20 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:20 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 09:14:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:23 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:23 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[13]毫秒
2025-06-16 09:14:23 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[15]毫秒
2025-06-16 09:14:24 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:24 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 09:14:24 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:24 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[13]毫秒
2025-06-16 09:14:26 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["50"]}]
2025-06-16 09:14:26 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[12]毫秒
2025-06-16 09:14:28 [boundedElastic-49] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:14:29 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:29 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:14:33 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-06-16 09:14:33 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[8]毫秒
2025-06-16 09:14:33 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-06-16 09:14:33 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-06-16 09:14:33 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:14:33 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[7]毫秒
2025-06-16 09:14:33 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 09:14:33 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[16]毫秒
2025-06-16 09:14:33 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:14:33 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[37]毫秒
2025-06-16 09:14:48 [boundedElastic-59] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:08 [boundedElastic-48] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:13 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:15:13 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[24]毫秒
2025-06-16 09:15:13 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:15:13 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[13]毫秒
2025-06-16 09:15:14 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:15:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:15:14 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:15:14 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:14 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[7]毫秒
2025-06-16 09:15:14 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-16 09:15:14 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[11]毫秒
2025-06-16 09:15:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-06-16 09:15:14 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:15:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[16]毫秒
2025-06-16 09:15:14 [boundedElastic-64] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:15:14 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-06-16 09:15:16 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:16 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[12]毫秒
2025-06-16 09:15:16 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-16 09:15:16 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[10]毫秒
2025-06-16 09:15:26 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:15:26 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[21]毫秒
2025-06-16 09:15:26 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:15:26 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 09:15:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:15:27 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:15:27 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:27 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:15:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-16 09:15:27 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-16 09:15:27 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[10]毫秒
2025-06-16 09:15:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 09:15:27 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:15:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:15:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:15:27 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 09:15:28 [boundedElastic-57] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:15:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:15:42 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[20]毫秒
2025-06-16 09:15:42 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:15:42 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[11]毫秒
2025-06-16 09:15:44 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:15:44 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:15:44 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:15:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:15:44 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[5]毫秒
2025-06-16 09:15:44 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[5]毫秒
2025-06-16 09:15:44 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[9]毫秒
2025-06-16 09:15:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-06-16 09:15:44 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:15:44 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[17]毫秒
2025-06-16 09:15:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:15:44 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[6]毫秒
2025-06-16 09:15:48 [boundedElastic-60] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:08 [boundedElastic-64] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:09 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:16:09 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[23]毫秒
2025-06-16 09:16:09 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:16:09 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[12]毫秒
2025-06-16 09:16:10 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:16:10 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:16:10 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:16:10 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:16:10 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[6]毫秒
2025-06-16 09:16:10 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[6]毫秒
2025-06-16 09:16:10 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[14]毫秒
2025-06-16 09:16:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-16 09:16:10 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:16:10 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[14]毫秒
2025-06-16 09:16:10 [boundedElastic-62] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"],"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI0VE05RndlOFZEUmxIRVJhcjNPeTFVTjFRVXhSQUpmbSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IldNU-ezu-e7n-euoeeQhiIsImRlcHRDYXRlZ29yeSI6IiJ9.yw_k4ipHR78FxXul6iq3ZdF1WFX0SgAAtWq1VheNcIw"]}]
2025-06-16 09:16:10 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[8]毫秒
2025-06-16 09:16:11 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:16:11 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[15]毫秒
2025-06-16 09:16:11 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/1931679466777804802],无参数
2025-06-16 09:16:11 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/1931679466777804802],耗时:[11]毫秒
2025-06-16 09:16:14 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /wms/warehouse],参数类型[json],参数:[{"warehouseId":"1931679466777804802","warehouseNumber":"W002","warehouseName":"测试仓库1","warehouseType":"0","warehouseInventoryStatus":"0","warehouseRecevingStatus":"1","remark":"测试专用","deptIds":[100]}]
2025-06-16 09:16:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /wms/warehouse],耗时:[217]毫秒
2025-06-16 09:16:14 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:16:14 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[17]毫秒
2025-06-16 09:16:18 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:16:18 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[20]毫秒
2025-06-16 09:16:28 [boundedElastic-39] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: [Endpoint{protocol=HTTP, host='************, port=8718}]
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-16 09:16:40 [Thread-8] INFO  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Completed destruction of Publisher
2025-06-16 09:16:40 [Thread-1] INFO  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Completed destruction of ThreadPool
2025-06-16 09:16:40 [Thread-6] INFO  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Completed destruction of HttpClient
2025-06-16 09:16:40 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-16 09:17:10 [SpringApplicationShutdownHook] INFO  o.s.c.s.DefaultLifecycleProcessor - Shutdown phase 2147482623 ends with 1 bean still running after timeout of 30000ms: [webServerGracefulShutdown]
2025-06-16 09:17:10 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown aborted with one or more requests still active
2025-06-16 09:17:12 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:17:12 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:17:12 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:17:12 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:32:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:32:12 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 39152 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:32:12 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 09:32:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:32:14 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:32:14 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:32:14 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:32:15 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:15 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:32:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 09:32:23 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 09:32:23 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 13.948 seconds (process running for 14.591)
2025-06-16 09:32:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 09:32:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:33:13 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:33:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1043]毫秒
2025-06-16 09:33:14 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:33:14 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[85]毫秒
2025-06-16 09:33:15 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:33:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:33:15 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:33:15 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:33:15 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[27]毫秒
2025-06-16 09:33:15 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[28]毫秒
2025-06-16 09:33:15 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[65]毫秒
2025-06-16 09:33:15 [boundedElastic-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:33:15 [boundedElastic-6] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:33:15 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:16 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[910]毫秒
2025-06-16 09:33:16 [boundedElastic-6] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:33:16 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:17 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1646]毫秒
2025-06-16 09:33:17 [boundedElastic-4] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:33:17 [boundedElastic-4] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:33:27 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[43]毫秒
2025-06-16 09:33:27 [reactor-http-nio-20] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:33:27 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-06-16 09:33:28 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:33:28 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:33:28 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:33:28 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:33:28 [reactor-http-nio-6] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[15]毫秒
2025-06-16 09:33:28 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[15]毫秒
2025-06-16 09:33:28 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-06-16 09:33:28 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[28]毫秒
2025-06-16 09:33:28 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:33:28 [boundedElastic-6] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:33:28 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:28 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[42]毫秒
2025-06-16 09:33:29 [boundedElastic-6] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:33:29 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:33:30 [boundedElastic-6] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:33:30 [boundedElastic-6] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:26 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:35:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[39]毫秒
2025-06-16 09:35:26 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:35:26 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-06-16 09:35:27 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:35:27 [reactor-http-nio-1] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:35:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:35:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:35:27 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[10]毫秒
2025-06-16 09:35:27 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[11]毫秒
2025-06-16 09:35:27 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[22]毫秒
2025-06-16 09:35:27 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-06-16 09:35:27 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:35:27 [boundedElastic-7] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:35:27 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:27 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[36]毫秒
2025-06-16 09:35:28 [boundedElastic-7] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:35:28 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:29 [boundedElastic-7] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:35:29 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:56 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:35:56 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[35]毫秒
2025-06-16 09:35:56 [reactor-http-nio-10] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:35:56 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[14]毫秒
2025-06-16 09:35:57 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:35:57 [reactor-http-nio-13] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:35:57 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:35:57 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:35:57 [reactor-http-nio-19] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[8]毫秒
2025-06-16 09:35:57 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[9]毫秒
2025-06-16 09:35:57 [reactor-http-nio-18] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[15]毫秒
2025-06-16 09:35:57 [reactor-http-nio-2] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[21]毫秒
2025-06-16 09:35:57 [reactor-http-nio-16] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:35:57 [boundedElastic-14] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:35:57 [boundedElastic-14] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:57 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[35]毫秒
2025-06-16 09:35:58 [boundedElastic-14] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:35:58 [boundedElastic-14] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:35:59 [boundedElastic-14] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:35:59 [boundedElastic-14] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:36:24 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:36:24 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:36:24 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:36:24 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-06-16 09:42:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-16 09:42:15 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Starting RuoYiGatewayApplication using Java 17.0.14 with PID 30052 (D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0\ruoyi-gateway\target\classes started by junxl in D:\kwsywms_test\RuoYi-Cloud-Plus-v2.4.0)
2025-06-16 09:42:15 [main] INFO  o.d.gateway.RuoYiGatewayApplication - The following 1 profile is active: "dev"
2025-06-16 09:42:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP] success
2025-06-16 09:42:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-06-16 09:42:18 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-16 09:42:18 [main] INFO  o.d.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-06-16 09:42:18 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-16 09:42:18 [redisson-netty-3-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:42:18 [redisson-netty-3-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-06-16 09:42:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ruoyi-gateway ************:8080 register finished
2025-06-16 09:42:28 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-06-16 09:42:28 [main] INFO  o.d.gateway.RuoYiGatewayApplication - Started RuoYiGatewayApplication in 16.604 seconds (process running for 17.488)
2025-06-16 09:42:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
2025-06-16 09:42:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-06-16 09:43:56 [boundedElastic-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:43:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1595]毫秒
2025-06-16 09:43:58 [reactor-http-nio-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:43:58 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[92]毫秒
2025-06-16 09:43:59 [reactor-http-nio-11] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:43:59 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:43:59 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:43:59 [boundedElastic-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:43:59 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[36]毫秒
2025-06-16 09:43:59 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[36]毫秒
2025-06-16 09:43:59 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[64]毫秒
2025-06-16 09:43:59 [boundedElastic-9] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:43:59 [boundedElastic-9] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:43:59 [boundedElastic-9] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:00 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1060]毫秒
2025-06-16 09:44:00 [boundedElastic-9] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:44:00 [boundedElastic-9] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:01 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[1511]毫秒
2025-06-16 09:44:01 [boundedElastic-9] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:44:01 [boundedElastic-9] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:36 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-06-16 09:44:36 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[85]毫秒
2025-06-16 09:44:36 [reactor-http-nio-3] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-06-16 09:44:36 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-06-16 09:44:37 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-16 09:44:37 [reactor-http-nio-5] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],无参数
2025-06-16 09:44:37 [reactor-http-nio-4] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/wms_warehouse_type],无参数
2025-06-16 09:44:37 [reactor-http-nio-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-06-16 09:44:37 [reactor-http-nio-14] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_type],耗时:[12]毫秒
2025-06-16 09:44:37 [reactor-http-nio-8] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/wms_warehouse_instocktype],耗时:[12]毫秒
2025-06-16 09:44:37 [reactor-http-nio-12] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[20]毫秒
2025-06-16 09:44:37 [reactor-http-nio-15] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-06-16 09:44:37 [boundedElastic-7] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:44:37 [boundedElastic-7] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:37 [boundedElastic-7] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /wms/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-16 09:44:37 [reactor-http-nio-17] INFO  o.d.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /wms/warehouse/list],耗时:[39]毫秒
2025-06-16 09:44:38 [boundedElastic-5] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:44:38 [boundedElastic-5] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:44:39 [boundedElastic-5] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: ruoyi-resource
2025-06-16 09:44:39 [boundedElastic-5] ERROR o.d.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/resource/sse,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for ruoyi-resource"
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-16 09:46:40 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-16 09:46:41 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='nacos', endpoint='', namespace='dev', watchDelay=30000, logName='', service='ruoyi-gateway', weight=1.0, clusterName='', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, IPv6=null, username=ruoyi, userpassword=123456}, registerEnabled=true, ip='************', networkInterface='', port=8080, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:645)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:624)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:310)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:295)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:277)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:123)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:214)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:202)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:281)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:299)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:233)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:798)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:748)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1474)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:707)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1467)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:155)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-16 09:46:41 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
